using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication.MQTT;
using HslCommunication.LogNet;

namespace EdgeCloudServer
{
	class Program
	{
		public static ILogNet logNet;
		public static MqttServer mqttServer;
		static void Main( string[] args )
		{
			if (HslCommunication.Authorization.SetAuthorizationCode( "dc7a17d2-862a-47e5-bbe5-1491c773a6ac" ))
				Console.WriteLine( "激活成功!" );
			else
				Console.WriteLine( "激活失败!" );

			logNet = new LogNetSingle( "" );
			logNet.ConsoleOutput = true;
			mqttServer = new MqttServer( );

			mqttServer.ClientVerification += MqttServer_ClientVerification;
			mqttServer.OnClientConnected += MqttServer_OnClientConnected;
			mqttServer.OnClientDisConnected += MqttServer_OnClientDisConnected;
			mqttServer.ServerStart( 2345 );

			Console.ReadLine( );
		}

		private static int MqttServer_ClientVerification( MqttSession mqttSession, string clientId, string userName, string passwrod )
		{
			if (userName == "hsl" && passwrod == "aoiwhdasdnhaiwd213HI") return 0;
			return 3;
		}

		private static void MqttServer_OnClientDisConnected( MqttSession session )
		{
			logNet.WriteDebug( $"Ip[{session.EndPoint}] Id[{session.ClientId}] 下线   当前客户端数量：{mqttServer.OnlineSessions.Length}" );
		}

		private static void MqttServer_OnClientConnected( MqttSession session )
		{
			logNet.WriteDebug( $"Ip[{session.EndPoint}] Id[{session.ClientId}] 上线   当前客户端数量：{mqttServer.OnlineSessions.Length}" );
		}


	}
}
