using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.IO;

namespace HslTechnology.EdgeAssist
{
	/***********************************************************************************************
	 * 
	 *  本程序用于网关系统的辅助程序，实现自重启，文件版本升级的关键信息
	 *  暂时不对边缘服务器进行动态监测，应该交由冗余设备及数据中心服务器来进行动态监测管理
	 * 
	 *  未来将会推出对所有的网关状态及数据的动态管理，状态监视的数据中心服务器，
	 *  配合组态界面快速实现工厂信息化系统的二次开发
	 * 
	 **********************************************************************************************/


	class Program
	{
		/*********************************************************************************************
		 * 
		 *  接收启动参数信息，初步实现重启及更新的命令
		 * 
		 *  目前有两种参数信息
		 *  
		 *  Restart [没有参数]
		 *  Update [可选 Restart]
		 * 
		 *********************************************************************************************/
		static void Main( string[] args )
		{
			Console.WriteLine( "Hello HslEdgeServer!" );
			if (args == null || args.Length == 0) return;
			string command = args[0].ToUpper( );
			string fileName = "HslTechnology.EdgeServer";
			if (!File.Exists( Path.Combine( AppDomain.CurrentDomain.BaseDirectory, fileName ) ))
			{
				fileName = "HslTechnology.EdgeServer.exe";
			}

			if(command == "RESTART")
			{
				// 重启的命令
				ReStartEdgeServer( Path.Combine( AppDomain.CurrentDomain.BaseDirectory, fileName ) );
			}

		}

		public static void ReStartEdgeServer( string fileName )
		{
			int i = 0;
			while(i < 3)
			{
				i++;
				Thread.Sleep( 1000 );
				if (Restart( fileName ))
					break;
			}
		}


		public static bool Restart( string fileName )
		{
			if (RuntimeInformation.IsOSPlatform( OSPlatform.Linux ))
			{
				Console.WriteLine( "Restart the acquisition server. System: Linux" );
				try
				{
					string cmd = "#!/bin/bash" + Environment.NewLine + "chmod 777 " + fileName + Environment.NewLine + fileName;
					ExecCmd( cmd );
					Console.WriteLine( "Restart the edge server successfully." );
					return true;
				}
				catch(Exception ex)
				{
					Console.WriteLine( "Restart the collection server failed. Reason: " + ex.Message );
					return false;
				}
			}
			else if (RuntimeInformation.IsOSPlatform( OSPlatform.Windows ))
			{
				Console.WriteLine( "Restart the acquisition server. System: Windows" );
				try
				{
					System.Diagnostics.Process.Start( fileName );
					Console.WriteLine( "Restart the edge server successfully." );
					return true;
				}
				catch( Exception ex)
				{
					Console.WriteLine( "Restart the collection server failed. Reason: " + ex.Message );
					return false;
				}
			}
			else
			{
				Console.WriteLine( "Not supported system" );
				return false;
			}
		}

		public static void ExecCmd( string cmd )
		{
			string args = cmd.Replace( "\"", "\\\"" );
			System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo( )
			{
				FileName = "/bin/bash",
				Arguments = $"-c \"{args}\"",
				RedirectStandardOutput = true,
				UseShellExecute = false,
				CreateNoWindow = false,
			};
			System.Diagnostics.Process.Start( startInfo );
		}
	}
}
