<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 颜色资源 -->
    <Color x:Key="PrimaryColor">#0078D4</Color>
    <Color x:Key="PrimaryDarkColor">#106EBE</Color>
    <Color x:Key="PrimaryLightColor">#E3F2FD</Color>
    
    <Color x:Key="SecondaryColor">#6C757D</Color>
    <Color x:Key="SecondaryLightColor">#F8F9FA</Color>
    
    <Color x:Key="SuccessColor">#28A745</Color>
    <Color x:Key="WarningColor">#FF9800</Color>
    <Color x:Key="ErrorColor">#D32F2F</Color>
    <Color x:Key="InfoColor">#17A2B8</Color>
    
    <Color x:Key="BackgroundColor">#F5F5F5</Color>
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="BorderColor">#E1E1E1</Color>
    
    <Color x:Key="TextPrimaryColor">#333333</Color>
    <Color x:Key="TextSecondaryColor">#666666</Color>
    <Color x:Key="TextDisabledColor">#999999</Color>

    <!-- 画刷资源 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource SecondaryLightColor}"/>
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledColor}"/>

    <!-- 阴影效果 -->
    <DropShadowEffect x:Key="CardShadow" BlurRadius="8" OffsetX="0" OffsetY="2" Opacity="0.1"/>
    <DropShadowEffect x:Key="ButtonShadow" BlurRadius="4" OffsetX="0" OffsetY="1" Opacity="0.15"/>

    <!-- 动画资源 -->
    <Duration x:Key="FastAnimation">0:0:0.15</Duration>
    <Duration x:Key="MediumAnimation">0:0:0.25</Duration>
    <Duration x:Key="SlowAnimation">0:0:0.35</Duration>

    <!-- 尺寸资源 -->
    <CornerRadius x:Key="SmallCornerRadius">3</CornerRadius>
    <CornerRadius x:Key="MediumCornerRadius">6</CornerRadius>
    <CornerRadius x:Key="LargeCornerRadius">12</CornerRadius>
    
    <Thickness x:Key="SmallPadding">4</Thickness>
    <Thickness x:Key="MediumPadding">8</Thickness>
    <Thickness x:Key="LargePadding">16</Thickness>
    
    <Thickness x:Key="SmallMargin">2</Thickness>
    <Thickness x:Key="MediumMargin">4</Thickness>
    <Thickness x:Key="LargeMargin">8</Thickness>

    <!-- 字体资源 -->
    <FontWeight x:Key="LightWeight">Light</FontWeight>
    <FontWeight x:Key="NormalWeight">Normal</FontWeight>
    <FontWeight x:Key="SemiBoldWeight">SemiBold</FontWeight>
    <FontWeight x:Key="BoldWeight">Bold</FontWeight>
    
    <FontSize x:Key="SmallFontSize">11</FontSize>
    <FontSize x:Key="NormalFontSize">13</FontSize>
    <FontSize x:Key="MediumFontSize">15</FontSize>
    <FontSize x:Key="LargeFontSize">18</FontSize>
    <FontSize x:Key="XLargeFontSize">24</FontSize>

</ResourceDictionary>
