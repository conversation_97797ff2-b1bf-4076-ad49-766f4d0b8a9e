using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;

namespace tzedgeview.Converters
{
    /// <summary>
    /// 报警级别到颜色的转换器
    /// </summary>
    public class AlarmLevelToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return Brushes.Black;

            string level = value.ToString().ToLower();
            
            return level switch
            {
                "严重" or "critical" or "error" => new SolidColorBrush(Color.Parse("#D32F2F")),
                "警告" or "warning" or "warn" => new SolidColorBrush(Color.Parse("#FF9800")),
                "信息" or "info" or "information" => new SolidColorBrush(Color.Parse("#2196F3")),
                "调试" or "debug" => new SolidColorBrush(Color.Parse("#9E9E9E")),
                "提示" or "hint" => new SolidColorBrush(Color.Parse("#4CAF50")),
                _ => new SolidColorBrush(Color.Parse("#333333"))
            };
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 日志级别到颜色的转换器
    /// </summary>
    public class LogLevelToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return Brushes.Black;

            string level = value.ToString().ToLower();
            
            return level switch
            {
                "error" or "错误" => new SolidColorBrush(Color.Parse("#D32F2F")),
                "warning" or "warn" or "警告" => new SolidColorBrush(Color.Parse("#FF9800")),
                "info" or "information" or "信息" => new SolidColorBrush(Color.Parse("#2196F3")),
                "debug" or "调试" => new SolidColorBrush(Color.Parse("#9E9E9E")),
                "trace" or "跟踪" => new SolidColorBrush(Color.Parse("#607D8B")),
                _ => new SolidColorBrush(Color.Parse("#333333"))
            };
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到颜色的转换器
    /// </summary>
    public class BooleanToColorConverter : IValueConverter
    {
        public Brush TrueBrush { get; set; } = new SolidColorBrush(Color.Parse("#4CAF50"));
        public Brush FalseBrush { get; set; } = new SolidColorBrush(Color.Parse("#F44336"));

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? TrueBrush : FalseBrush;
            }
            return FalseBrush;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 状态到颜色的转换器
    /// </summary>
    public class StatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return Brushes.Gray;

            string status = value.ToString().ToLower();
            
            return status switch
            {
                "online" or "在线" or "连接" or "正常" => new SolidColorBrush(Color.Parse("#4CAF50")),
                "offline" or "离线" or "断开" or "异常" => new SolidColorBrush(Color.Parse("#F44336")),
                "connecting" or "连接中" or "初始化" => new SolidColorBrush(Color.Parse("#FF9800")),
                "unknown" or "未知" or "待机" => new SolidColorBrush(Color.Parse("#9E9E9E")),
                _ => new SolidColorBrush(Color.Parse("#607D8B"))
            };
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
