using ReactiveUI;
using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading;
using System.Threading.Tasks;
using System.Reactive.Linq;
using Avalonia.Media;
using Avalonia.Threading;
using HslCommunication.MQTT;
using Newtonsoft.Json.Linq;
using tzedgeview.Core;
using HslTechnology.Edge.DataBusiness.Time;

namespace tzedgeview.ViewModels
{
    /// <summary>
    /// 设备边缘监控视图模型
    /// </summary>
    public class FormDeviceEdgeViewModel : ViewModelBase
    {
        #region Constructor

        /// <summary>
        /// 实例化一个指定服务器设置的对象
        /// </summary>
        /// <param name="serverSettings">服务器设置</param>
        public FormDeviceEdgeViewModel(EdgeServerSettings serverSettings)
        {
            this.serverSettings = serverSettings;
            this.client = serverSettings?.GetMqttSyncClient();
            
            InitializeProperties();
            InitializeCommands();
            InitializeCollections();
        }

        #endregion

        #region Properties

        private string _title = "设备边缘监控";
        public string Title
        {
            get => _title;
            set => this.RaiseAndSetIfChanged(ref _title, value);
        }

        private string _connectionStatus = "未连接";
        public string ConnectionStatus
        {
            get => _connectionStatus;
            set => this.RaiseAndSetIfChanged(ref _connectionStatus, value);
        }

        private IBrush _connectionStatusColor = Brushes.Red;
        public IBrush ConnectionStatusColor
        {
            get => _connectionStatusColor;
            set => this.RaiseAndSetIfChanged(ref _connectionStatusColor, value);
        }

        private IBrush _statusIndicatorColor = Brushes.Red;
        public IBrush StatusIndicatorColor
        {
            get => _statusIndicatorColor;
            set => this.RaiseAndSetIfChanged(ref _statusIndicatorColor, value);
        }

        private string _lastUpdateTime = "从未更新";
        public string LastUpdateTime
        {
            get => _lastUpdateTime;
            set => this.RaiseAndSetIfChanged(ref _lastUpdateTime, value);
        }

        private string _deviceName = "";
        public string DeviceName
        {
            get => _deviceName;
            set => this.RaiseAndSetIfChanged(ref _deviceName, value);
        }

        private string _deviceType = "";
        public string DeviceType
        {
            get => _deviceType;
            set => this.RaiseAndSetIfChanged(ref _deviceType, value);
        }

        private string _ipAddress = "";
        public string IpAddress
        {
            get => _ipAddress;
            set => this.RaiseAndSetIfChanged(ref _ipAddress, value);
        }

        private string _port = "";
        public string Port
        {
            get => _port;
            set => this.RaiseAndSetIfChanged(ref _port, value);
        }

        private string _runTime = "";
        public string RunTime
        {
            get => _runTime;
            set => this.RaiseAndSetIfChanged(ref _runTime, value);
        }

        private string _cpuUsage = "";
        public string CpuUsage
        {
            get => _cpuUsage;
            set => this.RaiseAndSetIfChanged(ref _cpuUsage, value);
        }

        private string _memoryUsage = "";
        public string MemoryUsage
        {
            get => _memoryUsage;
            set => this.RaiseAndSetIfChanged(ref _memoryUsage, value);
        }

        private string _threadCount = "";
        public string ThreadCount
        {
            get => _threadCount;
            set => this.RaiseAndSetIfChanged(ref _threadCount, value);
        }

        private string _statusMessage = "就绪";
        public string StatusMessage
        {
            get => _statusMessage;
            set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
        }

        private int _dataCount = 0;
        public int DataCount
        {
            get => _dataCount;
            set => this.RaiseAndSetIfChanged(ref _dataCount, value);
        }

        private int _refreshInterval = 1000;
        public int RefreshInterval
        {
            get => _refreshInterval;
            set => this.RaiseAndSetIfChanged(ref _refreshInterval, value);
        }

        public ObservableCollection<RealTimeDataItem> RealTimeData { get; set; }
        public ObservableCollection<AlarmDataItem> AlarmData { get; set; }

        #endregion

        #region Commands

        public ReactiveCommand<Unit, Unit> RefreshCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> SettingsCommand { get; private set; }

        #endregion

        #region Private Fields

        private EdgeServerSettings serverSettings;
        private MqttSyncClient client;
        private Timer refreshTimer;
        private bool isMonitoring = false;
        private bool isReading = false;

        #endregion

        #region Methods

        /// <summary>
        /// 初始化属性
        /// </summary>
        private void InitializeProperties()
        {
            if (serverSettings != null)
            {
                Title = $"设备边缘监控 - {serverSettings.GetEdgeDisplayName()}";
                DeviceName = serverSettings.GetEdgeDisplayName();
                IpAddress = serverSettings.IpAddress;
                Port = serverSettings.Port.ToString();
            }
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            RefreshCommand = ReactiveCommand.CreateFromTask(RefreshDataAsync);
            SettingsCommand = ReactiveCommand.Create(ShowSettings);
        }

        /// <summary>
        /// 初始化集合
        /// </summary>
        private void InitializeCollections()
        {
            RealTimeData = new ObservableCollection<RealTimeDataItem>();
            AlarmData = new ObservableCollection<AlarmDataItem>();
        }

        /// <summary>
        /// 启动监控
        /// </summary>
        public void StartMonitoring()
        {
            if (isMonitoring || serverSettings == null) return;

            isMonitoring = true;
            refreshTimer = new Timer(TimerCallback, null, 0, RefreshInterval);
            StatusMessage = "监控已启动";
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            if (!isMonitoring) return;

            isMonitoring = false;
            refreshTimer?.Dispose();
            refreshTimer = null;
            StatusMessage = "监控已停止";
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshData()
        {
            _ = RefreshDataAsync();
        }

        /// <summary>
        /// 设置刷新间隔
        /// </summary>
        /// <param name="interval">间隔时间（毫秒）</param>
        public void SetRefreshInterval(int interval)
        {
            RefreshInterval = interval;
            
            if (isMonitoring)
            {
                refreshTimer?.Change(0, interval);
            }
        }

        /// <summary>
        /// 定时器回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void TimerCallback(object state)
        {
            if (isReading) return;
            
            await RefreshDataAsync();
        }

        /// <summary>
        /// 异步刷新数据
        /// </summary>
        /// <returns>任务</returns>
        private async Task RefreshDataAsync()
        {
            if (isReading || client == null) return;

            try
            {
                isReading = true;
                
                // 读取设备状态数据
                var readStatus = await client.ReadRpcAsync<JObject>("Edge/DeviceData", new { data = "__status" });
                
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    if (readStatus.IsSuccess)
                    {
                        ConnectionStatus = "已连接";
                        ConnectionStatusColor = Brushes.Green;
                        StatusIndicatorColor = Brushes.Green;
                        StatusMessage = "数据读取成功";
                        
                        // 解析设备状态数据
                        ParseDeviceStatus(readStatus.Content);
                    }
                    else
                    {
                        ConnectionStatus = "连接失败";
                        ConnectionStatusColor = Brushes.Red;
                        StatusIndicatorColor = Brushes.Red;
                        StatusMessage = $"数据读取失败: {readStatus.Message}";
                    }
                    
                    LastUpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                });

                // 读取运行时间历史
                var readTimes = await client.ReadRpcAsync<EdgeTimeConsume[]>("Edge/GetEdgeRuntimeHistory", new { });
                if (readTimes.IsSuccess)
                {
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        // 处理运行时间历史数据
                        ProcessRuntimeHistory(readTimes.Content);
                    });
                }
            }
            catch (Exception ex)
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    ConnectionStatus = "异常";
                    ConnectionStatusColor = Brushes.Orange;
                    StatusIndicatorColor = Brushes.Orange;
                    StatusMessage = $"读取异常: {ex.Message}";
                    LastUpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                });
            }
            finally
            {
                isReading = false;
            }
        }

        /// <summary>
        /// 解析设备状态数据
        /// </summary>
        /// <param name="statusData">状态数据</param>
        private void ParseDeviceStatus(JObject statusData)
        {
            try
            {
                if (statusData != null)
                {
                    // 解析各种状态信息
                    // 这里需要根据实际的数据结构进行解析
                    // 示例代码：
                    // CpuUsage = statusData["cpu"]?.ToString() ?? "";
                    // MemoryUsage = statusData["memory"]?.ToString() ?? "";
                    // ThreadCount = statusData["threads"]?.ToString() ?? "";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"解析状态数据失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 处理运行时间历史数据
        /// </summary>
        /// <param name="runtimeHistory">运行时间历史</param>
        private void ProcessRuntimeHistory(EdgeTimeConsume[] runtimeHistory)
        {
            try
            {
                if (runtimeHistory != null && runtimeHistory.Length > 0)
                {
                    // 处理运行时间历史数据
                    // 这里可以更新图表或其他显示
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"处理运行时间历史失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 显示设置
        /// </summary>
        private void ShowSettings()
        {
            // 显示设置对话框
            StatusMessage = "显示设置";
        }

        #endregion
    }

    #region Data Models

    /// <summary>
    /// 实时数据项
    /// </summary>
    public class RealTimeDataItem
    {
        public string Name { get; set; } = "";
        public string Value { get; set; } = "";
        public string DataType { get; set; } = "";
        public string UpdateTime { get; set; } = "";
        public string Status { get; set; } = "";
    }

    /// <summary>
    /// 报警数据项
    /// </summary>
    public class AlarmDataItem
    {
        public string AlarmTime { get; set; } = "";
        public string Level { get; set; } = "";
        public string Message { get; set; } = "";
        public string Status { get; set; } = "";
    }

    #endregion
}
