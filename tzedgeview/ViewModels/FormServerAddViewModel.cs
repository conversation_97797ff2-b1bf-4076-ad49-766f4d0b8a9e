using Avalonia.Controls;
using Avalonia.Input;
using HslCommunication.MQTT;
using HslCommunication;
using HslTechnology.Edge.Config;
using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using tzedgeview.Core;
using tzedgeview.Views;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;

namespace tzedgeview.ViewModels
{
    public class FormServerAddViewModel : ViewModelBase
    {
        private FormServerAdd _Main;
        public FormServerAdd Main
        {
            get => _Main;
            set => this.RaiseAndSetIfChanged(ref _Main, value);
        }

        private EdgeServerSettings _EdgeServerSettings;
        public EdgeServerSettings EdgeServerSettings
        {
            get => _EdgeServerSettings;
            set => this.RaiseAndSetIfChanged(ref _EdgeServerSettings, value);
        }

        public FormServerAddViewModel(FormServerAdd frmserveradd)
        {
            _Main = frmserveradd;
            _Main.DataContext = this;
            EdgeServerSettings = new EdgeServerSettings();
            EdgeServerSettings.Port = 521;
            EdgeServerSettings.IpAddress = "127.0.0.1";
            EdgeServerSettings.UseEncryptedCommunication = true;
            Main.btntest.Click += async (s, e) =>
            {
                try
                {
                    MqttSyncClient client = new MqttSyncClient(new MqttConnectionOptions()
                    {
                        IpAddress = EdgeServerSettings.IpAddress,
                        Port = EdgeServerSettings.Port,
                        Credentials = new MqttCredential(EdgeServerSettings.UserName, EdgeServerSettings.Password),
                    });
                    client.ConnectTimeOut = 2000;

                    OperateResult<string> read = client.ReadRpc<string>("Edge/CommunicationTest", null);
                    if (read.IsSuccess)
                    {
                        EdgeServerSettings.EdgeID = read.Content;             
                        Main.gwid.Text = read.Content;  
                        Main.btnok.IsEnabled = true;
                        var box = MessageBoxManager
                                    .GetMessageBoxStandard("提示", "连接成功！",
                                    ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                    }
                    else
                    {
                        var box = MessageBoxManager
                                    .GetMessageBoxStandard("提示", "连接失败！",
                                    ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                    }
                }
                catch (Exception ex)
                {
                    var box = MessageBoxManager
                               .GetMessageBoxStandard("警告", "设备的IP地址输入错误，无法识别！",
                               ButtonEnum.Ok, Icon.Warning);
                    var result = await box.ShowAsync();
                }
            };
            Main.btnok.Click += (s, e) =>
            {
                EdgeServerSettings.Password = HslCommunication.BasicFramework.SoftSecurity.MD5Encrypt(EdgeServerSettings.Password, HslServerSettings.oasdjoasjdohfiasdasjd);
                Main.Close();
            };
        }

    }
}
