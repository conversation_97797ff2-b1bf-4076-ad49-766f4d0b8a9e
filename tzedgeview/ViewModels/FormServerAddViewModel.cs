using Avalonia.Controls;
using Avalonia.Input;
using HslCommunication.MQTT;
using HslCommunication;
using HslTechnology.Edge.Config;
using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using tzedgeview.Core;
using tzedgeview.Views;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;

namespace tzedgeview.ViewModels
{
    public class FormServerAddViewModel : ViewModelBase
    {
        private FormServerAdd _Main;
        public FormServerAdd Main
        {
            get => _Main;
            set => this.RaiseAndSetIfChanged(ref _Main, value);
        }

        private EdgeServerSettings _EdgeServerSettings;
        public EdgeServerSettings EdgeServerSettings
        {
            get => _EdgeServerSettings;
            set => this.RaiseAndSetIfChanged(ref _EdgeServerSettings, value);
        }

        /// <summary>
        /// 是否为编辑模式
        /// </summary>
        public bool IsEditMode { get; private set; }

        /// <summary>
        /// 对话框结果
        /// </summary>
        public bool? DialogResult { get; private set; }

        /// <summary>
        /// 构造函数 - 新增模式
        /// </summary>
        public FormServerAddViewModel(FormServerAdd frmserveradd) : this(frmserveradd, null)
        {
        }

        /// <summary>
        /// 构造函数 - 支持编辑模式
        /// </summary>
        /// <param name="frmserveradd">窗口实例</param>
        /// <param name="existingSettings">现有设置（编辑模式）</param>
        public FormServerAddViewModel(FormServerAdd frmserveradd, EdgeServerSettings? existingSettings)
        {
            _Main = frmserveradd;
            _Main.DataContext = this;

            IsEditMode = existingSettings != null;

            if (IsEditMode && existingSettings != null)
            {
                // 编辑模式：复制现有设置
                EdgeServerSettings = new EdgeServerSettings
                {
                    Alias = existingSettings.Alias,
                    EdgeID = existingSettings.EdgeID,
                    IpAddress = existingSettings.IpAddress,
                    Port = existingSettings.Port,
                    UserName = existingSettings.UserName,
                    Password = existingSettings.Password, // 注意：这里可能需要解密
                    UseEncryptedCommunication = existingSettings.UseEncryptedCommunication
                };

                // 编辑模式下更新窗口标题
                Main.Title = "编辑网关";

                // 如果已有EdgeID，启用完成按钮
                if (!string.IsNullOrEmpty(EdgeServerSettings.EdgeID))
                {
                    Main.btnok.IsEnabled = true;
                }
            }
            else
            {
                // 新增模式：使用默认设置
                EdgeServerSettings = new EdgeServerSettings();
                EdgeServerSettings.Port = 521;
                EdgeServerSettings.IpAddress = "127.0.0.1";
                EdgeServerSettings.UseEncryptedCommunication = true;
                Main.Title = "新增网关";
            }
            // 通讯测试按钮事件
            Main.btntest.Click += async (s, e) =>
            {
                await TestConnection();
            };

            // 完成按钮事件
            Main.btnok.Click += (s, e) =>
            {
                SaveAndClose();
            };

            // 窗口关闭事件 - 不保存
            Main.Closing += (s, e) =>
            {
                if (DialogResult != true)
                {
                    // 用户直接关闭窗口，不保存
                    DialogResult = false;
                }
            };
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        private async Task TestConnection()
        {
            try
            {
                MqttSyncClient client = new MqttSyncClient(new MqttConnectionOptions()
                {
                    IpAddress = EdgeServerSettings.IpAddress,
                    Port = EdgeServerSettings.Port,
                    Credentials = new MqttCredential(EdgeServerSettings.UserName, EdgeServerSettings.Password),
                });
                client.ConnectTimeOut = 2000;

                OperateResult<string> read = client.ReadRpc<string>("Edge/CommunicationTest", null);
                if (read.IsSuccess)
                {
                    EdgeServerSettings.EdgeID = read.Content;
                    Main.gwid.Text = read.Content;
                    Main.btnok.IsEnabled = true;
                    var box = MessageBoxManager
                                .GetMessageBoxStandard("提示", "连接成功！",
                                ButtonEnum.Ok, Icon.Success);
                    await box.ShowAsync();
                }
                else
                {
                    var box = MessageBoxManager
                                .GetMessageBoxStandard("提示", "连接失败！",
                                ButtonEnum.Ok, Icon.Warning);
                    await box.ShowAsync();
                }
            }
            catch (Exception ex)
            {
                var box = MessageBoxManager
                           .GetMessageBoxStandard("警告", "设备的IP地址输入错误，无法识别！",
                           ButtonEnum.Ok, Icon.Warning);
                await box.ShowAsync();
            }
        }

        /// <summary>
        /// 保存并关闭
        /// </summary>
        private void SaveAndClose()
        {
            // 加密密码
            EdgeServerSettings.Password = HslCommunication.BasicFramework.SoftSecurity.MD5Encrypt(EdgeServerSettings.Password, HslServerSettings.oasdjoasjdohfiasdasjd);

            // 设置对话框结果为成功
            DialogResult = true;
            
            // 关闭窗口
            Main.Close();
        }
    }
}
