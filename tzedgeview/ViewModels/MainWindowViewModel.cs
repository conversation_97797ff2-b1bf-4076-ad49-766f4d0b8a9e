using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Reflection.Metadata;
using tzedgeview.Core;
using tzedgeview.Views;

namespace tzedgeview.ViewModels
{
    public class MainWindowViewModel : ViewModelBase
    {
        public static MainWindowViewModel? Instance { get; set; }

        private frmedgeserversviewmodel _frmedgeserverVM;
        public frmedgeserversviewmodel frmedgeserverVM
        {
            get => _frmedgeserverVM;
            set => this.RaiseAndSetIfChanged(ref _frmedgeserverVM, value);
        }

        public frmedgeservers Server { get; set; }
        public MainWindowViewModel(frmedgeservers server)
        {
            Server = server;
            DeviceNodes = new ObservableCollection<EdgeServerSettings>();

        }


        private MainWindow _Main;
        public MainWindow Main
        {
            get => _Main;
            set => this.RaiseAndSetIfChanged(ref _Main, value);
        }

        private ObservableCollection<EdgeServerSettings> _DeviceNodes;
        public ObservableCollection<EdgeServerSettings> DeviceNodes
        {
            get => _DeviceNodes;
            set => this.RaiseAndSetIfChanged(ref _DeviceNodes, value);
        }

        private EdgeServerSettings _SelectDeviceNodes;
        public EdgeServerSettings SelectDeviceNodes
        {
            get => _SelectDeviceNodes;
            set => this.RaiseAndSetIfChanged(ref _SelectDeviceNodes, value);
        }

        public void ShowEdgeListAndSelected(List<EdgeServerSettings> serverSettings, EdgeServerSettings select)
        {
            DeviceNodes.Clear();
            foreach (var node in serverSettings)
            {
                DeviceNodes.Add(node);
            }
            if (select != null)
            {
                SelectDeviceNodes = select;
            }
        }

        public void UpdateEdgeStatus(string edge, bool online, string message)
        {
            try
            {
                Main.lbldevice.Text = "设备："+edge;
                if (online)
                {
                    Main.lblmessage.Text = "消息：设备连接成功！";
                }
                else
                {
                    Main.lblmessage.Text = "消息：设备连接失败！";
                }
            }
            catch
            {

            }
        }

        public ReactiveCommand<Unit, Unit> DoTheThing { get; }

        void RunTheThing()
        {
            // Code for executing the command here.
        }
    }
}