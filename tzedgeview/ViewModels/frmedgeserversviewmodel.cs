using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using DynamicData;
using HslCommunication;
using HslCommunication.MQTT;
using Microsoft.VisualBasic;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;
using Newtonsoft.Json.Linq;
using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using tzedgeview.Core;
using tzedgeview.Views;
using HslTechnology.Edge.Plugins;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Render;
using System.Reflection;

namespace tzedgeview.ViewModels
{
    public  class frmedgeserversviewmodel : ViewModelBase
    {
        private string serverListFilePath = "serverList.txt";

        private frmedgeservers _Main;
        public frmedgeservers Main
        {
            get => _Main;
            set => this.RaiseAndSetIfChanged(ref _Main, value);
        }

        private FormDeviceMonitor deviceMonitor;            // 本窗体绑定的显示设备状态的控件

        public FormDeviceMonitor FormDeviceMonitor 
        { 
            get => this.deviceMonitor; 
            set => this.RaiseAndSetIfChanged(ref deviceMonitor, value);

        }

        private ObservableCollection<AvTreeNode> _DeviceNodes;
        public ObservableCollection<AvTreeNode> DeviceNodes
        {
            get => _DeviceNodes;
            set => this.RaiseAndSetIfChanged(ref _DeviceNodes, value);
        }

        private AvTreeNode _SelectDeviceNode;
        public AvTreeNode SelectDeviceNode
        {
            get => _SelectDeviceNode;
            set => this.RaiseAndSetIfChanged(ref _SelectDeviceNode, value);
        }

        public frmedgeserversviewmodel(frmedgeservers frmedgeservers)
        {
            _Main = frmedgeservers;
            _Main.DataContext = this;

            this.serverListFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "serverList.txt");
            TVServerMouseDown = ReactiveCommand.Create<TreeView>(DoTVServerMouseDown);
            Main.tvdevice.AddHandler(TreeView.TappedEvent, new EventHandler<TappedEventArgs>(OnTapped));
            Main.tvdevice.AddHandler(TreeView.DoubleTappedEvent, OnDoubleTapped);

            Main.mnucjcs.AddHandler(MenuItem.TappedEvent, OnMenuCJCSTapped);
            Main.mnuckrz.AddHandler(MenuItem.TappedEvent, OnMenuCKRZTapped);
            Main.mnucqsb.AddHandler(MenuItem.TappedEvent, OnMenuCQSBTapped);
            Main.mnugbsz.AddHandler(MenuItem.TappedEvent, OnMenuGBSZTapped);
            Main.mnujk.AddHandler(MenuItem.TappedEvent, OnMenuJKTapped);
            Main.mnuljcs.AddHandler(MenuItem.TappedEvent, OnMenuLJCSTapped);
            Main.mnuscsb.AddHandler(MenuItem.TappedEvent, OnMenuSCSBTapped);
            Main.mnusbcs.AddHandler(MenuItem.TappedEvent, OnMenuSBCSTapped);

            Main.btnadd.Click += Button_add_server_Click;

            DeviceNodes = new ObservableCollection<AvTreeNode>();
            LoadServerListFromTxt();

        }

        private async void Button_add_server_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                var serverAddWindow = new FormServerAdd();
                var viewModel = new FormServerAddViewModel(serverAddWindow); // 新增模式
                serverAddWindow.Width = 500;
                serverAddWindow.Height = 300;

                var result = await serverAddWindow.ShowDialog<bool?>(MainWindowViewModel.Instance.Main);

                if (result == true && viewModel.DialogResult == true)
                {
                    // 只有在用户点击完成按钮时才保存
                    EdgeServerSettings serverSettings = viewModel.EdgeServerSettings;
                    AvTreeNode treeNode = new AvTreeNode(serverSettings.GetDisplayText(), serverSettings, null);
                    treeNode.Tag = serverSettings;
                    treeNode.Icon = IconUrl.GWDevice_Normal;
                    DeviceNodes.Add(treeNode);

                    treeNode.Text = serverSettings.GetDisplayText();
                    treeNode.Tag = serverSettings;

                    SaveServerListToTxt();
                    FormDeviceMonitorViewModel fvm = this.deviceMonitor.DataContext as FormDeviceMonitorViewModel;
                    MainWindowViewModel.Instance.ShowEdgeListAndSelected(GetEdgeServerSettings(), fvm.ServerSettings);

                    // 更新当前的网关连接状态
                    UpdateEdgeConnectStatus(treeNode);

                    var successBox = MessageBoxManager
                                    .GetMessageBoxStandard("成功", "网关添加成功！",
                                    ButtonEnum.Ok, Icon.Success);
                    await successBox.ShowAsync();
                }
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                                .GetMessageBoxStandard("错误", $"添加网关失败: {ex.Message}",
                                ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        public void LoadServerListFromTxt()
        {
            if (File.Exists(serverListFilePath))
            {
                try
                {
                    string content = Encoding.Default.GetString(File.ReadAllBytes(serverListFilePath));
                    List<EdgeServerSettings> serverSettings = JArray.Parse(content).ToObject<List<EdgeServerSettings>>();

                    foreach (var item in serverSettings)
                    {
                        AvTreeNode treeNode = new AvTreeNode(item.GetDisplayText(), item, null);
                        treeNode.Icon = IconUrl.GWDevice_Normal;
                        treeNode.Tag = item;
                        DeviceNodes.Add(treeNode);
                    }
                }
                catch (Exception ex)
                {
                }
            }
        }

        private void UpdateEdgeConnectStatus(AvTreeNode treeNode)
        {
            ThreadPool.QueueUserWorkItem(new WaitCallback(m =>
            {
                if (treeNode.Tag is EdgeServerSettings edgeServerSettings)
                {
                    MqttSyncClient client = edgeServerSettings.GetMqttSyncClient(true);
                    client.ConnectTimeOut = 2000;
                    OperateResult connect = client.ConnectServer();
                    try
                    {
                        var t = new Action(() =>
                        {
                            if (connect.IsSuccess)
                            {
                                //treeNode.SetImageKey("server_Local_16xLG_Green");
                            }
                            else
                            {
                                //treeNode.SetImageKey("server_Local_16xLG");
                            }
                        });
                        t.Invoke();
                    }
                    catch
                    {

                    }
                    client.ConnectClose();
                }
            }), null);
        }

        private void SaveServerListToTxt()
        {
            List<EdgeServerSettings> serverSettings = GetEdgeServerSettings();
            File.WriteAllBytes(serverListFilePath, Encoding.Default.GetBytes(JArray.FromObject(serverSettings).ToString()));
        }

        private List<EdgeServerSettings> GetEdgeServerSettings()
        {
            List<EdgeServerSettings> serverSettings = new List<EdgeServerSettings>();
            foreach (AvTreeNode node in DeviceNodes)
            {
                if (node.Tag is EdgeServerSettings settings)
                {
                    serverSettings.Add(settings);
                }
            }
            return serverSettings;
        }

        public void SetMainWinEdgeServer()
        {
            MainWindowViewModel.Instance.ShowEdgeListAndSelected(GetEdgeServerSettings(), null);
        }

        private void OnMenuSCSBTapped(object? sender, TappedEventArgs e)
        {
            throw new NotImplementedException();
        }

        private void OnMenuLJCSTapped(object? sender, TappedEventArgs e)
        {
            throw new NotImplementedException();
        }

        private void OnMenuJKTapped(object? sender, TappedEventArgs e)
        {
            throw new NotImplementedException();
        }

        private void OnMenuGBSZTapped(object? sender, TappedEventArgs e)
        {
            throw new NotImplementedException();
        }

        private void OnMenuCQSBTapped(object? sender, TappedEventArgs e)
        {
            throw new NotImplementedException();
        }

        private void OnMenuCKRZTapped(object? sender, TappedEventArgs e)
        {
            throw new NotImplementedException();
        }

        private void OnMenuCJCSTapped(object? sender, TappedEventArgs e)
        {
            throw new NotImplementedException();
        }

        private int isRenderEdgeInfo = 0;
        private async void OnDoubleTapped(object? sender, TappedEventArgs e)
        {
            if (SelectDeviceNode == null) return;

            // 双击了网关
            if (SelectDeviceNode.Tag is EdgeServerSettings serverSettings)
            {
                if (deviceMonitor == null) return;
                // 将监视界面显示出来
                MainWindowViewModel.Instance.Main.pnlmonitor.Children.Clear();
                MainWindowViewModel.Instance.Main.pnlmonitor.Children.Add(deviceMonitor);
                

                if (Interlocked.CompareExchange(ref isRenderEdgeInfo, 1, 0) == 0)
                {
                    if (await RenderServerInfo(SelectDeviceNode, serverSettings))
                    {
                        FormDeviceMonitorViewModel mdl = deviceMonitor.DataContext as FormDeviceMonitorViewModel;
                        mdl.SetServerSettings(serverSettings);
                        SelectDeviceNode.Icon = IconUrl.GWDevice_Select;
                    }
                    else
                    {
                        SelectDeviceNode.Icon = IconUrl.GWDevice_Normal;
                    }

                    // 在主窗体显示列表及更新选择
                    MainWindowViewModel.Instance.ShowEdgeListAndSelected(GetEdgeServerSettings(), serverSettings);
                    Interlocked.Exchange(ref isRenderEdgeInfo, 0);
                }
            }
            else if (SelectDeviceNode.Tag is DeviceNode deviceNode)
            {
                if (!string.IsNullOrEmpty(SelectDeviceNode.Text))
                {
                    // 获取根节点，展开相关的数据节点
                    AvTreeNode root = GetRootTreeNode(SelectDeviceNode);
                    if (root == null) return;
                    if (root.Tag is EdgeServerSettings serverSettings1)
                    {
                        MqttSyncClient rpc = serverSettings1.GetMqttSyncClient();

                        var read = await rpc.ReadRpcAsync<ScalarDataNode[]>("Edge/BrowseDeviceDataNodes", new { data = new TreeNodePath(SelectDeviceNode).GetActualPath() });
                        SelectDeviceNode.ChildNodes.Clear();
                        if (!read.IsSuccess)
                        {
                            var box = MessageBoxManager
                                .GetMessageBoxStandard("提示", "Failed:" + read.Message,
                                ButtonEnum.Ok, Icon.Warning);
                            var result = await box.ShowAsync();
                        }

                        for (int i = 0; i < read.Content.Length; i++)
                        {
                            ScalarDataNode scalarDataNode = read.Content[i];
                            AvTreeNode treeNode = new AvTreeNode(scalarDataNode.GetDisplayName(), null, SelectDeviceNode);
                            if (scalarDataNode.DataDimension == HslTechnology.Edge.Node.DataDimension.One)
                            {
                                treeNode.Icon = IconUrl.NodeRequestNode_en;
                                if (scalarDataNode.DataType == HslTechnology.Edge.Node.DataType.Struct)
                                {
                                    // 追加结构体列表的子节点信息
                                    for (int j = 0; j < scalarDataNode.ArrayLength; j++)
                                    {
                                        AvTreeNode structNode = new AvTreeNode(scalarDataNode.Name + $"[{j}]", null, treeNode);
                                        structNode.Icon = IconUrl.NodeRequestNode_en;
                                        structNode.Tag = scalarDataNode;
                                        treeNode.ChildNodes.Add(structNode);
                                    }
                                }
                            }
                            else if (scalarDataNode.DataDimension == HslTechnology.Edge.Node.DataDimension.Two)
                                treeNode.Icon = IconUrl.NodeRequestNode_en;
                            else
                            {
                                if (scalarDataNode.DataType == HslTechnology.Edge.Node.DataType.Struct)
                                    treeNode.Icon = IconUrl.NodeRequestNode_en;
                                else if (scalarDataNode.DataType == HslTechnology.Edge.Node.DataType.Method)
                                    treeNode.Icon = IconUrl.NodeRequestNode_en;
                                else
                                    treeNode.Icon = IconUrl.NodeRequestNode_en;
                            }
                            treeNode.Tag = scalarDataNode;
                            SelectDeviceNode.ChildNodes.Add(treeNode);
                        }

                    }
                }
            }
        }

        public async Task<bool> RenderServerInfo(AvTreeNode treeNode, EdgeServerSettings serverSettings)
        {
            MqttSyncClient rpc = serverSettings.GetMqttSyncClient();

            // 先请求显示的文档信息
            var readXml = await rpc.ReadRpcAsync<string>("Edge/XmlDeviceNodes", "");
            if (!readXml.IsSuccess) 
            {
                var box = MessageBoxManager
                            .GetMessageBoxStandard("提示", "Failed:" + readXml.Message,
                            ButtonEnum.Ok, Icon.Warning);
                var result = await box.ShowAsync();
                return false; 
            }

            // 请求下插件信息及图标，方便显示
            OperateResult<PluginsDefinition[]> readPlugins = await rpc.ReadRpcAsync<PluginsDefinition[]>("Plugins/GetRegisterPlugins", new { });

            // 添加设备信息
            XElement element = XElement.Parse(readXml.Content);
            SelectDeviceNode.ChildNodes.Clear();
            Utils.RenderEdgeServerTreeNodeBrowseNode(serverSettings, treeNode, element.Elements().ToArray()[0]);  // 展开显示

            var readStatus = await rpc.ReadRpcAsync<JObject>("Edge/DeviceData", new { data = "__status" });        // 获取网关的基础数据
            if (!readStatus.IsSuccess) 
            {
                var box = MessageBoxManager
                    .GetMessageBoxStandard("提示", "Failed:" + readXml.Message,
                    ButtonEnum.Ok, Icon.Warning);
                var result = await box.ShowAsync();
                return false;
            }

            try
            {
                JObject statusJson = readStatus.Content;                                 // JObject.Parse( readStatus.Content );
                if (serverSettings.EdgeID != statusJson["__name"].Value<string>())      // 如果网关名称发生了修复，则使用新的名称显示
                {
                    serverSettings.EdgeID = statusJson["__name"].Value<string>();
                    treeNode.Text = serverSettings.GetDisplayText();
                    SaveServerListToTxt();
                }
                FormDeviceMonitorViewModel fmv = deviceMonitor.DataContext as FormDeviceMonitorViewModel;
                fmv?.RenderDevices(serverSettings, statusJson);
                return true;
            }
            catch (Exception ex)
            {
                //HslCommunication.BasicFramework.SoftBasic.ShowExceptionMessage( "原数据：" + readStatus.Content + 
                //Environment.NewLine + "错误消息：" , ex );
                return false;
            }
        }

        private async void OnTapped(object? sender, TappedEventArgs e)
        {
            FormDeviceMonitorViewModel fmv = deviceMonitor.DataContext as FormDeviceMonitorViewModel;
            AvTreeNode edge = FindEdgeNode(SelectDeviceNode);
            if (edge == null) return;
            if (edge.Tag is EdgeServerSettings serverSettings2)
            {               
                if (!SelectDeviceNode.IsServerLocalNode() && !object.ReferenceEquals(fmv.ServerSettings, serverSettings2))
                {
                    var box = MessageBoxManager
                                    .GetMessageBoxStandard("提示", "当前无法切换查看数据，需要先双击另一个网关节点查看数据！",
                                    ButtonEnum.Ok, Icon.Warning);
                    var result = await box.ShowAsync();
                    return;
                }
                if (SelectDeviceNode.Icon == IconUrl.GroupNode) 
                {
                    // 单机了分类节点信息
                    fmv.SetServerSettings(serverSettings2, new TreeNodePath(SelectDeviceNode));
                }
                // 这里的逻辑是当哪个设备点击了，就在主监控界面切换监视的设备数据
                else if (SelectDeviceNode.IsServerLocalNode())
                {
                    // 点击了网关，如果网关在显示中，则显示其路径设备信息
                    if (deviceMonitor == null) return;
                    if (fmv.ServerSettings == null) return;
                    fmv.SetServerSettings(serverSettings2, new TreeNodePath());
                }
                else if (SelectDeviceNode.Tag is ScalarDataNode scalarDataNode)
                {
                    AvTreeNode deviceTreeNode = FindDeviceNode(SelectDeviceNode);
                    if (deviceTreeNode == null) return;                              // 找不到设备信息

                    TreeNodePath treeNodePath = new TreeNodePath(deviceTreeNode.Parent);
                    // 点击了数据内容，分为两种情况，父节点是设备节点，父节点是结构体数据
                    if (SelectDeviceNode.Parent.Tag is DeviceNode)
                    {
                        if (!fmv.CheckRenderPathSame(serverSettings2, treeNodePath))
                            fmv.SetServerSettings(serverSettings2, treeNodePath);

                        await fmv.ChangeSelectNode(new TreeNodePath(deviceTreeNode).GetActualPath(), scalarDataNode);
                    }
                    else
                    {
                        // 点击了结构体列表的子结构体
                        //await fmv.ChangeSelectNode(new TreeNodePath(deviceTreeNode).GetActualPath(), scalarDataNode, index);
                        await fmv.ChangeSelectNode(new TreeNodePath(deviceTreeNode).GetActualPath(), scalarDataNode, -1);
                    }
                }
                else if (SelectDeviceNode.Tag is DeviceNode deviceNode)
                {
                    // 检测当前的路径是否和主界面展示的路径一致，不一致则先切换路径显示操作。
                    if (SelectDeviceNode.Parent.IsServerLocalNode())
                        if (!fmv.CheckRenderPathSame(serverSettings2, new TreeNodePath(SelectDeviceNode.Parent)))
                            fmv.SetServerSettings(serverSettings2, new TreeNodePath(SelectDeviceNode.Parent));

                    await fmv.ChangeSelectNode(new TreeNodePath(SelectDeviceNode).GetActualPath(), null);
                }
            }
        }

        private AvTreeNode FindEdgeNode(AvTreeNode node)
        {
            while (true)
            {
                if (node.IsServerLocalNode()) return node;
                node = node.Parent;
                if (node == null) return node;
            }
        }

        private AvTreeNode FindDeviceNode(AvTreeNode node)
        {
            while (true)
            {
                if (node.IsServerLocalNode()) return null;
                if (node.Tag is DeviceNode) return node;
                node = node.Parent;
                if (node == null) return null;
            }
        }

        private AvTreeNode GetRootTreeNode(AvTreeNode treeNode)
        {
            while (treeNode.Parent != null)
            {
                treeNode = treeNode.Parent;
            }
            return treeNode;
        }

        public ReactiveCommand<TreeView, Unit> TVServerMouseDown { get; }

        void DoTVServerMouseDown(TreeView tv)
        {

        }

        /// <summary>
        /// 编辑设备参数菜单点击事件
        /// </summary>
        private async void OnMenuSBCSTapped(object? sender, TappedEventArgs e)
        {
            if (SelectDeviceNode == null) return;

            try
            {
                // 检查选中的节点类型
                if (SelectDeviceNode.Tag is EdgeServerSettings serverSettings)
                {
                    // 选中的是边缘网关节点，打开服务器配置窗口进行编辑
                    var serverAddWindow = new FormServerAdd();
                    var viewModel = new FormServerAddViewModel(serverAddWindow, serverSettings);

                    var result = await serverAddWindow.ShowDialog<bool?>(MainWindowViewModel.Instance.Main);

                    if (result == true && viewModel.DialogResult == true)
                    {
                        // 更新节点信息
                        SelectDeviceNode.Text = viewModel.EdgeServerSettings.GetDisplayText();
                        SelectDeviceNode.Tag = viewModel.EdgeServerSettings;

                        // 保存到文件
                        SaveServerListToTxt();

                        // 刷新服务器列表
                        LoadServerListFromTxt();

                        var successBox = MessageBoxManager
                                        .GetMessageBoxStandard("成功", "设备参数编辑完成！",
                                        ButtonEnum.Ok, Icon.Success);
                        await successBox.ShowAsync();
                    }
                }
                else if (SelectDeviceNode.Tag is DeviceNode deviceNode)
                {
                    // 选中的是设备节点
                    var infoBox = MessageBoxManager
                                    .GetMessageBoxStandard("设备参数编辑",
                                    $"设备: {deviceNode.Name}\n\n设备参数编辑功能正在开发中...\n请使用边缘网关配置来修改设备参数。",
                                    ButtonEnum.Ok, Icon.Info);
                    await infoBox.ShowAsync();
                }
                else
                {
                    // 其他类型的节点
                    var warningBox = MessageBoxManager
                                    .GetMessageBoxStandard("提示", "请选择边缘网关节点或设备节点进行参数编辑！",
                                    ButtonEnum.Ok, Icon.Warning);
                    await warningBox.ShowAsync();
                }
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                                .GetMessageBoxStandard("错误", $"打开设备参数编辑窗口失败: {ex.Message}",
                                ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }
    }
}
