using ReactiveUI;
using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using tzedgeview.Core;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;

namespace tzedgeview.ViewModels
{
    /// <summary>
    /// 应用程序设置视图模型
    /// </summary>
    public class FormSettingsViewModel : ViewModelBase
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public FormSettingsViewModel()
        {
            InitializeProperties();
            InitializeCommands();
            InitializeCollections();
            LoadSettings();
        }

        #endregion

        #region Properties

        // 常规设置
        private string _language = "zh-CN";
        public string Language
        {
            get => _language;
            set => this.RaiseAndSetIfChanged(ref _language, value);
        }

        private string _theme = "Light";
        public string Theme
        {
            get => _theme;
            set => this.RaiseAndSetIfChanged(ref _theme, value);
        }

        private bool _autoConnectLastServer = true;
        public bool AutoConnectLastServer
        {
            get => _autoConnectLastServer;
            set => this.RaiseAndSetIfChanged(ref _autoConnectLastServer, value);
        }

        private bool _minimizeToTray = false;
        public bool MinimizeToTray
        {
            get => _minimizeToTray;
            set => this.RaiseAndSetIfChanged(ref _minimizeToTray, value);
        }

        private bool _showDebugInfo = false;
        public bool ShowDebugInfo
        {
            get => _showDebugInfo;
            set => this.RaiseAndSetIfChanged(ref _showDebugInfo, value);
        }

        private bool _enableSoundAlert = true;
        public bool EnableSoundAlert
        {
            get => _enableSoundAlert;
            set => this.RaiseAndSetIfChanged(ref _enableSoundAlert, value);
        }

        // 数据刷新设置
        private int _dataRefreshInterval = 1000;
        public int DataRefreshInterval
        {
            get => _dataRefreshInterval;
            set => this.RaiseAndSetIfChanged(ref _dataRefreshInterval, value);
        }

        private int _connectionTimeout = 30;
        public int ConnectionTimeout
        {
            get => _connectionTimeout;
            set => this.RaiseAndSetIfChanged(ref _connectionTimeout, value);
        }

        private int _retryCount = 3;
        public int RetryCount
        {
            get => _retryCount;
            set => this.RaiseAndSetIfChanged(ref _retryCount, value);
        }

        // 日志设置
        private string _logLevel = "Info";
        public string LogLevel
        {
            get => _logLevel;
            set => this.RaiseAndSetIfChanged(ref _logLevel, value);
        }

        private string _logFilePath = "";
        public string LogFilePath
        {
            get => _logFilePath;
            set => this.RaiseAndSetIfChanged(ref _logFilePath, value);
        }

        private int _maxLogFileSize = 10;
        public int MaxLogFileSize
        {
            get => _maxLogFileSize;
            set => this.RaiseAndSetIfChanged(ref _maxLogFileSize, value);
        }

        private bool _enableFileLogging = true;
        public bool EnableFileLogging
        {
            get => _enableFileLogging;
            set => this.RaiseAndSetIfChanged(ref _enableFileLogging, value);
        }

        private bool _autoDeleteOldLogs = true;
        public bool AutoDeleteOldLogs
        {
            get => _autoDeleteOldLogs;
            set => this.RaiseAndSetIfChanged(ref _autoDeleteOldLogs, value);
        }

        // 网络设置
        private bool _useProxy = false;
        public bool UseProxy
        {
            get => _useProxy;
            set => this.RaiseAndSetIfChanged(ref _useProxy, value);
        }

        private string _proxyAddress = "";
        public string ProxyAddress
        {
            get => _proxyAddress;
            set => this.RaiseAndSetIfChanged(ref _proxyAddress, value);
        }

        private int _proxyPort = 8080;
        public int ProxyPort
        {
            get => _proxyPort;
            set => this.RaiseAndSetIfChanged(ref _proxyPort, value);
        }

        private string _proxyUsername = "";
        public string ProxyUsername
        {
            get => _proxyUsername;
            set => this.RaiseAndSetIfChanged(ref _proxyUsername, value);
        }

        private string _proxyPassword = "";
        public string ProxyPassword
        {
            get => _proxyPassword;
            set => this.RaiseAndSetIfChanged(ref _proxyPassword, value);
        }

        // 集合属性
        public ObservableCollection<string> Languages { get; set; }
        public ObservableCollection<string> Themes { get; set; }
        public ObservableCollection<string> LogLevels { get; set; }

        #endregion

        #region Commands

        public ReactiveCommand<Unit, Unit> OkCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> CancelCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ApplyCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ResetCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> BrowseLogFileCommand { get; private set; }

        #endregion

        #region Private Fields

        private ViewerSettings originalSettings;

        #endregion

        #region Methods

        /// <summary>
        /// 初始化属性
        /// </summary>
        private void InitializeProperties()
        {
            LogFilePath = Util.GetFileFullPath("logs/application.log");
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            OkCommand = ReactiveCommand.CreateFromTask(OkAsync);
            CancelCommand = ReactiveCommand.Create(Cancel);
            ApplyCommand = ReactiveCommand.CreateFromTask(ApplyAsync);
            ResetCommand = ReactiveCommand.CreateFromTask(ResetAsync);
            BrowseLogFileCommand = ReactiveCommand.CreateFromTask(BrowseLogFileAsync);
        }

        /// <summary>
        /// 初始化集合
        /// </summary>
        private void InitializeCollections()
        {
            Languages = new ObservableCollection<string>
            {
                "zh-CN", "en-US", "ja-JP", "ko-KR"
            };

            Themes = new ObservableCollection<string>
            {
                "Light", "Dark"
            };

            LogLevels = new ObservableCollection<string>
            {
                "Debug", "Info", "Warning", "Error"
            };
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            if (Util.ViewerSettings != null)
            {
                originalSettings = Util.ViewerSettings;

                // 加载常规设置
                Language = originalSettings.Language;
                Theme = originalSettings.Theme;
                AutoConnectLastServer = originalSettings.AutoConnectLastServer;
                ShowDebugInfo = originalSettings.ShowDebugInfo;
                EnableSoundAlert = originalSettings.EnableSoundAlert;

                // 加载数据刷新设置
                DataRefreshInterval = originalSettings.DataRefreshInterval;

                // 加载日志设置
                LogLevel = originalSettings.LogLevel;
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            if (Util.ViewerSettings != null)
            {
                // 保存常规设置
                Util.ViewerSettings.Language = Language;
                Util.ViewerSettings.Theme = Theme;
                Util.ViewerSettings.AutoConnectLastServer = AutoConnectLastServer;
                Util.ViewerSettings.ShowDebugInfo = ShowDebugInfo;
                Util.ViewerSettings.EnableSoundAlert = EnableSoundAlert;

                // 保存数据刷新设置
                Util.ViewerSettings.DataRefreshInterval = DataRefreshInterval;

                // 保存日志设置
                Util.ViewerSettings.LogLevel = LogLevel;

                // 保存到文件
                Util.ViewerSettings.SaveFile();

                // 应用主题变更
                if (ThemeManager.CurrentTheme != Theme)
                {
                    ThemeManager.SetTheme(Theme);
                }
            }
        }

        /// <summary>
        /// 异步确定操作
        /// </summary>
        /// <returns>任务</returns>
        private async Task OkAsync()
        {
            await ApplyAsync();
            
            // 关闭窗口
            if (GetWindow() is Views.FormSettings window)
            {
                window.SetDialogResult(true);
            }
        }

        /// <summary>
        /// 取消操作
        /// </summary>
        private void Cancel()
        {
            // 关闭窗口
            if (GetWindow() is Views.FormSettings window)
            {
                window.SetDialogResult(false);
            }
        }

        /// <summary>
        /// 异步应用操作
        /// </summary>
        /// <returns>任务</returns>
        private async Task ApplyAsync()
        {
            try
            {
                SaveSettings();
                
                var successBox = MessageBoxManager
                                 .GetMessageBoxStandard("成功", "设置已保存",
                                 ButtonEnum.Ok, Icon.Information);
                await successBox.ShowAsync();
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"保存设置失败: {ex.Message}",
                               ButtonEnum.Ok, Icon.Warning);
                await errorBox.ShowAsync();
            }
        }

        /// <summary>
        /// 异步重置操作
        /// </summary>
        /// <returns>任务</returns>
        private async Task ResetAsync()
        {
            var confirmBox = MessageBoxManager
                             .GetMessageBoxStandard("确认", "是否重置所有设置为默认值？",
                             ButtonEnum.YesNo, Icon.Warning);
            var result = await confirmBox.ShowAsync();
            
            if (result == ButtonResult.Yes)
            {
                ResetToDefaults();
            }
        }

        /// <summary>
        /// 重置为默认值
        /// </summary>
        private void ResetToDefaults()
        {
            Language = "zh-CN";
            Theme = "Light";
            AutoConnectLastServer = true;
            MinimizeToTray = false;
            ShowDebugInfo = false;
            EnableSoundAlert = true;
            DataRefreshInterval = 1000;
            ConnectionTimeout = 30;
            RetryCount = 3;
            LogLevel = "Info";
            MaxLogFileSize = 10;
            EnableFileLogging = true;
            AutoDeleteOldLogs = true;
            UseProxy = false;
            ProxyAddress = "";
            ProxyPort = 8080;
            ProxyUsername = "";
            ProxyPassword = "";
        }

        /// <summary>
        /// 异步浏览日志文件
        /// </summary>
        /// <returns>任务</returns>
        private async Task BrowseLogFileAsync()
        {
            try
            {
                // 这里可以实现文件选择对话框
                var infoBox = MessageBoxManager
                              .GetMessageBoxStandard("提示", "文件选择功能正在开发中...",
                              ButtonEnum.Ok, Icon.Information);
                await infoBox.ShowAsync();
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"浏览文件失败: {ex.Message}",
                               ButtonEnum.Ok, Icon.Warning);
                await errorBox.ShowAsync();
            }
        }

        /// <summary>
        /// 获取窗口对象
        /// </summary>
        /// <returns>窗口对象</returns>
        private object GetWindow()
        {
            // 这里需要实现获取当前窗口的逻辑
            // 在实际应用中，可以通过依赖注入或其他方式获取
            return null;
        }

        #endregion
    }
}
