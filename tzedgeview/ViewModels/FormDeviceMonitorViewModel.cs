using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Input;
using HslTechnology.Edge.Node.Device;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore;
using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using tzedgeview.Core;
using tzedgeview.Views;
using HslCommunication.Enthernet;
using Newtonsoft.Json.Linq;
using System.Reflection.Emit;
using HslCommunication.LogNet;
using System.Threading;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge;
using tzedgeview.UserControls;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;
using DynamicData;
using HslTechnology.Edge.DataBusiness.Time;
using HslTechnology.Edge.Reflection;
using CommunityToolkit.Mvvm.ComponentModel;
using HslCommunication.MQTT;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.Node.Alarm;
using Avalonia.Threading;
using System.Xml.Linq;
using HslCommunication;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.DataBusiness.Oee;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System.Runtime.Serialization;
using static System.Net.Mime.MediaTypeNames;
using System.Reactive;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using ExCSS;

namespace tzedgeview.ViewModels
{
    public class FormDeviceMonitorViewModel : ViewModelBase
    {
        private ObservableCollection<DeviceStatus> _deviceStatus;
        public ObservableCollection<DeviceStatus> deviceStatus
        {
            get => _deviceStatus;
            set => this.RaiseAndSetIfChanged(ref _deviceStatus, value);
        }
        private DeviceStatus _deviceCurrentSelected;    // 当前选择的设备对象
        public DeviceStatus deviceCurrentSelected
        {
            get => _deviceCurrentSelected;
            set => this.RaiseAndSetIfChanged(ref _deviceCurrentSelected, value);
        }                              
        private EdgeServerSettings serverSettingsCurrent = null;                    // 当前绑定的网关的配置信息
        private Thread threadUpdateDeviceStatus;                                    // 后台更新设备状态的线程
        private ILogNet logNet;                                                     // 客户端统一的事务日志对象
        private JToken[] devicesJoTokenArray;                                       // 当前所有的设备列表数组
        private JObject deviceDataPrev;                                             // 最新的设备数据缓存
        private int arrayIndex = -1;                                                // 当为结构体数组时，队列的索引值
        private ScalarDataNode[] scalarDataNodes;
        private MqttClient mqtt;             // 订阅的客户端操作

        public EdgeServerSettings ServerSettings => this.serverSettingsCurrent;

        #region 属性
        private FormDeviceMonitor _Main;
        public FormDeviceMonitor Main
        {
            get => _Main;
            set => this.RaiseAndSetIfChanged(ref _Main, value);
        }

        private string _DeviceFullName;
        public string DeviceFullName
        {
            get => _DeviceFullName;
            set => this.RaiseAndSetIfChanged(ref _DeviceFullName, value);
        }

        private string _DeviceDisplayPath;
        public string DeviceDisplayPath
        {
            get => _DeviceDisplayPath;
            set => this.RaiseAndSetIfChanged(ref _DeviceDisplayPath, value);
        }
        /// <summary>
        /// 设备数量
        /// </summary>
        private string _DeviceCount;
        public string DeviceCount
        {
            get => _DeviceCount;
            set => this.RaiseAndSetIfChanged(ref _DeviceCount, value);
        }
        /// <summary>
        /// 详情页面
        /// </summary>
        private TabItem _TabIndex;
        public TabItem TabIndex
        {
            get => _TabIndex;
            set => this.RaiseAndSetIfChanged(ref _TabIndex, value);
        }
        /// <summary>
        /// 设备名称
        /// </summary>
        private string _DeviceName;
        public string DeviceName
        {
            get => _DeviceName;
            set => this.RaiseAndSetIfChanged(ref _DeviceName, value);
        }
        /// <summary>
        /// 设备开始运行时间
        /// </summary>
        private string _DeviceRunTime;
        public string DeviceRunTime
        {
            get => _DeviceRunTime;
            set => this.RaiseAndSetIfChanged(ref _DeviceRunTime, value);
        }
        /// <summary>
        /// 版本
        /// </summary>
        private string _SoftVer;
        public string SoftVer
        {
            get => _SoftVer;
            set => this.RaiseAndSetIfChanged(ref _SoftVer, value);
        }
        /// <summary>
        /// 网关下设备数量
        /// </summary>
        private string _GWDeviceNum;
        public string GWDeviceNum
        {
            get => _GWDeviceNum;
            set => this.RaiseAndSetIfChanged(ref _GWDeviceNum, value);
        }
        /// <summary>
        /// 成功数量
        /// </summary>
        private string _DEVSuccNum;
        public string DEVSuccNum
        {
            get => _DEVSuccNum;
            set => this.RaiseAndSetIfChanged(ref _DEVSuccNum, value);
        }
        /// <summary>
        /// 暂停数量
        /// </summary>
        private string _DEVPauseNum;
        public string DEVPauseNum
        {
            get => _DEVPauseNum;
            set => this.RaiseAndSetIfChanged(ref _DEVPauseNum, value);
        }
        /// <summary>
        /// 错误数量
        /// </summary>
        private string _DEVErrorNum;
        public string DEVErrorNum
        {
            get => _DEVErrorNum;
            set => this.RaiseAndSetIfChanged(ref _DEVErrorNum, value);
        }
        /// <summary>
        /// 报警数量
        /// </summary>
        private string _DEVAlarmNum;
        public string DEVAlarmNum
        {
            get => _DEVAlarmNum;
            set => this.RaiseAndSetIfChanged(ref _DEVAlarmNum, value);
        }
        /// <summary>
        /// 设备数量
        /// </summary>
        private string _DEVNum;
        public string DEVNum
        {
            get => _DEVNum;
            set => this.RaiseAndSetIfChanged(ref _DEVNum, value);
        }

        private string _HintCount;
        public string HintCount
        {
            get => _HintCount;
            set => this.RaiseAndSetIfChanged(ref _HintCount, value);
        }

        private string _WarnCount;
        public string WarnCount
        {
            get => _WarnCount;
            set => this.RaiseAndSetIfChanged(ref _WarnCount, value);
        }

        private string _ErrorCount;
        public string ErrorCount
        {
            get => _ErrorCount;
            set => this.RaiseAndSetIfChanged(ref _ErrorCount, value);
        }

        private string _FatalCount;
        public string FatalCount
        {
            get => _FatalCount;
            set => this.RaiseAndSetIfChanged(ref _FatalCount, value);
        }

        /// <summary>
        /// 显示路径
        /// </summary>
        private string _DisplayPath;
        public string DisplayPath
        {
            get => _DisplayPath;
            set => this.RaiseAndSetIfChanged(ref _DisplayPath, value);
        }
        /// <summary>
        /// 节点名称
        /// </summary>
        private string _TabDisplayNode;
        public string TabDisplayNode
        {
            get => _TabDisplayNode;
            set => this.RaiseAndSetIfChanged(ref _TabDisplayNode, value);
        }
        /// <summary>
        /// 设备Url
        /// </summary>
        private string _TabDeviceUrl;
        public string TabDeviceUrl
        {
            get => _TabDeviceUrl;
            set => this.RaiseAndSetIfChanged(ref _TabDeviceUrl, value);
        }

        private string _Alarmfilter;
        public string Alarmfilter
        {
            get => _Alarmfilter;
            set => this.RaiseAndSetIfChanged(ref _Alarmfilter, value);
        }

        private ObservableCollection<ScalarDataInfo> _ScalarDataList = new ObservableCollection<ScalarDataInfo>();
        public ObservableCollection<ScalarDataInfo> ScalarDataList
        {
            get => _ScalarDataList;
            set => this.RaiseAndSetIfChanged(ref _ScalarDataList, value);
        }

        private ObservableCollection<MethodDataInfo> _MethodDataList = new ObservableCollection<MethodDataInfo>();
        public ObservableCollection<MethodDataInfo> MethodDataList
        {
            get => _MethodDataList;
            set => this.RaiseAndSetIfChanged(ref _MethodDataList, value);
        }

        private ObservableCollection<ScalarDataInfo> _ScalarNodeList = new ObservableCollection<ScalarDataInfo>();
        public ObservableCollection<ScalarDataInfo> ScalarNodeList
        {
            get => _ScalarNodeList;
            set => this.RaiseAndSetIfChanged(ref _ScalarNodeList, value);
        }

        private ObservableCollection<tzedgeview.Core.AlarmItem> _AlarmDataList = new ObservableCollection<tzedgeview.Core.AlarmItem>();
        public ObservableCollection<tzedgeview.Core.AlarmItem> AlarmDataList
        {
            get => _AlarmDataList;
            set => this.RaiseAndSetIfChanged(ref _AlarmDataList, value);
        }

        private ObservableCollection<string> _OEEItemList = new ObservableCollection<string>();
        public ObservableCollection<string> OEEItemList
        {
            get => _OEEItemList;
            set => this.RaiseAndSetIfChanged(ref _OEEItemList, value);
        }

        private ObservableCollection<OfflineDataInfo> _OfflineDataList = new ObservableCollection<OfflineDataInfo>();
        public ObservableCollection<OfflineDataInfo> OfflineDataList
        {
            get => _OfflineDataList;
            set => this.RaiseAndSetIfChanged(ref _OfflineDataList, value);
        }


        private string _CurrentOEE;
        public string CurrentOEE
        {
            get => _CurrentOEE;
            set => this.RaiseAndSetIfChanged(ref _CurrentOEE, value);
        }

        private string _MoreTimeSec;
        public string MoreTimeSec
        {
            get => _MoreTimeSec;
            set => this.RaiseAndSetIfChanged(ref _MoreTimeSec, value);
        }

        private string _OEEStatus;
        public string OEEStatus
        {
            get => _OEEStatus;
            set => this.RaiseAndSetIfChanged(ref _OEEStatus, value);
        }

        private int _OEEProgress;
        public int OEEProgress
        {
            get => _OEEProgress;
            set => this.RaiseAndSetIfChanged(ref _OEEProgress, value);
        }

        private string _OfflineTimeSec;
        public string OfflineTimeSec
        {
            get => _OfflineTimeSec;
            set => this.RaiseAndSetIfChanged(ref _OfflineTimeSec, value);
        }

        private string _OfflineCount;
        public string OfflineCount
        {
            get => _OfflineCount;
            set => this.RaiseAndSetIfChanged(ref _OfflineCount, value);
        }

        private string _Logs;
        public string Logs
        {
            get => _Logs;
            set => this.RaiseAndSetIfChanged(ref _Logs, value);
        }

        private bool _bolRun = true;
        public bool bolRun
        {
            get => _bolRun;
            set => this.RaiseAndSetIfChanged(ref _bolRun, value);
        }

        private bool _bolComm;
        public bool bolComm
        {
            get => _bolComm;
            set => this.RaiseAndSetIfChanged(ref _bolComm, value);
        }
        

        private string LogDevice;
        private MqttClient Logmqtt;             // 订阅的客户端操作
        #endregion

        private void LoadDeviceScalarNode(JObject json)
        {
            if (ScalarNodeList.Count == 0)
            {
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__name",
                    DisplayName = "设备名称",
                    Unit = string.Empty,
                    dataNode = "String",
                    AccessLevel = "Read",
                    Description = "唯一的设备路径名称"
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__displayName",
                    DisplayName = "设备别名",
                    Unit = string.Empty,
                    dataNode = "String",
                    AccessLevel = "Read",
                    Description = "用于在主界面显示的设备别名信息"
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__description",
                    DisplayName = "设备描述",
                    Unit = string.Empty,
                    dataNode = "String",
                    AccessLevel = "Read",
                    Description = "设备的实时状态信息",
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__failedMsg",
                    DisplayName = "设备错误消息",
                    Unit = string.Empty,
                    dataNode = "String",
                    AccessLevel = "Read",
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__startTime",
                    DisplayName = "开始采集时间",
                    Unit = string.Empty,
                    dataNode = "DateTime",
                    AccessLevel = "Read",
                    Description = "可以理解为网关启动时间"
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__config",
                    DisplayName = "设备配置信息",
                    Unit = string.Empty,
                    dataNode = "String",
                    AccessLevel = "Read",
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__deviceStatus",
                    DisplayName = "设备是否在线",
                    Unit = string.Empty,
                    dataNode = "Bool",
                    AccessLevel = "Read",
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__onlineTime",
                    DisplayName = "设备上线时间",
                    Unit = string.Empty,
                    dataNode = "DateTime",
                    AccessLevel = "Read",
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__activeTime",
                    DisplayName = "设备活动时间",
                    Unit = string.Empty,
                    dataNode = "DateTime",
                    AccessLevel = "Read",
                    Description = "最后一次活动的时间"
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__success",
                    DisplayName = "采集成功次数",
                    dataNode = "Int64",
                    AccessLevel = "Read",
                });
                ScalarNodeList.Add(new ScalarDataInfo()
                {
                    Name = "__failed",
                    DisplayName = "采集失败次数",
                    Unit = string.Empty,
                    dataNode = "Int64",
                    AccessLevel = "Read",
                });
            };
            for (int i = 0; i < ScalarNodeList.Count; i++)
            {
                if (ScalarNodeList[i] is ScalarDataInfo scalarDataNode)
                {
                    if (json.ContainsKey(scalarDataNode.Name))
                    {
                        JToken jToken = json.Property(scalarDataNode.Name).Value;
                        if (jToken.Type != JTokenType.Null)
                        {
                            ScalarNodeList[i].Value = (string)jToken;
                        }
                        else
                        {
                            ScalarNodeList[i].Value = string.Empty;
                        }
                    }
                    else
                        ScalarNodeList[i].Value = string.Empty;
                }
            }
        }

        private ObservableCollection<ISeries> _Series =new ObservableCollection<ISeries>() {
        new ColumnSeries<int>
        {
            Name = "状态1",
            Values = new int[] { 0 }
        },
        };
        public ObservableCollection<ISeries> Series
        {
            get => _Series;
            set => this.RaiseAndSetIfChanged(ref _Series, value);
        }

        private ObservableCollection<Axis> _XAxes = new ObservableCollection<Axis>() {
                new Axis
                {
                    Labels = new string[] { "状态1" }
                }
        };
        public ObservableCollection<Axis> XAxes
        {
            get => _XAxes;
            set => this.RaiseAndSetIfChanged(ref _XAxes, value);
        }

        public int FontSize = 15;

        private SolidColorPaint _TextPaint = new SolidColorPaint()
        {
            Color = SKColors.DarkSlateGray,
            SKTypeface = SKFontManager.Default.MatchCharacter('汉')
        };
        public SolidColorPaint TextPaint
        {
            get => _TextPaint;
            set => this.RaiseAndSetIfChanged(ref _TextPaint, value);
        }

        private SolidColorPaint _TextPaint2 = new SolidColorPaint()
        {
            Color = SKColors.DarkSlateGray,
            SKTypeface = SKFontManager.Default.MatchCharacter('汉')
        };
        public SolidColorPaint TextPaint2
        {
            get => _TextPaint2;
            set => this.RaiseAndSetIfChanged(ref _TextPaint2, value);
        }

        public ReactiveCommand<DeviceStatus, Unit> DoDeviceCommand { get; set; }

        public ReactiveCommand<ScalarDataInfo, Unit> EditDataCommand { get; set; }

        public FormDeviceMonitorViewModel(FormDeviceMonitor frm)
        {
            _Main = frm;
            _Main.DataContext = this;

            deviceStatus = new ObservableCollection<DeviceStatus>();

            Main.lbdevice.AddHandler(ListBox.SelectionChangedEvent, new EventHandler<SelectionChangedEventArgs>(OnDeviceChanged));
            Main.tabStatus.AddHandler(TabControl.SelectionChangedEvent, new EventHandler<SelectionChangedEventArgs>(OnTabChanged));
            Main.cbooee.AddHandler(ComboBox.SelectionChangedEvent, new EventHandler<SelectionChangedEventArgs>(OnCBOeeChanged));
            Main.rbRun.IsCheckedChanged += (s, e) =>
            {
                if (mqtt != null && bolRun)
                {
                    mqtt.UnSubscribeMessage(new string[] { "__log:" + TabDeviceUrl });
                    mqtt.SubscribeMessage(new string[] { "__log.Telegram:" + TabDeviceUrl });

                }
                else
                {
                    mqtt.UnSubscribeMessage(new string[] { "__log.Telegram:" + TabDeviceUrl });
                    mqtt.SubscribeMessage(new string[] { "__log:" + TabDeviceUrl });
                }
            };
            Main.btPause.Click += (s, e) =>
            {
                renderStop = !renderStop;
                if (renderStop)
                {
                    Main.btPause.Content = "继续";
                }
                else
                {
                    Main.btPause.Content = "暂停";
                }
            };
            Main.btClear.Click += (s, e) =>
            {
                Logs = string.Empty;
            };
            DoDeviceCommand = ReactiveCommand.Create<DeviceStatus>(DoStopDeviceCommand);
            EditDataCommand = ReactiveCommand.CreateFromTask<ScalarDataInfo>(EditDataValue);

            threadUpdateDeviceStatus = new Thread(new ThreadStart(ThreadReadServer));
            threadUpdateDeviceStatus.IsBackground = true;
            threadUpdateDeviceStatus.Start();
        }

        private async void DoStopDeviceCommand(DeviceStatus status)
        {
            if (status == null) return;
            if (!string.IsNullOrEmpty(status.DeviceName))
            {
                if (status.IsRun)
                {
                    var rpc = this.serverSettingsCurrent.GetMqttSyncClient();
                    var read = await rpc.ReadRpcAsync<string>("Edge/DeviceStopRequest", new { data = status.DeviceName });
                    if (read.IsSuccess)
                    {
                        var box = MessageBoxManager
                                .GetMessageBoxStandard("提示", $"暂停成功！",
                                ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                        status.IsRun = false;
                        string uri = "/Assets/images/start.png";
                        status.RunPic = new Bitmap(AssetLoader.Open(new Uri("avares://tzedgeview" + uri)));

                    }
                    else
                    {
                        var box = MessageBoxManager
                                .GetMessageBoxStandard("提示", $"暂停失败！",
                                ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                    }
                }
                else
                {
                    var rpc = this.serverSettingsCurrent.GetMqttSyncClient();
                    var read = await rpc.ReadRpcAsync<string>("Edge/DeviceContinueRequest", new { data = status.DeviceName });
                    if (read.IsSuccess)
                    {
                        var box = MessageBoxManager
                                .GetMessageBoxStandard("提示", $"继续成功！",
                                ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                        status.IsRun = true;
                        string uri = "/Assets/images/stop.png";
                        status.RunPic = new Bitmap(AssetLoader.Open(new Uri("avares://tzedgeview" + uri)));

                    }
                    else
                    {
                        var box = MessageBoxManager
                                .GetMessageBoxStandard("提示", $"继续失败！",
                                ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                    }
                }

            }
        }

        /// <summary>
        /// 编辑数据值 - 简化版本，暂时显示提示信息
        /// </summary>
        /// <param name="scalarDataInfo">标量数据信息</param>
        private async Task EditDataValue(ScalarDataInfo scalarDataInfo)
        {
            if (scalarDataInfo == null || this.serverSettingsCurrent == null || deviceCurrentSelected == null)
                return;

            // 检查访问权限 - 从AccessLevel字符串判断
            if (scalarDataInfo.AccessLevel != "ReadWrite")
            {
                var box = MessageBoxManager
                                .GetMessageBoxStandard("提示", "当前的值数据无法写入，不具备写入的权限！",
                                ButtonEnum.Ok, Icon.Warning);
                await box.ShowAsync();
                return;
            }

            try
            {
                // 构建值节点路径
                string valueNode = deviceCurrentSelected.DeviceName + "/" + scalarDataInfo.Name;

                // 暂时显示提示信息，后续可以实现完整的编辑功能
                var infoBox = MessageBoxManager
                                .GetMessageBoxStandard("数据编辑",
                                $"数据节点: {valueNode}\n当前值: {scalarDataInfo.Value}\n\n数据编辑功能正在开发中...",
                                ButtonEnum.Ok, Icon.Info);
                await infoBox.ShowAsync();
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                                .GetMessageBoxStandard("错误", $"编辑数据时发生错误: {ex.Message}",
                                ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        private async void OnCBOeeChanged(object? sender, SelectionChangedEventArgs e)
        {
            if (TabIndex != null && (string)TabIndex.Header != "方法接口")
            {
                await TabControlSelectedIndexChanged(false);
            }
        }

        public void RenderAlarm(JArray array)
        {
            try
            {
                List<HslTechnology.Edge.DataBusiness.Alarm.AlarmItem> list = array.ToObject<List<HslTechnology.Edge.DataBusiness.Alarm.AlarmItem>>();

                int hintCount = 0;
                int warnCount = 0;
                int errorCount = 0;
                int fatalCount = 0;
                for (int i = 0; i < list.Count; i++)
                {
                    if (list[i].Degree == AlarmDegree.Hint) hintCount++;
                    else if (list[i].Degree == AlarmDegree.Warn) warnCount++;
                    else if (list[i].Degree == AlarmDegree.Error) errorCount++;
                    else fatalCount++;
                }
                HintCount = hintCount.ToString();
                WarnCount = warnCount.ToString();
                ErrorCount = errorCount.ToString();
                FatalCount = fatalCount.ToString();

                string selectDegree = string.IsNullOrEmpty(Alarmfilter) ? "全部" : Alarmfilter;
                if (selectDegree == AlarmDegree.Hint.ToString()) list = list.FindAll(m => m.Degree == AlarmDegree.Hint);
                else if (selectDegree == AlarmDegree.Warn.ToString()) list = list.FindAll(m => m.Degree == AlarmDegree.Warn);
                else if (selectDegree == AlarmDegree.Error.ToString()) list = list.FindAll(m => m.Degree == AlarmDegree.Error);
                else if (selectDegree == AlarmDegree.Fatal.ToString()) list = list.FindAll(m => m.Degree == AlarmDegree.Fatal);

                if (AlarmDataList.Count != list.Count)
                {
                    AlarmDataList.Clear();
                    for (int i = 0; i < list.Count; i++)
                    { 
                        tzedgeview.Core.AlarmItem item = ConvertToViewAlarmItem(list[i]);
                        AlarmDataList.Add(item);
                    }
                }
                else
                {
                    for (int i = 0; i < list.Count; i++)
                    {
                        AlarmDataList[i].AlarmCode = list[i].AlarmCode;
                        AlarmDataList[i].AlarmContent = list[i].AlarmContent;
                        AlarmDataList[i].Checked = list[i].Checked;
                        AlarmDataList[i].Degree = list[i].Degree;
                        AlarmDataList[i].DeviceName = list[i].DeviceName;
                        AlarmDataList[i].FinishTime = list[i].FinishTime;
                        AlarmDataList[i].StartTime = list[i].StartTime;
                        AlarmDataList[i].Status = list[i].Status;
                        AlarmDataList[i].TagName = list[i].TagName;
                        AlarmDataList[i].UniqueId = list[i].UniqueId;

                    }
                }
                
            }
            catch (Exception ex)
            {

            }
        }

        public void RenderOee(string device, JArray array)
        {
            try
            {
                List<OEEItem> list = array.ToObject<List<OEEItem>>();
                if (list.Count > 0)
                {
                    var list1 = list.Select(m => m.TagName).ToArray();
                    for (int i = OEEItemList.Count - 1; i >= 0; i--)
                    {
                        bool found = false;
                        foreach (var s in list1)
                        {
                            if (s == OEEItemList[i])
                            {
                                found = true;
                                break;
                            }
                        }
                        if (!found)
                        { 
                            OEEItemList.RemoveAt(i);
                        }
                    }
                    foreach (var s in list1)
                    {
                        bool found = false;
                        foreach (var s1 in OEEItemList)
                        {
                            if (s == s1)
                            {
                                found = true;
                                break;
                            }
                        }
                        if (!found)
                        {
                            OEEItemList.Add(s);
                        }
                    }
                }
                else
                {
                    OEEItemList.Clear();
                }

                if (list == null || list.Count == 0)
                {
                    OEEProgress = 0;
                    MoreTimeSec = "0/0";
                    OEEStatus = "设备状态：";
                    return;
                }

                string selectDegree = CurrentOEE;
                if (string.IsNullOrEmpty(selectDegree))
                {
                    XAxes = new ObservableCollection<Axis>()
                    {
                         new Axis
                        {
                            Labels = new string[] { "状态1" }, LabelsPaint = TextPaint,
                        }
                    };
                    Series = new ObservableCollection<ISeries>() {
                            new ColumnSeries<int>
                            {
                                Name = "状态1",
                                Values = new int[] { 0 }, AnimationsSpeed = TimeSpan.FromSeconds(0),
                            },
                    };
                    return;
                }

                OEEItem item = list.Find(m => m.TagName == selectDegree);
                if (item == null) return;

                OEEProgress = Convert.ToInt32(item.OeePercent * 100);
                MoreTimeSec = $"{Convert.ToInt32(item.WorkingSecondsCount)}/{Convert.ToInt32(item.SecondsCount)}";
                if (item.StatusConsumes?.Length > 0)
                {
                    ItemStatusConsume consume = item.StatusConsumes.FirstOrDefault(m => m.StatusCode == item.OeeStatus);
                    if (consume != null)
                        OEEStatus = consume.Name;
                }

                if (item.StatusConsumes?.Length > 0)
                {
                    int[] values = item.StatusConsumes.Select(m => Convert.ToInt32(m.Consume)).ToArray();
                    string[] texts = item.StatusConsumes.Select(m => m.Name).ToArray();
                    if (Series.Count != texts.Length)
                    {
                        XAxes = new ObservableCollection<Axis>
                        {
                            new Axis{
                            Labels = texts, LabelsPaint = TextPaint,
                            }
                        };
                        Series = new ObservableCollection<ISeries>
                        {
                            new ColumnSeries<int>{
                            Values = values, AnimationsSpeed = TimeSpan.FromSeconds(0),
                            }
                        };
                    }
                    else
                    {
                        for (int i = 0; i < texts.Length; i++)
                        {
                            XAxes[i].Labels = new string[] { texts[i] };
                            Series[i].Values = new int[] { values[i] };
                        }
                    }

                }
                else
                {
                    XAxes = new ObservableCollection<Axis>()
                    {
                         new Axis
                        {
                            Labels = new string[] { "状态1" }, LabelsPaint = TextPaint,
                        }
                    };
                    Series = new ObservableCollection<ISeries>() {
                            new ColumnSeries<int>
                            {
                                Name = "状态1",
                                Values = new int[] { 0 },
                                AnimationsSpeed = TimeSpan.FromSeconds(0),
                            },
                    };
                }
            }
            catch (Exception ex)
            {

            }
        }

        public void RenderMachineOffline(TimeConsume[] consumes)
        {
            if (consumes == null) consumes = new TimeConsume[0];
            DEVAlarmNum = consumes.Length.ToString();
            if (consumes.Length != OfflineDataList.Count)
            {
                OfflineDataList.Clear();
            }
            double seconds = 0;
            for (int i = 0; i < consumes.Length; i++)
            {
                if (OfflineDataList.Count > i)
                {
                    OfflineDataList[i].Index = (i + 1).ToString();
                    OfflineDataList[i].BeginTime = consumes[i].StartTime.ToString();
                    OfflineDataList[i].EndTime = consumes[i].FinishTime.ToString();
                    OfflineDataList[i].InTime = HslTechnologyHelper.GetTimeSpanText(consumes[i].FinishTime - consumes[i].StartTime);
                    seconds += (consumes[i].FinishTime - consumes[i].StartTime).TotalSeconds;
                }
                else
                {
                    OfflineDataInfo odi = new OfflineDataInfo();
                    odi.Index = (i + 1).ToString();
                    odi.BeginTime = consumes[i].StartTime.ToString();
                    odi.EndTime = consumes[i].FinishTime.ToString();
                    odi.InTime = HslTechnologyHelper.GetTimeSpanText(consumes[i].FinishTime - consumes[i].StartTime);
                    OfflineDataList.Add(odi);
                }

            }
            OfflineCount = consumes.Length.ToString();
            OfflineTimeSec = seconds.ToString("F0");
        }

        public async Task RenderDeviceLogs(string device, Func<MqttClient> func)
        {
            if (this.TabDeviceUrl != device)
            {
                this.TabDeviceUrl = device;
            }
            mqtt?.ConnectClose();
            mqtt = func();
            mqtt.OnMqttMessageReceived += MqttClient_OnMqttMessageReceived;
            OperateResult connect = await mqtt.ConnectServerAsync();
            if (connect.IsSuccess)
            {
                Logs = string.Empty;
                if (bolRun)
                {
                    mqtt.SubscribeMessage(new string[] { "__log:" + this.TabDeviceUrl });
                }
                else if (bolComm)
                {
                    mqtt.SubscribeMessage(new string[] { "__log.Telegram:" + this.TabDeviceUrl });
                }
            }
        }

        private void MqttClient_OnMqttMessageReceived(MqttClient client, MqttApplicationMessage message)
        {
            Dispatcher.UIThread.Post(()=>RenderLog(message.Topic, Encoding.UTF8.GetString(message.Payload)));
        }

        private bool renderStop;

        private void RenderLog(string topic, string msg)
        {
            if (string.IsNullOrEmpty(msg)) return;
            if (renderStop == false)
            {
                if (topic.StartsWith("__log.Telegram:"))
                {
                    if (bolComm)
                    {
                        Logs = Logs + msg + Environment.NewLine;
                    }
                }
                else if (topic.StartsWith("__log:"))
                {
                    if (bolRun)
                    {
                        Logs = Logs + msg + Environment.NewLine;
                    }
                }
                Main.txtLogs.SelectionStart = Logs.Length;
                Main.txtLogs.SelectionEnd = Logs.Length;
            }
        }

        public void RenderClose()
        {
            if (!string.IsNullOrEmpty(this.TabDeviceUrl))
            {
                this.mqtt?.ConnectClose();
            }
        }

        private async void OnTabChanged(object? sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var item = e.AddedItems[0] as TabItem;
                if (this.serverSettingsCurrent == null || deviceCurrentSelected == null) return;
                string deviceFullName = deviceCurrentSelected.DeviceName;
                if (deviceFullName != TabDeviceUrl)
                {
                    TabDeviceUrl = deviceFullName;
                }
                if (item == null) return;

                var header = item.Header as string;
                if (header != "设备日志")
                {
                    if (!string.IsNullOrEmpty(LogDevice))
                    {
                        this.LogDevice = string.Empty;
                        this.Logmqtt?.ConnectClose();
                    }
                }        
                if (header == "方法接口")
                {
                    // 请求方法接口信息                
                    var client = this.serverSettingsCurrent.GetMqttSyncClient();
                    var readStatus = await client.ReadRpcAsync<MethodRpcInfo[]>("Edge/GetMethodInfoByDeviceID", new { data = deviceFullName });
                    if (readStatus.IsSuccess)
                    {
                        MethodDataList.Clear();
                        for (int i = 0; i < readStatus.Content.Length; i++)
                        {
                            MethodRpcInfo methodRpc = readStatus.Content[i];
                            MethodDataInfo mdi = new MethodDataInfo();
                            mdi.ApiTopic = methodRpc.RpcApiInfo.ApiTopic;
                            mdi.MethodSignature = methodRpc.RpcApiInfo.MethodSignature;
                            mdi.Description = methodRpc.RpcApiInfo.Description;
                            mdi.Tag = methodRpc;
                            MethodDataList.Add(mdi);
                        }
                    }
                }
                else if (header == "报警信息")
                {
                    // 请求了报警的信息
                    var client = this.serverSettingsCurrent.GetMqttSyncClient();
                    var readAlarms = await client.ReadRpcAsync<JArray>("Business/Alarm/GetAlarms", new { data = deviceFullName });
                    if (!readAlarms.IsSuccess)
                    {
                        var box = MessageBoxManager
                                    .GetMessageBoxStandard("提示", $"服务器报警读取失败！稍后重试！",
                                    ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                        this.serverSettingsCurrent = null;
                    }
                    else
                    {
                        this.RenderAlarm(readAlarms.Content);
                    }
                }
                else if (header == "OEE信息")
                {
                    // 请求了OEE信息
                    var client = this.serverSettingsCurrent.GetMqttSyncClient();
                    var readOees = await client.ReadRpcAsync<JArray>("Business/Oee/GetOees", new { data = deviceFullName });
                    if (!readOees.IsSuccess)
                    {
                        var box = MessageBoxManager
                            .GetMessageBoxStandard("提示", $"服务器OEE读取失败！稍后重试！",
                            ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                        this.serverSettingsCurrent = null;
                    }
                    else
                    {
                        this.RenderOee(deviceFullName, readOees.Content);
                    }
                }
                else if (header == "离线信息")
                {
                    // 请求了掉线信息
                    var client = this.serverSettingsCurrent.GetMqttSyncClient();

                    var readTimes = await client.ReadRpcAsync<TimeConsume[]>("Business/Time/GetDeviceOfflineInformation", new { data = deviceFullName });
                    if (!readTimes.IsSuccess)
                    {
                        var box = MessageBoxManager
                            .GetMessageBoxStandard("提示", $"服务器离线信息读取失败！稍后重试！",
                            ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                        this.serverSettingsCurrent = null;
                    }
                    else
                    {
                        this.RenderMachineOffline(readTimes.Content);
                    }
                }
                else if (header == "设备日志")
                {
                    // 查看设备日志信息
                    await this.RenderDeviceLogs(deviceFullName, this.serverSettingsCurrent.GetMqttClient);
                }
                else if (header == "数据信息" || header == "设备信息")
                {
                    // 设备信息和数据信息本来就是在一直请求的
                    //	var client = this.serverSettingsCurrent.GetMqttSyncClient( );

                    //	var readDevice = await client.ReadRpcAsync<JObject>( "Edge/DeviceData", new { data = deviceFullName } );
                    //	if (!readDevice.IsSuccess)
                    //	{
                    //		MessageBox.Show( "服务器设备数据读取失败！稍后重试！" );
                    //		this.serverSettingsCurrent = null;
                    //	}
                    //	else
                    //	{

                    //	}
                }
            }
        }

        private int edgeServerStatus = -1;
        private void SetEdgeServerStatus(int status, string msg)
        {
            if (edgeServerStatus != status)
            {
                if (status == 1)
                    logNet?.WriteInfo($"Edge[{this.serverSettingsCurrent.Alias}]: {msg}");
                else
                    logNet?.WriteDebug($"Edge[{this.serverSettingsCurrent.Alias}]: {msg}");
            }
            edgeServerStatus = status;
        }

        public Action<string, bool, string> UpdateEdgeStatus { get; set; }

        private async void ThreadReadServer()
        {
            HslTimerTick hslTimerTick = new HslTimerTick();
            while (true)
            {
                Thread.Sleep(20);
                if (!hslTimerTick.IsTickHappen(DateTime.Now)) continue;

                if (this.serverSettingsCurrent != null)
                {
                    string servername = serverSettingsCurrent.Alias;
                    var client = this.serverSettingsCurrent.GetMqttSyncClient();
                    var readStatus = await client.ReadRpcAsync<JObject>("Edge/DeviceData", new { data = "__status" });
                    if (!readStatus.IsSuccess)
                    {
                        SetEdgeServerStatus(0, $"读取服务器数据失败 -> {readStatus.Message}");
                        Dispatcher.UIThread.Post(new Action(() =>
                        {
                            UpdateEdgeStatus?.Invoke(servername, false, readStatus.Message);
                        }));
                        this.serverSettingsCurrent = null;
                        continue;
                    }
                    Dispatcher.UIThread.Post(new Action(() =>
                    {
                        UpdateEdgeStatus?.Invoke(this.serverSettingsCurrent.Alias, true, string.Empty);
                    }));
                    SetEdgeServerStatus(1, $"读取服务器数据成功！");
                    var readDevice = await client.ReadRpcAsync<JObject>("Edge/DeviceData", new { data = this.serverSettingsCurrent.RenderPath.GetActualPath() });
                    if (!readDevice.IsSuccess)
                    {
                        var box = MessageBoxManager
                                    .GetMessageBoxStandard("提示", $"服务器设备数据读取失败！稍后重试！{Environment.NewLine}原因：{readDevice.Message}{Environment.NewLine}API:{this.serverSettingsCurrent.RenderPath.GetActualPath()}",
                                    ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                        this.serverSettingsCurrent = null;
                        continue;
                    }

                    JObject statusJson = readStatus.Content;
                    // 可能刚启动的时候，值还没有赋值
                    if (statusJson["__deviceList"] == null)
                    {
                        var box = MessageBoxManager
                            .GetMessageBoxStandard("提示", "服务器设备数据读取未发现有效设备列表数据！可能设备刚刚启动，稍后重试！",
                            ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                        this.serverSettingsCurrent = null;
                        continue;
                    }

                    List<JToken> devices = statusJson["__deviceList"].Where(m => this.serverSettingsCurrent.RenderPath.IsDeviceNameInPath(m["__name"].Value<string>())).ToList();
                    try
                    {
                        Dispatcher.UIThread.Post(new Action(() =>
                        {
                            int __deviceCount = statusJson["__deviceCount"].Value<int>();
                            int __deviceOnlineCount = statusJson["__deviceOnlineCount"].Value<int>();
                            DEVSuccNum = __deviceOnlineCount.ToString();
                            DEVErrorNum = (__deviceCount - __deviceOnlineCount).ToString();
                            if (statusJson.ContainsKey("__deviceStopCount"))                                       // 兼容新版本的客户端连接旧的服务器
                                DEVPauseNum = (statusJson["__deviceStopCount"].Value<int>()).ToString();

                            long alarmCount = 0;
                            for (int i = 0; i < devices.Count; i++)
                            {
                                if (i < deviceStatus.Count)
                                {
                                    // 这里会报错：
                                    // System.InvalidCastException: 无法将类型为“Newtonsoft.Json.Linq.JValue”的对象强制转换为类型“Newtonsoft.Json.Linq.JObject”。
                                    // 在 HslTechnology.EdgeViewer.Forms.FormDeviceMonitor.<>c__DisplayClass17_0.<ThreadReadServer>b__2()
                                    // 位置 F:\HslProjects\HslTechnology\HslTechnology.EdgeViewer\Forms\FormDeviceMonitor.cs:行号 475
                                    JObject content = (JObject)readDevice.Content[devices[i].Value<string>("__name")];
                                    deviceStatus[i].LoadDeviceData(content);
                                    //alarmCount += deviceStatus[i].AlarmCount;
                                    if (deviceCurrentSelected != null)
                                    {
                                        if (deviceCurrentSelected.DeviceName == devices[i].Value<string>("__name"))
                                        {
                                            this.deviceDataPrev = content;
                                            LoadDeviceScalarNode(content);
                                            RenderMachineData(content);
                                            TabDeviceUrl = deviceCurrentSelected.DeviceName;
                                        }
                                    }
                                }
                            }
                            DEVAlarmNum = alarmCount.ToString();
                        }));

                    }
                    catch (Exception ex)
                    {
                    }

                    try
                    {
                        Dispatcher.UIThread.Post(new Action(UpdateDeviceUI));
                    }
                    catch
                    {

                    }
                }
                else
                {

                }
            }
        }

        private void RenderJArrayToDataGrid(byte[] array)
        {
            if (array == null)
            {
                for (int i = 0; i < ScalarDataList.Count; i++)
                {
                    ScalarDataList[i].Value = string.Empty;
                }
                return;
            }
            if (ScalarDataList.Count != array.Length)
            {
                // 如果两者行数不一致，则重新调整显示的列表信息
                ScalarDataList.Clear();
                for (int i = 0; i < array.Length; i++)
                {
                    ScalarDataInfo sdi = new ScalarDataInfo();
                    sdi.DisplayName = scalarDataNodes[i].GetDisplayName();
                    sdi.Value = string.Empty;
                    sdi.Unit = scalarDataNodes[i].Unit;
                    sdi.dataNode = scalarDataNodes[i].GetDataTypeText();
                    sdi.AccessLevel = scalarDataNodes[i].AccessLevel.ToString();
                    sdi.Description = scalarDataNodes[i].Description;
                    sdi.Tag = scalarDataNodes[i];
                    ScalarDataList.Add(sdi);
                }
            }
            for (int i = 0; i < ScalarDataList.Count; i++)
            {
                if (i < array.Length)
                {
                    ScalarDataList[i].Value = array[i].ToString();
                }
            }
        }

        private void RenderJObjectToDataGrid(JObject json, string dataName)
        {
            if (ScalarDataList.Count > 0)
            {
                if (json.ContainsKey(dataName))
                {
                    if (json.Property(dataName).Value.Type == JTokenType.Null)
                    {
                        ScalarDataList[0].Value = string.Empty;
                    }
                    else
                    {
                        ScalarDataList[0].Value = (string)json.Property(dataName).Value;
                    }
                }
                else
                    ScalarDataList[0].Value = string.Empty;
            }
        }

        private void RenderJArrayToDataGrid(JArray array)
        {
            if (array.Type != JTokenType.Null)
            {
                if (ScalarDataList.Count != array.Count)
                {
                    // 如果两者行数不一致，则重新调整显示的列表信息
                    ScalarDataList.Clear();
                    for (int i = 0; i < array.Count; i++)
                    {
                        ScalarDataInfo sdi = new ScalarDataInfo();
                        sdi.DisplayName = scalarDataNodes[i].GetDisplayName();
                        sdi.Value = string.Empty;
                        sdi.Unit = scalarDataNodes[i].Unit;
                        sdi.dataNode = scalarDataNodes[i].GetDataTypeText();
                        sdi.AccessLevel = scalarDataNodes[i].AccessLevel.ToString();
                        sdi.Description = scalarDataNodes[i].Description;
                        sdi.Tag = scalarDataNodes[i];
                        ScalarDataList.Add(sdi);
                    }
                }
                for (int i = 0; i < ScalarDataList.Count; i++)
                {
                    if (i < array.Count)
                    {
                        ScalarDataList[i].Value = (string)array[i];
                    }
                }
            }
            else
            {
                for (int i = 0; i < ScalarDataList.Count; i++)
                {
                    ScalarDataList[i].Value = string.Empty;
                }
            }
        }

        private ScalarDataNode selectDataNode;

        public void RenderMachineData(JObject json)
        {
            if (selectDataNode == null)
            {
                if (json == null) return; // 显示全部的数据信息
                for (int i = 0; i < ScalarDataList.Count; i++)
                {
                    if (ScalarDataList[i].Tag is ScalarDataNode scalarDataNode)
                    {
                        if (json.ContainsKey(scalarDataNode.Name))
                        {
                            JToken jToken = json.Property(scalarDataNode.Name).Value;
                            if (jToken.Type != JTokenType.Null)
                            {
                                if (scalarDataNode.TransDispalyFunction == null)
                                    ScalarDataList[i].Value = (string)jToken;
                                else
                                    ScalarDataList[i].Value = scalarDataNode.TransDispalyFunction(jToken);
                            }
                            else
                            {
                                ScalarDataList[i].Value = string.Empty;
                            }
                        }
                        else
                            ScalarDataList[i].Value = string.Empty;
                    }
                }
            }
            else if (selectDataNode.DataDimension == HslTechnology.Edge.Node.DataDimension.Scalar)
            {
                if (ScalarDataList.Count == 0) return;
                if (selectDataNode.DataType == HslTechnology.Edge.Node.DataType.Struct) // 处理结构体
                {
                    if (json.ContainsKey(selectDataNode.Name))
                    {
                        JObject token = (JObject)json[selectDataNode.Name];
                        if (json == null) return;
                        for (int i = 0; i < ScalarDataList.Count; i++)
                        {
                            if (ScalarDataList[i].Tag is ScalarDataNode scalarDataNode)
                            {
                                if (json.ContainsKey(scalarDataNode.Name))
                                {
                                    JToken jToken = json.Property(scalarDataNode.Name).Value;
                                    if (jToken.Type != JTokenType.Null)
                                    {
                                        if (scalarDataNode.TransDispalyFunction == null)
                                            ScalarDataList[i].Value = (string)jToken;
                                        else
                                            ScalarDataList[i].Value = scalarDataNode.TransDispalyFunction(jToken);
                                    }
                                    else
                                    {
                                        ScalarDataList[i].Value = string.Empty;
                                    }
                                }
                                else
                                    ScalarDataList[i].Value = string.Empty;
                            }
                        }
                    }
                    else
                    {
                        // 如果选择的标签找不到，则直接把表格所有行设置为空
                        for (int i = 0; i < ScalarDataList.Count; i++)
                        {
                            ScalarDataList[i].Value = string.Empty;
                        }
                    }
                }
                else
                {
                    if (ScalarDataList.Count > 0) //显示除结构体之外的
                    {
                        if (json.ContainsKey(selectDataNode.Name))
                        {
                            if (json.Property(selectDataNode.Name).Value.Type == JTokenType.Null)
                            {
                                ScalarDataList[0].Value = string.Empty;
                            }
                            else
                            {
                                ScalarDataList[0].Value = (string)json.Property(selectDataNode.Name).Value;
                            }
                        }
                        else
                            ScalarDataList[0].Value = string.Empty;
                    }
                }
            }
            else if (selectDataNode.DataDimension == HslTechnology.Edge.Node.DataDimension.One)
            {
                if (json.ContainsKey(selectDataNode.Name))
                {
                    if (selectDataNode.DataType == HslTechnology.Edge.Node.DataType.Byte && json[selectDataNode.Name] is JValue jValue)
                    {
                        if (jValue.Type != JTokenType.Null)
                        {
                            byte[] value = jValue.Value<string>().ToHexBytes();
                            RenderJArrayToDataGrid(value); // 普通的数组
                        }
                        else
                        {
                            byte[] array = null;
                            RenderJArrayToDataGrid(array);
                        }
                    }
                    else
                    {
                        JArray jArray = json[selectDataNode.Name] as JArray;
                        if (jArray == null)
                        {
                            // 如果当前的标签为空的情况
                            byte[] array = null;
                            RenderJArrayToDataGrid(array);
                            return;
                        }

                        if (selectDataNode.DataType == HslTechnology.Edge.Node.DataType.Struct && this.arrayIndex >= 0) // 结构体数组
                        {
                            if (this.arrayIndex >= jArray.Count) return;
                            if (json == null) return;
                            for (int i = 0; i < ScalarDataList.Count; i++)
                            {
                                if (ScalarDataList[i].Tag is ScalarDataNode scalarDataNode)
                                {
                                    if (json.ContainsKey(scalarDataNode.Name))
                                    {
                                        JToken jToken = json.Property(scalarDataNode.Name).Value;
                                        if (jToken.Type != JTokenType.Null)
                                        {
                                            if (scalarDataNode.TransDispalyFunction == null)
                                                ScalarDataList[i].Value = (string)jToken;
                                            else
                                                ScalarDataList[i].Value = scalarDataNode.TransDispalyFunction(jToken);
                                        }
                                        else
                                        {
                                            ScalarDataList[i].Value = string.Empty;
                                        }
                                    }
                                    else
                                        ScalarDataList[i].Value = string.Empty;
                                }
                            }
                        }
                        else
                        {
                            RenderJArrayToDataGrid(jArray); // 普通的数组
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < ScalarDataList.Count; i++)
                    {
                        ScalarDataList[i].Value = string.Empty;
                    }
                }
            }
            else
            {
                RenderJObjectToDataGrid(json, selectDataNode.Name); // 二维数据暂时按照标量处理
            }
        }

        public void RenderDataNodes(string device, ScalarDataNode[] scalarDatas, ScalarDataNode selected = null, int arrayIndex = -1)
        {
            this.scalarDataNodes = scalarDatas;
            this.selectDataNode = selected;
            this.arrayIndex = arrayIndex;
            this.TabDisplayNode = device;
            this.TabDeviceUrl = device;
            if (selected == null)
            {
                // 选择查看了整个数据
                if (ScalarDataList.Count != scalarDatas.Length)
                {
                    ScalarDataList.Clear();
                }
                for (int i = 0; i < scalarDatas.Length; i++)
                {
                    bool found = false;
                    foreach (var d in ScalarDataList)
                    {
                        if (d.DisplayName == scalarDataNodes[i].GetDataTypeText())
                        {
                            d.Value = string.Empty;
                            d.Unit = scalarDataNodes[i].Unit;
                            d.dataNode = scalarDataNodes[i].GetDisplayName();
                            d.AccessLevel = scalarDataNodes[i].AccessLevel.ToString();
                            d.Description = scalarDataNodes[i].Description;
                            d.Tag = scalarDataNodes[i];
                            break;
                        }
                    }
                    if (!found)
                    { 
                        ScalarDataInfo sdi = new ScalarDataInfo();
                        sdi.DisplayName = scalarDataNodes[i].GetDisplayName();
                        sdi.Value = string.Empty;
                        sdi.Unit = scalarDataNodes[i].Unit;
                        sdi.dataNode = scalarDataNodes[i].GetDataTypeText();
                        sdi.AccessLevel = scalarDataNodes[i].AccessLevel.ToString();
                        sdi.Description = scalarDataNodes[i].Description;
                        sdi.Tag = scalarDataNodes[i];
                        ScalarDataList.Add(sdi);
                    }
                }
            }
            else
            {
                // 选择查看了某个数据信息
                this.TabDisplayNode = device + "/" + selected.Name;
                if (selected.DataDimension == HslTechnology.Edge.Node.DataDimension.Scalar)
                {
                    // 结构体和类显示不一样，还没有处理
                    if (selected.DataType == HslTechnology.Edge.Node.DataType.Struct)
                    {
                        if (selected.StructNodes != null)
                        {
                            if (ScalarDataList.Count != selected.StructNodes.Length)
                            {
                                ScalarDataList.Clear();
                            }
                            for (int i = 0; i < selected.StructNodes.Length; i++)
                            {
                                var strNode = selected.StructNodes[i];
                                bool found = false;
                                foreach (var d in ScalarDataList)
                                {
                                    if (d.DisplayName == strNode.DisplayName)
                                    {
                                        d.Value = string.Empty;
                                        d.Unit = strNode.Unit;
                                        d.dataNode = strNode.GetDataTypeText();
                                        d.AccessLevel = strNode.AccessLevel.ToString();
                                        d.Description = strNode.Description;
                                        d.Tag = strNode;
                                        break;
                                    }
                                }
                                if (!found)
                                {
                                    ScalarDataInfo sdi = new ScalarDataInfo();
                                    sdi.DisplayName = selected.GetDisplayName() + "." + strNode.DisplayName;
                                    sdi.Value = string.Empty;
                                    sdi.Unit = strNode.Unit;
                                    sdi.dataNode = strNode.GetDataTypeText();
                                    sdi.AccessLevel = strNode.AccessLevel.ToString();
                                    sdi.Description = strNode.Description;
                                    sdi.Tag = strNode;
                                    ScalarDataList.Add(sdi);
                                }
                                selected.StructNodes[i].DataUrl = selected.Name + "." + selected.StructNodes[i].Name;
                            }
                        }
                    }
                    else
                    {
                        if (ScalarDataList.Count != 1)
                        {
                            ScalarDataList.Clear();
                        }
                        bool found = false;
                        foreach (var d in ScalarDataList)
                        {
                            if (d.DisplayName == selected.DisplayName)
                            {
                                d.Value = string.Empty;
                                d.Unit = selected.Unit;
                                d.dataNode = selected.GetDataTypeText();
                                d.AccessLevel = selected.AccessLevel.ToString();
                                d.Description = selected.Description;
                                d.Tag = selected;
                                break;
                            }
                        }
                        if (!found)
                        {
                            ScalarDataInfo sdi = new ScalarDataInfo();
                            sdi.DisplayName = selected.GetDisplayName();
                            sdi.Value = string.Empty;
                            sdi.Unit = selected.Unit;
                            sdi.dataNode = selected.GetDataTypeText();
                            sdi.AccessLevel = selected.AccessLevel.ToString();
                            sdi.Description = selected.Description;
                            sdi.Tag = selected;
                            ScalarDataList.Add(sdi);
                        }
                    }
                }
                else if (selected.DataDimension == HslTechnology.Edge.Node.DataDimension.One)
                {
                    // 结构体数组
                    if (selected.DataType == HslTechnology.Edge.Node.DataType.Struct && this.arrayIndex >= 0)
                    {
                        if (selected.StructNodes != null)
                        {
                            if (ScalarDataList.Count != selected.StructNodes.Length)
                            {
                                ScalarDataList.Clear();
                            }
                            for (int i = 0; i < selected.StructNodes.Length; i++)
                            {
                                var strNode = selected.StructNodes[i];
                                bool found = false;
                                foreach (var d in ScalarDataList)
                                {
                                    if (d.DisplayName == strNode.DisplayName)
                                    {
                                        d.Value = string.Empty;
                                        d.Unit = strNode.Unit;
                                        d.dataNode = strNode.GetDataTypeText();
                                        d.AccessLevel = strNode.AccessLevel.ToString();
                                        d.Description = strNode.Description;
                                        d.Tag = strNode;
                                        break;
                                    }
                                }
                                if (!found)
                                {
                                    ScalarDataInfo sdi = new ScalarDataInfo();
                                    sdi.DisplayName = selected.GetDisplayName() +
                                        $"[{this.arrayIndex}].{selected.StructNodes[i].GetDisplayName()}"; ;
                                    sdi.Value = string.Empty;
                                    sdi.Unit = strNode.Unit;
                                    sdi.dataNode = strNode.GetDataTypeText();
                                    sdi.AccessLevel = strNode.AccessLevel.ToString();
                                    sdi.Description = strNode.Description;
                                    sdi.Tag = strNode;
                                    ScalarDataList.Add(sdi);
                                }
                                selected.StructNodes[i].DataUrl = selected.Name + $"[{this.arrayIndex}].{selected.StructNodes[i].Name}";
                            }
                        }
                    }
                    // 普通的数组的情况
                    else
                    {
                        if (ScalarDataList.Count != selected.ArrayLength)
                        {
                            ScalarDataList.Clear();
                        }
                        for (int i = 0; i < selected.ArrayLength; i++)
                        {
                            bool found = false;
                            foreach (var d in ScalarDataList)
                            {
                                if (d.DisplayName == selected.GetDisplayName() + $"[{i}]")
                                {
                                    d.Value = string.Empty;
                                    d.Unit = selected.Unit;
                                    d.dataNode = selected.GetDataTypeText();
                                    d.AccessLevel = selected.AccessLevel.ToString();
                                    d.Description = selected.Description;
                                    d.Tag = selected;
                                    break;
                                }
                            }
                            if (!found)
                            {
                                ScalarDataInfo sdi = new ScalarDataInfo();
                                sdi.DisplayName = selected.GetDisplayName() + $"[{i}]";
                                sdi.Value = string.Empty;
                                sdi.Unit = selected.Unit;
                                sdi.dataNode = selected.GetDataTypeText();
                                sdi.AccessLevel = selected.AccessLevel.ToString();
                                sdi.Description = selected.Description;
                                sdi.Tag = selected;
                                ScalarDataList.Add(sdi);
                            }
                        }
                    }
                }
                else // 二维及以上数组不进行显示
                {
                    if (ScalarDataList.Count != 1)
                    {
                        ScalarDataList.Clear();
                    }
                    bool found = false;
                    foreach (var d in ScalarDataList)
                    {
                        if (d.DisplayName == selected.DisplayName)
                        {
                            d.Value = string.Empty;
                            d.Unit = selected.Unit;
                            d.dataNode = selected.GetDataTypeText();
                            d.AccessLevel = selected.AccessLevel.ToString();
                            d.Description = selected.Description;
                            d.Tag = selected;
                            break;
                        }
                    }
                    if (!found)
                    {
                        ScalarDataInfo sdi = new ScalarDataInfo();
                        sdi.DisplayName = selected.GetDisplayName();
                        sdi.Value = string.Empty;
                        sdi.Unit = selected.Unit;
                        sdi.dataNode = selected.GetDataTypeText();
                        sdi.AccessLevel = selected.AccessLevel.ToString();
                        sdi.Description = selected.Description;
                        sdi.Tag = selected;
                        ScalarDataList.Add(sdi);
                    }
                }
            }
        }

        private async void UpdateDeviceUI()
        {
            if (TabIndex != null && (string)TabIndex.Header != "方法接口")
            {
                await TabControlSelectedIndexChanged(false);
            }

        }

        private void OnDeviceChanged(object? sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            { 
                var item = e.AddedItems[0] as DeviceStatus;
                SetNewDeviceStatusControlSelect(item, null, true);
            }
        }

        /// <summary>
		/// 在当前的网关选择下，点击了不同的路径，显示不同的设备名称
		/// </summary>
		/// <param name="serverSettings">当前的选择的服务器的基本信息</param>
		/// <param name="newPaths">选择的路径信息</param>
		public void SetServerSettings(EdgeServerSettings serverSettings, TreeNodePath newPaths)
        {
            if (object.ReferenceEquals(this.serverSettingsCurrent, serverSettings))
            {
                this.serverSettingsCurrent.RenderPath = newPaths ?? new TreeNodePath();
                // 先隐藏多余的控件显示
                List<JToken> renderList = devicesJoTokenArray.Where(m => Utils.IsDeviceNameInPath(m["__name"].Value<string>(), serverSettings.RenderPath.ActualPath)).ToList();
                for (int i = deviceStatus.Count - 1; i >= 0; i--)
                {
                    if (i >= renderList.Count)
                    {
                        deviceStatus[i].IsVisible = false;
                    }
                    else
                    {
                        deviceStatus[i].IsVisible = true;
                        deviceStatus[i].DeviceName = renderList[i]["__name"].Value<string>();
                    }
                }

                if (this.deviceCurrentSelected != null)
                {
                    this.deviceCurrentSelected.IsSelected = false;
                    this.deviceCurrentSelected = null;                              // 清空设备的选择
                    Main.tabStatus.IsVisible = false;
                }

                DeviceDisplayPath = serverSettings.RenderPath.GetDisplayPath();
                DeviceCount = deviceStatus.Count.ToString();
                DEVNum = deviceStatus.Count.ToString();
            }
        }

        /// <summary>
        /// 重新双击了网关的节点后，进行重新连接，并刷新界面控件内容
        /// </summary>
        /// <param name="serverSettings">服务器的配置信息</param>
        public void SetServerSettings(EdgeServerSettings serverSettings)
        {
            this.serverSettingsCurrent?.MqttSyncClientConnectClose();
            this.serverSettingsCurrent = serverSettings;
            SetServerSettings(serverSettings, serverSettings.RenderPath);
        }

        public void RenderDevices(EdgeServerSettings serverSettings, JObject statusJson)
        {
            if (serverSettings == null) return;

            DeviceName = statusJson["__name"].Value<string>();
            DeviceRunTime = statusJson["__startTime"].Value<string>();
            SoftVer = statusJson["__version"].Value<string>();
            GWDeviceNum = statusJson["__deviceCount"].Value<string>();

            serverSettings.RenderPath = new TreeNodePath();
            deviceStatus.Clear();
            devicesJoTokenArray = statusJson["__deviceList"].ToArray();
            for (int i = 0; i < devicesJoTokenArray.Length; i++)
            {
                DeviceStatus statusControl = new DeviceStatus();
                statusControl.IsVisible = true;
                statusControl.DeviceName = devicesJoTokenArray[i]["__name"].Value<string>();
                string uri = "/Assets/images/stop.png";
                statusControl.RunPic = new Bitmap(AssetLoader.Open(new Uri("avares://tzedgeview" + uri)));
                statusControl.IsRun = true;
                statusControl.Tag = statusControl;                
                //statusControl.Tag = devicesJoTokenArray[i];
                deviceStatus.Add(statusControl);
            }
            devicesJoTokenArray = statusJson["__deviceList"].ToArray();

            ResetDeviceControlSelect();
        }

        /// <summary>
        /// 清空设备的选择
        /// </summary>
        public void ResetDeviceControlSelect()
        {
            if (this.deviceCurrentSelected != null)
            {
                this.deviceCurrentSelected.IsSelected = false;
                this.deviceCurrentSelected = null;                              // 清空设备的选择
                Main.tabStatus.IsVisible = true;
            }
        }

        /// <summary>
        /// 清空设备监控界面
        /// </summary>
        public void ClearDeviceMonitor()
        {
            try
            {
                // 停止当前连接
                this.serverSettingsCurrent?.MqttSyncClientConnectClose();
                this.serverSettingsCurrent = null;

                // 清空设备状态列表
                deviceStatus.Clear();

                // 清空设备选择
                ResetDeviceControlSelect();

                // 清空标量数据
                ScalarNodeList.Clear();

                // 清空日志
                Logs = string.Empty;

                // 重置各种计数器
                DeviceName = "";
                DeviceRunTime = "";
                SoftVer = "";
                GWDeviceNum = "0";
                DEVSuccNum = "0";
                DEVPauseNum = "0";
                DEVErrorNum = "0";
                DEVAlarmNum = "0";
                DEVNum = "0";

                // 清空设备路径显示
                DeviceDisplayPath = "";
                DeviceCount = "0";

                // 隐藏状态标签页
                Main.tabStatus.IsVisible = false;
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常
                System.Diagnostics.Debug.WriteLine($"清空设备监控时发生错误: {ex.Message}");
            }
        }

        /// <summary>
		/// 检测指定的路径是否和之前的路径一致，一致的话，返回True，否则返回False
		/// </summary>
		/// <param name="serverSettings">当前的选择的服务器的基本信息</param>
		/// <param name="newPaths">新的路径信息</param>
		/// <returns>是否一致</returns>
		public bool CheckRenderPathSame(EdgeServerSettings serverSettings, TreeNodePath newPaths)
        {
            if (object.ReferenceEquals(this.serverSettingsCurrent, serverSettings))
            {
                return this.serverSettingsCurrent.RenderPath.Equals(newPaths);
            }
            return false;
        }

        private string CurrentDeviceID { get; set; }
        /// <summary>
		/// 更改设备的选择内容，如果设备id不是当前的分类，则无效。并指示选择的数据节点，为null则不查看子数据
		/// </summary>
		/// <param name="deviceId">设备唯一的ID信息</param>
		/// <param name="scalarDataNode">选择的数据标签信息</param>
		/// <param name="arrayIndex">如果当前选择的节点数据为结构体数组，则表示选择的索引</param>
		public async Task ChangeSelectNode(string deviceId, ScalarDataNode scalarDataNode, int arrayIndex = -1)
        {
            // 先查找deviceid是否是列表设备之一，不是的话，直接返回
            bool exsist = false;
            DeviceStatus statusControl = null;
            for (int i = 0; i < deviceStatus.Count; i++)
            {
                if (deviceStatus[i].DeviceName == deviceId)
                {
                    statusControl = deviceStatus[i];
                    exsist = true;
                    break;
                }
            }
            if (!exsist)
            {
                // 选择了不同路径下的设备分类信息
                string newPath = HslTechnologyExtension.GetDevicePathFromDeviceID(deviceId);
                return;
            }

            // 判断是否当前设备选择，不是则重新选择设备信息
            this.arrayIndex = arrayIndex;
            deviceCurrentSelected = statusControl;
            await SetNewDeviceStatusControlSelect(statusControl, scalarDataNode, false, this.arrayIndex);
        }

        private async Task TabControlSelectedIndexChanged(bool isChangeDevice)
        {
            if (this.serverSettingsCurrent == null || deviceCurrentSelected == null) return;
            string deviceFullName = deviceCurrentSelected.DeviceName;
            if (deviceCurrentSelected.DeviceName != TabDeviceUrl)
            {
                TabDeviceUrl = deviceCurrentSelected.DeviceName;
            }
            if (string.IsNullOrEmpty(deviceFullName)) return;

            if (TabIndex != null && (string)TabIndex.Header != "设备日志")
            {
                this.RenderClose();
            }
            if (TabIndex != null && (string)TabIndex.Header == "方法接口")  //方法
            {
                // 请求方法接口信息
                var client = this.serverSettingsCurrent.GetMqttSyncClient();
                var readStatus = await client.ReadRpcAsync<MethodRpcInfo[]>("Edge/GetMethodInfoByDeviceID", new { data = deviceFullName });
                if (readStatus.IsSuccess)
                {
                    MethodDataList.Clear();
                    for (int i = 0; i < readStatus.Content.Length; i++)
                    {
                        MethodRpcInfo methodRpc = readStatus.Content[i];
                        MethodDataInfo mdi = new MethodDataInfo();
                        mdi.ApiTopic = methodRpc.RpcApiInfo.ApiTopic;
                        mdi.MethodSignature = methodRpc.RpcApiInfo.MethodSignature;
                        mdi.Description = methodRpc.RpcApiInfo.Description;
                        mdi.Tag = methodRpc;
                        MethodDataList.Add(mdi);
                    }
                }
            }
            else if (TabIndex != null && (string)TabIndex.Header == "报警信息")   //报警
            {
                // 请求了报警的信息
                var client = this.serverSettingsCurrent.GetMqttSyncClient();
                var readAlarms = await client.ReadRpcAsync<JArray>("Business/Alarm/GetAlarms", new { data = deviceFullName });
                if (!readAlarms.IsSuccess)
                {
                    var box = MessageBoxManager
                        .GetMessageBoxStandard("提示", "服务器报警读取失败！稍后重试！",
                        ButtonEnum.Ok, Icon.Warning);
                    var result = await box.ShowAsync();
                    this.serverSettingsCurrent = null;
                }
                else
                {
                    this.RenderAlarm(readAlarms.Content);
                }
            }
            else if (TabIndex != null && (string)TabIndex.Header == "OEE信息")   //oee
            {
                // 请求了OEE信息
                var client = this.serverSettingsCurrent.GetMqttSyncClient();
                var readOees = await client.ReadRpcAsync<JArray>("Business/Oee/GetOees", new { data = deviceFullName });
                if (!readOees.IsSuccess)
                {
                    var box = MessageBoxManager
                                        .GetMessageBoxStandard("提示", "服务器OEE读取失败！稍后重试！",
                                        ButtonEnum.Ok, Icon.Warning);
                    var result = await box.ShowAsync();
                    this.serverSettingsCurrent = null;
                }
                else
                {
                    this.RenderOee(deviceFullName, readOees.Content);
                }
            }
            else if (TabIndex != null && (string)TabIndex.Header == "离线信息")  //离线
            {
                // 请求了掉线信息
                var client = this.serverSettingsCurrent.GetMqttSyncClient();

                var readTimes = await client.ReadRpcAsync<TimeConsume[]>("Business/Time/GetDeviceOfflineInformation", new { data = deviceFullName });
                if (!readTimes.IsSuccess)
                {
                    var box = MessageBoxManager
                    .GetMessageBoxStandard("提示", "服务器离线信息读取失败！稍后重试！",
                    ButtonEnum.Ok, Icon.Warning);
                    var result = await box.ShowAsync();
                    this.serverSettingsCurrent = null;
                }
                else
                {
                    this.RenderMachineOffline(readTimes.Content);
                }
            }
            else if (TabIndex != null && (string)TabIndex.Header == "设备日志")   //日志
            {
                // 查看设备日志信息

            }
            else if (TabIndex != null && ((string)TabIndex.Header == "数据信息" || (string)TabIndex.Header == "设备信息"))
            {
                // 设备信息和数据信息本来就是在一直请求的
                //var client = this.serverSettingsCurrent.GetMqttSyncClient();

                //var readDevice = await client.ReadRpcAsync<JObject>("Edge/DeviceData", new { data = deviceFullName });
                //if (!readDevice.IsSuccess)
                //{
                //    MessageBox.Show("服务器设备数据读取失败！稍后重试！");
                //    this.serverSettingsCurrent = null;
                //}
                //else
                //{

                //}
            }
        }

        private async Task SetNewDeviceStatusControlSelect(DeviceStatus control, ScalarDataNode scalarDataNode, bool doubleSelectCancel, int arrayIndex = -1)
        {
            if (this.serverSettingsCurrent == null) return;
            this.arrayIndex = arrayIndex;

            if (deviceCurrentSelected == null)
            {
                // 之前没有选择，这是一个新选择
                deviceCurrentSelected = control;
                control.IsSelected = true;

                var rpc = this.serverSettingsCurrent.GetMqttSyncClient();
                var read = await rpc.ReadRpcAsync<ScalarDataNode[]>("Edge/BrowseDeviceDataNodes", new { data = control.DeviceName });
                if (!read.IsSuccess) {
                    var box = MessageBoxManager
                                    .GetMessageBoxStandard("提示", "Failed:" + read.Message,
                                    ButtonEnum.Ok, Icon.Warning);
                    var result = await box.ShowAsync();
                    return;
                }
                ScalarDataList.Clear();
                this.RenderDataNodes(control.DeviceName, read.Content, scalarDataNode, this.arrayIndex);

                await TabControlSelectedIndexChanged(true);

                Main.tabStatus.IsVisible = true;
            }
            else if (object.ReferenceEquals(deviceCurrentSelected, control))
            {
                if (doubleSelectCancel)
                {
                    // 取消选择
                    //ResetDeviceControlSelect();

                    deviceCurrentSelected.IsSelected = false;
                    deviceCurrentSelected = control;
                    control.IsSelected = true;

                    var rpc = this.serverSettingsCurrent.GetMqttSyncClient();
                    var read = await rpc.ReadRpcAsync<ScalarDataNode[]>("Edge/BrowseDeviceDataNodes", new { data = control.DeviceName });
                    if (!read.IsSuccess)
                    {
                        var box = MessageBoxManager
                                        .GetMessageBoxStandard("提示", "Failed:" + read.Message,
                                        ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                    }

                    this.RenderDataNodes(control.DeviceName, read.Content, scalarDataNode, this.arrayIndex);
                    await TabControlSelectedIndexChanged(true);
                }
                else
                {
                    // 切换数据显示操作
                    var rpc = this.serverSettingsCurrent.GetMqttSyncClient();
                    var read = await rpc.ReadRpcAsync<ScalarDataNode[]>("Edge/BrowseDeviceDataNodes", new { data = control.DeviceName });
                    if (!read.IsSuccess)
                    {
                        var box = MessageBoxManager
                                        .GetMessageBoxStandard("提示", "Failed:" + read.Message,
                                        ButtonEnum.Ok, Icon.Warning);
                        var result = await box.ShowAsync();
                        return;
                    }
                    ScalarDataList.Clear();
                    this.RenderDataNodes(control.DeviceName, read.Content, scalarDataNode, arrayIndex);
                    if (this.deviceDataPrev != null) this.RenderMachineData(this.deviceDataPrev);
                    await TabControlSelectedIndexChanged(true);
                }
            }
            else
            {
                // 切换选择
                deviceCurrentSelected.IsSelected = false;
                deviceCurrentSelected = control;
                control.IsSelected = true ;

                var rpc = this.serverSettingsCurrent.GetMqttSyncClient();
                var read = await rpc.ReadRpcAsync<ScalarDataNode[]>("Edge/BrowseDeviceDataNodes", new { data = control.DeviceName });
                if (!read.IsSuccess) {
                    var box = MessageBoxManager
                                    .GetMessageBoxStandard("提示", "Failed:" + read.Message,
                                    ButtonEnum.Ok, Icon.Warning);
                    var result = await box.ShowAsync();
                }

                this.RenderDataNodes(control.DeviceName, read.Content, scalarDataNode, this.arrayIndex);
                await TabControlSelectedIndexChanged(true);

            }
            Main.tabStatus.IsVisible = true;
        }

        /// <summary>
        /// 将HslTechnology.Edge.DataBusiness.Alarm.AlarmItem转换为tzedgeview.Core.AlarmItem
        /// </summary>
        /// <param name="sourceItem">源报警项</param>
        /// <returns>转换后的报警项</returns>
        private tzedgeview.Core.AlarmItem ConvertToViewAlarmItem(HslTechnology.Edge.DataBusiness.Alarm.AlarmItem sourceItem)
        {
            var viewItem = new tzedgeview.Core.AlarmItem();

            viewItem.UniqueId = sourceItem.UniqueId;
            viewItem.AlarmCode = sourceItem.AlarmCode;
            viewItem.AlarmContent = sourceItem.AlarmContent;
            viewItem.StartTime = sourceItem.StartTime;
            viewItem.FinishTime = sourceItem.FinishTime;
            viewItem.Checked = sourceItem.Checked;
            viewItem.Degree = sourceItem.Degree;
            viewItem.Status = sourceItem.Status;
            viewItem.DeviceName = sourceItem.DeviceName;
            viewItem.TagName = sourceItem.TagName;

            return viewItem;
        }
    }

    public partial class DeviceStatus : ObservableObject
    {
        private string _DeviceName;
        public string DeviceName
        {
            get { return _DeviceName; }
            set { SetProperty(ref _DeviceName, value); }
        }

        private string _ActiveTime;
        public string ActiveTime
        {
            get { return _ActiveTime; }
            set { SetProperty(ref _ActiveTime, value); }
        }

        private string _SpendTime;
        public string SpendTime
        {
            get { return _SpendTime; }
            set { SetProperty(ref _SpendTime, value); }
        }

        private string _SuccNum;
        public string SuccNum
        {
            get { return _SuccNum; }
            set { SetProperty(ref _SuccNum, value); }
        }

        private string _FailtNum;
        public string FailtNum
        {
            get { return _FailtNum; }
            set { SetProperty(ref _FailtNum, value); }
        }

        private string _Alarm;
        public string Alarm
        {
            get { return _Alarm; }
            set { SetProperty(ref _Alarm, value); }
        }

        private string _RunStop;
        public string RunStop
        {
            get { return _RunStop; }
            set { SetProperty(ref _RunStop, value); }
        }

        private bool _IsRun;
        public bool IsRun
        {
            get { return _IsRun; }
            set { SetProperty(ref _IsRun, value); }
        }

        private Boolean _IsVisible;
        public Boolean IsVisible
        {
            get { return _IsVisible; }
            set { SetProperty(ref _IsVisible, value); }
        }

        private Boolean _IsSelected;
        public Boolean IsSelected
        {
            get { return _IsSelected; }
            set { SetProperty(ref _IsSelected, value); }
        }

        private Bitmap _RunPic;
        public Bitmap RunPic
        {
            get { return _RunPic; }
            set { SetProperty(ref _RunPic, value); }
        }

        private object _Tag;
        public object Tag
        {
            get { return _Tag; }
            set { SetProperty(ref _Tag, value); }
        }

        public void LoadDeviceData(JObject json)
        {
            if (json == null) return;
            if (json.ContainsKey("__displayName"))
                DeviceName = json["__displayName"].Value<string>();
            else
                DeviceName = json["__name"].Value<string>();
            ActiveTime = json["__activeTime"].Value<string>();
            SpendTime =json["__captureSpendTime"].Value<double>().ToString("F0") + " ms";
            SuccNum = json["__success"].Value<int>().ToString();
            FailtNum = json["__failed"].Value<int>().ToString();

            bool deviceStatus = json["__deviceStatus"].Value<bool>();
            // 提取设备的消息
            if (json.ContainsKey("__deviceMessage"))
            {
                this.Alarm = json["__deviceMessage"].Value<string>();
            }
            else
            {
                this.Alarm = string.Empty;
            }

            if (json.ContainsKey("__statusType") && json["__statusType"].Value<string>() == "OnStop")
            {
                RunStop = "停用";
            }
            else
            {
                if (deviceStatus)
                {
                    // 如果因为暂停导致了时间差，也要显示出来
                    TimeSpan ts = DateTime.Now - Convert.ToDateTime(json["__activeTime"].Value<string>());
                    if (ts.TotalSeconds > 10)
                        RunStop = HslCommunication.BasicFramework.SoftBasic.GetTimeSpanDescription(ts);
                    else
                        RunStop = string.Empty;
                }
                else
                {
                    TimeSpan ts = DateTime.Now - Convert.ToDateTime(json["__activeTime"].Value<string>());
                    RunStop = HslCommunication.BasicFramework.SoftBasic.GetTimeSpanDescription(ts);
                }
            }

        }
    }
}
