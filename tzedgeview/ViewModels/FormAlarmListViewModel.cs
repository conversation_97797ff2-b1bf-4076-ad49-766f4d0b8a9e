using ReactiveUI;
using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Avalonia.Threading;
using HslCommunication.MQTT;
using Newtonsoft.Json.Linq;
using tzedgeview.Core;
using HslTechnology.Edge.Node;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;

namespace tzedgeview.ViewModels
{
    /// <summary>
    /// 报警列表视图模型
    /// </summary>
    public class FormAlarmListViewModel : ViewModelBase
    {
        #region Constructor

        /// <summary>
        /// 实例化一个指定参数的对象
        /// </summary>
        /// <param name="serverSettings">服务器设置</param>
        /// <param name="renderPath">渲染路径</param>
        public FormAlarmListViewModel(EdgeServerSettings serverSettings, TreeNodePath renderPath)
        {
            this.serverSettings = serverSettings;
            this.renderPath = renderPath;
            this.client = serverSettings?.GetMqttSyncClient();
            
            InitializeProperties();
            InitializeCommands();
            InitializeCollections();
        }

        #endregion

        #region Properties

        private string _title = "报警列表";
        public string Title
        {
            get => _title;
            set => this.RaiseAndSetIfChanged(ref _title, value);
        }

        private string _devicePath = "";
        public string DevicePath
        {
            get => _devicePath;
            set => this.RaiseAndSetIfChanged(ref _devicePath, value);
        }

        private string _statusMessage = "就绪";
        public string StatusMessage
        {
            get => _statusMessage;
            set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
        }

        private string _lastUpdateTime = "从未更新";
        public string LastUpdateTime
        {
            get => _lastUpdateTime;
            set => this.RaiseAndSetIfChanged(ref _lastUpdateTime, value);
        }

        private int _totalAlarms = 0;
        public int TotalAlarms
        {
            get => _totalAlarms;
            set => this.RaiseAndSetIfChanged(ref _totalAlarms, value);
        }

        private int _unacknowledgedAlarms = 0;
        public int UnacknowledgedAlarms
        {
            get => _unacknowledgedAlarms;
            set => this.RaiseAndSetIfChanged(ref _unacknowledgedAlarms, value);
        }

        private int _criticalAlarms = 0;
        public int CriticalAlarms
        {
            get => _criticalAlarms;
            set => this.RaiseAndSetIfChanged(ref _criticalAlarms, value);
        }

        private int _warningAlarms = 0;
        public int WarningAlarms
        {
            get => _warningAlarms;
            set => this.RaiseAndSetIfChanged(ref _warningAlarms, value);
        }

        private string _selectedAlarmLevel = "全部";
        public string SelectedAlarmLevel
        {
            get => _selectedAlarmLevel;
            set => this.RaiseAndSetIfChanged(ref _selectedAlarmLevel, value);
        }

        private string _selectedStatus = "全部";
        public string SelectedStatus
        {
            get => _selectedStatus;
            set => this.RaiseAndSetIfChanged(ref _selectedStatus, value);
        }

        private string _selectedTimeRange = "今天";
        public string SelectedTimeRange
        {
            get => _selectedTimeRange;
            set => this.RaiseAndSetIfChanged(ref _selectedTimeRange, value);
        }

        private AlarmItem _selectedAlarm;
        public AlarmItem SelectedAlarm
        {
            get => _selectedAlarm;
            set => this.RaiseAndSetIfChanged(ref _selectedAlarm, value);
        }

        public ObservableCollection<AlarmItem> AlarmList { get; set; }
        public ObservableCollection<string> AlarmLevels { get; set; }
        public ObservableCollection<string> StatusList { get; set; }
        public ObservableCollection<string> TimeRanges { get; set; }

        #endregion

        #region Commands

        public ReactiveCommand<Unit, Unit> RefreshCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ClearCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ExportCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ApplyFilterCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> AcknowledgeCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ViewDetailsCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> CopyCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ExportSelectedCommand { get; private set; }

        #endregion

        #region Private Fields

        private EdgeServerSettings serverSettings;
        private TreeNodePath renderPath;
        private MqttSyncClient client;
        private Timer refreshTimer;
        private bool isMonitoring = false;
        private bool isReading = false;

        #endregion

        #region Methods

        /// <summary>
        /// 初始化属性
        /// </summary>
        private void InitializeProperties()
        {
            if (renderPath != null)
            {
                DevicePath = renderPath.GetDisplayPath();
                Title = $"报警列表 - {DevicePath}";
            }
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            RefreshCommand = ReactiveCommand.CreateFromTask(RefreshAlarmsAsync);
            ClearCommand = ReactiveCommand.CreateFromTask(ClearAlarmsAsync);
            ExportCommand = ReactiveCommand.CreateFromTask(ExportAlarmsAsync);
            ApplyFilterCommand = ReactiveCommand.Create(ApplyFilter);
            AcknowledgeCommand = ReactiveCommand.CreateFromTask(AcknowledgeAlarmAsync);
            ViewDetailsCommand = ReactiveCommand.Create(ViewAlarmDetails);
            CopyCommand = ReactiveCommand.Create(CopyAlarmContent);
            ExportSelectedCommand = ReactiveCommand.CreateFromTask(ExportSelectedAlarmsAsync);
        }

        /// <summary>
        /// 初始化集合
        /// </summary>
        private void InitializeCollections()
        {
            AlarmList = new ObservableCollection<AlarmItem>();
            
            AlarmLevels = new ObservableCollection<string>
            {
                "全部", "严重", "警告", "信息"
            };
            
            StatusList = new ObservableCollection<string>
            {
                "全部", "未确认", "已确认", "已处理"
            };
            
            TimeRanges = new ObservableCollection<string>
            {
                "今天", "昨天", "最近7天", "最近30天", "全部"
            };
        }

        /// <summary>
        /// 启动监控
        /// </summary>
        public void StartMonitoring()
        {
            if (isMonitoring || serverSettings == null) return;

            isMonitoring = true;
            refreshTimer = new Timer(TimerCallback, null, 0, 5000); // 5秒刷新一次
            StatusMessage = "监控已启动";
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            if (!isMonitoring) return;

            isMonitoring = false;
            refreshTimer?.Dispose();
            refreshTimer = null;
            StatusMessage = "监控已停止";
        }

        /// <summary>
        /// 刷新报警数据
        /// </summary>
        public void RefreshAlarms()
        {
            _ = RefreshAlarmsAsync();
        }

        /// <summary>
        /// 设置设备路径
        /// </summary>
        /// <param name="devicePath">设备路径</param>
        public void SetDevicePath(string devicePath)
        {
            DevicePath = devicePath;
            Title = $"报警列表 - {devicePath}";
        }

        /// <summary>
        /// 清除所有报警
        /// </summary>
        public void ClearAllAlarms()
        {
            _ = ClearAlarmsAsync();
        }

        /// <summary>
        /// 确认选中的报警
        /// </summary>
        public void AcknowledgeSelectedAlarms()
        {
            _ = AcknowledgeAlarmAsync();
        }

        /// <summary>
        /// 导出报警数据
        /// </summary>
        public void ExportAlarms()
        {
            _ = ExportAlarmsAsync();
        }

        /// <summary>
        /// 定时器回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void TimerCallback(object state)
        {
            if (isReading) return;
            
            await RefreshAlarmsAsync();
        }

        /// <summary>
        /// 异步刷新报警数据
        /// </summary>
        /// <returns>任务</returns>
        private async Task RefreshAlarmsAsync()
        {
            if (isReading || client == null) return;

            try
            {
                isReading = true;
                
                // 读取报警数据
                var readAlarms = await client.ReadRpcAsync<JArray>("Business/Alarm/GetAlarms", 
                    new { data = renderPath?.GetActualPath() ?? "" });
                
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    if (readAlarms.IsSuccess)
                    {
                        StatusMessage = "报警数据读取成功";
                        
                        // 解析报警数据
                        ParseAlarmData(readAlarms.Content);
                        
                        // 更新统计信息
                        UpdateStatistics();
                    }
                    else
                    {
                        StatusMessage = $"报警数据读取失败: {readAlarms.Message}";
                    }
                    
                    LastUpdateTime = DateTime.Now.ToString("HH:mm:ss");
                });
            }
            catch (Exception ex)
            {
                await Dispatcher.UIThread.InvokeAsync(() =>
                {
                    StatusMessage = $"读取异常: {ex.Message}";
                    LastUpdateTime = DateTime.Now.ToString("HH:mm:ss");
                });
            }
            finally
            {
                isReading = false;
            }
        }

        /// <summary>
        /// 解析报警数据
        /// </summary>
        /// <param name="alarmData">报警数据</param>
        private void ParseAlarmData(JArray alarmData)
        {
            try
            {
                AlarmList.Clear();
                
                if (alarmData != null)
                {
                    int id = 1;
                    foreach (var item in alarmData)
                    {
                        var alarm = new AlarmItem
                        {
                            Id = id++,
                            AlarmTime = item["time"]?.ToString() ?? "",
                            DevicePath = item["path"]?.ToString() ?? "",
                            Level = item["level"]?.ToString() ?? "",
                            AlarmType = item["type"]?.ToString() ?? "",
                            Message = item["message"]?.ToString() ?? "",
                            AckStatus = item["ackStatus"]?.ToString() ?? "未确认",
                            AckTime = item["ackTime"]?.ToString() ?? "",
                            AckUser = item["ackUser"]?.ToString() ?? "",
                            Duration = item["duration"]?.ToString() ?? ""
                        };
                        
                        AlarmList.Add(alarm);
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"解析报警数据失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            TotalAlarms = AlarmList.Count;
            UnacknowledgedAlarms = AlarmList.Count(a => a.AckStatus == "未确认");
            CriticalAlarms = AlarmList.Count(a => a.Level == "严重");
            WarningAlarms = AlarmList.Count(a => a.Level == "警告");
        }

        /// <summary>
        /// 应用过滤器
        /// </summary>
        private void ApplyFilter()
        {
            // 实现过滤逻辑
            StatusMessage = "过滤器已应用";
        }

        /// <summary>
        /// 异步清除报警
        /// </summary>
        /// <returns>任务</returns>
        private async Task ClearAlarmsAsync()
        {
            var confirmBox = MessageBoxManager
                               .GetMessageBoxStandard("确认", "是否确认清除所有报警？",
                               ButtonEnum.YesNo, Icon.Question);
            var result = await confirmBox.ShowAsync();
            
            if (result == ButtonResult.Yes)
            {
                AlarmList.Clear();
                UpdateStatistics();
                StatusMessage = "报警已清除";
            }
        }

        /// <summary>
        /// 异步导出报警
        /// </summary>
        /// <returns>任务</returns>
        private async Task ExportAlarmsAsync()
        {
            // 实现导出逻辑
            StatusMessage = "报警数据导出完成";
            await Task.Delay(100); // 占位符
        }

        /// <summary>
        /// 异步确认报警
        /// </summary>
        /// <returns>任务</returns>
        private async Task AcknowledgeAlarmAsync()
        {
            if (SelectedAlarm != null)
            {
                SelectedAlarm.AckStatus = "已确认";
                SelectedAlarm.AckTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                SelectedAlarm.AckUser = "当前用户";
                
                UpdateStatistics();
                StatusMessage = "报警已确认";
            }
            await Task.Delay(100); // 占位符
        }

        /// <summary>
        /// 查看报警详情
        /// </summary>
        private void ViewAlarmDetails()
        {
            if (SelectedAlarm != null)
            {
                StatusMessage = $"查看报警详情: {SelectedAlarm.Message}";
            }
        }

        /// <summary>
        /// 复制报警内容
        /// </summary>
        private void CopyAlarmContent()
        {
            if (SelectedAlarm != null)
            {
                StatusMessage = "报警内容已复制到剪贴板";
            }
        }

        /// <summary>
        /// 异步导出选中报警
        /// </summary>
        /// <returns>任务</returns>
        private async Task ExportSelectedAlarmsAsync()
        {
            if (SelectedAlarm != null)
            {
                StatusMessage = "选中报警已导出";
            }
            await Task.Delay(100); // 占位符
        }

        #endregion
    }

    #region Data Models

    /// <summary>
    /// 报警项
    /// </summary>
    public class AlarmItem : ReactiveObject
    {
        private int _id;
        public int Id
        {
            get => _id;
            set => this.RaiseAndSetIfChanged(ref _id, value);
        }

        private string _alarmTime = "";
        public string AlarmTime
        {
            get => _alarmTime;
            set => this.RaiseAndSetIfChanged(ref _alarmTime, value);
        }

        private string _devicePath = "";
        public string DevicePath
        {
            get => _devicePath;
            set => this.RaiseAndSetIfChanged(ref _devicePath, value);
        }

        private string _level = "";
        public string Level
        {
            get => _level;
            set => this.RaiseAndSetIfChanged(ref _level, value);
        }

        private string _alarmType = "";
        public string AlarmType
        {
            get => _alarmType;
            set => this.RaiseAndSetIfChanged(ref _alarmType, value);
        }

        private string _message = "";
        public string Message
        {
            get => _message;
            set => this.RaiseAndSetIfChanged(ref _message, value);
        }

        private string _ackStatus = "";
        public string AckStatus
        {
            get => _ackStatus;
            set => this.RaiseAndSetIfChanged(ref _ackStatus, value);
        }

        private string _ackTime = "";
        public string AckTime
        {
            get => _ackTime;
            set => this.RaiseAndSetIfChanged(ref _ackTime, value);
        }

        private string _ackUser = "";
        public string AckUser
        {
            get => _ackUser;
            set => this.RaiseAndSetIfChanged(ref _ackUser, value);
        }

        private string _duration = "";
        public string Duration
        {
            get => _duration;
            set => this.RaiseAndSetIfChanged(ref _duration, value);
        }
    }

    #endregion
}
