using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using Avalonia.Controls;
using Avalonia.Interactivity;
using HslCommunication;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Plugins;
using MsBox.Avalonia;
using MsBox.Avalonia.Enums;
using tzedgeview.Views;
using tzedgeview.Core;

namespace tzedgeview.ViewModels
{
    public class FormNodeSettingsViewModel : INotifyPropertyChanged
    {
        private FormNodeSettings _main;
        private EdgeServerSettings _serverSettings;
        private XElement _xmlSettings;
        private bool _isModified = false;
        private TreeViewItem _selectedNode;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 主窗口引用
        /// </summary>
        public FormNodeSettings Main => _main;

        /// <summary>
        /// 服务器设置
        /// </summary>
        public EdgeServerSettings ServerSettings => _serverSettings;

        /// <summary>
        /// XML配置
        /// </summary>
        public XElement XmlSettings => _xmlSettings;

        /// <summary>
        /// 配置是否已修改
        /// </summary>
        public bool IsModified
        {
            get => _isModified;
            set
            {
                _isModified = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsModified)));
                UpdateModifiedStatus();
            }
        }

        /// <summary>
        /// 当前选中的节点
        /// </summary>
        public TreeViewItem SelectedNode
        {
            get => _selectedNode;
            set
            {
                _selectedNode = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(SelectedNode)));
                OnSelectedNodeChanged();
            }
        }

        /// <summary>
        /// 设备节点集合
        /// </summary>
        public ObservableCollection<TreeViewItem> DeviceNodes { get; set; }

        /// <summary>
        /// 备用配置节点集合
        /// </summary>
        public ObservableCollection<TreeViewItem> BackupNodes { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="main">主窗口</param>
        /// <param name="serverSettings">服务器设置</param>
        /// <param name="xmlSettings">XML配置</param>
        public FormNodeSettingsViewModel(FormNodeSettings main, EdgeServerSettings serverSettings, XElement xmlSettings)
        {
            _main = main;
            _serverSettings = serverSettings;
            _xmlSettings = xmlSettings;

            DeviceNodes = new ObservableCollection<TreeViewItem>();
            BackupNodes = new ObservableCollection<TreeViewItem>();

            InitializeWindow();
            LoadConfiguration();
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        private void InitializeWindow()
        {
            // 设置窗口标题
            Main.Title = $"设备采集参数配置 - {(_serverSettings?.GetDisplayText() ?? "新配置")}";

            // 绑定事件
            Main.BtnSave.Click += OnSaveClicked;
            Main.BtnDownload.Click += OnDownloadClicked;
            Main.BtnRefresh.Click += OnRefreshClicked;
            Main.BtnApplyXml.Click += OnApplyXmlClicked;

            // 绑定右键菜单事件
            Main.MnuAddDevice.Click += OnAddDeviceClicked;
            Main.MnuAddGroup.Click += OnAddGroupClicked;
            Main.MnuAddRequest.Click += OnAddRequestClicked;
            Main.MnuEdit.Click += OnEditClicked;
            Main.MnuDelete.Click += OnDeleteClicked;
            Main.MnuCopy.Click += OnCopyClicked;
            Main.MnuPaste.Click += OnPasteClicked;

            // 绑定树视图事件
            Main.TvDeviceConfig.SelectionChanged += OnTreeSelectionChanged;

            // 设置数据源
            Main.TvDeviceConfig.ItemsSource = DeviceNodes;
            Main.TvBackupConfig.ItemsSource = BackupNodes;

            // 窗口关闭事件
            Main.Closing += OnWindowClosing;
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                // 清空现有节点
                DeviceNodes.Clear();
                BackupNodes.Clear();

                // 加载设备配置树
                LoadDeviceConfigTree();

                // 加载备用配置
                LoadBackupConfigs();

                // 更新XML显示
                UpdateXmlDisplay();

                // 更新状态
                UpdateStatus("配置加载完成");
                UpdateDeviceCount();
            }
            catch (Exception ex)
            {
                ShowError($"加载配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载设备配置树
        /// </summary>
        private void LoadDeviceConfigTree()
        {
            if (_xmlSettings == null) return;

            // 创建根节点
            var rootNodes = new List<TreeViewItem>();

            // 解析XML并创建树节点
            foreach (var element in _xmlSettings.Elements())
            {
                var treeItem = CreateTreeItemFromXml(element);
                if (treeItem != null)
                {
                    rootNodes.Add(treeItem);
                }
            }

            // 添加到集合
            foreach (var node in rootNodes)
            {
                DeviceNodes.Add(node);
            }
        }

        /// <summary>
        /// 从XML创建树节点
        /// </summary>
        /// <param name="element">XML元素</param>
        /// <returns>树节点</returns>
        private TreeViewItem CreateTreeItemFromXml(XElement element)
        {
            var item = new TreeViewItem();
            item.Header = element.Attribute("Name")?.Value ?? element.Name.LocalName;
            item.Tag = element;

            // 递归创建子节点
            foreach (var child in element.Elements())
            {
                var childItem = CreateTreeItemFromXml(child);
                if (childItem != null)
                {
                    item.Items.Add(childItem);
                }
            }

            return item;
        }

        /// <summary>
        /// 加载备用配置
        /// </summary>
        private void LoadBackupConfigs()
        {
            // 创建备用配置节点
            var standbyItem = new TreeViewItem
            {
                Header = "XmlStandby",
                Tag = "XmlStandby"
            };

            var historyItem = new TreeViewItem
            {
                Header = "XmlHistory", 
                Tag = "XmlHistory"
            };

            BackupNodes.Add(standbyItem);
            BackupNodes.Add(historyItem);
        }

        /// <summary>
        /// 更新XML显示
        /// </summary>
        private void UpdateXmlDisplay()
        {
            if (_xmlSettings != null)
            {
                Main.TxtXmlConfig.Text = _xmlSettings.ToString();
            }
        }

        /// <summary>
        /// 树选择改变事件
        /// </summary>
        private void OnTreeSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0 && e.AddedItems[0] is TreeViewItem item)
            {
                SelectedNode = item;
                UpdateNodePath(item);
            }
        }

        /// <summary>
        /// 选中节点改变
        /// </summary>
        private void OnSelectedNodeChanged()
        {
            if (SelectedNode?.Tag is XElement element)
            {
                ShowNodeProperties(element);
            }
            else
            {
                ClearPropertyPanel();
            }
        }

        /// <summary>
        /// 显示节点属性
        /// </summary>
        /// <param name="element">XML元素</param>
        private void ShowNodeProperties(XElement element)
        {
            // 清空属性面板
            Main.SpPropertyPanel.Children.Clear();

            // 根据节点类型显示不同的属性编辑器
            var nodeType = element.Name.LocalName;
            
            switch (nodeType)
            {
                case "DeviceNode":
                    ShowDeviceNodeProperties(element);
                    break;
                case "GroupNode":
                    ShowGroupNodeProperties(element);
                    break;
                case "ScalarReadRequest":
                    ShowScalarRequestProperties(element);
                    break;
                default:
                    ShowGenericProperties(element);
                    break;
            }
        }

        /// <summary>
        /// 显示设备节点属性
        /// </summary>
        private void ShowDeviceNodeProperties(XElement element)
        {
            var panel = new StackPanel();
            
            // 设备名称
            AddPropertyEditor(panel, "设备名称", "Name", element.Attribute("Name")?.Value ?? "");
            
            // 设备描述
            AddPropertyEditor(panel, "设备描述", "Description", element.Attribute("Description")?.Value ?? "");
            
            // 设备类型
            AddPropertyEditor(panel, "设备类型", "DeviceType", element.Attribute("DeviceType")?.Value ?? "");
            
            // IP地址
            AddPropertyEditor(panel, "IP地址", "IpAddress", element.Attribute("IpAddress")?.Value ?? "");
            
            // 端口
            AddPropertyEditor(panel, "端口", "Port", element.Attribute("Port")?.Value ?? "");

            Main.SpPropertyPanel.Children.Add(panel);
        }

        /// <summary>
        /// 显示分组节点属性
        /// </summary>
        private void ShowGroupNodeProperties(XElement element)
        {
            var panel = new StackPanel();

            // 分组名称
            AddPropertyEditor(panel, "分组名称", "Name", element.Attribute("Name")?.Value ?? "");

            // 分组描述
            AddPropertyEditor(panel, "分组描述", "Description", element.Attribute("Description")?.Value ?? "");

            Main.SpPropertyPanel.Children.Add(panel);
        }

        /// <summary>
        /// 显示标量请求属性
        /// </summary>
        private void ShowScalarRequestProperties(XElement element)
        {
            var panel = new StackPanel();

            // 请求名称
            AddPropertyEditor(panel, "请求名称", "Name", element.Attribute("Name")?.Value ?? "");

            // 请求描述
            AddPropertyEditor(panel, "请求描述", "Description", element.Attribute("Description")?.Value ?? "");

            // 数据地址
            AddPropertyEditor(panel, "数据地址", "Address", element.Attribute("Address")?.Value ?? "");

            // 数据类型
            AddPropertyEditor(panel, "数据类型", "DataTypeCode", element.Attribute("DataTypeCode")?.Value ?? "");

            Main.SpPropertyPanel.Children.Add(panel);
        }

        /// <summary>
        /// 显示通用属性
        /// </summary>
        private void ShowGenericProperties(XElement element)
        {
            var panel = new StackPanel();

            // 显示所有属性
            foreach (var attr in element.Attributes())
            {
                AddPropertyEditor(panel, attr.Name.LocalName, attr.Name.LocalName, attr.Value);
            }

            Main.SpPropertyPanel.Children.Add(panel);
        }

        /// <summary>
        /// 添加属性编辑器
        /// </summary>
        private void AddPropertyEditor(StackPanel parent, string displayName, string propertyName, string value)
        {
            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.Margin = new Avalonia.Thickness(0, 5);

            var label = new TextBlock
            {
                Text = displayName + ":",
                VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center
            };
            Grid.SetColumn(label, 0);

            var textBox = new TextBox
            {
                Text = value,
                Tag = propertyName
            };
            textBox.TextChanged += OnPropertyValueChanged;
            Grid.SetColumn(textBox, 1);

            grid.Children.Add(label);
            grid.Children.Add(textBox);
            parent.Children.Add(grid);
        }

        /// <summary>
        /// 属性值改变事件
        /// </summary>
        private void OnPropertyValueChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox && SelectedNode?.Tag is XElement element)
            {
                var propertyName = textBox.Tag?.ToString();
                if (!string.IsNullOrEmpty(propertyName))
                {
                    // 更新XML属性
                    element.SetAttributeValue(propertyName, textBox.Text);
                    
                    // 更新节点显示
                    if (propertyName == "Name")
                    {
                        SelectedNode.Header = textBox.Text;
                    }
                    
                    // 标记为已修改
                    IsModified = true;
                }
            }
        }

        /// <summary>
        /// 清空属性面板
        /// </summary>
        private void ClearPropertyPanel()
        {
            Main.SpPropertyPanel.Children.Clear();
            var textBlock = new TextBlock
            {
                Text = "请在左侧选择要配置的节点",
                HorizontalAlignment = Avalonia.Layout.HorizontalAlignment.Center,
                VerticalAlignment = Avalonia.Layout.VerticalAlignment.Center,
                FontSize = 16,
                Foreground = Avalonia.Media.Brushes.Gray
            };
            Main.SpPropertyPanel.Children.Add(textBlock);
        }

        /// <summary>
        /// 更新节点路径
        /// </summary>
        private void UpdateNodePath(TreeViewItem item)
        {
            var path = GetNodePath(item);
            Main.TxtNodePath.Text = path;
        }

        /// <summary>
        /// 获取节点路径
        /// </summary>
        private string GetNodePath(TreeViewItem item)
        {
            var path = new List<string>();
            var current = item;
            
            while (current != null)
            {
                path.Insert(0, current.Header?.ToString() ?? "");
                current = current.Parent as TreeViewItem;
            }
            
            return string.Join("/", path);
        }

        /// <summary>
        /// 更新修改状态
        /// </summary>
        private void UpdateModifiedStatus()
        {
            Main.TxtModified.Text = IsModified ? "是" : "否";
            Main.TxtModified.Foreground = IsModified ? Avalonia.Media.Brushes.Red : Avalonia.Media.Brushes.Green;
        }

        /// <summary>
        /// 更新设备数量
        /// </summary>
        private void UpdateDeviceCount()
        {
            var count = CountDeviceNodes(DeviceNodes);
            Main.TxtDeviceCount.Text = count.ToString();
        }

        /// <summary>
        /// 统计设备节点数量
        /// </summary>
        private int CountDeviceNodes(IEnumerable<TreeViewItem> nodes)
        {
            int count = 0;
            foreach (var node in nodes)
            {
                if (node.Tag is XElement element && element.Name.LocalName == "DeviceNode")
                {
                    count++;
                }
                
                if (node.Items.Count > 0)
                {
                    count += CountDeviceNodes(node.Items.Cast<TreeViewItem>());
                }
            }
            return count;
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        private void UpdateStatus(string message)
        {
            Main.TxtStatus.Text = message;
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        private async void ShowError(string message)
        {
            var errorBox = MessageBoxManager
                            .GetMessageBoxStandard("错误", message,
                            ButtonEnum.Ok, Icon.Error);
            await errorBox.ShowAsync();
        }

        /// <summary>
        /// 显示信息
        /// </summary>
        private async void ShowInfo(string message)
        {
            var infoBox = MessageBoxManager
                            .GetMessageBoxStandard("信息", message,
                            ButtonEnum.Ok, Icon.Info);
            await infoBox.ShowAsync();
        }

        #region 事件处理

        /// <summary>
        /// 保存点击事件
        /// </summary>
        private async void OnSaveClicked(object sender, RoutedEventArgs e)
        {
            try
            {
                // 更新XML配置
                UpdateXmlDisplay();
                
                // 标记为未修改
                IsModified = false;
                
                UpdateStatus("配置已保存");
                ShowInfo("配置保存成功！");
            }
            catch (Exception ex)
            {
                ShowError($"保存配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 下载到设备点击事件
        /// </summary>
        private async void OnDownloadClicked(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_serverSettings == null)
                {
                    ShowError("未连接到边缘网关设备！");
                    return;
                }

                UpdateStatus("正在下载配置到设备...");
                
                // 这里应该实现实际的下载逻辑
                // var client = _serverSettings.GetMqttSyncClient();
                // var result = await client.ReadRpcAsync<string>("Admin/XmlSettingsModify", new { data = _xmlSettings.ToString() });
                
                // 暂时显示成功信息
                UpdateStatus("配置下载完成");
                ShowInfo("配置已成功下载到设备！");
                IsModified = false;
            }
            catch (Exception ex)
            {
                UpdateStatus("下载失败");
                ShowError($"下载配置到设备失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新点击事件
        /// </summary>
        private async void OnRefreshClicked(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在刷新配置...");
                LoadConfiguration();
                UpdateStatus("配置刷新完成");
            }
            catch (Exception ex)
            {
                ShowError($"刷新配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用XML修改点击事件
        /// </summary>
        private async void OnApplyXmlClicked(object sender, RoutedEventArgs e)
        {
            try
            {
                var xmlText = Main.TxtXmlConfig.Text;
                if (string.IsNullOrEmpty(xmlText))
                {
                    ShowError("XML配置不能为空！");
                    return;
                }

                // 解析XML
                var newXml = XElement.Parse(xmlText);
                _xmlSettings = newXml;

                // 重新加载配置树
                LoadDeviceConfigTree();

                IsModified = true;
                UpdateStatus("XML配置已应用");
                ShowInfo("XML配置修改已应用，请切换回可视化界面查看！");
            }
            catch (Exception ex)
            {
                ShowError($"应用XML配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加设备点击事件
        /// </summary>
        private async void OnAddDeviceClicked(object sender, RoutedEventArgs e)
        {
            ShowInfo("添加设备功能正在开发中...");
        }

        /// <summary>
        /// 添加分组点击事件
        /// </summary>
        private async void OnAddGroupClicked(object sender, RoutedEventArgs e)
        {
            ShowInfo("添加分组功能正在开发中...");
        }

        /// <summary>
        /// 添加请求点击事件
        /// </summary>
        private async void OnAddRequestClicked(object sender, RoutedEventArgs e)
        {
            ShowInfo("添加数据请求功能正在开发中...");
        }

        /// <summary>
        /// 编辑点击事件
        /// </summary>
        private async void OnEditClicked(object sender, RoutedEventArgs e)
        {
            if (SelectedNode == null)
            {
                ShowError("请先选择要编辑的节点！");
                return;
            }
            
            ShowInfo("节点编辑功能正在开发中...");
        }

        /// <summary>
        /// 删除点击事件
        /// </summary>
        private async void OnDeleteClicked(object sender, RoutedEventArgs e)
        {
            if (SelectedNode == null)
            {
                ShowError("请先选择要删除的节点！");
                return;
            }
            
            ShowInfo("节点删除功能正在开发中...");
        }

        /// <summary>
        /// 复制点击事件
        /// </summary>
        private async void OnCopyClicked(object sender, RoutedEventArgs e)
        {
            ShowInfo("复制功能正在开发中...");
        }

        /// <summary>
        /// 粘贴点击事件
        /// </summary>
        private async void OnPasteClicked(object sender, RoutedEventArgs e)
        {
            ShowInfo("粘贴功能正在开发中...");
        }

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        private async void OnWindowClosing(object sender, WindowClosingEventArgs e)
        {
            if (IsModified)
            {
                var confirmBox = MessageBoxManager
                                .GetMessageBoxStandard("确认", 
                                "当前的配置信息已经修改过，但还未保存，是否直接退出并放弃？",
                                ButtonEnum.YesNo, Icon.Warning);
                
                var result = await confirmBox.ShowAsync();
                
                if (result != ButtonResult.Yes)
                {
                    e.Cancel = true;
                }
            }
        }

        #endregion
    }
}
