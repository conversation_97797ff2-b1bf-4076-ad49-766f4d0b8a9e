using ReactiveUI;
using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using System.Linq;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;

namespace tzedgeview.ViewModels
{
    /// <summary>
    /// 设备模板管理视图模型
    /// </summary>
    public class FormTemplateDeviceViewModel : ViewModelBase
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public FormTemplateDeviceViewModel()
        {
            InitializeProperties();
            InitializeCommands();
            InitializeCollections();
            LoadTemplates();
        }

        #endregion

        #region Properties

        private string _searchText = "";
        public string SearchText
        {
            get => _searchText;
            set => this.RaiseAndSetIfChanged(ref _searchText, value);
        }

        private DeviceTemplate _selectedTemplate;
        public DeviceTemplate SelectedTemplate
        {
            get => _selectedTemplate;
            set
            {
                this.RaiseAndSetIfChanged(ref _selectedTemplate, value);
                LoadTemplateDetails();
                this.RaisePropertyChanged(nameof(HasSelectedTemplate));
            }
        }

        public bool HasSelectedTemplate => SelectedTemplate != null;

        private string _templateName = "";
        public string TemplateName
        {
            get => _templateName;
            set
            {
                this.RaiseAndSetIfChanged(ref _templateName, value);
                HasUnsavedChanges = true;
            }
        }

        private string _deviceType = "";
        public string DeviceType
        {
            get => _deviceType;
            set
            {
                this.RaiseAndSetIfChanged(ref _deviceType, value);
                HasUnsavedChanges = true;
            }
        }

        private string _manufacturer = "";
        public string Manufacturer
        {
            get => _manufacturer;
            set
            {
                this.RaiseAndSetIfChanged(ref _manufacturer, value);
                HasUnsavedChanges = true;
            }
        }

        private string _model = "";
        public string Model
        {
            get => _model;
            set
            {
                this.RaiseAndSetIfChanged(ref _model, value);
                HasUnsavedChanges = true;
            }
        }

        private string _version = "";
        public string Version
        {
            get => _version;
            set
            {
                this.RaiseAndSetIfChanged(ref _version, value);
                HasUnsavedChanges = true;
            }
        }

        private string _creator = "";
        public string Creator
        {
            get => _creator;
            set => this.RaiseAndSetIfChanged(ref _creator, value);
        }

        private string _description = "";
        public string Description
        {
            get => _description;
            set
            {
                this.RaiseAndSetIfChanged(ref _description, value);
                HasUnsavedChanges = true;
            }
        }

        private string _defaultIpAddress = "*************";
        public string DefaultIpAddress
        {
            get => _defaultIpAddress;
            set
            {
                this.RaiseAndSetIfChanged(ref _defaultIpAddress, value);
                HasUnsavedChanges = true;
            }
        }

        private int _defaultPort = 502;
        public int DefaultPort
        {
            get => _defaultPort;
            set
            {
                this.RaiseAndSetIfChanged(ref _defaultPort, value);
                HasUnsavedChanges = true;
            }
        }

        private int _timeoutMs = 3000;
        public int TimeoutMs
        {
            get => _timeoutMs;
            set
            {
                this.RaiseAndSetIfChanged(ref _timeoutMs, value);
                HasUnsavedChanges = true;
            }
        }

        private int _retryCount = 3;
        public int RetryCount
        {
            get => _retryCount;
            set
            {
                this.RaiseAndSetIfChanged(ref _retryCount, value);
                HasUnsavedChanges = true;
            }
        }

        private int _pollingInterval = 1000;
        public int PollingInterval
        {
            get => _pollingInterval;
            set
            {
                this.RaiseAndSetIfChanged(ref _pollingInterval, value);
                HasUnsavedChanges = true;
            }
        }

        private bool _autoReconnect = true;
        public bool AutoReconnect
        {
            get => _autoReconnect;
            set
            {
                this.RaiseAndSetIfChanged(ref _autoReconnect, value);
                HasUnsavedChanges = true;
            }
        }

        private DataPointTemplate _selectedDataPoint;
        public DataPointTemplate SelectedDataPoint
        {
            get => _selectedDataPoint;
            set => this.RaiseAndSetIfChanged(ref _selectedDataPoint, value);
        }

        private string _statusMessage = "就绪";
        public string StatusMessage
        {
            get => _statusMessage;
            set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
        }

        private int _totalTemplates = 0;
        public int TotalTemplates
        {
            get => _totalTemplates;
            set => this.RaiseAndSetIfChanged(ref _totalTemplates, value);
        }

        private int _totalDataPoints = 0;
        public int TotalDataPoints
        {
            get => _totalDataPoints;
            set => this.RaiseAndSetIfChanged(ref _totalDataPoints, value);
        }

        public bool HasUnsavedChanges { get; private set; } = false;

        public ObservableCollection<DeviceTemplate> Templates { get; set; }
        public ObservableCollection<DataPointTemplate> DataPoints { get; set; }
        public ObservableCollection<string> DeviceTypes { get; set; }

        #endregion

        #region Commands

        public ReactiveCommand<Unit, Unit> NewTemplateCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ImportTemplateCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ExportTemplateCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> RefreshCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> EditTemplateCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> CopyTemplateCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> DeleteTemplateCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ExportSelectedTemplateCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> AddDataPointCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> DeleteDataPointCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ImportDataPointsCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> ExportDataPointsCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> EditDataPointCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> CopyDataPointCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> EnableDataPointCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> DisableDataPointCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> SaveCommand { get; private set; }
        public ReactiveCommand<Unit, Unit> CloseCommand { get; private set; }

        #endregion

        #region Methods

        /// <summary>
        /// 初始化属性
        /// </summary>
        private void InitializeProperties()
        {
            Creator = Environment.UserName;
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            NewTemplateCommand = ReactiveCommand.CreateFromTask(NewTemplateAsync);
            ImportTemplateCommand = ReactiveCommand.CreateFromTask(ImportTemplateAsync);
            ExportTemplateCommand = ReactiveCommand.CreateFromTask(ExportTemplateAsync);
            RefreshCommand = ReactiveCommand.Create(LoadTemplates);
            EditTemplateCommand = ReactiveCommand.Create(EditTemplate);
            CopyTemplateCommand = ReactiveCommand.CreateFromTask(CopyTemplateAsync);
            DeleteTemplateCommand = ReactiveCommand.CreateFromTask(DeleteTemplateAsync);
            ExportSelectedTemplateCommand = ReactiveCommand.CreateFromTask(ExportSelectedTemplateAsync);
            AddDataPointCommand = ReactiveCommand.CreateFromTask(AddDataPointAsync);
            DeleteDataPointCommand = ReactiveCommand.CreateFromTask(DeleteDataPointAsync);
            ImportDataPointsCommand = ReactiveCommand.CreateFromTask(ImportDataPointsAsync);
            ExportDataPointsCommand = ReactiveCommand.CreateFromTask(ExportDataPointsAsync);
            EditDataPointCommand = ReactiveCommand.Create(EditDataPoint);
            CopyDataPointCommand = ReactiveCommand.Create(CopyDataPoint);
            EnableDataPointCommand = ReactiveCommand.Create(EnableDataPoint);
            DisableDataPointCommand = ReactiveCommand.Create(DisableDataPoint);
            SaveCommand = ReactiveCommand.CreateFromTask(SaveAsync);
            CloseCommand = ReactiveCommand.Create(Close);
        }

        /// <summary>
        /// 初始化集合
        /// </summary>
        private void InitializeCollections()
        {
            Templates = new ObservableCollection<DeviceTemplate>();
            DataPoints = new ObservableCollection<DataPointTemplate>();
            
            DeviceTypes = new ObservableCollection<string>
            {
                "Modbus TCP", "Modbus RTU", "OPC UA", "Siemens S7", 
                "Mitsubishi", "Omron", "Allen-Bradley", "Schneider",
                "Beckhoff", "Delta", "Panasonic", "Keyence"
            };
        }

        /// <summary>
        /// 加载模板列表
        /// </summary>
        private void LoadTemplates()
        {
            try
            {
                Templates.Clear();
                
                // 添加示例模板
                Templates.Add(new DeviceTemplate
                {
                    Id = Guid.NewGuid(),
                    Name = "Modbus TCP 标准模板",
                    DeviceType = "Modbus TCP",
                    Manufacturer = "通用",
                    Model = "标准",
                    Version = "1.0",
                    Description = "标准Modbus TCP设备模板",
                    DataPointCount = 10,
                    LastModified = DateTime.Now.AddDays(-5),
                    Creator = "系统"
                });

                Templates.Add(new DeviceTemplate
                {
                    Id = Guid.NewGuid(),
                    Name = "西门子S7-1200",
                    DeviceType = "Siemens S7",
                    Manufacturer = "西门子",
                    Model = "S7-1200",
                    Version = "1.2",
                    Description = "西门子S7-1200 PLC模板",
                    DataPointCount = 25,
                    LastModified = DateTime.Now.AddDays(-2),
                    Creator = "管理员"
                });

                TotalTemplates = Templates.Count;
                StatusMessage = $"已加载 {TotalTemplates} 个模板";
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载模板失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 加载模板详情
        /// </summary>
        private void LoadTemplateDetails()
        {
            if (SelectedTemplate == null) return;

            try
            {
                TemplateName = SelectedTemplate.Name;
                DeviceType = SelectedTemplate.DeviceType;
                Manufacturer = SelectedTemplate.Manufacturer;
                Model = SelectedTemplate.Model;
                Version = SelectedTemplate.Version;
                Description = SelectedTemplate.Description;
                Creator = SelectedTemplate.Creator;

                // 加载数据点
                LoadDataPoints();
                
                HasUnsavedChanges = false;
                StatusMessage = $"已加载模板: {SelectedTemplate.Name}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载模板详情失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 加载数据点
        /// </summary>
        private void LoadDataPoints()
        {
            DataPoints.Clear();
            
            // 添加示例数据点
            for (int i = 1; i <= 10; i++)
            {
                DataPoints.Add(new DataPointTemplate
                {
                    Id = Guid.NewGuid(),
                    Name = $"数据点{i}",
                    Address = $"40{i:D3}",
                    DataType = "Int16",
                    AccessType = "ReadWrite",
                    Unit = "",
                    Description = $"数据点{i}的描述",
                    IsEnabled = true
                });
            }

            TotalDataPoints = DataPoints.Count;
        }

        /// <summary>
        /// 异步新建模板
        /// </summary>
        /// <returns>任务</returns>
        private async Task NewTemplateAsync()
        {
            try
            {
                var newTemplate = new DeviceTemplate
                {
                    Id = Guid.NewGuid(),
                    Name = "新建模板",
                    DeviceType = "Modbus TCP",
                    Manufacturer = "",
                    Model = "",
                    Version = "1.0",
                    Description = "",
                    DataPointCount = 0,
                    LastModified = DateTime.Now,
                    Creator = Creator
                };

                Templates.Add(newTemplate);
                SelectedTemplate = newTemplate;
                TotalTemplates = Templates.Count;
                
                StatusMessage = "已创建新模板";
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"创建新模板失败: {ex.Message}",
                               ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        /// <summary>
        /// 异步导入模板
        /// </summary>
        /// <returns>任务</returns>
        private async Task ImportTemplateAsync()
        {
            var infoBox = MessageBoxManager
                          .GetMessageBoxStandard("提示", "导入模板功能正在开发中...",
                          ButtonEnum.Ok, Icon.None);
            await infoBox.ShowAsync();
        }

        /// <summary>
        /// 异步导出模板
        /// </summary>
        /// <returns>任务</returns>
        private async Task ExportTemplateAsync()
        {
            var infoBox = MessageBoxManager
                          .GetMessageBoxStandard("提示", "导出模板功能正在开发中...",
                          ButtonEnum.Ok, Icon.None);
            await infoBox.ShowAsync();
        }

        /// <summary>
        /// 编辑模板
        /// </summary>
        private void EditTemplate()
        {
            if (SelectedTemplate != null)
            {
                StatusMessage = $"正在编辑模板: {SelectedTemplate.Name}";
            }
        }

        /// <summary>
        /// 异步复制模板
        /// </summary>
        /// <returns>任务</returns>
        private async Task CopyTemplateAsync()
        {
            if (SelectedTemplate == null) return;

            try
            {
                var copiedTemplate = new DeviceTemplate
                {
                    Id = Guid.NewGuid(),
                    Name = $"{SelectedTemplate.Name} - 副本",
                    DeviceType = SelectedTemplate.DeviceType,
                    Manufacturer = SelectedTemplate.Manufacturer,
                    Model = SelectedTemplate.Model,
                    Version = SelectedTemplate.Version,
                    Description = SelectedTemplate.Description,
                    DataPointCount = SelectedTemplate.DataPointCount,
                    LastModified = DateTime.Now,
                    Creator = Creator
                };

                Templates.Add(copiedTemplate);
                TotalTemplates = Templates.Count;
                StatusMessage = "模板已复制";
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"复制模板失败: {ex.Message}",
                               ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        /// <summary>
        /// 异步删除模板
        /// </summary>
        /// <returns>任务</returns>
        private async Task DeleteTemplateAsync()
        {
            if (SelectedTemplate == null) return;

            var confirmBox = MessageBoxManager
                             .GetMessageBoxStandard("确认", $"是否确认删除模板 '{SelectedTemplate.Name}'？",
                             ButtonEnum.YesNo, Icon.Question);
            var result = await confirmBox.ShowAsync();
            
            if (result == ButtonResult.Yes)
            {
                Templates.Remove(SelectedTemplate);
                TotalTemplates = Templates.Count;
                StatusMessage = "模板已删除";
            }
        }

        /// <summary>
        /// 异步导出选中模板
        /// </summary>
        /// <returns>任务</returns>
        private async Task ExportSelectedTemplateAsync()
        {
            var infoBox = MessageBoxManager
                          .GetMessageBoxStandard("提示", "导出选中模板功能正在开发中...",
                          ButtonEnum.Ok, Icon.None);
            await infoBox.ShowAsync();
        }

        /// <summary>
        /// 异步添加数据点
        /// </summary>
        /// <returns>任务</returns>
        private async Task AddDataPointAsync()
        {
            try
            {
                var newDataPoint = new DataPointTemplate
                {
                    Id = Guid.NewGuid(),
                    Name = $"新数据点{DataPoints.Count + 1}",
                    Address = "40001",
                    DataType = "Int16",
                    AccessType = "ReadWrite",
                    Unit = "",
                    Description = "",
                    IsEnabled = true
                };

                DataPoints.Add(newDataPoint);
                TotalDataPoints = DataPoints.Count;
                HasUnsavedChanges = true;
                StatusMessage = "已添加新数据点";
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"添加数据点失败: {ex.Message}",
                               ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        /// <summary>
        /// 异步删除数据点
        /// </summary>
        /// <returns>任务</returns>
        private async Task DeleteDataPointAsync()
        {
            if (SelectedDataPoint == null) return;

            var confirmBox = MessageBoxManager
                             .GetMessageBoxStandard("确认", $"是否确认删除数据点 '{SelectedDataPoint.Name}'？",
                             ButtonEnum.YesNo, Icon.Question);
            var result = await confirmBox.ShowAsync();
            
            if (result == ButtonResult.Yes)
            {
                DataPoints.Remove(SelectedDataPoint);
                TotalDataPoints = DataPoints.Count;
                HasUnsavedChanges = true;
                StatusMessage = "数据点已删除";
            }
        }

        /// <summary>
        /// 异步导入数据点
        /// </summary>
        /// <returns>任务</returns>
        private async Task ImportDataPointsAsync()
        {
            var infoBox = MessageBoxManager
                          .GetMessageBoxStandard("提示", "导入数据点功能正在开发中...",
                          ButtonEnum.Ok, Icon.None);
            await infoBox.ShowAsync();
        }

        /// <summary>
        /// 异步导出数据点
        /// </summary>
        /// <returns>任务</returns>
        private async Task ExportDataPointsAsync()
        {
            var infoBox = MessageBoxManager
                          .GetMessageBoxStandard("提示", "导出数据点功能正在开发中...",
                          ButtonEnum.Ok, Icon.Information);
            await infoBox.ShowAsync();
        }

        /// <summary>
        /// 编辑数据点
        /// </summary>
        private void EditDataPoint()
        {
            if (SelectedDataPoint != null)
            {
                StatusMessage = $"正在编辑数据点: {SelectedDataPoint.Name}";
            }
        }

        /// <summary>
        /// 复制数据点
        /// </summary>
        private void CopyDataPoint()
        {
            if (SelectedDataPoint == null) return;

            var copiedDataPoint = new DataPointTemplate
            {
                Id = Guid.NewGuid(),
                Name = $"{SelectedDataPoint.Name}_副本",
                Address = SelectedDataPoint.Address,
                DataType = SelectedDataPoint.DataType,
                AccessType = SelectedDataPoint.AccessType,
                Unit = SelectedDataPoint.Unit,
                Description = SelectedDataPoint.Description,
                IsEnabled = SelectedDataPoint.IsEnabled
            };

            DataPoints.Add(copiedDataPoint);
            TotalDataPoints = DataPoints.Count;
            HasUnsavedChanges = true;
            StatusMessage = "数据点已复制";
        }

        /// <summary>
        /// 启用数据点
        /// </summary>
        private void EnableDataPoint()
        {
            if (SelectedDataPoint != null)
            {
                SelectedDataPoint.IsEnabled = true;
                HasUnsavedChanges = true;
                StatusMessage = "数据点已启用";
            }
        }

        /// <summary>
        /// 禁用数据点
        /// </summary>
        private void DisableDataPoint()
        {
            if (SelectedDataPoint != null)
            {
                SelectedDataPoint.IsEnabled = false;
                HasUnsavedChanges = true;
                StatusMessage = "数据点已禁用";
            }
        }

        /// <summary>
        /// 异步保存
        /// </summary>
        /// <returns>任务</returns>
        private async Task SaveAsync()
        {
            try
            {
                if (SelectedTemplate != null)
                {
                    SelectedTemplate.Name = TemplateName;
                    SelectedTemplate.DeviceType = DeviceType;
                    SelectedTemplate.Manufacturer = Manufacturer;
                    SelectedTemplate.Model = Model;
                    SelectedTemplate.Version = Version;
                    SelectedTemplate.Description = Description;
                    SelectedTemplate.DataPointCount = DataPoints.Count;
                    SelectedTemplate.LastModified = DateTime.Now;
                }

                HasUnsavedChanges = false;
                StatusMessage = "保存成功";
                
                var successBox = MessageBoxManager
                                 .GetMessageBoxStandard("成功", "模板已保存",
                                 ButtonEnum.Ok, Icon.Information);
                await successBox.ShowAsync();
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"保存失败: {ex.Message}",
                               ButtonEnum.Ok, Icon.Warning);
                await errorBox.ShowAsync();
            }
        }

        /// <summary>
        /// 关闭
        /// </summary>
        private void Close()
        {
            // 这里需要实现关闭窗口的逻辑
            StatusMessage = "关闭窗口";
        }

        #endregion
    }

    #region Data Models

    /// <summary>
    /// 设备模板
    /// </summary>
    public class DeviceTemplate : ReactiveObject
    {
        public Guid Id { get; set; }
        
        private string _name = "";
        public string Name
        {
            get => _name;
            set => this.RaiseAndSetIfChanged(ref _name, value);
        }

        private string _deviceType = "";
        public string DeviceType
        {
            get => _deviceType;
            set => this.RaiseAndSetIfChanged(ref _deviceType, value);
        }

        private string _manufacturer = "";
        public string Manufacturer
        {
            get => _manufacturer;
            set => this.RaiseAndSetIfChanged(ref _manufacturer, value);
        }

        private string _model = "";
        public string Model
        {
            get => _model;
            set => this.RaiseAndSetIfChanged(ref _model, value);
        }

        private string _version = "";
        public string Version
        {
            get => _version;
            set => this.RaiseAndSetIfChanged(ref _version, value);
        }

        private string _description = "";
        public string Description
        {
            get => _description;
            set => this.RaiseAndSetIfChanged(ref _description, value);
        }

        private string _creator = "";
        public string Creator
        {
            get => _creator;
            set => this.RaiseAndSetIfChanged(ref _creator, value);
        }

        private int _dataPointCount = 0;
        public int DataPointCount
        {
            get => _dataPointCount;
            set => this.RaiseAndSetIfChanged(ref _dataPointCount, value);
        }

        private DateTime _lastModified = DateTime.Now;
        public DateTime LastModified
        {
            get => _lastModified;
            set => this.RaiseAndSetIfChanged(ref _lastModified, value);
        }
    }

    /// <summary>
    /// 数据点模板
    /// </summary>
    public class DataPointTemplate : ReactiveObject
    {
        public Guid Id { get; set; }
        
        private string _name = "";
        public string Name
        {
            get => _name;
            set => this.RaiseAndSetIfChanged(ref _name, value);
        }

        private string _address = "";
        public string Address
        {
            get => _address;
            set => this.RaiseAndSetIfChanged(ref _address, value);
        }

        private string _dataType = "";
        public string DataType
        {
            get => _dataType;
            set => this.RaiseAndSetIfChanged(ref _dataType, value);
        }

        private string _accessType = "";
        public string AccessType
        {
            get => _accessType;
            set => this.RaiseAndSetIfChanged(ref _accessType, value);
        }

        private string _unit = "";
        public string Unit
        {
            get => _unit;
            set => this.RaiseAndSetIfChanged(ref _unit, value);
        }

        private string _description = "";
        public string Description
        {
            get => _description;
            set => this.RaiseAndSetIfChanged(ref _description, value);
        }

        private bool _isEnabled = true;
        public bool IsEnabled
        {
            get => _isEnabled;
            set => this.RaiseAndSetIfChanged(ref _isEnabled, value);
        }
    }

    #endregion
}
