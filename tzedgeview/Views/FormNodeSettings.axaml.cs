using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;

namespace tzedgeview.Views
{
    public partial class FormNodeSettings : Window
    {
        public FormNodeSettings()
        {
            InitializeComponent();
#if DEBUG
            this.AttachDevTools();
#endif
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        // 控件引用 - 使用属性而不是字段来避免与自动生成的代码冲突
        public Button BtnSave => this.FindControl<Button>("btnSave");
        public Button BtnDownload => this.FindControl<Button>("btnDownload");
        public Button BtnRefresh => this.FindControl<Button>("btnRefresh");
        public TextBox TxtNodePath => this.FindControl<TextBox>("txtNodePath");
        public TreeView TvDeviceConfig => this.FindControl<TreeView>("tvDeviceConfig");
        public TreeView TvBackupConfig => this.FindControl<TreeView>("tvBackupConfig");
        public TabControl TcMain => this.FindControl<TabControl>("tcMain");
        public StackPanel SpPropertyPanel => this.FindControl<StackPanel>("spPropertyPanel");
        public TextBox TxtXmlConfig => this.FindControl<TextBox>("txtXmlConfig");
        public Button BtnApplyXml => this.FindControl<Button>("btnApplyXml");
        public TextBlock TxtStatus => this.FindControl<TextBlock>("txtStatus");
        public TextBlock TxtDeviceCount => this.FindControl<TextBlock>("txtDeviceCount");
        public TextBlock TxtModified => this.FindControl<TextBlock>("txtModified");

        // 右键菜单项
        public MenuItem MnuAddDevice => this.FindControl<MenuItem>("mnuAddDevice");
        public MenuItem MnuAddGroup => this.FindControl<MenuItem>("mnuAddGroup");
        public MenuItem MnuAddRequest => this.FindControl<MenuItem>("mnuAddRequest");
        public MenuItem MnuEdit => this.FindControl<MenuItem>("mnuEdit");
        public MenuItem MnuDelete => this.FindControl<MenuItem>("mnuDelete");
        public MenuItem MnuCopy => this.FindControl<MenuItem>("mnuCopy");
        public MenuItem MnuPaste => this.FindControl<MenuItem>("mnuPaste");
    }
}
