<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:tzedgeview.ViewModels"
        mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="500"
        x:Class="tzedgeview.Views.FormUpdateCloud"
        x:DataType="vm:FormUpdateCloudViewModel"
        Title="云端更新"
        Width="600" Height="500"
        MinWidth="500" MinHeight="400"
        WindowStartupLocation="CenterOwner"
        CanResize="True">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="云端更新管理" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>

        <!-- 主要内容 -->
        <TabView Grid.Row="1">
            
            <!-- 系统更新 -->
            <TabViewItem Header="系统更新">
                <ScrollViewer Padding="20">
                    <StackPanel Spacing="20">
                        
                        <!-- 当前版本信息 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="10">
                                <TextBlock Text="当前版本信息" FontWeight="Bold" FontSize="14"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="应用程序版本:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentVersion}" 
                                              VerticalAlignment="Center" Margin="0,0,0,5"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="发布日期:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ReleaseDate}" 
                                              VerticalAlignment="Center" Margin="0,0,0,5"/>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="更新通道:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <ComboBox Grid.Row="2" Grid.Column="1" SelectedItem="{Binding UpdateChannel}" 
                                             ItemsSource="{Binding UpdateChannels}" Margin="0,0,0,5"/>
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="自动更新:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <CheckBox Grid.Row="3" Grid.Column="1" IsChecked="{Binding AutoUpdate}" 
                                             Content="启用自动更新" VerticalAlignment="Center"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- 更新检查 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="10">
                                <TextBlock Text="更新检查" FontWeight="Bold" FontSize="14"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0" Spacing="5">
                                        <TextBlock Text="{Binding UpdateStatus}" FontWeight="SemiBold"/>
                                        <TextBlock Text="{Binding LastCheckTime, StringFormat='上次检查: {0}'}" 
                                                  Opacity="0.7" FontSize="12"/>
                                        <TextBlock Text="{Binding AvailableVersion, StringFormat='可用版本: {0}'}" 
                                                  IsVisible="{Binding HasUpdate}" FontSize="12"/>
                                    </StackPanel>
                                    
                                    <Button Grid.Column="1" Content="检查更新" 
                                           Command="{Binding CheckUpdateCommand}"
                                           IsEnabled="{Binding !IsChecking}"
                                           Classes="accent"/>
                                </Grid>
                                
                                <ProgressBar IsVisible="{Binding IsChecking}" 
                                            IsIndeterminate="True" 
                                            Height="4"/>
                            </StackPanel>
                        </Border>

                        <!-- 更新详情 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15"
                                IsVisible="{Binding HasUpdate}">
                            <StackPanel Spacing="10">
                                <TextBlock Text="更新详情" FontWeight="Bold" FontSize="14"/>
                                
                                <ScrollViewer Height="150" 
                                             Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                                             Padding="10">
                                    <TextBlock Text="{Binding UpdateNotes}" 
                                              TextWrapping="Wrap"/>
                                </ScrollViewer>
                                
                                <StackPanel Orientation="Horizontal" Spacing="10">
                                    <Button Content="立即更新" 
                                           Command="{Binding UpdateNowCommand}"
                                           IsEnabled="{Binding !IsUpdating}"
                                           Classes="accent"/>
                                    <Button Content="稍后提醒" 
                                           Command="{Binding RemindLaterCommand}"/>
                                    <Button Content="跳过此版本" 
                                           Command="{Binding SkipVersionCommand}"/>
                                </StackPanel>
                                
                                <ProgressBar IsVisible="{Binding IsUpdating}" 
                                            Value="{Binding UpdateProgress}" 
                                            Height="8"/>
                                <TextBlock Text="{Binding UpdateProgressText}" 
                                          IsVisible="{Binding IsUpdating}"
                                          FontSize="12" Opacity="0.7"/>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabViewItem>

            <!-- 配置同步 -->
            <TabViewItem Header="配置同步">
                <ScrollViewer Padding="20">
                    <StackPanel Spacing="20">
                        
                        <!-- 云端配置 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="10">
                                <TextBlock Text="云端配置同步" FontWeight="Bold" FontSize="14"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="云端服务器:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding CloudServerUrl}" 
                                            Watermark="https://cloud.example.com" Margin="0,0,0,10"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="用户账号:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding UserAccount}" 
                                            Watermark="用户名或邮箱" Margin="0,0,0,10"/>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="访问密钥:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding AccessKey}" 
                                            PasswordChar="*" Watermark="访问密钥"/>
                                </Grid>
                                
                                <StackPanel Orientation="Horizontal" Spacing="10" Margin="0,10,0,0">
                                    <Button Content="测试连接" Command="{Binding TestConnectionCommand}"/>
                                    <Button Content="登录" Command="{Binding LoginCommand}" Classes="accent"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- 同步选项 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="10">
                                <TextBlock Text="同步选项" FontWeight="Bold" FontSize="14"/>
                                
                                <StackPanel Spacing="5">
                                    <CheckBox IsChecked="{Binding SyncServerList}" 
                                             Content="同步服务器列表"/>
                                    <CheckBox IsChecked="{Binding SyncUserSettings}" 
                                             Content="同步用户设置"/>
                                    <CheckBox IsChecked="{Binding SyncDeviceTemplates}" 
                                             Content="同步设备模板"/>
                                    <CheckBox IsChecked="{Binding SyncAlarmRules}" 
                                             Content="同步报警规则"/>
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal" Spacing="10" Margin="0,10,0,0">
                                    <Button Content="上传配置" Command="{Binding UploadConfigCommand}" Classes="accent"/>
                                    <Button Content="下载配置" Command="{Binding DownloadConfigCommand}"/>
                                    <Button Content="自动同步" Command="{Binding AutoSyncCommand}"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- 同步状态 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="10">
                                <TextBlock Text="同步状态" FontWeight="Bold" FontSize="14"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="连接状态:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ConnectionStatus}" 
                                              VerticalAlignment="Center" Margin="0,0,0,5"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="上次同步:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding LastSyncTime}" 
                                              VerticalAlignment="Center" Margin="0,0,0,5"/>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="同步状态:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SyncStatus}" 
                                              VerticalAlignment="Center"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabViewItem>

        </TabView>

        <!-- 进度信息 -->
        <Border Grid.Row="2" Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                CornerRadius="5" Padding="10" Margin="0,10,0,0"
                IsVisible="{Binding ShowProgress}">
            <StackPanel Spacing="5">
                <TextBlock Text="{Binding ProgressMessage}" FontWeight="SemiBold"/>
                <ProgressBar Value="{Binding ProgressValue}" Height="6"/>
            </StackPanel>
        </Border>

        <!-- 按钮栏 -->
        <Border Grid.Row="3" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,1,0,0" Padding="0,15,0,0" Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Right" 
                       Spacing="10">
                <Button Content="关闭" 
                       Command="{Binding CloseCommand}"
                       Width="80"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
