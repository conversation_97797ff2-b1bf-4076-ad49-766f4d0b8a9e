<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="450" 
             x:Class="tzedgeview.Views.FormDeviceMonitor" 
			 xmlns:sys="clr-namespace:System;assembly=mscorlib"
			 xmlns:vm="using:tzedgeview.ViewModels" 
			 x:DataType="vm:FormDeviceMonitorViewModel"
			 xmlns:lvc="using:LiveChartsCore.SkiaSharpView.Avalonia">
	<UserControl.Resources>
		<!--<StringToImageSourceConverter x:Key="StringToImageSourceConverter" />-->
	</UserControl.Resources>
	<Grid HorizontalAlignment="Stretch">
		<Grid.RowDefinitions>
			<RowDefinition Height="32"/>
			<RowDefinition Height="28"/>
			<RowDefinition Height="*"/>
			<RowDefinition Height="350"/>
		</Grid.RowDefinitions>
		<Grid.ColumnDefinitions>
			<ColumnDefinition Width="*"/>
		</Grid.ColumnDefinitions>
		<Grid Grid.Row="0" Grid.Column="0">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="230"/>
				<ColumnDefinition Width="300"/>
				<ColumnDefinition Width="180"/>
				<ColumnDefinition Width="*"/>
				<ColumnDefinition Width="300"/>
			</Grid.ColumnDefinitions>
			<StackPanel Grid.Row="0" Grid.Column="0" Margin="8,0,0,0" VerticalAlignment="Center" Orientation="Horizontal">
				<TextBlock Width="80" Text="设备名称:"/>
				<TextBlock Width="150" Text="{Binding DeviceName}"/>
			</StackPanel>
			<StackPanel Grid.Row="0" Grid.Column="1" VerticalAlignment="Center" Orientation="Horizontal">
				<TextBlock Width="100" Text="开始运行时间:"/>
				<TextBlock Width="200" Text="{Binding DeviceRunTime}"/>
			</StackPanel>
			<StackPanel Grid.Row="0" Grid.Column="2" VerticalAlignment="Center" Orientation="Horizontal">
				<TextBlock Width="80" Text="版本:"/>
				<TextBlock Width="100" Text="{Binding SoftVer}"/>
			</StackPanel>
			<Grid Grid.Column="3"  />
			<StackPanel Grid.Row="0" Grid.Column="4" Orientation="Horizontal" VerticalAlignment="Center" >
                <Image Width="16" Height="16" Margin="4,0,0,0" Source="/Assets/images/glasses_16xLG.png" />
				<TextBlock Margin="8,0,0,0" Width="40" Text="{Binding GWDeviceNum}"/>
				<Image Width="16" Height="16" Margin="8,0,0,0" Source="/Assets/images/StatusAnnotations_Complete_and_ok_16xLG_color.png" />
				<TextBlock Margin="8,0,0,0" Width="40" Text="{Binding DEVSuccNum}"/>
				<Image Width="16" Height="16" Margin="8,0,0,0" Source="/Assets/images/StatusAnnotations_Pause_16xLG_color.png" />
				<TextBlock Margin="8,0,0,0" Width="40" Text="{Binding DEVPauseNum}"/>
				<Image Width="16" Height="16" Margin="8,0,0,0" Source="/Assets/images/StatusAnnotations_Critical_16xLG_color.png" />
				<TextBlock Margin="8,0,0,0" Width="40" Text="{Binding DEVErrorNum}"/>
			</StackPanel>
		</Grid>
		<Grid Grid.Row="1" Grid.Column="1">
			<Grid>
				<Grid.ColumnDefinitions>
					<ColumnDefinition Width="310"/>
					<ColumnDefinition Width="*"/>
					<ColumnDefinition Width="380"/>
				</Grid.ColumnDefinitions>
				<StackPanel Grid.Row="0" Grid.Column="0" Margin="8,0,0,0" VerticalAlignment="Center" Orientation="Horizontal">
					<TextBlock Width="140" Text="详细的设备列表如下:"/>
					<TextBlock Width="250" Text="{Binding DisplayPath}" />
				</StackPanel>
				<Grid Grid.Row="0" Grid.Column="1" />
				<StackPanel Grid.Row="0" Grid.Column="2" Margin="4,0,0,0" HorizontalAlignment="Center" VerticalAlignment="Center" Orientation="Horizontal">
					<Border Background="#FF333333" CornerRadius="8,0,0,8" VerticalAlignment="Center" Width="40" Height="26">
						<TextBlock Margin="2" Width="36" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White" Text="设备" />
					</Border>
					<Border Background="#FF098dee" CornerRadius="0,8,8,0" HorizontalAlignment="Center" VerticalAlignment="Center" Width="40" Height="26">
						<TextBlock Margin="2" Width="36" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White" Text="{Binding DEVNum}" />
					</Border>
					<Border Margin="100,0,0,0" Background="#FF333333" CornerRadius="8,0,0,8" VerticalAlignment="Center" Width="40" Height="26">
						<TextBlock Margin="2" Width="36" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White" Text="报警" />
					</Border>
					<Border Background="#FFdcd011" CornerRadius="0,8,8,0" HorizontalAlignment="Center" VerticalAlignment="Center" Width="40" Height="26">
						<TextBlock Margin="2" Width="36" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White" Text="{Binding DEVAlarmNum}" />
					</Border>
				</StackPanel>
			</Grid>			
		</Grid>
		<Grid Grid.Row="2">
			<Grid.RowDefinitions>
				<RowDefinition Height="*"/>
			</Grid.RowDefinitions>
			<ListBox Grid.Row="0" x:Name="lbdevice" ItemsSource="{Binding deviceStatus}" SelectedItem="{Binding deviceCurrentSelected}" >
				<ListBox.ItemsPanel>
					<ItemsPanelTemplate>
						<WrapPanel />
					</ItemsPanelTemplate>
				</ListBox.ItemsPanel>
				<ListBox.ItemTemplate>
					<DataTemplate>
						<Border Width="320" Height="130">
							<Grid ColumnDefinitions="70,60,70,60,60" RowDefinitions="24,24,24,24,24">
								<TextBlock Grid.Row="0" Grid.Column="0" Margin="4,0,0,0" VerticalAlignment="Center" Text="设备名称:"/>
								<TextBlock Grid.Row="1" Grid.Column="0" Margin="4,0,0,0" VerticalAlignment="Center" Text="活动时间:"/>
								<TextBlock Grid.Row="2" Grid.Column="0" Margin="4,0,0,0" VerticalAlignment="Center" Text="采集耗时:"/>
								<TextBlock Grid.Row="3" Grid.Column="0" Margin="4,0,0,0" VerticalAlignment="Center" Text="成功次数:"/>
								<TextBlock Grid.Row="3" Grid.Column="2" Margin="4,0,0,0" VerticalAlignment="Center" Text="失败次数:"/>
								<TextBlock Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="3" VerticalAlignment="Center" Text="{Binding DeviceName}"/>
								<TextBlock Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3" VerticalAlignment="Center" Text="{Binding ActiveTime}"/>
								<TextBlock Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" VerticalAlignment="Center" Text="{Binding SpendTime}"/>
								<TextBlock Grid.Row="3" Grid.Column="1" VerticalAlignment="Center" Text="{Binding SuccNum}"/>
								<TextBlock Grid.Row="3" Grid.Column="3" VerticalAlignment="Center" Text="{Binding FailtNum}"/>
								<TextBlock Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="5" VerticalAlignment="Center" Text="{Binding Alarm}"/>
								<Button Grid.Row="0" Grid.Column="4" Grid.RowSpan="4" Width="56" Margin="2" Height="56" 
										HorizontalAlignment="Center" VerticalAlignment="Center" 
										CommandParameter="{Binding .}"
										Command="{Binding $parent[ListBox].((vm:FormDeviceMonitorViewModel)DataContext).DoDeviceCommand}">
									<Image Width="50" Height="50" Stretch="Fill" Source="{Binding RunPic}"></Image>									
								</Button>
							</Grid>
						</Border>
					</DataTemplate>
				</ListBox.ItemTemplate>
			</ListBox>
		</Grid>
		<Grid Grid.Row="3">
			<TabControl x:Name="tabStatus" SelectedValue="{Binding TabIndex}">
				<TabItem Header="数据信息" FontSize="14">
					<Grid RowDefinitions="32,*" VerticalAlignment="Top">
						<Grid ColumnDefinitions="80,*" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="节点名称:"/>
							<TextBox Grid.Column="1" Text="{Binding TabDisplayNode}" IsReadOnly="True" />
						</Grid>
						<Grid Grid.Row="1" Margin="0,4,0,4" Height="260">
							<DataGrid Margin="4" 
									  GridLinesVisibility="All" IsReadOnly="True"
									  BorderThickness="1" BorderBrush="Gray" 
									  ItemsSource="{Binding ScalarDataList}" 
									  Height="{Binding $parent.Height}"
									  >
								<DataGrid.Columns>
									<DataGridTextColumn Header="数据名称" Width="160" Binding="{Binding DisplayName}" />
									<DataGridTextColumn Header="值" Width="260" Binding="{Binding Value}" />
									<DataGridTextColumn Header="Unit" Width="80" Binding="{Binding Unit}" />
									<DataGridTextColumn Header="类型" Width="150" Binding="{Binding dataNode}" />
									<DataGridTextColumn Header="权限" Width="150" Binding="{Binding AccessLevel}" />
									<DataGridTextColumn Header="描述" Width="300" Binding="{Binding Description}" />
								</DataGrid.Columns>
							</DataGrid>
						</Grid>
					</Grid>
				</TabItem>
				<TabItem Header="设备信息" FontSize="14">
					<Grid RowDefinitions="32,*" VerticalAlignment="Top">
						<Grid ColumnDefinitions="80,*" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="设备Url:"/>
							<TextBox Grid.Column="1" Text="{Binding TabDeviceUrl}" IsReadOnly="True" />
						</Grid>
						<Grid Grid.Row="1" Margin="0,4,0,4" Height="260">
							<DataGrid Margin="4" ItemsSource="{Binding ScalarNodeList}"
									  GridLinesVisibility="All" IsReadOnly="True"
									  BorderThickness="1" BorderBrush="Gray"
									  Height="{Binding $parent.Height}"
									  DoubleTapped="DataGrid_DoubleTapped"
									  >
								<DataGrid.Columns>
									<DataGridTextColumn Header="数据名称" Width="160" Binding="{Binding DisplayName}" />
									<DataGridTextColumn Header="值" Width="260" Binding="{Binding Value}" />
									<DataGridTextColumn Header="Unit" Width="80" Binding="{Binding Unit}" />
									<DataGridTextColumn Header="类型" Width="150" Binding="{Binding dataNode}" />
									<DataGridTextColumn Header="权限" Width="150" Binding="{Binding AccessLevel}" />
									<DataGridTextColumn Header="描述" Width="300" Binding="{Binding Description}" />
								</DataGrid.Columns>
							</DataGrid>
						</Grid>
					</Grid>
				</TabItem>
				<TabItem Header="方法接口" FontSize="14">
					<Grid RowDefinitions="32,*" VerticalAlignment="Top">
						<Grid ColumnDefinitions="80,*" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="设备Url:"/>
							<TextBox Grid.Column="1" Text="{Binding TabDeviceUrl}" IsReadOnly="True" />
						</Grid>
						<Grid Grid.Row="1" Margin="0,4,0,4" Height="260">
							<DataGrid Margin="4"
									  GridLinesVisibility="All" IsReadOnly="True"
									  BorderThickness="1" BorderBrush="Gray"
									  Height="{Binding $parent.Height}"
									  ItemsSource="{Binding MethodDataList}"
									  >
								<DataGrid.Columns>
									<DataGridTextColumn Header="方法名称" Width="210" Binding="{Binding ApiTopic}" />
									<DataGridTextColumn Header="签名" Width="260" Binding="{Binding MethodSignature}" />
									<DataGridTextColumn Header="描述" Width="300" Binding="{Binding Description}" />
								</DataGrid.Columns>
							</DataGrid>
						</Grid>
					</Grid>
				</TabItem>
				<TabItem Header="报警信息" FontSize="14">
					<Grid RowDefinitions="32,*" VerticalAlignment="Top">
						<Grid ColumnDefinitions="80,*,380" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="设备Url:"/>
							<TextBox Grid.Column="1" Text="{Binding TabDeviceUrl}" IsReadOnly="True" />
							<StackPanel Grid.Row="0" Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" >
								<Image Width="16" Height="16" Margin="10,0,0,0" Source="/Assets/images/StatusAnnotations_Help_and_inconclusive_16xLG_color.png" />
								<TextBlock Margin="4,0,0,0" Width="30" VerticalAlignment="Center" Text="{Binding HintCount}"/>
								<Image Width="16" Height="16" Margin="4,0,0,0" Source="/Assets/images/StatusAnnotations_Warning_16xLG_color.png" />
								<TextBlock Margin="4,0,0,0" Width="30" VerticalAlignment="Center" Text="{Binding WarnCount}"/>
								<Image Width="16" Height="16" Margin="4,0,0,0" Source="/Assets/images/StatusAnnotations_Critical_16xLG_color.png" />
								<TextBlock Margin="4,0,0,0" Width="30" VerticalAlignment="Center" Text="{Binding ErrorCount}"/>
								<Image Width="16" Height="16" Margin="4,0,0,0" Source="/Assets/images/action_Cancel_16xLG.png" />
								<TextBlock Margin="4,0,0,0" Width="30" VerticalAlignment="Center" Text="{Binding FatalCount}"/>
								<ComboBox Margin="20,0,0,0" Width="130" MaxDropDownHeight="180" SelectedItem="{Binding Alarmfilter}">
									<sys:String>全部</sys:String>
									<sys:String>Hint</sys:String>
									<sys:String>Warn</sys:String>
									<sys:String>Error</sys:String>
									<sys:String>Fatal</sys:String>
								</ComboBox>
							</StackPanel>
						</Grid>
						<Grid Grid.Row="1" Margin="0,4,0,4" Height="260">
							<DataGrid Margin="4" ItemsSource="{Binding AlarmDataList}"
									  GridLinesVisibility="All" IsReadOnly="True"
									  BorderThickness="1" BorderBrush="Gray"
									  Height="{Binding $parent.Height}"
									  >
								<DataGrid.Columns>
									<DataGridTextColumn Header="UniqueID" Width="120" Binding="{Binding UniqueId}" />
									<DataGridTextColumn Header="Code" Width="100" Binding="{Binding AlarmCode}" />
									<DataGridTextColumn Header="报警内容" Width="220" Binding="{Binding AlarmContent}" />
									<DataGridTextColumn Header="开始时间" Width="180" Binding="{Binding StartTime}" />
									<DataGridTextColumn Header="持续时间" Width="180" Binding="{Binding FinishTime}" />
									<DataGridTextColumn Header="报警等级" Width="120" Binding="{Binding Degree}" />
									<DataGridTextColumn Header="状态" Width="100" Binding="{Binding Status}" />
									<DataGridCheckBoxColumn Header="是否确认" Width="100" Binding="{Binding Status}" />
								</DataGrid.Columns>
							</DataGrid>
						</Grid>
					</Grid>
				</TabItem>
				<TabItem Header="OEE信息" FontSize="14">
					<Grid RowDefinitions="32,32,*" VerticalAlignment="Top">
						<Grid ColumnDefinitions="80,*,80,140" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="设备Url:"/>
							<TextBox Grid.Column="1" Text="{Binding TabDeviceUrl}" IsReadOnly="True" />
							<TextBlock Margin="20,0,0,0" VerticalAlignment="Center" Grid.Column="2" Text="可选状态:"/>
							<ComboBox Name="cbooee" Margin="4,0,0,0" Width="130" Grid.Column="3" MaxDropDownHeight="180" 
									  ItemsSource="{Binding OEEItemList}" SelectedItem="{Binding CurrentOEE}"/>
						</Grid>
						<Grid ColumnDefinitions="80,300,140,80,80,*" Margin="0,4,0,0" Grid.Row="1" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="实时OEE:"/>
							<ProgressBar  Margin="4,0,0,0" Grid.Column="1" Height="20" Minimum="0" Maximum="100" Value="{Binding OEEProgress}" />
							<TextBlock VerticalAlignment="Center" Grid.Column="2" Margin="10,0,0,0" Text="详细时间信息（秒）:"/>
							<TextBlock VerticalAlignment="Center" Grid.Column="3" Margin="8,0,0,0" Text="{Binding MoreTimeSec}"/>
							<TextBlock VerticalAlignment="Center" Grid.Column="4" Margin="20,0,0,0" Text="设备状态:"/>
							<TextBlock VerticalAlignment="Center" Grid.Column="5" Margin="20,0,0,0" Text="{Binding OEEStatus}"/>
						</Grid>
						<Grid Grid.Row="2" Margin="0,4,0,4" Height="222">
							<lvc:CartesianChart Series="{Binding Series}" XAxes="{Binding XAxes}" LegendPosition="Hidden"
												LegendTextPaint="{Binding TextPaint}" 
                                                TooltipTextPaint="{Binding TextPaint2}">
							</lvc:CartesianChart>
						</Grid>
					</Grid>
				</TabItem>
				<TabItem Header="离线信息" FontSize="14">
					<Grid RowDefinitions="32,32,*" VerticalAlignment="Top">
						<Grid ColumnDefinitions="80,*" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="设备Url:"/>
							<TextBox Grid.Column="1" Text="{Binding TabDeviceUrl}" IsReadOnly="True" />
						</Grid>
						<Grid ColumnDefinitions="80,200,90,200,*" Margin="0,4,0,0" Grid.Row="1" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="离线次数:"/>
							<TextBox Grid.Column="1" HorizontalAlignment="Left" Width="190" Text="{Binding OfflineCount}" IsReadOnly="True" />
							<TextBlock VerticalAlignment="Center" Grid.Column="2" Margin="20,0,0,0" Text="离线总时长:"/>
							<TextBox Grid.Column="3" Width="190" Text="{Binding OfflineTimeSec}" IsReadOnly="True" />
						</Grid>
						<Grid Grid.Row="2" Margin="0,4,0,4" Height="222">
							<DataGrid Margin="4" ItemsSource="{Binding OfflineDataList}"
									  GridLinesVisibility="All" IsReadOnly="True"
									  BorderThickness="1" BorderBrush="Gray"
									  Height="{Binding $parent.Height}"
									  >
								<DataGrid.Columns>
									<DataGridTextColumn Header="索引" Width="120" Binding="{Binding Index}" />
									<DataGridTextColumn Header="开始时间" Width="180" Binding="{Binding BeginTime}" />
									<DataGridTextColumn Header="结束时间" Width="180" Binding="{Binding EndTime}" />
									<DataGridTextColumn Header="持续时间" Width="160" Binding="{Binding InTime}" />
									<DataGridTextColumn Header="备注" Width="200" Binding="{Binding Memo}" />
								</DataGrid.Columns>
							</DataGrid>
						</Grid>
					</Grid>
				</TabItem>
				<TabItem Header="设备日志" FontSize="14">
					<Grid RowDefinitions="32,32,*" VerticalAlignment="Top">
						<Grid ColumnDefinitions="80,*" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="设备Url:"/>
							<TextBox Grid.Column="1" Text="{Binding TabDeviceUrl}" IsReadOnly="True" />
						</Grid>
						<Grid ColumnDefinitions="80,200,200,*,300" Margin="0,4,0,0" Grid.Row="1" Height="32">
							<TextBlock VerticalAlignment="Center" Grid.Column="0" Text="日志列表:"/>
							<RadioButton Name="rbRun" Margin="8,4,0,0" Grid.Column="1" GroupName="log" Content="运行日志" IsChecked="{Binding bolRun}"/>
							<RadioButton Name="rbComm" Margin="8,4,0,0" Grid.Column="2" GroupName="log" Content="通信报文" IsChecked="{Binding bolComm}"/>
							<StackPanel Grid.Column="4" Margin="4,0,10,0" HorizontalAlignment="Right" VerticalAlignment="Center" Orientation="Horizontal">
								<Button Name="btPause">暂停</Button>
								<Button Name="btClear" Margin="10,0,0,0">清空</Button>
							</StackPanel>
						</Grid>
						<Grid Grid.Row="2" Margin="0,4,0,4" Height="222">
							<TextBox Name="txtLogs" Height="222" AcceptsReturn="True" IsReadOnly="True" VerticalContentAlignment="Top" 
									 TextWrapping="Wrap" Text="{Binding Logs}"/>
						</Grid>
					</Grid>
				</TabItem>
			</TabControl>
		</Grid>
	</Grid>
</UserControl>
