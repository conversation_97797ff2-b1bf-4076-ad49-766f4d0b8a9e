<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:tzedgeview.ViewModels"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
        x:Class="tzedgeview.Views.FormLogView"
        x:DataType="vm:FormLogViewViewModel"
        Title="日志查看器"
        Width="800" Height="600"
        MinWidth="600" MinHeight="400"
        WindowStartupLocation="CenterOwner">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Classes="toolbar" Padding="10,8">
            <StackPanel Orientation="Horizontal" Spacing="10">
                <Button Content="刷新" Command="{Binding RefreshCommand}" Classes="accent"/>
                <Button Content="清除" Command="{Binding ClearCommand}"/>
                <Button Content="导出" Command="{Binding ExportCommand}"/>
                <Separator/>
                <Button Content="暂停" Command="{Binding PauseCommand}" 
                        IsVisible="{Binding !IsPaused}"/>
                <Button Content="继续" Command="{Binding ResumeCommand}" 
                        IsVisible="{Binding IsPaused}"/>
                <Separator/>
                <CheckBox Content="自动滚动" IsChecked="{Binding AutoScroll}"/>
            </StackPanel>
        </Border>

        <!-- 过滤器 -->
        <Border Grid.Row="1" Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="级别:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Grid.Column="1" SelectedItem="{Binding SelectedLogLevel}" 
                          ItemsSource="{Binding LogLevels}" Margin="0,0,10,0"/>
                
                <TextBlock Grid.Column="2" Text="搜索:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBox Grid.Column="3" Text="{Binding SearchText}" 
                         Watermark="输入搜索关键字..." Margin="0,0,10,0"/>
                
                <TextBlock Grid.Column="4" Text="最大行数:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <NumericUpDown Grid.Column="5" Value="{Binding MaxLines}" 
                              Minimum="100" Maximum="10000" Increment="100" 
                              Width="100" Margin="0,0,10,0"/>
                
                <Button Grid.Column="6" Content="应用" Command="{Binding ApplyFilterCommand}"/>
            </Grid>
        </Border>

        <!-- 日志内容 -->
        <DataGrid Grid.Row="2" ItemsSource="{Binding LogEntries}" 
                  SelectedItem="{Binding SelectedLogEntry}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  CanUserReorderColumns="True"
                  CanUserResizeColumns="True"
                  CanUserSortColumns="True">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="时间" Binding="{Binding Timestamp}" Width="150" SortMemberPath="Timestamp"/>
                <DataGridTextColumn Header="级别" Binding="{Binding Level}" Width="80"/>
                <DataGridTextColumn Header="来源" Binding="{Binding Source}" Width="150"/>
                <DataGridTextColumn Header="消息" Binding="{Binding Message}" Width="*"/>
            </DataGrid.Columns>

            <DataGrid.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="复制消息" Command="{Binding CopyMessageCommand}"/>
                    <MenuItem Header="复制行" Command="{Binding CopyRowCommand}"/>
                    <Separator/>
                    <MenuItem Header="查看详情" Command="{Binding ViewDetailsCommand}"/>
                    <Separator/>
                    <MenuItem Header="过滤此级别" Command="{Binding FilterByLevelCommand}"/>
                    <MenuItem Header="过滤此来源" Command="{Binding FilterBySourceCommand}"/>
                </ContextMenu>
            </DataGrid.ContextMenu>
        </DataGrid>

        <!-- 状态栏 -->
        <Border Grid.Row="3" Classes="statusbar" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="20">
                    <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding IsPaused, StringFormat={}状态: {0}}"
                              VerticalAlignment="Center" Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="15">
                    <TextBlock Text="{Binding TotalEntries, StringFormat={}总计: {0}}"
                              VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding FilteredEntries, StringFormat={}显示: {0}}"
                              VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding ErrorCount, StringFormat={}错误: {0}}"
                              VerticalAlignment="Center" Foreground="#D32F2F"/>
                    <TextBlock Text="{Binding WarningCount, StringFormat={}警告: {0}}"
                              VerticalAlignment="Center" Foreground="#FF9800"/>
                    <TextBlock Text="{Binding LastUpdateTime, StringFormat={}更新: {0}}"
                              VerticalAlignment="Center" FontSize="11" Opacity="0.7"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</Window>
