using Avalonia.Controls;
using tzedgeview.ViewModels;
using tzedgeview.Core;

namespace tzedgeview.Views
{
    /// <summary>
    /// 设备模板管理窗体
    /// </summary>
    public partial class FormTemplateDevice : Window
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public FormTemplateDevice()
        {
            InitializeComponent();
            
            // 创建并设置ViewModel
            var viewModel = new FormTemplateDeviceViewModel();
            DataContext = viewModel;
            
            // 应用主题
            ApplyTheme();
        }

        #endregion

        #region Properties

        /// <summary>
        /// 获取当前的ViewModel
        /// </summary>
        public FormTemplateDeviceViewModel ViewModel => DataContext as FormTemplateDeviceViewModel;

        #endregion

        #region Methods

        /// <summary>
        /// 应用主题
        /// </summary>
        private void ApplyTheme()
        {
            try
            {
                ThemeManager.ApplyThemeToWindow(this);
            }
            catch (System.Exception ex)
            {
                System.Console.WriteLine($"Error applying theme: {ex.Message}");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// 窗口关闭时调用
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnClosing(WindowClosingEventArgs e)
        {
            // 检查是否有未保存的更改
            if (ViewModel?.HasUnsavedChanges == true)
            {
                // 这里可以添加保存确认对话框
            }
            
            base.OnClosing(e);
        }

        #endregion
    }
}
