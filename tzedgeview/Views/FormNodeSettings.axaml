<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
        x:Class="tzedgeview.Views.FormNodeSettings"
        Title="设备采集参数配置"
        Width="1200" Height="800"
        WindowStartupLocation="CenterOwner">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5" Background="LightGray">
            <Button Name="btnSave" Content="保存配置" Margin="5" Padding="10,5"/>
            <Button Name="btnDownload" Content="下载到设备" Margin="5" Padding="10,5"/>
            <Button Name="btnRefresh" Content="刷新" Margin="5" Padding="10,5"/>
            <Separator/>
            <TextBlock Text="节点路径:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <TextBox Name="txtNodePath" Width="300" IsReadOnly="True" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧树形结构 -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="5"/>
                    <RowDefinition Height="200"/>
                </Grid.RowDefinitions>

                <!-- 设备配置树 -->
                <Border Grid.Row="0" BorderBrush="Gray" BorderThickness="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Grid.Row="0" Text="设备配置树:" Margin="5" FontWeight="Bold"/>
                        <TreeView Grid.Row="1" Name="tvDeviceConfig" Margin="5">
                            <TreeView.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Name="mnuAddDevice" Header="添加设备"/>
                                    <MenuItem Name="mnuAddGroup" Header="添加分组"/>
                                    <MenuItem Name="mnuAddRequest" Header="添加数据请求"/>
                                    <Separator/>
                                    <MenuItem Name="mnuEdit" Header="编辑"/>
                                    <MenuItem Name="mnuDelete" Header="删除"/>
                                    <Separator/>
                                    <MenuItem Name="mnuCopy" Header="复制"/>
                                    <MenuItem Name="mnuPaste" Header="粘贴"/>
                                </ContextMenu>
                            </TreeView.ContextMenu>
                        </TreeView>
                    </Grid>
                </Border>

                <GridSplitter Grid.Row="1" Height="5" HorizontalAlignment="Stretch" Background="Gray"/>

                <!-- 备用配置列表 -->
                <Border Grid.Row="2" BorderBrush="Gray" BorderThickness="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Grid.Row="0" Text="备用配置文件:" Margin="5" FontWeight="Bold"/>
                        <TreeView Grid.Row="1" Name="tvBackupConfig" Margin="5"/>
                    </Grid>
                </Border>
            </Grid>

            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="Gray"/>

            <!-- 右侧详细配置区域 -->
            <Grid Grid.Column="2">
                <TabControl Name="tcMain">
                    <!-- 可视化配置标签页 -->
                    <TabItem Header="可视化配置">
                        <ScrollViewer>
                            <StackPanel Name="spPropertyPanel" Margin="10">
                                <TextBlock Text="请在左侧选择要配置的节点" 
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          FontSize="16" 
                                          Foreground="Gray"/>
                            </StackPanel>
                        </ScrollViewer>
                    </TabItem>

                    <!-- XML配置标签页 -->
                    <TabItem Header="XML配置">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" 
                                      Text="以下是当前所有配置信息的XML格式文档，可以手动修改点击保存来生效。" 
                                      Margin="5" 
                                      TextWrapping="Wrap"/>
                            
                            <TextBox Grid.Row="1" 
                                    Name="txtXmlConfig" 
                                    AcceptsReturn="True" 
                                    TextWrapping="Wrap" 
                                    ScrollViewer.VerticalScrollBarVisibility="Auto"
                                    FontFamily="Consolas"
                                    FontSize="12"
                                    Margin="5"/>
                            
                            <Button Grid.Row="2" 
                                   Name="btnApplyXml" 
                                   Content="应用XML修改" 
                                   HorizontalAlignment="Right" 
                                   Margin="5" 
                                   Padding="10,5"/>
                        </Grid>
                    </TabItem>
                </TabControl>
            </Grid>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="LightGray" BorderBrush="Gray" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Name="txtStatus" Text="就绪" Margin="5" VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="5" VerticalAlignment="Center">
                    <TextBlock Text="设备数量: "/>
                    <TextBlock Name="txtDeviceCount" Text="0"/>
                    <TextBlock Text=" | 配置已修改: "/>
                    <TextBlock Name="txtModified" Text="否" Foreground="Green"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
