<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:vm="using:tzedgeview.ViewModels"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="tzedgeview.Views.frmedgeservers"
			 x:DataType="vm:frmedgeserversviewmodel">
	<DockPanel>
		<StackPanel DockPanel.Dock="Top">
			<Button Name="btnadd" Width="32" Height="32" Margin="2,2,2,2" HorizontalAlignment="Left">
				<Button.Content>
					<Image Width="16" Height="16" Stretch="UniformToFill" Source="/Assets/images/action_add_16xLG.png" />
				</Button.Content>
			</Button>
			
		</StackPanel>
		<TreeView Name="tvdevice" Width="276" ItemsSource="{Binding DeviceNodes}" 
                  HorizontalAlignment="Left"
				  SelectedItem="{Binding SelectDeviceNode}">
			<TreeView.ContextMenu>
				<ContextMenu>
					<MenuItem Name="mnujk" Header="网关信息监控"/>
					<Separator></Separator>
					<MenuItem Name="mnusbcs" Header="编辑设备参数"/>
					<MenuItem Name="mnucjcs" Header="编辑采集参数"/>
					<MenuItem Name="mnuljcs" Header="编辑连接信息"/>
					<MenuItem Name="mnuscsb" Header="删除设备"/>
					<MenuItem Name="mnucqsb" Header="重启设备"/>
					<MenuItem Name="mnugbsz" Header="关闭设备"/>
					<MenuItem Name="mnuckrz" Header="查看日志"/>
				</ContextMenu>
			</TreeView.ContextMenu>
			<TreeView.ItemTemplate>
				<TreeDataTemplate ItemsSource="{Binding ChildNodes}">
					<StackPanel Name="PART_Header" Orientation="Horizontal" Spacing="5">
						<Svg Name="svgSvg" Width="16" Height="16" Path="{Binding Icon}" />
						<TextBlock Text="{Binding Text}"/>
					</StackPanel>							
				</TreeDataTemplate>
			</TreeView.ItemTemplate>
		</TreeView>
	</DockPanel>
</UserControl>
