<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:Width="500" d:Height="300"
        x:Class="tzedgeview.Views.FormServerAdd"
		WindowStartupLocation="CenterOwner"
		xmlns:vm="using:tzedgeview.ViewModels"
        x:DataType="vm:FormServerAddViewModel"
        Title="新增网关">
	<Grid Margin="30">
		<Grid.RowDefinitions>
			<RowDefinition Height="40"/>
			<RowDefinition Height="40"/>
			<RowDefinition Height="40"/>
			<RowDefinition Height="40"/>
			<RowDefinition Height="40"/>
			<RowDefinition Height="50"/>
		</Grid.RowDefinitions>
		<Grid.ColumnDefinitions>
			<ColumnDefinition Width="90"/>
			<ColumnDefinition Width="150"/>
			<ColumnDefinition Width="60"/>
			<ColumnDefinition Width="110"/>
		</Grid.ColumnDefinitions>
		<TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Margin="4" Text="网关别名:" />
		<TextBox Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="3" Width="350" Margin="4" Text ="{Binding EdgeServerSettings.Alias}"/>

		<TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" Margin="4" Text="网关标识:" />
		<TextBox Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3" Width="350" Margin="4" x:Name="gwid" IsEnabled="False" Text ="{Binding EdgeServerSettings.EdgeID}"/>

		<TextBlock Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" Margin="4" Text="IP地址:" />
		<TextBox Grid.Row="2" Grid.Column="1" Width="140" Margin="-8,4,4,4" HorizontalAlignment="Left" Text ="{Binding EdgeServerSettings.IpAddress}"/>
		<TextBlock Grid.Row="2" Grid.Column="2" Margin="4" HorizontalAlignment="Left" VerticalAlignment="Center" Text="端口:" />
		<TextBox Grid.Row="2" Grid.Column="3" Width="130" Margin="-8,4,4,4" HorizontalAlignment="Left" VerticalAlignment="Center" Text ="{Binding EdgeServerSettings.Port}"/>

		<TextBlock Grid.Row="3" Grid.Column="0" VerticalAlignment="Center" Margin="4" Text="用户名:" />
		<TextBox Grid.Row="3" Grid.Column="1" Width="140" Margin="-8,4,4,4" HorizontalAlignment="Left" Text ="{Binding EdgeServerSettings.UserName}"/>
		<TextBlock Grid.Row="3" Grid.Column="2" Margin="4" HorizontalAlignment="Left" VerticalAlignment="Center" Text="密码:" />
		<TextBox Grid.Row="3" Grid.Column="3" Width="130" Margin="-8,4,4,4" HorizontalAlignment="Left" VerticalAlignment="Center" Text ="{Binding EdgeServerSettings.Password}"/>

		<CheckBox Grid.Row="4" Grid.ColumnSpan="4" IsChecked="{Binding EdgeServerSettings.UseEncryptedCommunication}">使用加密技术进行通讯</CheckBox>
		<Grid Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="6" HorizontalAlignment="Left">
			<Button Margin="0,10,0,0" Width="120" Name="btntest" HorizontalAlignment="Left">通讯测试</Button>
			<Button Margin="310,10,0,0" Width="120" HorizontalContentAlignment="Left" Name="btnok" IsEnabled="False">完成</Button>
		</Grid>
	</Grid>
</Window>
