using Avalonia.Controls;
using tzedgeview.ViewModels;
using tzedgeview.Core;

namespace tzedgeview.Views
{
    /// <summary>
    /// 云端更新窗体
    /// </summary>
    public partial class FormUpdateCloud : Window
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public FormUpdateCloud()
        {
            InitializeComponent();
            
            // 创建并设置ViewModel
            var viewModel = new FormUpdateCloudViewModel();
            DataContext = viewModel;
            
            // 应用主题
            ApplyTheme();
        }

        #endregion

        #region Properties

        /// <summary>
        /// 获取当前的ViewModel
        /// </summary>
        public FormUpdateCloudViewModel ViewModel => DataContext as FormUpdateCloudViewModel;

        #endregion

        #region Methods

        /// <summary>
        /// 应用主题
        /// </summary>
        private void ApplyTheme()
        {
            try
            {
                ThemeManager.ApplyThemeToWindow(this);
            }
            catch (System.Exception ex)
            {
                System.Console.WriteLine($"Error applying theme: {ex.Message}");
            }
        }

        #endregion
    }
}
