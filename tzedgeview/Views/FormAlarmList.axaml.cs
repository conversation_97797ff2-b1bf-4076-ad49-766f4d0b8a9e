using Avalonia.Controls;
using Avalonia.Interactivity;
using tzedgeview.ViewModels;
using tzedgeview.Core;
using HslTechnology.Edge.Node;

namespace tzedgeview.Views
{
    /// <summary>
    /// 报警列表窗体
    /// </summary>
    public partial class FormAlarmList : UserControl
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public FormAlarmList()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 实例化一个指定参数的对象
        /// </summary>
        /// <param name="serverSettings">服务器设置</param>
        /// <param name="renderPath">渲染路径</param>
        public FormAlarmList(EdgeServerSettings serverSettings, TreeNodePath renderPath)
        {
            InitializeComponent();
            
            // 创建并设置ViewModel
            var viewModel = new FormAlarmListViewModel(serverSettings, renderPath);
            DataContext = viewModel;
            
            // 应用主题
            ApplyTheme();
        }

        #endregion

        #region Properties

        /// <summary>
        /// 获取当前的ViewModel
        /// </summary>
        public FormAlarmListViewModel ViewModel => DataContext as FormAlarmListViewModel;

        #endregion

        #region Methods

        /// <summary>
        /// 应用主题
        /// </summary>
        private void ApplyTheme()
        {
            try
            {
                // 根据当前主题设置样式
                ThemeManager.ApplyThemeToWindow(null); // 这里传入null，因为这是UserControl
            }
            catch (System.Exception ex)
            {
                System.Console.WriteLine($"Error applying theme: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动报警监控
        /// </summary>
        public void StartMonitoring()
        {
            ViewModel?.StartMonitoring();
        }

        /// <summary>
        /// 停止报警监控
        /// </summary>
        public void StopMonitoring()
        {
            ViewModel?.StopMonitoring();
        }

        /// <summary>
        /// 刷新报警数据
        /// </summary>
        public void RefreshAlarms()
        {
            ViewModel?.RefreshAlarms();
        }

        /// <summary>
        /// 设置设备路径
        /// </summary>
        /// <param name="devicePath">设备路径</param>
        public void SetDevicePath(string devicePath)
        {
            ViewModel?.SetDevicePath(devicePath);
        }

        /// <summary>
        /// 清除所有报警
        /// </summary>
        public void ClearAllAlarms()
        {
            ViewModel?.ClearAllAlarms();
        }

        /// <summary>
        /// 确认选中的报警
        /// </summary>
        public void AcknowledgeSelectedAlarms()
        {
            ViewModel?.AcknowledgeSelectedAlarms();
        }

        /// <summary>
        /// 导出报警数据
        /// </summary>
        public void ExportAlarms()
        {
            ViewModel?.ExportAlarms();
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// 控件加载完成事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 启动监控
            StartMonitoring();
        }

        /// <summary>
        /// 控件卸载事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            // 停止监控
            StopMonitoring();
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// 控件附加到可视化树时调用
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnAttachedToVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
        {
            base.OnAttachedToVisualTree(e);
            
            // 启动监控
            StartMonitoring();
        }

        /// <summary>
        /// 控件从可视化树分离时调用
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnDetachedFromVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
        {
            base.OnDetachedFromVisualTree(e);
            
            // 停止监控
            StopMonitoring();
        }

        #endregion
    }
}
