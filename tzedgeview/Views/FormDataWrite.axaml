<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="500" d:DesignHeight="300"
        x:Class="tzedgeview.Views.FormDataWrite"
        Title="数据写入操作"
        Width="500" Height="300"
        WindowStartupLocation="CenterOwner"
        CanResize="False"
        Icon="/Assets/images/device.png">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 数据节点名称 -->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="100"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="数据节点:" VerticalAlignment="Center" FontWeight="SemiBold"/>
            <TextBox Grid.Column="1" x:Name="txtDataNode" IsReadOnly="True" Background="#F5F5F5"/>
        </Grid>

        <!-- 当前值 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="100"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="当前值:" VerticalAlignment="Center" FontWeight="SemiBold"/>
            <TextBox Grid.Column="1" x:Name="txtCurrentValue" IsReadOnly="True" Background="#F5F5F5"/>
        </Grid>

        <!-- 新值 -->
        <Grid Grid.Row="4">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="100"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="新值:" VerticalAlignment="Center" FontWeight="SemiBold"/>
            <TextBox Grid.Column="1" x:Name="txtNewValue" Watermark="请输入新的数值"/>
        </Grid>

        <!-- 数据类型提示 -->
        <Grid Grid.Row="6">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="100"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="数据类型:" VerticalAlignment="Center" FontWeight="SemiBold"/>
            <TextBlock Grid.Column="1" x:Name="txtDataType" VerticalAlignment="Center" Foreground="#666666"/>
        </Grid>

        <!-- 按钮区域 -->
        <Grid Grid.Row="8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="80"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="80"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="2" x:Name="btnOK" Content="确定" 
                    Background="#0078D4" Foreground="White"
                    HorizontalAlignment="Stretch" Height="32"
                    Click="BtnOK_Click"/>
            <Button Grid.Column="4" x:Name="btnCancel" Content="取消"
                    Background="#F3F2F1" Foreground="#323130"
                    HorizontalAlignment="Stretch" Height="32"
                    Click="BtnCancel_Click"/>
        </Grid>
    </Grid>
</Window>
