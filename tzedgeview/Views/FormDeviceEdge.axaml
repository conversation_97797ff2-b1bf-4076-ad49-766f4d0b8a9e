<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:tzedgeview.ViewModels"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
             x:Class="tzedgeview.Views.FormDeviceEdge"
             x:DataType="vm:FormDeviceEdgeViewModel">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding Title}" 
                          FontSize="16" FontWeight="Bold" 
                          VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                    <Button Name="btnRefresh" Content="刷新" 
                            Command="{Binding RefreshCommand}"
                            Classes="accent"/>
                    <Button Name="btnSettings" Content="设置" 
                            Command="{Binding SettingsCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="1" Padding="10">
            <StackPanel Spacing="15">
                
                <!-- 连接状态区域 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0" Spacing="5">
                            <TextBlock Text="连接状态" FontWeight="Bold" FontSize="14"/>
                            <TextBlock Text="{Binding ConnectionStatus}" 
                                      Foreground="{Binding ConnectionStatusColor}"/>
                            <TextBlock Text="{Binding LastUpdateTime}" 
                                      FontSize="12" Opacity="0.7"/>
                        </StackPanel>
                        
                        <Ellipse Grid.Column="1" Width="20" Height="20" 
                                Fill="{Binding StatusIndicatorColor}" 
                                VerticalAlignment="Center"/>
                    </Grid>
                </Border>

                <!-- 设备信息区域 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <StackPanel Spacing="10">
                        <TextBlock Text="设备信息" FontWeight="Bold" FontSize="14"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="设备名称:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding DeviceName}" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="设备类型:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding DeviceType}" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="IP地址:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding IpAddress}" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="端口:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding Port}" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="运行时间:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding RunTime}" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="CPU使用率:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="3" Text="{Binding CpuUsage}" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="内存使用:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding MemoryUsage}" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="2" Text="线程数:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="3" Grid.Column="3" Text="{Binding ThreadCount}" VerticalAlignment="Center"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 实时数据区域 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <StackPanel Spacing="10">
                        <TextBlock Text="实时数据" FontWeight="Bold" FontSize="14"/>
                        
                        <DataGrid ItemsSource="{Binding RealTimeData}" 
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="数据点" Binding="{Binding Name}" Width="200"/>
                                <DataGridTextColumn Header="当前值" Binding="{Binding Value}" Width="150"/>
                                <DataGridTextColumn Header="数据类型" Binding="{Binding DataType}" Width="100"/>
                                <DataGridTextColumn Header="更新时间" Binding="{Binding UpdateTime}" Width="150"/>
                                <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- 运行历史图表区域 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <StackPanel Spacing="10">
                        <TextBlock Text="运行时间历史" FontWeight="Bold" FontSize="14"/>
                        
                        <!-- 这里可以添加图表控件 -->
                        <Border Height="200" Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                                CornerRadius="3">
                            <TextBlock Text="运行时间图表区域" 
                                      HorizontalAlignment="Center" 
                                      VerticalAlignment="Center"
                                      Opacity="0.5"/>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- 报警信息区域 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <StackPanel Spacing="10">
                        <TextBlock Text="报警信息" FontWeight="Bold" FontSize="14"/>
                        
                        <DataGrid ItemsSource="{Binding AlarmData}" 
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  MaxHeight="200">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="报警时间" Binding="{Binding AlarmTime}" Width="150"/>
                                <DataGridTextColumn Header="报警级别" Binding="{Binding Level}" Width="100"/>
                                <DataGridTextColumn Header="报警内容" Binding="{Binding Message}" Width="*"/>
                                <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" 
                          VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="15">
                    <TextBlock Text="{Binding DataCount, StringFormat='数据点: {0}'}" 
                              VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding RefreshInterval, StringFormat='刷新间隔: {0}ms'}" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</UserControl>
