<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:tzedgeview.ViewModels"
        mc:Ignorable="d" d:DesignWidth="900" d:DesignHeight="700"
        x:Class="tzedgeview.Views.FormTemplateDevice"
        x:DataType="vm:FormTemplateDeviceViewModel"
        Title="设备模板管理"
        Width="900" Height="700"
        MinWidth="800" MinHeight="600"
        WindowStartupLocation="CenterOwner">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="设备模板管理" FontSize="16" FontWeight="Bold" 
                          VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                    <Button Content="新建模板" Command="{Binding NewTemplateCommand}" Classes="accent"/>
                    <Button Content="导入模板" Command="{Binding ImportTemplateCommand}"/>
                    <Button Content="导出模板" Command="{Binding ExportTemplateCommand}"/>
                    <Button Content="刷新" Command="{Binding RefreshCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 模板列表 -->
            <Border Grid.Column="0" Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                    BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                    BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 搜索栏 -->
                    <Border Grid.Row="0" Padding="10,5" 
                            BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                            BorderThickness="0,0,0,1">
                        <TextBox Text="{Binding SearchText}" 
                                Watermark="搜索模板..." 
                                Classes="clearButton"/>
                    </Border>

                    <!-- 模板列表 -->
                    <ListBox Grid.Row="1" ItemsSource="{Binding Templates}" 
                            SelectedItem="{Binding SelectedTemplate}"
                            Padding="5">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="Transparent" 
                                       Padding="10,8" 
                                       CornerRadius="3"
                                       Margin="2">
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="{Binding Name}" 
                                                  FontWeight="SemiBold" 
                                                  FontSize="14"/>
                                        <TextBlock Text="{Binding DeviceType}" 
                                                  Opacity="0.7" 
                                                  FontSize="12"/>
                                        <TextBlock Text="{Binding Description}" 
                                                  Opacity="0.6" 
                                                  FontSize="11" 
                                                  TextWrapping="Wrap"/>
                                        <StackPanel Orientation="Horizontal" Spacing="10">
                                            <TextBlock Text="{Binding DataPointCount, StringFormat='数据点: {0}'}" 
                                                      FontSize="10" Opacity="0.5"/>
                                            <TextBlock Text="{Binding LastModified, StringFormat='修改: {0:MM-dd}'}" 
                                                      FontSize="10" Opacity="0.5"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                        
                        <ListBox.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="编辑" Command="{Binding EditTemplateCommand}"/>
                                <MenuItem Header="复制" Command="{Binding CopyTemplateCommand}"/>
                                <MenuItem Header="删除" Command="{Binding DeleteTemplateCommand}"/>
                                <Separator/>
                                <MenuItem Header="导出" Command="{Binding ExportSelectedTemplateCommand}"/>
                            </ContextMenu>
                        </ListBox.ContextMenu>
                    </ListBox>
                </Grid>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" Background="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>

            <!-- 模板详情 -->
            <Grid Grid.Column="2" Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 模板信息 -->
                <Border Grid.Row="0" Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15" Margin="0,0,0,10">
                    <Grid IsEnabled="{Binding HasSelectedTemplate}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="模板名称:" 
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding TemplateName}" 
                                Margin="0,0,10,10"/>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="设备类型:" 
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Grid.Row="0" Grid.Column="3" SelectedItem="{Binding DeviceType}" 
                                 ItemsSource="{Binding DeviceTypes}" Margin="0,0,0,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="制造商:" 
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Manufacturer}" 
                                Margin="0,0,10,10"/>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="型号:" 
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding Model}" 
                                Margin="0,0,0,10"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="版本:" 
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Version}" 
                                Margin="0,0,10,10"/>

                        <TextBlock Grid.Row="2" Grid.Column="2" Text="创建者:" 
                                  VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding Creator}" 
                                IsReadOnly="True" Margin="0,0,0,10"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="描述:" 
                                  VerticalAlignment="Top" Margin="0,5,10,0"/>
                        <TextBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3" 
                                Text="{Binding Description}" 
                                AcceptsReturn="True" TextWrapping="Wrap" 
                                MinHeight="60"/>
                    </Grid>
                </Border>

                <!-- 数据点配置 -->
                <TabView Grid.Row="1" IsEnabled="{Binding HasSelectedTemplate}">
                    
                    <!-- 数据点列表 -->
                    <TabViewItem Header="数据点">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 数据点工具栏 -->
                            <Border Grid.Row="0" Padding="5" 
                                    BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                                    BorderThickness="0,0,0,1">
                                <StackPanel Orientation="Horizontal" Spacing="10">
                                    <Button Content="添加数据点" Command="{Binding AddDataPointCommand}" Classes="accent"/>
                                    <Button Content="删除数据点" Command="{Binding DeleteDataPointCommand}"/>
                                    <Button Content="导入数据点" Command="{Binding ImportDataPointsCommand}"/>
                                    <Button Content="导出数据点" Command="{Binding ExportDataPointsCommand}"/>
                                </StackPanel>
                            </Border>

                            <!-- 数据点列表 -->
                            <DataGrid Grid.Row="1" ItemsSource="{Binding DataPoints}" 
                                     SelectedItem="{Binding SelectedDataPoint}"
                                     AutoGenerateColumns="False"
                                     CanUserReorderColumns="True"
                                     CanUserResizeColumns="True"
                                     CanUserSortColumns="True"
                                     GridLinesVisibility="Horizontal"
                                     HeadersVisibility="Column">
                                
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="150"/>
                                    <DataGridTextColumn Header="地址" Binding="{Binding Address}" Width="100"/>
                                    <DataGridTextColumn Header="数据类型" Binding="{Binding DataType}" Width="100"/>
                                    <DataGridTextColumn Header="访问权限" Binding="{Binding AccessType}" Width="80"/>
                                    <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="60"/>
                                    <DataGridTextColumn Header="描述" Binding="{Binding Description}" Width="*"/>
                                    <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsEnabled}" Width="60"/>
                                </DataGrid.Columns>

                                <DataGrid.ContextMenu>
                                    <ContextMenu>
                                        <MenuItem Header="编辑" Command="{Binding EditDataPointCommand}"/>
                                        <MenuItem Header="复制" Command="{Binding CopyDataPointCommand}"/>
                                        <MenuItem Header="删除" Command="{Binding DeleteDataPointCommand}"/>
                                        <Separator/>
                                        <MenuItem Header="启用" Command="{Binding EnableDataPointCommand}"/>
                                        <MenuItem Header="禁用" Command="{Binding DisableDataPointCommand}"/>
                                    </ContextMenu>
                                </DataGrid.ContextMenu>
                            </DataGrid>
                        </Grid>
                    </TabViewItem>

                    <!-- 通信参数 -->
                    <TabViewItem Header="通信参数">
                        <ScrollViewer Padding="10">
                            <StackPanel Spacing="15">
                                
                                <!-- 连接参数 -->
                                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                        CornerRadius="5" Padding="15">
                                    <StackPanel Spacing="10">
                                        <TextBlock Text="连接参数" FontWeight="Bold" FontSize="13"/>
                                        
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="120"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="120"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>
                                            
                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="默认IP:" 
                                                      VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding DefaultIpAddress}" 
                                                    Watermark="*************" Margin="0,0,10,10"/>
                                            
                                            <TextBlock Grid.Row="0" Grid.Column="2" Text="默认端口:" 
                                                      VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <NumericUpDown Grid.Row="0" Grid.Column="3" Value="{Binding DefaultPort}" 
                                                          Minimum="1" Maximum="65535" Margin="0,0,0,10"/>
                                            
                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="超时时间:" 
                                                      VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <NumericUpDown Grid.Row="1" Grid.Column="1" Value="{Binding TimeoutMs}" 
                                                          Minimum="100" Maximum="30000" Increment="100" 
                                                          Margin="0,0,10,10"/>
                                            
                                            <TextBlock Grid.Row="1" Grid.Column="2" Text="重试次数:" 
                                                      VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <NumericUpDown Grid.Row="1" Grid.Column="3" Value="{Binding RetryCount}" 
                                                          Minimum="0" Maximum="10" Margin="0,0,0,10"/>
                                            
                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="轮询间隔:" 
                                                      VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <NumericUpDown Grid.Row="2" Grid.Column="1" Value="{Binding PollingInterval}" 
                                                          Minimum="100" Maximum="60000" Increment="100" 
                                                          Margin="0,0,10,0"/>
                                            
                                            <CheckBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" 
                                                     IsChecked="{Binding AutoReconnect}" 
                                                     Content="自动重连" VerticalAlignment="Center"/>
                                        </Grid>
                                    </StackPanel>
                                </Border>

                            </StackPanel>
                        </ScrollViewer>
                    </TabViewItem>

                </TabView>
            </Grid>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="15">
                    <TextBlock Text="{Binding TotalTemplates, StringFormat='模板总数: {0}'}" 
                              VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalDataPoints, StringFormat='数据点: {0}'}" 
                              VerticalAlignment="Center"/>
                    <Button Content="保存" Command="{Binding SaveCommand}" Classes="accent"/>
                    <Button Content="关闭" Command="{Binding CloseCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</Window>
