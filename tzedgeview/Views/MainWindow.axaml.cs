using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using System;
using Avalonia.Styling;
using tzedgeview.Core;
using tzedgeview.ViewModels;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;
using Semi.Avalonia;

namespace tzedgeview.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            InitializeEventHandlers();
            ApplyTheme();
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            // 文件菜单
            if (this.FindControl<MenuItem>("mnuNewServer") is MenuItem mnuNewServer)
                mnuNewServer.Click += MnuNewServer_Click;

            if (this.FindControl<MenuItem>("mnuExit") is MenuItem mnuExit)
                mnuExit.Click += MnuExit_Click;

            // 视图菜单
            if (this.FindControl<MenuItem>("mnuServerManager") is MenuItem mnuServerManager)
                mnuServerManager.Click += MnuServerManager_Click;

            if (this.FindControl<MenuItem>("mnuDeviceMonitor") is MenuItem mnuDeviceMonitor)
                mnuDeviceMonitor.Click += MnuDeviceMonitor_Click;

            if (this.FindControl<MenuItem>("mnuDeviceEdge") is MenuItem mnuDeviceEdge)
                mnuDeviceEdge.Click += MnuDeviceEdge_Click;

            if (this.FindControl<MenuItem>("mnuAlarmList") is MenuItem mnuAlarmList)
                mnuAlarmList.Click += MnuAlarmList_Click;

            if (this.FindControl<MenuItem>("mnuRefresh") is MenuItem mnuRefresh)
                mnuRefresh.Click += MnuRefresh_Click;

            // 工具菜单
            if (this.FindControl<MenuItem>("mnuConnectionTest") is MenuItem mnuConnectionTest)
                mnuConnectionTest.Click += MnuConnectionTest_Click;

            if (this.FindControl<MenuItem>("mnuLogViewer") is MenuItem mnuLogViewer)
                mnuLogViewer.Click += MnuLogViewer_Click;

            if (this.FindControl<MenuItem>("mnuTemplateManager") is MenuItem mnuTemplateManager)
                mnuTemplateManager.Click += MnuTemplateManager_Click;

            if (this.FindControl<MenuItem>("mnuSettings") is MenuItem mnuSettings)
                mnuSettings.Click += MnuSettings_Click;

            // 主题菜单
            if (this.FindControl<MenuItem>("mnuLightTheme") is MenuItem mnuLightTheme)
                mnuLightTheme.Click += MnuLightTheme_Click;

            if (this.FindControl<MenuItem>("mnuDarkTheme") is MenuItem mnuDarkTheme)
                mnuDarkTheme.Click += MnuDarkTheme_Click;

            // 帮助菜单
            if (this.FindControl<MenuItem>("mnuUserManual") is MenuItem mnuUserManual)
                mnuUserManual.Click += MnuUserManual_Click;

            if (this.FindControl<MenuItem>("mnuTechSupport") is MenuItem mnuTechSupport)
                mnuTechSupport.Click += MnuTechSupport_Click;

            if (this.FindControl<MenuItem>("mnuAbout") is MenuItem mnuAbout)
                mnuAbout.Click += MnuAbout_Click;
        }

        /// <summary>
        /// 应用主题
        /// </summary>
        private void ApplyTheme()
        {
            ThemeManager.ApplyThemeToWindow(this);
        }

        #region Event Handlers

        private async void MnuNewServer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var serverAddWindow = new FormServerAdd();
                var result = await serverAddWindow.ShowDialog<bool?>(this);

                if (result == true)
                {
                    // 刷新服务器列表
                    (DataContext as MainWindowViewModel)?.frmedgeserverVM?.LoadServerListFromTxt();
                }
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"打开服务器添加窗口失败: {ex.Message}",
                               ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        private void MnuExit_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MnuServerManager_Click(object sender, RoutedEventArgs e)
        {
            // 显示服务器管理器
        }

        private void MnuDeviceMonitor_Click(object sender, RoutedEventArgs e)
        {
            // 显示设备监控界面
        }

        private void MnuDeviceEdge_Click(object sender, RoutedEventArgs e)
        {
            // 显示设备边缘监控
            var selectedServer = (DataContext as MainWindowViewModel)?.SelectDeviceNodes;
            if (selectedServer != null)
            {
                var deviceEdgeControl = new FormDeviceEdge(selectedServer);
                // 这里需要添加到主界面的逻辑
            }
        }

        private void MnuAlarmList_Click(object sender, RoutedEventArgs e)
        {
            // 显示报警列表
        }

        private void MnuRefresh_Click(object sender, RoutedEventArgs e)
        {
            (DataContext as MainWindowViewModel)?.frmedgeserverVM?.LoadServerListFromTxt();
        }

        private async void MnuConnectionTest_Click(object sender, RoutedEventArgs e)
        {
            var selectedServer = (DataContext as MainWindowViewModel)?.SelectDeviceNodes;
            if (selectedServer != null)
            {
                var result = await EdgeServerHelper.TestConnection(selectedServer);

                var messageBox = MessageBoxManager
                                 .GetMessageBoxStandard(
                                     result.IsSuccess ? "成功" : "失败",
                                     result.IsSuccess ? "连接测试成功" : $"连接测试失败: {result.Message}",
                                     ButtonEnum.Ok,
                                     result.IsSuccess ? MsBox.Avalonia.Enums.Icon.None : MsBox.Avalonia.Enums.Icon.Error);
                await messageBox.ShowAsync();
            }
            else
            {
                var warningBox = MessageBoxManager
                                 .GetMessageBoxStandard("警告", "请先选择一个服务器",
                                 ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.Warning);
                await warningBox.ShowAsync();
            }
        }

        private void MnuLogViewer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logViewWindow = new FormLogView();
                logViewWindow.Show();
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"打开日志查看器失败: {ex.Message}",
                               ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.Error);
                _ = errorBox.ShowAsync();
            }
        }

        private void MnuTemplateManager_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var templateWindow = new FormTemplateDevice();
                templateWindow.Show();
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"打开设备模板管理器失败: {ex.Message}",
                               ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.Error);
                _ = errorBox.ShowAsync();
            }
        }

        private async void MnuSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsWindow = new FormSettings();
                var result = await settingsWindow.ShowDialog<bool?>(this);

                if (result == true)
                {
                    // 设置已保存，可能需要刷新界面
                    ApplyTheme();
                }
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"打开设置窗口失败: {ex.Message}",
                               ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        private void MnuLightTheme_Click(object sender, RoutedEventArgs e)
        {
            if (Application.Current is { } app)
            {
                app.RequestedThemeVariant = ThemeVariant.Light;
            }
        }

        private void MnuDarkTheme_Click(object sender, RoutedEventArgs e)
        {
            if (Application.Current is { } app)
            {
                app.RequestedThemeVariant = SemiTheme.NightSky;
            }
        }

        private async void MnuUserManual_Click(object sender, RoutedEventArgs e)
        {
            var infoBox = MessageBoxManager
                          .GetMessageBoxStandard("用户手册", "用户手册功能正在开发中...",
                          ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.None);
            await infoBox.ShowAsync();
        }

        private async void MnuTechSupport_Click(object sender, RoutedEventArgs e)
        {
            var infoBox = MessageBoxManager
                          .GetMessageBoxStandard("技术支持", "技术支持联系方式：\n邮箱：<EMAIL>\n电话：400-123-4567",
                          ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.None);
            await infoBox.ShowAsync();
        }

        private async void MnuAbout_Click(object sender, RoutedEventArgs e)
        {
            var version = Util.GetApplicationVersion();
            var aboutText = $"tzedgeview 边缘网关客户端\n\n版本：{version}\n发布日期：{Util.ReleaseDate}\n\n{Util.CopyRightAuthor}";

            var aboutBox = MessageBoxManager
                           .GetMessageBoxStandard("关于", aboutText,
                           ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.None);
            await aboutBox.ShowAsync();
        }

        #endregion

        protected override void OnClosing(WindowClosingEventArgs e)
        {
            // 保存窗口状态
            if (Util.ViewerSettings != null)
            {
                Util.ViewerSettings.WindowWidth = Width;
                Util.ViewerSettings.WindowHeight = Height;
                Util.ViewerSettings.WindowX = Position.X;
                Util.ViewerSettings.WindowY = Position.Y;
                Util.ViewerSettings.IsMaximized = WindowState == WindowState.Maximized;
                Util.ViewerSettings.SaveFile();
            }

            base.OnClosing(e);
        }
    }
}