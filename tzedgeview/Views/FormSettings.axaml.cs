using Avalonia.Controls;
using tzedgeview.ViewModels;
using tzedgeview.Core;

namespace tzedgeview.Views
{
    /// <summary>
    /// 应用程序设置窗体
    /// </summary>
    public partial class FormSettings : Window
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public FormSettings()
        {
            InitializeComponent();
            
            // 创建并设置ViewModel
            var viewModel = new FormSettingsViewModel();
            DataContext = viewModel;
            
            // 应用主题
            ApplyTheme();
        }

        #endregion

        #region Properties

        /// <summary>
        /// 获取当前的ViewModel
        /// </summary>
        public FormSettingsViewModel ViewModel => DataContext as FormSettingsViewModel;

        /// <summary>
        /// 对话框结果
        /// </summary>
        public bool? DialogResult { get; private set; }

        #endregion

        #region Methods

        /// <summary>
        /// 应用主题
        /// </summary>
        private void ApplyTheme()
        {
            try
            {
                ThemeManager.ApplyThemeToWindow(this);
            }
            catch (System.Exception ex)
            {
                System.Console.WriteLine($"Error applying theme: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置对话框结果并关闭
        /// </summary>
        /// <param name="result">结果</param>
        public void SetDialogResult(bool? result)
        {
            DialogResult = result;
            Close();
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// 窗口关闭时调用
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnClosing(WindowClosingEventArgs e)
        {
            // 如果没有设置结果，默认为取消
            if (DialogResult == null)
            {
                DialogResult = false;
            }
            
            base.OnClosing(e);
        }

        #endregion
    }
}
