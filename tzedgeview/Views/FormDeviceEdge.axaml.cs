using Avalonia.Controls;
using Avalonia.Interactivity;
using tzedgeview.ViewModels;
using tzedgeview.Core;

namespace tzedgeview.Views
{
    /// <summary>
    /// 设备边缘监控窗体
    /// </summary>
    public partial class FormDeviceEdge : UserControl
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public FormDeviceEdge()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 实例化一个指定服务器设置的对象
        /// </summary>
        /// <param name="serverSettings">服务器设置</param>
        public FormDeviceEdge(EdgeServerSettings serverSettings)
        {
            InitializeComponent();
            
            // 创建并设置ViewModel
            var viewModel = new FormDeviceEdgeViewModel(serverSettings);
            DataContext = viewModel;
            
            // 应用主题
            ApplyTheme();
        }

        #endregion

        #region Properties

        /// <summary>
        /// 获取当前的ViewModel
        /// </summary>
        public FormDeviceEdgeViewModel ViewModel => DataContext as FormDeviceEdgeViewModel;

        #endregion

        #region Methods

        /// <summary>
        /// 应用主题
        /// </summary>
        private void ApplyTheme()
        {
            try
            {
                // 根据当前主题设置样式
                ThemeManager.ApplyThemeToWindow(null); // 这里传入null，因为这是UserControl
            }
            catch (System.Exception ex)
            {
                System.Console.WriteLine($"Error applying theme: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动数据监控
        /// </summary>
        public void StartMonitoring()
        {
            ViewModel?.StartMonitoring();
        }

        /// <summary>
        /// 停止数据监控
        /// </summary>
        public void StopMonitoring()
        {
            ViewModel?.StopMonitoring();
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshData()
        {
            ViewModel?.RefreshData();
        }

        /// <summary>
        /// 设置刷新间隔
        /// </summary>
        /// <param name="interval">间隔时间（毫秒）</param>
        public void SetRefreshInterval(int interval)
        {
            ViewModel?.SetRefreshInterval(interval);
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// 控件加载完成事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 启动监控
            StartMonitoring();
        }

        /// <summary>
        /// 控件卸载事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            // 停止监控
            StopMonitoring();
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// 控件附加到可视化树时调用
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnAttachedToVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
        {
            base.OnAttachedToVisualTree(e);
            
            // 启动监控
            StartMonitoring();
        }

        /// <summary>
        /// 控件从可视化树分离时调用
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnDetachedFromVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
        {
            base.OnDetachedFromVisualTree(e);
            
            // 停止监控
            StopMonitoring();
        }

        #endregion
    }
}
