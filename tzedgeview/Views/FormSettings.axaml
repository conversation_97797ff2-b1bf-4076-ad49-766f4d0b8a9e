<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:tzedgeview.ViewModels"
        mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="500"
        x:Class="tzedgeview.Views.FormSettings"
        x:DataType="vm:FormSettingsViewModel"
        Title="应用程序设置"
        Width="600" Height="500"
        MinWidth="500" MinHeight="400"
        WindowStartupLocation="CenterOwner"
        CanResize="True">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 主要内容 -->
        <TabControl Grid.Row="0">

            <!-- 常规设置 -->
            <TabItem Header="常规">
                <ScrollViewer Padding="20">
                    <StackPanel Spacing="20">
                        
                        <!-- 应用程序设置 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="12">
                                <TextBlock Text="应用程序设置" FontWeight="Bold" FontSize="14"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="语言:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <ComboBox Grid.Row="0" Grid.Column="1" SelectedItem="{Binding Language}" 
                                             ItemsSource="{Binding Languages}" Margin="0,0,0,10"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="主题:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <ComboBox Grid.Row="1" Grid.Column="1" SelectedItem="{Binding Theme}" 
                                             ItemsSource="{Binding Themes}" Margin="0,0,0,10"/>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="启动时:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <StackPanel Grid.Row="2" Grid.Column="1" Spacing="5" Margin="0,0,0,10">
                                        <CheckBox IsChecked="{Binding AutoConnectLastServer}" 
                                                 Content="自动连接上次的服务器"/>
                                        <CheckBox IsChecked="{Binding MinimizeToTray}" 
                                                 Content="最小化到系统托盘"/>
                                    </StackPanel>
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="其他:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <StackPanel Grid.Row="3" Grid.Column="1" Spacing="5">
                                        <CheckBox IsChecked="{Binding ShowDebugInfo}" 
                                                 Content="显示调试信息"/>
                                        <CheckBox IsChecked="{Binding EnableSoundAlert}" 
                                                 Content="启用声音提醒"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- 数据刷新设置 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="12">
                                <TextBlock Text="数据刷新设置" FontWeight="Bold" FontSize="14"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="60"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="刷新间隔:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <Slider Grid.Row="0" Grid.Column="1" Value="{Binding DataRefreshInterval}" 
                                           Minimum="500" Maximum="10000" TickFrequency="500" 
                                           IsSnapToTickEnabled="True" Margin="0,0,10,10"/>
                                    <TextBlock Grid.Row="0" Grid.Column="2" Text="{Binding DataRefreshInterval, StringFormat={}{0}ms}"
                                              VerticalAlignment="Center" Margin="0,0,0,10"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="超时时间:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <Slider Grid.Row="1" Grid.Column="1" Value="{Binding ConnectionTimeout}" 
                                           Minimum="5" Maximum="60" TickFrequency="5" 
                                           IsSnapToTickEnabled="True" Margin="0,0,10,10"/>
                                    <TextBlock Grid.Row="1" Grid.Column="2" Text="{Binding ConnectionTimeout, StringFormat={}{0}s}"
                                              VerticalAlignment="Center" Margin="0,0,0,10"/>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="重试次数:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <Slider Grid.Row="2" Grid.Column="1" Value="{Binding RetryCount}" 
                                           Minimum="0" Maximum="10" TickFrequency="1" 
                                           IsSnapToTickEnabled="True" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Row="2" Grid.Column="2" Text="{Binding RetryCount, StringFormat={}{0}次}"
                                              VerticalAlignment="Center"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 日志设置 -->
            <TabItem Header="日志">
                <ScrollViewer Padding="20">
                    <StackPanel Spacing="20">
                        
                        <!-- 日志级别设置 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="12">
                                <TextBlock Text="日志设置" FontWeight="Bold" FontSize="14"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="日志级别:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <ComboBox Grid.Row="0" Grid.Column="1" SelectedItem="{Binding LogLevel}" 
                                             ItemsSource="{Binding LogLevels}" Margin="0,0,0,10"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="日志文件:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Spacing="10" Margin="0,0,0,10">
                                        <TextBox Text="{Binding LogFilePath}" IsReadOnly="True" Width="300"/>
                                        <Button Content="浏览..." Command="{Binding BrowseLogFileCommand}"/>
                                    </StackPanel>
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="最大文件大小:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Spacing="10" Margin="0,0,0,10">
                                        <NumericUpDown Value="{Binding MaxLogFileSize}" 
                                                      Minimum="1" Maximum="1000" Increment="1" Width="100"/>
                                        <TextBlock Text="MB" VerticalAlignment="Center"/>
                                    </StackPanel>
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="选项:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <StackPanel Grid.Row="3" Grid.Column="1" Spacing="5">
                                        <CheckBox IsChecked="{Binding EnableFileLogging}" 
                                                 Content="启用文件日志"/>
                                        <CheckBox IsChecked="{Binding AutoDeleteOldLogs}" 
                                                 Content="自动删除旧日志文件"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 网络设置 -->
            <TabItem Header="网络">
                <ScrollViewer Padding="20">
                    <StackPanel Spacing="20">
                        
                        <!-- 代理设置 -->
                        <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                                CornerRadius="5" Padding="15">
                            <StackPanel Spacing="12">
                                <TextBlock Text="代理设置" FontWeight="Bold" FontSize="14"/>
                                
                                <CheckBox IsChecked="{Binding UseProxy}" Content="使用代理服务器"/>
                                
                                <Grid IsEnabled="{Binding UseProxy}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="100"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="60"/>
                                        <ColumnDefinition Width="100"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="代理地址:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding ProxyAddress}" 
                                            Watermark="127.0.0.1" Margin="0,0,10,10"/>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="2" Text="端口:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <NumericUpDown Grid.Row="0" Grid.Column="3" Value="{Binding ProxyPort}" 
                                                  Minimum="1" Maximum="65535" Margin="0,0,0,10"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="用户名:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding ProxyUsername}" 
                                            Watermark="可选" Margin="0,0,10,0"/>
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="2" Text="密码:" 
                                              VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding ProxyPassword}" 
                                            PasswordChar="*" Watermark="可选"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>

        </TabControl>

        <!-- 按钮栏 -->
        <Border Grid.Row="1" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,1,0,0" Padding="0,15,0,0" Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Right" 
                       Spacing="10">
                <Button Content="确定" 
                       Command="{Binding OkCommand}"
                       Classes="accent"
                       Width="80"/>
                <Button Content="取消" 
                       Command="{Binding CancelCommand}"
                       Width="80"/>
                <Button Content="应用" 
                       Command="{Binding ApplyCommand}"
                       Width="80"/>
                <Button Content="重置" 
                       Command="{Binding ResetCommand}"
                       Width="80"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
