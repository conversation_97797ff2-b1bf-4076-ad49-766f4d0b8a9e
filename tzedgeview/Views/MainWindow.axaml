<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:tzedgeview.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="tzedgeview.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Icon="/Assets/HslCommunication4.ico"
        Title="tzedgeview"
		>
	<DockPanel>
		<Menu DockPanel.Dock="Top">
			<MenuItem Header="文件">
				<MenuItem Header="新建...">
					<MenuItem Header="采集配置文件"/>
				</MenuItem>				
				<Separator/>
				<MenuItem Header="打开...">
					<MenuItem Header="打开采集配置文件"/>
				</MenuItem>
			</MenuItem>
			<MenuItem Header="视图">
				<MenuItem Header="边缘网关管理器"/>
				<MenuItem Header="边缘网关监控界面"/>
			</MenuItem>
			<MenuItem Header="关于">
			</MenuItem>
		</Menu>
		<DockPanel DockPanel.Dock="Bottom">
			<TextBlock Name="lbldevice" DockPanel.Dock="Left" Text="设备：" Width="260" />
			<TextBlock Name="lblmessage" DockPanel.Dock="Left" Text="消息：" Width="260" />
			<TextBlock Name="lbltime" DockPanel.Dock="Right" Text="" Width="131" />
			<TextBlock Name="lblmemo" DockPanel.Dock="Left" Text="" />
		</DockPanel>
		<Grid ColumnDefinitions="286,*">
			<Grid RowDefinitions="40,*" Grid.Column="0">
				<StackPanel Orientation="Horizontal" Grid.Row="0">
					<ComboBox Name="cboview" Width="180" Margin="16,2,2,2" ItemsSource="{Binding DeviceNodes}"
							  SelectedItem="{Binding SelectDeviceNodes}" >
					</ComboBox>
					<Button Name="btnstop" Width="32" Height="32" Margin="2,2,2,2">
						<Button.Content>
							<Image x:Name="imgstop" Width="16" Height="16" Stretch="UniformToFill" Source="/Assets/images/StatusAnnotations_Pause_16xLG_color.png" />
						</Button.Content>
					</Button>
					<Button Name="btnrestart" Width="32" Height="32" Margin="2,2,2,2">
						<Button.Content>
							<Image x:Name="imgrestart" Width="16" Height="16" Stretch="UniformToFill" Source="/Assets/images/refresh_16xLG.png" />
						</Button.Content>
					</Button>
				</StackPanel>
				<Panel Name="pnlserver" Margin="2" Grid.Row="1" HorizontalAlignment="Stretch" />
			</Grid>
			<Grid ColumnDefinitions="*" Grid.Column="1">
				<Border Name="pnlcontent" Margin="2" Grid.Column="0" BorderBrush="Black" BorderThickness="1" HorizontalAlignment="Stretch" >
					<Grid RowDefinitions="*">
						<Grid Name="pnlmonitor" Grid.Row="0" Margin="2" />
					</Grid>

				</Border>
			</Grid>
		</Grid>
	</DockPanel>
    
</Window>
