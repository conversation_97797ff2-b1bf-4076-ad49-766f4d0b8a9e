<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:tzedgeview.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="tzedgeview.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Icon="/Assets/HslCommunication4.ico"
        Title="tzedgeview"
		>
	<DockPanel>
		<Menu DockPanel.Dock="Top">
			<MenuItem Header="文件">
				<MenuItem Header="新建...">
					<MenuItem Header="采集配置文件"/>
					<MenuItem Header="服务器连接" Name="mnuNewServer"/>
				</MenuItem>
				<Separator/>
				<MenuItem Header="打开...">
					<MenuItem Header="打开采集配置文件"/>
					<MenuItem Header="导入服务器配置"/>
				</MenuItem>
				<Separator/>
				<MenuItem Header="导出服务器配置"/>
				<MenuItem Header="退出" Name="mnuExit"/>
			</MenuItem>
			<MenuItem Header="视图">
				<MenuItem Header="边缘网关管理器" Name="mnuServerManager"/>
				<MenuItem Header="边缘网关监控界面" Name="mnuDeviceMonitor"/>
				<MenuItem Header="设备边缘监控" Name="mnuDeviceEdge"/>
				<MenuItem Header="报警列表" Name="mnuAlarmList"/>
				<Separator/>
				<MenuItem Header="刷新" Name="mnuRefresh"/>
			</MenuItem>
			<MenuItem Header="工具">
				<MenuItem Header="连接测试" Name="mnuConnectionTest"/>
				<MenuItem Header="日志查看器" Name="mnuLogViewer"/>
				<MenuItem Header="设备模板管理" Name="mnuTemplateManager"/>
				<Separator/>
				<MenuItem Header="设置" Name="mnuSettings"/>
			</MenuItem>
			<MenuItem Header="主题">
				<MenuItem Header="浅色主题" Name="mnuLightTheme"/>
				<MenuItem Header="深色主题" Name="mnuDarkTheme"/>
			</MenuItem>
			<MenuItem Header="帮助">
				<MenuItem Header="用户手册" Name="mnuUserManual"/>
				<MenuItem Header="技术支持" Name="mnuTechSupport"/>
				<Separator/>
				<MenuItem Header="关于" Name="mnuAbout"/>
			</MenuItem>
		</Menu>
		<DockPanel DockPanel.Dock="Bottom">
			<TextBlock Name="lbldevice" DockPanel.Dock="Left" Text="设备：" Width="260" />
			<TextBlock Name="lblmessage" DockPanel.Dock="Left" Text="消息：" Width="260" />
			<TextBlock Name="lbltime" DockPanel.Dock="Right" Text="" Width="131" />
			<TextBlock Name="lblmemo" DockPanel.Dock="Left" Text="" />
		</DockPanel>
		<Grid ColumnDefinitions="286,*">
			<Grid RowDefinitions="40,*" Grid.Column="0">
				<StackPanel Orientation="Horizontal" Grid.Row="0">
					<ComboBox Name="cboview" Width="180" Margin="16,2,2,2" ItemsSource="{Binding DeviceNodes}"
							  SelectedItem="{Binding SelectDeviceNodes}" >
					</ComboBox>
					<Button Name="btnstop" Width="32" Height="32" Margin="2,2,2,2">
						<Button.Content>
							<Image x:Name="imgstop" Width="16" Height="16" Stretch="UniformToFill" Source="/Assets/images/StatusAnnotations_Pause_16xLG_color.png" />
						</Button.Content>
					</Button>
					<Button Name="btnrestart" Width="32" Height="32" Margin="2,2,2,2">
						<Button.Content>
							<Image x:Name="imgrestart" Width="16" Height="16" Stretch="UniformToFill" Source="/Assets/images/refresh_16xLG.png" />
						</Button.Content>
					</Button>
				</StackPanel>
				<Panel Name="pnlserver" Margin="2" Grid.Row="1" HorizontalAlignment="Stretch" />
			</Grid>
			<Grid ColumnDefinitions="*" Grid.Column="1">
				<Border Name="pnlcontent" Margin="2" Grid.Column="0" BorderBrush="Black" BorderThickness="1" HorizontalAlignment="Stretch" >
					<Grid RowDefinitions="*">
						<Grid Name="pnlmonitor" Grid.Row="0" Margin="2" />
					</Grid>

				</Border>
			</Grid>
		</Grid>
	</DockPanel>
    
</Window>
