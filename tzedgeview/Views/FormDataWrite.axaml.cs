using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using HslTechnology.Edge.Node.Render;
using MsBox.Avalonia;
using MsBox.Avalonia.Enums;
using System;
using System.Threading.Tasks;

namespace tzedgeview.Views
{
    /// <summary>
    /// 数据写入对话框
    /// </summary>
    public partial class FormDataWrite : Window
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public FormDataWrite()
        {
            InitializeComponent();
            
            // 设置窗口属性
            this.WindowStartupLocation = WindowStartupLocation.CenterOwner;
            this.CanResize = false;
            
            // 设置焦点到新值输入框
            this.Opened += FormDataWrite_Opened;
        }

        #endregion

        #region Properties

        /// <summary>
        /// 对话框结果
        /// </summary>
        public bool? DialogResult { get; private set; }

        /// <summary>
        /// 获取输入的数据值
        /// </summary>
        public string DataValue => txtNewValue.Text ?? string.Empty;

        #endregion

        #region Private Fields

        private ScalarDataNode? scalarDataNode;
        private string valueNode = string.Empty;

        #endregion

        #region Methods

        /// <summary>
        /// 设置数据节点信息
        /// </summary>
        /// <param name="scalarDataNode">标量数据节点</param>
        /// <param name="valueNode">值节点</param>
        /// <param name="valueOld">旧值</param>
        public void SetDataNode(ScalarDataNode scalarDataNode, string valueNode, string valueOld)
        {
            this.scalarDataNode = scalarDataNode;
            this.valueNode = valueNode;
            
            txtDataNode.Text = valueNode;
            txtCurrentValue.Text = valueOld;
            txtDataType.Text = scalarDataNode.GetDataTypeText();
            
            // 设置新值输入框的初始值为当前值
            txtNewValue.Text = valueOld;
        }

        /// <summary>
        /// 窗口打开时的处理
        /// </summary>
        private void FormDataWrite_Opened(object? sender, EventArgs e)
        {
            // 设置焦点到新值输入框并选中所有文本
            txtNewValue.Focus();
            txtNewValue.SelectAll();
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private async void BtnOK_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                if (scalarDataNode == null)
                {
                    await ShowErrorMessage("数据节点信息无效");
                    return;
                }

                // 验证输入值的合法性
                string newValue = txtNewValue.Text ?? string.Empty;
                if (string.IsNullOrWhiteSpace(newValue))
                {
                    await ShowErrorMessage("请输入新的数值");
                    txtNewValue.Focus();
                    return;
                }

                // 检查输入字符串是否合法
                scalarDataNode.CheckInputStringLegal(newValue);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                await ShowErrorMessage($"当前的输入不合法，需要输入字符串可转换类型 {scalarDataNode?.GetDataTypeText()} 的值！\n原因：{ex.Message}");
                txtNewValue.Focus();
                txtNewValue.SelectAll();
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        private async Task ShowErrorMessage(string message)
        {
            var box = MessageBoxManager.GetMessageBoxStandard("写入异常", message, ButtonEnum.Ok, MsBox.Avalonia.Enums.Icon.Error);
            await box.ShowWindowDialogAsync(this);
        }

        #endregion
    }
}
