<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:tzedgeview.ViewModels"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
             x:Class="tzedgeview.Views.FormAlarmList"
             x:DataType="vm:FormAlarmListViewModel">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding Title}" 
                          FontSize="16" FontWeight="Bold" 
                          VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                    <Button Name="btnRefresh" Content="刷新" 
                            Command="{Binding RefreshCommand}"
                            Classes="accent"/>
                    <Button Name="btnClear" Content="清除" 
                            Command="{Binding ClearCommand}"/>
                    <Button Name="btnExport" Content="导出" 
                            Command="{Binding ExportCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 过滤器栏 -->
        <Border Grid.Row="1" Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="报警级别:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Grid.Column="1" SelectedItem="{Binding SelectedAlarmLevel}" 
                          ItemsSource="{Binding AlarmLevels}" Margin="0,0,10,0"/>
                
                <TextBlock Grid.Column="2" Text="状态:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Grid.Column="3" SelectedItem="{Binding SelectedStatus}" 
                          ItemsSource="{Binding StatusList}" Margin="0,0,10,0"/>
                
                <TextBlock Grid.Column="4" Text="时间范围:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Grid.Column="5" SelectedItem="{Binding SelectedTimeRange}" 
                          ItemsSource="{Binding TimeRanges}" Margin="0,0,10,0"/>
                
                <Button Grid.Column="7" Content="应用过滤" 
                        Command="{Binding ApplyFilterCommand}"/>
            </Grid>
        </Border>

        <!-- 报警列表 -->
        <DataGrid Grid.Row="2" ItemsSource="{Binding AlarmList}" 
                  SelectedItem="{Binding SelectedAlarm}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  CanUserReorderColumns="True"
                  CanUserResizeColumns="True"
                  CanUserSortColumns="True">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="序号" Binding="{Binding Id}" Width="60" CanUserResize="False"/>
                <DataGridTextColumn Header="报警时间" Binding="{Binding AlarmTime}" Width="150" SortMemberPath="AlarmTime"/>
                <DataGridTextColumn Header="设备路径" Binding="{Binding DevicePath}" Width="200"/>
                <DataGridTextColumn Header="报警级别" Binding="{Binding Level}" Width="100">
                    <DataGridTextColumn.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Level}" Value="严重">
                                    <Setter Property="Foreground" Value="Red"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Level}" Value="警告">
                                    <Setter Property="Foreground" Value="Orange"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Level}" Value="信息">
                                    <Setter Property="Foreground" Value="Blue"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGridTextColumn.CellStyle>
                </DataGridTextColumn>
                <DataGridTextColumn Header="报警类型" Binding="{Binding AlarmType}" Width="120"/>
                <DataGridTextColumn Header="报警内容" Binding="{Binding Message}" Width="*"/>
                <DataGridTextColumn Header="确认状态" Binding="{Binding AckStatus}" Width="100"/>
                <DataGridTextColumn Header="确认时间" Binding="{Binding AckTime}" Width="150"/>
                <DataGridTextColumn Header="确认用户" Binding="{Binding AckUser}" Width="100"/>
                <DataGridTextColumn Header="持续时间" Binding="{Binding Duration}" Width="100"/>
            </DataGrid.Columns>

            <DataGrid.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="确认报警" Command="{Binding AcknowledgeCommand}"/>
                    <MenuItem Header="查看详情" Command="{Binding ViewDetailsCommand}"/>
                    <Separator/>
                    <MenuItem Header="复制内容" Command="{Binding CopyCommand}"/>
                    <MenuItem Header="导出选中" Command="{Binding ExportSelectedCommand}"/>
                </ContextMenu>
            </DataGrid.ContextMenu>
        </DataGrid>

        <!-- 状态栏 -->
        <Border Grid.Row="3" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="20">
                    <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding DevicePath, StringFormat='设备路径: {0}'}" 
                              VerticalAlignment="Center" Opacity="0.7"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="15">
                    <TextBlock Text="{Binding TotalAlarms, StringFormat='总计: {0}'}" 
                              VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding UnacknowledgedAlarms, StringFormat='未确认: {0}'}" 
                              VerticalAlignment="Center" Foreground="Red"/>
                    <TextBlock Text="{Binding CriticalAlarms, StringFormat='严重: {0}'}" 
                              VerticalAlignment="Center" Foreground="Red"/>
                    <TextBlock Text="{Binding WarningAlarms, StringFormat='警告: {0}'}" 
                              VerticalAlignment="Center" Foreground="Orange"/>
                    <TextBlock Text="{Binding LastUpdateTime, StringFormat='更新: {0}'}" 
                              VerticalAlignment="Center" FontSize="11" Opacity="0.7"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</UserControl>
