<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <AvaloniaResource Include="Assets\**" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.3.2" />
    <PackageReference Include="Avalonia.Controls.DataGrid" Version="11.3.2" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.2" />
    <PackageReference Include="Avalonia.Svg.Skia" Version="11.3.0" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.2" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.2" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.3.2" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.3.2" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="LiveChartsCore.SkiaSharpView.Avalonia" Version="2.0.0-rc5.4" />
    <PackageReference Include="ScottPlot.Avalonia" Version="4.1.71" />
    <PackageReference Include="MessageBox.Avalonia" Version="3.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Semi.Avalonia" Version="11.2.1.8" />
    <PackageReference Include="Semi.Avalonia.DataGrid" Version="11.2.1.8" />
    <PackageReference Include="System.IO.Ports" Version="10.0.0-preview.5.25277.114" />
  </ItemGroup>


  <ItemGroup>
    <Reference Include="HslCommunication">
      <HintPath>..\..\hsllib\netstandard2.1\HslCommunication.dll</HintPath>
    </Reference>
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\HslTechnology.Edge\HslTechnology.Edge.csproj" />
  </ItemGroup>
</Project>
