using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using System;
using System.Diagnostics.Tracing;
using System.Drawing;
using tzedgeview.Views;

namespace tzedgeview.UserControls;

public partial class DeviceStatusControl : UserControl
{
    public string DeviceName
    {
        get { return GetValue(DeviceNameProperty); }
        set { SetValue(DeviceNameProperty, value); }
    }

    public static readonly StyledProperty<string> DeviceNameProperty =
        AvaloniaProperty.Register<DeviceStatusControl, string>
    (
        nameof(DeviceName)
    );

    public string ActiveTime
    {
        get { return GetValue(ActiveTimeProperty); }
        set { SetValue(ActiveTimeProperty, value); }
    }

    public static readonly StyledProperty<string> ActiveTimeProperty =
        AvaloniaProperty.Register<DeviceStatusControl, string>
    (
        nameof(ActiveTime)
    );

    public string SpendTime
    {
        get { return GetValue(SpendTimeProperty); }
        set { SetValue(SpendTimeProperty, value); }
    }

    public static readonly StyledProperty<string> SpendTimeProperty =
        AvaloniaProperty.Register<DeviceStatusControl, string>
    (
        nameof(SpendTime)
    );

    public string SuccNum
    {
        get { return GetValue(SuccNumProperty); }
        set { SetValue(SuccNumProperty, value); }
    }

    public static readonly StyledProperty<string> SuccNumProperty =
        AvaloniaProperty.Register<DeviceStatusControl, string>
    (
        nameof(SuccNum)
    );

    public string FailtNum
    {
        get { return GetValue(FailtNumProperty); }
        set { SetValue(FailtNumProperty, value); }
    }

    public static readonly StyledProperty<string> FailtNumProperty =
        AvaloniaProperty.Register<DeviceStatusControl, string>
    (
        nameof(FailtNum)
    );

    public string Alarm
    {
        get { return GetValue(AlarmProperty); }
        set { SetValue(AlarmProperty, value); }
    }

    public static readonly StyledProperty<string> AlarmProperty =
        AvaloniaProperty.Register<DeviceStatusControl, string>
    (
        nameof(Alarm)
    );

    public static readonly RoutedEvent<RoutedEventArgs> RequestClickedEvent =
            RoutedEvent.Register<DeviceStatusControl, RoutedEventArgs>(nameof(RequestClicked), RoutingStrategies.Tunnel);

    public event EventHandler<RoutedEventArgs> RequestClicked
    {
        add => AddHandler(RequestClickedEvent, value);
        remove => RemoveHandler(RequestClickedEvent, value);
    }


    public DeviceStatusControl()
    {
        InitializeComponent();
        btnRequest.AddHandler(Button.ClickEvent, OnBtnClicked);
    }

    private void OnBtnClicked(object? sender, RoutedEventArgs e)
    {
        this.RaiseEvent(new RoutedEventArgs(DeviceStatusControl.RequestClickedEvent));
    }

    public void SetSelected(bool select)
    {
        if (select)
        {
            this.Background = new SolidColorBrush(new Avalonia.Media.Color(0xFF,0xf9,0xad,0x29));
        }
        else
        {
            this.Background = new SolidColorBrush(new Avalonia.Media.Color(0xFF, 0xf8, 0xe5, 0xc3));
        }
    }

}