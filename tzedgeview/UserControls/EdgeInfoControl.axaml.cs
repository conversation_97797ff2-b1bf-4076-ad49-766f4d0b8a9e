using Avalonia.Controls;
using Avalonia.Threading;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using HslTechnology.Edge.DataBusiness.Time;

namespace tzedgeview.UserControls
{
    /// <summary>
    /// 边缘信息控件
    /// </summary>
    public partial class EdgeInfoControl : UserControl
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public EdgeInfoControl()
        {
            InitializeComponent();
            InitializeControls();
        }

        #endregion

        #region Private Fields

        private List<EdgeTimeConsume> runtimeHistory = new List<EdgeTimeConsume>();

        #endregion

        #region Methods

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 设置默认值
            ClearAllData();
        }

        /// <summary>
        /// 清除所有数据
        /// </summary>
        public void ClearAllData()
        {
            Dispatcher.UIThread.InvokeAsync(() =>
            {
                // 基本信息
                txtGatewayName.Text = "未知";
                txtGatewayId.Text = "未知";
                txtIpAddress.Text = "未知";
                txtPort.Text = "未知";
                txtVersion.Text = "未知";
                txtStatus.Text = "离线";

                // 系统资源
                txtCpuUsage.Text = "0%";
                txtMemoryUsage.Text = "0 MB";
                txtThreadCount.Text = "0";
                txtHandleCount.Text = "0";
                txtRunTime.Text = "00:00:00";
                txtStartTime.Text = "未知";

                progressCpu.Value = 0;
                progressMemory.Value = 0;

                // 设备统计
                txtTotalDevices.Text = "0";
                txtOnlineDevices.Text = "0";
                txtOfflineDevices.Text = "0";
                txtErrorDevices.Text = "0";

                // 数据统计
                txtTotalDataPoints.Text = "0";
                txtActiveDataPoints.Text = "0";
                txtReadFrequency.Text = "0/s";
                txtWriteFrequency.Text = "0/s";
            });
        }

        /// <summary>
        /// 渲染机器数据
        /// </summary>
        /// <param name="machineData">机器数据</param>
        public void RenderMachineData(JObject machineData)
        {
            if (machineData == null) return;

            try
            {
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    // 解析基本信息
                    if (machineData.ContainsKey("gatewayName"))
                        txtGatewayName.Text = machineData["gatewayName"]?.ToString() ?? "未知";

                    if (machineData.ContainsKey("gatewayId"))
                        txtGatewayId.Text = machineData["gatewayId"]?.ToString() ?? "未知";

                    if (machineData.ContainsKey("ipAddress"))
                        txtIpAddress.Text = machineData["ipAddress"]?.ToString() ?? "未知";

                    if (machineData.ContainsKey("port"))
                        txtPort.Text = machineData["port"]?.ToString() ?? "未知";

                    if (machineData.ContainsKey("version"))
                        txtVersion.Text = machineData["version"]?.ToString() ?? "未知";

                    if (machineData.ContainsKey("status"))
                        txtStatus.Text = machineData["status"]?.ToString() ?? "离线";

                    // 解析系统资源
                    if (machineData.ContainsKey("cpuUsage"))
                    {
                        if (double.TryParse(machineData["cpuUsage"]?.ToString(), out double cpu))
                        {
                            txtCpuUsage.Text = $"{cpu:F1}%";
                            progressCpu.Value = cpu;
                        }
                    }

                    if (machineData.ContainsKey("memoryUsage"))
                    {
                        if (long.TryParse(machineData["memoryUsage"]?.ToString(), out long memory))
                        {
                            txtMemoryUsage.Text = FormatBytes(memory);
                            // 假设总内存为8GB，计算百分比
                            double memoryPercent = (memory / (8.0 * 1024 * 1024 * 1024)) * 100;
                            progressMemory.Value = Math.Min(memoryPercent, 100);
                        }
                    }

                    if (machineData.ContainsKey("threadCount"))
                        txtThreadCount.Text = machineData["threadCount"]?.ToString() ?? "0";

                    if (machineData.ContainsKey("handleCount"))
                        txtHandleCount.Text = machineData["handleCount"]?.ToString() ?? "0";

                    if (machineData.ContainsKey("runTime"))
                    {
                        if (TimeSpan.TryParse(machineData["runTime"]?.ToString(), out TimeSpan runTime))
                        {
                            txtRunTime.Text = FormatTimeSpan(runTime);
                        }
                    }

                    if (machineData.ContainsKey("startTime"))
                    {
                        if (DateTime.TryParse(machineData["startTime"]?.ToString(), out DateTime startTime))
                        {
                            txtStartTime.Text = startTime.ToString("yyyy-MM-dd HH:mm:ss");
                        }
                    }

                    // 解析设备统计
                    if (machineData.ContainsKey("totalDevices"))
                        txtTotalDevices.Text = machineData["totalDevices"]?.ToString() ?? "0";

                    if (machineData.ContainsKey("onlineDevices"))
                        txtOnlineDevices.Text = machineData["onlineDevices"]?.ToString() ?? "0";

                    if (machineData.ContainsKey("offlineDevices"))
                        txtOfflineDevices.Text = machineData["offlineDevices"]?.ToString() ?? "0";

                    if (machineData.ContainsKey("errorDevices"))
                        txtErrorDevices.Text = machineData["errorDevices"]?.ToString() ?? "0";

                    // 解析数据统计
                    if (machineData.ContainsKey("totalDataPoints"))
                        txtTotalDataPoints.Text = machineData["totalDataPoints"]?.ToString() ?? "0";

                    if (machineData.ContainsKey("activeDataPoints"))
                        txtActiveDataPoints.Text = machineData["activeDataPoints"]?.ToString() ?? "0";

                    if (machineData.ContainsKey("readFrequency"))
                        txtReadFrequency.Text = $"{machineData["readFrequency"]}/s";

                    if (machineData.ContainsKey("writeFrequency"))
                        txtWriteFrequency.Text = $"{machineData["writeFrequency"]}/s";
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering machine data: {ex.Message}");
            }
        }

        /// <summary>
        /// 渲染边缘时间数据
        /// </summary>
        /// <param name="edgeTimeData">边缘时间数据</param>
        public void RenderEdgeTime(EdgeTimeConsume[] edgeTimeData)
        {
            if (edgeTimeData == null) return;

            try
            {
                runtimeHistory.Clear();
                runtimeHistory.AddRange(edgeTimeData);

                // 这里可以更新图表显示
                // 由于没有具体的图表控件，暂时只记录数据
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    // 可以在这里更新图表或其他可视化组件
                    Console.WriteLine($"Updated runtime history with {edgeTimeData.Length} entries");
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering edge time data: {ex.Message}");
            }
        }

        /// <summary>
        /// 渲染RAM使用历史
        /// </summary>
        /// <param name="ramHistory">RAM历史数据</param>
        /// <param name="threadHistory">线程历史数据</param>
        public void RenderRamUseHistory(List<double> ramHistory, List<int> threadHistory)
        {
            try
            {
                // 这里可以更新内存和线程使用历史图表
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    Console.WriteLine($"Updated RAM history with {ramHistory?.Count ?? 0} entries");
                    Console.WriteLine($"Updated thread history with {threadHistory?.Count ?? 0} entries");
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering RAM use history: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化字节数
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的字符串</returns>
        private string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 格式化时间跨度
        /// </summary>
        /// <param name="timeSpan">时间跨度</param>
        /// <returns>格式化的字符串</returns>
        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
            {
                return $"{(int)timeSpan.TotalDays}天 {timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
            else
            {
                return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
        }

        #endregion
    }
}
