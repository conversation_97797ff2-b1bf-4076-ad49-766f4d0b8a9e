using Avalonia.Controls;
using Avalonia.Threading;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using HslTechnology.Edge.DataBusiness.Time;
using ReactiveUI;

namespace tzedgeview.UserControls
{
    /// <summary>
    /// 运行时间历史图表控件
    /// </summary>
    public partial class RuntimeHistoryChart : UserControl
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public RuntimeHistoryChart()
        {
            InitializeComponent();
            InitializeChart();
            InitializeEventHandlers();
        }

        #endregion

        #region Properties

        /// <summary>
        /// 图表系列
        /// </summary>
        public ObservableCollection<ISeries> Series { get; set; }

        /// <summary>
        /// X轴配置
        /// </summary>
        public ObservableCollection<Axis> XAxes { get; set; }

        /// <summary>
        /// Y轴配置
        /// </summary>
        public ObservableCollection<Axis> YAxes { get; set; }

        /// <summary>
        /// 是否没有数据
        /// </summary>
        public bool HasNoData { get; private set; } = true;

        #endregion

        #region Private Fields

        private List<EdgeTimeConsume> runtimeData = new List<EdgeTimeConsume>();
        private string selectedTimeRange = "最近1小时";

        #endregion

        #region Methods

        /// <summary>
        /// 初始化图表
        /// </summary>
        private void InitializeChart()
        {
            // 初始化系列
            Series = new ObservableCollection<ISeries>
            {
                new LineSeries<TimeValue>
                {
                    Name = "运行时间",
                    Values = new ObservableCollection<TimeValue>(),
                    Fill = null,
                    Stroke = new SolidColorPaint(SKColors.DodgerBlue) { StrokeThickness = 2 },
                    GeometryFill = new SolidColorPaint(SKColors.DodgerBlue),
                    GeometryStroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 1 },
                    GeometrySize = 4,
                    LineSmoothness = 0.3
                }
            };

            // 初始化X轴（时间轴）
            XAxes = new ObservableCollection<Axis>
            {
                new Axis
                {
                    Name = "时间",
                    NamePaint = new SolidColorPaint(SKColors.Gray),
                    LabelsPaint = new SolidColorPaint(SKColors.Gray),
                    TextSize = 12,
                    SeparatorsPaint = new SolidColorPaint(SKColors.LightGray) { StrokeThickness = 1 },
                    Labeler = value => new DateTime((long)value).ToString("HH:mm")
                }
            };

            // 初始化Y轴（运行时间轴）
            YAxes = new ObservableCollection<Axis>
            {
                new Axis
                {
                    Name = "运行时间 (秒)",
                    NamePaint = new SolidColorPaint(SKColors.Gray),
                    LabelsPaint = new SolidColorPaint(SKColors.Gray),
                    TextSize = 12,
                    SeparatorsPaint = new SolidColorPaint(SKColors.LightGray) { StrokeThickness = 1 },
                    Labeler = value => $"{value:F1}s"
                }
            };

            // 设置数据上下文
            DataContext = this;
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            if (this.FindControl<ComboBox>("cboTimeRange") is ComboBox cboTimeRange)
            {
                cboTimeRange.SelectionChanged += (s, e) =>
                {
                    if (cboTimeRange.SelectedItem is ComboBoxItem item)
                    {
                        selectedTimeRange = item.Content?.ToString() ?? "最近1小时";
                        RefreshChart();
                    }
                };
            }

            if (this.FindControl<Button>("btnRefresh") is Button btnRefresh)
            {
                btnRefresh.Click += (s, e) => RefreshChart();
            }

            if (this.FindControl<Button>("btnExport") is Button btnExport)
            {
                btnExport.Click += (s, e) => ExportChart();
            }
        }

        /// <summary>
        /// 更新运行时间数据
        /// </summary>
        /// <param name="timeData">时间数据</param>
        public void UpdateRuntimeData(EdgeTimeConsume[] timeData)
        {
            if (timeData == null) return;

            try
            {
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    runtimeData.Clear();
                    runtimeData.AddRange(timeData);
                    RefreshChart();
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating runtime data: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加运行时间数据点
        /// </summary>
        /// <param name="timeConsume">时间消耗数据</param>
        public void AddRuntimeDataPoint(EdgeTimeConsume timeConsume)
        {
            if (timeConsume == null) return;

            try
            {
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    runtimeData.Add(timeConsume);
                    
                    // 限制数据点数量
                    if (runtimeData.Count > 1000)
                    {
                        runtimeData.RemoveAt(0);
                    }
                    
                    RefreshChart();
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding runtime data point: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新图表
        /// </summary>
        private void RefreshChart()
        {
            try
            {
                var filteredData = FilterDataByTimeRange();
                var chartData = ConvertToChartData(filteredData);

                if (Series.Count > 0 && Series[0] is LineSeries<TimeValue> lineSeries)
                {
                    lineSeries.Values = new ObservableCollection<TimeValue>(chartData);
                }

                HasNoData = chartData.Count == 0;
                UpdateStatistics(filteredData);
                
                // 通知属性变更
                this.RaisePropertyChanged(nameof(HasNoData));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error refreshing chart: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据时间范围过滤数据
        /// </summary>
        /// <returns>过滤后的数据</returns>
        private List<EdgeTimeConsume> FilterDataByTimeRange()
        {
            var now = DateTime.Now;
            var startTime = selectedTimeRange switch
            {
                "最近1小时" => now.AddHours(-1),
                "最近6小时" => now.AddHours(-6),
                "最近24小时" => now.AddDays(-1),
                "最近7天" => now.AddDays(-7),
                "最近30天" => now.AddDays(-30),
                _ => now.AddHours(-1)
            };

            return runtimeData.Where(d => d.StartTime >= startTime).ToList();
        }

        /// <summary>
        /// 转换为图表数据
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>图表数据</returns>
        private List<TimeValue> ConvertToChartData(List<EdgeTimeConsume> data)
        {
            return data.Select(d => new TimeValue
            {
                Time = d.StartTime.Ticks,
                Value = d.ConsumeTime.TotalSeconds
            }).OrderBy(tv => tv.Time).ToList();
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        /// <param name="data">数据</param>
        private void UpdateStatistics(List<EdgeTimeConsume> data)
        {
            try
            {
                if (this.FindControl<TextBlock>("txtDataPoints") is TextBlock txtDataPoints)
                    txtDataPoints.Text = $"数据点: {data.Count}";

                if (this.FindControl<TextBlock>("txtTimeRange") is TextBlock txtTimeRange)
                    txtTimeRange.Text = $"时间范围: {selectedTimeRange}";

                if (data.Count > 0)
                {
                    var avgRuntime = data.Average(d => d.ConsumeTime.TotalSeconds);
                    var maxRuntime = data.Max(d => d.ConsumeTime.TotalSeconds);
                    var minRuntime = data.Min(d => d.ConsumeTime.TotalSeconds);

                    if (this.FindControl<TextBlock>("txtAvgRuntime") is TextBlock txtAvgRuntime)
                        txtAvgRuntime.Text = $"平均运行时间: {avgRuntime:F2}s";

                    if (this.FindControl<TextBlock>("txtMaxRuntime") is TextBlock txtMaxRuntime)
                        txtMaxRuntime.Text = $"最大: {maxRuntime:F2}s";

                    if (this.FindControl<TextBlock>("txtMinRuntime") is TextBlock txtMinRuntime)
                        txtMinRuntime.Text = $"最小: {minRuntime:F2}s";
                }
                else
                {
                    if (this.FindControl<TextBlock>("txtAvgRuntime") is TextBlock txtAvgRuntime)
                        txtAvgRuntime.Text = "平均运行时间: --";

                    if (this.FindControl<TextBlock>("txtMaxRuntime") is TextBlock txtMaxRuntime)
                        txtMaxRuntime.Text = "最大: --";

                    if (this.FindControl<TextBlock>("txtMinRuntime") is TextBlock txtMinRuntime)
                        txtMinRuntime.Text = "最小: --";
                }

                if (this.FindControl<TextBlock>("txtLastUpdate") is TextBlock txtLastUpdate)
                    txtLastUpdate.Text = $"更新: {DateTime.Now:HH:mm:ss}";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出图表
        /// </summary>
        private void ExportChart()
        {
            try
            {
                // 这里可以实现图表导出功能
                Console.WriteLine("Export chart functionality to be implemented");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting chart: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除数据
        /// </summary>
        public void ClearData()
        {
            Dispatcher.UIThread.InvokeAsync(() =>
            {
                runtimeData.Clear();
                RefreshChart();
            });
        }

        #endregion
    }

    #region Data Models

    /// <summary>
    /// 时间值数据模型
    /// </summary>
    public class TimeValue
    {
        public long Time { get; set; }
        public double Value { get; set; }
    }

    #endregion
}
