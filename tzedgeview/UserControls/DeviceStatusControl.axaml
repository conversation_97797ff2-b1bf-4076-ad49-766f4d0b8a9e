<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="150" x:Name="usercontrol"
             x:Class="tzedgeview.UserControls.DeviceStatusControl">
	<Border Background="#FFf8e5c3" Width="400" Height="150">
		<Grid ColumnDefinitions="80,120,80,120" RowDefinitions="32,32,32,32,32">
			<TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Text="设备名称:"/>
			<TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center" Text="活动时间:"/>
			<TextBlock Grid.Row="2" Grid.Column="0" VerticalAlignment="Center" Text="采集耗时:"/>
			<TextBlock Grid.Row="3" Grid.Column="0" VerticalAlignment="Center" Text="成功次数:"/>
			<TextBlock Grid.Row="3" Grid.Column="2" VerticalAlignment="Center" Text="失败次数:"/>
			<TextBlock Grid.Row="0" Grid.Column="1" VerticalAlignment="Center" Text="{Binding #usercontrol.DeviceName}"/>
			<TextBlock Grid.Row="1" Grid.Column="1" VerticalAlignment="Center" Text="{Binding #usercontrol.ActiveTime}:"/>
			<TextBlock Grid.Row="2" Grid.Column="1" VerticalAlignment="Center" Text="{Binding #usercontrol.SpendTime}"/>
			<TextBlock Grid.Row="3" Grid.Column="1" VerticalAlignment="Center" Text="{Binding #usercontrol.SuccNum}"/>
			<TextBlock Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="3" VerticalAlignment="Center" Text="{Binding #usercontrol.FailtNum}"/>
			<Border Background="AliceBlue">
				<TextBlock Grid.Row="0" Grid.Column="0" VerticalAlignment="Center" Text="{Binding #usercontrol.Alarm}"/>				
			</Border>
			<Button Width="80" Height="80" HorizontalAlignment="Center" VerticalAlignment="Center" x:Name="btnRequest"></Button>
		</Grid>
	</Border>
</UserControl>
