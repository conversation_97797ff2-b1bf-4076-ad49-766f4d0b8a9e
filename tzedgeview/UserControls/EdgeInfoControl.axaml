<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:local="using:tzedgeview.UserControls"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
             x:Class="tzedgeview.UserControls.EdgeInfoControl">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,0,0,1">
            <TextBlock Text="边缘网关信息" FontSize="14" FontWeight="Bold" 
                      VerticalAlignment="Center"/>
        </Border>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="1" Padding="10">
            <StackPanel Spacing="15">

                <!-- 基本信息 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <StackPanel Spacing="10">
                        <TextBlock Text="基本信息" FontWeight="Bold" FontSize="13"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="网关名称:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="txtGatewayName" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="网关ID:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Name="txtGatewayId" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="IP地址:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="txtIpAddress" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="端口:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="3" Name="txtPort" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="版本:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="txtVersion" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="状态:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="3" Name="txtStatus" VerticalAlignment="Center"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 系统资源 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <StackPanel Spacing="10">
                        <TextBlock Text="系统资源" FontWeight="Bold" FontSize="13"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="CPU使用率:" VerticalAlignment="Center"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock Name="txtCpuUsage" VerticalAlignment="Center"/>
                                <ProgressBar Name="progressCpu" Width="100" Height="8" Margin="10,0,0,0" 
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="内存使用:" VerticalAlignment="Center"/>
                            <StackPanel Grid.Row="0" Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock Name="txtMemoryUsage" VerticalAlignment="Center"/>
                                <ProgressBar Name="progressMemory" Width="100" Height="8" Margin="10,0,0,0" 
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="线程数:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="txtThreadCount" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="句柄数:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="3" Name="txtHandleCount" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="运行时间:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="txtRunTime" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="启动时间:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="3" Name="txtStartTime" VerticalAlignment="Center"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 设备统计 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <StackPanel Spacing="10">
                        <TextBlock Text="设备统计" FontWeight="Bold" FontSize="13"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="设备总数:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="txtTotalDevices" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="在线设备:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Name="txtOnlineDevices" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="离线设备:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="txtOfflineDevices" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="异常设备:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="3" Name="txtErrorDevices" VerticalAlignment="Center"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 数据统计 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                        CornerRadius="5" Padding="15">
                    <StackPanel Spacing="10">
                        <TextBlock Text="数据统计" FontWeight="Bold" FontSize="13"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="数据点总数:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="txtTotalDataPoints" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="活跃数据点:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Name="txtActiveDataPoints" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="读取频率:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="txtReadFrequency" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="写入频率:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="3" Name="txtWriteFrequency" VerticalAlignment="Center"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 运行时间历史图表 -->
                <Border Background="{DynamicResource SystemControlBackgroundAltHighBrush}"
                        CornerRadius="5" Padding="5">
                    <local:RuntimeHistoryChart Name="runtimeHistoryChart" Height="250"/>
                </Border>

            </StackPanel>
        </ScrollViewer>

    </Grid>
</UserControl>
