<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:lvc="using:LiveChartsCore.SkiaSharpView.Avalonia"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="400"
             x:Class="tzedgeview.UserControls.RuntimeHistoryChart">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和控制栏 -->
        <Border Grid.Row="0" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="运行时间历史" FontWeight="Bold" 
                          VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                    <ComboBox Name="cboTimeRange" SelectedIndex="0" Width="120">
                        <ComboBoxItem Content="最近1小时"/>
                        <ComboBoxItem Content="最近6小时"/>
                        <ComboBoxItem Content="最近24小时"/>
                        <ComboBoxItem Content="最近7天"/>
                        <ComboBoxItem Content="最近30天"/>
                    </ComboBox>
                    <Button Name="btnRefresh" Content="刷新" Classes="accent"/>
                    <Button Name="btnExport" Content="导出"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 图表区域 -->
        <Border Grid.Row="1" Background="{DynamicResource SystemControlBackgroundAltHighBrush}" 
                BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="1" Margin="5">
            <Grid>
                <!-- LiveCharts图表 -->
                <lvc:CartesianChart Name="runtimeChart" 
                                   Series="{Binding Series}"
                                   XAxes="{Binding XAxes}"
                                   YAxes="{Binding YAxes}"
                                   ZoomMode="X"
                                   TooltipPosition="Top"/>
                
                <!-- 无数据提示 -->
                <StackPanel Name="noDataPanel" 
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           IsVisible="{Binding HasNoData}">
                    <TextBlock Text="📊" FontSize="48" 
                              HorizontalAlignment="Center" 
                              Opacity="0.3"/>
                    <TextBlock Text="暂无运行时间数据" 
                              HorizontalAlignment="Center" 
                              Opacity="0.5" 
                              Margin="0,10,0,0"/>
                    <TextBlock Text="请等待数据加载或检查设备连接" 
                              HorizontalAlignment="Center" 
                              Opacity="0.3" 
                              FontSize="12"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 统计信息栏 -->
        <Border Grid.Row="2" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}" 
                Padding="10,5" BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}" 
                BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="20">
                    <TextBlock Name="txtDataPoints" Text="数据点: 0" VerticalAlignment="Center"/>
                    <TextBlock Name="txtTimeRange" Text="时间范围: --" VerticalAlignment="Center"/>
                    <TextBlock Name="txtAvgRuntime" Text="平均运行时间: --" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="15">
                    <TextBlock Name="txtMaxRuntime" Text="最大: --" VerticalAlignment="Center"/>
                    <TextBlock Name="txtMinRuntime" Text="最小: --" VerticalAlignment="Center"/>
                    <TextBlock Name="txtLastUpdate" Text="更新: --" VerticalAlignment="Center" 
                              FontSize="11" Opacity="0.7"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</UserControl>
