<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="tzedgeview.App"
             xmlns:local="using:tzedgeview"
             xmlns:converters="using:tzedgeview.Converters"
             RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>

    <!-- 暂时注释掉资源，先解决编译问题
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="/Styles/AppTheme.axaml"/>
            </ResourceDictionary.MergedDictionaries>

            <converters:AlarmLevelToColorConverter x:Key="AlarmLevelToColorConverter"/>
            <converters:LogLevelToColorConverter x:Key="LogLevelToColorConverter"/>
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
            <converters:StatusToColorConverter x:Key="StatusToColorConverter"/>
        </ResourceDictionary>
    </Application.Resources>
    -->

	<Application.Styles>
		<FluentTheme />

		<!-- 全局样式 -->
		<Style Selector="Window">
			<Setter Property="FontFamily" Value="../Assets/Fonts/msyh.ttf#Microsoft YaHei"/>
			<Setter Property="Background" Value="#F5F5F5"/>
			<Setter Property="Foreground" Value="#333333"/>
		</Style>

		<!-- 文本块样式 -->
		<Style Selector="TextBlock">
			<Setter Property="Foreground" Value="#333333"/>
		</Style>

		<!-- 按钮样式 -->
		<Style Selector="Button">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="Foreground" Value="#333333"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="3"/>
			<Setter Property="Padding" Value="12,6"/>
		</Style>

		<Style Selector="Button:pointerover">
			<Setter Property="Background" Value="#E9ECEF"/>
		</Style>

		<Style Selector="Button:pressed">
			<Setter Property="Background" Value="#DEE2E6"/>
		</Style>

		<!-- 强调按钮样式 -->
		<Style Selector="Button.accent">
			<Setter Property="Background" Value="#0078D4"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderBrush" Value="#106EBE"/>
		</Style>

		<Style Selector="Button.accent:pointerover">
			<Setter Property="Background" Value="#106EBE"/>
		</Style>

		<Style Selector="Button.accent:pressed">
			<Setter Property="Background" Value="#005A9E"/>
		</Style>

		<!-- 输入框样式 -->
		<Style Selector="TextBox">
			<Setter Property="Background" Value="White"/>
			<Setter Property="Foreground" Value="#333333"/>
			<Setter Property="BorderBrush" Value="#CCCCCC"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="3"/>
			<Setter Property="Padding" Value="8,6"/>
		</Style>

		<Style Selector="TextBox:focus">
			<Setter Property="BorderBrush" Value="#0078D4"/>
			<Setter Property="BorderThickness" Value="2"/>
		</Style>

		<!-- 组合框样式 -->
		<Style Selector="ComboBox">
			<Setter Property="Background" Value="White"/>
			<Setter Property="Foreground" Value="#333333"/>
			<Setter Property="BorderBrush" Value="#CCCCCC"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="3"/>
			<Setter Property="Padding" Value="8,6"/>
		</Style>

		<!-- 复选框样式 -->
		<Style Selector="CheckBox">
			<Setter Property="Foreground" Value="#333333"/>
		</Style>

		<!-- 边框样式 -->
		<Style Selector="Border.card">
			<Setter Property="Background" Value="White"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="6"/>
			<Setter Property="Margin" Value="5"/>
			<Setter Property="Padding" Value="15"/>
		</Style>

		<!-- 数据网格样式 -->
		<Style Selector="DataGrid">
			<Setter Property="Background" Value="White"/>
			<Setter Property="Foreground" Value="#333333"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="3"/>
		</Style>

		<Style Selector="DataGridColumnHeader">
			<Setter Property="Background" Value="#F8F8F8"/>
			<Setter Property="Foreground" Value="#333333"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
		</Style>

		<Style Selector="DataGridRow:nth-child(even)">
			<Setter Property="Background" Value="#FAFAFA"/>
		</Style>

		<Style Selector="DataGridRow:selected">
			<Setter Property="Background" Value="#E3F2FD"/>
		</Style>

		<!-- 标签控件样式 -->
		<Style Selector="TabControl">
			<Setter Property="Background" Value="Transparent"/>
		</Style>

		<Style Selector="TabItem">
			<Setter Property="Background" Value="#F0F0F0"/>
			<Setter Property="Foreground" Value="#333333"/>
			<Setter Property="BorderBrush" Value="#CCCCCC"/>
			<Setter Property="Padding" Value="12,8"/>
			<Setter Property="Margin" Value="2,0"/>
		</Style>

		<Style Selector="TabItem:selected">
			<Setter Property="Background" Value="White"/>
			<Setter Property="BorderBrush" Value="#0078D4"/>
			<Setter Property="BorderThickness" Value="0,0,0,2"/>
		</Style>

		<!-- 菜单样式 -->
		<Style Selector="Menu">
			<Setter Property="Background" Value="#F8F8F8"/>
			<Setter Property="Foreground" Value="#333333"/>
		</Style>

		<Style Selector="MenuItem">
			<Setter Property="Foreground" Value="#333333"/>
		</Style>

		<Style Selector="MenuItem:selected">
			<Setter Property="Background" Value="#E3F2FD"/>
		</Style>

		<!-- 状态栏样式 -->
		<Style Selector="Border.statusbar">
			<Setter Property="Background" Value="#F0F0F0"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
			<Setter Property="BorderThickness" Value="0,1,0,0"/>
		</Style>

		<!-- 工具栏样式 -->
		<Style Selector="Border.toolbar">
			<Setter Property="Background" Value="#F8F8F8"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
			<Setter Property="BorderThickness" Value="0,0,0,1"/>
		</Style>

		<!-- 侧边栏样式 -->
		<Style Selector="Border.sidebar">
			<Setter Property="Background" Value="#FAFAFA"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
		</Style>

		<!-- 列表框样式 -->
		<Style Selector="ListBox">
			<Setter Property="Background" Value="White"/>
			<Setter Property="Foreground" Value="#333333"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
			<Setter Property="BorderThickness" Value="1"/>
		</Style>

		<Style Selector="ListBoxItem">
			<Setter Property="Foreground" Value="#333333"/>
			<Setter Property="Padding" Value="8"/>
		</Style>

		<Style Selector="ListBoxItem:selected">
			<Setter Property="Background" Value="#E3F2FD"/>
			<Setter Property="Foreground" Value="#1976D2"/>
		</Style>

		<Style Selector="ListBoxItem:pointerover">
			<Setter Property="Background" Value="#F5F5F5"/>
		</Style>

	</Application.Styles>


</Application>