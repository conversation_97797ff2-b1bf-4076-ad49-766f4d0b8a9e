<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="tzedgeview.App"
             xmlns:local="using:tzedgeview"
             xmlns:semi="https://irihi.tech/semi"
             xmlns:converters="using:tzedgeview.Converters"
             RequestedThemeVariant="Light">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>

    <!-- 暂时注释掉资源，先解决编译问题
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="/Styles/AppTheme.axaml"/>
            </ResourceDictionary.MergedDictionaries>

            <converters:AlarmLevelToColorConverter x:Key="AlarmLevelToColorConverter"/>
            <converters:LogLevelToColorConverter x:Key="LogLevelToColorConverter"/>
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
            <converters:StatusToColorConverter x:Key="StatusToColorConverter"/>
        </ResourceDictionary>
    </Application.Resources>
    -->

	<Application.Styles>
		<semi:SemiTheme Locale="zh-CN" />
		<semi:DataGridSemiTheme />
		<semi:TreeDataGridSemiTheme />
        
	</Application.Styles>


</Application>