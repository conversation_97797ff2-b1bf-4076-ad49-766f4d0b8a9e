<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="tzedgeview.App"
             xmlns:local="using:tzedgeview"
             RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>

	<Application.Styles>
		<FluentTheme />
		<Style Selector="Window">
			<Setter Property="FontFamily" Value="../Assets/Fonts/msyh.ttf#Microsoft YaHei"/>
		</Style>
	</Application.Styles>


</Application>