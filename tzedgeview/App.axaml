<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="tzedgeview.App"
             xmlns:local="using:tzedgeview"
             xmlns:converters="using:tzedgeview.Converters"
             RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.DataTemplates>
        <local:ViewLocator/>
    </Application.DataTemplates>

    <!-- 暂时注释掉资源，先解决编译问题
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="/Styles/AppTheme.axaml"/>
            </ResourceDictionary.MergedDictionaries>

            <converters:AlarmLevelToColorConverter x:Key="AlarmLevelToColorConverter"/>
            <converters:LogLevelToColorConverter x:Key="LogLevelToColorConverter"/>
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
            <converters:StatusToColorConverter x:Key="StatusToColorConverter"/>
        </ResourceDictionary>
    </Application.Resources>
    -->

	<Application.Styles>
		<FluentTheme />

		<!-- 全局样式 -->
		<Style Selector="Window">
			<Setter Property="FontFamily" Value="../Assets/Fonts/msyh.ttf#Microsoft YaHei"/>
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#212529"/>
		</Style>

		<!-- 文本块样式 -->
		<Style Selector="TextBlock">
			<Setter Property="Foreground" Value="#212529"/>
		</Style>

		<!-- 标签文本样式 -->
		<Style Selector="Label">
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<!-- 按钮样式 -->
		<Style Selector="Button">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="BorderBrush" Value="#CED4DA"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Padding" Value="12,8"/>
			<Setter Property="FontWeight" Value="Normal"/>
		</Style>

		<Style Selector="Button:pointerover">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="BorderBrush" Value="#ADB5BD"/>
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<Style Selector="Button:pressed">
			<Setter Property="Background" Value="#E9ECEF"/>
			<Setter Property="BorderBrush" Value="#ADB5BD"/>
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<!-- 强调按钮样式 -->
		<Style Selector="Button.accent">
			<Setter Property="Background" Value="#0078D4"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderBrush" Value="#106EBE"/>
		</Style>

		<Style Selector="Button.accent:pointerover">
			<Setter Property="Background" Value="#106EBE"/>
		</Style>

		<Style Selector="Button.accent:pressed">
			<Setter Property="Background" Value="#005A9E"/>
		</Style>

		<!-- 输入框样式 -->
		<Style Selector="TextBox">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="BorderBrush" Value="#CED4DA"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Padding" Value="10,8"/>
		</Style>

		<Style Selector="TextBox:focus">
			<Setter Property="BorderBrush" Value="#0078D4"/>
			<Setter Property="BorderThickness" Value="2"/>
		</Style>

		<Style Selector="TextBox:pointerover">
			<Setter Property="BorderBrush" Value="#ADB5BD"/>
		</Style>

		<!-- 组合框样式 -->
		<Style Selector="ComboBox">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="BorderBrush" Value="#CED4DA"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="4"/>
			<Setter Property="Padding" Value="10,8"/>
		</Style>

		<Style Selector="ComboBox:pointerover">
			<Setter Property="BorderBrush" Value="#ADB5BD"/>
		</Style>

		<Style Selector="ComboBox:focus">
			<Setter Property="BorderBrush" Value="#0078D4"/>
			<Setter Property="BorderThickness" Value="2"/>
		</Style>

		<!-- 复选框样式 -->
		<Style Selector="CheckBox">
			<Setter Property="Foreground" Value="#333333"/>
		</Style>

		<!-- 边框样式 -->
		<Style Selector="Border.card">
			<Setter Property="Background" Value="White"/>
			<Setter Property="BorderBrush" Value="#E1E1E1"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="6"/>
			<Setter Property="Margin" Value="5"/>
			<Setter Property="Padding" Value="15"/>
		</Style>

		<!-- 数据网格样式 -->
		<Style Selector="DataGrid">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="4"/>
		</Style>

		<Style Selector="DataGridColumnHeader">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="Padding" Value="12,8"/>
		</Style>

		<Style Selector="DataGridRow">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<Style Selector="DataGridRow:nth-child(even)">
			<Setter Property="Background" Value="#F8F9FA"/>
		</Style>

		<Style Selector="DataGridRow:selected">
			<Setter Property="Background" Value="#E3F2FD"/>
			<Setter Property="Foreground" Value="#1976D2"/>
		</Style>

		<Style Selector="DataGridRow:pointerover">
			<Setter Property="Background" Value="#F0F0F0"/>
		</Style>

		<Style Selector="DataGridCell">
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="Padding" Value="8,6"/>
		</Style>

		<!-- 标签控件样式 -->
		<Style Selector="TabControl">
			<Setter Property="Background" Value="Transparent"/>
		</Style>

		<!-- Tab条样式 -->
		<Style Selector="TabStrip">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="0,0,0,1"/>
		</Style>

		<Style Selector="TabItem">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="1,1,1,1"/>
			<Setter Property="Padding" Value="16,10"/>
			<Setter Property="Margin" Value="0,0,2,0"/>
			<Setter Property="FontWeight" Value="Normal"/>
			<Setter Property="FontSize" Value="13"/>
			<Setter Property="CornerRadius" Value="4,4,0,0"/>
		</Style>

		<Style Selector="TabItem:pointerover">
			<Setter Property="Background" Value="#E9ECEF"/>
			<Setter Property="Foreground" Value="#212529"/>
			<Setter Property="BorderBrush" Value="#ADB5BD"/>
		</Style>

		<Style Selector="TabItem:selected">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#0078D4"/>
			<Setter Property="BorderBrush" Value="#0078D4"/>
			<Setter Property="BorderThickness" Value="1,1,1,0"/>
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="ZIndex" Value="1"/>
			<Setter Property="Margin" Value="0,0,2,-1"/>
		</Style>

		<Style Selector="TabItem:selected:pointerover">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="Foreground" Value="#0078D4"/>
		</Style>

		<Style Selector="TabItem:disabled">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="Foreground" Value="#ADB5BD"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
		</Style>

		<!-- Tab内容区域样式 -->
		<Style Selector="TabControl ContentPresenter">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Padding" Value="16"/>
		</Style>

		<!-- Tab标题文本样式 -->
		<Style Selector="TabItem TextBlock">
			<Setter Property="FontWeight" Value="Normal"/>
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<Style Selector="TabItem:selected TextBlock">
			<Setter Property="FontWeight" Value="SemiBold"/>
			<Setter Property="Foreground" Value="#0078D4"/>
		</Style>

		<Style Selector="TabItem:pointerover TextBlock">
			<Setter Property="Foreground" Value="#212529"/>
		</Style>

		<Style Selector="TabItem:selected:pointerover TextBlock">
			<Setter Property="Foreground" Value="#0078D4"/>
		</Style>

		<!-- 菜单样式 -->
		<Style Selector="Menu">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="0,0,0,1"/>
		</Style>

		<!-- 菜单项样式 -->
		<Style Selector="MenuItem">
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="Background" Value="Transparent"/>
			<Setter Property="Padding" Value="12,8"/>
			<Setter Property="FontSize" Value="13"/>
		</Style>

		<!-- 顶级菜单项悬停 -->
		<Style Selector="MenuItem:pointerover">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<!-- 顶级菜单项选中/激活 -->
		<Style Selector="MenuItem:selected">
			<Setter Property="Background" Value="#E3F2FD"/>
			<Setter Property="Foreground" Value="#0078D4"/>
		</Style>

		<!-- 顶级菜单项按下 -->
		<Style Selector="MenuItem:pressed">
			<Setter Property="Background" Value="#BBDEFB"/>
			<Setter Property="Foreground" Value="#0078D4"/>
		</Style>

		<!-- 菜单弹出面板 - 移除不支持的属性 -->

		<!-- 子菜单项样式 -->
		<Style Selector="MenuItem MenuItem">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="Padding" Value="12,8"/>
			<Setter Property="Margin" Value="2"/>
			<Setter Property="CornerRadius" Value="3"/>
		</Style>

		<!-- 子菜单项悬停 -->
		<Style Selector="MenuItem MenuItem:pointerover">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<!-- 子菜单项选中 -->
		<Style Selector="MenuItem MenuItem:selected">
			<Setter Property="Background" Value="#E3F2FD"/>
			<Setter Property="Foreground" Value="#0078D4"/>
		</Style>

		<!-- 子菜单项按下 -->
		<Style Selector="MenuItem MenuItem:pressed">
			<Setter Property="Background" Value="#BBDEFB"/>
			<Setter Property="Foreground" Value="#0078D4"/>
		</Style>

		<!-- 菜单分隔符 -->
		<Style Selector="Separator">
			<Setter Property="Background" Value="#DEE2E6"/>
			<Setter Property="Height" Value="1"/>
			<Setter Property="Margin" Value="8,4"/>
		</Style>

		<!-- 菜单图标样式 -->
		<Style Selector="MenuItem Image">
			<Setter Property="Width" Value="16"/>
			<Setter Property="Height" Value="16"/>
			<Setter Property="Margin" Value="0,0,8,0"/>
		</Style>

		<!-- 禁用的菜单项 -->
		<Style Selector="MenuItem:disabled">
			<Setter Property="Foreground" Value="#ADB5BD"/>
			<Setter Property="Background" Value="Transparent"/>
		</Style>

		<!-- 菜单快捷键文本 -->
		<Style Selector="MenuItem AccessText">
			<Setter Property="Foreground" Value="#6C757D"/>
			<Setter Property="FontSize" Value="12"/>
		</Style>

		<!-- 弹出菜单容器样式 -->
		<Style Selector="MenuFlyoutPresenter">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="6"/>
			<Setter Property="Padding" Value="4"/>
			<Setter Property="MinWidth" Value="180"/>
		</Style>

		<!-- 上下文菜单样式 -->
		<Style Selector="ContextMenu">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="6"/>
			<Setter Property="Padding" Value="4"/>
		</Style>

		<!-- 弹出框样式 - 移除不支持的属性 -->

		<!-- 菜单弹出框边框 -->
		<Style Selector="Border.MenuPopup">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="6"/>
			<Setter Property="Padding" Value="4"/>
		</Style>

		<!-- 状态栏样式 -->
		<Style Selector="Border.statusbar">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="0,1,0,0"/>
		</Style>

		<Style Selector="Border.statusbar TextBlock">
			<Setter Property="Foreground" Value="#6C757D"/>
			<Setter Property="FontSize" Value="12"/>
		</Style>

		<!-- 工具栏样式 -->
		<Style Selector="Border.toolbar">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="0,0,0,1"/>
		</Style>

		<Style Selector="Border.toolbar TextBlock">
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<!-- 侧边栏样式 -->
		<Style Selector="Border.sidebar">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="0,0,1,0"/>
		</Style>

		<Style Selector="Border.sidebar TextBlock">
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<!-- 列表框样式 -->
		<Style Selector="ListBox">
			<Setter Property="Background" Value="#FFFFFF"/>
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="BorderBrush" Value="#DEE2E6"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="CornerRadius" Value="4"/>
		</Style>

		<Style Selector="ListBoxItem">
			<Setter Property="Foreground" Value="#495057"/>
			<Setter Property="Padding" Value="12,8"/>
			<Setter Property="Background" Value="Transparent"/>
		</Style>

		<Style Selector="ListBoxItem:selected">
			<Setter Property="Background" Value="#0078D4"/>
			<Setter Property="Foreground" Value="#FFFFFF"/>
		</Style>

		<Style Selector="ListBoxItem:pointerover">
			<Setter Property="Background" Value="#F8F9FA"/>
			<Setter Property="Foreground" Value="#495057"/>
		</Style>

		<Style Selector="ListBoxItem:selected:pointerover">
			<Setter Property="Background" Value="#106EBE"/>
			<Setter Property="Foreground" Value="#FFFFFF"/>
		</Style>

	</Application.Styles>


</Application>