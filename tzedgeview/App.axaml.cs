using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using ExCSS;
using HslCommunication.MQTT;
using HslCommunication;
using System;
using tzedgeview.Core;
using tzedgeview.ViewModels;
using tzedgeview.Views;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;

namespace tzedgeview
{
    public partial class App : Application
    {
        public override void Initialize()
        {
            AvaloniaXamlLoader.Load(this);

            // 初始化应用程序设置
            InitializeApplication();
        }

        /// <summary>
        /// 初始化应用程序
        /// </summary>
        private void InitializeApplication()
        {
            try
            {
                // 初始化配置
                string settingsPath = Util.GetFileFullPath("ViewerSettings.json");
                Util.ViewerSettings = new ViewerSettings(settingsPath);

                // 初始化主题管理器
                ThemeManager.Initialize();
                ThemeManager.SetTheme(Util.ViewerSettings.Theme);

                // 初始化图标资源
                Util.GroupNodeImages = new GroupNodeImages();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing application: {ex.Message}");
            }
        }

        public override void OnFrameworkInitializationCompleted()
        {
            if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                frmedgeservers frmserver = new frmedgeservers();
                frmedgeserversviewmodel frmservermodel = new frmedgeserversviewmodel(frmserver);
                frmserver.Width = double.NaN;

                MainWindowViewModel.Instance = new MainWindowViewModel(frmserver);
                MainWindow main = new MainWindow
                {
                    DataContext = MainWindowViewModel.Instance,
                };
                MainWindowViewModel.Instance.Main = main;
                MainWindowViewModel.Instance.frmedgeserverVM = frmservermodel;
                main.Title = "��Ե���ؿͻ���";
                FormDeviceMonitor frmDMonitor = new FormDeviceMonitor();
                FormDeviceMonitorViewModel frmDM = new FormDeviceMonitorViewModel(frmDMonitor);
                frmDM.UpdateEdgeStatus = MainWindowViewModel.Instance.UpdateEdgeStatus;
                frmDMonitor.Width = double.NaN;
                frmservermodel.FormDeviceMonitor = frmDMonitor;
                frmservermodel.SetMainWinEdgeServer();
                               
                main.pnlserver.Children.Add(frmserver);
                main.pnlmonitor.Children.Add(frmDMonitor);
                desktop.MainWindow = main;

                main.btnstop.Click += async (s, e) =>
                {
                    if (main.cboview.SelectedItem is EdgeServerSettings serverSettings)
                    {
                        if (serverSettings.EdgeAllDeivcePause)
                        {
                            MqttSyncClient rpc = serverSettings.GetMqttSyncClient();
                            OperateResult<string> read = await rpc.ReadRpcAsync<string>("Edge/DeviceContinueRequest", null);
                            if (!read.IsSuccess)
                            {
                                var box = MessageBoxManager
                                    .GetMessageBoxStandard("��ʾ", "���������������ʧ�ܣ�ԭ��" + read.Message,
                                    ButtonEnum.Ok, Icon.Warning);
                                    var result = await box.ShowAsync();
                                return;
                            }
                            else
                            {
                                string uri = "/Assets/images/StatusAnnotations_Pause_16xLG_color.png";
                                main.imgstop.Source = new Bitmap(AssetLoader.Open(new Uri("avares://tzedgeview" + uri)));
                                serverSettings.EdgeAllDeivcePause = false;
                                var box = MessageBoxManager
                                    .GetMessageBoxStandard("��ʾ", "֪ͨ���вɼ������ɹ���",
                                    ButtonEnum.Ok, Icon.Warning);
                                var result = await box.ShowAsync();
                            }
                        }
                        else
                        {
                            MqttSyncClient rpc = serverSettings.GetMqttSyncClient();
                            OperateResult<string> read = await rpc.ReadRpcAsync<string>("Edge/DeviceStopRequest", null);
                            if (!read.IsSuccess)
                            {
                                serverSettings.EdgeAllDeivcePause = false;
                                var box = MessageBoxManager
                                    .GetMessageBoxStandard("��ʾ", "���������������ʧ�ܣ�ԭ��" + read.Message,
                                    ButtonEnum.Ok, Icon.Warning);
                                var result = await box.ShowAsync();
                                return;
                            }
                            else
                            {
                                string uri = "/Assets/images/Running_16xLG.png";
                                main.imgstop.Source = new Bitmap(AssetLoader.Open(new Uri("avares://tzedgeview" + uri)));
                                serverSettings.EdgeAllDeivcePause = true;
                                var box = MessageBoxManager
                                    .GetMessageBoxStandard("��ʾ", "֪ͨ���вɼ���ͣ�ɹ���",
                                    ButtonEnum.Ok, Icon.Warning);
                                var result = await box.ShowAsync();
                            }
                        }
                    }
                };
                main.btnrestart.Click += (s, e) =>
                {
                    if (main.cboview.SelectedItem is EdgeServerSettings serverSettings)
                    {
                        EdgeServerHelper.RestartEdgeServer(serverSettings);
                    }
                };
            }

            base.OnFrameworkInitializationCompleted();
        }
        public override void RegisterServices()
        {
            base.RegisterServices();
        }
    }
}