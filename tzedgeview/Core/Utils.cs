using HslCommunication.BasicFramework;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Request;
using HslCommunication;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge;

namespace tzedgeview.Core
{
    public static class Utils
    {
        private static bool IsNodeSameGroupNodeExist(AvTreeNode node, string name)
        {
            bool result = false;
            foreach (AvTreeNode item in node.ChildNodes)
            {
                if (item.Tag != null)
                {
                    if (item.Tag.GetType() == typeof(GroupNode))
                    {
                        if (item.Text == name)
                        {
                            result = true;
                            break;
                        }
                    }
                }
            }

            return result;
        }

        public static string GetUniqueGroupName(AvTreeNode node, string name)
        {
            if (!IsNodeSameGroupNodeExist(node, name)) return name;

            int index = 1;
            while (IsNodeSameGroupNodeExist(node, name + index))
            {
                index++;
            }
            return name + index;
        }

        private static AvTreeNode CreateGroupNodeFromXml<T>(XElement element, Action markChaneg, NodeDisplayMode displayMode) where T : GroupNode, new()
        {
            if (element.Attribute(nameof(GroupNode.Name)) == null) return null;

            T nodeClass = new T();
            nodeClass.LoadByXmlElement(element);
            return CreateTreeNodeFromXml(serverSettings: null, nodeClass, markChaneg, displayMode);
        }

        private static AvTreeNode CreateTreeNodeFromXml<T>(AvTreeNode parent, XElement element, Action markChaneg, NodeDisplayMode displayMode) where T : GroupNode, new()
        {
            AvTreeNode treeNode = CreateGroupNodeFromXml<T>(element, markChaneg, displayMode);
            treeNode.Parent = parent;
            if (treeNode != null) parent.ChildNodes.Add(treeNode);
            return treeNode;
        }

        private static AvTreeNode CreateTreeNodeFromXml(EdgeServerSettings serverSettings, AvTreeNode parent, GroupNode group, Action markChaneg, NodeDisplayMode displayMode)
        {
            AvTreeNode treeNode = CreateTreeNodeFromXml(serverSettings, group, markChaneg, displayMode);
            treeNode.Parent = parent;
            if (treeNode != null) parent.ChildNodes.Add(treeNode);
            return treeNode;
        }

        public static AvTreeNode CreateTreeNodeFromXml(EdgeServerSettings serverSettings, GroupNode group, Action markChange, NodeDisplayMode displayMode)
        {
            AvTreeNode node = new AvTreeNode(group.GetDisplayName(displayMode), group, null);
            node.Icon = GetImageKeyByGroupNode(serverSettings, group);
            node.Tag = group;

            group.OnNameChanged += (m, n) =>
            {
                GroupNode gn = m as GroupNode;
                gn.Name = GetUniqueGroupName(node, gn.Name);
                node.Text = gn.GetDisplayName();
                markChange?.Invoke();
            };
            if (group.NodeType == NodeType.RequestNode)
            {
                if (group is RequestBase request)
                {
                    request.OnEnableChanged += (m, n) =>
                    {
                        GroupNode gn = m as GroupNode;
                        node.Icon = Utils.GetImageKeyByGroupNode(serverSettings, gn);
                        markChange?.Invoke();
                    };
                }
            }
            return node;
        }

        /// <summary>
		/// 主要用于网关主界面呈现网关设备列表，显示的是别名信息
		/// </summary>
		/// <param name="serverSettings">网关服务器的连接配置对象</param>
		/// <param name="treeNode">当前的树形节点信息</param>
		/// <param name="element">当前显示的元素信息</param>
		public static void RenderEdgeServerTreeNodeBrowseNode(EdgeServerSettings serverSettings, AvTreeNode treeNode, XElement element)
        {
            foreach (XElement item in element.Elements())
            {
                if (item.Name == NodeType.GroupNode.ToString())
                {
                    AvTreeNode node = CreateTreeNodeFromXml<GroupNode>(treeNode, item, null, NodeDisplayMode.ShowDisplayName);
                    RenderEdgeServerTreeNodeBrowseNode(serverSettings, node, item);
                }
                else if (item.Name == NodeType.GroupSerialPipe.ToString())
                {
                    NodeSerialPipe nodeSerialPipe = new NodeSerialPipe(item);
                    if (nodeSerialPipe.UseAsGroupNode)
                    {
                        AvTreeNode node = CreateTreeNodeFromXml<NodeSerialPipe>(treeNode, item, null, NodeDisplayMode.ShowDisplayName);
                        RenderEdgeServerTreeNodeBrowseNode(serverSettings, node, item);
                    }
                    else
                    {
                        RenderEdgeServerTreeNodeBrowseNode(serverSettings, treeNode, item);
                    }
                }
                else if (item.Name == NodeType.GroupSocketPipe.ToString())
                {
                    NodeSocketPipe nodeSocketPipe = new NodeSocketPipe(item);
                    if (nodeSocketPipe.UseAsGroupNode)
                    {
                        AvTreeNode node = CreateTreeNodeFromXml<NodeSocketPipe>(treeNode, item, null, NodeDisplayMode.ShowDisplayName);
                        RenderEdgeServerTreeNodeBrowseNode(serverSettings, node, item);
                    }
                    else
                    {
                        RenderEdgeServerTreeNodeBrowseNode(serverSettings, treeNode, item);
                    }
                }
                else if (item.Name == NodeType.GroupSingleThread.ToString())
                {
                    NodeSingleThread nodeSingleThread = new NodeSingleThread(item);
                    if (nodeSingleThread.UseAsGroupNode)
                    {
                        AvTreeNode node = CreateTreeNodeFromXml<NodeSingleThread>(treeNode, item, null, NodeDisplayMode.ShowDisplayName);
                        RenderEdgeServerTreeNodeBrowseNode(serverSettings, node, item);
                    }
                    else
                    {
                        RenderEdgeServerTreeNodeBrowseNode(serverSettings, treeNode, item);
                    }
                }
                else if (item.Name == NodeType.DeviceNode.ToString() ||
                         item.Name == NodeType.RobotNode.ToString() ||
                         item.Name == NodeType.CncNode.ToString())
                {
                    DeviceType type = SoftBasic.GetEnumFromString<DeviceType>(item.Attribute(nameof(DeviceType)).Value);
                    DeviceNode dn = new DeviceNode(item, type);
                    AvTreeNode deviceTreeNode = CreateTreeNodeFromXml(serverSettings, dn, null, NodeDisplayMode.ShowDisplayName);
                    deviceTreeNode.Parent = treeNode;
                    deviceTreeNode.Tag = dn;
                    treeNode.ChildNodes.Add(deviceTreeNode);
                }
            }
        }

        /// <summary>
		/// 判断设备的名称是否在设备的路径下
		/// </summary>
		/// <param name="deviceName">设备的名称信息</param>
		/// <param name="path">路径 信息</param>
		/// <returns>非真即假的结果对象</returns>
		public static bool IsDeviceNameInPath(string deviceName, string[] path)
        {
            string[] devices = deviceName.Split(new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
            if (devices.Length == path.Length)
            {
                for (int i = 0; i < devices.Length; i++)
                {
                    if (devices[i] != path[i])
                    {
                        return false;
                    }
                }
                return true;
            }
            return false;
        }

        public static string GetImageKeyByGroupNode(EdgeServerSettings serverSettings, GroupNode groupNode)
        {
            if (groupNode == null) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.GroupNode) return IconUrl.GroupNode;
            if (groupNode.NodeType == NodeType.GroupSerialPipe) return IconUrl.GroupSerialPipe;
            if (groupNode.NodeType == NodeType.GroupSocketPipe) return IconUrl.GroupSocketPipe;
            if (groupNode.NodeType == NodeType.GroupSingleThread) return IconUrl.GroupSocketPipe;
            if (groupNode.NodeType == NodeType.RegularStructItemNode) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.RegularStructNode) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.RegularScalarNode) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.RegularWriteMapping) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.RequestNode)
            {
                if (groupNode is ScalarReadRequest scalarReadRequest)
                    return scalarReadRequest.Enable ? IconUrl.NodeRequestNode_en : IconUrl.NodeRequestNode_dis;
                if (groupNode is SourceReadRequest sourceReadRequest)
                    return sourceReadRequest.Enable ? IconUrl.NodeRequestNode_en : IconUrl.NodeRequestNode_dis;
                if (groupNode is CallMethodRequest scallMethodRequest ||
                    groupNode is ScalarWriteRequest writeRequest)
                    return IconUrl.NodeRequestNode_en;
                if (groupNode is DatabaseRequest databaseRequest)
                    return IconUrl.NodeRequestNode_en;
            }
            if (groupNode.NodeType == NodeType.DeviceNode ||
                groupNode.NodeType == NodeType.CncNode ||
                groupNode.NodeType == NodeType.RobotNode)
            {
                if (groupNode is DeviceNode device)
                {
                    if (device.DeviceType == DeviceType.Plugins || !string.IsNullOrEmpty(device.PluginsType))
                        return serverSettings != null ?
                            IconUrl.NodeRequestNode_en : IconUrl.NodeRequestNode_en;
                    else
                        return IconUrl.NodeRequestNode_en;
                }
            }
            if (groupNode.NodeType == NodeType.AlarmNode) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.AlarmDefinitionNode) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.AlarmDatabase) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.OeeNode) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.DataOeeDefinitionNode) return IconUrl.GroupAll;
            if (groupNode.NodeType == NodeType.DatabaseNode) return IconUrl.GroupAll;
            return IconUrl.GroupAll;
        }

        public static bool IsServerLocalNode(this AvTreeNode treeNode)
        {          
            return treeNode == null || treeNode.Parent == null;
        }

        private static char[] deviceSplitChars = new char[] { '\\', '/' };
        public readonly static string DeviceDefaultSplit = "/";

        public static string GetDevicePathFromDeviceID(string deviceID)
        {
            string[] splits = deviceID.Split(deviceSplitChars, StringSplitOptions.RemoveEmptyEntries);
            if (splits == null || splits.Length < 1) return DeviceDefaultSplit;
            if (splits.Length == 1) return splits[0];
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < splits.Length - 1; i++)
            {
                sb.Append(splits[i]);
                if (i < splits.Length - 2)
                    sb.Append(HslTechnologyExtension.DeviceDefaultSplit);
            }
            return sb.ToString();
        }
    }
}
