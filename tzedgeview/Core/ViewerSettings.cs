using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace tzedgeview.Core
{
    /// <summary>
    /// 客户端的配置类信息
    /// </summary>
    public class ViewerSettings
    {
        #region Constructor

        /// <summary>
        /// 指定文件的存储路径来实例化一个对象
        /// </summary>
        /// <param name="path">路径信息</param>
        public ViewerSettings(string path)
        {
            this.filePath = path;
            LoadFile();
        }

        #endregion

        #region Properties

        /// <summary>
        /// 获取或设置当前的主题信息
        /// </summary>
        public string Theme { get; set; } = "Light";

        /// <summary>
        /// 获取或设置窗口宽度
        /// </summary>
        public double WindowWidth { get; set; } = 1200;

        /// <summary>
        /// 获取或设置窗口高度
        /// </summary>
        public double WindowHeight { get; set; } = 800;

        /// <summary>
        /// 获取或设置窗口是否最大化
        /// </summary>
        public bool IsMaximized { get; set; } = false;

        /// <summary>
        /// 获取或设置窗口X位置
        /// </summary>
        public double WindowX { get; set; } = 100;

        /// <summary>
        /// 获取或设置窗口Y位置
        /// </summary>
        public double WindowY { get; set; } = 100;

        /// <summary>
        /// 获取或设置是否自动连接上次的服务器
        /// </summary>
        public bool AutoConnectLastServer { get; set; } = true;

        /// <summary>
        /// 获取或设置上次连接的服务器ID
        /// </summary>
        public string LastConnectedServerId { get; set; } = string.Empty;

        /// <summary>
        /// 获取或设置数据刷新间隔（毫秒）
        /// </summary>
        public int DataRefreshInterval { get; set; } = 1000;

        /// <summary>
        /// 获取或设置是否显示调试信息
        /// </summary>
        public bool ShowDebugInfo { get; set; } = false;

        /// <summary>
        /// 获取或设置语言设置
        /// </summary>
        public string Language { get; set; } = "zh-CN";

        /// <summary>
        /// 获取或设置是否启用声音提醒
        /// </summary>
        public bool EnableSoundAlert { get; set; } = true;

        /// <summary>
        /// 获取或设置日志级别
        /// </summary>
        public string LogLevel { get; set; } = "Info";

        #endregion

        #region Methods

        /// <summary>
        /// 从文件加载配置
        /// </summary>
        public void LoadFile()
        {
            if (!string.IsNullOrEmpty(filePath))
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        string jsonContent = File.ReadAllText(filePath, Encoding.UTF8);
                        JObject json = JObject.Parse(jsonContent);

                        Theme = GetValueFromJson(json, nameof(Theme), Theme);
                        WindowWidth = GetValueFromJson(json, nameof(WindowWidth), WindowWidth);
                        WindowHeight = GetValueFromJson(json, nameof(WindowHeight), WindowHeight);
                        IsMaximized = GetValueFromJson(json, nameof(IsMaximized), IsMaximized);
                        WindowX = GetValueFromJson(json, nameof(WindowX), WindowX);
                        WindowY = GetValueFromJson(json, nameof(WindowY), WindowY);
                        AutoConnectLastServer = GetValueFromJson(json, nameof(AutoConnectLastServer), AutoConnectLastServer);
                        LastConnectedServerId = GetValueFromJson(json, nameof(LastConnectedServerId), LastConnectedServerId);
                        DataRefreshInterval = GetValueFromJson(json, nameof(DataRefreshInterval), DataRefreshInterval);
                        ShowDebugInfo = GetValueFromJson(json, nameof(ShowDebugInfo), ShowDebugInfo);
                        Language = GetValueFromJson(json, nameof(Language), Language);
                        EnableSoundAlert = GetValueFromJson(json, nameof(EnableSoundAlert), EnableSoundAlert);
                        LogLevel = GetValueFromJson(json, nameof(LogLevel), LogLevel);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading settings: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        public void SaveFile()
        {
            if (!string.IsNullOrEmpty(filePath))
            {
                try
                {
                    JObject json = new JObject();
                    json.Add(nameof(Theme), Theme);
                    json.Add(nameof(WindowWidth), WindowWidth);
                    json.Add(nameof(WindowHeight), WindowHeight);
                    json.Add(nameof(IsMaximized), IsMaximized);
                    json.Add(nameof(WindowX), WindowX);
                    json.Add(nameof(WindowY), WindowY);
                    json.Add(nameof(AutoConnectLastServer), AutoConnectLastServer);
                    json.Add(nameof(LastConnectedServerId), LastConnectedServerId);
                    json.Add(nameof(DataRefreshInterval), DataRefreshInterval);
                    json.Add(nameof(ShowDebugInfo), ShowDebugInfo);
                    json.Add(nameof(Language), Language);
                    json.Add(nameof(EnableSoundAlert), EnableSoundAlert);
                    json.Add(nameof(LogLevel), LogLevel);

                    // 确保目录存在
                    string directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    File.WriteAllText(filePath, json.ToString(), Encoding.UTF8);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error saving settings: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 从JSON对象获取值
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="json">JSON对象</param>
        /// <param name="key">键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>获取的值</returns>
        private T GetValueFromJson<T>(JObject json, string key, T defaultValue)
        {
            try
            {
                if (json.ContainsKey(key))
                {
                    return json[key].ToObject<T>();
                }
            }
            catch
            {
                // 忽略转换错误，返回默认值
            }
            return defaultValue;
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefault()
        {
            Theme = "Light";
            WindowWidth = 1200;
            WindowHeight = 800;
            IsMaximized = false;
            WindowX = 100;
            WindowY = 100;
            AutoConnectLastServer = true;
            LastConnectedServerId = string.Empty;
            DataRefreshInterval = 1000;
            ShowDebugInfo = false;
            Language = "zh-CN";
            EnableSoundAlert = true;
            LogLevel = "Info";
        }

        #endregion

        #region Private Fields

        private string filePath = string.Empty;

        #endregion
    }
}
