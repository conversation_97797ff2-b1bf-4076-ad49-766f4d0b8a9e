using System;
using System.Collections.Generic;
using System.IO;
using Avalonia.Media.Imaging;
using Avalonia.Platform;

namespace tzedgeview.Core
{
    /// <summary>
    /// 节点图标管理类
    /// </summary>
    public class GroupNodeImages
    {
        #region Constructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public GroupNodeImages()
        {
            InitializeImages();
        }

        #endregion

        #region Properties

        /// <summary>
        /// 设备图标列表
        /// </summary>
        public Dictionary<string, Bitmap> DeviceImages { get; private set; }

        /// <summary>
        /// 状态图标列表
        /// </summary>
        public Dictionary<string, Bitmap> StatusImages { get; private set; }

        /// <summary>
        /// 文件夹图标
        /// </summary>
        public Bitmap FolderIcon { get; private set; }

        /// <summary>
        /// 设备图标
        /// </summary>
        public Bitmap DeviceIcon { get; private set; }

        /// <summary>
        /// 连接成功图标
        /// </summary>
        public Bitmap ConnectedIcon { get; private set; }

        /// <summary>
        /// 连接失败图标
        /// </summary>
        public Bitmap DisconnectedIcon { get; private set; }

        /// <summary>
        /// 警告图标
        /// </summary>
        public Bitmap WarningIcon { get; private set; }

        /// <summary>
        /// 错误图标
        /// </summary>
        public Bitmap ErrorIcon { get; private set; }

        /// <summary>
        /// 信息图标
        /// </summary>
        public Bitmap InfoIcon { get; private set; }

        #endregion

        #region Methods

        /// <summary>
        /// 初始化图标
        /// </summary>
        private void InitializeImages()
        {
            DeviceImages = new Dictionary<string, Bitmap>();
            StatusImages = new Dictionary<string, Bitmap>();

            try
            {
                // 加载基本图标
                LoadBasicIcons();

                // 加载设备图标
                LoadDeviceIcons();

                // 加载状态图标
                LoadStatusIcons();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing images: {ex.Message}");
                CreateDefaultImages();
            }
        }

        /// <summary>
        /// 加载基本图标
        /// </summary>
        private void LoadBasicIcons()
        {
            FolderIcon = LoadImageFromResource("Assets/Icons/folder.png");
            DeviceIcon = LoadImageFromResource("Assets/Icons/device.png");
            ConnectedIcon = LoadImageFromResource("Assets/Icons/connected.png");
            DisconnectedIcon = LoadImageFromResource("Assets/Icons/disconnected.png");
            WarningIcon = LoadImageFromResource("Assets/Icons/warning.png");
            ErrorIcon = LoadImageFromResource("Assets/Icons/error.png");
            InfoIcon = LoadImageFromResource("Assets/Icons/info.png");
        }

        /// <summary>
        /// 加载设备图标
        /// </summary>
        private void LoadDeviceIcons()
        {
            var deviceTypes = new[]
            {
                "PLC", "Modbus", "OpcUa", "Siemens", "Mitsubishi", "Omron",
                "Allen-Bradley", "Schneider", "Beckhoff", "Delta", "Panasonic",
                "Keyence", "Fuji", "Yokogawa", "Honeywell", "GE", "ABB"
            };

            foreach (var deviceType in deviceTypes)
            {
                var icon = LoadImageFromResource($"Assets/Icons/Devices/{deviceType}.png");
                if (icon != null)
                {
                    DeviceImages[deviceType] = icon;
                }
                else
                {
                    DeviceImages[deviceType] = DeviceIcon ?? CreateDefaultBitmap();
                }
            }
        }

        /// <summary>
        /// 加载状态图标
        /// </summary>
        private void LoadStatusIcons()
        {
            var statusTypes = new[]
            {
                "Online", "Offline", "Error", "Warning", "Unknown", "Connecting"
            };

            foreach (var statusType in statusTypes)
            {
                var icon = LoadImageFromResource($"Assets/Icons/Status/{statusType}.png");
                if (icon != null)
                {
                    StatusImages[statusType] = icon;
                }
                else
                {
                    StatusImages[statusType] = CreateStatusIcon(statusType);
                }
            }
        }

        /// <summary>
        /// 从资源加载图像
        /// </summary>
        /// <param name="resourcePath">资源路径</param>
        /// <returns>位图对象</returns>
        private Bitmap LoadImageFromResource(string resourcePath)
        {
            try
            {
                var uri = new Uri($"avares://tzedgeview/{resourcePath}");
                using var stream = AssetLoader.Open(uri);
                return new Bitmap(stream);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 创建默认图像
        /// </summary>
        private void CreateDefaultImages()
        {
            FolderIcon = CreateDefaultBitmap();
            DeviceIcon = CreateDefaultBitmap();
            ConnectedIcon = CreateDefaultBitmap();
            DisconnectedIcon = CreateDefaultBitmap();
            WarningIcon = CreateDefaultBitmap();
            ErrorIcon = CreateDefaultBitmap();
            InfoIcon = CreateDefaultBitmap();
        }

        /// <summary>
        /// 创建默认位图
        /// </summary>
        /// <returns>默认位图</returns>
        private Bitmap CreateDefaultBitmap()
        {
            try
            {
                // 创建一个16x16的默认图标
                var bitmap = new WriteableBitmap(new Avalonia.PixelSize(16, 16), new Avalonia.Vector(96, 96), Avalonia.Platform.PixelFormat.Bgra8888);
                return bitmap;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 创建状态图标
        /// </summary>
        /// <param name="statusType">状态类型</param>
        /// <returns>状态图标</returns>
        private Bitmap CreateStatusIcon(string statusType)
        {
            switch (statusType)
            {
                case "Online":
                    return ConnectedIcon ?? CreateDefaultBitmap();
                case "Offline":
                    return DisconnectedIcon ?? CreateDefaultBitmap();
                case "Error":
                    return ErrorIcon ?? CreateDefaultBitmap();
                case "Warning":
                    return WarningIcon ?? CreateDefaultBitmap();
                default:
                    return InfoIcon ?? CreateDefaultBitmap();
            }
        }

        /// <summary>
        /// 获取设备图标
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>设备图标</returns>
        public Bitmap GetDeviceIcon(string deviceType)
        {
            if (string.IsNullOrEmpty(deviceType))
                return DeviceIcon ?? CreateDefaultBitmap();

            if (DeviceImages.ContainsKey(deviceType))
                return DeviceImages[deviceType];

            return DeviceIcon ?? CreateDefaultBitmap();
        }

        /// <summary>
        /// 获取状态图标
        /// </summary>
        /// <param name="status">状态</param>
        /// <returns>状态图标</returns>
        public Bitmap GetStatusIcon(string status)
        {
            if (string.IsNullOrEmpty(status))
                return InfoIcon ?? CreateDefaultBitmap();

            if (StatusImages.ContainsKey(status))
                return StatusImages[status];

            return InfoIcon ?? CreateDefaultBitmap();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                FolderIcon?.Dispose();
                DeviceIcon?.Dispose();
                ConnectedIcon?.Dispose();
                DisconnectedIcon?.Dispose();
                WarningIcon?.Dispose();
                ErrorIcon?.Dispose();
                InfoIcon?.Dispose();

                foreach (var image in DeviceImages.Values)
                {
                    image?.Dispose();
                }
                DeviceImages.Clear();

                foreach (var image in StatusImages.Values)
                {
                    image?.Dispose();
                }
                StatusImages.Clear();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error disposing images: {ex.Message}");
            }
        }

        #endregion
    }
}
