using HslCommunication.MQTT;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;
using System.Threading.Tasks;
using System;
using HslCommunication;

namespace tzedgeview.Core
{
    /// <summary>
    /// 边缘服务器帮助类
    /// </summary>
    public class EdgeServerHelper
    {
        /// <summary>
        /// 重启边缘网关系统
        /// </summary>
        /// <param name="serverSettings">网关对象信息</param>
        /// <returns>空</returns>
        public static async Task RestartEdgeServer(EdgeServerSettings serverSettings)
        {
            if (serverSettings == null)
            {
                var box = MessageBoxManager
                               .GetMessageBoxStandard("警告", "当前的选择的网关对象为空，无法重新启动！",
                               ButtonEnum.Ok, Icon.Warning);
                await box.ShowAsync();
                return;
            }

            var confirmBox = MessageBoxManager
                               .GetMessageBoxStandard("重启确认", "是否确认真的重启网关？",
                               ButtonEnum.YesNo, Icon.Question);
            var confirmResult = await confirmBox.ShowAsync();

            if (confirmResult != ButtonResult.Yes)
                return;

            try
            {
                MqttSyncClient client = serverSettings.GetMqttSyncClient();
                var readStatus = await client.ReadStringAsync("Admin/ServerCloseAndRestart", null);

                if (!readStatus.IsSuccess)
                {
                    var errorBox = MessageBoxManager
                                   .GetMessageBoxStandard("错误", $"重启失败: {readStatus.Message}",
                                   ButtonEnum.Ok, Icon.Error);
                    await errorBox.ShowAsync();
                }
                else
                {
                    var successBox = MessageBoxManager
                                     .GetMessageBoxStandard("成功", "通知采集服务重启成功。",
                                     ButtonEnum.Ok, Icon.Success);
                    await successBox.ShowAsync();
                    serverSettings.MqttSyncClientConnectClose();
                }
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"重启操作异常: {ex.Message}",
                               ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        /// <summary>
        /// 关闭网关服务器的操作
        /// </summary>
        /// <param name="serverSettings">网关对象信息</param>
        /// <returns>空</returns>
        public static async Task ShutDownEdgeServer(EdgeServerSettings serverSettings)
        {
            if (serverSettings == null)
            {
                var box = MessageBoxManager
                               .GetMessageBoxStandard("警告", "当前的选择的网关对象为空，无法关闭操作！",
                               ButtonEnum.Ok, Icon.Warning);
                await box.ShowAsync();
                return;
            }

            var confirmBox = MessageBoxManager
                               .GetMessageBoxStandard("关闭确认", "是否确认关闭网关服务器？",
                               ButtonEnum.YesNo, Icon.Question);
            var confirmResult = await confirmBox.ShowAsync();

            if (confirmResult != ButtonResult.Yes)
                return;

            try
            {
                MqttSyncClient client = serverSettings.GetMqttSyncClient();
                var readStatus = await client.ReadStringAsync("Admin/ServerClose", null);

                if (!readStatus.IsSuccess)
                {
                    var errorBox = MessageBoxManager
                                   .GetMessageBoxStandard("错误", $"关闭失败: {readStatus.Message}",
                                   ButtonEnum.Ok, Icon.Error);
                    await errorBox.ShowAsync();
                }
                else
                {
                    var successBox = MessageBoxManager
                                     .GetMessageBoxStandard("成功", "通知采集服务关闭成功。",
                                     ButtonEnum.Ok, Icon.Success);
                    await successBox.ShowAsync();
                    serverSettings.MqttSyncClientConnectClose();
                }
            }
            catch (Exception ex)
            {
                var errorBox = MessageBoxManager
                               .GetMessageBoxStandard("错误", $"关闭操作异常: {ex.Message}",
                               ButtonEnum.Ok, Icon.Error);
                await errorBox.ShowAsync();
            }
        }

        /// <summary>
        /// 测试服务器连接
        /// </summary>
        /// <param name="serverSettings">服务器设置</param>
        /// <returns>连接测试结果</returns>
        public static async Task<OperateResult> TestConnection(EdgeServerSettings serverSettings)
        {
            if (serverSettings == null)
            {
                return new OperateResult("服务器设置为空");
            }

            try
            {
                MqttSyncClient client = serverSettings.GetMqttSyncClient();
                var readStatus = await client.ReadStringAsync("Edge/CommunicationTest", null);

                if (readStatus.IsSuccess)
                {
                    return OperateResult.CreateSuccessResult();
                }
                else
                {
                    return new OperateResult(readStatus.Message);
                }
            }
            catch (Exception ex)
            {
                return new OperateResult($"连接测试异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 暂停/恢复所有设备
        /// </summary>
        /// <param name="serverSettings">服务器设置</param>
        /// <param name="pause">是否暂停</param>
        /// <returns>操作结果</returns>
        public static async Task<OperateResult> PauseResumeAllDevices(EdgeServerSettings serverSettings, bool pause)
        {
            if (serverSettings == null)
            {
                return new OperateResult("服务器设置为空");
            }

            try
            {
                MqttSyncClient client = serverSettings.GetMqttSyncClient();
                string action = pause ? "Admin/PauseAllDevices" : "Admin/ResumeAllDevices";
                var readStatus = await client.ReadStringAsync(action, null);

                if (readStatus.IsSuccess)
                {
                    serverSettings.EdgeAllDeivcePause = pause;
                    return OperateResult.CreateSuccessResult();
                }
                else
                {
                    return new OperateResult(readStatus.Message);
                }
            }
            catch (Exception ex)
            {
                return new OperateResult($"操作异常: {ex.Message}");
            }
        }
    }
}
