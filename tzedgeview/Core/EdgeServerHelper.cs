using HslCommunication.MQTT;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;
using System.Threading.Tasks;
using MessageBox = MsBox.Avalonia.Dto;

namespace tzedgeview.Core
{
    public class EdgeServerHelper
    {
        /// <summary>
        /// 重启边缘网关系统
        /// </summary>
        /// <param name="serverSettings">网关对象信息</param>
        /// <returns>空</returns>
        public static void RestartEdgeServer(EdgeServerSettings serverSettings)
        {
            //FormRestartEdgeServer formRestartEdge = new Pages.FormRestartEdgeServer(serverSettings);
            //formRestartEdge.ShowDialog();
            //formRestartEdge.Dispose();
            //if (serverSettings == null)
            //{
            //    MessageBox.Show("当前的选择的网关对象为空，无法重新启动！");
            //    return;
            //}
            //if (MessageBox.Show("是否确认真的重启网关？", "重启确认", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) != DialogResult.Yes) return;

            //MqttSyncClient client = serverSettings.GetMqttSyncClient();
            //var readStatus = await client.ReadStringAsync("Admin/ServerCloseAndRestart", null);

            //if (!readStatus.IsSuccess)
            //{
            //    MessageBox.Show("Failed:" + readStatus.Message);
            //}
            //else
            //{
            //    MessageBox.Show("Ok:通知采集服务重启成功。");
            //    serverSettings.MqttSyncClientConnectClose();
            //}
        }

        /// <summary>
        /// 关闭网关服务器的操作
        /// </summary>
        /// <param name="serverSettings">网关对象信息</param>
        /// <returns>空</returns>
        public static async Task ShutDownEdgeServer(EdgeServerSettings serverSettings)
        {
            if (serverSettings == null)
            {
                var box = MessageBoxManager
                               .GetMessageBoxStandard("警告", "当前的选择的网关对象为空，无法关闭操作！",
                               ButtonEnum.Ok, Icon.Warning);
                var result = await box.ShowAsync();
                return;
            }

            MqttSyncClient client = serverSettings.GetMqttSyncClient();
            var readStatus = await client.ReadStringAsync("Admin/ServerClose", null);

            if (!readStatus.IsSuccess)
            {
                //MessageBox.Show("Failed:" + readStatus.Message);
            }
            else
            {
                var box = MessageBoxManager
                            .GetMessageBoxStandard("提示", "通知采集服务关闭成功",
                                ButtonEnum.Ok, Icon.Info);
                var result = await box.ShowAsync();
                serverSettings.MqttSyncClientConnectClose();
            }
        }
    }
}
