using System;
using System.Collections.Generic;
using System.Linq;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml.Styling;
using Avalonia.Styling;

namespace tzedgeview.Core
{
    /// <summary>
    /// 主题管理器
    /// </summary>
    public static class ThemeManager
    {
        #region Properties

        /// <summary>
        /// 当前主题
        /// </summary>
        public static string CurrentTheme { get; private set; } = "Light";

        /// <summary>
        /// 可用的主题列表
        /// </summary>
        public static List<string> AvailableThemes { get; } = new List<string>
        {
            "Light",
            "Dark"
        };

        #endregion

        #region Events

        /// <summary>
        /// 主题变更事件
        /// </summary>
        public static event EventHandler<string>? ThemeChanged;

        #endregion

        #region Methods

        /// <summary>
        /// 初始化主题管理器
        /// </summary>
        public static void Initialize()
        {
            // 设置默认主题
            SetTheme("Light");
        }

        /// <summary>
        /// 设置主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        public static void SetTheme(string themeName)
        {
            if (string.IsNullOrEmpty(themeName) || !AvailableThemes.Contains(themeName))
            {
                themeName = "Light";
            }

            try
            {
                var app = Application.Current;
                if (app != null)
                {
                    // 移除现有主题
                    RemoveExistingThemes(app);

                    // 应用新主题
                    ApplyTheme(app, themeName);

                    CurrentTheme = themeName;
                    ThemeChanged?.Invoke(null, themeName);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting theme: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换主题
        /// </summary>
        public static void ToggleTheme()
        {
            string newTheme = CurrentTheme == "Light" ? "Dark" : "Light";
            SetTheme(newTheme);
        }

        /// <summary>
        /// 获取主题资源
        /// </summary>
        /// <param name="resourceKey">资源键</param>
        /// <returns>资源对象</returns>
        public static object GetThemeResource(string resourceKey)
        {
            try
            {
                var app = Application.Current;
                if (app != null && app.TryGetResource(resourceKey, out var resource))
                {
                    return resource;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting theme resource: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 获取主题颜色
        /// </summary>
        /// <param name="colorKey">颜色键</param>
        /// <returns>颜色对象</returns>
        public static Avalonia.Media.IBrush GetThemeColor(string colorKey)
        {
            var resource = GetThemeResource(colorKey);
            return resource as Avalonia.Media.IBrush;
        }

        /// <summary>
        /// 应用主题到窗口
        /// </summary>
        /// <param name="window">窗口对象</param>
        public static void ApplyThemeToWindow(Window window)
        {
            if (window == null) return;

            try
            {
                // 根据当前主题设置窗口样式
                if (CurrentTheme == "Dark")
                {
                    window.Background = GetThemeColor("SystemControlBackgroundChromeMediumBrush");
                }
                else
                {
                    window.Background = GetThemeColor("SystemControlBackgroundAltHighBrush");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying theme to window: {ex.Message}");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 移除现有主题
        /// </summary>
        /// <param name="app">应用程序对象</param>
        private static void RemoveExistingThemes(Application app)
        {
            try
            {
                var stylesToRemove = app.Styles
                    .OfType<StyleInclude>()
                    .Where(s => s.Source != null && 
                               (s.Source.ToString().Contains("Themes/") || 
                                s.Source.ToString().Contains("Semi.Avalonia")))
                    .ToList();

                foreach (var style in stylesToRemove)
                {
                    app.Styles.Remove(style);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error removing existing themes: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用主题
        /// </summary>
        /// <param name="app">应用程序对象</param>
        /// <param name="themeName">主题名称</param>
        private static void ApplyTheme(Application app, string themeName)
        {
            try
            {
                // 应用Semi.Avalonia主题
                app.Styles.Add(new StyleInclude(new Uri("avares://Semi.Avalonia/Themes/Index.axaml"))
                {
                    Source = new Uri("avares://Semi.Avalonia/Themes/Index.axaml")
                });

                app.Styles.Add(new StyleInclude(new Uri("avares://Semi.Avalonia.DataGrid/Index.axaml"))
                {
                    Source = new Uri("avares://Semi.Avalonia.DataGrid/Index.axaml")
                });

                // 设置请求的主题变体
                if (themeName == "Dark")
                {
                    app.RequestedThemeVariant = ThemeVariant.Dark;
                }
                else
                {
                    app.RequestedThemeVariant = ThemeVariant.Light;
                }

                // 应用自定义样式
                ApplyCustomStyles(app, themeName);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying theme: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用自定义样式
        /// </summary>
        /// <param name="app">应用程序对象</param>
        /// <param name="themeName">主题名称</param>
        private static void ApplyCustomStyles(Application app, string themeName)
        {
            try
            {
                // 这里可以添加自定义样式
                // 例如字体、特殊控件样式等
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying custom styles: {ex.Message}");
            }
        }

        #endregion
    }
}
