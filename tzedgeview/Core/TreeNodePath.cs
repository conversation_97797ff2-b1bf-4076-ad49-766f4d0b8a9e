using HslCommunication;
using HslTechnology.Edge.Node;
using HslTechnology.Edge;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.ObjectModel;
using HslTechnology.Edge.Node.Device;
using ReactiveUI;

namespace tzedgeview.Core
{
    public class TreeNodePath
    {
        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public TreeNodePath()
        {
            this.DisplayPath = new string[0];
            this.ActualPath = new string[0];
        }

        /// <summary>
        /// 通过另一个对象来实例化一个拷贝的副本信息
        /// </summary>
        /// <param name="nodePath">节点路径类对象</param>
        public TreeNodePath(TreeNodePath nodePath)
        {
            this.DisplayPath = nodePath.DisplayPath.ToArray();
            this.ActualPath = nodePath.ActualPath.ToArray();
            this.isPath = nodePath.IsPath;
        }

        /// <summary>
        /// 指定显示路径，实际路径来实例化一个对象
        /// </summary>
        /// <param name="displayPath">显示路径信息</param>
        /// <param name="actualPath">实际的路径信息</param>
        public TreeNodePath(string[] displayPath, string[] actualPath)
        {
            this.DisplayPath = displayPath;
            this.ActualPath = actualPath;
        }

        /// <summary>
        /// 使用指定的树节点来实例化当前的节点路径对象
        /// </summary>
        /// <param name="treeNode">树节点信息</param>
        public TreeNodePath(AvTreeNode treeNode)
        {
            if (treeNode.Tag is GroupNode group)
            {
                if (group.NodeType == NodeType.GroupNode || group.NodeType == NodeType.GroupSerialPipe || group.NodeType == NodeType.GroupSocketPipe)
                    isPath = true;
            }
            List<string> listDisplay = new List<string>();
            List<string> listActual = new List<string>();
            while (true)
            {
                if (treeNode.Parent == null) break;

                listDisplay.Add(treeNode.Text);
                if (treeNode.Tag is GroupNode groupNode)
                    listActual.Add(groupNode.Name);
                else
                    listActual.Add(treeNode.Text);

                // if (node.Tag is ScalarDataNode scalarDataNode)
                //	  listDisplay.Add( scalarDataNode.Name );
                // else if (node.Tag is GroupNode groupNode)
                //	  listDisplay.Add( groupNode.Name );
                // else
                //	listDisplay.Add( node.Text );

                treeNode = treeNode.Parent;
                if (treeNode == null) break;
            }
            listDisplay.Reverse();
            listActual.Reverse();
            this.DisplayPath = listDisplay.ToArray();
            this.ActualPath = listActual.ToArray();
        }

        /// <summary>
        /// 用于显示的路径信息
        /// </summary>
        public string[] DisplayPath { get; set; }

        /// <summary>
        /// 真实的路径信息
        /// </summary>
        public string[] ActualPath { get; set; }

        /// <summary>
        /// 是否是路径的信息
        /// </summary>
        public bool IsPath { get => isPath; }

        /// <summary>
        /// 获取用于显示的完整的路径信息
        /// </summary>
        public string GetDisplayPath() => HslTechnologyExtension.ContactStringArrayPath(DisplayPath, isPath);

        /// <summary>
        /// 获取真实的完整的路径信息
        /// </summary>
        public string GetActualPath() => HslTechnologyExtension.ContactStringArrayPath(ActualPath, isPath);

        /// <summary>
        /// 判断两个的路径节点信息是否表示同一个
        /// </summary>
        /// <param name="nodePath">等待判断的路径信息</param>
        /// <returns>路径是否一致，这里不是判断是否同一个对象</returns>
        public bool Equals(TreeNodePath nodePath)
        {
            if (nodePath == null) nodePath = new TreeNodePath();
            if (this.ActualPath.Length != nodePath.ActualPath.Length) return false;
            for (int i = 0; i < this.ActualPath.Length; i++)
            {
                if (this.ActualPath[i] != nodePath.ActualPath[i]) return false;
            }
            return true;
        }

        /// <summary>
        /// 判断设备的名称是否在设备的路径下
        /// </summary>
        /// <param name="deviceName">设备的名称信息</param>
        /// <param name="path">路径 信息</param>
        /// <returns>非真即假的结果对象</returns>
        public bool IsDeviceNameInPath(string deviceName)
        {
            string[] devices = deviceName.Split(new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries).RemoveLast(1);
            if (devices.Length == this.ActualPath.Length)
            {
                for (int i = 0; i < devices.Length; i++)
                {
                    if (devices[i] != this.ActualPath[i])
                    {
                        return false;
                    }
                }
                return true;
            }
            return false;
        }


        private bool isPath = false;

        #region Object Override

        /// <inheritdoc/>
        public override string ToString() => GetDisplayPath();

        #endregion
    }

    public class AvTreeNode : ReactiveObject
    {
        private AvTreeNode _Parent;
        public AvTreeNode Parent
        {
            get => _Parent;
            set => this.RaiseAndSetIfChanged(ref _Parent, value);
        }
        private ObservableCollection<AvTreeNode> _ChildNodes;
        public ObservableCollection<AvTreeNode> ChildNodes
        {
            get => _ChildNodes;
            set => this.RaiseAndSetIfChanged(ref _ChildNodes, value);
        }
        private string _Text;
        public string Text
        {
            get => _Text;
            set => this.RaiseAndSetIfChanged(ref _Text, value);
        }
        private string _Icon;
        public string Icon
        {
            get => _Icon;
            set => this.RaiseAndSetIfChanged(ref _Icon, value);
        }
        private object _Tag;
        public object Tag
        {
            get => _Tag;
            set => this.RaiseAndSetIfChanged(ref _Tag, value);
        }
        public AvTreeNode(string _name, object tag, AvTreeNode parent)
        {
            Text = _name;
            Tag = tag;
            ChildNodes = new ObservableCollection<AvTreeNode>();
            Parent = parent;
        }
    }


}
