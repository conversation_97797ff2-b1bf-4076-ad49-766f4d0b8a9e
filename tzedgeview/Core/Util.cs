using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using HslTechnology.Edge.Node.Database;

namespace tzedgeview.Core
{
    /// <summary>
    /// 跨平台工具类
    /// </summary>
    public static class Util
    {
        #region Static Member

        public static string RemoteServerIp = "";
        public static string CopyRightAuthor = "福建省天正信息资讯工程有限公司";
        public static string ReleaseDate = "2025-02-10";                                    // 发布日期
        public static GroupNodeImages GroupNodeImages = new GroupNodeImages();             // 默认的图标显示资源

        #endregion

        #region Static Method

        /// <summary>
        /// 客户端的配置信息
        /// </summary>
        public static ViewerSettings ViewerSettings { get; set; }

        /// <summary>
        /// 当前的主题信息
        /// </summary>
        public static string CurrentTheme { get; set; } = "Light";

        /// <summary>
        /// 获取文件的完整路径信息
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>完整路径</returns>
        public static string GetFileFullPath(string fileName)
        {
            try
            {
                string directory = AppDomain.CurrentDomain.BaseDirectory;
                return Path.Combine(directory, fileName);
            }
            catch
            {
                return fileName;
            }
        }

        /// <summary>
        /// 获取应用程序数据目录
        /// </summary>
        /// <returns>数据目录路径</returns>
        public static string GetAppDataDirectory()
        {
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            string appDirectory = Path.Combine(appDataPath, "tzedgeview");
            
            if (!Directory.Exists(appDirectory))
            {
                Directory.CreateDirectory(appDirectory);
            }
            
            return appDirectory;
        }

        /// <summary>
        /// 从资源加载位图
        /// </summary>
        /// <param name="resourcePath">资源路径</param>
        /// <returns>位图对象</returns>
        public static Bitmap LoadBitmapFromResource(string resourcePath)
        {
            try
            {
                var assets = AssetLoader.Open(new Uri($"avares://tzedgeview/{resourcePath}"));
                return new Bitmap(assets);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 安全执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        public static void SafeExecute(Action action)
        {
            try
            {
                action?.Invoke();
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"Error executing action: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步安全执行操作
        /// </summary>
        /// <param name="action">要执行的异步操作</param>
        public static async Task SafeExecuteAsync(Func<Task> action)
        {
            try
            {
                if (action != null)
                    await action();
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"Error executing async action: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的文件大小字符串</returns>
        public static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 验证IP地址格式
        /// </summary>
        /// <param name="ipAddress">IP地址字符串</param>
        /// <returns>是否为有效IP地址</returns>
        public static bool IsValidIPAddress(string ipAddress)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                return false;

            return System.Net.IPAddress.TryParse(ipAddress, out _);
        }

        /// <summary>
        /// 验证端口号
        /// </summary>
        /// <param name="port">端口号</param>
        /// <returns>是否为有效端口号</returns>
        public static bool IsValidPort(int port)
        {
            return port > 0 && port <= 65535;
        }

        /// <summary>
        /// 获取当前时间戳
        /// </summary>
        /// <returns>时间戳字符串</returns>
        public static string GetTimestamp()
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 获取应用程序版本信息
        /// </summary>
        /// <returns>版本字符串</returns>
        public static string GetApplicationVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "1.0.0.0";
            }
            catch
            {
                return "1.0.0.0";
            }
        }

        #endregion
    }
}
