using CommunityToolkit.Mvvm.ComponentModel;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json.Serialization;

namespace tzedgeview.Core
{
    public class IconUrl
    {
        public const string GWDevice_Normal = "/Assets/Svg/building.svg";
        public const string GWDevice_Select = "/Assets/Svg/building-circle-arrow-right.svg";
        public const string GroupNode = "/Assets/Svg/Class_489.svg";
        public const string GroupSocketPipe = "/Assets/Svg/cloud.svg";
        public const string GroupSerialPipe = "/Assets/Svg/network-wired.svg";
        public const string GroupAll = "/Assets/Svg/Module_648.svg";
        public const string NodeRequestNode_en = "/Assets/Svg/folder-open.svg";
        public const string NodeRequestNode_dis = "/Assets/Svg/folder-closed.svg";
    }

    public class ScalarDataInfo : ObservableObject
    {
        private string _Name;
        public string Name
        {
            get => _Name;
            set => SetProperty(ref _Name, value);
        }
        private string _DisplayName;
        public string DisplayName
        {
            get => _DisplayName;
            set => SetProperty(ref _DisplayName, value);
        }
        private string _Value;
        public string Value
        {
            get => _Value;
            set => SetProperty(ref _Value, value);
        }
        private string _Unit;
        public string Unit
        {
            get => _Unit;
            set => SetProperty(ref _Unit, value);
        }
        private string _dataNode;
        public string dataNode
        {
            get => _dataNode;
            set => SetProperty(ref _dataNode, value);
        }
        private string _AccessLevel;
        public string AccessLevel
        {
            get => _AccessLevel;
            set => SetProperty(ref _AccessLevel, value);
        }
        private string _Description;
        public string Description
        {
            get => _Description;
            set => SetProperty(ref _Description, value);
        }
        private object _Tag;
        public object Tag
        {
            get => _Tag;
            set => SetProperty(ref _Tag, value);
        }
    }

    public class MethodDataInfo : ObservableObject
    {
        private string _ApiTopic;
        public string ApiTopic
        {
            get => _ApiTopic;
            set => SetProperty(ref _ApiTopic, value);
        }
        private string _MethodSignature;
        public string MethodSignature
        {
            get => _MethodSignature;
            set => SetProperty(ref _MethodSignature, value);
        }
        private string _Description;
        public string Description
        {
            get => _Description;
            set => SetProperty(ref _Description, value);
        }
        private object _Tag;
        public object Tag
        {
            get => _Tag;
            set => SetProperty(ref _Tag, value);
        }
    }

    public class AlarmItem : ObservableObject
    {
        #region Contructor

        /// <summary>
        /// 实例化一个默认的对象
        /// </summary>
        public AlarmItem()
        {
            this.StartTime = HslTechnologyHelper.GetDateTimeNow();
            this.FinishTime = this.StartTime;
        }

        /// <summary>
        /// 从另一个报警的对象实例化，相当于拷贝的操作信息
        /// </summary>
        /// <param name="item">另一个报警对象</param>
        public AlarmItem(AlarmItem item)
        {
            this.uniqueId = item.UniqueId;
            this.AlarmCode = item.AlarmCode;
            this.AlarmContent = item.AlarmContent;
            this.StartTime = item.StartTime;
            if (item.Status == AlarmStatus.Alarm)
                this.FinishTime = DateTime.Now;
            else
                this.FinishTime = item.FinishTime;
            this.Checked = item.Checked;
            this.Degree = item.Degree;
            this.Status = item.Status;
            this.DeviceName = item.DeviceName;
            this.TagName = item.TagName;
        }

        /// <summary>
        /// 使用唯一的设备标识和报警描述信息来初始化报警
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <param name="alarmCode">报警的代号</param>
        /// <param name="alarmContent">报警的描述信息</param>
        public AlarmItem(string deviceName, int alarmCode, string alarmContent) : this()
        {
            this.DeviceName = deviceName;
            this.AlarmCode = alarmCode;
            this.AlarmContent = alarmContent;
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// 本次系统运行的唯一报警信息，用来标识操作的信息的
        /// </summary>
        private long _UniqueId;
        public long UniqueId
        {
            get => _UniqueId;
            set => SetProperty(ref _UniqueId, value);
        }

        /// <summary>
        /// 报警的ID信息，在bool, integer, hex报警里作为区分的标记
        /// </summary>
        private int _AlarmCode;
        public int AlarmCode
        {
            get => _AlarmCode;
            set => SetProperty(ref _AlarmCode, value);
        }

        /// <summary>
        /// 报警的开始时间
        /// </summary>
        [JsonConverter(typeof(DateTimeFormatConverter))]
        private DateTime _StartTime;
        public DateTime StartTime
        {
            get => _StartTime;
            set => SetProperty(ref _StartTime, value);
        }

        /// <summary>
        /// 报警的结束时间
        /// </summary>
        [JsonConverter(typeof(DateTimeFormatConverter))]
        private DateTime _FinishTime;
        public DateTime FinishTime
        {
            get => _FinishTime;
            set => SetProperty(ref _FinishTime, value);
        }

        /// <summary>
        /// 报警的内容
        /// </summary>
        private string _AlarmContent;
        public string AlarmContent
        {
            get => _AlarmContent;
            set => SetProperty(ref _AlarmContent, value);
        }

        /// <summary>
        /// 当前的报警是否被确认过
        /// </summary>
        private bool _Checked;
        public bool Checked
        {
            get => _Checked;
            set => SetProperty(ref _Checked, value);
        }

        /// <summary>
        /// 当前报警的等级
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        private AlarmDegree _Degree;
        public AlarmDegree Degree
        {
            get => _Degree;
            set => SetProperty(ref _Degree, value);
        }

        /// <summary>
        /// 报警的状态，表示当前的标记
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        private AlarmStatus _Status;
        public AlarmStatus Status
        {
            get => _Status;
            set => SetProperty(ref _Status, value);
        }

        /// <summary>
        /// 当前报警所在的设备名称，带路径的唯一设备名称
        /// </summary>
        private string _DeviceName;
        public string DeviceName
        {
            get => _DeviceName;
            set => SetProperty(ref _DeviceName, value);
        }

        /// <summary>
        /// 当前报警所绑定的数据标签名称
        /// </summary>
        public string TagName { get; set; }

        #endregion

        #region Public Method

        /// <summary>
        /// 根据节点的配置规则来提升当前的报警等级
        /// </summary>
        /// <param name="alarmDefinition">报警的定义</param>
        public void RaiseAlarmDegree(AlarmDefinitionNode alarmDefinition)
        {
            switch (Degree)
            {
                case AlarmDegree.Hint:
                    if (alarmDefinition.DegreeHintRaise > 0)
                        if ((DateTime.Now - StartTime).TotalSeconds > alarmDefinition.DegreeHintRaise)
                            Degree = AlarmDegree.Warn;
                    break;
                case AlarmDegree.Warn:
                    if (alarmDefinition.DegreeWarnRaise > 0)
                        if ((DateTime.Now - StartTime).TotalSeconds > alarmDefinition.DegreeWarnRaise)
                            Degree = AlarmDegree.Error;
                    break;
                case AlarmDegree.Error:
                    if (alarmDefinition.DegreeErrorRaise > 0)
                        if ((DateTime.Now - StartTime).TotalSeconds > alarmDefinition.DegreeErrorRaise)
                            Degree = AlarmDegree.Fatal;
                    break;
            }
        }

        #endregion

        #region Private Member

        private long uniqueId = 0;                              // 报警的唯一标识
        private static long AlarmIdCurrent = 0;

        #endregion

        #region Static Helper

        /// <summary>
        /// 获取一个新的<see cref="AlarmItem"/>对象，将会赋值一个唯一的报警ID信息
        /// </summary>
        /// <returns>报警对象信息</returns>
        public static AlarmItem GetAlamItemWithUniqueId()
        {
            return new AlarmItem()
            {
                uniqueId = Interlocked.Increment(ref AlarmIdCurrent)
            };
        }

        #endregion
    }

    public class OfflineDataInfo : ObservableObject
    {
        private string _Index;
        public string Index
        {
            get => _Index;
            set => SetProperty(ref _Index, value);
        }

        private string _BeginTime;
        public string BeginTime
        {
            get => _BeginTime;
            set => SetProperty(ref _BeginTime, value);
        }

        private string _EndTime;
        public string EndTime
        {
            get => _EndTime;
            set => SetProperty(ref _EndTime, value);
        }

        private string _InTime;
        public string InTime
        {
            get => _InTime;
            set => SetProperty(ref _InTime, value);
        }

        private string _Memo;
        public string Memo
        {
            get => _Memo;
            set => SetProperty(ref _Memo, value);
        }

        private object _Tag;
        public object Tag
        {
            get => _Tag;
            set => SetProperty(ref _Tag, value);
        }
    }
}
