using HslTechnology.Edge.Controls;
using HslTechnology.EdgeViewer.Controls;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Viewer
{
	public partial class FormAbout : HslForm
	{
		public FormAbout( )
		{
			InitializeComponent( );
		}

		private void FormAbout_Load( object sender, EventArgs e )
		{
			label2.Text = "版本号：" + System.Reflection.Assembly.GetExecutingAssembly( ).GetName( ).Version.ToString( );
			label4.Text = "日期：" + Util.ReleaseDate;
			label3.Text = "版权: " + Util.CopyRightAuthor;
		}
	}
}
