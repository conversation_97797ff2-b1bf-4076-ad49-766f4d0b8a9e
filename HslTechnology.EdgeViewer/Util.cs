using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using HslCommunication.BasicFramework;
using System.IO;
using System.Drawing.Imaging;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Reflection;
using System.Reflection;
using HslCommunication;
using HslTechnology.EdgeViewer.Core;
using HslTechnology.EdgeViewer.Pages;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Plugins;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Node.Render;
using System.Runtime.InteropServices;
using WeifenLuo.WinFormsUI.Docking;
using WeifenLuo.WinFormsUI.ThemeVS2015;
using HslTechnology.Edge.Node.Database;

namespace HslTechnology.EdgeViewer
{
	public static class Util
	{
		#region Static Member

		public static string RemoteServerIp = "";
		public static string CopyRightAuthor = "福建省天正信息资讯工程有限公司";
		public static string ReleaseDate = "2025-02-10";                                    // 发布日期
		public static GroupNodeImages GroupNodeImages = new GroupNodeImages( );             // 默认的图标显示资源

		#endregion

		#region Static Method

		/// <summary>
		/// 客户端的配置信息
		/// </summary>
		public static ViewerSettings ViewerSettings { get; set; }

		/// <summary>
		/// 当前的主题信息
		/// </summary>
		public static VS2015ThemeBase Theme { get; set; }

		/// <summary>
		/// 获取文件的完整的路径名称，会携带当前exe程序的目录
		/// </summary>
		/// <param name="fileName">文件的名称</param>
		/// <returns>完整的路径名词</returns>
		public static string GetFileFullPath( string fileName )
		{
			return System.IO.Path.Combine( AppDomain.CurrentDomain.BaseDirectory, fileName );
		}

		/// <summary>
		/// 将原始字节的数据转为实际的图片资源
		/// </summary>
		/// <param name="buffer">原始字节数据</param>
		/// <returns>图片资源</returns>
		public static Image GetImageFromBytes( byte[] buffer )
		{
			MemoryStream ms = new MemoryStream( buffer );
			Image image = Image.FromStream( ms );
			ms.Dispose( );
			return image;
		}

		/// <summary>
		/// 将节点的列表赋值到树形控件上去，树形控件根据显示的节点的数量自动适配节点数量
		/// </summary>
		/// <typeparam name="T">节点的实际类型信息</typeparam>
		/// <param name="treeNode">树形节点控件</param>
		/// <param name="nodes">等待更新的节点数组</param>
		/// <param name="markChaneg">标记变更操作委托</param>
		public static void TreeNodeSpecifyCount<T>( TreeNode treeNode, T[] nodes, Action markChaneg ) where T : GroupNode
		{
			int count = treeNode.Nodes.Count;
			for (int i = 0; i < nodes.Length; i++)
			{
				if (i < count)
				{
					treeNode.Nodes[i].Text = nodes[i].GetDisplayName(NodeDisplayMode.ShowCombine);
					treeNode.Nodes[i].Tag = nodes[i];
				}
				else
				{
					treeNode.Nodes.Add( CreateTreeNodeFromXml( serverSettings: null, nodes[i], markChaneg, NodeDisplayMode.ShowCombine ) );
				}
			}
			while(treeNode.Nodes.Count > nodes.Length)
			{
				// 移除多余的节点信息
				treeNode.Nodes.RemoveAt( treeNode.Nodes.Count - 1 );
			}
		}

		/// <summary>
		/// 判断设备的名称是否在设备的路径下
		/// </summary>
		/// <param name="deviceName">设备的名称信息</param>
		/// <param name="path">路径 信息</param>
		/// <returns>非真即假的结果对象</returns>
		public static bool IsDeviceNameInPath( string deviceName, string[] path )
		{
			string[] devices = deviceName.Split( new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries ).RemoveLast( 1 );
			if (devices.Length == path.Length)
			{
				for (int i = 0; i < devices.Length; i++)
				{
					if (devices[i] != path[i])
					{
						return false;
					}
				}
				return true;
			}
			return false;
		}

		/// <summary>
		/// 子窗口的图标显示信息
		/// </summary>
		/// <returns>获取图标</returns>
		public static Icon GetWinformIcon( )
		{
			return Icon.ExtractAssociatedIcon( Application.ExecutablePath );
		}

		/// <summary>
		/// 获取消息的小图标内容
		/// </summary>
		/// <param name="type">小图标的类型</param>
		/// <param name="backColor">背景色</param>
		/// <returns>图形信息</returns>
		public static Bitmap CreatIconImage( string type, Color backColor )
		{
			Bitmap bitmap = new Bitmap( 16, 16 );
			Graphics g = Graphics.FromImage( bitmap );
			g.Clear( backColor );
			g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
			if (type == "Message")
			{
				g.DrawRectangle( Pens.WhiteSmoke, 1, 3, 14, 10 );
				g.DrawLines( Pens.WhiteSmoke, new Point[] { new Point( 1, 3 ), new Point( 8, 8 ), new Point( 14, 3 ) } );
			}
			else if (type == "Clock")
			{
				g.DrawEllipse( Pens.WhiteSmoke, 2, 2, 12, 12 );
				g.DrawLines( Pens.WhiteSmoke, new Point[] { new Point( 8, 2 ), new Point( 8, 8 ), new Point( 13, 8 ) } );
			}
			else if (type == "StatusOk")
			{
				g.FillEllipse( Brushes.Lime, 2, 2, 12, 12 );
			}
			else if (type == "StatusInfo")
			{
				g.FillEllipse( Brushes.LightGray, 2, 2, 12, 12 );
			}
			else if (type == "StatusError")
			{
				g.FillEllipse( Brushes.Red, 2, 2, 12, 12 );
			}
			g.Dispose( );
			return bitmap;
		}

		/// <summary>
		/// 解析一个配置文件中的所有的规则解析，并返回一个词典信息
		/// </summary>
		/// <param name="nodeClass">配置文件的根信息</param>
		/// <returns>词典</returns>
		public static Dictionary<string, List<RegularScalarNode>> ParesRegular( XElement nodeClass )
		{
			Dictionary<string, List<RegularScalarNode>> regularkeyValuePairs = new Dictionary<string, List<RegularScalarNode>>( );
			foreach (var xmlNode in nodeClass.Elements( ))
			{
				if (xmlNode.Attribute( nameof( GroupNode.Name ) ).Value == GroupNode.RootRegular)
				{
					foreach (XElement element in xmlNode.Elements( nameof( RegularScalarNode ) ))
					{
						List<RegularScalarNode> itemNodes = new List<RegularScalarNode>( );
						foreach (XElement xmlItemNode in element.Elements( nameof( RegularScalarNode ) ))
						{
							RegularScalarNode regularItemNode = new RegularScalarNode( );
							regularItemNode.LoadByXmlElement( xmlItemNode );
							itemNodes.Add( regularItemNode );
						}

						if (regularkeyValuePairs.ContainsKey( element.Attribute( nameof( GroupNode.Name ) ).Value ))
						{
							regularkeyValuePairs[element.Attribute( nameof( GroupNode.Name ) ).Value] = itemNodes;
						}
						else
						{
							regularkeyValuePairs.Add( element.Attribute( nameof( GroupNode.Name ) ).Value, itemNodes );
						}
					}
				}
			}
			return regularkeyValuePairs;
		}

		public static Image GetImageByGroupNode( EdgeServerSettings serverSettings, GroupNode groupNode )
		{
			if (serverSettings != null)
				return serverSettings.NodeImages.DeviceImageList.ImageList.Images[GetImageKeyByGroupNode( serverSettings, groupNode )];
			return GroupNodeImages.DeviceImageList.ImageList.Images[GetImageKeyByGroupNode( serverSettings, groupNode )];
		}

		public static string GetImageKeyByGroupNode( EdgeServerSettings serverSettings, GroupNode groupNode )
		{
			if (groupNode == null)                                     return "StatusAnnotations_Help_and_inconclusive_16xLG_color";
			if (groupNode.NodeType == NodeType.GroupNode)              return "Class_489";
			if (groupNode.NodeType == NodeType.RequestGroupNode)       return "Class_489";
			if (groupNode.NodeType == NodeType.GroupSerialPipe)        return "SerialPort";
			if (groupNode.NodeType == NodeType.GroupSocketPipe)        return "NetworkAdapter";
			if (groupNode.NodeType == NodeType.GroupSingleThread)      return "ClassIcon";
			if (groupNode.NodeType == NodeType.RegularStructItemNode)  return "Module_648";
			if (groupNode.NodeType == NodeType.RegularStructNode)      return "Module_blue";
			if (groupNode.NodeType == NodeType.RegularScalarNode)      return "Operator_660";
			if (groupNode.NodeType == NodeType.RegularWriteMapping)    return "interface_16xLG";
			if (groupNode.NodeType == NodeType.RequestNode)
			{
				if (groupNode is ScalarReadRequest scalarReadRequest)
					return scalarReadRequest.Enable ? "Enum_582" : "Enum_582_cancel";
				if (groupNode is SourceReadRequest sourceReadRequest) 
					return sourceReadRequest.Enable ? "hardware_16xLG" : "hardware_16xLG_cancel";
				if (groupNode is CallMethodRequest scallMethodRequest ||
					groupNode is ScalarWriteRequest writeRequest )
					return "Method_636";
				if (groupNode is DatabaseRequest databaseRequest)
					return "Database_request";
			}
			if (groupNode.NodeType == NodeType.DeviceNode ||
				groupNode.NodeType == NodeType.CncNode ||
				groupNode.NodeType == NodeType.RobotNode)
			{
				if (groupNode is DeviceNode device)
				{
					if (device.DeviceType == DeviceType.Plugins || !string.IsNullOrEmpty( device.PluginsType ))
						return serverSettings != null ? 
							serverSettings.NodeImages.GetImageKeyByPlugins( device.PluginsType ) : 
							GroupNodeImages.GetImageKeyByPlugins( device.PluginsType );
					else
						return EdgeReflectionHelper.GetImageKeyFrom( device.DeviceType );
				}
			}
			if (groupNode.NodeType == NodeType.AlarmNode)             return "HotSpot_10548_color";
			if (groupNode.NodeType == NodeType.AlarmDefinitionNode)   return "HotSpot_10548";
			if (groupNode.NodeType == NodeType.AlarmDatabase)         return "Database_request";
			if (groupNode.NodeType == NodeType.OeeNode)               return "oee";
			if (groupNode.NodeType == NodeType.DataOeeDefinitionNode) return "oee";
			if (groupNode.NodeType == NodeType.DatabaseNode)          return "Database_node";
			return "StatusAnnotations_Help_and_inconclusive_16xLG_color";
		}

		private static TreeNode CreateGroupNodeFromXml<T>( XElement element, Action markChaneg, NodeDisplayMode displayMode ) where T : GroupNode, new()
		{
			if (element.Attribute( nameof( GroupNode.Name ) ) == null) return null;

			T nodeClass = new T( );
			nodeClass.LoadByXmlElement( element );
			return CreateTreeNodeFromXml( serverSettings : null, nodeClass, markChaneg, displayMode );
		}

		private static TreeNode CreateTreeNodeFromXml<T>( TreeNode parent, XElement element, Action markChaneg, NodeDisplayMode displayMode ) where T : GroupNode, new()
		{
			TreeNode treeNode = CreateGroupNodeFromXml<T>( element, markChaneg, displayMode );
			if (treeNode != null) parent.Nodes.Add( treeNode );
			return treeNode;
		}

		private static TreeNode CreateTreeNodeFromXml( EdgeServerSettings serverSettings, TreeNode parent, GroupNode group, Action markChaneg, NodeDisplayMode displayMode, bool nameChanged = true )
		{
			TreeNode treeNode = CreateTreeNodeFromXml( serverSettings, group, markChaneg, displayMode, nameChanged );
			if (treeNode != null) parent.Nodes.Add( treeNode );
			return treeNode;
		}

		public static TreeNode CreateTreeNodeFromXml( EdgeServerSettings serverSettings, GroupNode group, Action markChange, NodeDisplayMode displayMode, bool nameChanged = true )
		{
			TreeNode node = new TreeNode( group.GetDisplayName( displayMode ) );
			node.SetImageKey( GetImageKeyByGroupNode( serverSettings, group ) );
			node.Tag = group;

			if (nameChanged)
			{
				group.OnNameChanged += ( m, n ) =>
				{
					GroupNode gn = m as GroupNode;
					gn.Name = FormNodeSettings.GetUniqueGroupName( node, gn.Name );
					node.Text = gn.GetDisplayName( );
					markChange?.Invoke( );
				};
			}
			if (group.NodeType == NodeType.RequestNode)
			{
				if (group is RequestBase request)
				{
					request.OnEnableChanged += ( m, n ) =>
					{
						GroupNode gn = m as GroupNode;
						node.SetImageKey( Util.GetImageKeyByGroupNode( serverSettings, gn ) );
						markChange?.Invoke( );
					};
				}
			}
			return node;
		}

		/// <summary>
		/// 主要用于网关主界面呈现网关设备列表，显示的是别名信息
		/// </summary>
		/// <param name="serverSettings">网关服务器的连接配置对象</param>
		/// <param name="treeNode">当前的树形节点信息</param>
		/// <param name="element">当前显示的元素信息</param>
		public static void RenderEdgeServerTreeNodeBrowseNode( EdgeServerSettings serverSettings, TreeNode treeNode, XElement element )
		{
			foreach (XElement item in element.Elements( ))
			{
				if (item.Name == NodeType.GroupNode.ToString( ))
				{
					TreeNode node = CreateTreeNodeFromXml<GroupNode>( treeNode, item, null, NodeDisplayMode.ShowDisplayName );
					RenderEdgeServerTreeNodeBrowseNode( serverSettings, node, item );
				}
				else if (item.Name == NodeType.GroupSerialPipe.ToString( ))
				{
					NodeSerialPipe nodeSerialPipe = new NodeSerialPipe( item );
					if (nodeSerialPipe.UseAsGroupNode)
					{
						TreeNode node = CreateTreeNodeFromXml<NodeSerialPipe>( treeNode, item, null, NodeDisplayMode.ShowDisplayName );
						RenderEdgeServerTreeNodeBrowseNode( serverSettings, node, item );
					}
					else
					{
						RenderEdgeServerTreeNodeBrowseNode( serverSettings, treeNode, item );
					}
				}
				else if (item.Name == NodeType.GroupSocketPipe.ToString( ))
				{
					NodeSocketPipe nodeSocketPipe = new NodeSocketPipe( item );
					if (nodeSocketPipe.UseAsGroupNode)
					{
						TreeNode node = CreateTreeNodeFromXml<NodeSocketPipe>( treeNode, item, null, NodeDisplayMode.ShowDisplayName );
						RenderEdgeServerTreeNodeBrowseNode( serverSettings, node, item );
					}
					else
					{
						RenderEdgeServerTreeNodeBrowseNode( serverSettings, treeNode, item );
					}
				}
				else if (item.Name == NodeType.GroupSingleThread.ToString( ))
				{
					NodeSingleThread nodeSingleThread = new NodeSingleThread( item );
					if (nodeSingleThread.UseAsGroupNode)
					{
						TreeNode node = CreateTreeNodeFromXml<NodeSingleThread>( treeNode, item, null, NodeDisplayMode.ShowDisplayName );
						RenderEdgeServerTreeNodeBrowseNode( serverSettings, node, item );
					}
					else
					{
						RenderEdgeServerTreeNodeBrowseNode( serverSettings, treeNode, item );
					}
				}
				else if (item.Name == NodeType.DeviceNode.ToString( ) ||
						 item.Name == NodeType.RobotNode.ToString( ) ||
						 item.Name == NodeType.CncNode.ToString( ))
				{
					DeviceType type = SoftBasic.GetEnumFromString<DeviceType>( item.Attribute( nameof( DeviceNode.DeviceType ) ).Value );
					DeviceNode dn = new DeviceNode( item, type );
					TreeNode deviceTreeNode = CreateTreeNodeFromXml( serverSettings, dn, null, NodeDisplayMode.ShowDisplayName );
					deviceTreeNode.Tag = dn;
					deviceTreeNode.Nodes.Add( "" );
					treeNode.Nodes.Add( deviceTreeNode );
				}
			}
		}

		public static GroupNode CreateGroupNodeFromXml( EdgeServerSettings serverSettings, XElement element, out bool isExpand )
		{
			isExpand = false;
			if      (element.Name == NodeType.GroupNode.ToString( ))         { isExpand = true; return new GroupNode(        element ); }
			if      (element.Name == NodeType.RequestGroupNode.ToString( ))  { isExpand = true; return new RequestGroupNode( element ); }
			else if (element.Name == NodeType.GroupSerialPipe.ToString( ))   { isExpand = true; return new NodeSerialPipe(   element ); }
			else if (element.Name == NodeType.GroupSocketPipe.ToString( ))   { isExpand = true; return new NodeSocketPipe(   element ); }
			else if (element.Name == NodeType.GroupSingleThread.ToString( )) { isExpand = true; return new NodeSingleThread( element ); }
			else if (element.Name == NodeType.DeviceNode.ToString( ) ||
					 element.Name == NodeType.RobotNode.ToString( ) ||
					 element.Name == NodeType.CncNode.ToString( ))
			{
				string type = "Device";
				try
				{
					type = element.Attribute( nameof( DeviceNode.DeviceType ) ).Value;
					if (!DeviceDefinitions.ContainsDeviceKey( type )) throw new Exception( "DeviceType: " + type + " 注册列表不存在" );
				}
				catch (Exception ex)
				{
					MessageBox.Show( $"当前类型 [{element.Attribute( nameof( DeviceType ) ).Value}] 获取失败：{ex.Message}\r\n" +
						$"是否忽略继续解析操作？", "错误提示" );
					return null;
				}

				// 区分插件设备和内置设备，如果是内置设备，可以从类型反射加载
				DeviceNode deviceNode = null;
				if (type == DeviceDefinitions.Plugins)
				{
					deviceNode = new DeviceNode( element, DeviceType.Plugins );

					if (serverSettings == null || serverSettings.PluginsDefinition == null || !serverSettings.PluginsDefinition.ContainsKey( deviceNode.PluginsType ))
					{
						// 这里只拷贝设备相关的节点信息
						XElement deviceXml = new XElement( element.Name );
						foreach (XAttribute attribute in element.Attributes( ))
						{
							deviceXml.SetAttributeValue( attribute.Name, attribute.Value );
						}
						deviceNode.Tag = deviceXml;
					}
					else
					{
						NodePropertyConfig[] cloneArray = NodePropertyConfig.DeepCloneArray( serverSettings.PluginsDefinition[deviceNode.PluginsType] );
						NodePropertyConfig.SetNodePropertyConfigFromXml( cloneArray, element );
						deviceNode.Tag = cloneArray;
					}
				}
				else
				{
					deviceNode = EdgeReflectionHelper.CreateInstanceFrom( type );
					deviceNode.LoadByXmlElement( element );
				}

				isExpand = true;
				return deviceNode;
			}
			else if (element.Name == NodeType.RequestNode.ToString( ))
			{
				RequestType requestType = SoftBasic.GetEnumFromString<RequestType>( element.Attribute( nameof( RequestType ) ).Value );
				if      (requestType == RequestType.ScalarRead)  return new ScalarReadRequest( element );
				else if (requestType == RequestType.ScalarCache) return new ScalarCacheRequest( element );
				else if (requestType == RequestType.SourceRead)  { isExpand = true; return new SourceReadRequest( element ); }
				else if (requestType == RequestType.MethodCall)  return new CallMethodRequest( element );
				else if (requestType == RequestType.DatabaseOperate) return new DatabaseRequest( element );
				else if (requestType == RequestType.WriteInterval) return new ScalarWriteRequest( element );
			}
			else if (element.Name == nameof( NodeType.RequestGroupNode )) return new RequestGroupNode( element );
			else if (element.Name == nameof( NodeType.RegularScalarNode )) return new RegularScalarNode( element );
			else if (element.Name == nameof( NodeType.RegularStructNode )) return new RegularStructNode( element );
			else if (element.Name == nameof( NodeType.RegularStructItemNode )) { isExpand = true; return new RegularStructItemNode( element ); }
			else if (element.Name == nameof( NodeType.RegularWriteMapping )) return new RegularAddressWriteMappingNode( element );
			else if (element.Name == nameof( NodeType.AlarmNode ))
			{
				AlarmNode alarmNode = AlarmNode.CreateAlarmNodeFromXml( element );
				isExpand = alarmNode.AlarmType == AlarmType.Boolean || alarmNode.AlarmType == AlarmType.Integer || alarmNode.AlarmType == AlarmType.Hex;
				return alarmNode;
			}
			else if (element.Name == nameof( NodeType.AlarmDefinitionNode )) return new AlarmDefinitionNode( element );
			else if (element.Name == nameof( NodeType.AlarmDatabase )) return new AlarmDatabaseNode( element );
			else if (element.Name == nameof( NodeType.OeeNode )) { isExpand = true; return new OeeNode( element ); }
			else if (element.Name == nameof( NodeType.DataOeeDefinitionNode )) return new OeeDefinitionNode( element );
			else if (element.Name == nameof( NodeType.DatabaseNode ))
			{
				DatabaseType databaseType = SoftBasic.GetEnumFromString<DatabaseType>( element.Attribute( nameof( DatabaseNodeNet.DbType ) ).Value );
				if (databaseType == DatabaseType.SQLServer) return new NodeSqlServer( element );
			}
			return null;
		}

		/// <summary>
		/// 将Xml中的设备信息，显示到树形结构中去，并提供一个图标显示的方案，主要用于配置设备的节点内容
		/// </summary>
		/// <param name="serverSettings">服务器的配置信息</param>
		/// <param name="treeNode">树形信息</param>
		/// <param name="element">元素信息</param>
		/// <param name="markChaneg">是否标记错误的委托</param>
		/// <param name="nameChanged">是否引发名称变化的事件</param>
		/// <param name="isRenderRequest">是否将Request信息显示到节点上去</param>
		public static void RenderEdgeSettingsTreeNode( EdgeServerSettings serverSettings, TreeNode treeNode, XElement element, bool isRenderRequest, Action markChaneg, bool nameChanged = true )
		{
			foreach (XElement item in element.Elements( ))
			{
				GroupNode node = CreateGroupNodeFromXml( serverSettings, item, out bool isExpand );
				if (node == null) continue;
				if (node.Name.StartsWith( "__" )) continue;  // 不需要显示的特殊节点信息

				if (node.NodeType == NodeType.RequestNode) 
					if (!isRenderRequest) 
						continue;
				TreeNode treeNodeChild = CreateTreeNodeFromXml( serverSettings, treeNode, node, markChaneg, NodeDisplayMode.ShowCombine, nameChanged );
				if (isExpand) RenderEdgeSettingsTreeNode( serverSettings, treeNodeChild, item, isRenderRequest, markChaneg );
			}
		}

		public static OperateResult SetAlarmRelateByAlarmNode( IAlarmRelateNode alarmRelateNode, AlarmNode alarmNode )
		{
			if (alarmRelateNode.DataTypeCode == RegularNodeTypeItem.Bool.Text)
			{
				if (alarmNode.AlarmType != AlarmType.Boolean && alarmNode.AlarmType != AlarmType.Hex)
					return new OperateResult( $"当前的数据类型只支持 {AlarmType.Boolean} 类型或是 {AlarmType.Hex} 报警" );
			}
			else if (alarmRelateNode.DataTypeCode == RegularNodeTypeItem.Byte.Text && alarmRelateNode.Length > 1)
			{
				if (alarmNode.AlarmType != AlarmType.Boolean && alarmNode.AlarmType != AlarmType.Hex) 
					return new OperateResult( $"当前的数据类型只支持 {AlarmType.Boolean} 类型或是 {AlarmType.Hex} 报警" );
			}
			else if (
				alarmRelateNode.Length < 0 &&
				alarmRelateNode.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
				alarmRelateNode.DataTypeCode == RegularNodeTypeItem.Int16.Text ||
				alarmRelateNode.DataTypeCode == RegularNodeTypeItem.UInt16.Text ||
				alarmRelateNode.DataTypeCode == RegularNodeTypeItem.Int32.Text ||
				alarmRelateNode.DataTypeCode == RegularNodeTypeItem.UInt32.Text ||
				alarmRelateNode.DataTypeCode == RegularNodeTypeItem.Int64.Text ||
				alarmRelateNode.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
			{
				if (alarmNode.AlarmType == AlarmType.Boolean) return new OperateResult( $"当前的数据类型不支持 {AlarmType.Boolean} 类型报警" );
				if (alarmNode.AlarmType == AlarmType.Hex)     return new OperateResult( $"当前的数据类型不支持 {AlarmType.Hex} 类型报警" );
				if (alarmNode.AlarmType == AlarmType.String)  return new OperateResult( $"当前的数据类型不支持 {AlarmType.String} 类型报警" );
			}
			else if (alarmRelateNode.Length < 0 &&
			   alarmRelateNode.DataTypeCode == RegularNodeTypeItem.Float.Text ||
			   alarmRelateNode.DataTypeCode == RegularNodeTypeItem.Double.Text)
			{
				if (alarmNode.AlarmType == AlarmType.Boolean) return new OperateResult( $"当前的数据类型只支持 {AlarmType.DataRange}，不支持 {AlarmType.Boolean} 类型报警" );
				if (alarmNode.AlarmType == AlarmType.Hex)     return new OperateResult( $"当前的数据类型只支持 {AlarmType.DataRange}，不支持 {AlarmType.Hex} 类型报警" );
				if (alarmNode.AlarmType == AlarmType.String)  return new OperateResult( $"当前的数据类型只支持 {AlarmType.DataRange}，不支持 {AlarmType.String} 类型报警" );
				if (alarmNode.AlarmType == AlarmType.Integer) return new OperateResult( $"当前的数据类型只支持 {AlarmType.DataRange}，不支持 {AlarmType.Integer} 类型报警" );
			}
			alarmRelateNode.AlarmRelate = alarmNode.Name;
			return OperateResult.CreateSuccessResult( );
		}

		public static OperateResult SetOeeRelateBy( IOeeRelateNode oeeRelateNode, OeeNode oeeNode )
		{
			if (
				oeeRelateNode.Length < 0 &&
				oeeRelateNode.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
				oeeRelateNode.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
				oeeRelateNode.DataTypeCode == RegularNodeTypeItem.Int16.Text ||
				oeeRelateNode.DataTypeCode == RegularNodeTypeItem.UInt16.Text ||
				oeeRelateNode.DataTypeCode == RegularNodeTypeItem.Int32.Text ||
				oeeRelateNode.DataTypeCode == RegularNodeTypeItem.UInt32.Text ||
				oeeRelateNode.DataTypeCode == RegularNodeTypeItem.Int64.Text ||
				oeeRelateNode.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
			{
				oeeRelateNode.OeeRelate = oeeNode.Name;
				return OperateResult.CreateSuccessResult( );
			}
			else
				return new OperateResult( $"当前的数据类型不支持配置OEE，必须为整数，不能是数组。" );
		}

		/// <summary>
		/// 转换Image为Icon，当image为null时是否返回null。
		/// </summary>
		/// <param name="image">要转换为图标的Image对象</param>
		/// <exception cref="ArgumentNullException" />
		public static Icon ConvertToIcon( Image image )
		{
			if (image == null) return null;
			using (MemoryStream msImg = new MemoryStream( ), msIco = new MemoryStream( ))
			{
				image.Save( msImg, ImageFormat.Png );

				using (var bin = new BinaryWriter( msIco ))
				{
					//写图标头部
					bin.Write( (short)0 );           //0-1保留
					bin.Write( (short)1 );           //2-3文件类型。1=图标, 2=光标
					bin.Write( (short)1 );           //4-5图像数量（图标可以包含多个图像）

					bin.Write( (byte)image.Width );  //6图标宽度
					bin.Write( (byte)image.Height ); //7图标高度
					bin.Write( (byte)0 );            //8颜色数（若像素位深>=8，填0。这是显然的，达到8bpp的颜色数最少是256，byte不够表示）
					bin.Write( (byte)0 );            //9保留。必须为0
					bin.Write( (short)0 );           //10-11调色板
					bin.Write( (short)32 );          //12-13位深
					bin.Write( (int)msImg.Length );  //14-17位图数据大小
					bin.Write( 22 );                 //18-21位图数据起始字节

					//写图像数据
					bin.Write( msImg.ToArray( ) );

					bin.Flush( );
					bin.Seek( 0, SeekOrigin.Begin );
					return new Icon( msIco );
				}
			}
		}

		/// <summary>
		/// 判断当前的节点是否是网关设备节点
		/// </summary>
		/// <param name="treeNode">树节点</param>
		/// <returns>是否网关节点</returns>
		public static bool IsServerLocalNode( this TreeNode treeNode )
		{
			return treeNode.ImageKey == "server_Local_16xLG" || treeNode.ImageKey == "server_Local_16xLG_Green";
		}

		#endregion
	}
}
