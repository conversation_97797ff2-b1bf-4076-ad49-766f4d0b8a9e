using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node;

namespace HslTechnology.EdgeViewer
{
	/// <summary>
	/// 设备的图形列表信息
	/// </summary>
	public class DeviceImageList
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public DeviceImageList( )
		{
			imagelist = new ImageList( );
			imagelist.ColorDepth = ColorDepth.Depth24Bit;
			imagelist.ImageSize = new Size( 16, 16 );
			images = new List<Image>( );
		}

		public void AddImage( string key, Image image )
		{
			if (imagelist.Images.Keys.Contains( key ))
			{
				imagelist.Images.RemoveByKey( key );
			}
			imagelist.Images.Add( key, image );
			images.Add( image );
		}

		public void AddImage( DeviceType deviceType, Image image )
		{
			AddImage( deviceType.ToString( ), image );
		}

		public ImageList ImageList => imagelist;

		public string GetKey( string key, Image image )
		{
			if (imagelist.Images.Keys.Contains( key ))
			{
				return key;
			}
			else
			{
				imagelist.Images.Add( key, image );
				return key;
			}
		}

		public string GetKey( DeviceType deviceType, Image image )
		{
			return GetKey( deviceType.ToString( ), image );
		}

		public Image GetImage( string key )
		{
			if (imagelist.Images.Keys.Contains( key ))
			{
				return imagelist.Images[key];
			}
			return null;
		}

		private ImageList imagelist;
		private List<Image> images;
	}
}
