using HslTechnology.Edge;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer
{
	public class GroupNodePropertyGrid : PropertyGrid
	{
		public GroupNodePropertyGrid( )
		{

		}

		public void SetSelectedObject( object obj )
		{
			this.SelectedObject = obj;

			//this.propertySort = this.PropertySort;
			this.PropertySort = PropertySort.CategorizedAlphabetical;
			this.Paint += PropertyGrid1_Paint;
		}


		private int GetIndexOf( List<string> categorys, string value )
		{
			if (value == "DTU信息") return 10000;
			if (value == "额外功能") return 10001;
			if (categorys.Contains( value )) return categorys.IndexOf( value );
			return categorys.Count;
		}

		private void PropertyGrid1_Paint( object sender, PaintEventArgs e )
		{
			this.CollapseAllGridItems( );
			GridItemCollection currentPropEntries = typeof( PropertyGrid ).GetField( "currentPropEntries",
				BindingFlags.NonPublic | BindingFlags.Instance ).GetValue( this ) as GridItemCollection;
			var newarray = currentPropEntries.Cast<GridItem>( ).OrderBy( ( t ) => GetIndexOf( HslTechnologyHelper.GroupNodeCategorys, t.Label ) ).ToArray( );
			currentPropEntries.GetType( ).GetField( "entries", BindingFlags.NonPublic | BindingFlags.Instance ).SetValue( currentPropEntries, newarray );

			this.ExpandAllGridItems( );
			//this.PropertySort = this.propertySort;
			this.Paint -= PropertyGrid1_Paint;
		}


		//private PropertySort propertySort = PropertySort.CategorizedAlphabetical;
		

	}
}
