using HslTechnology.Edge.Config;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication.LogNet;
using WeifenLuo.WinFormsUI.Docking;
using HslCommunication.MQTT;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.EdgeViewer.Core;
using HslTechnology.Edge.DataBusiness.Time;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormDeviceEdge : HslPage
	{
		public FormDeviceEdge( ILogNet logNet, EdgeServerSettings serverSettings, float[] ramUseHistory, float[] threadUseHistroy )
		{
			InitializeComponent( );
			this.serverSettings = serverSettings;
			this.log = logNet;
			this.ramUseHistory = ramUseHistory;
			this.threadUseHistroy = threadUseHistroy;
			if (this.serverSettings != null)
				this.client = this.serverSettings.GetMqttSyncClient( true );
		}

		private void FormDeviceEdge_Load( object sender, EventArgs e )
		{
			edgeInfoControl1.RenderRamUseHistory( this.ramUseHistory, this.threadUseHistroy );
			timer = new Timer( );
			timer.Interval = 1000;
			timer.Tick += Timer_Tick;
			FormClosing += FormDeviceEdge_FormClosing;
			timer.Start( );
			if (this.serverSettings != null)
			{
				Text = this.serverSettings.GetEdgeDisplayName( );
			}


			if (Util.ViewerSettings.Theme == "Dark")
			{
				BackColor = Util.Theme.ColorPalette.TabUnselected.Background;
				ForeColor = Util.Theme.ColorPalette.TabUnselected.Text;
				edgeInfoControl1.BackColor = Util.Theme.ColorPalette.TabUnselected.Background;
				edgeInfoControl1.ForeColor = Util.Theme.ColorPalette.TabUnselected.Text;
			}
		}

		private async void FormDeviceEdge_FormClosing( object sender, FormClosingEventArgs e )
		{
			serverSettings = null;
			await client.ConnectCloseAsync( );
			timer.Dispose( );
		}

		private async void Timer_Tick( object sender, EventArgs e )
		{
			if (serverSettings != null)
			{
				if (readings) return;
				readings = true;
				var readStatus = await client.ReadRpcAsync<JObject>( "Edge/DeviceData", new { data = "__status" } );
				if (!readStatus.IsSuccess)
				{
					label2.Visible = true;
					if (label2.ForeColor == Color.Red)
						label2.ForeColor = Color.Transparent;
					else
						label2.ForeColor = Color.Red;
					label2.Text = "当前服务器数据读取失败！";
				}
				else
				{
					label2.Visible = false;
					edgeInfoControl1.RenderMachineData( readStatus.Content );
				}

				// 请求运行记录
				var readTimes = await client.ReadRpcAsync<EdgeTimeConsume[]>( "Edge/GetEdgeRuntimeHistory", new { } );
				if (readTimes.IsSuccess)
				{
					edgeInfoControl1.RenderEdgeTime( readTimes.Content );
				}

				// 请求异常信息
				if (tickRequest == 0)
				{
					var message = await client.ReadRpcAsync<string>( "Edge/GetEdgeUnhandledException", new { } );
					if (message.IsSuccess)
					{
						edgeInfoControl1.RenderMessage( message.Content );
					}

					message = await client.ReadRpcAsync<string>( "Edge/GetEdgeInitializeTxt", new { } );
					if (message.IsSuccess)
					{
						edgeInfoControl1.RenderIniLog( message.Content );
					}

					var exceptions = await client.ReadRpcAsync<string[]>( "Edge/GetEdgeRuntimeExceptions", new { } );
					if (exceptions.IsSuccess)
					{
						edgeInfoControl1.RenderExceptions( exceptions.Content );
					}
				}


				tickRequest++;
				readings = false;
			}
		}

		private bool readings = false;
		private int tickRequest = 0;
		private EdgeServerSettings serverSettings;
		private MqttSyncClient client;
		private Timer timer;
		private ILogNet log;
		private float[] ramUseHistory;
		private float[] threadUseHistroy;
	}
}
