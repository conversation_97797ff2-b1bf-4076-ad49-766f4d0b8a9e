using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Config;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.EdgeViewer.Core;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormAlarmList : HslPage
	{
		public FormAlarmList( ILogNet logNet, EdgeServerSettings serverSettings )
		{
			InitializeComponent( );
			this.Load             += FormAlarmList_Load;
			this.FormClosing      += FormAlarmList_FormClosing;
			this.serverSettings   = serverSettings;
			this.log              = logNet;
			if (this.serverSettings != null)
			{
				this.renderPath  = new TreeNodePath( this.serverSettings.RenderPath );
				this.client      = this.serverSettings.GetMqttSyncClient( true );
			}
		}

		private void FormAlarmList_Load( object sender, EventArgs e )
		{
			timer = new Timer( );
			timer.Interval = 1000;
			timer.Tick += Timer_Tick;
			timer.Start( );
			if (this.serverSettings != null)
			{
				Text = this.serverSettings.GetDisplayText( );
			}
		}

		private void FormAlarmList_FormClosing( object sender, FormClosingEventArgs e )
		{
			client.ConnectClose( );
			timer.Dispose( );
		}

		private async void Timer_Tick( object sender, EventArgs e )
		{
			if (serverSettings != null)
			{
				var readAlarms = await client.ReadRpcAsync<JArray>( "Business/Alarm/GetAlarms", new { data = renderPath.GetActualPath( ) } );
				if (readAlarms.IsSuccess)
				{
					// UNDONE: 这里应该显示别名呢？还是原始的路径呢？
					this.deviceAlarmControl1.RenderDevicePath( renderPath.GetDisplayPath( ) );
					this.deviceAlarmControl1.RenderAlarm( readAlarms.Content );
				}
				else
				{
					timer.Enabled = false;
				}
			}
		}


		private EdgeServerSettings serverSettings;
		private MqttSyncClient client;
		private Timer timer;
		private ILogNet log;
		private TreeNodePath renderPath = null;

		private void FormAlarmList_Load_1( object sender, EventArgs e )
		{

		}
	}
}
