using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI.Docking;
using System.IO;
using Newtonsoft.Json.Linq;
using HslTechnology.Edge.Config;
using HslCommunication.MQTT;
using System.Xml.Linq;
using HslTechnology.EdgeViewer.Pages;
using System.Threading;
using HslCommunication.LogNet;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Render;
using HslTechnology.EdgeViewer.Core;
using HslCommunication;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Plugins;
using HslTechnology.Edge.Node;
using HslTechnology.Edge;
using System.Runtime.Remoting.Contexts;
using HslTechnology.Edge.Reflection;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormEdgeServers : DockContent
	{
		#region Contructor

		/// <inheritdoc cref="HslTechnology.Edge.Node.GroupNode.GroupNode( )"/>
		public FormEdgeServers( ILogNet log )
		{
			InitializeComponent( );
			this.logNet = log;
			this.Icon = Util.GetWinformIcon( );
			this.serverListFilePath = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "serverList.txt" );

			treeViewEx1.NodeMouseClick                  += TreeViewEx1_NodeMouseClick;
			treeViewEx1.NodeMouseDoubleClick            += TreeViewEx1_NodeMouseDoubleClick;
			editEdgeSettingToolStripMenuItem.Click      += editEdgeParameterToolStripMenuItem_Click;
			editDevicesToolStripMenuItem.Click          += editCaptureInfoToolStripMenuItem_Click;
			deleteDevicesToolStripMenuItem.Click        += deleteEdgeToolStripMenuItem_Click;
			restartEdgeToolStripMenuItem.Click          += restartToolStripMenuItem_Click;
			pluginsToolStripMenuItem.Click              += PluginsToolStripMenuItem_Click;
			viewLogToolStripMenuItem.Click              += 查看日志ToolStripMenuItem_Click;
			updateEdgeToolStripMenuItem.Click           += 更新程序ToolStripMenuItem_Click;
			addServerToolStripMenuItem.Click            += Button_add_server_Click;
			edgeInfoToolStripMenuItem.Click             += EdgeInfoToolStripMenuItem_Click;
			shutDownToolStripMenuItem.Click             += ShutDownToolStripMenuItem_Click;
			editConnectToolStripMenuItem.Click          += EditEdgeSettingToolStripMenuItem_Click;
			reloadServerToolStripMenuItem.Click         += ReloadServerToolStripMenuItem_Click;

			treeViewEx1.BeforeExpand                    += TreeViewEx1_BeforeExpand;
			allDeviceStopRequestToolStripMenuItem.Click += AllDeviceStopRequestToolStripMenuItem_Click;
			allDeviceContinueRequestToolStripMenuItem.Click += AllDeviceContinueRequestToolStripMenuItem_Click;
		}

		#endregion

		#region Form Load Show

		private void FormEdgeServers_Load( object sender, EventArgs e )
		{
			treeViewEx1.ImageList                        = Util.GroupNodeImages.DeviceImageList.ImageList;
			CloseButtonVisible                           = false;
			Text                                         = "服务器列表";

			LoadServerListFromTxt( );
			this.FormMain?.ShowEdgeListAndSelected( GetEdgeServerSettings( ), null );

			if (Util.ViewerSettings.Theme == "Dark")
			{
				BackColor = Util.Theme.ColorPalette.TabUnselected.Background;
				ForeColor = Util.Theme.ColorPalette.TabUnselected.Text;
				treeViewEx1.BackColor = Util.Theme.ColorPalette.TabUnselected.Background;
				treeViewEx1.ForeColor = Util.Theme.ColorPalette.TabUnselected.Text;
			}
			//if(Util.ViewerSettings.Theme== "Dark")
			//{
			//	treeViewEx1.BackColor = Color.Gray;
			//}

			UpdateEdgeConnectStatus( );
		}

		private void EditEdgeSettingToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;

			if (treeNode.Tag is EdgeServerSettings serverSettings)
			{
				using (FormServerAdd form = new FormServerAdd( serverSettings ))
				{
					if (form.ShowDialog( ) == DialogResult.OK)
					{
						serverSettings = form.EdgeServerSettings;
						treeNode.Text = serverSettings.GetDisplayText( );
						treeNode.Tag = serverSettings;

						SaveServerListToTxt( );
						this.FormMain?.ShowEdgeListAndSelected( GetEdgeServerSettings( ), this.deviceMonitor?.ServerSettings );
					}
				}
			}
		}

		private void ReloadServerToolStripMenuItem_Click( object sender, EventArgs e )
		{
			UpdateEdgeConnectStatus( );
		}

		private async void TreeViewEx1_BeforeExpand( object sender, TreeViewCancelEventArgs e )
		{
			if (e.Node.Tag is DeviceNode deviceNode)
			{
				if (e.Node.Nodes.Count == 1 && string.IsNullOrEmpty( e.Node.Nodes[0].Text ))
				{
					e.Node.Nodes[0].Text = "正在加载...";
					e.Node.Nodes[0].SetImageKey( "action_create_16xLG" );
					// 获取根节点，展开相关的数据节点
					TreeNode root = GetRootTreeNode( e.Node );
					if (root == null) return;
					if(root.Tag is EdgeServerSettings serverSettings)
					{
						MqttSyncClient rpc = serverSettings.GetMqttSyncClient( );

						var read = await rpc.ReadRpcAsync<ScalarDataNode[]>( "Edge/BrowseDeviceDataNodes", new { data = new TreeNodePath( e.Node ).GetActualPath( ) } );
						e.Node.Nodes.Clear( );
						if (!read.IsSuccess) { MessageBox.Show( "Failed:" + read.Message ); return; }

						for (int i = 0; i < read.Content.Length; i++)
						{
							ScalarDataNode scalarDataNode = read.Content[i];
							TreeNode treeNode = new TreeNode( scalarDataNode.GetDisplayName( ) );
							if(scalarDataNode.DataDimension == Edge.Node.DataDimension.One)
							{
								treeNode.SetImageKey( "brackets_Square_16xMD" );
								if(scalarDataNode.DataType == Edge.Node.DataType.Struct)
								{
									// 追加结构体列表的子节点信息
									for (int j = 0; j < scalarDataNode.ArrayLength; j++)
									{
										TreeNode structNode = new TreeNode( scalarDataNode.Name + $"[{j}]" );
										structNode.SetImageKey( "brackets_Curly_16xMD" );
										structNode.Tag = scalarDataNode;
										treeNode.Nodes.Add( structNode );
									}
								}
							}
							else if (scalarDataNode.DataDimension == Edge.Node.DataDimension.Two)
								treeNode.SetImageKey( "Module_648" );
							else
							{
								if(scalarDataNode.DataType == Edge.Node.DataType.Struct)
									treeNode.SetImageKey( "brackets_Curly_16xMD" );
								else if (scalarDataNode.DataType == Edge.Node.DataType.Method)
									treeNode.SetImageKey( "Method_636" );
								else
									treeNode.SetImageKey( "Enum_582" );
							}

							treeNode.Tag = scalarDataNode;
							e.Node.Nodes.Add( treeNode );
						}
					}
				}
			}
		}

		private TreeNode GetRootTreeNode( TreeNode treeNode )
		{
			while(treeNode.Parent != null)
			{
				treeNode = treeNode.Parent;
			}
			return treeNode;
		}

		protected override void OnClosing( CancelEventArgs e )
		{
			// 当关闭的时候，不进行销毁窗体，只是将其隐藏
			base.OnClosing( e );
			this.Hide( );
			e.Cancel = true;
		}

		#endregion

		#region Menu Click

		private void Button_add_server_Click( object sender, EventArgs e )
		{
			using (FormServerAdd form = new FormServerAdd( ))
			{
				if (form.ShowDialog( ) == DialogResult.OK)
				{
					EdgeServerSettings serverSettings = form.EdgeServerSettings;
					TreeNode treeNode = new TreeNode( serverSettings.GetDisplayText( ) );
					treeNode.SetImageKey( "server_Local_16xLG" );
					treeNode.Tag = serverSettings;
					treeViewEx1.Nodes.Add( treeNode );

					SaveServerListToTxt( );
					this.FormMain?.ShowEdgeListAndSelected( GetEdgeServerSettings( ), this.deviceMonitor?.ServerSettings );

					// 更新当前的网关连接状态
					UpdateEdgeConnectStatus( treeNode );
				}
			}
		}

		private void 更新程序ToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;

			if (treeNode.Tag is EdgeServerSettings serverSettings)
			{
				using (FormUpdateRemote form = new FormUpdateRemote( serverSettings ))
					form.ShowDialog( );
			}
		}

		public void restartToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 重启网关
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;
			if (treeNode.Tag is EdgeServerSettings serverSettings)
				EdgeServerHelper.RestartEdgeServer( serverSettings );
		}

		public async void ShutDownToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 关闭网关
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;
			if (treeNode.Tag is EdgeServerSettings serverSettings)
				await EdgeServerHelper.ShutDownEdgeServer( serverSettings );
		}

		private async void EdgeInfoToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 查看网关的基本信息，包括RAM使用
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;

			if (treeNode.Tag is EdgeServerSettings serverSettings)
			{
				MqttSyncClient client = serverSettings.GetMqttSyncClient( );
				OperateResult<float[]> read = await client.ReadRpcAsync<float[]>( "Edge/GetRamUseHistoryData", null );
				if (!read.IsSuccess)
				{
					MessageBox.Show( "向服务器请求初始化数据失败！原因：" + read.Message );
					return;
				}
				OperateResult<float[]> readThread = await client.ReadRpcAsync<float[]>( "Edge/GetThreadUseHistoryData", null );

				FormDeviceEdge form = new FormDeviceEdge( this.logNet, serverSettings, read.Content, readThread.Content );
				form.Show( dockPanelMain );
			}
		}

		private async void PluginsToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 插件管理的功能
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;

			if (treeNode.Tag is EdgeServerSettings serverSettings)
			{
				MqttSyncClient client = serverSettings.GetMqttSyncClient( );
				OperateResult<PluginsDefinition[]> readPlugins = await client.ReadRpcAsync<PluginsDefinition[]>( "Plugins/GetRegisterPlugins", new { } );
				if (readPlugins.IsSuccess) { serverSettings.NodeImages.AddImageItem( readPlugins.Content ); serverSettings.Plugins = readPlugins.Content; }

				if (!readPlugins.IsSuccess)
				{
					MessageBox.Show( "向服务器请求数据失败！原因：" + readPlugins.Message );
					return;
				}

				FormPluginsManagement form = new FormPluginsManagement( this.logNet, serverSettings );
				form.Show( dockPanelMain );
				form.SetPlugins( readPlugins.Content );
			}
		}

		private async void editCaptureInfoToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 编辑采集信息的按钮
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;

			if (treeNode.Tag is EdgeServerSettings serverSettings)
			{
				MqttSyncClient rpc = serverSettings.GetMqttSyncClient( );
				OperateResult<string> read = await rpc.ReadRpcAsync<string>( "Admin/XmlSettingsRequest", null );
				if (!read.IsSuccess)
				{
					MessageBox.Show( "向服务器请求数据失败！原因：" + read.Message );
					return;
				}

				// 请求下插件信息及图标，方便显示
				OperateResult<PluginsDefinition[]> readPlugins = await rpc.ReadRpcAsync<PluginsDefinition[]>( "Plugins/GetRegisterPlugins", new { } );
				if (readPlugins.IsSuccess) { serverSettings.NodeImages.AddImageItem( readPlugins.Content ); serverSettings.Plugins = readPlugins.Content; }

				// 请求下载网关里所有插件设备的节点定义信息
				if (readPlugins.Content?.Length > 0)
				{
					OperateResult<Dictionary<string, NodePropertyConfig[]>> readPluginsDevice = await rpc.ReadRpcAsync<Dictionary<string, NodePropertyConfig[]>>(
						"Plugins/GetAllPluginsPropertyConfigs", new { } );
					if (readPluginsDevice.IsSuccess) { serverSettings.PluginsDefinition = readPluginsDevice.Content; }
				}
				// 请求下载网关的一些参数信息
				OperateResult<JObject> readEdgeInfo = await rpc.ReadRpcAsync<JObject>( "Admin/ServerInfos", new { } );
				if (readEdgeInfo.IsSuccess)
				{
					// 当前支持最大的设备数量
					serverSettings.MaxDevicesCount = HslCommunication.BasicFramework.SoftBasic.GetValueFromJsonObject( readEdgeInfo.Content, "MaxDeviceCount", serverSettings.MaxDevicesCount );
				}

				// 初始化下串口的资源
				OperateResult<string[]> readSerials = rpc.ReadRpc<string[]>( "Edge/DeviceSerialPorts", new { needMapping = true } );
				if (readSerials.IsSuccess) SerialPortConverter.StandardValues = readSerials.Content;

				FormNodeSettings form = new FormNodeSettings( this.logNet, serverSettings, XElement.Parse( read.Content ) );
				form.Show( dockPanelMain );
			}
		}

		private void deleteEdgeToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 删除网关的设备信息
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;

			if (treeNode.Tag is EdgeServerSettings serverSettings)
			{
				treeViewEx1.Nodes.Remove( treeNode );

				SaveServerListToTxt( );
			}
		}

		private void editEdgeParameterToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 编辑网关的参数信息
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;

			if (treeNode.Tag is EdgeServerSettings serverSettings)
			{
				using (FormServerSettings form = new FormServerSettings( serverSettings ))
				{
					if (form.ShowDialog( ) == DialogResult.OK)
					{
						treeNode.Text = form.EdgeServerSettings.Alias;
					}

					if (form.IsUpdateServerInfo)
					{
						// 服务器有更新的话，重新更新文件信息
						SaveServerListToTxt( );
					}
				}
			}
		}

		private void 查看日志ToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode treeNode = treeViewEx1.SelectedNode;
			if (treeNode == null) return;

			if (treeNode.Tag is EdgeServerSettings serverSettings)
			{
				FormLogView form = new FormLogView( serverSettings );
				form.Text += "-" + serverSettings.Alias;
				form.Show( dockPanelMain, DockState.DockBottom );
			}
		}

		#endregion

		#region TreeView Click

		private async void TreeViewEx1_NodeMouseClick( object sender, TreeNodeMouseClickEventArgs e )
		{
			if (e.Node == null) return;
			treeViewEx1.SelectedNode = e.Node;
			TreeNode selectedNode = treeViewEx1.SelectedNode;
			if (selectedNode == null) return;

			if (e.Button == MouseButtons.Right)
			{
				// 针对边缘网关的节点，可以右键菜单操作
				if (selectedNode.Tag is EdgeServerSettings serverSettings)
					contextMenuStrip1.Show( treeViewEx1, e.Location );
				else
				{
					if (selectedNode.Tag is GroupNode groupNode)
					{
						if (groupNode.GetType( ) == typeof( GroupNode ) || groupNode.GetType( ) == typeof( DeviceNode ))
						{
							contextMenuStrip_path.Show( treeViewEx1, e.Location );
						}
					}
				}
			}
			else if (e.Button == MouseButtons.Left)
			{
				TreeNode edge = FindEdgeNode( selectedNode );
				if (edge == null) return;
				if (edge.Tag is EdgeServerSettings serverSettings2)
				{
					if (!selectedNode.IsServerLocalNode( ) && !object.ReferenceEquals( deviceMonitor.ServerSettings, serverSettings2 ))
					{
						MessageBox.Show( "当前无法切换查看数据，需要先双击另一个网关节点查看数据！" );
						return;
					}

					if (selectedNode.IsServerLocalNode( ) && !object.ReferenceEquals( deviceMonitor.ServerSettings, serverSettings2 ))
					{
						return;
					}

					// 这里的逻辑是当哪个设备点击了，就在主监控界面切换监视的设备数据
					if (selectedNode.ImageKey == "Class_489")
					{
						// 单机了分类节点信息
						deviceMonitor.SetServerSettings( serverSettings2, new TreeNodePath( selectedNode ) );
					}
					else if (selectedNode.IsServerLocalNode( ))
					{
						// 点击了网关，如果网关在显示中，则显示其路径设备信息
						if (deviceMonitor == null) return;
						if (deviceMonitor.ServerSettings == null) return;
						deviceMonitor.SetServerSettings( serverSettings2, new TreeNodePath( ) );
					}
					else if (selectedNode.Tag is ScalarDataNode scalarDataNode)
					{
						TreeNode deviceTreeNode = FindDeviceNode( selectedNode );
						if (deviceTreeNode == null) return;                              // 找不到设备信息

						TreeNodePath treeNodePath = new TreeNodePath( deviceTreeNode.Parent );
						// 点击了数据内容，分为两种情况，父节点是设备节点，父节点是结构体数据
						if (selectedNode.Parent.Tag is DeviceNode)
						{
							if (!deviceMonitor.CheckRenderPathSame( serverSettings2, treeNodePath ))
								deviceMonitor.SetServerSettings( serverSettings2, treeNodePath );

							await deviceMonitor.ChangeSelectNode( new TreeNodePath( deviceTreeNode ).GetActualPath( ), scalarDataNode );
						}
						else
						{
							// 点击了结构体列表的子结构体
							int index = selectedNode.Index;
							await deviceMonitor.ChangeSelectNode( new TreeNodePath( deviceTreeNode ).GetActualPath( ), scalarDataNode, index );
						}
					}
					else if (selectedNode.Tag is DeviceNode deviceNode)
					{
						// 检测当前的路径是否和主界面展示的路径一致，不一致则先切换路径显示操作。
						if (selectedNode.Parent.ImageKey == "Class_489" || selectedNode.Parent.IsServerLocalNode( ))
							if (!deviceMonitor.CheckRenderPathSame( serverSettings2, new TreeNodePath( selectedNode.Parent ) ))
								deviceMonitor.SetServerSettings( serverSettings2, new TreeNodePath( selectedNode.Parent ) );

						await deviceMonitor.ChangeSelectNode( new TreeNodePath( selectedNode ).GetActualPath( ), null );
					}
				}
			}
		}

		private void AllDeviceStopRequestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode selectedNode = treeViewEx1.SelectedNode;
			if (selectedNode == null) return;

			TreeNode edge = FindEdgeNode( selectedNode );
			if (edge == null) return;
			if (edge.Tag is EdgeServerSettings serverSettings2)
			{
				if (!selectedNode.IsServerLocalNode( ) && !object.ReferenceEquals( deviceMonitor.ServerSettings, serverSettings2 ))
				{
					MessageBox.Show( "当前操作的网关未选中状态，需要先双击另一个网关选中操作！" );
					return;
				}

				if (selectedNode.Tag is GroupNode groupNode)
				{
					string path = GetNodePath( selectedNode );
					if (groupNode.IsGroup( )) path = path + "/";
					// 通知服务器，暂停设备信息
					MqttSyncClient rpc = serverSettings2.GetMqttSyncClient( );
					OperateResult<string> read = rpc.ReadRpc<string>( "Edge/DeviceStopRequest", new { data = path } );
					if (read.IsSuccess)
					{
						MessageBox.Show( "暂停设备成功！" );
					}
					else
					{
						MessageBox.Show( "暂停设备失败: " + read.Message );
					}
				}
			}
		}

		private void AllDeviceContinueRequestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode selectedNode = treeViewEx1.SelectedNode;
			if (selectedNode == null) return;

			TreeNode edge = FindEdgeNode( selectedNode );
			if (edge == null) return;
			if (edge.Tag is EdgeServerSettings serverSettings2)
			{
				if (!selectedNode.IsServerLocalNode( ) && !object.ReferenceEquals( deviceMonitor.ServerSettings, serverSettings2 ))
				{
					MessageBox.Show( "当前操作的网关未选中状态，需要先双击另一个网关选中操作！" );
					return;
				}

				if (selectedNode.Tag is GroupNode groupNode)
				{
					string path = GetNodePath( selectedNode );
					if (groupNode.IsGroup( )) path = path + "/";
					// 通知服务器，暂停设备信息
					MqttSyncClient rpc = serverSettings2.GetMqttSyncClient( );
					OperateResult<string> read = rpc.ReadRpc<string>( "Edge/DeviceContinueRequest", new { data = path } );
					if (read.IsSuccess)
					{
						MessageBox.Show( "设备继续成功！" );
					}
					else
					{
						MessageBox.Show( "设备继续失败: " + read.Message );
					}
				}
			}
		}

		private string GetNodePath( TreeNode node )
		{
			if (node == null) return string.Empty;
			if (node.IsServerLocalNode( )) return string.Empty;

			string nodeText = string.Empty;
			if (node.Tag is GroupNode groupNode)
			{
				nodeText = groupNode.Name;
			}
			return GetNodePath( node.Parent ) + "/" + nodeText;
		}

		private int isRenderEdgeInfo = 0;

		private async void TreeViewEx1_NodeMouseDoubleClick( object sender, TreeNodeMouseClickEventArgs e )
		{
			// 双击了树形菜单，切换了菜单的选择
			treeViewEx1.SelectedNode = e.Node;
			TreeNode clickTreeNode = treeViewEx1.SelectedNode;
			if (clickTreeNode == null) return;

			// 双击了网关
			if (clickTreeNode.Tag is EdgeServerSettings serverSettings)
			{
				if (deviceMonitor == null) return;
				// 将监视界面显示出来
				deviceMonitor.Show( dockPanelMain );

				if (Interlocked.CompareExchange( ref isRenderEdgeInfo, 1, 0 ) == 0)
				{
					deviceMonitor.DataGridSpecifyRowCount( 0 );

					if (await RenderServerInfo( clickTreeNode, serverSettings ))
					{
						deviceMonitor.SetServerSettings( serverSettings );
						treeViewEx1.SelectedNode.Expand( );
						clickTreeNode.SetImageKey( "server_Local_16xLG_Green" );
					}
					else
					{
						clickTreeNode.SetImageKey( "server_Local_16xLG" );
					}

					// 在主窗体显示列表及更新选择
					this.FormMain?.ShowEdgeListAndSelected( GetEdgeServerSettings( ), serverSettings );
					Interlocked.Exchange( ref isRenderEdgeInfo, 0 );
				}
			}
		}

		private TreeNode FindEdgeNode( TreeNode node )
		{
			while (true)
			{
				if (node.IsServerLocalNode( )) return node;
				node = node.Parent;
				if (node == null) return node;
			}
		}

		private TreeNode FindDeviceNode( TreeNode node )
		{
			while (true)
			{
				if (node.IsServerLocalNode( )) return null;
				if (node.Tag is DeviceNode ) return node;
				node = node.Parent;
				if (node == null) return null;
			}
		}

		#endregion

		#region ServerRender

		public async Task<bool> RenderServerInfo( TreeNode treeNode, EdgeServerSettings serverSettings )
		{
			MqttSyncClient rpc = serverSettings.GetMqttSyncClient( );

			// 先请求显示的文档信息
			var readXml = await rpc.ReadRpcAsync<string>( "Edge/XmlDeviceNodes", "" );
			if (!readXml.IsSuccess) { MessageBox.Show( "Failed:" + readXml.Message ); return false; }

			// 请求下插件信息及图标，方便显示
			OperateResult<PluginsDefinition[]> readPlugins = await rpc.ReadRpcAsync<PluginsDefinition[]>( "Plugins/GetRegisterPlugins", new { } );
			if (readPlugins.IsSuccess) { serverSettings.NodeImages.AddImageItem( readPlugins.Content ); }

			// 添加设备信息
			XElement element = XElement.Parse( readXml.Content );
			treeNode.Nodes.Clear( );
			treeViewEx1.ImageList = serverSettings.NodeImages.DeviceImageList.ImageList;                             // 更新树形控件的图标信息
			Util.RenderEdgeServerTreeNodeBrowseNode( serverSettings, treeNode, element.Elements( ).ToArray( )[0] );  // 展开显示
			treeNode.Expand( );

			var readStatus = await rpc.ReadRpcAsync<JObject>( "Edge/DeviceData", new { data = "__status" } );        // 获取网关的基础数据
			if (!readStatus.IsSuccess) { MessageBox.Show( "Failed:" + readStatus.Message ); return false; }

			// toolStripStatusLabel1.Text = "连接设备成功！";
			// if (string.IsNullOrEmpty( readStatus.Content ) || readStatus.Content == "{}") return false;

			try
			{
				JObject statusJson = readStatus.Content;                                 // JObject.Parse( readStatus.Content );
				if (serverSettings.EdgeID != statusJson["__name"].Value<string>( ))      // 如果网关名称发生了修复，则使用新的名称显示
				{
					serverSettings.EdgeID = statusJson["__name"].Value<string>( );
					treeNode.Text = serverSettings.GetDisplayText( );
					treeViewEx1.Refresh( );
					SaveServerListToTxt( );
				}

				deviceMonitor?.RenderDevices( serverSettings, statusJson );
				return true;
			}
			catch (Exception ex)
			{
				HslCommunication.BasicFramework.SoftBasic.ShowExceptionMessage( "原数据：" + readStatus.Content + 
					Environment.NewLine + "错误消息：" , ex );
				return false;
			}
		}

		#endregion

		#region Server List Save Load

		// 本区域的代码是用来存储本地的服务器列表的代码的
		private string serverListFilePath = "serverList.txt";

		private List<EdgeServerSettings> GetEdgeServerSettings( )
		{
			List<EdgeServerSettings> serverSettings = new List<EdgeServerSettings>( );
			foreach (TreeNode node in treeViewEx1.Nodes)
			{
				if (node.Tag is EdgeServerSettings settings)
				{
					serverSettings.Add( settings );
				}
			}
			return serverSettings;
		}

		private void SaveServerListToTxt( )
		{
			List<EdgeServerSettings> serverSettings = GetEdgeServerSettings( );
			File.WriteAllBytes( serverListFilePath, Encoding.Default.GetBytes( JArray.FromObject( serverSettings ).ToString( ) ) );
		}

		private void LoadServerListFromTxt( )
		{
			if (File.Exists( serverListFilePath ))
			{
				try
				{
					string content = Encoding.Default.GetString( File.ReadAllBytes( serverListFilePath ) );
					List<EdgeServerSettings> serverSettings = JArray.Parse( content ).ToObject<List<EdgeServerSettings>>( );

					foreach (var item in serverSettings)
					{
						TreeNode treeNode          = new TreeNode( item.GetDisplayText( ) );
						treeNode.Tag               = item;
						treeNode.SetImageKey( "server_Local_16xLG" );
						treeViewEx1.Nodes.Add( treeNode );
					}
				}
				catch (Exception ex)
				{
					MessageBox.Show( "Fatal:" + ex.Message );
				}
			}
		}

		private void UpdateEdgeConnectStatus( TreeNode treeNode )
		{
			ThreadPool.QueueUserWorkItem( new WaitCallback( m =>
			{
				if (treeNode.Tag is EdgeServerSettings edgeServerSettings)
				{
					MqttSyncClient client = edgeServerSettings.GetMqttSyncClient( true );
					client.ConnectTimeOut = 2000;
					OperateResult connect = client.ConnectServer( );
					try
					{
						Invoke( new Action( ( ) =>
						{
							if (connect.IsSuccess)
							{
								treeNode.SetImageKey( "server_Local_16xLG_Green" );
							}
							else
							{
								treeNode.SetImageKey( "server_Local_16xLG" );
							}
						} ) );
					}
					catch
					{

					}
					client.ConnectClose( );
				}
			} ), null );
		}

		/// <summary>
		/// 更新当前网关客户端里所有的树节点在线状态信息
		/// </summary>
		private void UpdateEdgeConnectStatus( )
		{
			for (int i = 0; i < treeViewEx1.Nodes.Count; i++)
			{
				TreeNode treeNode = treeViewEx1.Nodes[i];
				UpdateEdgeConnectStatus( treeNode );
			}
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 获取或设置当前界面绑定的主
		/// </summary>
		public DockPanel DockPanelMain { get => this.dockPanelMain; set => this.dockPanelMain = value; }

		/// <summary>
		/// 获取或设置当前界面绑定的监视设备数据的界面
		/// </summary>
		public FormDeviceMonitor FormDeviceMonitor { get => this.deviceMonitor; set => this.deviceMonitor = value; }

		/// <summary>
		/// 获取或设置当前的列表窗体绑定的主窗体信息
		/// </summary>
		public FormMain FormMain { get => this.formMain; set => this.formMain = value;}

		#endregion

		#region Private Member

		// 本界面需要和另外的两个界面做交互，一个是 dockpanel 一个是 FormDeviceMonitor
		private DockPanel dockPanelMain;                    // 本窗体依附的容器控件
		private FormDeviceMonitor deviceMonitor;            // 本窗体绑定的显示设备状态的控件
		private ILogNet logNet;                             // 客户端的消息提示
		private FormMain formMain;                          // 主窗体信息

		#endregion
	}
}
