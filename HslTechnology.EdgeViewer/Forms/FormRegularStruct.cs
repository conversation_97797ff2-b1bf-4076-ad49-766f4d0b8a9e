using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node.Regular;
using HslCommunication;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormRegularStruct : HslForm
	{
		#region Constructor

		public FormRegularStruct( RegularStructNode structNode, string[] structNodes )
		{
			InitializeComponent( );

			this.RegularStructNode = structNode ?? new RegularStructNode( );
			this.Icon = Util.ConvertToIcon( Util.GetImageByGroupNode( null, this.RegularStructNode ) );
			this.structNodes = structNodes;
		}

		#endregion

		#region Form Load
		
		private void FormRegularItemNode_Load( object sender, EventArgs e )
		{
			comboBox_struct.DataSource = this.structNodes;

			checkBox_isArray.CheckedChanged += CheckBox_isArray_CheckedChanged;
			label5.Visible = false;
			textBox_length.Visible = false;

			if (RegularStructNode != null)
			{
				textBox1.Text = RegularStructNode.Name;
				textBox2.Text = RegularStructNode.Description;
				textBox3.Text = RegularStructNode.StructIndex.ToString( );
				comboBox_struct.SelectedItem = RegularStructNode.StructName;
				textBox_length.Text = RegularStructNode.ArrayLength.ToString( );
				checkBox_isArray.Checked = RegularStructNode.ArrayLength >= 0;
				if (RegularStructNode.StructParseType == Edge.Node.Core.ParseType.Struct)
					radioButton_struct.Checked = true;
				else if (RegularStructNode.StructParseType == Edge.Node.Core.ParseType.FullName)
					radioButton_fullName.Checked = true;
				else
					radioButton_unfold.Checked = true;
			}
			else
			{
			}

			this.Shown += FormRegularStruct_Shown;
		}

		private void FormRegularStruct_Shown( object sender, EventArgs e )
		{
			if(RegularStructNode != null)
			{
				if(!string.IsNullOrEmpty( RegularStructNode.StructName ))
				{
					if(!this.structNodes.Contains( RegularStructNode.StructName ))
					{
						MessageBox.Show( $"当前的结构体名称[{RegularStructNode.StructName}]在结构体资源列表不存在，请重新确认名称。" );
					}
				}
			}
		}

		private void CheckBox_isArray_CheckedChanged( object sender, EventArgs e )
		{
			if (checkBox_isArray.Checked)
			{
				label5.Visible = true;
				textBox_length.Visible = true;
				//checkBox_unfold.Visible = false;
				//label_unfold.Visible = false;
				if(textBox_length.Text == "-1")
				{
					textBox_length.Text = "3";
				}
			}
			else
			{
				//checkBox_unfold.Visible = true;
				//label_unfold.Visible = true;
				label5.Visible = false;
				textBox_length.Visible = false;
			}
		}

		#endregion

		#region Button Click

		private void button1_Click( object sender, EventArgs e )
		{
			// 检查数据输入
			if (string.IsNullOrEmpty( textBox1.Text ))
			{
				MessageBox.Show( "名称不能为空！" );
				textBox1.Focus( );
				return;
			}

			if (!int.TryParse( textBox3.Text, out int index ))
			{
				MessageBox.Show( "索引号输入的格式有误，请重新输入。" );
				textBox3.Focus( );
				return;
			}

			if (index < 0)
			{
				MessageBox.Show( "索引号不能小于0，请重新输入。" );
				textBox3.Focus( );
				return;
			}

			if(comboBox_struct.SelectedItem == null)
			{
				MessageBox.Show( "当前没有选择任何的结构体，或是没有定义结构体信息，请先在结构体资源里定义，" );
				return;
			}

			RegularStructNode = new RegularStructNode( )
			{
				Name = textBox1.Text,
				Description = textBox2.Text,
				StructIndex = index,
				StructName = comboBox_struct.SelectedItem.ToString()
			};
			if (checkBox_isArray.Checked)
			{
				if (!int.TryParse( textBox_length.Text, out int arrayLength ))
				{
					MessageBox.Show( "数组长度输入错误。" );
					textBox_length.Focus( );
					return;
				}
				RegularStructNode.ArrayLength = arrayLength;
			}
			else
			{
				RegularStructNode.ArrayLength = -1;
			}

			if (radioButton_struct.Checked)
				RegularStructNode.StructParseType = Edge.Node.Core.ParseType.Struct;
			else if (radioButton_fullName.Checked)
				RegularStructNode.StructParseType = Edge.Node.Core.ParseType.FullName;
			else
				RegularStructNode.StructParseType = Edge.Node.Core.ParseType.Unflod;
			DialogResult = DialogResult.OK;
		}

		#endregion

		#region Private Member

		public RegularStructNode RegularStructNode { get; set; }
		private string[] structNodes;

		#endregion

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}
	}
}
