using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.EdgeViewer.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormDataRangeAlarm : HslForm
	{
		public FormDataRangeAlarm( NodeDataRangeAlarm rangeAlarm )
		{
			InitializeComponent( );
			this.rangeAlarm = rangeAlarm ?? new NodeDataRangeAlarm( );
			this.Icon = Util.ConvertToIcon( Util.GetImageByGroupNode( null, this.rangeAlarm ) );
			this.stringFormat = new StringFormat( );
			this.stringFormat.Alignment = StringAlignment.Near;
			this.stringFormat.LineAlignment = StringAlignment.Center;
		}

		private void FormDataRangeAlarm_Load( object sender, EventArgs e )
		{
			textBox_name.Text = this.rangeAlarm.Name;
			textBox_description.Text = this.rangeAlarm.Description;
			if (this.rangeAlarm.Degree == AlarmDegree.Hint) radioButton1.Checked = true;
			else if (this.rangeAlarm.Degree == AlarmDegree.Warn) radioButton4.Checked = true;
			else if (this.rangeAlarm.Degree == AlarmDegree.Error) radioButton2.Checked = true;
			else radioButton3.Checked = true;

			if (this.rangeAlarm.SecondDegree == AlarmDegree.Hint)
				radioButton_11.Checked = true;
			else if (this.rangeAlarm.SecondDegree == AlarmDegree.Warn)
				radioButton_12.Checked = true;
			else if (this.rangeAlarm.SecondDegree == AlarmDegree.Error)
				radioButton_13.Checked = true;
			else
				radioButton_14.Checked = true;

			textBox_delay.Text = this.rangeAlarm.Delay.ToString( );
			textBox_condition.Text = this.rangeAlarm.Conditions;
			textBox_maxAlarmContent.Text = this.rangeAlarm.MaxAlarmContent;
			textBox_minAlarmContent.Text = this.rangeAlarm.MinAlarmContent;
			textBox_maxmaxAlarmContent.Text = this.rangeAlarm.MaxMaxAlarmContent;
			textBox_minminAlarmContent.Text = this.rangeAlarm.MinMinAlarmContent;
			if (!double.IsNaN( this.rangeAlarm.MaxValue ))
			{
				textBox_maxValue.Text = this.rangeAlarm.MaxValue.ToString( );
				checkBox_max.Checked = true;
			}
			if (!double.IsNaN( this.rangeAlarm.MinValue ))
			{
				textBox_minValue.Text = this.rangeAlarm.MinValue.ToString( );
				checkBox_min.Checked = true;
			}
			if(!double.IsNaN( this.rangeAlarm.MaxMaxValue ))
			{
				textBox_maxmaxValue.Text = this.rangeAlarm.MaxMaxValue.ToString( );
				checkBox_maxmax.Checked = true;
			}
			if (!double.IsNaN( this.rangeAlarm.MinMinValue ))
			{
				textBox_minminValue.Text = this.rangeAlarm.MinMinValue.ToString( );
				checkBox_minmin.Checked = true;
			}
		}

		public NodeDataRangeAlarm RangeAlarm => rangeAlarm;


		private NodeDataRangeAlarm rangeAlarm;

		private void button5_Click( object sender, EventArgs e )
		{
			// 确认
			if (string.IsNullOrEmpty( textBox_name.Text )) { MessageBox.Show( "节点名称不能为空，请重新输入" ); textBox_maxValue.Focus( ); return; }
			rangeAlarm                   = new NodeDataRangeAlarm( );
			rangeAlarm.Name              = textBox_name.Text;
			rangeAlarm.Description       = textBox_description.Text;
			rangeAlarm.MaxAlarmContent   = textBox_maxAlarmContent.Text;
			rangeAlarm.MinAlarmContent   = textBox_minAlarmContent.Text;
			if (radioButton1.Checked)
				rangeAlarm.Degree = AlarmDegree.Hint;
			else if (radioButton4.Checked)
				rangeAlarm.Degree = AlarmDegree.Warn;
			else if (radioButton2.Checked)
				rangeAlarm.Degree = AlarmDegree.Error;
			else
				rangeAlarm.Degree = AlarmDegree.Fatal;

			if (radioButton_11.Checked)
				rangeAlarm.SecondDegree = AlarmDegree.Hint;
			else if (radioButton_12.Checked)
				rangeAlarm.SecondDegree = AlarmDegree.Warn;
			else if (radioButton_13.Checked)
				rangeAlarm.SecondDegree = AlarmDegree.Error;
			else
				rangeAlarm.SecondDegree = AlarmDegree.Fatal;

			if(!int.TryParse(textBox_delay.Text, out int delay ))
			{
				MessageBox.Show( "延迟触发的时间输入错误，需要输入整数。" );
				return;
			}
			rangeAlarm.Delay = delay;
			rangeAlarm.Conditions = textBox_condition.Text;
			if (checkBox_max.Checked)
			{
				if (!double.TryParse( textBox_maxValue.Text, out double max ))
				{
					MessageBox.Show( "上限值输入错误，需要输入浮点数。" );
					return;
				}
				rangeAlarm.MaxValue = max;
			}
			else
				rangeAlarm.MaxValue = double.NaN;
			if (checkBox_min.Checked)
			{
				if (!double.TryParse( textBox_minValue.Text, out double min ))
				{
					MessageBox.Show( "下限值输入错误，需要输入浮点数。" );
					return;
				}
				rangeAlarm.MinValue = min;
			}
			else
				rangeAlarm.MinValue = double.NaN;

			if (checkBox_maxmax.Checked)
			{
				if (!double.TryParse( textBox_maxmaxValue.Text, out double maxMax ))
				{
					MessageBox.Show( "极大值输入错误，需要输入浮点数。" );
					return;
				}
				rangeAlarm.MaxMaxValue = maxMax;
			}
			else
				rangeAlarm.MaxMaxValue = double.NaN;

			if (checkBox_minmin.Checked)
			{
				if (!double.TryParse( textBox_minminValue.Text, out double minMin ))
				{
					MessageBox.Show( "极小值输入错误，需要输入浮点数。" );
					return;
				}
				rangeAlarm.MinMinValue = minMin;
			}
			else
				rangeAlarm.MinMinValue = double.NaN;


			if (!(checkBox_min.Checked || checkBox_max.Checked || checkBox_minmin.Checked || checkBox_maxmax.Checked))
			{
				MessageBox.Show( "至少需要选择一个上限值或是下限值。" );
				return;
			}

			if(checkBox_max.Checked && checkBox_maxmax.Checked)
			{
				if(rangeAlarm.MaxMaxValue < rangeAlarm.MaxValue)
				{
					MessageBox.Show( "最大值应该小于极大值！请重新输入。" );
					return;
				}
			}

			if (checkBox_min.Checked && checkBox_minmin.Checked)
			{
				if (rangeAlarm.MinMinValue > rangeAlarm.MinValue)
				{
					MessageBox.Show( "最小值应该大于极大值！请重新输入。" );
					return;
				}
			}

			DialogResult = DialogResult.OK;
		}

		private StringFormat stringFormat = new StringFormat( );

		private void FormDataRangeAlarm_Paint( object sender, PaintEventArgs e )
		{
			// 绘制说明
			Graphics g = e.Graphics;
			Rectangle rectangleDanger = new Rectangle( 323, 51, 20, 229 );
			g.DrawLines( Pens.Red, new Point[]
			{
				new Point( 370, 51 ),
				new Point( 340, 51 ),
				new Point( 340, 280 ),
				new Point( 370, 280 ),
			} );
			Rectangle rectangleSafe = new Rectangle( 353, 123, 20, 78 );
			g.DrawLines( Pens.DodgerBlue, new Point[]
			{
				new Point( 370, 123 ),
				new Point( 350, 123 ),
				new Point( 350, 201 ),
				new Point( 370, 201 ),
			} );
			g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
			g.DrawString( "安全范围", this.Font, Brushes.DodgerBlue, rectangleSafe, this.stringFormat );
			g.DrawString( "警戒范围", this.Font, Brushes.Red, rectangleDanger, this.stringFormat );
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button5.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button4.PerformClick( );
		}
	}
}
