using HslTechnology.Edge.Node.Alarm;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormCodeAlarms : HslForm
	{
		public FormCodeAlarms( )
		{
			InitializeComponent( );
		}

		private void FormCodeAlarms_Load( object sender, EventArgs e )
		{
			this.alarmItemDataTable1.BackColor = this.BackColor;
			this.alarmItemDataTable1.DataGridView.CellMouseDoubleClick += DataGridView_CellMouseDoubleClick;
		}

		private void DataGridView_CellMouseDoubleClick( object sender, DataGridViewCellMouseEventArgs e )
		{
			if (this.alarmItemDataTable1.DataGridView.SelectedRows.Count > 0)
			{
				if (this.alarmItemDataTable1.DataGridView.SelectedRows[0].Tag is AlarmDefinitionNode item)
				{
					ShowNodeAlarmMarkItem( item );
					selectedAlarm = item;
				}
			}
		}

		private void ShowNodeAlarmMarkItem( AlarmDefinitionNode item )
		{
			if (item == null) return;

			textBox6.Text = item.Name;
			textBox7.Text = item.Description;
			textBox3.Text = item.Code.ToString( );
			if (item.Degree == AlarmDegree.Hint)
				radioButton_Hint.Checked = true;
			else if (item.Degree == AlarmDegree.Warn)
				radioButton_Warn.Checked = true;
			else if (item.Degree == AlarmDegree.Error)
				radioButton_Error.Checked = true;
			else
				radioButton_Fatal.Checked = true;
			textBox_Hint.Text  = item.DegreeHintRaise.ToString( );
			textBox_Warn.Text  = item.DegreeWarnRaise.ToString( );
			textBox_Error.Text = item.DegreeErrorRaise.ToString( );
			textBox4.Text      = item.Delay.ToString( );
			textBox5.Text      = item.Conditions;
		}

		private OperateResult<AlarmDefinitionNode> GetNodeAlarmMarkItem( )
		{
			AlarmDefinitionNode markItem = new AlarmDefinitionNode( );
			if (string.IsNullOrEmpty( textBox6.Text )) return new OperateResult<AlarmDefinitionNode>( "报警名称不能为空" );
			if (!int.TryParse( textBox3.Text, out int code )) return new OperateResult<AlarmDefinitionNode>( "报警代号输入错误！需要为整数" );
			if (alarmNode.AlarmType == AlarmType.Integer && code == 0) return new OperateResult<AlarmDefinitionNode>( "报警代号为 0 表示正常，无法配置报警" );
			if (!int.TryParse( textBox4.Text, out int delay )) return new OperateResult<AlarmDefinitionNode>( "延迟触发输入错误！需要为整数" );
			if (!int.TryParse( textBox_Hint.Text, out int hintTime )) return new OperateResult<AlarmDefinitionNode>( "提示性报警动态提升时间输入错误！需要为整数" );
			if (!int.TryParse( textBox_Warn.Text, out int warnTime )) return new OperateResult<AlarmDefinitionNode>( "警告性报警动态提升时间输入错误！需要为整数" );
			if (!int.TryParse( textBox_Error.Text, out int errorTime )) return new OperateResult<AlarmDefinitionNode>( "错误性报警动态提升时间输入错误！需要为整数" );

			markItem.Name = textBox6.Text;
			markItem.Description = textBox7.Text;
			markItem.Code = code;
			markItem.Degree = radioButton_Hint.Checked ? AlarmDegree.Hint :
				radioButton_Warn.Checked ? AlarmDegree.Warn :
				radioButton_Error.Checked ? AlarmDegree.Error : AlarmDegree.Fatal;
			markItem.Delay = delay;
			markItem.Conditions = textBox5.Text;
			markItem.DegreeHintRaise = hintTime;
			markItem.DegreeWarnRaise = warnTime;
			markItem.DegreeErrorRaise = errorTime;
			return OperateResult.CreateSuccessResult( markItem );
		}

		public void ShowAlarmInfo( AlarmNode alarmNode, List<AlarmDefinitionNode> alarmMarkItems )
		{
			if (alarmNode != null) this.Icon = Util.ConvertToIcon( Util.GetImageByGroupNode( null, alarmNode ) );
			if (alarmNode == null) alarmNode = new AlarmNode( );
			this.alarmNode = alarmNode;
			if (alarmMarkItems == null)
				this.alarmMarkItems = new List<AlarmDefinitionNode>( );
			else
				this.alarmMarkItems = AlarmDefinitionNode.CopyList( alarmMarkItems );


			this.Text = "报警信息设置 - " + alarmNode.AlarmType.ToString( );
			this.textBox1.Text = alarmNode.Name;
			this.textBox2.Text = alarmNode.Description;
			if(alarmNode.AlarmType == AlarmType.Boolean || alarmNode.AlarmType == AlarmType.Hex)
			{
				textBox3.Text = "0";
			}

			if (alarmNode.AlarmType == AlarmType.Hex)
			{
				NodeAlarmHex nodeAlarmHex = alarmNode as NodeAlarmHex;
				if (nodeAlarmHex != null)
				{
					checkBox1.Checked = nodeAlarmHex.RevserveByWord;
				}
			}
			else
			{
				checkBox1.Visible = false;
				panel2.Location = new Point(panel2.Location.X, panel2.Location.Y - 19);
			}

			this.alarmItemDataTable1.ShowList( this.alarmMarkItems );
		}

		private void button1_Click( object sender, EventArgs e )
		{
			// 新增alarm item
			var result = GetNodeAlarmMarkItem( );
			if (!result.IsSuccess) { MessageBox.Show( result.Message ); return; }
			var markItem = result.Content;

			if (alarmMarkItems.Find( m => m.Name == markItem.Name ) != null) { MessageBox.Show( "报警名称已经存在，不能重复。" ); return; }
			if (alarmMarkItems.Find( m => m.Code == markItem.Code ) != null) { MessageBox.Show( "报警代号已经存在，不能重复。" ); return; }
			alarmMarkItems.Add( markItem );
			alarmMarkItems.Sort( );

			this.alarmItemDataTable1.ShowList( alarmMarkItems );
			MessageBox.Show( "新增成功!" );
		}

		private void button2_Click( object sender, EventArgs e )
		{
			// edit alarm item
			if(selectedAlarm== null) { MessageBox.Show( "双击需要编辑的行，然后进行编辑" ); return; }

			var result = GetNodeAlarmMarkItem( );
			if (!result.IsSuccess) { MessageBox.Show( result.Message ); return; }
			var markItem = result.Content;

			List<AlarmDefinitionNode> tmp = alarmMarkItems.Take( alarmMarkItems.Count ).ToList( );
			tmp.Remove( selectedAlarm );

			if (tmp.Find( m => m.Name == markItem.Name ) != null) { MessageBox.Show( "报警名称已经存在，不能重复。" ); return; }
			if (tmp.Find( m => m.Code == markItem.Code ) != null) { MessageBox.Show( "报警代号已经存在，不能重复。" ); return; }

			// 可以编辑添加
			selectedAlarm.Name             = markItem.Name;
			selectedAlarm.Description      = markItem.Description;
			selectedAlarm.Code             = markItem.Code;
			selectedAlarm.Degree           = markItem.Degree;
			selectedAlarm.Delay            = markItem.Delay;
			selectedAlarm.Conditions       = markItem.Conditions;
			selectedAlarm.DegreeHintRaise  = markItem.DegreeHintRaise;
			selectedAlarm.DegreeErrorRaise = markItem.DegreeErrorRaise;
			selectedAlarm.DegreeWarnRaise  = markItem.DegreeWarnRaise;

			this.alarmItemDataTable1.ShowList( alarmMarkItems );
			MessageBox.Show( "更新成功！" );
		}

		private void button3_Click( object sender, EventArgs e )
		{
			// delete alarm item
			if (this.alarmItemDataTable1.DataGridView.SelectedRows.Count <= 0) { MessageBox.Show( "当前没有选中的报警配置信息" ); return; }
			if (MessageBox.Show( "请确认是否删除表格选中的 " + this.alarmItemDataTable1.DataGridView.SelectedRows.Count +
				" 行报警配置数据？", "删除确认", MessageBoxButtons.YesNo, MessageBoxIcon.Warning ) == DialogResult.Yes)
			{
				for (int i = 0; i < this.alarmItemDataTable1.DataGridView.SelectedRows.Count; i++)
				{
					if (this.alarmItemDataTable1.DataGridView.SelectedRows[i].Tag is AlarmDefinitionNode item)
					{
						alarmMarkItems.Remove( item );
					}
				}
				this.selectedAlarm = null;
				this.alarmItemDataTable1.ShowList( alarmMarkItems );
				MessageBox.Show( "删除成功！" );
			}
		}

		public AlarmNode AlarmNode => alarmNode;

		public List<AlarmDefinitionNode> AlarmMarkItems => alarmMarkItems;

		private AlarmNode alarmNode;
		private List<AlarmDefinitionNode> alarmMarkItems;
		private AlarmDefinitionNode selectedAlarm;

		private void button5_Click( object sender, EventArgs e )
		{
			if (string.IsNullOrEmpty( textBox1.Text )) { MessageBox.Show( "节点名称不能为空，请重新输入" ); textBox1.Focus( ); return; }
			switch( alarmNode.AlarmType)
			{
				case AlarmType.Boolean: alarmNode = new NodeAlarmBool( ); break;
				case AlarmType.Integer: alarmNode = new NodeAlarmInteger( ); break;
				case AlarmType.Hex:
					{
						NodeAlarmHex alarmNodeHex = new NodeAlarmHex( );
						alarmNodeHex.RevserveByWord = checkBox1.Checked;
						alarmNode = alarmNodeHex;
						break;
					}
			}
			alarmNode.Name = textBox1.Text;
			alarmNode.Description = textBox2.Text;
			DialogResult = DialogResult.OK;
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button5.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button4.PerformClick( );
		}
	}
}
