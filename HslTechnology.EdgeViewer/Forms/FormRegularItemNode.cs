using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node.Regular;
using HslCommunication;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormRegularItemNode : HslForm
	{
		#region Constructor

		public FormRegularItemNode( 
			RegularScalarNode regularItemNode = null, 
			List<AlarmNode> alarmNodes = null, 
			List<OeeNode> oeeNodes = null )
		{
			InitializeComponent( );

			this.alarmNodes = alarmNodes;
			this.oeeNodes = oeeNodes;
			this.RegularItemNode = regularItemNode ?? new RegularScalarNode( );
			this.Icon = Util.ConvertToIcon( Util.GetImageByGroupNode( null, RegularItemNode ) );
			this.userControls = new List<UserControl>( );
		}

		#endregion

		#region Form Load
		
		private void FormRegularItemNode_Load( object sender, EventArgs e )
		{
			comboBox_alarm.DataSource = this.alarmNodes;
			comboBox_oee.DataSource = this.oeeNodes;
			comboBox_type.DataSource = RegularNodeTypeItem.GetAllRegularNodeTypeItems( withBoolOfByte: true );

			this.userControls.Add( this.requestDataScaleControl1 );
			this.userControls.Add( this.regularStringControl1 );
			this.userControls.Add( this.regularBCDControl1 );
			regularStringControl1.Visible = false;
			regularBCDControl1.Visible = false;
			requestDataScaleControl1.Location = regularStringControl1.Location;

			regularBCDControl1.Location = new Point( requestDataScaleControl1.Location.X, requestDataScaleControl1.Location.Y + requestDataScaleControl1.Height + 5 );

			comboBox_type.SelectedIndexChanged += ComboBox_Type_SelectedIndexChanged;
			checkBox_isArray.CheckedChanged    += CheckBox_isArray_CheckedChanged;

			if (RegularItemNode != null)
			{
				textBox_name.Text                  = RegularItemNode.Name;
				textBox_description.Text           = RegularItemNode.Description;
				textBox_displayName.Text           = RegularItemNode.DisplayName;
				textBox3.Text                      = RegularItemNode.Index.ToString( );
				comboBox_type.SelectedItem         = RegularNodeTypeItem.GetDataPraseItemByCode( RegularItemNode.DataTypeCode );
				checkBox_isArray.Checked           = RegularItemNode.Length >= 0;
				checkBox_forbidRemoteWrite.Checked = RegularItemNode.ForbidRemoteWrite;
				checkBox_subscription.Checked      = RegularItemNode.Subscription;
				textBox_length.Text                = RegularItemNode.Length.ToString( );
				textBox_stringLength.Text          = RegularItemNode.StringLength.ToString( );
				textBox_unit.Text                  = RegularItemNode.Unit;

				if (!string.IsNullOrEmpty( RegularItemNode.AlarmRelate ))
				{
					AlarmNode find = this.alarmNodes.Find( m => m.Name == RegularItemNode.AlarmRelate );
					if (find != null)
					{
						checkBox_alarm.Checked = true;
						comboBox_alarm.SelectedItem = find;
					}
				}
				if (!string.IsNullOrEmpty( RegularItemNode.OeeRelate ))
				{
					OeeNode find = this.oeeNodes.Find( m => m.Name == RegularItemNode.OeeRelate );
					if (find != null)
					{
						checkBox_oee.Checked = true;
						comboBox_oee.SelectedItem = find;
					}
				}
				requestDataScaleControl1.ShowScalarTransform( RegularItemNode );
				regularStringControl1.ShowRegularScalarNode( RegularItemNode );
				regularBCDControl1.ShowRegularScalarNode( RegularItemNode );
			}
			else
			{
				comboBox_type.SelectedItem = RegularNodeTypeItem.GetDataPraseItemByCode( RegularNodeTypeItem.Int16.Text );
			}

			ComboBox_Type_SelectedIndexChanged( comboBox_type, e );
		}

		private void ShowUserControl( UserControl control )
		{
			control.Visible = true;
			for (int i = 0; i < this.userControls.Count; i++)
			{
				if(!object.ReferenceEquals(control, this.userControls[i] ))
				{
					this.userControls[i].Visible = false;
				}
			}
		}

		private void CheckBox_isArray_CheckedChanged( object sender, EventArgs e )
		{
			if (checkBox_isArray.Checked)
			{
				label5.Visible = true;
				textBox_length.Visible = true;
				if (textBox_length.Text == "-1")
					textBox_length.Text = "3";
			}
			else
			{
				label5.Visible = false;
				textBox_length.Visible = false;
			}
		}

		private void ComboBox_Type_SelectedIndexChanged( object sender, EventArgs e )
		{
			if(RegularNodeTypeItem.IsDataTypeString( comboBox_type.SelectedItem.ToString( ) ))
			{
				//checkBox_isArray.Visible = false;
				//label5.Text = "字符串长度(byte)";
				if(comboBox_type.SelectedItem.ToString( ) == RegularNodeTypeItem.BCD.Text)
				{
					requestDataScaleControl1.Visible = true;
					regularBCDControl1.Visible = true;
					regularStringControl1.Visible = false;
				}
				else
				{
					requestDataScaleControl1.Visible = false;
					regularBCDControl1.Visible = false;
					regularStringControl1.Visible = true;
				}
				textBox_stringLength.Enabled = true;
			}
			else
			{
				//checkBox_isArray.Visible = true;
				//label5.Text = "数组长度";

				// ShowUserControl( requestDataScaleControl1 );
				requestDataScaleControl1.Visible = true;
				regularBCDControl1.Visible = false;
				regularStringControl1.Visible = false;
				textBox_stringLength.Enabled = false;
			}
			label5.Visible = checkBox_isArray.Checked;
			textBox_length.Visible = checkBox_isArray.Checked;

			if (comboBox_type.SelectedItem.ToString( ) == RegularNodeTypeItem.Bool.Text)
			{
				label3.Text = "位索引：";
				label7.Text = "起始按照位为单位，从0开始，长度也是位长度";
			}
			else if (comboBox_type.SelectedItem.ToString( ) == RegularNodeTypeItem.BoolOfByte.Text)
			{
				label3.Text = "字节索引：";
				label7.Text = "起始按照字节为单位，长度为字节数，一个字节一个bool";
			}
			else
			{
				label3.Text = "字节索引：";
				label7.Text = "字节为单位，从0开始，长度为数据长度";
			}
		}

		#endregion

		#region Button Click

		private void button1_Click( object sender, EventArgs e )
		{
			// 检查数据输入
			if (string.IsNullOrEmpty( textBox_name.Text ))
			{
				MessageBox.Show( "名称不能为空！" );
				textBox_name.Focus( );
				return;
			}

			if (!int.TryParse( textBox3.Text, out int index ))
			{
				MessageBox.Show( "索引号输入的格式有误，请重新输入。" );
				textBox3.Focus( );
				return;
			}

			if (index < 0)
			{
				MessageBox.Show( "索引号不能小于0，请重新输入。" );
				textBox3.Focus( );
				return;
			}

			if (!int.TryParse( textBox_length.Text, out int typeLength ))
			{
				MessageBox.Show( "数据长度输入错误。" );
				textBox_length.Focus( );
				return;
			}

			RegularItemNode = new RegularScalarNode( )
			{
				Name              = textBox_name.Text,
				DisplayName       = textBox_displayName.Text,
				Description       = textBox_description.Text,
				Index             = index,
				DataTypeCode      = ((RegularNodeTypeItem)comboBox_type.SelectedItem).Text,
				ForbidRemoteWrite = checkBox_forbidRemoteWrite.Checked,
				Subscription      = checkBox_subscription.Checked,
				Unit              = textBox_unit.Text,
			};
			RegularItemNode.Length = checkBox_isArray.Checked ? typeLength : -1;

			if (RegularNodeTypeItem.IsDataTypeString( RegularItemNode.DataTypeCode ))
			{
				if (!int.TryParse( textBox_stringLength.Text, out int stringLength ))
				{
					MessageBox.Show( "字符串的长度输入错误。" );
					textBox_stringLength.Focus( );
					return;
				}

				if (stringLength <= 0)
				{
					MessageBox.Show( "字符串的长度输入不能小于0，必须是大于0的整数。" );
					textBox_stringLength.Focus( );
					return;
				}

				RegularItemNode.StringLength = stringLength;
			}

			OperateResult result1 = requestDataScaleControl1.GetScalarTransform( RegularItemNode );
			if (!result1.IsSuccess) { MessageBox.Show( result1.Message ); return; }

			if (RegularItemNode.DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				// BCD 字符串的配置信息
				OperateResult result3 = regularBCDControl1.GetRegularScalarNode( RegularItemNode );
				if (!result3.IsSuccess) { MessageBox.Show( result3.Message ); return; }

			}
			else if (RegularNodeTypeItem.IsDataTypeString( RegularItemNode.DataTypeCode ))
			{
				// 其他字符串的配置信息
				OperateResult result2 = regularStringControl1.GetRegularScalarNode( RegularItemNode );
				if (!result2.IsSuccess) { MessageBox.Show( result2.Message ); return; }
			}

			if (checkBox_alarm.Checked)
			{
				AlarmNode select = comboBox_alarm.SelectedItem as AlarmNode;
				OperateResult setAlarm = Util.SetAlarmRelateByAlarmNode( RegularItemNode, select );
				if(!setAlarm.IsSuccess) { MessageBox.Show( setAlarm.Message ); return; }
			}

			if (checkBox_oee.Checked)
			{
				OeeNode select = comboBox_oee.SelectedItem as OeeNode;
				OperateResult setAlarm = Util.SetOeeRelateBy( RegularItemNode, select );
				if (!setAlarm.IsSuccess) { MessageBox.Show( setAlarm.Message ); return; }
			}

			DialogResult = DialogResult.OK;
		}

		#endregion

		#region Private Member

		public RegularScalarNode RegularItemNode { get; set; }
		private List<AlarmNode> alarmNodes = null;
		private List<OeeNode> oeeNodes = null;
		private List<UserControl> userControls;                   // 自定义的控件列表信息

		#endregion

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}
	}
}
