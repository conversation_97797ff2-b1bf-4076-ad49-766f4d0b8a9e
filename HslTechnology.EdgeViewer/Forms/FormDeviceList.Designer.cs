
namespace HslTechnology.EdgeViewer.Forms
{
	partial class FormDeviceList
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
			System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
			this.hslDataGridView1 = new HslTechnology.Edge.Controls.Basic.HslDataGridView();
			this.Column_number = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Column_name = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Column_time = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Column_reason = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.label1 = new System.Windows.Forms.Label();
			this.textBox_regex = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.panel1 = new System.Windows.Forms.Panel();
			this.radioButton_sort_timeSmall = new System.Windows.Forms.RadioButton();
			this.radioButton_sort_timeBig = new System.Windows.Forms.RadioButton();
			this.radioButton_sort_normal = new System.Windows.Forms.RadioButton();
			this.label_count = new System.Windows.Forms.Label();
			((System.ComponentModel.ISupportInitialize)(this.hslDataGridView1)).BeginInit();
			this.panel1.SuspendLayout();
			this.SuspendLayout();
			// 
			// hslDataGridView1
			// 
			this.hslDataGridView1.AllowUserToAddRows = false;
			this.hslDataGridView1.AllowUserToDeleteRows = false;
			dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(238)))), ((int)(((byte)(238)))), ((int)(((byte)(255)))));
			this.hslDataGridView1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
			this.hslDataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.hslDataGridView1.BackgroundColor = System.Drawing.Color.White;
			this.hslDataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.hslDataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Column_number,
            this.Column_name,
            this.Column_time,
            this.Column_reason});
			this.hslDataGridView1.Location = new System.Drawing.Point(1, 33);
			this.hslDataGridView1.Name = "hslDataGridView1";
			this.hslDataGridView1.ReadOnly = true;
			this.hslDataGridView1.RowHeadersVisible = false;
			this.hslDataGridView1.RowTemplate.Height = 23;
			this.hslDataGridView1.Size = new System.Drawing.Size(840, 539);
			this.hslDataGridView1.TabIndex = 0;
			// 
			// Column_number
			// 
			this.Column_number.HeaderText = "序号";
			this.Column_number.Name = "Column_number";
			this.Column_number.ReadOnly = true;
			this.Column_number.Width = 70;
			// 
			// Column_name
			// 
			this.Column_name.HeaderText = "设备名称";
			this.Column_name.Name = "Column_name";
			this.Column_name.ReadOnly = true;
			this.Column_name.Width = 300;
			// 
			// Column_time
			// 
			this.Column_time.HeaderText = "掉线时间";
			this.Column_time.Name = "Column_time";
			this.Column_time.ReadOnly = true;
			// 
			// Column_reason
			// 
			this.Column_reason.HeaderText = "异常原因";
			this.Column_reason.Name = "Column_reason";
			this.Column_reason.ReadOnly = true;
			this.Column_reason.Width = 350;
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(5, 8);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(95, 17);
			this.label1.TabIndex = 1;
			this.label1.Text = "异常设备总数量:";
			// 
			// textBox_regex
			// 
			this.textBox_regex.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox_regex.Location = new System.Drawing.Point(676, 5);
			this.textBox_regex.Name = "textBox_regex";
			this.textBox_regex.Size = new System.Drawing.Size(163, 23);
			this.textBox_regex.TabIndex = 2;
			// 
			// label2
			// 
			this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(607, 8);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(59, 17);
			this.label2.TabIndex = 3;
			this.label2.Text = "正则过滤:";
			// 
			// panel1
			// 
			this.panel1.Controls.Add(this.radioButton_sort_timeSmall);
			this.panel1.Controls.Add(this.radioButton_sort_timeBig);
			this.panel1.Controls.Add(this.radioButton_sort_normal);
			this.panel1.Location = new System.Drawing.Point(148, 3);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(319, 29);
			this.panel1.TabIndex = 4;
			// 
			// radioButton_sort_timeSmall
			// 
			this.radioButton_sort_timeSmall.AutoSize = true;
			this.radioButton_sort_timeSmall.Location = new System.Drawing.Point(205, 3);
			this.radioButton_sort_timeSmall.Name = "radioButton_sort_timeSmall";
			this.radioButton_sort_timeSmall.Size = new System.Drawing.Size(98, 21);
			this.radioButton_sort_timeSmall.TabIndex = 2;
			this.radioButton_sort_timeSmall.Text = "掉线时间递减";
			this.radioButton_sort_timeSmall.UseVisualStyleBackColor = true;
			// 
			// radioButton_sort_timeBig
			// 
			this.radioButton_sort_timeBig.AutoSize = true;
			this.radioButton_sort_timeBig.Location = new System.Drawing.Point(94, 3);
			this.radioButton_sort_timeBig.Name = "radioButton_sort_timeBig";
			this.radioButton_sort_timeBig.Size = new System.Drawing.Size(98, 21);
			this.radioButton_sort_timeBig.TabIndex = 1;
			this.radioButton_sort_timeBig.Text = "掉线时间递增";
			this.radioButton_sort_timeBig.UseVisualStyleBackColor = true;
			// 
			// radioButton_sort_normal
			// 
			this.radioButton_sort_normal.AutoSize = true;
			this.radioButton_sort_normal.Checked = true;
			this.radioButton_sort_normal.Location = new System.Drawing.Point(3, 3);
			this.radioButton_sort_normal.Name = "radioButton_sort_normal";
			this.radioButton_sort_normal.Size = new System.Drawing.Size(74, 21);
			this.radioButton_sort_normal.TabIndex = 0;
			this.radioButton_sort_normal.TabStop = true;
			this.radioButton_sort_normal.Text = "默认排序";
			this.radioButton_sort_normal.UseVisualStyleBackColor = true;
			// 
			// label_count
			// 
			this.label_count.AutoSize = true;
			this.label_count.Location = new System.Drawing.Point(106, 8);
			this.label_count.Name = "label_count";
			this.label_count.Size = new System.Drawing.Size(15, 17);
			this.label_count.TabIndex = 5;
			this.label_count.Text = "0";
			this.label_count.Click += new System.EventHandler(this.label3_Click);
			// 
			// FormDeviceList
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.Honeydew;
			this.ClientSize = new System.Drawing.Size(842, 574);
			this.Controls.Add(this.label_count);
			this.Controls.Add(this.panel1);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.textBox_regex);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.hslDataGridView1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "FormDeviceList";
			this.Text = "所有设备列表";
			this.Load += new System.EventHandler(this.FormDeviceList_Load);
			((System.ComponentModel.ISupportInitialize)(this.hslDataGridView1)).EndInit();
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private Edge.Controls.Basic.HslDataGridView hslDataGridView1;
		private System.Windows.Forms.DataGridViewTextBoxColumn Column_number;
		private System.Windows.Forms.DataGridViewTextBoxColumn Column_name;
		private System.Windows.Forms.DataGridViewTextBoxColumn Column_time;
		private System.Windows.Forms.DataGridViewTextBoxColumn Column_reason;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TextBox textBox_regex;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.RadioButton radioButton_sort_timeSmall;
		private System.Windows.Forms.RadioButton radioButton_sort_timeBig;
		private System.Windows.Forms.RadioButton radioButton_sort_normal;
		private System.Windows.Forms.Label label_count;
	}
}