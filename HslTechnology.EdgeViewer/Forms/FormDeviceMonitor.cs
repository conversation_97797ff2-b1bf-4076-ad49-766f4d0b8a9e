using HslCommunication.MQTT;
using HslTechnology.Edge.Config;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using HslCommunication.LogNet;
using HslCommunication;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Controls.Machine;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge.DataBusiness.Time;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.Reflection;
using HslTechnology.EdgeViewer.Controls;
using System.Runtime.InteropServices;
using HslTechnology.EdgeViewer.Core;
using HslTechnology.Edge.Device.Base;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormDeviceMonitor : HslPage
	{
		public FormDeviceMonitor( ILogNet logNet, FormMain formMain )
		{
			InitializeComponent( );
			this.logNet       = logNet;
			this.Icon         = Util.GetWinformIcon( );
			this.formMain     = formMain;

			

			this.dataInfomationControl1.onValueCellMouseDoubleClick     += DataInfomationControl1_onValueCellMouseDoubleClick;
			this.dataInfomationControl1.onValueNameCellMouseDoubleClick += DataInfomationControl1_onValueNameCellMouseDoubleClick;
			this.deviceMethodControl1.onMethodCellMouseDoubleClick      += DeviceMethodControl1_onMethodCellMouseDoubleClick;
			this.tabControl1.SelectedIndexChanged                       += TabControl1_SelectedIndexChanged;
			this.hslBadge2.Click                                        += Label_alarmCount_Click;
			this.label_device_error.Click                               += Label_device_error_Click;
			this.label_device_run.Click                                 += Label_device_run_Click;
			this.label_device_stop.Click                                += Label_device_stop_Click;
		}


		#region Form Load Close

		private void FormDeviceMonitor_Load( object sender, EventArgs e )
		{
			if (eventLoaded == false)
			{
				eventLoaded = true;
				deviceStatus                          = new List<DeviceStatusControl>( );
				threadUpdateDeviceStatus              = new Thread( new ThreadStart( ThreadReadServer ) );
				threadUpdateDeviceStatus.IsBackground = true;
				threadUpdateDeviceStatus.Start( );

				Text = "设备监视界面";
				splitContainer2.Panel2Collapsed = true;

				toolTip1.SetToolTip( label4,  "设备固件版本" );
				toolTip1.SetToolTip( label8,  "设备总数" );
				toolTip1.SetToolTip( label_device_run,  "在线设备数量" );
				toolTip1.SetToolTip( label_device_stop, "暂停采集数量" );
				toolTip1.SetToolTip( label_device_error, "网络异常数量" );

				if (Util.ViewerSettings.Theme == "Dark")
				{
					//tabControl1.Appearance = TabAppearance.Buttons;

					BackColor                   = Util.Theme.ColorPalette.TabUnselected.Background;
					ForeColor                   = Util.Theme.ColorPalette.TabUnselected.Text;
					tabPage_data.BackColor      = Util.Theme.ColorPalette.TabUnselected.Background;
					tabPage_data.ForeColor      = Util.Theme.ColorPalette.TabUnselected.Text;
					tabPage_data.BorderStyle    = BorderStyle.FixedSingle;
					tabPage_device.BackColor    = Util.Theme.ColorPalette.TabUnselected.Background;
					tabPage_device.ForeColor    = Util.Theme.ColorPalette.TabUnselected.Text;
					tabPage_device.BorderStyle  = BorderStyle.FixedSingle;
					tabPage_method.BackColor    = Util.Theme.ColorPalette.TabUnselected.Background;
					tabPage_method.ForeColor    = Util.Theme.ColorPalette.TabUnselected.Text;
					tabPage_method.BorderStyle  = BorderStyle.FixedSingle;
					tabPage_alarm.BackColor     = Util.Theme.ColorPalette.TabUnselected.Background;
					tabPage_alarm.ForeColor     = Util.Theme.ColorPalette.TabUnselected.Text;
					tabPage_alarm.BorderStyle   = BorderStyle.FixedSingle;
					tabPage_oee.BackColor       = Util.Theme.ColorPalette.TabUnselected.Background;
					tabPage_oee.ForeColor       = Util.Theme.ColorPalette.TabUnselected.Text;
					tabPage_oee.BorderStyle     = BorderStyle.FixedSingle;
					tabPage_offline.BackColor   = Util.Theme.ColorPalette.TabUnselected.Background;
					tabPage_offline.ForeColor   = Util.Theme.ColorPalette.TabUnselected.Text;
					tabPage_offline.BorderStyle = BorderStyle.FixedSingle;
					tabPage_log.BackColor       = Util.Theme.ColorPalette.TabUnselected.Background;
					tabPage_log.ForeColor       = Util.Theme.ColorPalette.TabUnselected.Text;
					tabPage_log.BorderStyle     = BorderStyle.FixedSingle;

					panel5.BackColor = Util.Theme.ColorPalette.TabUnselected.Background;
					//tabControl1.DrawMode = TabDrawMode.OwnerDrawFixed;
					//tabControl1.DrawItem += TabControl1_DrawItem;

					this.dataInfomationControl1.ThemeDark( );
					this.deviceInfoControl1.ThemeDark( );
					this.deviceMethodControl1.ThemeDark( );
					this.deviceAlarmControl1.ThemeDark( );
					this.deviceOfflineControl1.ThemeDark( );
					this.deviceLogControl1.ThemeDark( );
					this.deviceOeeControl1.ThemeDark( );
				}
			}
		}

		private void TabControl1_DrawItem( object sender, DrawItemEventArgs e )
		{
			Rectangle rectangle = tabControl1.GetTabRect( e.Index );
			if (e.State == DrawItemState.Selected)
			{
				e.Graphics.DrawLine( Pens.LimeGreen, rectangle.X, rectangle.Y, rectangle.X + rectangle.Width, rectangle.Y );
				using (Brush brush = new SolidBrush( Util.Theme.ColorPalette.TabSelectedActive.Background ))
					e.Graphics.FillRectangle( brush, rectangle );
				using (Brush brush = new SolidBrush( Util.Theme.ColorPalette.TabSelectedActive.Text ))
					e.Graphics.DrawString( tabControl1.TabPages[e.Index].Text, Font, brush, rectangle, HslControls.HslHelper.StringFormatCenter );
			}
			else
			{
				using (Brush brush = new SolidBrush( Util.Theme.ColorPalette.TabUnselected.Background ))
					e.Graphics.FillRectangle( brush, rectangle );
				using (Brush brush = new SolidBrush( Util.Theme.ColorPalette.TabUnselected.Text ))
					e.Graphics.DrawString( tabControl1.TabPages[e.Index].Text, Font, brush, rectangle, HslControls.HslHelper.StringFormatCenter );
			}
		}

		private void Label_alarmCount_Click( object sender, EventArgs e )
		{
			if (this.serverSettingsCurrent == null) return;
			FormAlarmList formAlarmList = new FormAlarmList( this.logNet, this.serverSettingsCurrent );
			formMain?.ShowDockPanel( formAlarmList );
		}

		private void Label_device_error_Click( object sender, EventArgs e )
		{
			if (this.serverSettingsCurrent == null) return;
			FormDeviceList formDeviceList = new FormDeviceList( this.logNet, this.serverSettingsCurrent, 2 );
			formMain?.ShowDockPanel( formDeviceList );
		}

		private void Label_device_stop_Click( object sender, EventArgs e )
		{
			if (this.serverSettingsCurrent == null) return;
			FormDeviceList formDeviceList = new FormDeviceList( this.logNet, this.serverSettingsCurrent, 1 );
			formMain?.ShowDockPanel( formDeviceList );
		}

		private void Label_device_run_Click( object sender, EventArgs e )
		{
			if (this.serverSettingsCurrent == null) return;
			FormDeviceList formDeviceList = new FormDeviceList( this.logNet, this.serverSettingsCurrent, 0 );
			formMain?.ShowDockPanel( formDeviceList );
		}

		private void DeviceMethodControl1_onMethodCellMouseDoubleClick( MethodRpcInfo rpc )
		{
			if (this.serverSettingsCurrent == null || deviceCurrentSelected == null) return;
			var client = this.serverSettingsCurrent.GetMqttSyncClient( );
			FormMethodCallSupport form = new FormMethodCallSupport( );
			form.SetDataNode( client, rpc, deviceCurrentSelected.DeviceFullName );
			form.ShowDialog( );
			form.Dispose( );
		}

		private async void DataInfomationControl1_onValueNameCellMouseDoubleClick( ScalarDataNode scalarDataNode, string valueNode, string valueOld )
		{
			// 点击查看数据标签的真实地址
			if (this.serverSettingsCurrent == null ) return;
			var rpc = this.serverSettingsCurrent.GetMqttSyncClient( );
			var read = await rpc.ReadRpcAsync<DeviceSingleAddressLabel>( "Admin/GetAddressLabelByTagName", new { tagName = valueNode } );
			if (!read.IsSuccess)
			{
				MessageBox.Show( this, "获取地址标签失败:" + read.Message);
			}
			else
			{
				FormShowText.ShowText( read.Content.ToJsonString( ) );
			}
		}

		private async void DataInfomationControl1_onValueCellMouseDoubleClick( ScalarDataNode scalarDataNode, string valueNode, string valueOld )
		{
			// 点击了写入数据的操作
			if (this.serverSettingsCurrent == null) return;

			FormDataInformationWriteSupport form = new FormDataInformationWriteSupport( );
			form.SetDataNode( scalarDataNode, valueNode, valueOld );
			if (form.ShowDialog( ) == DialogResult.OK)
			{
				var rpc = this.serverSettingsCurrent.GetMqttSyncClient( );
				var read = await rpc.ReadRpcAsync<string>( "Edge/WriteData",
					new
					{
						data = valueNode,
						value = form.GetDataValue( )
					} );
				if (!read.IsSuccess) 
				{ 
					MessageBox.Show( "Write Failed:" + read.Message, "写入异常", MessageBoxButtons.OK, MessageBoxIcon.Error );
				}
				else
				{
					MessageBox.Show( "Write Success!", "写入成功", MessageBoxButtons.OK, MessageBoxIcon.Information );
				}
			}
			form.Dispose( );
		}

		private void FormDeviceMonitor_FormClosing( object sender, FormClosingEventArgs e )
		{
			if (e.CloseReason == CloseReason.UserClosing)
			{
				this.Hide( );
				e.Cancel = true;
			}
		}

		#endregion

		#region Server Render

		/// <summary>
		/// 每秒触发的状态变更
		/// </summary>
		/// <param name="tickStatus">状态信息</param>
		public void EveryBoolTick( bool tickStatus )
		{
			if (deviceStatus == null) return;
			for (int i = 0; i < deviceStatus.Count; i++)
			{
				deviceStatus[i].EveryBoolTick( tickStatus );
			}
		}

		public void RenderDevices( EdgeServerSettings serverSettings, JObject statusJson )
		{
			if (serverSettings == null) return;

			label2.Text = "设备名称："          + statusJson["__name"].Value<string>( );
			label3.Text = "开始运行时间："      + statusJson["__startTime"].Value<string>( );
			label4.Text = "Version: "          + statusJson["__version"].Value<string>( );
			label8.Text = "      "             + statusJson["__deviceCount"].Value<string>( );

			serverSettings.RenderPath = new TreeNodePath( );
			deviceStatus.ForEach( m => m.Dispose( ) );
			deviceStatus.Clear( );
			Size size = new Size( 300, 103 );
			int widthCount = (panel3.Width - 7 - 18) / (size.Width + 7);
			if (widthCount == 0) widthCount = 1;
			int everyWidth = (panel3.Width - 7 - 18) / widthCount - 7;
			if (everyWidth < 300) everyWidth = 300;
			size = new Size( everyWidth, 103 );

			devicesJoTokenArray = statusJson["__deviceList"].ToArray( );
			int x = 7, y = 50;
			for (int i = 0; i < devicesJoTokenArray.Length; i++)
			{
				DeviceStatusControl statusControl = new DeviceStatusControl( Util.ViewerSettings.Theme );

				if (i != 0 && i % widthCount == 0)
				{
					x = 7;
					y = y + size.Height + 5;
				}
				statusControl.Size = size;
				statusControl.Location = new Point( x, y );
				statusControl.Parent = panel3;
				statusControl.Visible = true;
				statusControl.Tag = devicesJoTokenArray[i];
				statusControl.MouseClick += StatusControl_MouseClick;
				statusControl.DeviceFullName = devicesJoTokenArray[i]["__name"].Value<string>( );
				statusControl.OnDeviceRequestChangeClick += StatusControl_OnDeviceRequestChangeClick;

				x += size.Width + 7;

				deviceStatus.Add( statusControl );
			}

			ResetDeviceControlSelect( );
		}

		/// <summary>
		/// 清空设备的选择
		/// </summary>
		private void ResetDeviceControlSelect( )
		{
			if (this.deviceCurrentSelected != null)
			{
				this.deviceCurrentSelected.SetSelected( false );
				this.deviceCurrentSelected = null;                              // 清空设备的选择
				splitContainer2.Panel2Collapsed = true;
			}
		}

		private async void StatusControl_OnDeviceRequestChangeClick( object sender, DeviceEventArgs e )
		{
			if (this.serverSettingsCurrent == null) return;
			if (sender is DeviceStatusControl deviceStatusControl)
			{
				if (!string.IsNullOrEmpty( deviceStatusControl.DeviceFullName ))
				{
					var rpc = this.serverSettingsCurrent.GetMqttSyncClient( );
					var read = await rpc.ReadRpcAsync<string>( e.RequestEnable ? "Edge/DeviceStopRequest" :
						"Edge/DeviceContinueRequest", new { data = deviceStatusControl.DeviceFullName } );
					if (!read.IsSuccess) 
					{ 
						MessageBox.Show( $"通知 [{deviceStatusControl.DeviceFullName}] " + 
							(e.RequestEnable ? "暂停失败: " : "继续失败: " ) + read.Message );
					}
					else
					{
						MessageBox.Show( $"通知 [{deviceStatusControl.DeviceFullName}] " +
							(e.RequestEnable ? "暂停成功! " : "继续成功! ") );
					}
				}
			}
		}

		private async Task SetNewDeviceStatusControlSelect( DeviceStatusControl control, ScalarDataNode scalarDataNode, bool doubleSelectCancel, int arrayIndex = -1 )
		{
			if (this.serverSettingsCurrent == null) return;
			this.arrayIndex = arrayIndex;

			if (deviceCurrentSelected == null)
			{
				// 之前没有选择，这是一个新选择
				deviceCurrentSelected = control;
				control.SetSelected( true );

				var rpc = this.serverSettingsCurrent.GetMqttSyncClient( );
				var read = await rpc.ReadRpcAsync<ScalarDataNode[]>( "Edge/BrowseDeviceDataNodes", new { data = control.DeviceFullName } );
				if (!read.IsSuccess) { MessageBox.Show( "Failed:" + read.Message ); return; }
				this.dataInfomationControl1.RenderDataNodes( control.DeviceFullName, read.Content, scalarDataNode, this.arrayIndex );

				await TabControlSelectedIndexChanged( true );
				splitContainer2.Panel2Collapsed = false;
			}
			else if (object.ReferenceEquals( deviceCurrentSelected, control ))
			{
				if (doubleSelectCancel)
				{
					// 取消选择
					ResetDeviceControlSelect( );
				}
				else
				{
					// 切换数据显示操作
					this.dataInfomationControl1.RenderDataNodes( control.DeviceFullName, scalarDataNode, this.arrayIndex );
					if (this.deviceDataPrev != null) this.dataInfomationControl1.RenderMachineData( this.deviceDataPrev );
					await TabControlSelectedIndexChanged( true );
				}
			}
			else
			{
				// 切换选择
				deviceCurrentSelected.SetSelected( false );
				deviceCurrentSelected = control;
				control.SetSelected( true );

				var rpc = this.serverSettingsCurrent.GetMqttSyncClient( );
				var read = await rpc.ReadRpcAsync<ScalarDataNode[]>( "Edge/BrowseDeviceDataNodes", new { data = control.DeviceFullName } );
				if (!read.IsSuccess) { MessageBox.Show( "Failed:" + read.Message ); return; }

				this.dataInfomationControl1.RenderDataNodes( control.DeviceFullName, read.Content, scalarDataNode, this.arrayIndex );
				await TabControlSelectedIndexChanged( true );
			}
		}

		private async void StatusControl_MouseClick( object sender, MouseEventArgs e )
		{
			if (e.Button == MouseButtons.Left)
			{
				if (sender is DeviceStatusControl control)
				{
					await SetNewDeviceStatusControlSelect( control, null, true );
				}
			}
		}

		/// <summary>
		/// 重新双击了网关的节点后，进行重新连接，并刷新界面控件内容
		/// </summary>
		/// <param name="serverSettings">服务器的配置信息</param>
		public void SetServerSettings( EdgeServerSettings serverSettings )
		{
			this.serverSettingsCurrent?.MqttSyncClientConnectClose( );
			this.serverSettingsCurrent = serverSettings;
			SetServerSettings( serverSettings, serverSettings.RenderPath );
		}

		/// <summary>
		/// 在当前的网关选择下，点击了不同的路径，显示不同的设备名称
		/// </summary>
		/// <param name="serverSettings">当前的选择的服务器的基本信息</param>
		/// <param name="newPaths">选择的路径信息</param>
		public void SetServerSettings( EdgeServerSettings serverSettings, TreeNodePath newPaths )
		{
			if(object.ReferenceEquals( this.serverSettingsCurrent , serverSettings ))
			{
				this.serverSettingsCurrent.RenderPath = newPaths ?? new TreeNodePath( );
				// 先隐藏多余的控件显示
				List<JToken> renderList = devicesJoTokenArray.Where( m => Util.IsDeviceNameInPath( m["__name"].Value<string>( ), serverSettings.RenderPath.ActualPath ) ).ToList( );
				for (int i = deviceStatus.Count - 1; i >= 0; i--)
				{
					if (i >= renderList.Count)
					{
						deviceStatus[i].Visible = false;
					}
					else
					{
						deviceStatus[i].Visible = true;
						deviceStatus[i].DeviceFullName = renderList[i]["__name"].Value<string>( );
					}
				}
				label1.Text = serverSettings.RenderPath.GetDisplayPath( );
				hslBadge1.RightText = renderList.Count.ToString( );

				if (this.deviceCurrentSelected != null)
				{
					this.deviceCurrentSelected.SetSelected( false );
					this.deviceCurrentSelected = null;                              // 清空设备的选择
					splitContainer2.Panel2Collapsed = true;
				}
			}
		}

		/// <summary>
		/// 检测指定的路径是否和之前的路径一致，一致的话，返回True，否则返回False
		/// </summary>
		/// <param name="serverSettings">当前的选择的服务器的基本信息</param>
		/// <param name="newPaths">新的路径信息</param>
		/// <returns>是否一致</returns>
		public bool CheckRenderPathSame( EdgeServerSettings serverSettings, TreeNodePath newPaths )
		{
			if (object.ReferenceEquals( this.serverSettingsCurrent, serverSettings ))
			{
				return this.serverSettingsCurrent.RenderPath.Equals( newPaths );
			}
			return false;
		}

		/// <summary>
		/// 更改设备的选择内容，如果设备id不是当前的分类，则无效。并指示选择的数据节点，为null则不查看子数据
		/// </summary>
		/// <param name="deviceId">设备唯一的ID信息</param>
		/// <param name="scalarDataNode">选择的数据标签信息</param>
		/// <param name="arrayIndex">如果当前选择的节点数据为结构体数组，则表示选择的索引</param>
		public async Task ChangeSelectNode( string deviceId, ScalarDataNode scalarDataNode, int arrayIndex = -1 )
		{
			// 先查找deviceid是否是列表设备之一，不是的话，直接返回
			bool exsist = false;
			DeviceStatusControl statusControl = null;
			for (int i = 0; i < deviceStatus.Count; i++)
			{
				if (deviceStatus[i].Visible && deviceStatus[i].DeviceFullName == deviceId)
				{
					statusControl = deviceStatus[i];
					exsist = true;
					break;
				}
			}
			if (!exsist)
			{
				// 选择了不同路径下的设备分类信息
				string newPath = HslTechnologyExtension.GetDevicePathFromDeviceID( deviceId );
				return;
			}

			// 判断是否当前设备选择，不是则重新选择设备信息
			this.arrayIndex = arrayIndex;
			await SetNewDeviceStatusControlSelect( statusControl, scalarDataNode, false, this.arrayIndex );
		}

		public EdgeServerSettings ServerSettings => this.serverSettingsCurrent;

		private async void TabControl1_SelectedIndexChanged( object sender, EventArgs e )
		{
			await TabControlSelectedIndexChanged( false );
		}

		private async Task TabControlSelectedIndexChanged( bool isChangeDevice )
		{
			if (this.serverSettingsCurrent == null || deviceCurrentSelected == null) return;
			string deviceFullName = deviceCurrentSelected.DeviceFullName;
			if (string.IsNullOrEmpty( deviceFullName )) return;

			if (!object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_log ))
			{
				this.deviceLogControl1.RenderClose( );
			}

			if (object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_method ))
			{
				// 请求方法接口信息
				this.deviceMethodControl1.RenderDevicePath( deviceFullName );
				var client = this.serverSettingsCurrent.GetMqttSyncClient( );
				var readStatus = await client.ReadRpcAsync<MethodRpcInfo[]>( "Edge/GetMethodInfoByDeviceID", new { data = deviceFullName } );
				if (readStatus.IsSuccess)
				{
					this.deviceMethodControl1.RenderMachineMethod( readStatus.Content );
				}
			}
			else if (object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_alarm ))
			{
				// 请求了报警的信息
				var client = this.serverSettingsCurrent.GetMqttSyncClient( );
				var readAlarms = await client.ReadRpcAsync<JArray>( "Business/Alarm/GetAlarms", new { data = deviceFullName } );
				if (!readAlarms.IsSuccess)
				{
					MessageBox.Show( "服务器报警读取失败！稍后重试！" );
					this.serverSettingsCurrent = null;
				}
				else
				{
					this.deviceAlarmControl1.RenderDevicePath( deviceFullName );
					this.deviceAlarmControl1.RenderAlarm( readAlarms.Content );
				}
			}
			else if (object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_oee ))
			{
				// 请求了OEE信息
				var client = this.serverSettingsCurrent.GetMqttSyncClient( );
				var readOees = await client.ReadRpcAsync<JArray>( "Business/Oee/GetOees", new { data = deviceFullName } );
				if (!readOees.IsSuccess)
				{
					MessageBox.Show( "服务器OEE读取失败！稍后重试！" );
					this.serverSettingsCurrent = null;
				}
				else
				{
					this.deviceOeeControl1.RenderOee( deviceFullName, readOees.Content, isChangeDevice );
				}
			}
			else if (object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_offline ))
			{
				// 请求了掉线信息
				var client = this.serverSettingsCurrent.GetMqttSyncClient( );

				var readTimes = await client.ReadRpcAsync<TimeConsume[]>( "Business/Time/GetDeviceOfflineInformation", new { data = deviceFullName } );
				if (!readTimes.IsSuccess)
				{
					MessageBox.Show( "服务器离线信息读取失败！稍后重试！" );
					this.serverSettingsCurrent = null;
				}
				else
				{
					this.deviceOfflineControl1.RenderDevicePath( deviceFullName );
					this.deviceOfflineControl1.RenderMachineOffline( readTimes.Content );
				}
			}
			else if (object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_log ))
			{
				// 查看设备日志信息
				await this.deviceLogControl1.RenderDevicePath( deviceFullName, this.serverSettingsCurrent.GetMqttClient );
			}
			else if (object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_data ) ||
				object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_device ))
			{
				// 设备信息和数据信息本来就是在一直请求的
				//	var client = this.serverSettingsCurrent.GetMqttSyncClient( );

					//	var readDevice = await client.ReadRpcAsync<JObject>( "Edge/DeviceData", new { data = deviceFullName } );
					//	if (!readDevice.IsSuccess)
					//	{
					//		MessageBox.Show( "服务器设备数据读取失败！稍后重试！" );
					//		this.serverSettingsCurrent = null;
					//	}
					//	else
					//	{

					//	}
			}
		}

		private int edgeServerStatus = -1;
		private void SetEdgeServerStatus( int status, string msg )
		{
			if(edgeServerStatus != status)
			{
				if(status == 1) 
					logNet?.WriteInfo( $"Edge[{ this.serverSettingsCurrent.Alias}]: {msg}" );
				else
					logNet?.WriteDebug( $"Edge[{ this.serverSettingsCurrent.Alias}]: {msg}" );
			}
			edgeServerStatus = status;
		}
		private async void ThreadReadServer( )
		{
			HslTimerTick hslTimerTick = new HslTimerTick( );
			while (true)
			{
				Thread.Sleep( 20 );
				if (!hslTimerTick.IsTickHappen( DateTime.Now )) continue;

				if (this.serverSettingsCurrent != null)
				{
					var client = this.serverSettingsCurrent.GetMqttSyncClient( );
					var readStatus = await client.ReadRpcAsync<JObject>( "Edge/DeviceData", new { data = "__status" } );
					if (!readStatus.IsSuccess)
					{
						SetEdgeServerStatus( 0, $"读取服务器数据失败 -> {readStatus.Message}" );
						Invoke( new Action( ( ) =>
						{
							hslLanternSimple1.LanternBackground = Color.Tomato;
							UpdateEdgeStatus?.Invoke( this.serverSettingsCurrent.Alias, false, readStatus.Message );
						} ) );
						this.serverSettingsCurrent = null;
						continue;
					}
					Invoke( new Action( ( ) =>
					{
						UpdateEdgeStatus?.Invoke( this.serverSettingsCurrent.Alias, true, string.Empty );
					} ) );
					SetEdgeServerStatus( 1, $"读取服务器数据成功！" );
					var readDevice = await client.ReadRpcAsync<JObject>( "Edge/DeviceData", new { data = this.serverSettingsCurrent.RenderPath.GetActualPath( ) } );
					if (!readDevice.IsSuccess)
					{
						MessageBox.Show( $"服务器设备数据读取失败！稍后重试！{Environment.NewLine}原因：{readDevice.Message}{Environment.NewLine}API:{this.serverSettingsCurrent.RenderPath.GetActualPath( )}" );
						this.serverSettingsCurrent = null;
						continue;
					}

					JObject statusJson = readStatus.Content;
					// 可能刚启动的时候，值还没有赋值
					if (statusJson["__deviceList"] == null)
					{
						MessageBox.Show( "服务器设备数据读取未发现有效设备列表数据！可能设备刚刚启动，稍后重试！" );
						this.serverSettingsCurrent = null;
						continue;
					}

					List<JToken> devices = statusJson["__deviceList"].Where( m => this.serverSettingsCurrent.RenderPath.IsDeviceNameInPath( m["__name"].Value<string>( ) ) ).ToList( );
					try
					{
						Invoke( new Action( ( ) =>
						{
							int __deviceCount       = statusJson["__deviceCount"].Value<int>( );
							int __deviceOnlineCount = statusJson["__deviceOnlineCount"].Value<int>( );
							label_device_run.Text             = "      " + __deviceOnlineCount;
							label_device_error.Text            = "      " + (__deviceCount - __deviceOnlineCount);
							if (statusJson.ContainsKey( "__deviceStopCount" ))                                       // 兼容新版本的客户端连接旧的服务器
							label_device_stop.Text = "      " + statusJson["__deviceStopCount"].Value<int>( );

							long alarmCount = 0;
							for (int i = 0; i < devices.Count; i++)
							{
								if (i < deviceStatus.Count)
								{
								// 这里会报错：
								// System.InvalidCastException: 无法将类型为“Newtonsoft.Json.Linq.JValue”的对象强制转换为类型“Newtonsoft.Json.Linq.JObject”。
								// 在 HslTechnology.EdgeViewer.Forms.FormDeviceMonitor.<>c__DisplayClass17_0.<ThreadReadServer>b__2()
								// 位置 F:\HslProjects\HslTechnology\HslTechnology.EdgeViewer\Forms\FormDeviceMonitor.cs:行号 475
								JObject content = (JObject)readDevice.Content[devices[i].Value<string>( "__name" )];
									deviceStatus[i].LoadDeviceData( content, toolTip1 );
									alarmCount += deviceStatus[i].AlarmCount;
									if (deviceCurrentSelected != null)
									{
										if (deviceCurrentSelected.DeviceFullName == devices[i].Value<string>( "__name" ))
										{
											this.deviceDataPrev = content;
											this.dataInfomationControl1.RenderMachineData( content );
											this.deviceInfoControl1.RenderDevicePath( deviceCurrentSelected.DeviceFullName );
											this.deviceInfoControl1.RenderMachineData( content );
										}
									}
								}
							}
							hslBadge2.RightText = alarmCount.ToString( );
						} ) );

					}
					catch(Exception ex)
					{
						SoftBasic.ShowExceptionMessage( ex );
					}

					try
					{
						Invoke( new Action( UpdateDeviceUI ) );
					}
					catch
					{

					}


					//Invoke( new Func<Task>( ( ) =>
					// {
					//	 if (!object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_method ))
					//		 return TabControlSelectedIndexChanged( );
					//	 else
					//		 return null;
					// } ) );
				}
				else
				{

				}
			}
		}

		private async void UpdateDeviceUI( )
		{
			if (!object.ReferenceEquals( this.tabControl1.SelectedTab, this.tabPage_method ))
				await TabControlSelectedIndexChanged( false );
		}


		public void DataGridSpecifyRowCount( int row )
		{
			this.dataInfomationControl1.DataGridSpecifyRowCount( row );
		}

		public Action<string,bool,string> UpdateEdgeStatus { get; set; }

		#endregion

		#region Private Member

		private List<DeviceStatusControl> deviceStatus;
		private EdgeServerSettings serverSettingsCurrent = null;                    // 当前绑定的网关的配置信息
		private Thread threadUpdateDeviceStatus;                                    // 后台更新设备状态的线程
		private ILogNet logNet;                                                     // 客户端统一的事务日志对象
		private JToken[] devicesJoTokenArray;                                       // 当前所有的设备列表数组
		private DeviceStatusControl deviceCurrentSelected;                          // 当前选择的设备对象
		private JObject deviceDataPrev;                                             // 最新的设备数据缓存
		private int arrayIndex = -1;                                                // 当为结构体数组时，队列的索引值
		private FormMain formMain;                                                  // 主窗体的对象
		private bool eventLoaded = false;

		#endregion

	}
}
