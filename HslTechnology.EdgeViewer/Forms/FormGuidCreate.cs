using HslTechnology.EdgeViewer.Controls;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormGuidCreate : HslPage
	{
		public FormGuidCreate( )
		{
			InitializeComponent( );
			this.Icon = Util.GetWinformIcon( );
		}

		private void FormGuidCreate_Load( object sender, EventArgs e )
		{
			Guid guid = Guid.NewGuid( );
			radioButton_N.Text = "N example: " + guid.ToString( "N" );
			radioButton_D.Text = "D example: " + guid.ToString( "D" );
			radioButton_B.Text = "B example: " + guid.ToString( "B" );
			radioButton_P.Text = "P example: " + guid.ToString( "P" );
			radioButton_X.Text = "X example: " + guid.ToString( "X" );
		}

		private void button2_Click( object sender, EventArgs e )
		{
			// calculate guid
			Guid guid = Guid.NewGuid( );
			string str = string.Empty;
			if(radioButton_B.Checked) str = guid.ToString( "B" );
			else if (radioButton_P.Checked) str = guid.ToString( "P" );
			else if (radioButton_X.Checked) str = guid.ToString( "X" );
			else if (radioButton_N.Checked) str = guid.ToString( "N" );
			else if (radioButton_D.Checked) str = guid.ToString( "D" );

			if (checkBox1.Checked) str = str.ToUpper( );
			if(guids.Count == 0)
				guids.Add( str );
			else
				guids.Insert( 0, str );
			if (guids.Count > 30)
			{
				guids.RemoveAt( guids.Count - 1 );
			}
			comboBox1.DataSource = guids.ToArray( );
			comboBox1.Text = str;
		}

		private List<string> guids = new List<string>( );

		private void button1_Click( object sender, EventArgs e )
		{
			try
			{
				Clipboard.SetText( comboBox1.Text );
				MessageBox.Show( "复制到剪贴板成功！" );
			}
			catch(Exception ex)
			{
				MessageBox.Show( "复制到剪贴板失败：" + ex.Message );
			}
		}
	}
}
