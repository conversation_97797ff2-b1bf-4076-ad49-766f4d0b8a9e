using HslCommunication.MQTT;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication;
using Newtonsoft.Json.Linq;
using HslTechnology.EdgeViewer;
using HslTechnology.EdgeViewer.Controls;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class FormMethodCallSupport : HslForm
	{
		public FormMethodCallSupport( )
		{
			InitializeComponent( );
		}

		public void SetDataNode( MqttSyncClient client, MethodRpcInfo methodRpc, string deviceId )
		{
			this.client = client;
			this.methodRpc = methodRpc;
			this.deviceId = deviceId;
			textBox1.Text = deviceId;
			textBox2.Text = methodRpc.RpcApiInfo.ApiTopic;
			textBox3.Text = methodRpc.RpcApiInfo.ExamplePayload;
			label7.Text = methodRpc.RpcApiInfo.MethodSignature;
			label8.Text = methodRpc.RpcApiInfo.Description;
		}



		private MqttSyncClient client;
		private MethodRpcInfo methodRpc;
		private string deviceId;

		private void FormDataInformationWriteSupport_Load( object sender, EventArgs e )
		{

		}


		private void FormMethodCallSupport_Shown( object sender, EventArgs e )
		{
			textBox4.Focus( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			this.DialogResult = DialogResult.Cancel;
		}

		private async void buttonOk1_Click( object sender, EventArgs e )
		{
			buttonOk1.Enabled = false;
			try
			{
				JObject json = new JObject( );
				json.Add( "data", new JValue( this.deviceId ) );
				json.Add( "method", new JValue( this.methodRpc.RpcApiInfo.ApiTopic ) );
				json.Add( "parameterJson", new JValue( textBox3.Text ) );
				DateTime start = DateTime.Now;
				OperateResult<string, string> read = await client.ReadStringAsync( "Edge/CallDeviceMethod", json.ToString( ) );
				label_timecost.Text = "TimeCost: " + (DateTime.Now - start).TotalMilliseconds.ToString( "F1" ) + " ms";
				if (!read.IsSuccess)
				{
					MessageBox.Show( "Read Failed: " + read.Message );
				}
				else
				{
					textBox4.Text = read.Content2;
					if (string.IsNullOrEmpty( read.Content2 ))
					{
						textBox4.Text = "[Called Success]";
					}
				}
			}
			catch (Exception ex)
			{
				HslCommunication.BasicFramework.SoftBasic.ShowExceptionMessage( ex );
			}
			finally
			{
				buttonOk1.Enabled = true;
			}
		}

	}
}
