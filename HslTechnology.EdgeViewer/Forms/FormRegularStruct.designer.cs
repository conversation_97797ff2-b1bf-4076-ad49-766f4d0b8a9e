using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
    partial class FormRegularStruct
	{
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose( bool disposing )
        {
            if (disposing && (components != null))
            {
                components.Dispose( );
            }
            base.Dispose( disposing );
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent( )
        {
            this.textBox_length = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.comboBox_struct = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.edgeLabel2 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
            this.edgeLabel1 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
            this.checkBox_isArray = new System.Windows.Forms.CheckBox();
            this.buttonOk1 = new HslTechnology.Edge.Controls.ButtonOk();
            this.buttonCancel1 = new HslTechnology.Edge.Controls.ButtonCancel();
            this.label6 = new System.Windows.Forms.Label();
            this.edgeLabel3 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.radioButton_unfold = new System.Windows.Forms.RadioButton();
            this.radioButton_fullName = new System.Windows.Forms.RadioButton();
            this.radioButton_struct = new System.Windows.Forms.RadioButton();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // textBox_length
            // 
            this.textBox_length.Location = new System.Drawing.Point(301, 187);
            this.textBox_length.Name = "textBox_length";
            this.textBox_length.Size = new System.Drawing.Size(105, 23);
            this.textBox_length.TabIndex = 56;
            this.textBox_length.Text = "1";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(224, 190);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(68, 17);
            this.label5.TabIndex = 55;
            this.label5.Text = "数组长度：";
            // 
            // comboBox_struct
            // 
            this.comboBox_struct.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox_struct.FormattingEnabled = true;
            this.comboBox_struct.Location = new System.Drawing.Point(89, 185);
            this.comboBox_struct.Name = "comboBox_struct";
            this.comboBox_struct.Size = new System.Drawing.Size(121, 25);
            this.comboBox_struct.TabIndex = 54;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(12, 188);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(80, 17);
            this.label4.TabIndex = 53;
            this.label4.Text = "结构体名称：";
            // 
            // textBox3
            // 
            this.textBox3.Location = new System.Drawing.Point(89, 149);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(203, 23);
            this.textBox3.TabIndex = 52;
            this.textBox3.Text = "0";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(12, 152);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(68, 17);
            this.label3.TabIndex = 51;
            this.label3.Text = "字节索引：";
            // 
            // textBox2
            // 
            this.textBox2.Location = new System.Drawing.Point(89, 80);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(320, 23);
            this.textBox2.TabIndex = 50;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 83);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(68, 17);
            this.label2.TabIndex = 49;
            this.label2.Text = "节点备注：";
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(89, 48);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(320, 23);
            this.textBox1.TabIndex = 48;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 51);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(68, 17);
            this.label1.TabIndex = 47;
            this.label1.Text = "节点名称：";
            // 
            // button2
            // 
            this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.button2.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Critical_16xLG_color;
            this.button2.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.button2.Location = new System.Drawing.Point(-212, 298);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(143, 43);
            this.button2.TabIndex = 68;
            this.button2.Text = " 取消";
            this.button2.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.button2.UseVisualStyleBackColor = true;
            // 
            // button1
            // 
            this.button1.BackColor = System.Drawing.Color.Transparent;
            this.button1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color;
            this.button1.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.button1.Location = new System.Drawing.Point(-212, 249);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(143, 43);
            this.button1.TabIndex = 67;
            this.button1.Text = " 确认";
            this.button1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.button1.UseVisualStyleBackColor = false;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // edgeLabel2
            // 
            this.edgeLabel2.BackColor = System.Drawing.Color.DodgerBlue;
            this.edgeLabel2.ForeColor = System.Drawing.Color.White;
            this.edgeLabel2.Location = new System.Drawing.Point(12, 115);
            this.edgeLabel2.Name = "edgeLabel2";
            this.edgeLabel2.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.edgeLabel2.Size = new System.Drawing.Size(397, 22);
            this.edgeLabel2.TabIndex = 62;
            this.edgeLabel2.Text = "解析地址信息";
            this.edgeLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // edgeLabel1
            // 
            this.edgeLabel1.BackColor = System.Drawing.Color.DodgerBlue;
            this.edgeLabel1.ForeColor = System.Drawing.Color.White;
            this.edgeLabel1.Location = new System.Drawing.Point(12, 14);
            this.edgeLabel1.Name = "edgeLabel1";
            this.edgeLabel1.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.edgeLabel1.Size = new System.Drawing.Size(397, 22);
            this.edgeLabel1.TabIndex = 61;
            this.edgeLabel1.Text = "节点信息";
            this.edgeLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // checkBox_isArray
            // 
            this.checkBox_isArray.AutoSize = true;
            this.checkBox_isArray.Location = new System.Drawing.Point(331, 151);
            this.checkBox_isArray.Name = "checkBox_isArray";
            this.checkBox_isArray.Size = new System.Drawing.Size(75, 21);
            this.checkBox_isArray.TabIndex = 71;
            this.checkBox_isArray.Text = "是否数组";
            this.checkBox_isArray.UseVisualStyleBackColor = true;
            // 
            // buttonOk1
            // 
            this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonOk1.Location = new System.Drawing.Point(74, 337);
            this.buttonOk1.Name = "buttonOk1";
            this.buttonOk1.Size = new System.Drawing.Size(121, 36);
            this.buttonOk1.TabIndex = 74;
            this.buttonOk1.Text = "00";
            this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
            // 
            // buttonCancel1
            // 
            this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonCancel1.Location = new System.Drawing.Point(219, 337);
            this.buttonCancel1.Name = "buttonCancel1";
            this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
            this.buttonCancel1.TabIndex = 75;
            this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.ForeColor = System.Drawing.Color.Red;
            this.label6.Location = new System.Drawing.Point(6, 381);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(392, 17);
            this.label6.TabIndex = 76;
            this.label6.Text = "注意：当本地结构体和全局结构体名称冲突时，优先选择本地的结构体。";
            // 
            // edgeLabel3
            // 
            this.edgeLabel3.BackColor = System.Drawing.Color.DodgerBlue;
            this.edgeLabel3.ForeColor = System.Drawing.Color.White;
            this.edgeLabel3.Location = new System.Drawing.Point(12, 225);
            this.edgeLabel3.Name = "edgeLabel3";
            this.edgeLabel3.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.edgeLabel3.Size = new System.Drawing.Size(397, 22);
            this.edgeLabel3.TabIndex = 77;
            this.edgeLabel3.Text = "数据解析方式";
            this.edgeLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.radioButton_unfold);
            this.panel1.Controls.Add(this.radioButton_fullName);
            this.panel1.Controls.Add(this.radioButton_struct);
            this.panel1.Location = new System.Drawing.Point(10, 254);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(398, 74);
            this.panel1.TabIndex = 78;
            // 
            // radioButton_unfold
            // 
            this.radioButton_unfold.AutoSize = true;
            this.radioButton_unfold.Location = new System.Drawing.Point(5, 50);
            this.radioButton_unfold.Name = "radioButton_unfold";
            this.radioButton_unfold.Size = new System.Drawing.Size(254, 21);
            this.radioButton_unfold.TabIndex = 2;
            this.radioButton_unfold.Text = "展开，使用属性名 (名称相同可能存在覆盖)";
            this.radioButton_unfold.UseVisualStyleBackColor = true;
            // 
            // radioButton_fullName
            // 
            this.radioButton_fullName.AutoSize = true;
            this.radioButton_fullName.Location = new System.Drawing.Point(5, 27);
            this.radioButton_fullName.Name = "radioButton_fullName";
            this.radioButton_fullName.Size = new System.Drawing.Size(193, 21);
            this.radioButton_fullName.TabIndex = 1;
            this.radioButton_fullName.Text = "展开，使用结构体+ \'.\' +属性名";
            this.radioButton_fullName.UseVisualStyleBackColor = true;
            // 
            // radioButton_struct
            // 
            this.radioButton_struct.AutoSize = true;
            this.radioButton_struct.Checked = true;
            this.radioButton_struct.Location = new System.Drawing.Point(5, 3);
            this.radioButton_struct.Name = "radioButton_struct";
            this.radioButton_struct.Size = new System.Drawing.Size(242, 21);
            this.radioButton_struct.TabIndex = 0;
            this.radioButton_struct.TabStop = true;
            this.radioButton_struct.Text = "不展开，使用结构体对象，一个json对象";
            this.radioButton_struct.UseVisualStyleBackColor = true;
            // 
            // FormRegularStruct
            // 
            this.AcceptButton = this.button1;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.button2;
            this.ClientSize = new System.Drawing.Size(422, 404);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.edgeLabel3);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.buttonCancel1);
            this.Controls.Add(this.buttonOk1);
            this.Controls.Add(this.checkBox_isArray);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.edgeLabel2);
            this.Controls.Add(this.edgeLabel1);
            this.Controls.Add(this.textBox_length);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.comboBox_struct);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.textBox3);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.textBox2);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.label1);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.Name = "FormRegularStruct";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "新增结构体数据信息";
            this.Load += new System.EventHandler(this.FormRegularItemNode_Load);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.TextBox textBox_length;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ComboBox comboBox_struct;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox textBox3;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox textBox2;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox textBox1;
        private System.Windows.Forms.Label label1;
        private Controls.EdgeLabel edgeLabel1;
        private Controls.EdgeLabel edgeLabel2;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Button button1;
		private System.Windows.Forms.CheckBox checkBox_isArray;
		private ButtonOk buttonOk1;
		private ButtonCancel buttonCancel1;
        private System.Windows.Forms.Label label6;
		private Controls.EdgeLabel edgeLabel3;
		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.RadioButton radioButton_unfold;
		private System.Windows.Forms.RadioButton radioButton_fullName;
		private System.Windows.Forms.RadioButton radioButton_struct;
	}
}