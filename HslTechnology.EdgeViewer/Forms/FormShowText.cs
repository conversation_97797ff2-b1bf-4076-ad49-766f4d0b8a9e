using HslTechnology.Edge.Controls;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormShowText : HslForm
	{
		public FormShowText( string text )
		{
			InitializeComponent( );
			Icon = Util.GetWinformIcon( );
			this._text = text;
		}


		private void FormShowText_Load( object sender, EventArgs e )
		{
			textBox1.Text = _text;
			//Text = Text + $"[{_size.Width},{_size.Height}]";

			Graphics g = textBox1.CreateGraphics( );
			_size = g.MeasureString( this._text, this.Font );
			if(_size.Width > 800f)
				_size = g.MeasureString( this._text, this.Font, 800 );
			this.Width = Math.Min( (int)_size.Width + 50, 800 );
			this.Height = Math.Min( (int)(_size.Height * 1.1f) + 50, 500 );
		}


		private string _text;
		private SizeF _size;


		public static DialogResult ShowText( string text )
		{
			using (FormShowText formShowText = new FormShowText( text ))
			{
				formShowText.StartPosition = FormStartPosition.CenterParent;
				return formShowText.ShowDialog( );
			}
		}

		public static DialogResult ShowText( string text, string title )
		{
			using (FormShowText formShowText = new FormShowText( text ))
			{
				formShowText.StartPosition = FormStartPosition.CenterParent;
				formShowText.Text = title;
				return formShowText.ShowDialog( );
			}
		}
	}
}
