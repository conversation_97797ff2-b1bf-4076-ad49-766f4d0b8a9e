
namespace HslTechnology.EdgeViewer.Forms
{
	partial class FormEdgeServers
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.edgeInfoToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editEdgeSettingToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editDevicesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editConnectToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.pluginsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.deleteDevicesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.restartEdgeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.shutDownToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.viewLogToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.updateEdgeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.addServerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.reloadServerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.treeViewEx1 = new HslTechnology.EdgeViewer.TreeViewEx();
            this.contextMenuStrip_path = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.allDeviceStopRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.allDeviceContinueRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip1.SuspendLayout();
            this.menuStrip1.SuspendLayout();
            this.contextMenuStrip_path.SuspendLayout();
            this.SuspendLayout();
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.edgeInfoToolStripMenuItem,
            this.editEdgeSettingToolStripMenuItem,
            this.editDevicesToolStripMenuItem,
            this.editConnectToolStripMenuItem,
            this.pluginsToolStripMenuItem,
            this.deleteDevicesToolStripMenuItem,
            this.restartEdgeToolStripMenuItem,
            this.shutDownToolStripMenuItem,
            this.viewLogToolStripMenuItem,
            this.updateEdgeToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(149, 224);
            // 
            // edgeInfoToolStripMenuItem
            // 
            this.edgeInfoToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Monitor_Screen_16xLG;
            this.edgeInfoToolStripMenuItem.Name = "edgeInfoToolStripMenuItem";
            this.edgeInfoToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.edgeInfoToolStripMenuItem.Text = "网关信息监控";
            // 
            // editEdgeSettingToolStripMenuItem
            // 
            this.editEdgeSettingToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Lock;
            this.editEdgeSettingToolStripMenuItem.Name = "editEdgeSettingToolStripMenuItem";
            this.editEdgeSettingToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.editEdgeSettingToolStripMenuItem.Text = "编辑设备参数";
            // 
            // editDevicesToolStripMenuItem
            // 
            this.editDevicesToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.EditDocument;
            this.editDevicesToolStripMenuItem.Name = "editDevicesToolStripMenuItem";
            this.editDevicesToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.editDevicesToolStripMenuItem.Text = "编辑采集信息";
            // 
            // editConnectToolStripMenuItem
            // 
            this.editConnectToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Online;
            this.editConnectToolStripMenuItem.Name = "editConnectToolStripMenuItem";
            this.editConnectToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.editConnectToolStripMenuItem.Text = "编辑连接信息";
            // 
            // pluginsToolStripMenuItem
            // 
            this.pluginsToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Library;
            this.pluginsToolStripMenuItem.Name = "pluginsToolStripMenuItem";
            this.pluginsToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.pluginsToolStripMenuItem.Text = "插件管理";
            // 
            // deleteDevicesToolStripMenuItem
            // 
            this.deleteDevicesToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_Cancel_16xLG;
            this.deleteDevicesToolStripMenuItem.Name = "deleteDevicesToolStripMenuItem";
            this.deleteDevicesToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.deleteDevicesToolStripMenuItem.Text = "删除设备";
            // 
            // restartEdgeToolStripMenuItem
            // 
            this.restartEdgeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Refresh;
            this.restartEdgeToolStripMenuItem.Name = "restartEdgeToolStripMenuItem";
            this.restartEdgeToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.restartEdgeToolStripMenuItem.Text = "重启设备";
            // 
            // shutDownToolStripMenuItem
            // 
            this.shutDownToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.ShutDown;
            this.shutDownToolStripMenuItem.Name = "shutDownToolStripMenuItem";
            this.shutDownToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.shutDownToolStripMenuItem.Text = "关闭设备";
            // 
            // viewLogToolStripMenuItem
            // 
            this.viewLogToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Log;
            this.viewLogToolStripMenuItem.Name = "viewLogToolStripMenuItem";
            this.viewLogToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.viewLogToolStripMenuItem.Text = "查看日志";
            // 
            // updateEdgeToolStripMenuItem
            // 
            this.updateEdgeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.CloudDownload;
            this.updateEdgeToolStripMenuItem.Name = "updateEdgeToolStripMenuItem";
            this.updateEdgeToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.updateEdgeToolStripMenuItem.Text = "更新程序";
            // 
            // menuStrip1
            // 
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addServerToolStripMenuItem,
            this.reloadServerToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new System.Drawing.Size(241, 24);
            this.menuStrip1.TabIndex = 7;
            this.menuStrip1.Text = "menuStrip1";
            // 
            // addServerToolStripMenuItem
            // 
            this.addServerToolStripMenuItem.AutoSize = false;
            this.addServerToolStripMenuItem.AutoToolTip = true;
            this.addServerToolStripMenuItem.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.addServerToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addServerToolStripMenuItem.Name = "addServerToolStripMenuItem";
            this.addServerToolStripMenuItem.Size = new System.Drawing.Size(20, 20);
            this.addServerToolStripMenuItem.Text = "新增一个边缘服务器设备";
            this.addServerToolStripMenuItem.ToolTipText = "新增一个边缘服务器设备";
            // 
            // reloadServerToolStripMenuItem
            // 
            this.reloadServerToolStripMenuItem.AutoSize = false;
            this.reloadServerToolStripMenuItem.AutoToolTip = true;
            this.reloadServerToolStripMenuItem.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.reloadServerToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StandardRefresh_16xLG;
            this.reloadServerToolStripMenuItem.Name = "reloadServerToolStripMenuItem";
            this.reloadServerToolStripMenuItem.Size = new System.Drawing.Size(20, 20);
            this.reloadServerToolStripMenuItem.Text = "刷新当前的网关设备列表的在线信息";
            this.reloadServerToolStripMenuItem.ToolTipText = "刷新当前的网关设备列表的在线信息";
            // 
            // treeViewEx1
            // 
            this.treeViewEx1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.treeViewEx1.Location = new System.Drawing.Point(4, 30);
            this.treeViewEx1.Name = "treeViewEx1";
            this.treeViewEx1.Size = new System.Drawing.Size(233, 443);
            this.treeViewEx1.TabIndex = 4;
            // 
            // contextMenuStrip_path
            // 
            this.contextMenuStrip_path.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.allDeviceStopRequestToolStripMenuItem,
            this.allDeviceContinueRequestToolStripMenuItem});
            this.contextMenuStrip_path.Name = "contextMenuStrip_path";
            this.contextMenuStrip_path.Size = new System.Drawing.Size(149, 48);
            // 
            // allDeviceStopRequestToolStripMenuItem
            // 
            this.allDeviceStopRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Pause;
            this.allDeviceStopRequestToolStripMenuItem.Name = "allDeviceStopRequestToolStripMenuItem";
            this.allDeviceStopRequestToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.allDeviceStopRequestToolStripMenuItem.Text = "设备暂停请求";
            // 
            // allDeviceContinueRequestToolStripMenuItem
            // 
            this.allDeviceContinueRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Play;
            this.allDeviceContinueRequestToolStripMenuItem.Name = "allDeviceContinueRequestToolStripMenuItem";
            this.allDeviceContinueRequestToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.allDeviceContinueRequestToolStripMenuItem.Text = "设备继续请求";
            // 
            // FormEdgeServers
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.Lavender;
            this.ClientSize = new System.Drawing.Size(241, 477);
            this.Controls.Add(this.menuStrip1);
            this.Controls.Add(this.treeViewEx1);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.MainMenuStrip = this.menuStrip1;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FormEdgeServers";
            this.Text = "FormEdgeServers";
            this.Load += new System.EventHandler(this.FormEdgeServers_Load);
            this.contextMenuStrip1.ResumeLayout(false);
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.contextMenuStrip_path.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		#endregion
		private TreeViewEx treeViewEx1;
		private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
		private System.Windows.Forms.ToolStripMenuItem editEdgeSettingToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem editDevicesToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem deleteDevicesToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem restartEdgeToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem viewLogToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem updateEdgeToolStripMenuItem;
		private System.Windows.Forms.MenuStrip menuStrip1;
		private System.Windows.Forms.ToolStripMenuItem addServerToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem edgeInfoToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem shutDownToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem pluginsToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem editConnectToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem reloadServerToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip_path;
        private System.Windows.Forms.ToolStripMenuItem allDeviceStopRequestToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem allDeviceContinueRequestToolStripMenuItem;
    }
}