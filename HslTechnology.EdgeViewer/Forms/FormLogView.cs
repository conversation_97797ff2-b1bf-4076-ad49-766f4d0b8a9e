using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication.Enthernet;
using HslTechnology.Edge.Config;
using HslCommunication.LogNet;
using WeifenLuo.WinFormsUI.Docking;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.EdgeViewer.Core;
using HslCommunication.MQTT;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormLogView : HslPage
	{
		#region Constructor

		public FormLogView( EdgeServerSettings serverSettings )
		{
			InitializeComponent( );
			this.Icon = Util.GetWinformIcon( );
			this.serverSettings = serverSettings;
		}

		public FormLogView( ILogNet logNet )
		{
			InitializeComponent( );
			Icon = Util.GetWinformIcon( );
			this.logNet = logNet;
		}

		#endregion

		private void FormLogView_Load( object sender, EventArgs e )
		{
			if (serverSettings != null)
			{
				mqttClient?.ConnectClose( );
				mqttClient                             =  this.serverSettings.GetMqttClient( );
				mqttClient.OnMqttMessageReceived       += MqttClient_OnMqttMessageReceived;
				HslCommunication.OperateResult connect =  mqttClient.ConnectServer( );
				this.FormClosing                       += FormLogView_FormClosing;
				if (connect.IsSuccess)
				{
					mqttClient.SubscribeMessage( "__log" );
				}
				else
				{
					textBox1.AppendText( "Failed:" + connect.Message + Environment.NewLine );
				}
			}

			if(this.logNet != null)
			{
				this.FormClosing             += FormLogView_FormClosing;
				this.logNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;
			}
		}

		private void LogNet_BeforeSaveToFile( object sender, HslEventArgs e )
		{
			try
			{
				Invoke( new Action( ( ) =>
				{
					if (hslButton1.Text == "暂停")
						textBox1.AppendText( e.HslMessage.ToString( ) + Environment.NewLine );
				} ) );
			}
			catch
			{
				return;
			}
		}

		private void MqttClient_OnMqttMessageReceived( HslCommunication.MQTT.MqttClient mqttClient, MqttApplicationMessage message )
		{
			try
			{
				Invoke( new Action( ( ) =>
				{
					if (hslButton1.Text == "暂停")
						textBox1.AppendText( Encoding.UTF8.GetString( message.Payload ) + Environment.NewLine );
				} ) );
			}
			catch
			{
				return;
			}
		}

		private void FormLogView_FormClosing( object sender, FormClosingEventArgs e )
		{
			mqttClient?.ConnectClose( );
			if (this.logNet != null) 
			{
				if (e.CloseReason == CloseReason.UserClosing)
				{
					this.DockState = DockState.DockBottomAutoHide;
					e.Cancel = true;
				}
			}
		}

		private EdgeServerSettings serverSettings = null;
		private HslCommunication.MQTT.MqttClient mqttClient = null;
		private ILogNet logNet;

		private void HslButton1_Click( object sender, EventArgs e )
		{
			if (hslButton1.Text == "暂停")
			{
				hslButton1.Text = "继续";
			}
			else
			{
				hslButton1.Text = "暂停";
			}
		}

	}
}
