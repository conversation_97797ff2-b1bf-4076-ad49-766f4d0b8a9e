using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
    partial class FormRegularItemNode
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose( bool disposing )
        {
            if (disposing && (components != null))
            {
                components.Dispose( );
            }
            base.Dispose( disposing );
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent( )
        {
			this.textBox_length = new System.Windows.Forms.TextBox();
			this.label5 = new System.Windows.Forms.Label();
			this.comboBox_type = new System.Windows.Forms.ComboBox();
			this.label4 = new System.Windows.Forms.Label();
			this.textBox3 = new System.Windows.Forms.TextBox();
			this.label3 = new System.Windows.Forms.Label();
			this.textBox_description = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.textBox_name = new System.Windows.Forms.TextBox();
			this.label1 = new System.Windows.Forms.Label();
			this.button2 = new System.Windows.Forms.Button();
			this.button1 = new System.Windows.Forms.Button();
			this.regularStringControl1 = new HslTechnology.EdgeViewer.Controls.NodeSettings.RegularStringControl();
			this.requestDataScaleControl1 = new HslTechnology.EdgeViewer.Controls.NodeSettings.RequestDataScaleControl();
			this.edgeLabel2 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.edgeLabel1 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.checkBox_isArray = new System.Windows.Forms.CheckBox();
			this.comboBox_alarm = new System.Windows.Forms.ComboBox();
			this.checkBox_alarm = new System.Windows.Forms.CheckBox();
			this.edgeLabel4 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.comboBox_oee = new System.Windows.Forms.ComboBox();
			this.checkBox_oee = new System.Windows.Forms.CheckBox();
			this.checkBox_subscription = new System.Windows.Forms.CheckBox();
			this.checkBox_forbidRemoteWrite = new System.Windows.Forms.CheckBox();
			this.label6 = new System.Windows.Forms.Label();
			this.label7 = new System.Windows.Forms.Label();
			this.buttonOk1 = new HslTechnology.Edge.Controls.ButtonOk();
			this.buttonCancel1 = new HslTechnology.Edge.Controls.ButtonCancel();
			this.regularBCDControl1 = new HslTechnology.EdgeViewer.Controls.NodeSettings.RegularBCDControl();
			this.textBox_displayName = new System.Windows.Forms.TextBox();
			this.label8 = new System.Windows.Forms.Label();
			this.label9 = new System.Windows.Forms.Label();
			this.textBox_unit = new System.Windows.Forms.TextBox();
			this.label10 = new System.Windows.Forms.Label();
			this.textBox_stringLength = new System.Windows.Forms.TextBox();
			this.SuspendLayout();
			// 
			// textBox_length
			// 
			this.textBox_length.Location = new System.Drawing.Point(355, 175);
			this.textBox_length.Name = "textBox_length";
			this.textBox_length.Size = new System.Drawing.Size(54, 23);
			this.textBox_length.TabIndex = 56;
			this.textBox_length.Text = "1";
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(287, 178);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(68, 17);
			this.label5.TabIndex = 55;
			this.label5.Text = "数组长度：";
			// 
			// comboBox_type
			// 
			this.comboBox_type.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_type.FormattingEnabled = true;
			this.comboBox_type.Location = new System.Drawing.Point(89, 225);
			this.comboBox_type.Name = "comboBox_type";
			this.comboBox_type.Size = new System.Drawing.Size(108, 25);
			this.comboBox_type.TabIndex = 54;
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(12, 229);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(68, 17);
			this.label4.TabIndex = 53;
			this.label4.Text = "数据类型：";
			// 
			// textBox3
			// 
			this.textBox3.Location = new System.Drawing.Point(89, 175);
			this.textBox3.Name = "textBox3";
			this.textBox3.Size = new System.Drawing.Size(108, 23);
			this.textBox3.TabIndex = 52;
			this.textBox3.Text = "0";
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(12, 178);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(68, 17);
			this.label3.TabIndex = 51;
			this.label3.Text = "字节索引：";
			// 
			// textBox_description
			// 
			this.textBox_description.Location = new System.Drawing.Point(89, 108);
			this.textBox_description.Name = "textBox_description";
			this.textBox_description.Size = new System.Drawing.Size(320, 23);
			this.textBox_description.TabIndex = 50;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(12, 111);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(68, 17);
			this.label2.TabIndex = 49;
			this.label2.Text = "节点备注：";
			// 
			// textBox_name
			// 
			this.textBox_name.Location = new System.Drawing.Point(89, 48);
			this.textBox_name.Name = "textBox_name";
			this.textBox_name.Size = new System.Drawing.Size(320, 23);
			this.textBox_name.TabIndex = 48;
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(12, 51);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(68, 17);
			this.label1.TabIndex = 47;
			this.label1.Text = "节点名称：";
			// 
			// button2
			// 
			this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button2.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Critical_16xLG_color;
			this.button2.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
			this.button2.Location = new System.Drawing.Point(-215, 433);
			this.button2.Name = "button2";
			this.button2.Size = new System.Drawing.Size(143, 43);
			this.button2.TabIndex = 68;
			this.button2.Text = " 取消";
			this.button2.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.button2.UseVisualStyleBackColor = true;
			// 
			// button1
			// 
			this.button1.BackColor = System.Drawing.Color.Transparent;
			this.button1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color;
			this.button1.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
			this.button1.Location = new System.Drawing.Point(-215, 384);
			this.button1.Name = "button1";
			this.button1.Size = new System.Drawing.Size(143, 43);
			this.button1.TabIndex = 67;
			this.button1.Text = " 确认";
			this.button1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.button1.UseVisualStyleBackColor = false;
			this.button1.Click += new System.EventHandler(this.button1_Click);
			// 
			// regularStringControl1
			// 
			this.regularStringControl1.BackColor = System.Drawing.Color.Transparent;
			this.regularStringControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.regularStringControl1.Location = new System.Drawing.Point(430, 12);
			this.regularStringControl1.Name = "regularStringControl1";
			this.regularStringControl1.Size = new System.Drawing.Size(413, 377);
			this.regularStringControl1.TabIndex = 70;
			// 
			// requestDataScaleControl1
			// 
			this.requestDataScaleControl1.BackColor = System.Drawing.Color.Transparent;
			this.requestDataScaleControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.requestDataScaleControl1.Location = new System.Drawing.Point(542, 28);
			this.requestDataScaleControl1.MinimumSize = new System.Drawing.Size(400, 127);
			this.requestDataScaleControl1.Name = "requestDataScaleControl1";
			this.requestDataScaleControl1.Size = new System.Drawing.Size(400, 127);
			this.requestDataScaleControl1.TabIndex = 69;
			// 
			// edgeLabel2
			// 
			this.edgeLabel2.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel2.ForeColor = System.Drawing.Color.White;
			this.edgeLabel2.Location = new System.Drawing.Point(12, 143);
			this.edgeLabel2.Name = "edgeLabel2";
			this.edgeLabel2.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel2.Size = new System.Drawing.Size(397, 22);
			this.edgeLabel2.TabIndex = 62;
			this.edgeLabel2.Text = "解析地址信息";
			this.edgeLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// edgeLabel1
			// 
			this.edgeLabel1.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel1.ForeColor = System.Drawing.Color.White;
			this.edgeLabel1.Location = new System.Drawing.Point(12, 14);
			this.edgeLabel1.Name = "edgeLabel1";
			this.edgeLabel1.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel1.Size = new System.Drawing.Size(397, 22);
			this.edgeLabel1.TabIndex = 61;
			this.edgeLabel1.Text = "节点信息";
			this.edgeLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// checkBox_isArray
			// 
			this.checkBox_isArray.AutoSize = true;
			this.checkBox_isArray.Location = new System.Drawing.Point(207, 177);
			this.checkBox_isArray.Name = "checkBox_isArray";
			this.checkBox_isArray.Size = new System.Drawing.Size(75, 21);
			this.checkBox_isArray.TabIndex = 71;
			this.checkBox_isArray.Text = "是否数组";
			this.checkBox_isArray.UseVisualStyleBackColor = true;
			// 
			// comboBox_alarm
			// 
			this.comboBox_alarm.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_alarm.FormattingEnabled = true;
			this.comboBox_alarm.Location = new System.Drawing.Point(104, 333);
			this.comboBox_alarm.Name = "comboBox_alarm";
			this.comboBox_alarm.Size = new System.Drawing.Size(304, 25);
			this.comboBox_alarm.TabIndex = 75;
			// 
			// checkBox_alarm
			// 
			this.checkBox_alarm.AutoSize = true;
			this.checkBox_alarm.Location = new System.Drawing.Point(11, 336);
			this.checkBox_alarm.Name = "checkBox_alarm";
			this.checkBox_alarm.Size = new System.Drawing.Size(75, 21);
			this.checkBox_alarm.TabIndex = 74;
			this.checkBox_alarm.Text = "报警分析";
			this.checkBox_alarm.UseVisualStyleBackColor = true;
			// 
			// edgeLabel4
			// 
			this.edgeLabel4.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel4.ForeColor = System.Drawing.Color.White;
			this.edgeLabel4.Location = new System.Drawing.Point(12, 306);
			this.edgeLabel4.Name = "edgeLabel4";
			this.edgeLabel4.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel4.Size = new System.Drawing.Size(397, 22);
			this.edgeLabel4.TabIndex = 73;
			this.edgeLabel4.Text = "附加功能";
			this.edgeLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// comboBox_oee
			// 
			this.comboBox_oee.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_oee.FormattingEnabled = true;
			this.comboBox_oee.Location = new System.Drawing.Point(104, 364);
			this.comboBox_oee.Name = "comboBox_oee";
			this.comboBox_oee.Size = new System.Drawing.Size(304, 25);
			this.comboBox_oee.TabIndex = 77;
			// 
			// checkBox_oee
			// 
			this.checkBox_oee.AutoSize = true;
			this.checkBox_oee.Location = new System.Drawing.Point(11, 367);
			this.checkBox_oee.Name = "checkBox_oee";
			this.checkBox_oee.Size = new System.Drawing.Size(75, 21);
			this.checkBox_oee.TabIndex = 76;
			this.checkBox_oee.Text = "Oee分析";
			this.checkBox_oee.UseVisualStyleBackColor = true;
			// 
			// checkBox_subscription
			// 
			this.checkBox_subscription.AutoSize = true;
			this.checkBox_subscription.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
			this.checkBox_subscription.Location = new System.Drawing.Point(89, 278);
			this.checkBox_subscription.Name = "checkBox_subscription";
			this.checkBox_subscription.Size = new System.Drawing.Size(195, 21);
			this.checkBox_subscription.TabIndex = 80;
			this.checkBox_subscription.Text = "启用数据发布订阅（支持数组）";
			this.checkBox_subscription.UseVisualStyleBackColor = true;
			// 
			// checkBox_forbidRemoteWrite
			// 
			this.checkBox_forbidRemoteWrite.AutoSize = true;
			this.checkBox_forbidRemoteWrite.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
			this.checkBox_forbidRemoteWrite.Location = new System.Drawing.Point(89, 258);
			this.checkBox_forbidRemoteWrite.Name = "checkBox_forbidRemoteWrite";
			this.checkBox_forbidRemoteWrite.Size = new System.Drawing.Size(231, 21);
			this.checkBox_forbidRemoteWrite.TabIndex = 79;
			this.checkBox_forbidRemoteWrite.Text = "禁止远程账户写入（包括管理员账户）";
			this.checkBox_forbidRemoteWrite.UseVisualStyleBackColor = true;
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.Location = new System.Drawing.Point(12, 259);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(68, 17);
			this.label6.TabIndex = 78;
			this.label6.Text = "数据权限：";
			// 
			// label7
			// 
			this.label7.AutoSize = true;
			this.label7.ForeColor = System.Drawing.Color.Gray;
			this.label7.Location = new System.Drawing.Point(86, 201);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(123, 17);
			this.label7.TabIndex = 81;
			this.label7.Text = "字节为单位，从0开始";
			// 
			// buttonOk1
			// 
			this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonOk1.Location = new System.Drawing.Point(286, 410);
			this.buttonOk1.Name = "buttonOk1";
			this.buttonOk1.Size = new System.Drawing.Size(121, 36);
			this.buttonOk1.TabIndex = 82;
			this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
			// 
			// buttonCancel1
			// 
			this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonCancel1.Location = new System.Drawing.Point(440, 410);
			this.buttonCancel1.Name = "buttonCancel1";
			this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
			this.buttonCancel1.TabIndex = 83;
			this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
			// 
			// regularBCDControl1
			// 
			this.regularBCDControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.regularBCDControl1.Location = new System.Drawing.Point(585, 229);
			this.regularBCDControl1.Name = "regularBCDControl1";
			this.regularBCDControl1.Size = new System.Drawing.Size(414, 313);
			this.regularBCDControl1.TabIndex = 84;
			// 
			// textBox_displayName
			// 
			this.textBox_displayName.Location = new System.Drawing.Point(89, 78);
			this.textBox_displayName.Name = "textBox_displayName";
			this.textBox_displayName.Size = new System.Drawing.Size(202, 23);
			this.textBox_displayName.TabIndex = 86;
			// 
			// label8
			// 
			this.label8.AutoSize = true;
			this.label8.Location = new System.Drawing.Point(12, 81);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(68, 17);
			this.label8.TabIndex = 85;
			this.label8.Text = "显示别名：";
			// 
			// label9
			// 
			this.label9.AutoSize = true;
			this.label9.Location = new System.Drawing.Point(297, 81);
			this.label9.Name = "label9";
			this.label9.Size = new System.Drawing.Size(44, 17);
			this.label9.TabIndex = 87;
			this.label9.Text = "单位：";
			// 
			// textBox_unit
			// 
			this.textBox_unit.Location = new System.Drawing.Point(341, 78);
			this.textBox_unit.Name = "textBox_unit";
			this.textBox_unit.Size = new System.Drawing.Size(68, 23);
			this.textBox_unit.TabIndex = 88;
			// 
			// label10
			// 
			this.label10.AutoSize = true;
			this.label10.Location = new System.Drawing.Point(215, 229);
			this.label10.Name = "label10";
			this.label10.Size = new System.Drawing.Size(129, 17);
			this.label10.TabIndex = 89;
			this.label10.Text = "字符串占用byte长度：";
			// 
			// textBox_stringLength
			// 
			this.textBox_stringLength.Location = new System.Drawing.Point(355, 226);
			this.textBox_stringLength.Name = "textBox_stringLength";
			this.textBox_stringLength.Size = new System.Drawing.Size(54, 23);
			this.textBox_stringLength.TabIndex = 90;
			this.textBox_stringLength.Text = "1";
			// 
			// FormRegularItemNode
			// 
			this.AcceptButton = this.button1;
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CancelButton = this.button2;
			this.ClientSize = new System.Drawing.Size(854, 471);
			this.Controls.Add(this.textBox_stringLength);
			this.Controls.Add(this.label10);
			this.Controls.Add(this.textBox_unit);
			this.Controls.Add(this.label9);
			this.Controls.Add(this.textBox_displayName);
			this.Controls.Add(this.label8);
			this.Controls.Add(this.regularBCDControl1);
			this.Controls.Add(this.buttonCancel1);
			this.Controls.Add(this.buttonOk1);
			this.Controls.Add(this.checkBox_subscription);
			this.Controls.Add(this.checkBox_forbidRemoteWrite);
			this.Controls.Add(this.label6);
			this.Controls.Add(this.comboBox_oee);
			this.Controls.Add(this.checkBox_oee);
			this.Controls.Add(this.requestDataScaleControl1);
			this.Controls.Add(this.comboBox_alarm);
			this.Controls.Add(this.checkBox_alarm);
			this.Controls.Add(this.edgeLabel4);
			this.Controls.Add(this.checkBox_isArray);
			this.Controls.Add(this.button2);
			this.Controls.Add(this.button1);
			this.Controls.Add(this.edgeLabel2);
			this.Controls.Add(this.edgeLabel1);
			this.Controls.Add(this.textBox_length);
			this.Controls.Add(this.label5);
			this.Controls.Add(this.comboBox_type);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.textBox3);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.textBox_description);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.textBox_name);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.label7);
			this.Controls.Add(this.regularStringControl1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
			this.MaximizeBox = false;
			this.Name = "FormRegularItemNode";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "解析节点数据信息";
			this.Load += new System.EventHandler(this.FormRegularItemNode_Load);
			this.ResumeLayout(false);
			this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.TextBox textBox_length;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ComboBox comboBox_type;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox textBox3;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox textBox_description;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox textBox_name;
        private System.Windows.Forms.Label label1;
        private Controls.EdgeLabel edgeLabel1;
        private Controls.EdgeLabel edgeLabel2;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Button button1;
        private Controls.NodeSettings.RequestDataScaleControl requestDataScaleControl1;
		private Controls.NodeSettings.RegularStringControl regularStringControl1;
		private System.Windows.Forms.CheckBox checkBox_isArray;
		private System.Windows.Forms.ComboBox comboBox_alarm;
		private System.Windows.Forms.CheckBox checkBox_alarm;
		private Controls.EdgeLabel edgeLabel4;
		private System.Windows.Forms.ComboBox comboBox_oee;
		private System.Windows.Forms.CheckBox checkBox_oee;
		private System.Windows.Forms.CheckBox checkBox_subscription;
		private System.Windows.Forms.CheckBox checkBox_forbidRemoteWrite;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label7;
		private ButtonOk buttonOk1;
		private ButtonCancel buttonCancel1;
		private Controls.NodeSettings.RegularBCDControl regularBCDControl1;
		private System.Windows.Forms.TextBox textBox_displayName;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.TextBox textBox_unit;
		private System.Windows.Forms.Label label10;
		private System.Windows.Forms.TextBox textBox_stringLength;
	}
}