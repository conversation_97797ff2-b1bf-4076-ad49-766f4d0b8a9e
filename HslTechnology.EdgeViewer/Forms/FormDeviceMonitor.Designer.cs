
using HslTechnology.EdgeViewer.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
	partial class FormDeviceMonitor
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
			this.components = new System.ComponentModel.Container();
			this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
			this.splitContainer2 = new System.Windows.Forms.SplitContainer();
			this.panel3 = new System.Windows.Forms.Panel();
			this.hslBadge2 = new HslControls.HslBadge();
			this.hslBadge1 = new HslControls.HslBadge();
			this.label_device_error = new System.Windows.Forms.Label();
			this.label_device_stop = new System.Windows.Forms.Label();
			this.label_device_run = new System.Windows.Forms.Label();
			this.label8 = new System.Windows.Forms.Label();
			this.label1 = new System.Windows.Forms.Label();
			this.hslLanternSimple1 = new HslControls.HslLanternSimple();
			this.label5 = new System.Windows.Forms.Label();
			this.label4 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.panel4 = new System.Windows.Forms.Panel();
			this.panel5 = new System.Windows.Forms.Panel();
			this.tabControl1 = new System.Windows.Forms.TabControl();
			this.tabPage_data = new System.Windows.Forms.TabPage();
			this.dataInfomationControl1 = new HslTechnology.Edge.Controls.Machine.DataInfomationControl();
			this.tabPage_device = new System.Windows.Forms.TabPage();
			this.deviceInfoControl1 = new HslTechnology.Edge.Controls.Machine.DeviceInfoControl();
			this.tabPage_method = new System.Windows.Forms.TabPage();
			this.deviceMethodControl1 = new HslTechnology.Edge.Controls.Machine.DeviceMethodControl();
			this.tabPage_alarm = new System.Windows.Forms.TabPage();
			this.deviceAlarmControl1 = new HslTechnology.Edge.Controls.Machine.DeviceAlarmControl();
			this.tabPage_oee = new System.Windows.Forms.TabPage();
			this.deviceOeeControl1 = new HslTechnology.Edge.Controls.Machine.DeviceOeeControl();
			this.tabPage_offline = new System.Windows.Forms.TabPage();
			this.deviceOfflineControl1 = new HslTechnology.Edge.Controls.Machine.DeviceOfflineControl();
			this.tabPage_log = new System.Windows.Forms.TabPage();
			this.deviceLogControl1 = new HslTechnology.Edge.Controls.Machine.DeviceLogControl();
			((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).BeginInit();
			this.splitContainer2.Panel1.SuspendLayout();
			this.splitContainer2.Panel2.SuspendLayout();
			this.splitContainer2.SuspendLayout();
			this.panel3.SuspendLayout();
			this.panel4.SuspendLayout();
			this.panel5.SuspendLayout();
			this.tabControl1.SuspendLayout();
			this.tabPage_data.SuspendLayout();
			this.tabPage_device.SuspendLayout();
			this.tabPage_method.SuspendLayout();
			this.tabPage_alarm.SuspendLayout();
			this.tabPage_oee.SuspendLayout();
			this.tabPage_offline.SuspendLayout();
			this.tabPage_log.SuspendLayout();
			this.SuspendLayout();
			// 
			// splitContainer2
			// 
			this.splitContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
			this.splitContainer2.Location = new System.Drawing.Point(0, 0);
			this.splitContainer2.Name = "splitContainer2";
			this.splitContainer2.Orientation = System.Windows.Forms.Orientation.Horizontal;
			// 
			// splitContainer2.Panel1
			// 
			this.splitContainer2.Panel1.Controls.Add(this.panel3);
			// 
			// splitContainer2.Panel2
			// 
			this.splitContainer2.Panel2.Controls.Add(this.panel4);
			this.splitContainer2.Size = new System.Drawing.Size(842, 574);
			this.splitContainer2.SplitterDistance = 245;
			this.splitContainer2.TabIndex = 1;
			// 
			// panel3
			// 
			this.panel3.AutoScroll = true;
			this.panel3.BackColor = System.Drawing.Color.Transparent;
			this.panel3.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
			this.panel3.Controls.Add(this.hslBadge2);
			this.panel3.Controls.Add(this.hslBadge1);
			this.panel3.Controls.Add(this.label_device_error);
			this.panel3.Controls.Add(this.label_device_stop);
			this.panel3.Controls.Add(this.label_device_run);
			this.panel3.Controls.Add(this.label8);
			this.panel3.Controls.Add(this.label1);
			this.panel3.Controls.Add(this.hslLanternSimple1);
			this.panel3.Controls.Add(this.label5);
			this.panel3.Controls.Add(this.label4);
			this.panel3.Controls.Add(this.label3);
			this.panel3.Controls.Add(this.label2);
			this.panel3.Dock = System.Windows.Forms.DockStyle.Fill;
			this.panel3.Location = new System.Drawing.Point(0, 0);
			this.panel3.Name = "panel3";
			this.panel3.Padding = new System.Windows.Forms.Padding(0, 0, 0, 10);
			this.panel3.Size = new System.Drawing.Size(842, 245);
			this.panel3.TabIndex = 0;
			// 
			// hslBadge2
			// 
			this.hslBadge2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.hslBadge2.AutoSize = false;
			this.hslBadge2.ButtonHoverStyle = true;
			this.hslBadge2.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.hslBadge2.LeftBackground = System.Drawing.Color.FromArgb(((int)(((byte)(79)))), ((int)(((byte)(79)))), ((int)(((byte)(79)))));
			this.hslBadge2.Location = new System.Drawing.Point(748, 26);
			this.hslBadge2.Name = "hslBadge2";
			this.hslBadge2.RightBackground = System.Drawing.Color.FromArgb(((int)(((byte)(252)))), ((int)(((byte)(118)))), ((int)(((byte)(0)))));
			this.hslBadge2.RightText = "0";
			this.hslBadge2.Size = new System.Drawing.Size(85, 20);
			this.hslBadge2.TabIndex = 13;
			this.hslBadge2.Text = "报警";
			// 
			// hslBadge1
			// 
			this.hslBadge1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.hslBadge1.AutoSize = false;
			this.hslBadge1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.hslBadge1.LeftBackground = System.Drawing.Color.FromArgb(((int)(((byte)(79)))), ((int)(((byte)(79)))), ((int)(((byte)(79)))));
			this.hslBadge1.Location = new System.Drawing.Point(656, 26);
			this.hslBadge1.Name = "hslBadge1";
			this.hslBadge1.RightBackground = System.Drawing.Color.FromArgb(((int)(((byte)(2)))), ((int)(((byte)(115)))), ((int)(((byte)(179)))));
			this.hslBadge1.RightText = "0";
			this.hslBadge1.Size = new System.Drawing.Size(86, 20);
			this.hslBadge1.TabIndex = 12;
			this.hslBadge1.Text = "设备";
			// 
			// label_device_error
			// 
			this.label_device_error.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.label_device_error.AutoSize = true;
			this.label_device_error.Cursor = System.Windows.Forms.Cursors.Hand;
			this.label_device_error.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Critical_16xLG_color;
			this.label_device_error.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.label_device_error.Location = new System.Drawing.Point(789, 6);
			this.label_device_error.Name = "label_device_error";
			this.label_device_error.Size = new System.Drawing.Size(39, 17);
			this.label_device_error.TabIndex = 10;
			this.label_device_error.Text = "      0";
			// 
			// label_device_stop
			// 
			this.label_device_stop.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.label_device_stop.AutoSize = true;
			this.label_device_stop.Cursor = System.Windows.Forms.Cursors.Hand;
			this.label_device_stop.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Pause_16xLG_color;
			this.label_device_stop.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.label_device_stop.Location = new System.Drawing.Point(740, 6);
			this.label_device_stop.Name = "label_device_stop";
			this.label_device_stop.Size = new System.Drawing.Size(39, 17);
			this.label_device_stop.TabIndex = 9;
			this.label_device_stop.Text = "      0";
			// 
			// label_device_run
			// 
			this.label_device_run.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.label_device_run.AutoSize = true;
			this.label_device_run.Cursor = System.Windows.Forms.Cursors.Hand;
			this.label_device_run.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color;
			this.label_device_run.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.label_device_run.Location = new System.Drawing.Point(689, 6);
			this.label_device_run.Name = "label_device_run";
			this.label_device_run.Size = new System.Drawing.Size(39, 17);
			this.label_device_run.TabIndex = 8;
			this.label_device_run.Text = "      0";
			// 
			// label8
			// 
			this.label8.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.label8.AutoSize = true;
			this.label8.Image = global::HslTechnology.EdgeViewer.Properties.Resources.glasses_16xLG;
			this.label8.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.label8.Location = new System.Drawing.Point(631, 6);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(39, 17);
			this.label8.TabIndex = 7;
			this.label8.Text = "      0";
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.ForeColor = System.Drawing.Color.Gray;
			this.label1.Location = new System.Drawing.Point(141, 31);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(13, 17);
			this.label1.TabIndex = 5;
			this.label1.Text = "/";
			// 
			// hslLanternSimple1
			// 
			this.hslLanternSimple1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.hslLanternSimple1.LanternBackground = System.Drawing.Color.Tomato;
			this.hslLanternSimple1.Location = new System.Drawing.Point(874, 4);
			this.hslLanternSimple1.Name = "hslLanternSimple1";
			this.hslLanternSimple1.Size = new System.Drawing.Size(22, 19);
			this.hslLanternSimple1.TabIndex = 4;
			this.hslLanternSimple1.Text = "hslLanternSimple1";
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.BackColor = System.Drawing.Color.Transparent;
			this.label5.ForeColor = System.Drawing.Color.Gray;
			this.label5.Location = new System.Drawing.Point(7, 31);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(128, 17);
			this.label5.TabIndex = 3;
			this.label5.Text = "详细的设备列表如下：";
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(487, 7);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(55, 17);
			this.label4.TabIndex = 2;
			this.label4.Text = "Version:";
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(240, 7);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(92, 17);
			this.label3.TabIndex = 1;
			this.label3.Text = "开始运行时间：";
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(7, 7);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(68, 17);
			this.label2.TabIndex = 0;
			this.label2.Text = "设备名称：";
			// 
			// panel4
			// 
			this.panel4.Controls.Add(this.panel5);
			this.panel4.Dock = System.Windows.Forms.DockStyle.Fill;
			this.panel4.Location = new System.Drawing.Point(0, 0);
			this.panel4.Name = "panel4";
			this.panel4.Size = new System.Drawing.Size(842, 325);
			this.panel4.TabIndex = 1;
			// 
			// panel5
			// 
			this.panel5.BackColor = System.Drawing.Color.Honeydew;
			this.panel5.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
			this.panel5.Controls.Add(this.tabControl1);
			this.panel5.Dock = System.Windows.Forms.DockStyle.Fill;
			this.panel5.Location = new System.Drawing.Point(0, 0);
			this.panel5.Name = "panel5";
			this.panel5.Size = new System.Drawing.Size(842, 325);
			this.panel5.TabIndex = 0;
			// 
			// tabControl1
			// 
			this.tabControl1.Controls.Add(this.tabPage_data);
			this.tabControl1.Controls.Add(this.tabPage_device);
			this.tabControl1.Controls.Add(this.tabPage_method);
			this.tabControl1.Controls.Add(this.tabPage_alarm);
			this.tabControl1.Controls.Add(this.tabPage_oee);
			this.tabControl1.Controls.Add(this.tabPage_offline);
			this.tabControl1.Controls.Add(this.tabPage_log);
			this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.tabControl1.Location = new System.Drawing.Point(0, 0);
			this.tabControl1.Name = "tabControl1";
			this.tabControl1.SelectedIndex = 0;
			this.tabControl1.Size = new System.Drawing.Size(840, 323);
			this.tabControl1.TabIndex = 8;
			// 
			// tabPage_data
			// 
			this.tabPage_data.Controls.Add(this.dataInfomationControl1);
			this.tabPage_data.Location = new System.Drawing.Point(4, 26);
			this.tabPage_data.Name = "tabPage_data";
			this.tabPage_data.Padding = new System.Windows.Forms.Padding(3);
			this.tabPage_data.Size = new System.Drawing.Size(832, 293);
			this.tabPage_data.TabIndex = 0;
			this.tabPage_data.Text = "数据信息";
			this.tabPage_data.UseVisualStyleBackColor = true;
			// 
			// dataInfomationControl1
			// 
			this.dataInfomationControl1.BackColor = System.Drawing.Color.White;
			this.dataInfomationControl1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.dataInfomationControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.dataInfomationControl1.Location = new System.Drawing.Point(3, 3);
			this.dataInfomationControl1.Name = "dataInfomationControl1";
			this.dataInfomationControl1.Size = new System.Drawing.Size(826, 287);
			this.dataInfomationControl1.TabIndex = 0;
			// 
			// tabPage_device
			// 
			this.tabPage_device.Controls.Add(this.deviceInfoControl1);
			this.tabPage_device.Location = new System.Drawing.Point(4, 22);
			this.tabPage_device.Name = "tabPage_device";
			this.tabPage_device.Size = new System.Drawing.Size(832, 297);
			this.tabPage_device.TabIndex = 3;
			this.tabPage_device.Text = "设备信息";
			this.tabPage_device.UseVisualStyleBackColor = true;
			// 
			// deviceInfoControl1
			// 
			this.deviceInfoControl1.BackColor = System.Drawing.Color.Transparent;
			this.deviceInfoControl1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.deviceInfoControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.deviceInfoControl1.Location = new System.Drawing.Point(0, 0);
			this.deviceInfoControl1.Name = "deviceInfoControl1";
			this.deviceInfoControl1.Size = new System.Drawing.Size(832, 297);
			this.deviceInfoControl1.TabIndex = 0;
			// 
			// tabPage_method
			// 
			this.tabPage_method.Controls.Add(this.deviceMethodControl1);
			this.tabPage_method.Location = new System.Drawing.Point(4, 22);
			this.tabPage_method.Name = "tabPage_method";
			this.tabPage_method.Padding = new System.Windows.Forms.Padding(3);
			this.tabPage_method.Size = new System.Drawing.Size(832, 297);
			this.tabPage_method.TabIndex = 5;
			this.tabPage_method.Text = "方法接口";
			this.tabPage_method.UseVisualStyleBackColor = true;
			// 
			// deviceMethodControl1
			// 
			this.deviceMethodControl1.BackColor = System.Drawing.Color.Transparent;
			this.deviceMethodControl1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.deviceMethodControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.deviceMethodControl1.Location = new System.Drawing.Point(3, 3);
			this.deviceMethodControl1.Name = "deviceMethodControl1";
			this.deviceMethodControl1.Size = new System.Drawing.Size(826, 291);
			this.deviceMethodControl1.TabIndex = 0;
			// 
			// tabPage_alarm
			// 
			this.tabPage_alarm.Controls.Add(this.deviceAlarmControl1);
			this.tabPage_alarm.Location = new System.Drawing.Point(4, 22);
			this.tabPage_alarm.Name = "tabPage_alarm";
			this.tabPage_alarm.Padding = new System.Windows.Forms.Padding(3);
			this.tabPage_alarm.Size = new System.Drawing.Size(832, 297);
			this.tabPage_alarm.TabIndex = 1;
			this.tabPage_alarm.Text = "报警信息";
			this.tabPage_alarm.UseVisualStyleBackColor = true;
			// 
			// deviceAlarmControl1
			// 
			this.deviceAlarmControl1.BackColor = System.Drawing.Color.Transparent;
			this.deviceAlarmControl1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.deviceAlarmControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.deviceAlarmControl1.Location = new System.Drawing.Point(3, 3);
			this.deviceAlarmControl1.Name = "deviceAlarmControl1";
			this.deviceAlarmControl1.Size = new System.Drawing.Size(826, 291);
			this.deviceAlarmControl1.TabIndex = 0;
			// 
			// tabPage_oee
			// 
			this.tabPage_oee.Controls.Add(this.deviceOeeControl1);
			this.tabPage_oee.Location = new System.Drawing.Point(4, 26);
			this.tabPage_oee.Name = "tabPage_oee";
			this.tabPage_oee.Padding = new System.Windows.Forms.Padding(3);
			this.tabPage_oee.Size = new System.Drawing.Size(832, 293);
			this.tabPage_oee.TabIndex = 2;
			this.tabPage_oee.Text = "OEE信息";
			this.tabPage_oee.UseVisualStyleBackColor = true;
			// 
			// deviceOeeControl1
			// 
			this.deviceOeeControl1.BackColor = System.Drawing.Color.Transparent;
			this.deviceOeeControl1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.deviceOeeControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.deviceOeeControl1.Location = new System.Drawing.Point(3, 3);
			this.deviceOeeControl1.Name = "deviceOeeControl1";
			this.deviceOeeControl1.Size = new System.Drawing.Size(826, 287);
			this.deviceOeeControl1.TabIndex = 0;
			// 
			// tabPage_offline
			// 
			this.tabPage_offline.Controls.Add(this.deviceOfflineControl1);
			this.tabPage_offline.Location = new System.Drawing.Point(4, 22);
			this.tabPage_offline.Name = "tabPage_offline";
			this.tabPage_offline.Padding = new System.Windows.Forms.Padding(3);
			this.tabPage_offline.Size = new System.Drawing.Size(832, 297);
			this.tabPage_offline.TabIndex = 4;
			this.tabPage_offline.Text = "离线信息";
			this.tabPage_offline.UseVisualStyleBackColor = true;
			// 
			// deviceOfflineControl1
			// 
			this.deviceOfflineControl1.BackColor = System.Drawing.Color.Transparent;
			this.deviceOfflineControl1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.deviceOfflineControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.deviceOfflineControl1.Location = new System.Drawing.Point(3, 3);
			this.deviceOfflineControl1.Name = "deviceOfflineControl1";
			this.deviceOfflineControl1.Size = new System.Drawing.Size(826, 291);
			this.deviceOfflineControl1.TabIndex = 0;
			// 
			// tabPage_log
			// 
			this.tabPage_log.Controls.Add(this.deviceLogControl1);
			this.tabPage_log.Location = new System.Drawing.Point(4, 22);
			this.tabPage_log.Name = "tabPage_log";
			this.tabPage_log.Padding = new System.Windows.Forms.Padding(3);
			this.tabPage_log.Size = new System.Drawing.Size(832, 297);
			this.tabPage_log.TabIndex = 6;
			this.tabPage_log.Text = "设备日志";
			this.tabPage_log.UseVisualStyleBackColor = true;
			// 
			// deviceLogControl1
			// 
			this.deviceLogControl1.BackColor = System.Drawing.Color.Transparent;
			this.deviceLogControl1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.deviceLogControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.deviceLogControl1.Location = new System.Drawing.Point(3, 3);
			this.deviceLogControl1.Name = "deviceLogControl1";
			this.deviceLogControl1.Size = new System.Drawing.Size(826, 291);
			this.deviceLogControl1.TabIndex = 0;
			// 
			// FormDeviceMonitor
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.Honeydew;
			this.ClientSize = new System.Drawing.Size(842, 574);
			this.Controls.Add(this.splitContainer2);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
			this.Name = "FormDeviceMonitor";
			this.Text = "FormDeviceMonitor";
			this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormDeviceMonitor_FormClosing);
			this.Load += new System.EventHandler(this.FormDeviceMonitor_Load);
			this.splitContainer2.Panel1.ResumeLayout(false);
			this.splitContainer2.Panel2.ResumeLayout(false);
			((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).EndInit();
			this.splitContainer2.ResumeLayout(false);
			this.panel3.ResumeLayout(false);
			this.panel3.PerformLayout();
			this.panel4.ResumeLayout(false);
			this.panel5.ResumeLayout(false);
			this.tabControl1.ResumeLayout(false);
			this.tabPage_data.ResumeLayout(false);
			this.tabPage_device.ResumeLayout(false);
			this.tabPage_method.ResumeLayout(false);
			this.tabPage_alarm.ResumeLayout(false);
			this.tabPage_oee.ResumeLayout(false);
			this.tabPage_offline.ResumeLayout(false);
			this.tabPage_log.ResumeLayout(false);
			this.ResumeLayout(false);

		}

		#endregion

		private System.Windows.Forms.SplitContainer splitContainer2;
		private System.Windows.Forms.Panel panel3;
		private HslControls.HslLanternSimple hslLanternSimple1;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Panel panel4;
		private System.Windows.Forms.Panel panel5;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.ToolTip toolTip1;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label label_device_error;
		private System.Windows.Forms.Label label_device_stop;
		private System.Windows.Forms.Label label_device_run;
		private System.Windows.Forms.TabControl tabControl1;
		private System.Windows.Forms.TabPage tabPage_data;
		private System.Windows.Forms.TabPage tabPage_alarm;
		private Edge.Controls.Machine.DataInfomationControl dataInfomationControl1;
		private Edge.Controls.Machine.DeviceAlarmControl deviceAlarmControl1;
		private System.Windows.Forms.TabPage tabPage_oee;
		private Edge.Controls.Machine.DeviceOeeControl deviceOeeControl1;
		private System.Windows.Forms.TabPage tabPage_device;
		private Edge.Controls.Machine.DeviceInfoControl deviceInfoControl1;
		private System.Windows.Forms.TabPage tabPage_offline;
		private Edge.Controls.Machine.DeviceOfflineControl deviceOfflineControl1;
		private System.Windows.Forms.TabPage tabPage_method;
		private Edge.Controls.Machine.DeviceMethodControl deviceMethodControl1;
		private HslControls.HslBadge hslBadge2;
		private HslControls.HslBadge hslBadge1;
		private System.Windows.Forms.TabPage tabPage_log;
		private Edge.Controls.Machine.DeviceLogControl deviceLogControl1;
	}
}