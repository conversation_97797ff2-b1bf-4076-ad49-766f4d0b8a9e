using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Device;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.EdgeViewer.Core;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormDeviceList : HslPage
	{
		public FormDeviceList( ILogNet logNet, EdgeServerSettings serverSettings, int status )
		{
			InitializeComponent( );
			this.FormClosing      += FormAlarmList_FormClosing;
			this.serverSettings   = serverSettings;
			this.log              = logNet;
			this.status           = status;
			if (this.serverSettings != null)
			{
				this.renderPath  = new TreeNodePath( this.serverSettings.RenderPath );
				this.client      = this.serverSettings.GetMqttSyncClient( true );
			}

			timer = new Timer( );
			timer.Interval = 1000;
			timer.Tick += Timer_Tick;

			hslDataGridView1.SizeChanged += HslDataGridView1_SizeChanged;
			textBox_regex.TextChanged += TextBox_regex_TextChanged;
			radioButton_sort_normal.CheckedChanged += RadioButton_sort_normal_CheckedChanged;
			radioButton_sort_timeBig.CheckedChanged += RadioButton_sort_timeBig_CheckedChanged;
			radioButton_sort_timeSmall.CheckedChanged += RadioButton_sort_timeSmall_CheckedChanged;
		}


		private void HslDataGridView1_SizeChanged( object sender, EventArgs e )
		{
			hslDataGridView1.Columns[0].Width = 70;
			hslDataGridView1.Columns[1].Width = 300 + (hslDataGridView1.Width - 840) / 2;
			hslDataGridView1.Columns[2].Width = 100;
			hslDataGridView1.Columns[3].Width = 350 + (hslDataGridView1.Width - 840) / 2;


		}

		private void FormDeviceList_Load( object sender, EventArgs e )
		{
			timer.Start( );
			if (this.serverSettings != null)
			{
				Text = this.serverSettings.GetDisplayText( );
			}

			if (status == 0)
			{
				radioButton_sort_timeBig.Text = "在线时间递增";
				radioButton_sort_timeSmall.Text = "在线时间递减";
				hslDataGridView1.Columns[2].HeaderText = "在线时间";
				label1.Text = "在线设备总数量:";
			}
			else if (status == 1)
			{
				radioButton_sort_timeBig.Text = "暂时时间递增";
				radioButton_sort_timeSmall.Text = "暂时时间递减";
				hslDataGridView1.Columns[2].HeaderText = "暂停时间";
				label1.Text = "暂停设备总数量:";
			}
		}

		private void FormAlarmList_FormClosing( object sender, FormClosingEventArgs e )
		{
			client.ConnectClose( );
			timer.Dispose( );
		}

		private async void Timer_Tick( object sender, EventArgs e )
		{
			if (serverSettings != null)
			{
				var read = await client.ReadRpcAsync<DeviceExceptionMessage[]>( "Edge/GetExceptionDeviceList", new { status = this.status } );
				if (read.IsSuccess)
				{
					all_messages = read.Content;
					RenderExceptionMessage( new List<DeviceExceptionMessage>( read.Content ) );
				}
				else
				{
					timer.Enabled = false;
				}

			}
		}

		private void TextBox_regex_TextChanged( object sender, EventArgs e )
		{
			if (all_messages != null) RenderExceptionMessage( new List<DeviceExceptionMessage>( all_messages ) );
		}

		private void RadioButton_sort_normal_CheckedChanged( object sender, EventArgs e )
		{
			if (radioButton_sort_normal.Checked && all_messages != null) RenderExceptionMessage( new List<DeviceExceptionMessage>( all_messages ) );
		}

		private void RadioButton_sort_timeBig_CheckedChanged( object sender, EventArgs e )
		{
			if (radioButton_sort_timeBig.Checked && all_messages != null) RenderExceptionMessage( new List<DeviceExceptionMessage>( all_messages ) );
		}

		private void RadioButton_sort_timeSmall_CheckedChanged( object sender, EventArgs e )
		{
			if (radioButton_sort_timeSmall.Checked && all_messages != null) RenderExceptionMessage( new List<DeviceExceptionMessage>( all_messages ) );
		}

		private void RenderExceptionMessage( List<DeviceExceptionMessage> list )
		{
			if (radioButton_sort_timeBig.Checked) list.Sort( ( m1, m2 ) => m2.ActiveTime.CompareTo( m1.ActiveTime ) );
			else if (radioButton_sort_timeSmall.Checked) list.Sort( ( m1, m2 ) => m1.ActiveTime.CompareTo( m2.ActiveTime ) );
			if (!string.IsNullOrEmpty( textBox_regex.Text ))
			{
				list = list.Where( m => Regex.IsMatch( m.Name, textBox_regex.Text ) || Regex.IsMatch( m.Message, textBox_regex.Text ) ).ToList( );
			}

			label_count.Text = list.Count.ToString( );
			HslTechnologyControlHelper.DataGridSpecifyRowCount( hslDataGridView1, list.Count );
			for (int i = 0; i < list.Count; i++)
			{
				hslDataGridView1.Rows[i].Cells[0].Value = (i + 1).ToString( );
				hslDataGridView1.Rows[i].Cells[1].Value = list[i].Name;

				if (status == 0)
				{
					TimeSpan ts = DateTime.Now - list[i].StartTime;
					hslDataGridView1.Rows[i].Cells[2].Value = HslCommunication.BasicFramework.SoftBasic.GetTimeSpanDescription( ts );
				}
				else
				{
					TimeSpan ts = DateTime.Now - list[i].ActiveTime;
					hslDataGridView1.Rows[i].Cells[2].Value = HslCommunication.BasicFramework.SoftBasic.GetTimeSpanDescription( ts );
				}
				hslDataGridView1.Rows[i].Cells[3].Value = list[i].Message;
			}
		}

		private DeviceExceptionMessage[] all_messages;
		private EdgeServerSettings serverSettings;
		private MqttSyncClient client;
		private Timer timer;
		private ILogNet log;
		private TreeNodePath renderPath = null;
		private int status = 2;

		private void label3_Click( object sender, EventArgs e )
		{

		}
	}
}
