using HslCommunication;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Plugins;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.EdgeViewer.Core;
using HslTechnology.EdgeViewer.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormPluginsManagement : HslPage
	{
		public FormPluginsManagement( ILogNet logNet, EdgeServerSettings serverSettings )
		{
			InitializeComponent( );
			this.log = logNet;
			this.serverSettings = serverSettings;
			this.Icon = Util.ConvertToIcon( Resources.Library );
		}

		private void FormPluginsManagement_Load( object sender, EventArgs e )
		{
			if (this.serverSettings != null)
			{
				Text = "Plugins-" + this.serverSettings.GetEdgeDisplayName( );
			}
			this.pluginsListControl1.PluginSelectEvent += PluginsListControl1_PluginSelectEvent;
			this.pluginsDetailsControl1.PluginUnloadEvent += PluginsDetailsControl1_PluginUnloadEvent;
			this.KeyDown += this.pluginsListControl1.ItemControl_KeyDown;
			this.button_search.Click += Button_search_Click;
			this.textBox_search.KeyDown += TextBox_search_KeyDown;
			this.button_install.Click += Button_install_Click;
			this.linkLabel_refresh.Click += LinkLabel_refresh_Click;
		}

		private async void LinkLabel_refresh_Click( object sender, EventArgs e )
		{
			await RefreshPlugins( );
		}

		private async void Button_install_Click( object sender, EventArgs e )
		{
			// 安装本地插件
			using (Pages.FormPluginsInstall form = new Pages.FormPluginsInstall( this.serverSettings ))
			{
				form.ShowDialog( );
			}
			await RefreshPlugins( );
		}

		private void TextBox_search_KeyDown( object sender, KeyEventArgs e )
		{
			if( e.KeyCode == Keys.Enter)
			{
				button_search.PerformClick( );
			}
			else
			{
				this.pluginsListControl1.ItemControl_KeyDown(sender, e);
			}
		}

		private void Button_search_Click( object sender, EventArgs e )
		{
			this.pluginsListControl1.SetSearchCondition( textBox_search.Text );
		}

		private async void PluginsDetailsControl1_PluginUnloadEvent( PluginsDefinition pluginsDefinition )
		{
			if (this.serverSettings == null) return;
			if (MessageBox.Show( $"是否确认真的卸载插件：{pluginsDefinition.DllName}", "Unload Plugins", MessageBoxButtons.YesNo ) == DialogResult.Yes)
			{
				OperateResult<string> unload = await this.serverSettings.GetMqttSyncClient( ).ReadRpcAsync<string>( "Plugins/UnloadRegisterPlugins",
					new { pluginsDllName = pluginsDefinition.DllName } );
				if (unload.IsSuccess)
				{
					MessageBox.Show( "卸载插件成功，网关重启生效。" );
				}
				else
				{
					MessageBox.Show( "卸载插件失败：" + unload.Message );
				}
			}
		}

		private void PluginsListControl1_PluginSelectEvent( PluginsDefinition pluginsDefinition )
		{
			this.pluginsDetailsControl1.SetPluginsDefinition( pluginsDefinition );
		}

		public void SetPlugins( PluginsDefinition[] pluginsDefinitions )
		{
			this.pluginsListControl1.SetPlugins( pluginsDefinitions );
		}

		public async Task RefreshPlugins( )
		{
			MqttSyncClient client = serverSettings.GetMqttSyncClient( );
			OperateResult<PluginsDefinition[]> readPlugins = await client.ReadRpcAsync<PluginsDefinition[]>( "Plugins/GetRegisterPlugins", new { } );
			if (readPlugins.IsSuccess) { serverSettings.NodeImages.AddImageItem( readPlugins.Content ); serverSettings.Plugins = readPlugins.Content; }

			if (!readPlugins.IsSuccess)
			{
				MessageBox.Show( "向服务器请求数据失败！原因：" + readPlugins.Message );
				return;
			}
			this.pluginsListControl1.SetPlugins( readPlugins.Content );
		}


		private EdgeServerSettings serverSettings;
		private MqttSyncClient client;
		private ILogNet log;
	}
}
