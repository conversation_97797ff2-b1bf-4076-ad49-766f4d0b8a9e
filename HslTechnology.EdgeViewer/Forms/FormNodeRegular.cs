using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.EdgeViewer.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormNodeRegular : HslForm
	{
		#region Constructor

		public FormNodeRegular( RegularStructItemNode regularNode = null )
		{
			InitializeComponent( );

			this.Icon = Util.GetWinformIcon( );
			this.RegularNode = regularNode ?? new RegularStructItemNode( );
		}

		#endregion

		#region Form Load
		
		private void FormSiemensS7_Load( object sender, EventArgs e )
		{
			if (RegularNode != null)
			{
				textBox1.Text = RegularNode.Name;
				textBox2.Text = RegularNode.Description;
				textBox3.Text = RegularNode.StructLength.ToString( );
			}
		}

		#endregion

		#region Private Member
		
		public RegularStructItemNode RegularNode { get; set; }

		#endregion

		private void button1_Click( object sender, EventArgs e )
		{

			if (string.IsNullOrEmpty( textBox1.Text ))
			{
				MessageBox.Show( "节点分类不能为空！" );
				return;
			}

			if (!int.TryParse( textBox3.Text, out int length ))
			{
				MessageBox.Show( "长度数据填写错误，必须为整数！" );
				return;
			}

			RegularNode = new RegularStructItemNode( )
			{
				Name = textBox1.Text,
				Description = textBox2.Text,
				StructLength = length,
			};

			DialogResult = DialogResult.OK;
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}
	}
}
