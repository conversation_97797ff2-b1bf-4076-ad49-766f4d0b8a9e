using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.EdgeViewer.Controls;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormCodeOee : HslForm
	{
		public FormCodeOee( OeeNode nodeDataOee, OeeDefinitionNode[] definitionNodes, OeeTimePeriod[] timePeriods )
		{
			InitializeComponent( );
			this.nodeDataOee = nodeDataOee ?? new OeeNode( );
			this.definitionNodes = definitionNodes ?? new OeeDefinitionNode[0];
			this.Icon = Util.ConvertToIcon( Util.GetImageByGroupNode( null, this.nodeDataOee ) );
			this.timePeriods = timePeriods ?? new OeeTimePeriod[0];
		}

		private void FormCodeOee_Load( object sender, EventArgs e )
		{
			textBox1.Text = this.nodeDataOee.Name;
			textBox2.Text = this.nodeDataOee.Description;
			textBox_reset_time.Text = OeeTimePeriod.GetTimeText( this.nodeDataOee.ResetTime );

			HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, this.definitionNodes.Length );
			for (int i = 0; i < this.definitionNodes.Length; i++)
			{
				dataGridView1.Rows[i].Cells[0].Value = this.definitionNodes[i].Name;
				dataGridView1.Rows[i].Cells[1].Value = this.definitionNodes[i].Description;
				dataGridView1.Rows[i].Cells[2].Value = this.definitionNodes[i].Code;
				dataGridView1.Rows[i].Cells[3].Value = this.definitionNodes[i].IsWorkingTime;
			}

			HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView2, this.timePeriods.Length );
			for (int i = 0; i < this.timePeriods.Length; i++)
			{
				dataGridView2.Rows[i].Cells[0].Value = OeeTimePeriod.GetTimeText(this.timePeriods[i].StartSecond );
				dataGridView2.Rows[i].Cells[1].Value = OeeTimePeriod.GetTimeText(this.timePeriods[i].EndSecond ); 
				dataGridView2.Rows[i].Cells[2].Value = this.timePeriods[i].ShiftName;
			}
		}

		private void button5_Click( object sender, EventArgs e )
		{
			List<OeeDefinitionNode> definitionNodes = new List<OeeDefinitionNode>( );
			int rowCount = dataGridView1.Rows.Count;
			if (dataGridView1.AllowUserToAddRows) rowCount--;
			for (int i = 0; i < rowCount; i++)
			{
				try
				{
					if(dataGridView1.Rows[i].Cells[2].Value == null)
					{
						MessageBox.Show( $"当前的第 [{i}] 行的 [代号] 数据不能为空" );
						return;
					}
					OeeDefinitionNode oeeDefinition = new OeeDefinitionNode( );
					oeeDefinition.Name              = dataGridView1.Rows[i].Cells[0].Value == null ? string.Empty : dataGridView1.Rows[i].Cells[0].Value.ToString( );
					oeeDefinition.Description       = dataGridView1.Rows[i].Cells[1].Value == null ? string.Empty : dataGridView1.Rows[i].Cells[1].Value.ToString( );
					oeeDefinition.Code              = int.Parse( dataGridView1.Rows[i].Cells[2].Value.ToString( ) );
					oeeDefinition.IsWorkingTime     = dataGridView1.Rows[i].Cells[3].Value == null ? false : ( bool)dataGridView1.Rows[i].Cells[3].Value;

					if(string.IsNullOrEmpty( oeeDefinition.Name.Trim( ) ))
					{
						MessageBox.Show( $"当前的第 [{i}] 行的 [名称] 数据不能为空，或是全空格" );
						return;
					}
					if(definitionNodes.Count> 0)
					{
						if(definitionNodes.Find( m => m.Name == oeeDefinition.Name || m.Code == oeeDefinition.Code ) != null)
						{
							MessageBox.Show( $"当前的 [名称] 数据[{oeeDefinition.Name}] 出现了重复，" +
								$"或是 [代号] 数据[{oeeDefinition.Code}] 出现了重复，请重新输入！" );
							return;
						}
					}
					definitionNodes.Add( oeeDefinition );
				}
				catch
				{
					throw;
					//MessageBox.Show( $"当前的第 [{i}] 行解析时发生了异常，请重新输入！" + Environment.NewLine + ex.Message );
					//return;
				}
			}

			List<OeeTimePeriod> timePeriods = new List<OeeTimePeriod>( );
			rowCount = dataGridView2.RowCount;
			if (dataGridView2.AllowUserToAddRows) rowCount--;
			for (int i = 0; i < rowCount; i++)
			{
				try
				{
					for (int j = 0; j < dataGridView2.ColumnCount; j++)
					{
						if (dataGridView2.Rows[i].Cells[j].Value == null)
						{
							MessageBox.Show( $"当前的第 [{i}] 行的 [{dataGridView2.Columns[j].HeaderText}] 数据不能为空" );
							return;
						}
					}
					OeeTimePeriod oeeTimePeriod = new OeeTimePeriod( );
					oeeTimePeriod.StartSecond = OeeTimePeriod.GetTimeFromText( dataGridView2.Rows[i].Cells[0].Value.ToString( ) );
					oeeTimePeriod.EndSecond = OeeTimePeriod.GetTimeFromText( dataGridView2.Rows[i].Cells[1].Value.ToString( ) );
					oeeTimePeriod.ShiftName = dataGridView2.Rows[i].Cells[2].Value.ToString( );

					if (string.IsNullOrEmpty( oeeTimePeriod.ShiftName.Trim( ) ))
					{
						MessageBox.Show( $"当前的第 [{i}] 行的 [班组名] 数据不能为空，或是全空格" );
						return;
					}

					if (oeeTimePeriod.ShiftName.Contains( "-" ) || oeeTimePeriod.ShiftName.Contains( "|" ))
					{
						MessageBox.Show( $"当前的第 [{i}] 行的 [班组名] 数据不能包含 - 和 | 字符，请重新输入" );
						return;
					}

					timePeriods.Add( oeeTimePeriod );
				}
				catch
				{
					throw;
					//MessageBox.Show( $"当前的第 [{i}] 行解析时发生了异常，请重新输入！" + Environment.NewLine + ex.Message );
					//return;
				}

			}


			if (string.IsNullOrEmpty( textBox1.Text )) { MessageBox.Show( "节点名称不能为空，请重新输入" ); textBox1.Focus( ); return; }
			nodeDataOee = new OeeNode( ) { Name = textBox1.Text, Description = textBox2.Text };
			nodeDataOee.ResetTime = OeeTimePeriod.GetTimeFromText( textBox_reset_time.Text );

			if(nodeDataOee.ResetTime % 600 != 0)
			{
				MessageBox.Show( "当前重置时间只能是30分钟的倍数，请重新输入" );
				return;
			}
			nodeDataOee.OeeTimePeriods = OeeTimePeriod.GetXmlAttr( timePeriods.ToArray( ) );
			this.definitionNodes = definitionNodes.ToArray( );
			DialogResult = DialogResult.OK;
		}

		private OeeNode nodeDataOee;
		private OeeDefinitionNode[] definitionNodes;
		private OeeTimePeriod[] timePeriods;

		/// <summary>
		/// 获取当前的OEE节点信息
		/// </summary>
		public OeeNode NodeDataOee => nodeDataOee;

		/// <summary>
		/// OEE的所有的节点定义
		/// </summary>
		public OeeDefinitionNode[] OeeDefinitions => definitionNodes;

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button5.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button4.PerformClick( );
		}
	}
}
