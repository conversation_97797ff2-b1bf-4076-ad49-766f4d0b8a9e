using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Core
{
	public static class Extension
	{
		/// <summary>
		/// 统一的设置节点控件的图片的扩展方法，同时设置普通状态和选中状态的关键字。
		/// </summary>
		/// <param name="node">树节点控件</param>
		/// <param name="key">图形关键字</param>
		public static void SetImageKey( this TreeNode node, string key )
		{
			node.ImageKey = key;
			node.SelectedImageKey = key;
		}

	}
}
