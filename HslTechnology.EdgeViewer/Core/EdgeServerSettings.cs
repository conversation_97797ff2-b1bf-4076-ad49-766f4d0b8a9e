using HslTechnology.Edge.Config;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.EdgeViewer.Core
{
	/// <summary>
	/// 边缘网关的对象信息
	/// </summary>
	public class EdgeServerSettings : HslServerSettings
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public EdgeServerSettings( )
		{
			this.NodeImages = new GroupNodeImages( );
			this.RenderPath = new TreeNodePath( );
		}

		/// <summary>
		/// 当前的网关绑定的节点图标类
		/// </summary>
		[JsonIgnore]
		public GroupNodeImages NodeImages { get; set; }

		/// <summary>
		/// 当前网关是否处于全部暂时操作中，默认是 False
		/// </summary>
		[JsonIgnore]
		public bool EdgeAllDeivcePause { get; set; }

		/// <summary>
		/// 当前网关中支持的配置最大的设备数量，如果小于0，则为无限
		/// </summary>
		public int MaxDevicesCount { get; set; } = -1;

		/// <summary>
		/// 当前显示的设备分类路径信息
		/// </summary>
		public TreeNodePath RenderPath { get; set; }

		/// <summary>
		/// 获取当前网关的显示的名称s
		/// </summary>
		/// <returns>字符串信息</returns>
		public string GetEdgeDisplayName( )
		{
			return string.IsNullOrEmpty( this.Alias ) ? this.EdgeID : this.Alias;
		}

		/// <inheritdoc/>
		public override string ToString( ) => string.IsNullOrEmpty( Alias ) ? EdgeID : Alias;

	}
}
