using HslTechnology.Edge.Plugins;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.EdgeViewer.Core
{
	/// <summary>
	/// 所有的节点的图标管理类信息
	/// </summary>
	public class GroupNodeImages
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public GroupNodeImages( )
		{
			this.deviceImageList = new DeviceImageList( );

			this.deviceImageList.AddImage( "action_Cancel_16xLG",           Properties.Resources.action_Cancel_16xLG );
			this.deviceImageList.AddImage( "abstr1",                        Properties.Resources.abstr1 );
			this.deviceImageList.AddImage( "action_add_16xLG",              Properties.Resources.action_add_16xLG );
			this.deviceImageList.AddImage( "ClassIcon",                     Properties.Resources.ClassIcon );
			this.deviceImageList.AddImage( "Class_489",                     Properties.Resources.Class_489 );
			this.deviceImageList.AddImage( "Enum_582",                      Properties.Resources.Enum_582 );
			this.deviceImageList.AddImage( "Enum_582_cancel",               Properties.Resources.Enum_582_cancel );
			this.deviceImageList.AddImage( "Event_594",                     Properties.Resources.Event_594 );
			this.deviceImageList.AddImage( "Event_594_exp",                 Properties.Resources.Event_594_exp );
			this.deviceImageList.AddImage( "FieldsHeader_12x",              Properties.Resources.FieldsHeader_12x );
			this.deviceImageList.AddImage( "FlagRed_16x",                   Properties.Resources.FlagRed_16x );
			this.deviceImageList.AddImage( "FlagSpace_16x",                 Properties.Resources.FlagSpace_16x );
			this.deviceImageList.AddImage( "flag_16xLG",                    Properties.Resources.flag_16xLG );
			this.deviceImageList.AddImage( "GenericVSEditor_9905",          Properties.Resources.GenericVSEditor_9905 );
			this.deviceImageList.AddImage( "HotSpot_10548",                 Properties.Resources.HotSpot_10548 );
			this.deviceImageList.AddImage( "ExtensionManager_vsix",         Properties.Resources.ExtensionManager_vsix );
			this.deviceImageList.AddImage( "HotSpot_10548_color",           Properties.Resources.HotSpot_10548_color );
			this.deviceImageList.AddImage( "library_16xLG",                 Properties.Resources.library_16xLG );
			this.deviceImageList.AddImage( "Method_636",                    Properties.Resources.Method_636 );
			this.deviceImageList.AddImage( "Module_648",                    Properties.Resources.Module_648 );
			this.deviceImageList.AddImage( "Module_blue",                   Properties.Resources.Module_blue );
			this.deviceImageList.AddImage( "Monitor_Screen_16xLG",          Properties.Resources.Monitor_Screen_16xLG );
			this.deviceImageList.AddImage( "Operator_660",                  Properties.Resources.Operator_660 );
			this.deviceImageList.AddImage( "PencilAngled_16xLG",            Properties.Resources.PencilAngled_16xLG );
			this.deviceImageList.AddImage( "Property_501",                  Properties.Resources.Property_501 );
			this.deviceImageList.AddImage( "server_Local_16xLG",            Properties.Resources.server_Local_16xLG );
			this.deviceImageList.AddImage( "star_16xLG",                    Properties.Resources.star_16xLG );
			this.deviceImageList.AddImage( "usbcontroller",                 Properties.Resources.usbcontroller );
			this.deviceImageList.AddImage( "VirtualMachine_16xLG",          Properties.Resources.VirtualMachine_16xLG );
			this.deviceImageList.AddImage( "WindowsAzure_16xLG",            Properties.Resources.WindowsAzure_16xLG );
			this.deviceImageList.AddImage( "WindowsAzure_16xLG_Cyan",       Properties.Resources.WindowsAzure_16xLG_Cyan );
			this.deviceImageList.AddImage( "xbox1Color_16x",                Properties.Resources.xbox1Color_16x );
			this.deviceImageList.AddImage( "hardware_16xLG",                Properties.Resources.hardware_16xLG );
			this.deviceImageList.AddImage( "hardware_16xLG_cancel",         Properties.Resources.hardware_16xLG_cancel );
			this.deviceImageList.AddImage( "robot",                         Properties.Resources.robot );
			this.deviceImageList.AddImage( "melsec",                        Properties.Resources.melsec );
			this.deviceImageList.AddImage( "siemens",                       Properties.Resources.siemens );
			this.deviceImageList.AddImage( "keyence",                       Properties.Resources.keyence );
			this.deviceImageList.AddImage( "ab",                            Properties.Resources.ab );
			this.deviceImageList.AddImage( "omron",                         Properties.Resources.omron );
			this.deviceImageList.AddImage( "modbus",                        Properties.Resources.modbus );
			this.deviceImageList.AddImage( "panasonic",                     Properties.Resources.panasonic );
			this.deviceImageList.AddImage( "abb",                           Properties.Resources.abb );
			this.deviceImageList.AddImage( "beckhoff",                      Properties.Resources.beckhoff );
			this.deviceImageList.AddImage( "Cloud_16xLG",                   Properties.Resources.Cloud_16xLG );
			this.deviceImageList.AddImage( "Copy_6524",                     Properties.Resources.Copy_6524 );
			this.deviceImageList.AddImage( "debug",                         Properties.Resources.debug );
			this.deviceImageList.AddImage( "delta",                         Properties.Resources.delta );
			this.deviceImageList.AddImage( "efort",                         Properties.Resources.efort );
			this.deviceImageList.AddImage( "fanuc",                         Properties.Resources.fanuc );
			this.deviceImageList.AddImage( "fatek",                         Properties.Resources.fatek );
			this.deviceImageList.AddImage( "fujifilm",                      Properties.Resources.fujifilm );
			this.deviceImageList.AddImage( "ge",                            Properties.Resources.ge );
			this.deviceImageList.AddImage( "glasses_16xLG",                 Properties.Resources.glasses_16xLG );
			this.deviceImageList.AddImage( "HslCommunication",              Properties.Resources.HslCommunication );
			this.deviceImageList.AddImage( "inovance",                      Properties.Resources.inovance );
			this.deviceImageList.AddImage( "kuka",                          Properties.Resources.kuka );
			this.deviceImageList.AddImage( "ls",                            Properties.Resources.ls );
			this.deviceImageList.AddImage( "mqtt",                          Properties.Resources.mqtt );
			this.deviceImageList.AddImage( "redis",                         Properties.Resources.redis );
			this.deviceImageList.AddImage( "schneider",                     Properties.Resources.schneider );
			this.deviceImageList.AddImage( "toledo",                        Properties.Resources.toledo );
			this.deviceImageList.AddImage( "websocket",                     Properties.Resources.websocket );
			this.deviceImageList.AddImage( "xinje",                         Properties.Resources.xinje );
			this.deviceImageList.AddImage( "yaskawa",                       Properties.Resources.yaskawa );
			this.deviceImageList.AddImage( "yokogawa",                      Properties.Resources.yokogawa );
			this.deviceImageList.AddImage( "zkt",                           Properties.Resources.zkt );
			this.deviceImageList.AddImage( "oee",                           Properties.Resources.oee );
			this.deviceImageList.AddImage( "vigor",                         Properties.Resources.vigor );
			this.deviceImageList.AddImage( "water",                         Properties.Resources.water );
			this.deviceImageList.AddImage( "SpherePreview",                 Properties.Resources.SpherePreview );
			this.deviceImageList.AddImage( "brackets_Square_16xMD",         Properties.Resources.brackets_Square_16xMD );
			this.deviceImageList.AddImage( "brackets_Curly_16xMD",          Properties.Resources.brackets_Curly_16xMD );
			this.deviceImageList.AddImage( "action_create_16xLG",           Properties.Resources.action_create_16xLG );
			this.deviceImageList.AddImage( "Database_node",                 Properties.Resources.Database_node );
			this.deviceImageList.AddImage( "Database_request",              Properties.Resources.Database_request );
			this.deviceImageList.AddImage( "house_16xLG",                   Properties.Resources.house_16xLG );
			this.deviceImageList.AddImage( "FileGroup_10135_16x",           Properties.Resources.FileGroup_10135_16x );
			this.deviceImageList.AddImage( "XMLFile_828_16x",               Properties.Resources.XMLFile_828_16x );
			this.deviceImageList.AddImage( "StatusAnnotations_Help_and_inconclusive_16xLG_color", Properties.Resources.StatusAnnotations_Help_and_inconclusive_16xLG_color );
			this.deviceImageList.AddImage( "StatusAnnotations_Complete_and_ok_16xLG_color",       Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color );
			this.deviceImageList.AddImage( "StatusAnnotations_Critical_16xLG_color",              Properties.Resources.StatusAnnotations_Critical_16xLG_color );
			this.deviceImageList.AddImage( "StatusAnnotations_Information_16xLG_color",           Properties.Resources.StatusAnnotations_Information_16xLG_color );
			this.deviceImageList.AddImage( "StatusAnnotations_Pause_16xLG_color",                 Properties.Resources.StatusAnnotations_Pause_16xLG_color );
			this.deviceImageList.AddImage( "StatusAnnotations_Play_16xLG_color",                  Properties.Resources.StatusAnnotations_Play_16xLG_color );
			this.deviceImageList.AddImage( "StatusAnnotations_Warning_16xLG_color",               Properties.Resources.StatusAnnotations_Warning_16xLG_color );
			this.deviceImageList.AddImage( "interface_16xLG",                                     Properties.Resources.interface_16xLG );
			this.deviceImageList.AddImage( "SerialPort",                                          Properties.Resources.SerialPort );
			this.deviceImageList.AddImage( "NetworkAdapter",                                      Properties.Resources.NetworkAdapter );
			this.deviceImageList.AddImage( "VirtualMachineGroup",                                 Properties.Resources.VirtualMachineGroup );



			this.deviceImageList.AddImage( "server_Local_16xLG_Green", GetGreenPoint( Properties.Resources.server_Local_16xLG ));
		}

		private byte[] ImageToByte( Image image )
		{
			MemoryStream ms = new MemoryStream( );
			image.Save( ms, image.RawFormat );
			return ms.GetBuffer( );
		}
		private Image ByteToImage( byte[] bImage )
		{
			System.IO.MemoryStream ms = new System.IO.MemoryStream( bImage );
			System.Drawing.Image image = System.Drawing.Image.FromStream( ms );
			return image;
		}

		private Bitmap GetGreenPoint( Image image )
		{
			Bitmap bitmap = (Bitmap)ByteToImage( ImageToByte( image ) );
			Graphics g = Graphics.FromImage( bitmap );

			g.DrawLine( Pens.Lime, 6, 3, 9, 3 );
			g.DrawLine( Pens.Lime, 6, 5, 9, 5 );
			bitmap.SetPixel( 9, 12, Color.Lime );
			g.Dispose( );
			return bitmap;
		}


		private void AddImageItem( string key, string pluginsType, Image image )
		{
			this.deviceImageList.AddImage( key, image );
			lock (lockPlugins)
			{
				if (!pluginsImage.ContainsKey( pluginsType ))
				{
					pluginsImage.Add( pluginsType, key );
				}
			}
		}

		/// <summary>
		/// 将插件包含的图标信息加载到对应的网关类对象里
		/// </summary>
		/// <param name="pluginsDevices">插件的信息</param>
		public void AddImageItem( PluginsDefinition[] pluginsDevices )
		{
			if (pluginsDevices != null)
			{
				foreach (PluginsDefinition pluginsDevice in pluginsDevices)
				{
					if (pluginsDevice.Icon16 != null)
					{
						Image image = Util.GetImageFromBytes( pluginsDevice.Icon16 );
						foreach (var item in pluginsDevice.DeviceDefinitions)
						{
							AddImageItem( pluginsDevice.DllName, item.Key, image );
						}
					}
				}
			}
		}

		/// <summary>
		/// 根据插件的类型来获取当前的图标关键字信息
		/// </summary>
		/// <param name="pluginsType">插件类型</param>
		/// <returns>图标的关键字信息</returns>
		public string GetImageKeyByPlugins( string pluginsType )
		{
			lock (lockPlugins)
			{
				if (pluginsImage.ContainsKey( pluginsType )) return pluginsImage[pluginsType];
				return "action_Cancel_16xLG";
			}
		}

		/// <summary>
		/// 获取当前的网关的设备的图片列表信息
		/// </summary>
		public DeviceImageList DeviceImageList => this.deviceImageList;


		private DeviceImageList deviceImageList;
		private Dictionary<string, string> pluginsImage = new Dictionary<string, string>( );
		private object lockPlugins = new object( );
	}
}
