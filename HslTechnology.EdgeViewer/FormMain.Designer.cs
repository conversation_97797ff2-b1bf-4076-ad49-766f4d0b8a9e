namespace HslTechnology.EdgeViewer
{
    partial class FormMain
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose( bool disposing )
        {
            if (disposing && (components != null))
            {
                components.Dispose( );
            }
            base.Dispose( disposing );
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent( )
        {
			this.statusStrip1 = new System.Windows.Forms.StatusStrip();
			this.toolStripStatusLabel4 = new System.Windows.Forms.ToolStripStatusLabel();
			this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
			this.toolStripStatusLabel2 = new System.Windows.Forms.ToolStripStatusLabel();
			this.toolStripStatusLabel3 = new System.Windows.Forms.ToolStripStatusLabel();
			this.vS2015BlueTheme1 = new WeifenLuo.WinFormsUI.Docking.VS2015BlueTheme();
			this.menuStrip1 = new System.Windows.Forms.MenuStrip();
			this.fileToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.viewToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.toolToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.themeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.blueThemeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.lightThemeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.deepThemeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.createGUIDToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.aboutToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.updateToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.startMqttServerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStrip1 = new System.Windows.Forms.ToolStrip();
			this.toolStripComboBox1 = new System.Windows.Forms.ToolStripComboBox();
			this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
			this.dockPanel1 = new WeifenLuo.WinFormsUI.Docking.DockPanel();
			this.vS2015LightTheme1 = new WeifenLuo.WinFormsUI.Docking.VS2015LightTheme();
			this.vS2015DarkTheme1 = new WeifenLuo.WinFormsUI.Docking.VS2015DarkTheme();
			this.toolStripButton_stopAndContinue = new System.Windows.Forms.ToolStripButton();
			this.toolStripButton_restartEdge = new System.Windows.Forms.ToolStripButton();
			this.newCreateToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.captureFileNewToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.openToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.captureFileOpenToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.edgeServerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.edgeMonitorToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.statusStrip1.SuspendLayout();
			this.menuStrip1.SuspendLayout();
			this.toolStrip1.SuspendLayout();
			this.SuspendLayout();
			// 
			// statusStrip1
			// 
			this.statusStrip1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
			this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel4,
            this.toolStripStatusLabel1,
            this.toolStripStatusLabel2,
            this.toolStripStatusLabel3});
			this.statusStrip1.Location = new System.Drawing.Point(0, 739);
			this.statusStrip1.Name = "statusStrip1";
			this.statusStrip1.Size = new System.Drawing.Size(1284, 22);
			this.statusStrip1.TabIndex = 2;
			this.statusStrip1.Text = "statusStrip1";
			// 
			// toolStripStatusLabel4
			// 
			this.toolStripStatusLabel4.AutoSize = false;
			this.toolStripStatusLabel4.ForeColor = System.Drawing.Color.WhiteSmoke;
			this.toolStripStatusLabel4.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.toolStripStatusLabel4.Name = "toolStripStatusLabel4";
			this.toolStripStatusLabel4.Size = new System.Drawing.Size(260, 17);
			this.toolStripStatusLabel4.Text = " 设备:";
			this.toolStripStatusLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// toolStripStatusLabel1
			// 
			this.toolStripStatusLabel1.ForeColor = System.Drawing.Color.WhiteSmoke;
			this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
			this.toolStripStatusLabel1.Size = new System.Drawing.Size(48, 17);
			this.toolStripStatusLabel1.Text = " 消息：";
			// 
			// toolStripStatusLabel2
			// 
			this.toolStripStatusLabel2.ForeColor = System.Drawing.Color.WhiteSmoke;
			this.toolStripStatusLabel2.Name = "toolStripStatusLabel2";
			this.toolStripStatusLabel2.Size = new System.Drawing.Size(0, 17);
			// 
			// toolStripStatusLabel3
			// 
			this.toolStripStatusLabel3.ForeColor = System.Drawing.Color.WhiteSmoke;
			this.toolStripStatusLabel3.Name = "toolStripStatusLabel3";
			this.toolStripStatusLabel3.Size = new System.Drawing.Size(131, 17);
			this.toolStripStatusLabel3.Text = "toolStripStatusLabel3";
			// 
			// menuStrip1
			// 
			this.menuStrip1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(215)))), ((int)(((byte)(218)))), ((int)(((byte)(233)))));
			this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.fileToolStripMenuItem,
            this.viewToolStripMenuItem,
            this.toolToolStripMenuItem,
            this.aboutToolStripMenuItem,
            this.updateToolStripMenuItem,
            this.startMqttServerToolStripMenuItem});
			this.menuStrip1.Location = new System.Drawing.Point(0, 0);
			this.menuStrip1.Name = "menuStrip1";
			this.menuStrip1.Size = new System.Drawing.Size(1284, 25);
			this.menuStrip1.TabIndex = 12;
			this.menuStrip1.Text = "menuStrip1";
			// 
			// fileToolStripMenuItem
			// 
			this.fileToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.newCreateToolStripMenuItem,
            this.openToolStripMenuItem});
			this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
			this.fileToolStripMenuItem.Size = new System.Drawing.Size(58, 21);
			this.fileToolStripMenuItem.Text = "文件(&F)";
			// 
			// viewToolStripMenuItem
			// 
			this.viewToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.edgeServerToolStripMenuItem,
            this.edgeMonitorToolStripMenuItem});
			this.viewToolStripMenuItem.Name = "viewToolStripMenuItem";
			this.viewToolStripMenuItem.Size = new System.Drawing.Size(60, 21);
			this.viewToolStripMenuItem.Text = "视图(&V)";
			// 
			// toolToolStripMenuItem
			// 
			this.toolToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.themeToolStripMenuItem,
            this.createGUIDToolStripMenuItem});
			this.toolToolStripMenuItem.Name = "toolToolStripMenuItem";
			this.toolToolStripMenuItem.Size = new System.Drawing.Size(59, 21);
			this.toolToolStripMenuItem.Text = "工具(&T)";
			// 
			// themeToolStripMenuItem
			// 
			this.themeToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.blueThemeToolStripMenuItem,
            this.lightThemeToolStripMenuItem,
            this.deepThemeToolStripMenuItem});
			this.themeToolStripMenuItem.Name = "themeToolStripMenuItem";
			this.themeToolStripMenuItem.Size = new System.Drawing.Size(131, 22);
			this.themeToolStripMenuItem.Text = "主题";
			// 
			// blueThemeToolStripMenuItem
			// 
			this.blueThemeToolStripMenuItem.Name = "blueThemeToolStripMenuItem";
			this.blueThemeToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
			this.blueThemeToolStripMenuItem.Text = "蓝色";
			// 
			// lightThemeToolStripMenuItem
			// 
			this.lightThemeToolStripMenuItem.Name = "lightThemeToolStripMenuItem";
			this.lightThemeToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
			this.lightThemeToolStripMenuItem.Text = "浅色";
			// 
			// deepThemeToolStripMenuItem
			// 
			this.deepThemeToolStripMenuItem.Name = "deepThemeToolStripMenuItem";
			this.deepThemeToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
			this.deepThemeToolStripMenuItem.Text = "深色";
			// 
			// createGUIDToolStripMenuItem
			// 
			this.createGUIDToolStripMenuItem.Name = "createGUIDToolStripMenuItem";
			this.createGUIDToolStripMenuItem.Size = new System.Drawing.Size(131, 22);
			this.createGUIDToolStripMenuItem.Text = "创建GUID";
			// 
			// aboutToolStripMenuItem
			// 
			this.aboutToolStripMenuItem.ForeColor = System.Drawing.Color.Black;
			this.aboutToolStripMenuItem.Name = "aboutToolStripMenuItem";
			this.aboutToolStripMenuItem.Size = new System.Drawing.Size(60, 21);
			this.aboutToolStripMenuItem.Text = "关于(&A)";
			// 
			// updateToolStripMenuItem
			// 
			this.updateToolStripMenuItem.ForeColor = System.Drawing.Color.Black;
			this.updateToolStripMenuItem.Name = "updateToolStripMenuItem";
			this.updateToolStripMenuItem.Size = new System.Drawing.Size(80, 21);
			this.updateToolStripMenuItem.Text = "更新服务器";
			// 
			// startMqttServerToolStripMenuItem
			// 
			this.startMqttServerToolStripMenuItem.ForeColor = System.Drawing.Color.Black;
			this.startMqttServerToolStripMenuItem.Name = "startMqttServerToolStripMenuItem";
			this.startMqttServerToolStripMenuItem.Size = new System.Drawing.Size(144, 21);
			this.startMqttServerToolStripMenuItem.Text = "启动测试的Mqtt服务器";
			// 
			// toolStrip1
			// 
			this.toolStrip1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(215)))), ((int)(((byte)(218)))), ((int)(((byte)(233)))));
			this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripComboBox1,
            this.toolStripButton_stopAndContinue,
            this.toolStripButton_restartEdge,
            this.toolStripSeparator1});
			this.toolStrip1.Location = new System.Drawing.Point(0, 25);
			this.toolStrip1.Name = "toolStrip1";
			this.toolStrip1.Size = new System.Drawing.Size(1284, 25);
			this.toolStrip1.TabIndex = 13;
			this.toolStrip1.Text = "toolStrip1";
			// 
			// toolStripComboBox1
			// 
			this.toolStripComboBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.toolStripComboBox1.Name = "toolStripComboBox1";
			this.toolStripComboBox1.Size = new System.Drawing.Size(171, 25);
			// 
			// toolStripSeparator1
			// 
			this.toolStripSeparator1.Name = "toolStripSeparator1";
			this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
			// 
			// dockPanel1
			// 
			this.dockPanel1.Location = new System.Drawing.Point(238, 100);
			this.dockPanel1.Name = "dockPanel1";
			this.dockPanel1.Size = new System.Drawing.Size(615, 299);
			this.dockPanel1.TabIndex = 14;
			// 
			// toolStripButton_stopAndContinue
			// 
			this.toolStripButton_stopAndContinue.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButton_stopAndContinue.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Pause;
			this.toolStripButton_stopAndContinue.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButton_stopAndContinue.Name = "toolStripButton_stopAndContinue";
			this.toolStripButton_stopAndContinue.Size = new System.Drawing.Size(23, 22);
			this.toolStripButton_stopAndContinue.Text = "停止或是继续网关所有设备的采集操作";
			// 
			// toolStripButton_restartEdge
			// 
			this.toolStripButton_restartEdge.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButton_restartEdge.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Refresh;
			this.toolStripButton_restartEdge.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButton_restartEdge.Name = "toolStripButton_restartEdge";
			this.toolStripButton_restartEdge.Size = new System.Drawing.Size(23, 22);
			this.toolStripButton_restartEdge.Text = "重启边缘网关";
			// 
			// newCreateToolStripMenuItem
			// 
			this.newCreateToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.captureFileNewToolStripMenuItem});
			this.newCreateToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.NewFile_6276;
			this.newCreateToolStripMenuItem.Name = "newCreateToolStripMenuItem";
			this.newCreateToolStripMenuItem.Size = new System.Drawing.Size(118, 22);
			this.newCreateToolStripMenuItem.Text = "新建(&N)";
			// 
			// captureFileNewToolStripMenuItem
			// 
			this.captureFileNewToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.XMLFile_828_16x;
			this.captureFileNewToolStripMenuItem.Name = "captureFileNewToolStripMenuItem";
			this.captureFileNewToolStripMenuItem.Size = new System.Drawing.Size(164, 22);
			this.captureFileNewToolStripMenuItem.Text = "采集配置文件(&C)";
			// 
			// openToolStripMenuItem
			// 
			this.openToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.captureFileOpenToolStripMenuItem});
			this.openToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.FileSystemEditor_5852;
			this.openToolStripMenuItem.Name = "openToolStripMenuItem";
			this.openToolStripMenuItem.Size = new System.Drawing.Size(118, 22);
			this.openToolStripMenuItem.Text = "打开(&O)";
			// 
			// captureFileOpenToolStripMenuItem
			// 
			this.captureFileOpenToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.XMLFile_828_16x;
			this.captureFileOpenToolStripMenuItem.Name = "captureFileOpenToolStripMenuItem";
			this.captureFileOpenToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
			this.captureFileOpenToolStripMenuItem.Text = "采集配置文件";
			// 
			// edgeServerToolStripMenuItem
			// 
			this.edgeServerToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Cloud_16xLG;
			this.edgeServerToolStripMenuItem.Name = "edgeServerToolStripMenuItem";
			this.edgeServerToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
			this.edgeServerToolStripMenuItem.Text = "边缘网关管理器";
			// 
			// edgeMonitorToolStripMenuItem
			// 
			this.edgeMonitorToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Monitor_Screen_16xLG;
			this.edgeMonitorToolStripMenuItem.Name = "edgeMonitorToolStripMenuItem";
			this.edgeMonitorToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
			this.edgeMonitorToolStripMenuItem.Text = "边缘网关监视界面";
			// 
			// FormMain
			// 
			this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = System.Drawing.Color.AliceBlue;
			this.ClientSize = new System.Drawing.Size(1284, 761);
			this.Controls.Add(this.dockPanel1);
			this.Controls.Add(this.toolStrip1);
			this.Controls.Add(this.menuStrip1);
			this.Controls.Add(this.statusStrip1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.IsMdiContainer = true;
			this.KeyPreview = true;
			this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
			this.Name = "FormMain";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "边缘网关系统客户端";
			this.Load += new System.EventHandler(this.FormMain_Load);
			this.statusStrip1.ResumeLayout(false);
			this.statusStrip1.PerformLayout();
			this.menuStrip1.ResumeLayout(false);
			this.menuStrip1.PerformLayout();
			this.toolStrip1.ResumeLayout(false);
			this.toolStrip1.PerformLayout();
			this.ResumeLayout(false);
			this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
		private WeifenLuo.WinFormsUI.Docking.VS2015BlueTheme vS2015BlueTheme1;
		private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel2;
		private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel3;
		private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel4;
		private System.Windows.Forms.MenuStrip menuStrip1;
		private System.Windows.Forms.ToolStripMenuItem aboutToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem updateToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem startMqttServerToolStripMenuItem;
		private System.Windows.Forms.ToolStrip toolStrip1;
		private WeifenLuo.WinFormsUI.Docking.DockPanel dockPanel1;
		private System.Windows.Forms.ToolStripMenuItem viewToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem edgeServerToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem edgeMonitorToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem fileToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem newCreateToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem captureFileNewToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem openToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem captureFileOpenToolStripMenuItem;
		private System.Windows.Forms.ToolStripComboBox toolStripComboBox1;
		private System.Windows.Forms.ToolStripButton toolStripButton_stopAndContinue;
		private System.Windows.Forms.ToolStripButton toolStripButton_restartEdge;
		private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
		private System.Windows.Forms.ToolStripMenuItem toolToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem themeToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem blueThemeToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem lightThemeToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem deepThemeToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem createGUIDToolStripMenuItem;
		private WeifenLuo.WinFormsUI.Docking.VS2015LightTheme vS2015LightTheme1;
		private WeifenLuo.WinFormsUI.Docking.VS2015DarkTheme vS2015DarkTheme1;
	}
}

