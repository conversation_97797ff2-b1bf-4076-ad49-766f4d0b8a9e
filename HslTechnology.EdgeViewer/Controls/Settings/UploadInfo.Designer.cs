
namespace HslTechnology.EdgeViewer.Controls.Settings
{
	partial class UploadInfo
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.textBox_uploadInteval = new System.Windows.Forms.TextBox();
			this.label12 = new System.Windows.Forms.Label();
			this.textBox_mqtt_password = new System.Windows.Forms.TextBox();
			this.label_mqtt_password = new System.Windows.Forms.Label();
			this.textBox_mqtt_username = new System.Windows.Forms.TextBox();
			this.label_mqtt_userName = new System.Windows.Forms.Label();
			this.textBox_mqtt_port = new System.Windows.Forms.TextBox();
			this.label8 = new System.Windows.Forms.Label();
			this.textBox_mqtt_ipaddress = new System.Windows.Forms.TextBox();
			this.label9 = new System.Windows.Forms.Label();
			this.checkBox_useMqtt = new System.Windows.Forms.CheckBox();
			this.textBox_redis_password = new System.Windows.Forms.TextBox();
			this.label6 = new System.Windows.Forms.Label();
			this.textBox_redis_port = new System.Windows.Forms.TextBox();
			this.label5 = new System.Windows.Forms.Label();
			this.textBox_redis_ipaddress = new System.Windows.Forms.TextBox();
			this.label4 = new System.Windows.Forms.Label();
			this.checkBox_useRedies = new System.Windows.Forms.CheckBox();
			this.textBox_redis_db = new System.Windows.Forms.TextBox();
			this.label1 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.textBox_mqtt_clientID = new System.Windows.Forms.TextBox();
			this.label3 = new System.Windows.Forms.Label();
			this.checkBox_subscription = new System.Windows.Forms.CheckBox();
			this.label11 = new System.Windows.Forms.Label();
			this.panel1 = new System.Windows.Forms.Panel();
			this.textBox_dataupload_forceTimer = new System.Windows.Forms.TextBox();
			this.label7 = new System.Windows.Forms.Label();
			this.checkBox_dataChange = new System.Windows.Forms.CheckBox();
			this.label13 = new System.Windows.Forms.Label();
			this.radioButton3 = new System.Windows.Forms.RadioButton();
			this.radioButton2 = new System.Windows.Forms.RadioButton();
			this.radioButton1 = new System.Windows.Forms.RadioButton();
			this.label14 = new System.Windows.Forms.Label();
			this.label15 = new System.Windows.Forms.Label();
			this.checkBox_mqtt_retain = new System.Windows.Forms.CheckBox();
			this.checkBox_upload_alarm = new System.Windows.Forms.CheckBox();
			this.label16 = new System.Windows.Forms.Label();
			this.textBox_caFile = new System.Windows.Forms.TextBox();
			this.button_select = new System.Windows.Forms.Button();
			this.button_clear = new System.Windows.Forms.Button();
			this.checkBox_mqtt_ssl = new System.Windows.Forms.CheckBox();
			this.panel2 = new System.Windows.Forms.Panel();
			this.radioButton_mqtt_jetlinks = new System.Windows.Forms.RadioButton();
			this.radioButton_mqtt_regular = new System.Windows.Forms.RadioButton();
			this.tabControl1 = new System.Windows.Forms.TabControl();
			this.tabPage1 = new System.Windows.Forms.TabPage();
			this.tabPage2 = new System.Windows.Forms.TabPage();
			this.checkBox_useMqtt2 = new System.Windows.Forms.CheckBox();
			this.panel3 = new System.Windows.Forms.Panel();
			this.radioButton_mqtt_jetlinks2 = new System.Windows.Forms.RadioButton();
			this.radioButton_mqtt_regular2 = new System.Windows.Forms.RadioButton();
			this.label10 = new System.Windows.Forms.Label();
			this.checkBox_mqtt_ssl2 = new System.Windows.Forms.CheckBox();
			this.textBox_mqtt_ipaddress2 = new System.Windows.Forms.TextBox();
			this.button_clear2 = new System.Windows.Forms.Button();
			this.label17 = new System.Windows.Forms.Label();
			this.button_select2 = new System.Windows.Forms.Button();
			this.textBox_mqtt_port2 = new System.Windows.Forms.TextBox();
			this.textBox_caFile2 = new System.Windows.Forms.TextBox();
			this.label18 = new System.Windows.Forms.Label();
			this.label19 = new System.Windows.Forms.Label();
			this.textBox_mqtt_username2 = new System.Windows.Forms.TextBox();
			this.label20 = new System.Windows.Forms.Label();
			this.checkBox_mqtt_retain2 = new System.Windows.Forms.CheckBox();
			this.textBox_mqtt_password2 = new System.Windows.Forms.TextBox();
			this.label21 = new System.Windows.Forms.Label();
			this.label22 = new System.Windows.Forms.Label();
			this.textBox_mqtt_clientID2 = new System.Windows.Forms.TextBox();
			this.label23 = new System.Windows.Forms.Label();
			this.panel1.SuspendLayout();
			this.panel2.SuspendLayout();
			this.tabControl1.SuspendLayout();
			this.tabPage1.SuspendLayout();
			this.tabPage2.SuspendLayout();
			this.panel3.SuspendLayout();
			this.SuspendLayout();
			// 
			// textBox_uploadInteval
			// 
			this.textBox_uploadInteval.Location = new System.Drawing.Point(99, 358);
			this.textBox_uploadInteval.Name = "textBox_uploadInteval";
			this.textBox_uploadInteval.Size = new System.Drawing.Size(91, 23);
			this.textBox_uploadInteval.TabIndex = 131;
			this.textBox_uploadInteval.Text = "1000";
			// 
			// label12
			// 
			this.label12.AutoSize = true;
			this.label12.Location = new System.Drawing.Point(1, 361);
			this.label12.Name = "label12";
			this.label12.Size = new System.Drawing.Size(92, 17);
			this.label12.TabIndex = 130;
			this.label12.Text = "数据上传间隔：";
			// 
			// textBox_mqtt_password
			// 
			this.textBox_mqtt_password.Location = new System.Drawing.Point(330, 81);
			this.textBox_mqtt_password.Name = "textBox_mqtt_password";
			this.textBox_mqtt_password.Size = new System.Drawing.Size(132, 23);
			this.textBox_mqtt_password.TabIndex = 129;
			// 
			// label_mqtt_password
			// 
			this.label_mqtt_password.AutoSize = true;
			this.label_mqtt_password.Location = new System.Drawing.Point(256, 84);
			this.label_mqtt_password.Name = "label_mqtt_password";
			this.label_mqtt_password.Size = new System.Drawing.Size(44, 17);
			this.label_mqtt_password.TabIndex = 121;
			this.label_mqtt_password.Text = "密码：";
			// 
			// textBox_mqtt_username
			// 
			this.textBox_mqtt_username.Location = new System.Drawing.Point(94, 81);
			this.textBox_mqtt_username.Name = "textBox_mqtt_username";
			this.textBox_mqtt_username.Size = new System.Drawing.Size(140, 23);
			this.textBox_mqtt_username.TabIndex = 128;
			// 
			// label_mqtt_userName
			// 
			this.label_mqtt_userName.AutoSize = true;
			this.label_mqtt_userName.Location = new System.Drawing.Point(20, 84);
			this.label_mqtt_userName.Name = "label_mqtt_userName";
			this.label_mqtt_userName.Size = new System.Drawing.Size(56, 17);
			this.label_mqtt_userName.TabIndex = 120;
			this.label_mqtt_userName.Text = "用户名：";
			// 
			// textBox_mqtt_port
			// 
			this.textBox_mqtt_port.Location = new System.Drawing.Point(330, 53);
			this.textBox_mqtt_port.Name = "textBox_mqtt_port";
			this.textBox_mqtt_port.Size = new System.Drawing.Size(132, 23);
			this.textBox_mqtt_port.TabIndex = 127;
			this.textBox_mqtt_port.Text = "1883";
			// 
			// label8
			// 
			this.label8.AutoSize = true;
			this.label8.Location = new System.Drawing.Point(256, 56);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(76, 17);
			this.label8.TabIndex = 119;
			this.label8.Text = "Mqtt 端口：";
			// 
			// textBox_mqtt_ipaddress
			// 
			this.textBox_mqtt_ipaddress.Location = new System.Drawing.Point(94, 53);
			this.textBox_mqtt_ipaddress.Name = "textBox_mqtt_ipaddress";
			this.textBox_mqtt_ipaddress.Size = new System.Drawing.Size(140, 23);
			this.textBox_mqtt_ipaddress.TabIndex = 126;
			// 
			// label9
			// 
			this.label9.AutoSize = true;
			this.label9.Location = new System.Drawing.Point(20, 56);
			this.label9.Name = "label9";
			this.label9.Size = new System.Drawing.Size(64, 17);
			this.label9.TabIndex = 118;
			this.label9.Text = "Mqtt Ip：";
			// 
			// checkBox_useMqtt
			// 
			this.checkBox_useMqtt.AutoSize = true;
			this.checkBox_useMqtt.Location = new System.Drawing.Point(3, 6);
			this.checkBox_useMqtt.Name = "checkBox_useMqtt";
			this.checkBox_useMqtt.Size = new System.Drawing.Size(103, 21);
			this.checkBox_useMqtt.TabIndex = 117;
			this.checkBox_useMqtt.Text = "启动Mqtt上传";
			this.checkBox_useMqtt.UseVisualStyleBackColor = true;
			// 
			// textBox_redis_password
			// 
			this.textBox_redis_password.Location = new System.Drawing.Point(94, 52);
			this.textBox_redis_password.Name = "textBox_redis_password";
			this.textBox_redis_password.Size = new System.Drawing.Size(140, 23);
			this.textBox_redis_password.TabIndex = 125;
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.Location = new System.Drawing.Point(20, 55);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(76, 17);
			this.label6.TabIndex = 116;
			this.label6.Text = "Redis密码：";
			// 
			// textBox_redis_port
			// 
			this.textBox_redis_port.Location = new System.Drawing.Point(330, 24);
			this.textBox_redis_port.Name = "textBox_redis_port";
			this.textBox_redis_port.Size = new System.Drawing.Size(138, 23);
			this.textBox_redis_port.TabIndex = 124;
			this.textBox_redis_port.Text = "6379";
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(256, 27);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(76, 17);
			this.label5.TabIndex = 115;
			this.label5.Text = "Redis端口：";
			// 
			// textBox_redis_ipaddress
			// 
			this.textBox_redis_ipaddress.Location = new System.Drawing.Point(94, 24);
			this.textBox_redis_ipaddress.Name = "textBox_redis_ipaddress";
			this.textBox_redis_ipaddress.Size = new System.Drawing.Size(140, 23);
			this.textBox_redis_ipaddress.TabIndex = 123;
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(20, 27);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(68, 17);
			this.label4.TabIndex = 114;
			this.label4.Text = "Redis Ip：";
			// 
			// checkBox_useRedies
			// 
			this.checkBox_useRedies.AutoSize = true;
			this.checkBox_useRedies.Location = new System.Drawing.Point(3, 3);
			this.checkBox_useRedies.Name = "checkBox_useRedies";
			this.checkBox_useRedies.Size = new System.Drawing.Size(107, 21);
			this.checkBox_useRedies.TabIndex = 113;
			this.checkBox_useRedies.Text = "是否启动Redis";
			this.checkBox_useRedies.UseVisualStyleBackColor = true;
			// 
			// textBox_redis_db
			// 
			this.textBox_redis_db.Location = new System.Drawing.Point(330, 52);
			this.textBox_redis_db.Name = "textBox_redis_db";
			this.textBox_redis_db.Size = new System.Drawing.Size(138, 23);
			this.textBox_redis_db.TabIndex = 133;
			this.textBox_redis_db.Text = "0";
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(256, 55);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(49, 17);
			this.label1.TabIndex = 132;
			this.label1.Text = "DB块：";
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.ForeColor = System.Drawing.Color.Gray;
			this.label2.Location = new System.Drawing.Point(196, 361);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(76, 17);
			this.label2.TabIndex = 134;
			this.label2.Text = "(单位：毫秒)";
			// 
			// textBox_mqtt_clientID
			// 
			this.textBox_mqtt_clientID.Location = new System.Drawing.Point(94, 109);
			this.textBox_mqtt_clientID.Name = "textBox_mqtt_clientID";
			this.textBox_mqtt_clientID.Size = new System.Drawing.Size(280, 23);
			this.textBox_mqtt_clientID.TabIndex = 136;
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(20, 112);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(72, 17);
			this.label3.TabIndex = 135;
			this.label3.Text = "Cliend Id：";
			// 
			// checkBox_subscription
			// 
			this.checkBox_subscription.AutoSize = true;
			this.checkBox_subscription.Location = new System.Drawing.Point(3, 389);
			this.checkBox_subscription.Name = "checkBox_subscription";
			this.checkBox_subscription.Size = new System.Drawing.Size(159, 21);
			this.checkBox_subscription.TabIndex = 137;
			this.checkBox_subscription.Text = "开启所有数据点发布订阅";
			this.checkBox_subscription.UseVisualStyleBackColor = true;
			// 
			// label11
			// 
			this.label11.AutoSize = true;
			this.label11.ForeColor = System.Drawing.Color.DimGray;
			this.label11.Location = new System.Drawing.Point(105, 7);
			this.label11.Name = "label11";
			this.label11.Size = new System.Drawing.Size(200, 17);
			this.label11.TabIndex = 139;
			this.label11.Text = " (以客户端的形式发布到以下服务器)";
			// 
			// panel1
			// 
			this.panel1.Controls.Add(this.textBox_dataupload_forceTimer);
			this.panel1.Controls.Add(this.label7);
			this.panel1.Controls.Add(this.checkBox_dataChange);
			this.panel1.Controls.Add(this.label13);
			this.panel1.Controls.Add(this.radioButton3);
			this.panel1.Controls.Add(this.radioButton2);
			this.panel1.Controls.Add(this.radioButton1);
			this.panel1.Location = new System.Drawing.Point(0, 305);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(480, 48);
			this.panel1.TabIndex = 141;
			// 
			// textBox_dataupload_forceTimer
			// 
			this.textBox_dataupload_forceTimer.Location = new System.Drawing.Point(387, 23);
			this.textBox_dataupload_forceTimer.Name = "textBox_dataupload_forceTimer";
			this.textBox_dataupload_forceTimer.Size = new System.Drawing.Size(81, 23);
			this.textBox_dataupload_forceTimer.TabIndex = 149;
			this.textBox_dataupload_forceTimer.Text = "-1";
			this.textBox_dataupload_forceTimer.Visible = false;
			// 
			// label7
			// 
			this.label7.AutoSize = true;
			this.label7.Location = new System.Drawing.Point(249, 27);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(132, 17);
			this.label7.TabIndex = 148;
			this.label7.Text = "同时强制上传周期(ms):";
			this.label7.Visible = false;
			// 
			// checkBox_dataChange
			// 
			this.checkBox_dataChange.AutoSize = true;
			this.checkBox_dataChange.Location = new System.Drawing.Point(93, 26);
			this.checkBox_dataChange.Name = "checkBox_dataChange";
			this.checkBox_dataChange.Size = new System.Drawing.Size(123, 21);
			this.checkBox_dataChange.TabIndex = 147;
			this.checkBox_dataChange.Text = "有数据变化才发布";
			this.checkBox_dataChange.UseVisualStyleBackColor = true;
			// 
			// label13
			// 
			this.label13.AutoSize = true;
			this.label13.Location = new System.Drawing.Point(20, 4);
			this.label13.Name = "label13";
			this.label13.Size = new System.Drawing.Size(68, 17);
			this.label13.TabIndex = 121;
			this.label13.Text = "发布模式：";
			// 
			// radioButton3
			// 
			this.radioButton3.AutoSize = true;
			this.radioButton3.Location = new System.Drawing.Point(341, 2);
			this.radioButton3.Name = "radioButton3";
			this.radioButton3.Size = new System.Drawing.Size(110, 21);
			this.radioButton3.TabIndex = 2;
			this.radioButton3.TabStop = true;
			this.radioButton3.Text = "数据标签单主题";
			this.radioButton3.UseVisualStyleBackColor = true;
			// 
			// radioButton2
			// 
			this.radioButton2.AutoSize = true;
			this.radioButton2.Checked = true;
			this.radioButton2.Location = new System.Drawing.Point(218, 2);
			this.radioButton2.Name = "radioButton2";
			this.radioButton2.Size = new System.Drawing.Size(86, 21);
			this.radioButton2.TabIndex = 1;
			this.radioButton2.TabStop = true;
			this.radioButton2.Text = "设备单主题";
			this.radioButton2.UseVisualStyleBackColor = true;
			// 
			// radioButton1
			// 
			this.radioButton1.AutoSize = true;
			this.radioButton1.Location = new System.Drawing.Point(93, 2);
			this.radioButton1.Name = "radioButton1";
			this.radioButton1.Size = new System.Drawing.Size(86, 21);
			this.radioButton1.TabIndex = 0;
			this.radioButton1.TabStop = true;
			this.radioButton1.Text = "网关单主题";
			this.radioButton1.UseVisualStyleBackColor = true;
			// 
			// label14
			// 
			this.label14.AutoSize = true;
			this.label14.ForeColor = System.Drawing.Color.DimGray;
			this.label14.Location = new System.Drawing.Point(166, 390);
			this.label14.Name = "label14";
			this.label14.Size = new System.Drawing.Size(152, 17);
			this.label14.TabIndex = 143;
			this.label14.Text = " (支持标量数据，支持数组)";
			// 
			// label15
			// 
			this.label15.AutoSize = true;
			this.label15.ForeColor = System.Drawing.Color.Gray;
			this.label15.Location = new System.Drawing.Point(96, 133);
			this.label15.Name = "label15";
			this.label15.Size = new System.Drawing.Size(243, 17);
			this.label15.TabIndex = 144;
			this.label15.Text = "默认为空，为空表示使用网关的EdgeID信息";
			// 
			// checkBox_mqtt_retain
			// 
			this.checkBox_mqtt_retain.AutoSize = true;
			this.checkBox_mqtt_retain.Location = new System.Drawing.Point(388, 112);
			this.checkBox_mqtt_retain.Name = "checkBox_mqtt_retain";
			this.checkBox_mqtt_retain.Size = new System.Drawing.Size(69, 21);
			this.checkBox_mqtt_retain.TabIndex = 145;
			this.checkBox_mqtt_retain.Text = "Retain?";
			this.checkBox_mqtt_retain.UseVisualStyleBackColor = true;
			// 
			// checkBox_upload_alarm
			// 
			this.checkBox_upload_alarm.AutoSize = true;
			this.checkBox_upload_alarm.Location = new System.Drawing.Point(301, 360);
			this.checkBox_upload_alarm.Name = "checkBox_upload_alarm";
			this.checkBox_upload_alarm.Size = new System.Drawing.Size(99, 21);
			this.checkBox_upload_alarm.TabIndex = 146;
			this.checkBox_upload_alarm.Text = "上传报警数据";
			this.checkBox_upload_alarm.UseVisualStyleBackColor = true;
			// 
			// label16
			// 
			this.label16.AutoSize = true;
			this.label16.Location = new System.Drawing.Point(96, 157);
			this.label16.Name = "label16";
			this.label16.Size = new System.Drawing.Size(44, 17);
			this.label16.TabIndex = 147;
			this.label16.Text = "证书：";
			// 
			// textBox_caFile
			// 
			this.textBox_caFile.Location = new System.Drawing.Point(137, 154);
			this.textBox_caFile.Name = "textBox_caFile";
			this.textBox_caFile.ReadOnly = true;
			this.textBox_caFile.Size = new System.Drawing.Size(195, 23);
			this.textBox_caFile.TabIndex = 148;
			// 
			// button_select
			// 
			this.button_select.Location = new System.Drawing.Point(338, 151);
			this.button_select.Name = "button_select";
			this.button_select.Size = new System.Drawing.Size(62, 27);
			this.button_select.TabIndex = 149;
			this.button_select.Text = "选择";
			this.button_select.UseVisualStyleBackColor = true;
			this.button_select.Click += new System.EventHandler(this.button_select_Click);
			// 
			// button_clear
			// 
			this.button_clear.Location = new System.Drawing.Point(406, 151);
			this.button_clear.Name = "button_clear";
			this.button_clear.Size = new System.Drawing.Size(62, 27);
			this.button_clear.TabIndex = 150;
			this.button_clear.Text = " 清除";
			this.button_clear.UseVisualStyleBackColor = true;
			this.button_clear.Click += new System.EventHandler(this.button_clear_Click);
			// 
			// checkBox_mqtt_ssl
			// 
			this.checkBox_mqtt_ssl.AutoSize = true;
			this.checkBox_mqtt_ssl.Location = new System.Drawing.Point(22, 156);
			this.checkBox_mqtt_ssl.Name = "checkBox_mqtt_ssl";
			this.checkBox_mqtt_ssl.Size = new System.Drawing.Size(72, 21);
			this.checkBox_mqtt_ssl.TabIndex = 151;
			this.checkBox_mqtt_ssl.Text = "SSL/TLS";
			this.checkBox_mqtt_ssl.UseVisualStyleBackColor = true;
			// 
			// panel2
			// 
			this.panel2.Controls.Add(this.radioButton_mqtt_jetlinks);
			this.panel2.Controls.Add(this.radioButton_mqtt_regular);
			this.panel2.Location = new System.Drawing.Point(16, 26);
			this.panel2.Name = "panel2";
			this.panel2.Size = new System.Drawing.Size(461, 25);
			this.panel2.TabIndex = 152;
			// 
			// radioButton_mqtt_jetlinks
			// 
			this.radioButton_mqtt_jetlinks.AutoSize = true;
			this.radioButton_mqtt_jetlinks.Location = new System.Drawing.Point(112, 3);
			this.radioButton_mqtt_jetlinks.Name = "radioButton_mqtt_jetlinks";
			this.radioButton_mqtt_jetlinks.Size = new System.Drawing.Size(71, 21);
			this.radioButton_mqtt_jetlinks.TabIndex = 1;
			this.radioButton_mqtt_jetlinks.Text = "JetLinks";
			this.radioButton_mqtt_jetlinks.UseVisualStyleBackColor = true;
			// 
			// radioButton_mqtt_regular
			// 
			this.radioButton_mqtt_regular.AutoSize = true;
			this.radioButton_mqtt_regular.Checked = true;
			this.radioButton_mqtt_regular.Location = new System.Drawing.Point(6, 3);
			this.radioButton_mqtt_regular.Name = "radioButton_mqtt_regular";
			this.radioButton_mqtt_regular.Size = new System.Drawing.Size(86, 21);
			this.radioButton_mqtt_regular.TabIndex = 0;
			this.radioButton_mqtt_regular.TabStop = true;
			this.radioButton_mqtt_regular.Text = "普通MQTT";
			this.radioButton_mqtt_regular.UseVisualStyleBackColor = true;
			// 
			// tabControl1
			// 
			this.tabControl1.Controls.Add(this.tabPage1);
			this.tabControl1.Controls.Add(this.tabPage2);
			this.tabControl1.Location = new System.Drawing.Point(3, 88);
			this.tabControl1.Name = "tabControl1";
			this.tabControl1.SelectedIndex = 0;
			this.tabControl1.Size = new System.Drawing.Size(476, 213);
			this.tabControl1.TabIndex = 153;
			// 
			// tabPage1
			// 
			this.tabPage1.Controls.Add(this.checkBox_useMqtt);
			this.tabPage1.Controls.Add(this.panel2);
			this.tabPage1.Controls.Add(this.label9);
			this.tabPage1.Controls.Add(this.checkBox_mqtt_ssl);
			this.tabPage1.Controls.Add(this.textBox_mqtt_ipaddress);
			this.tabPage1.Controls.Add(this.button_clear);
			this.tabPage1.Controls.Add(this.label8);
			this.tabPage1.Controls.Add(this.button_select);
			this.tabPage1.Controls.Add(this.textBox_mqtt_port);
			this.tabPage1.Controls.Add(this.textBox_caFile);
			this.tabPage1.Controls.Add(this.label_mqtt_userName);
			this.tabPage1.Controls.Add(this.label16);
			this.tabPage1.Controls.Add(this.textBox_mqtt_username);
			this.tabPage1.Controls.Add(this.label_mqtt_password);
			this.tabPage1.Controls.Add(this.checkBox_mqtt_retain);
			this.tabPage1.Controls.Add(this.textBox_mqtt_password);
			this.tabPage1.Controls.Add(this.label15);
			this.tabPage1.Controls.Add(this.label3);
			this.tabPage1.Controls.Add(this.textBox_mqtt_clientID);
			this.tabPage1.Controls.Add(this.label11);
			this.tabPage1.Location = new System.Drawing.Point(4, 26);
			this.tabPage1.Name = "tabPage1";
			this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
			this.tabPage1.Size = new System.Drawing.Size(468, 183);
			this.tabPage1.TabIndex = 0;
			this.tabPage1.Text = "MQTT1";
			this.tabPage1.UseVisualStyleBackColor = true;
			// 
			// tabPage2
			// 
			this.tabPage2.Controls.Add(this.checkBox_useMqtt2);
			this.tabPage2.Controls.Add(this.panel3);
			this.tabPage2.Controls.Add(this.label10);
			this.tabPage2.Controls.Add(this.checkBox_mqtt_ssl2);
			this.tabPage2.Controls.Add(this.textBox_mqtt_ipaddress2);
			this.tabPage2.Controls.Add(this.button_clear2);
			this.tabPage2.Controls.Add(this.label17);
			this.tabPage2.Controls.Add(this.button_select2);
			this.tabPage2.Controls.Add(this.textBox_mqtt_port2);
			this.tabPage2.Controls.Add(this.textBox_caFile2);
			this.tabPage2.Controls.Add(this.label18);
			this.tabPage2.Controls.Add(this.label19);
			this.tabPage2.Controls.Add(this.textBox_mqtt_username2);
			this.tabPage2.Controls.Add(this.label20);
			this.tabPage2.Controls.Add(this.checkBox_mqtt_retain2);
			this.tabPage2.Controls.Add(this.textBox_mqtt_password2);
			this.tabPage2.Controls.Add(this.label21);
			this.tabPage2.Controls.Add(this.label22);
			this.tabPage2.Controls.Add(this.textBox_mqtt_clientID2);
			this.tabPage2.Controls.Add(this.label23);
			this.tabPage2.Location = new System.Drawing.Point(4, 26);
			this.tabPage2.Name = "tabPage2";
			this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
			this.tabPage2.Size = new System.Drawing.Size(468, 183);
			this.tabPage2.TabIndex = 1;
			this.tabPage2.Text = "MQTT2";
			this.tabPage2.UseVisualStyleBackColor = true;
			// 
			// checkBox_useMqtt2
			// 
			this.checkBox_useMqtt2.AutoSize = true;
			this.checkBox_useMqtt2.Location = new System.Drawing.Point(3, 6);
			this.checkBox_useMqtt2.Name = "checkBox_useMqtt2";
			this.checkBox_useMqtt2.Size = new System.Drawing.Size(103, 21);
			this.checkBox_useMqtt2.TabIndex = 153;
			this.checkBox_useMqtt2.Text = "启动Mqtt上传";
			this.checkBox_useMqtt2.UseVisualStyleBackColor = true;
			// 
			// panel3
			// 
			this.panel3.Controls.Add(this.radioButton_mqtt_jetlinks2);
			this.panel3.Controls.Add(this.radioButton_mqtt_regular2);
			this.panel3.Location = new System.Drawing.Point(16, 26);
			this.panel3.Name = "panel3";
			this.panel3.Size = new System.Drawing.Size(461, 25);
			this.panel3.TabIndex = 172;
			// 
			// radioButton_mqtt_jetlinks2
			// 
			this.radioButton_mqtt_jetlinks2.AutoSize = true;
			this.radioButton_mqtt_jetlinks2.Location = new System.Drawing.Point(112, 3);
			this.radioButton_mqtt_jetlinks2.Name = "radioButton_mqtt_jetlinks2";
			this.radioButton_mqtt_jetlinks2.Size = new System.Drawing.Size(71, 21);
			this.radioButton_mqtt_jetlinks2.TabIndex = 1;
			this.radioButton_mqtt_jetlinks2.Text = "JetLinks";
			this.radioButton_mqtt_jetlinks2.UseVisualStyleBackColor = true;
			// 
			// radioButton_mqtt_regular2
			// 
			this.radioButton_mqtt_regular2.AutoSize = true;
			this.radioButton_mqtt_regular2.Checked = true;
			this.radioButton_mqtt_regular2.Location = new System.Drawing.Point(6, 3);
			this.radioButton_mqtt_regular2.Name = "radioButton_mqtt_regular2";
			this.radioButton_mqtt_regular2.Size = new System.Drawing.Size(86, 21);
			this.radioButton_mqtt_regular2.TabIndex = 0;
			this.radioButton_mqtt_regular2.TabStop = true;
			this.radioButton_mqtt_regular2.Text = "普通MQTT";
			this.radioButton_mqtt_regular2.UseVisualStyleBackColor = true;
			// 
			// label10
			// 
			this.label10.AutoSize = true;
			this.label10.Location = new System.Drawing.Point(20, 56);
			this.label10.Name = "label10";
			this.label10.Size = new System.Drawing.Size(64, 17);
			this.label10.TabIndex = 154;
			this.label10.Text = "Mqtt Ip：";
			// 
			// checkBox_mqtt_ssl2
			// 
			this.checkBox_mqtt_ssl2.AutoSize = true;
			this.checkBox_mqtt_ssl2.Location = new System.Drawing.Point(22, 156);
			this.checkBox_mqtt_ssl2.Name = "checkBox_mqtt_ssl2";
			this.checkBox_mqtt_ssl2.Size = new System.Drawing.Size(72, 21);
			this.checkBox_mqtt_ssl2.TabIndex = 171;
			this.checkBox_mqtt_ssl2.Text = "SSL/TLS";
			this.checkBox_mqtt_ssl2.UseVisualStyleBackColor = true;
			// 
			// textBox_mqtt_ipaddress2
			// 
			this.textBox_mqtt_ipaddress2.Location = new System.Drawing.Point(94, 53);
			this.textBox_mqtt_ipaddress2.Name = "textBox_mqtt_ipaddress2";
			this.textBox_mqtt_ipaddress2.Size = new System.Drawing.Size(140, 23);
			this.textBox_mqtt_ipaddress2.TabIndex = 158;
			// 
			// button_clear2
			// 
			this.button_clear2.Location = new System.Drawing.Point(406, 151);
			this.button_clear2.Name = "button_clear2";
			this.button_clear2.Size = new System.Drawing.Size(62, 27);
			this.button_clear2.TabIndex = 170;
			this.button_clear2.Text = " 清除";
			this.button_clear2.UseVisualStyleBackColor = true;
			this.button_clear2.Click += new System.EventHandler(this.button_clear2_Click);
			// 
			// label17
			// 
			this.label17.AutoSize = true;
			this.label17.Location = new System.Drawing.Point(256, 56);
			this.label17.Name = "label17";
			this.label17.Size = new System.Drawing.Size(76, 17);
			this.label17.TabIndex = 155;
			this.label17.Text = "Mqtt 端口：";
			// 
			// button_select2
			// 
			this.button_select2.Location = new System.Drawing.Point(338, 151);
			this.button_select2.Name = "button_select2";
			this.button_select2.Size = new System.Drawing.Size(62, 27);
			this.button_select2.TabIndex = 169;
			this.button_select2.Text = "选择";
			this.button_select2.UseVisualStyleBackColor = true;
			this.button_select2.Click += new System.EventHandler(this.button_select2_Click);
			// 
			// textBox_mqtt_port2
			// 
			this.textBox_mqtt_port2.Location = new System.Drawing.Point(330, 53);
			this.textBox_mqtt_port2.Name = "textBox_mqtt_port2";
			this.textBox_mqtt_port2.Size = new System.Drawing.Size(132, 23);
			this.textBox_mqtt_port2.TabIndex = 159;
			this.textBox_mqtt_port2.Text = "1883";
			// 
			// textBox_caFile2
			// 
			this.textBox_caFile2.Location = new System.Drawing.Point(137, 154);
			this.textBox_caFile2.Name = "textBox_caFile2";
			this.textBox_caFile2.ReadOnly = true;
			this.textBox_caFile2.Size = new System.Drawing.Size(195, 23);
			this.textBox_caFile2.TabIndex = 168;
			// 
			// label18
			// 
			this.label18.AutoSize = true;
			this.label18.Location = new System.Drawing.Point(20, 84);
			this.label18.Name = "label18";
			this.label18.Size = new System.Drawing.Size(56, 17);
			this.label18.TabIndex = 156;
			this.label18.Text = "用户名：";
			// 
			// label19
			// 
			this.label19.AutoSize = true;
			this.label19.Location = new System.Drawing.Point(96, 157);
			this.label19.Name = "label19";
			this.label19.Size = new System.Drawing.Size(44, 17);
			this.label19.TabIndex = 167;
			this.label19.Text = "证书：";
			// 
			// textBox_mqtt_username2
			// 
			this.textBox_mqtt_username2.Location = new System.Drawing.Point(94, 81);
			this.textBox_mqtt_username2.Name = "textBox_mqtt_username2";
			this.textBox_mqtt_username2.Size = new System.Drawing.Size(140, 23);
			this.textBox_mqtt_username2.TabIndex = 160;
			// 
			// label20
			// 
			this.label20.AutoSize = true;
			this.label20.Location = new System.Drawing.Point(256, 84);
			this.label20.Name = "label20";
			this.label20.Size = new System.Drawing.Size(44, 17);
			this.label20.TabIndex = 157;
			this.label20.Text = "密码：";
			// 
			// checkBox_mqtt_retain2
			// 
			this.checkBox_mqtt_retain2.AutoSize = true;
			this.checkBox_mqtt_retain2.Location = new System.Drawing.Point(388, 112);
			this.checkBox_mqtt_retain2.Name = "checkBox_mqtt_retain2";
			this.checkBox_mqtt_retain2.Size = new System.Drawing.Size(69, 21);
			this.checkBox_mqtt_retain2.TabIndex = 166;
			this.checkBox_mqtt_retain2.Text = "Retain?";
			this.checkBox_mqtt_retain2.UseVisualStyleBackColor = true;
			// 
			// textBox_mqtt_password2
			// 
			this.textBox_mqtt_password2.Location = new System.Drawing.Point(330, 81);
			this.textBox_mqtt_password2.Name = "textBox_mqtt_password2";
			this.textBox_mqtt_password2.Size = new System.Drawing.Size(132, 23);
			this.textBox_mqtt_password2.TabIndex = 161;
			// 
			// label21
			// 
			this.label21.AutoSize = true;
			this.label21.ForeColor = System.Drawing.Color.Gray;
			this.label21.Location = new System.Drawing.Point(96, 133);
			this.label21.Name = "label21";
			this.label21.Size = new System.Drawing.Size(243, 17);
			this.label21.TabIndex = 165;
			this.label21.Text = "默认为空，为空表示使用网关的EdgeID信息";
			// 
			// label22
			// 
			this.label22.AutoSize = true;
			this.label22.Location = new System.Drawing.Point(20, 112);
			this.label22.Name = "label22";
			this.label22.Size = new System.Drawing.Size(72, 17);
			this.label22.TabIndex = 162;
			this.label22.Text = "Cliend Id：";
			// 
			// textBox_mqtt_clientID2
			// 
			this.textBox_mqtt_clientID2.Location = new System.Drawing.Point(94, 109);
			this.textBox_mqtt_clientID2.Name = "textBox_mqtt_clientID2";
			this.textBox_mqtt_clientID2.Size = new System.Drawing.Size(280, 23);
			this.textBox_mqtt_clientID2.TabIndex = 163;
			// 
			// label23
			// 
			this.label23.AutoSize = true;
			this.label23.ForeColor = System.Drawing.Color.DimGray;
			this.label23.Location = new System.Drawing.Point(105, 7);
			this.label23.Name = "label23";
			this.label23.Size = new System.Drawing.Size(200, 17);
			this.label23.TabIndex = 164;
			this.label23.Text = " (以客户端的形式发布到以下服务器)";
			// 
			// UploadInfo
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(215)))), ((int)(((byte)(218)))), ((int)(((byte)(233)))));
			this.Controls.Add(this.tabControl1);
			this.Controls.Add(this.checkBox_upload_alarm);
			this.Controls.Add(this.label14);
			this.Controls.Add(this.panel1);
			this.Controls.Add(this.checkBox_subscription);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.textBox_redis_db);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.textBox_uploadInteval);
			this.Controls.Add(this.label12);
			this.Controls.Add(this.textBox_redis_password);
			this.Controls.Add(this.label6);
			this.Controls.Add(this.textBox_redis_port);
			this.Controls.Add(this.label5);
			this.Controls.Add(this.textBox_redis_ipaddress);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.checkBox_useRedies);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "UploadInfo";
			this.Size = new System.Drawing.Size(480, 418);
			this.Paint += new System.Windows.Forms.PaintEventHandler(this.UploadInfo_Paint);
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.panel2.ResumeLayout(false);
			this.panel2.PerformLayout();
			this.tabControl1.ResumeLayout(false);
			this.tabPage1.ResumeLayout(false);
			this.tabPage1.PerformLayout();
			this.tabPage2.ResumeLayout(false);
			this.tabPage2.PerformLayout();
			this.panel3.ResumeLayout(false);
			this.panel3.PerformLayout();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.TextBox textBox_uploadInteval;
		private System.Windows.Forms.Label label12;
		private System.Windows.Forms.TextBox textBox_mqtt_password;
		private System.Windows.Forms.Label label_mqtt_password;
		private System.Windows.Forms.TextBox textBox_mqtt_username;
		private System.Windows.Forms.Label label_mqtt_userName;
		private System.Windows.Forms.TextBox textBox_mqtt_port;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.TextBox textBox_mqtt_ipaddress;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.CheckBox checkBox_useMqtt;
		private System.Windows.Forms.TextBox textBox_redis_password;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.TextBox textBox_redis_port;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.TextBox textBox_redis_ipaddress;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.CheckBox checkBox_useRedies;
		private System.Windows.Forms.TextBox textBox_redis_db;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox textBox_mqtt_clientID;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.CheckBox checkBox_subscription;
		private System.Windows.Forms.Label label11;
		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.Label label13;
		private System.Windows.Forms.RadioButton radioButton3;
		private System.Windows.Forms.RadioButton radioButton2;
		private System.Windows.Forms.RadioButton radioButton1;
		private System.Windows.Forms.Label label14;
		private System.Windows.Forms.Label label15;
		private System.Windows.Forms.CheckBox checkBox_mqtt_retain;
		private System.Windows.Forms.CheckBox checkBox_upload_alarm;
		private System.Windows.Forms.CheckBox checkBox_dataChange;
		private System.Windows.Forms.Label label16;
		private System.Windows.Forms.TextBox textBox_caFile;
		private System.Windows.Forms.Button button_select;
		private System.Windows.Forms.Button button_clear;
		private System.Windows.Forms.CheckBox checkBox_mqtt_ssl;
		private System.Windows.Forms.Panel panel2;
		private System.Windows.Forms.RadioButton radioButton_mqtt_regular;
		private System.Windows.Forms.RadioButton radioButton_mqtt_jetlinks;
		private System.Windows.Forms.TextBox textBox_dataupload_forceTimer;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.TabControl tabControl1;
		private System.Windows.Forms.TabPage tabPage1;
		private System.Windows.Forms.TabPage tabPage2;
		private System.Windows.Forms.CheckBox checkBox_useMqtt2;
		private System.Windows.Forms.Panel panel3;
		private System.Windows.Forms.RadioButton radioButton_mqtt_jetlinks2;
		private System.Windows.Forms.RadioButton radioButton_mqtt_regular2;
		private System.Windows.Forms.Label label10;
		private System.Windows.Forms.CheckBox checkBox_mqtt_ssl2;
		private System.Windows.Forms.TextBox textBox_mqtt_ipaddress2;
		private System.Windows.Forms.Button button_clear2;
		private System.Windows.Forms.Label label17;
		private System.Windows.Forms.Button button_select2;
		private System.Windows.Forms.TextBox textBox_mqtt_port2;
		private System.Windows.Forms.TextBox textBox_caFile2;
		private System.Windows.Forms.Label label18;
		private System.Windows.Forms.Label label19;
		private System.Windows.Forms.TextBox textBox_mqtt_username2;
		private System.Windows.Forms.Label label20;
		private System.Windows.Forms.CheckBox checkBox_mqtt_retain2;
		private System.Windows.Forms.TextBox textBox_mqtt_password2;
		private System.Windows.Forms.Label label21;
		private System.Windows.Forms.Label label22;
		private System.Windows.Forms.TextBox textBox_mqtt_clientID2;
		private System.Windows.Forms.Label label23;
	}
}
