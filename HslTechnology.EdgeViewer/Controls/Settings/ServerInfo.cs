using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json.Linq;
using HslTechnology.Edge.Config;
using HslCommunication;
using HslTechnology.Edge.Reflection;

namespace HslTechnology.EdgeViewer.Controls.Settings
{
	public partial class ServerInfo : IConfigControl
	{
		public ServerInfo( )
		{
			InitializeComponent( );

			dataGridView1.RowsAdded += DataGridView1_RowsAdded;

		}

		private void DataGridView1_RowsAdded( object sender, DataGridViewRowsAddedEventArgs e )
		{
			dataGridView1.Rows[e.RowIndex].Cells[2].Value = true;
		}

		/// <inheritdoc/>
		public override void RenderParameter( JToken paraJson )
		{
			this.token = paraJson;
			textBox3.Text              = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.DeviceName ), "");
			textBox1.Text              = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.ServerPort ), 521).ToString( );
			textBox_admin.Text         = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.UserName ),   "" );
			string password            = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.Password ), "" );
			if (password.Length > 100) password = JsonPasswordConverter.TransValueFromJson( password );
			textBox_adminPassword.Text = password;
			RenderAccounts( ConfigHelper.GetConfigArray<UserAccount>( paraJson, nameof( ServerInfoConfig.Accounts ) ) );
			textBox4.Text              = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.RemoteServerIp ), "www.hslcommunication.cn" );
			textBox5.Text              = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.UniqueId ),   "");
			checkBox_wildcard.Checked  = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.MqttWildcard ), true );
			checkBox_resetNull.Checked = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.ResetNull ), false );
			textBox_location.Text      = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.Location ), "" );
			textBox_gps.Text           = ConfigHelper.GetConfig( paraJson, nameof( ServerInfoConfig.Coordinate ), "" );
		}

		/// <inheritdoc/>
		public override void SetParameterToJToken( )
		{
			if (token == null) return;
			token[nameof( ServerInfoConfig.DeviceName )]     = textBox3.Text;
			token[nameof( ServerInfoConfig.ServerPort )]     = int.Parse( textBox1.Text );
			token[nameof( ServerInfoConfig.UserName )]       = textBox_admin.Text;
			token[nameof( ServerInfoConfig.Password )]       = textBox_adminPassword.Text;
			token[nameof( ServerInfoConfig.RemoteServerIp )] = textBox4.Text;
			token[nameof( ServerInfoConfig.UniqueId )]       = textBox5.Text;
			token[nameof( ServerInfoConfig.Accounts )]       = JArray.FromObject( CreateUserAccounts( ) );
			token[nameof( ServerInfoConfig.MqttWildcard )]   = checkBox_wildcard.Checked;
			token[nameof( ServerInfoConfig.ResetNull )]      = checkBox_resetNull.Checked;
			token[nameof( ServerInfoConfig.Location )]       = textBox_location.Text;
			token[nameof( ServerInfoConfig.Coordinate )]     = textBox_gps.Text;
		}

		private JToken token;

		private bool GetCheckBoxValue( DataGridViewRow row, int colIndex )
		{
			if (row.Cells[colIndex].Value == null) return false;
			return (bool)row.Cells[colIndex].Value;
		}

		private List<UserAccount> CreateUserAccounts( )
		{
			List<UserAccount> accounts = new List<UserAccount>( );
			for (int i = 0; i < dataGridView1.Rows.Count; i++)
			{
				DataGridViewRow row = dataGridView1.Rows[i];
				if (row.IsNewRow) continue;

				UserAccount account = new UserAccount( );
				account.UserName = row.Cells[0].Value.ToString( );
				account.Password = row.Cells[1].Value.ToString( );
				account.LoginEnable = GetCheckBoxValue( row, 2 );
				account.ModifyEdgeSetting = GetCheckBoxValue( row, 3 );
				account.CloseRestartEdge = GetCheckBoxValue( row, 4 );
				account.ModifyDeviceSetting = GetCheckBoxValue( row, 5 );
				account.WriteDeviceData = GetCheckBoxValue( row, 6 );
				account.CallDeviceMethod = GetCheckBoxValue( row, 7 );

				accounts.Add( account );
			}

			return accounts;
		}

		private void RenderAccounts( List<UserAccount> accounts )
		{
			if (accounts == null) return;
			if (accounts.Count == 0) return;

			HslTechnology.Edge.Controls.HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, accounts.Count );
			for (int i = 0; i < accounts.Count; i++)
			{
				UserAccount account = accounts[i];
				DataGridViewRow row = dataGridView1.Rows[i];
				row.Cells[0].Value = account.UserName;
				row.Cells[1].Value = account.Password;
				row.Cells[2].Value = account.LoginEnable;
				row.Cells[3].Value = account.ModifyEdgeSetting;
				row.Cells[4].Value = account.CloseRestartEdge;
				row.Cells[5].Value = account.ModifyDeviceSetting;
				row.Cells[6].Value = account.WriteDeviceData;
				row.Cells[7].Value = account.CallDeviceMethod;
			}
		}

		/// <inheritdoc/>
		public override OperateResult CheckInputLegal( )
		{
			if (string.IsNullOrEmpty( textBox3.Text )) return new OperateResult( "设备的名称不能输入空" );
			if (textBox3.Text.Contains( " " )) return new OperateResult( "设备的名称不能包含空格" );
			if (!int.TryParse( textBox1.Text, out int port )) return new OperateResult( "设备的端口号必须为整型" );
			if (port < 100) return new OperateResult( "设备的端口号大于100" );
			return OperateResult.CreateSuccessResult( );
		}

		private void dataGridView1_CellContentClick( object sender, DataGridViewCellEventArgs e )
		{

		}
	}
}
