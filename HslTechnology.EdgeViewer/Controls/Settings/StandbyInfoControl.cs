using HslCommunication;
using HslTechnology.Edge.Config;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls.Settings
{
	public partial class StandbyInfoControl : IConfigControl
	{
		public StandbyInfoControl( )
		{
			InitializeComponent( );
		}

		
		/// <inheritdoc/>
		public override void RenderParameter( JToken paraJson )
		{
			this.token = paraJson;
			textBox_server_ip.Text       = ConfigHelper.GetConfig( paraJson, nameof( StandbyInfoConfig.MainServerIp ),         "127.0.0.1" );
			textBox_server_port.Text     = ConfigHelper.GetConfig( paraJson, nameof( StandbyInfoConfig.MainServerPort ),       521 ).ToString( );
			textBox_server_account.Text  = ConfigHelper.GetConfig( paraJson, nameof( StandbyInfoConfig.MainServerAccount ),    "admin" );
			textBox_server_password.Text = ConfigHelper.GetConfig( paraJson, nameof( StandbyInfoConfig.MainServerPassword ),   "" );
			checkBox_sync_edge.Checked   = ConfigHelper.GetConfig( paraJson, nameof( StandbyInfoConfig.SyncEdgeParameters ),   true );
			checkBox_sync_device.Checked = ConfigHelper.GetConfig( paraJson, nameof( StandbyInfoConfig.SyncDeviceParameters ), true );
			checkBox1.Checked            = ConfigHelper.GetConfig( paraJson, nameof( StandbyInfoConfig.StandbyEnable ),        false );
			textBox_server_time.Text     = ConfigHelper.GetConfig( paraJson, nameof( StandbyInfoConfig.CheckTimeoutInSeconds ), 60 ).ToString( );
		}

		/// <inheritdoc/>
		public override void SetParameterToJToken( )
		{
			if (token == null) return;
			token[nameof( StandbyInfoConfig.MainServerIp )]          = textBox_server_ip.Text;
			token[nameof( StandbyInfoConfig.MainServerPort )]        = int.Parse( textBox_server_port.Text );
			token[nameof( StandbyInfoConfig.MainServerAccount )]     = textBox_server_account.Text;
			token[nameof( StandbyInfoConfig.MainServerPassword )]    = textBox_server_password.Text;
			token[nameof( StandbyInfoConfig.SyncEdgeParameters )]    = checkBox_sync_edge.Checked;
			token[nameof( StandbyInfoConfig.SyncDeviceParameters )]  = checkBox_sync_device.Checked;
			token[nameof( StandbyInfoConfig.StandbyEnable )]         = checkBox1.Checked;
			token[nameof( StandbyInfoConfig.CheckTimeoutInSeconds )] = int.Parse( textBox_server_time.Text );
		}

		private JToken token;

		/// <inheritdoc/>
		public override OperateResult CheckInputLegal( )
		{
			if (string.IsNullOrEmpty( textBox_server_ip.Text )) return new OperateResult( "主服务器的IP地址不能输入空" );

			if (!int.TryParse( textBox_server_port.Text, out int port )) return new OperateResult( "设备的端口号必须为整型" );
			if (port < 100) return new OperateResult( "设备的端口号大于100" );

			if (!int.TryParse( textBox_server_time.Text, out int time )) return new OperateResult( "检测超时的时间必须为整型" );
			if (time < 2) return new OperateResult( "检测超时时间比如大于1" );

			return OperateResult.CreateSuccessResult( );
		}
	}
}
