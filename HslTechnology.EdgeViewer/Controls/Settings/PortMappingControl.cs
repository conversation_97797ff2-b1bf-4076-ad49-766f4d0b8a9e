using HslCommunication;
using HslTechnology.Edge.Config;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls.Settings
{
	public partial class PortMappingControl : IConfigControl
	{
		public PortMappingControl( )
		{
			InitializeComponent( );
		}

		private void PortMappingControl_Load( object sender, EventArgs e )
		{

		}

		public void RenderPorts( string[] ports )
		{
			comboBox1.DataSource = ports;
		}

		/// <inheritdoc/>
		public override void RenderParameter( JToken paraJson )
		{
			this.token = paraJson;

			SerialPortMapping[] portMappings = paraJson[nameof( PortMappingConfig.PortMappings )].ToObject<SerialPortMapping[]>( );
			HslTechnology.Edge.Controls.HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, portMappings.Length );
			for (int i = 0; i < portMappings.Length; i++)
			{
				dataGridView1.Rows[i].Cells[0].Value = portMappings[i].SerialPort;
				dataGridView1.Rows[i].Cells[1].Value = portMappings[i].SerialMapping;
			}
		}

		/// <inheritdoc/>
		public override void SetParameterToJToken( )
		{
			if (token == null) return;

			List<SerialPortMapping> portMappings = new List<SerialPortMapping>( );
			for (int i = 0; i < dataGridView1.Rows.Count; i++)
			{
				object value_source = dataGridView1.Rows[i].Cells[0].Value;
				object value_target = dataGridView1.Rows[i].Cells[1].Value;
				if (value_source != null)
				{
					if (!string.IsNullOrEmpty( value_source.ToString( ) ) )
					{
						portMappings.Add( new SerialPortMapping( )
						{
							SerialPort    = value_source.ToString( ),
							SerialMapping = value_target == null ? string.Empty : value_target.ToString( )
						} );
					}
				}
			}

			this.token[nameof( PortMappingConfig.PortMappings )] = JArray.FromObject( portMappings );
		}

		/// <inheritdoc/>
		public override OperateResult CheckInputLegal( )
		{
			return OperateResult.CreateSuccessResult( );
		}
		private JToken token;

	}
}
