using HslCommunication;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.EdgeViewer.Controls.Settings
{
	public class IConfigControl : System.Windows.Forms.UserControl
	{
		/// <summary>
		/// 设置参数进行显示
		/// </summary>
		/// <param name="paraJson">参数信息</param>
		public virtual void RenderParameter( JToken paraJson )
		{

		}

		/// <summary>
		/// 将所有的参数设置到 JToken中去
		/// </summary>
		public virtual void SetParameterToJToken( )
		{

		}

		/// <summary>
		/// 检查当前的输入是否合法
		/// </summary>
		/// <returns>结果信息</returns>
		public virtual OperateResult CheckInputLegal( )
		{
			return new OperateResult( );
		}

	}
}
