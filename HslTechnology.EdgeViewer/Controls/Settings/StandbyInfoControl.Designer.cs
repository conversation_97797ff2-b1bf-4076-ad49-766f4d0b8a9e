
namespace HslTechnology.EdgeViewer.Controls.Settings
{
	partial class StandbyInfoControl
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.checkBox1 = new System.Windows.Forms.CheckBox();
			this.label1 = new System.Windows.Forms.Label();
			this.textBox_server_ip = new System.Windows.Forms.TextBox();
			this.textBox_server_port = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.textBox_server_account = new System.Windows.Forms.TextBox();
			this.label3 = new System.Windows.Forms.Label();
			this.textBox_server_password = new System.Windows.Forms.TextBox();
			this.label4 = new System.Windows.Forms.Label();
			this.checkBox_sync_edge = new System.Windows.Forms.CheckBox();
			this.checkBox_sync_device = new System.Windows.Forms.CheckBox();
			this.textBox_server_time = new System.Windows.Forms.TextBox();
			this.label5 = new System.Windows.Forms.Label();
			this.label6 = new System.Windows.Forms.Label();
			this.label7 = new System.Windows.Forms.Label();
			this.SuspendLayout();
			// 
			// checkBox1
			// 
			this.checkBox1.AutoSize = true;
			this.checkBox1.Location = new System.Drawing.Point(3, 3);
			this.checkBox1.Name = "checkBox1";
			this.checkBox1.Size = new System.Drawing.Size(219, 21);
			this.checkBox1.TabIndex = 0;
			this.checkBox1.Text = "将本边缘网关系统设置为备份服务器";
			this.checkBox1.UseVisualStyleBackColor = true;
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(19, 28);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(79, 17);
			this.label1.TabIndex = 1;
			this.label1.Text = "主服务器IP：";
			// 
			// textBox_server_ip
			// 
			this.textBox_server_ip.Location = new System.Drawing.Point(117, 25);
			this.textBox_server_ip.Name = "textBox_server_ip";
			this.textBox_server_ip.Size = new System.Drawing.Size(138, 23);
			this.textBox_server_ip.TabIndex = 2;
			// 
			// textBox_server_port
			// 
			this.textBox_server_port.Location = new System.Drawing.Point(359, 25);
			this.textBox_server_port.Name = "textBox_server_port";
			this.textBox_server_port.Size = new System.Drawing.Size(115, 23);
			this.textBox_server_port.TabIndex = 4;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(261, 28);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(92, 17);
			this.label2.TabIndex = 3;
			this.label2.Text = "主服务器端口：";
			// 
			// textBox_server_account
			// 
			this.textBox_server_account.Location = new System.Drawing.Point(117, 56);
			this.textBox_server_account.Name = "textBox_server_account";
			this.textBox_server_account.Size = new System.Drawing.Size(138, 23);
			this.textBox_server_account.TabIndex = 6;
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(19, 59);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(92, 17);
			this.label3.TabIndex = 5;
			this.label3.Text = "主服务器账号：";
			// 
			// textBox_server_password
			// 
			this.textBox_server_password.Location = new System.Drawing.Point(359, 56);
			this.textBox_server_password.Name = "textBox_server_password";
			this.textBox_server_password.Size = new System.Drawing.Size(115, 23);
			this.textBox_server_password.TabIndex = 8;
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(261, 59);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(92, 17);
			this.label4.TabIndex = 7;
			this.label4.Text = "主服务器密码：";
			// 
			// checkBox_sync_edge
			// 
			this.checkBox_sync_edge.AutoSize = true;
			this.checkBox_sync_edge.Location = new System.Drawing.Point(22, 86);
			this.checkBox_sync_edge.Name = "checkBox_sync_edge";
			this.checkBox_sync_edge.Size = new System.Drawing.Size(159, 21);
			this.checkBox_sync_edge.TabIndex = 9;
			this.checkBox_sync_edge.Text = "同步主服务器的网关参数";
			this.checkBox_sync_edge.UseVisualStyleBackColor = true;
			// 
			// checkBox_sync_device
			// 
			this.checkBox_sync_device.AutoSize = true;
			this.checkBox_sync_device.Location = new System.Drawing.Point(22, 113);
			this.checkBox_sync_device.Name = "checkBox_sync_device";
			this.checkBox_sync_device.Size = new System.Drawing.Size(207, 21);
			this.checkBox_sync_device.TabIndex = 10;
			this.checkBox_sync_device.Text = "同步主服务器的设备采集配置文件";
			this.checkBox_sync_device.UseVisualStyleBackColor = true;
			// 
			// textBox_server_time
			// 
			this.textBox_server_time.Location = new System.Drawing.Point(117, 139);
			this.textBox_server_time.Name = "textBox_server_time";
			this.textBox_server_time.Size = new System.Drawing.Size(138, 23);
			this.textBox_server_time.TabIndex = 12;
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(19, 142);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(92, 17);
			this.label5.TabIndex = 11;
			this.label5.Text = "离线最大时间：";
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.Location = new System.Drawing.Point(261, 142);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(20, 17);
			this.label6.TabIndex = 13;
			this.label6.Text = "秒";
			// 
			// label7
			// 
			this.label7.ForeColor = System.Drawing.Color.Gray;
			this.label7.Location = new System.Drawing.Point(114, 165);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(360, 37);
			this.label7.TabIndex = 14;
			this.label7.Text = "备用服务器判断主服务器掉线超过该时间时，将自身切换主服务器模式运行";
			// 
			// StandbyInfoControl
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(215)))), ((int)(((byte)(218)))), ((int)(((byte)(233)))));
			this.Controls.Add(this.label7);
			this.Controls.Add(this.label6);
			this.Controls.Add(this.textBox_server_time);
			this.Controls.Add(this.label5);
			this.Controls.Add(this.checkBox_sync_device);
			this.Controls.Add(this.checkBox_sync_edge);
			this.Controls.Add(this.textBox_server_password);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.textBox_server_account);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.textBox_server_port);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.textBox_server_ip);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.checkBox1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "StandbyInfoControl";
			this.Size = new System.Drawing.Size(480, 244);
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.CheckBox checkBox1;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TextBox textBox_server_ip;
		private System.Windows.Forms.TextBox textBox_server_port;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox textBox_server_account;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.TextBox textBox_server_password;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.CheckBox checkBox_sync_edge;
		private System.Windows.Forms.CheckBox checkBox_sync_device;
		private System.Windows.Forms.TextBox textBox_server_time;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label7;
	}
}
