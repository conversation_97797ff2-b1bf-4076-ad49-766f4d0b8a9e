using HslCommunication;
using HslTechnology.Edge.Config;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls.Settings
{
	public partial class DtuInfoControl : IConfigControl
	{
		public DtuInfoControl( )
		{
			InitializeComponent( );
		}


		public override void RenderParameter( JToken paraJson )
		{
			this.token = paraJson;

			checkBox1.Checked     = ConfigHelper.GetConfig( paraJson, nameof( DtuConfig.UseDtuServer ), false );
			textBox_port.Text     = ConfigHelper.GetConfig( paraJson, nameof( DtuConfig.Port ), 10000 ).ToString( );
			textBox_password.Text = ConfigHelper.GetConfig( paraJson, nameof( DtuConfig.Password ), "123456" );
			checkBox2.Checked     = ConfigHelper.GetConfig( paraJson, nameof( DtuConfig.ResponseAck ), false );
		}

		public override void SetParameterToJToken( )
		{
			if (token == null) return;
			token[nameof( DtuConfig.UseDtuServer )] = checkBox1.Checked;
			token[nameof( DtuConfig.Port )]         = int.Parse( textBox_port.Text );
			token[nameof( DtuConfig.Password )]     = textBox_password.Text;
			token[nameof( DtuConfig.ResponseAck )]  = checkBox2.Checked;
		}

		public override OperateResult CheckInputLegal( )
		{
			if (!int.TryParse( textBox_port.Text, out int port )) return new OperateResult( "DTU的端口号必须为整型" );
			if (port < 1000) return new OperateResult( "DTU的端口号必须大于1000" );

			if (checkBox1.Checked && !Regex.IsMatch( textBox_password.Text, "^[0-9A-Za-z]{6}$" ))
			{
				return new OperateResult( "密码必须是数字即英文字母组合，并且只有6位，请重新输入！" );
			}
			return OperateResult.CreateSuccessResult( );
		}

		private JToken token;

		private void DtuInfoControl_Paint( object sender, PaintEventArgs e )
		{
			e.Graphics.DrawLine( Pens.LightGray, 4, 114, Width - 30, 114 );
		}
	}
}
