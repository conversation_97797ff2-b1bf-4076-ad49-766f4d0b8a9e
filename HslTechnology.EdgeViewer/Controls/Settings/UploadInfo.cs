using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication;
using HslTechnology.Edge.Config;
using Newtonsoft.Json.Linq;

namespace HslTechnology.EdgeViewer.Controls.Settings
{
	public partial class UploadInfo : IConfigControl
	{
		public UploadInfo( )
		{
			InitializeComponent( );

			radioButton_mqtt_jetlinks.CheckedChanged += RadioButton_mqtt_CheckedChanged;
			radioButton_mqtt_regular.CheckedChanged += RadioButton_mqtt_CheckedChanged;
			checkBox_dataChange.CheckedChanged += CheckBox_dataChange_CheckedChanged;
		}

		private void CheckBox_dataChange_CheckedChanged( object sender, EventArgs e )
		{
			if (checkBox_dataChange.Checked)
			{
				label7.Visible = true;
				textBox_dataupload_forceTimer.Visible = true;
			}
			else
			{
				label7.Visible = false;
				textBox_dataupload_forceTimer.Visible = false;
			}
		}

		private void RadioButton_mqtt_CheckedChanged( object sender, EventArgs e )
		{
			if (sender is RadioButton radioButton)
			{
				if (object.ReferenceEquals(radioButton, radioButton_mqtt_regular ))
				{
					label_mqtt_userName.Text = "用户名:";
					label_mqtt_password.Text = "密码:";
				}
				else if(object.ReferenceEquals( radioButton, radioButton_mqtt_jetlinks ))
				{
					label_mqtt_userName.Text = "secureId:";
					label_mqtt_password.Text = "secureKey:";
				}
			}
		}

		/// <inheritdoc/>
		public override void RenderParameter( JToken paraJson )
		{
			token = paraJson;

			checkBox_useRedies.Checked    = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UseRedisServer ), false );
			textBox_redis_ipaddress.Text  = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.RedisIpAddress ), "" );
			textBox_redis_port.Text       = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.RedisPort ), 6379 ).ToString( );
			textBox_redis_password.Text   = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.RedisPassword ), "" );
			textBox_redis_db.Text         = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.RedisDBNumber ), 0 ).ToString( );

			checkBox_useMqtt.Checked      = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UseMqttServer ), false );
			textBox_mqtt_ipaddress.Text   = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttIpAddress ), "" );
			textBox_mqtt_port.Text        = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttPort ), 1883 ).ToString( );
			textBox_mqtt_username.Text    = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttUserName ), "" );
			textBox_mqtt_password.Text    = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttPassword ), "" );
			textBox_mqtt_clientID.Text    = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttClientID ), "" );
			checkBox_mqtt_retain.Checked  = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttRetain ), true );
			checkBox_upload_alarm.Checked = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UploadAlarm ), true );
			checkBox_mqtt_ssl.Checked     = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UseSSL ),      false );
			int mqttType                  = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttType ),    UploadInfoConfig.MqttTypeNormal );
			if (mqttType == UploadInfoConfig.MqttTypeNormal)
				radioButton_mqtt_regular.Checked = true;
			else if (mqttType == UploadInfoConfig.MqttTypeJetLinks)
				radioButton_mqtt_jetlinks.Checked = true;
			textBox_caFile.Text = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttCAFileName ), string.Empty );
			ca_file_content = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttCAFileContent ), string.Empty );

			// MQTT2 的上传
			checkBox_useMqtt2.Checked = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UseMqttServer2 ), false );
			textBox_mqtt_ipaddress2.Text = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttIpAddress2 ), "" );
			textBox_mqtt_port2.Text = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttPort2 ), 1883 ).ToString( );
			textBox_mqtt_username2.Text = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttUserName2 ), "" );
			textBox_mqtt_password2.Text = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttPassword2 ), "" );
			textBox_mqtt_clientID2.Text = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttClientID2 ), "" );
			checkBox_mqtt_retain2.Checked = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttRetain2 ), true );
			checkBox_mqtt_ssl2.Checked = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UseSSL2 ), false );
			int mqttType2 = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttType2 ), UploadInfoConfig.MqttTypeNormal );
			if (mqttType2 == UploadInfoConfig.MqttTypeNormal)
				radioButton_mqtt_regular2.Checked = true;
			else if (mqttType2 == UploadInfoConfig.MqttTypeJetLinks)
				radioButton_mqtt_jetlinks2.Checked = true;
			textBox_caFile2.Text = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttCAFileName2 ), string.Empty );
			ca_file_content2 = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.MqttCAFileContent2 ), string.Empty );


			int uploadRemoteMode          = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UploadRemoteMode ), 0 );
			if (uploadRemoteMode == 0)
				radioButton1.Checked = true;
			else if (uploadRemoteMode == 1)
				radioButton2.Checked = true;
			else
				radioButton3.Checked = true;
			checkBox_dataChange.Checked        = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UploadWhenDataChange ), false );
			if (checkBox_dataChange.Checked)
			{
				textBox_dataupload_forceTimer.Text = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UploadWhenTimedTriggers ), -1 ).ToString( );
			}
			else
			{
				textBox_dataupload_forceTimer.Text = "-1";
			}
			textBox_uploadInteval.Text         = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.UploadTimeInterval ), 1000 ).ToString( );
			checkBox_subscription.Checked      = ConfigHelper.GetConfig( paraJson, nameof( UploadInfoConfig.EnableSubscription ), false );

		}

		/// <inheritdoc/>
		public override void SetParameterToJToken( )
		{
			if (token == null) token = new JObject( );
			token[nameof( UploadInfoConfig.UseRedisServer )] = checkBox_useRedies.Checked;
			token[nameof( UploadInfoConfig.RedisIpAddress )] = textBox_redis_ipaddress.Text;
			token[nameof( UploadInfoConfig.RedisPort )]      = int.Parse( textBox_redis_port.Text );
			token[nameof( UploadInfoConfig.RedisPassword )]  = textBox_redis_password.Text;
			token[nameof( UploadInfoConfig.RedisDBNumber )]  = int.Parse( textBox_redis_db.Text );

			token[nameof( UploadInfoConfig.UseMqttServer )]  = checkBox_useMqtt.Checked;
			token[nameof( UploadInfoConfig.MqttIpAddress )]  = textBox_mqtt_ipaddress.Text;
			token[nameof( UploadInfoConfig.MqttPort )]       = int.Parse( textBox_mqtt_port.Text );
			token[nameof( UploadInfoConfig.MqttUserName )]   = textBox_mqtt_username.Text;
			token[nameof( UploadInfoConfig.MqttPassword )]   = textBox_mqtt_password.Text;
			token[nameof( UploadInfoConfig.MqttClientID )]   = textBox_mqtt_clientID.Text;
			token[nameof( UploadInfoConfig.MqttRetain )]     = checkBox_mqtt_retain.Checked;
			token[nameof( UploadInfoConfig.UploadAlarm )]    = checkBox_upload_alarm.Checked;
			token[nameof( UploadInfoConfig.MqttCAFileName )] = textBox_caFile.Text;
			token[nameof( UploadInfoConfig.MqttCAFileContent )] = this.ca_file_content;
			token[nameof( UploadInfoConfig.MqttType )] = radioButton_mqtt_regular.Checked ? UploadInfoConfig.MqttTypeNormal : 
				radioButton_mqtt_jetlinks.Checked ? UploadInfoConfig.MqttTypeJetLinks : UploadInfoConfig.MqttTypeNormal;

			// MQTT2
			token[nameof( UploadInfoConfig.UseMqttServer2 )] = checkBox_useMqtt2.Checked;
			token[nameof( UploadInfoConfig.MqttIpAddress2 )] = textBox_mqtt_ipaddress2.Text;
			token[nameof( UploadInfoConfig.MqttPort2 )] = int.Parse( textBox_mqtt_port2.Text );
			token[nameof( UploadInfoConfig.MqttUserName2 )] = textBox_mqtt_username2.Text;
			token[nameof( UploadInfoConfig.MqttPassword2 )] = textBox_mqtt_password2.Text;
			token[nameof( UploadInfoConfig.MqttClientID2 )] = textBox_mqtt_clientID2.Text;
			token[nameof( UploadInfoConfig.MqttRetain2 )] = checkBox_mqtt_retain2.Checked;
			token[nameof( UploadInfoConfig.MqttCAFileName2 )] = textBox_caFile2.Text;
			token[nameof( UploadInfoConfig.MqttCAFileContent2 )] = this.ca_file_content2;
			token[nameof( UploadInfoConfig.MqttType2 )] = radioButton_mqtt_regular2.Checked ? UploadInfoConfig.MqttTypeNormal :
				radioButton_mqtt_jetlinks2.Checked ? UploadInfoConfig.MqttTypeJetLinks : UploadInfoConfig.MqttTypeNormal;


			token[nameof( UploadInfoConfig.UploadRemoteMode )]     = radioButton1.Checked ? 0 : radioButton2.Checked ? 1 : 2;
			token[nameof( UploadInfoConfig.UploadWhenDataChange )] = checkBox_dataChange.Checked;
			if (checkBox_dataChange.Checked)
				token[nameof( UploadInfoConfig.UploadWhenTimedTriggers )] = int.Parse( textBox_dataupload_forceTimer.Text );

			token[nameof( UploadInfoConfig.UploadTimeInterval )]   = int.Parse( textBox_uploadInteval.Text );
			token[nameof( UploadInfoConfig.EnableSubscription )]   = checkBox_subscription.Checked;
			token[nameof( UploadInfoConfig.UseSSL )]               = checkBox_mqtt_ssl.Checked;
		}

		private JToken token;

		/// <inheritdoc/>
		public override OperateResult CheckInputLegal( )
		{
			if (!int.TryParse( textBox_redis_port.Text, out int redisPort )) return new OperateResult( "Redis端口必须为整数" );
			if (redisPort < 10) return new OperateResult( "Redis端口必须大于10" );
			if (!int.TryParse( textBox_mqtt_port.Text, out int mqttPort )) return new OperateResult( "MQTT端口必须为整数" );
			if (mqttPort < 10) return new OperateResult( "MQTT端口必须大于10" );
			if (!int.TryParse( textBox_uploadInteval.Text, out int timeInterval )) return new OperateResult( "上传间隔必须为整数" );
			if (timeInterval < 0) return new OperateResult( "上传间隔必须大于0" );

			if (checkBox_useMqtt.Checked)
			{
				if (string.IsNullOrEmpty( textBox_mqtt_ipaddress.Text )) return new OperateResult( "MQTT 的IP地址不能为空!" );
				if (Regex.IsMatch( textBox_mqtt_ipaddress.Text, @"^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$" ))
				{
					if (!IPAddress.TryParse( textBox_mqtt_ipaddress.Text, out IPAddress address ))
					{
						return new OperateResult( "MQTT 的IP地址输入错误: " + StringResources.Language.IpAddressError );
					}
				}
			}
			if (checkBox_useMqtt2.Checked)
			{
				if (string.IsNullOrEmpty( textBox_mqtt_ipaddress2.Text )) return new OperateResult( "MQTT2 的IP地址不能为空!" );
				if (Regex.IsMatch( textBox_mqtt_ipaddress2.Text, @"^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$" ))
				{
					if (!IPAddress.TryParse( textBox_mqtt_ipaddress2.Text, out IPAddress address ))
					{
						return new OperateResult( "MQTT2 的IP地址输入错误: " + StringResources.Language.IpAddressError );
					}
				}
			}
			if (checkBox_useRedies.Checked)
			{
				if (string.IsNullOrEmpty( textBox_redis_ipaddress.Text )) return new OperateResult( "Redis 的IP地址不能为空!" );
				if (Regex.IsMatch( textBox_redis_ipaddress.Text, @"^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$" ))
				{
					if (!IPAddress.TryParse( textBox_redis_ipaddress.Text, out IPAddress address ))
					{
						return new OperateResult( "Redis 的IP地址输入错误: " + StringResources.Language.IpAddressError );
					}
				}
			}
			if (checkBox_dataChange.Checked)
			{
				if (!int.TryParse( textBox_dataupload_forceTimer.Text, out int dataupload_forceTimer )) return new OperateResult( "同时强制上传周期必须为整数" );
			}
			return OperateResult.CreateSuccessResult( );
		}

		private void UploadInfo_Paint( object sender, PaintEventArgs e )
		{
			e.Graphics.DrawLine( Pens.LightGray, 4, 84,  Width - 30, 84 );
			e.Graphics.DrawLine( Pens.LightGray, 4, 352, Width - 30, 352 );
			e.Graphics.DrawLine( Pens.LightGray, 4, 386, Width - 30, 386 );
		}

		private void button_select_Click( object sender, EventArgs e )
		{
			using (OpenFileDialog ofd = new OpenFileDialog( ))
			{
				ofd.Multiselect = false;
				if (ofd.ShowDialog( ) == DialogResult.OK)
				{
					try
					{
						string fileName = ofd.FileName;
						ca_file_content = Convert.ToBase64String( File.ReadAllBytes( fileName ) );

						textBox_caFile.Text = ofd.SafeFileName;
					}
					catch( Exception ex)
					{
						HslCommunication.BasicFramework.SoftBasic.ShowExceptionMessage( ex );
					}
				}
			}
		}

		private string ca_file_content = string.Empty;
		private string ca_file_content2 = string.Empty;

		private void button_clear_Click( object sender, EventArgs e )
		{
			textBox_caFile.Text = string.Empty;
			ca_file_content = string.Empty;
		}

		private void button_select2_Click( object sender, EventArgs e )
		{
			using (OpenFileDialog ofd = new OpenFileDialog( ))
			{
				ofd.Multiselect = false;
				if (ofd.ShowDialog( ) == DialogResult.OK)
				{
					try
					{
						string fileName = ofd.FileName;
						ca_file_content2 = Convert.ToBase64String( File.ReadAllBytes( fileName ) );

						textBox_caFile2.Text = ofd.SafeFileName;
					}
					catch (Exception ex)
					{
						HslCommunication.BasicFramework.SoftBasic.ShowExceptionMessage( ex );
					}
				}
			}
		}

		private void button_clear2_Click( object sender, EventArgs e )
		{
			textBox_caFile2.Text = string.Empty;
			ca_file_content2 = string.Empty;
		}
	}
}
