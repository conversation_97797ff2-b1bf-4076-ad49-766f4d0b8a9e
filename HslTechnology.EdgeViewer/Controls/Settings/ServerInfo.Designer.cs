
namespace HslTechnology.EdgeViewer.Controls.Settings
{
	partial class ServerInfo
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
            this.textBox_adminPassword = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.textBox_admin = new System.Windows.Forms.TextBox();
            this.checkBox2 = new System.Windows.Forms.CheckBox();
            this.textBox3 = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.hslPanelText1 = new HslControls.HslPanelText();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.Column_name = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column_pwd = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column_login = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.Column_modify_edge = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.Column_close_start = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.Column_device_setting = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.Column_write_data = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.Column_call_method = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.checkBox_wildcard = new System.Windows.Forms.CheckBox();
            this.checkBox_resetNull = new System.Windows.Forms.CheckBox();
            this.label_location = new System.Windows.Forms.Label();
            this.textBox_location = new System.Windows.Forms.TextBox();
            this.label_gps = new System.Windows.Forms.Label();
            this.textBox_gps = new System.Windows.Forms.TextBox();
            this.hslPanelText1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // textBox_adminPassword
            // 
            this.textBox_adminPassword.Location = new System.Drawing.Point(334, 24);
            this.textBox_adminPassword.Name = "textBox_adminPassword";
            this.textBox_adminPassword.Size = new System.Drawing.Size(120, 23);
            this.textBox_adminPassword.TabIndex = 111;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(284, 27);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(44, 17);
            this.label11.TabIndex = 108;
            this.label11.Text = "密码：";
            // 
            // textBox_admin
            // 
            this.textBox_admin.Location = new System.Drawing.Point(151, 24);
            this.textBox_admin.Name = "textBox_admin";
            this.textBox_admin.Size = new System.Drawing.Size(125, 23);
            this.textBox_admin.TabIndex = 110;
            // 
            // checkBox2
            // 
            this.checkBox2.AutoSize = true;
            this.checkBox2.Location = new System.Drawing.Point(247, 374);
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Size = new System.Drawing.Size(99, 21);
            this.checkBox2.TabIndex = 107;
            this.checkBox2.Text = "是否开机启动";
            this.checkBox2.UseVisualStyleBackColor = true;
            this.checkBox2.Visible = false;
            // 
            // textBox3
            // 
            this.textBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBox3.Location = new System.Drawing.Point(97, 1);
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new System.Drawing.Size(175, 23);
            this.textBox3.TabIndex = 106;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(3, 3);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(92, 17);
            this.label3.TabIndex = 105;
            this.label3.Text = "服务器的名称：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(89, 27);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(56, 17);
            this.label2.TabIndex = 104;
            this.label2.Text = "用户名：";
            // 
            // textBox1
            // 
            this.textBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.textBox1.Location = new System.Drawing.Point(388, 1);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(84, 23);
            this.textBox1.TabIndex = 109;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(278, 3);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(104, 17);
            this.label1.TabIndex = 103;
            this.label1.Text = "服务器的端口号：";
            // 
            // hslPanelText1
            // 
            this.hslPanelText1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.hslPanelText1.Controls.Add(this.dataGridView1);
            this.hslPanelText1.Controls.Add(this.label7);
            this.hslPanelText1.Controls.Add(this.label6);
            this.hslPanelText1.Controls.Add(this.textBox_adminPassword);
            this.hslPanelText1.Controls.Add(this.label2);
            this.hslPanelText1.Controls.Add(this.label11);
            this.hslPanelText1.Controls.Add(this.textBox_admin);
            this.hslPanelText1.Location = new System.Drawing.Point(6, 30);
            this.hslPanelText1.Name = "hslPanelText1";
            this.hslPanelText1.Size = new System.Drawing.Size(466, 258);
            this.hslPanelText1.TabIndex = 112;
            this.hslPanelText1.Text = "安全设置";
            this.hslPanelText1.TextOffect = 10;
            this.hslPanelText1.ThemeColor = System.Drawing.Color.FromArgb(((int)(((byte)(41)))), ((int)(((byte)(57)))), ((int)(((byte)(85)))));
            // 
            // dataGridView1
            // 
            this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Column_name,
            this.Column_pwd,
            this.Column_login,
            this.Column_modify_edge,
            this.Column_close_start,
            this.Column_device_setting,
            this.Column_write_data,
            this.Column_call_method});
            this.dataGridView1.Location = new System.Drawing.Point(3, 69);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.RowTemplate.Height = 23;
            this.dataGridView1.Size = new System.Drawing.Size(460, 183);
            this.dataGridView1.TabIndex = 114;
            this.dataGridView1.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView1_CellContentClick);
            // 
            // Column_name
            // 
            this.Column_name.HeaderText = "用户名";
            this.Column_name.Name = "Column_name";
            // 
            // Column_pwd
            // 
            this.Column_pwd.HeaderText = "密码";
            this.Column_pwd.Name = "Column_pwd";
            // 
            // Column_login
            // 
            this.Column_login.HeaderText = "允许登录";
            this.Column_login.Name = "Column_login";
            this.Column_login.Width = 70;
            // 
            // Column_modify_edge
            // 
            this.Column_modify_edge.HeaderText = "网关配置";
            this.Column_modify_edge.Name = "Column_modify_edge";
            this.Column_modify_edge.Width = 70;
            // 
            // Column_close_start
            // 
            this.Column_close_start.HeaderText = "关机重启";
            this.Column_close_start.Name = "Column_close_start";
            this.Column_close_start.Width = 70;
            // 
            // Column_device_setting
            // 
            this.Column_device_setting.HeaderText = "设备配置";
            this.Column_device_setting.Name = "Column_device_setting";
            this.Column_device_setting.Width = 70;
            // 
            // Column_write_data
            // 
            this.Column_write_data.HeaderText = "反写数据";
            this.Column_write_data.Name = "Column_write_data";
            this.Column_write_data.Width = 70;
            // 
            // Column_call_method
            // 
            this.Column_call_method.HeaderText = "方法调用";
            this.Column_call_method.Name = "Column_call_method";
            this.Column_call_method.Width = 70;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(3, 51);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(92, 17);
            this.label7.TabIndex = 113;
            this.label7.Text = "其他账户列表：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(3, 27);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(56, 17);
            this.label6.TabIndex = 112;
            this.label6.Text = "管理员：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(0, 295);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(104, 17);
            this.label4.TabIndex = 113;
            this.label4.Text = "远程更新服务器：";
            // 
            // textBox4
            // 
            this.textBox4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBox4.Location = new System.Drawing.Point(109, 292);
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(360, 23);
            this.textBox4.TabIndex = 114;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(0, 324);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(104, 17);
            this.label5.TabIndex = 115;
            this.label5.Text = "设备的内部编号：";
            // 
            // textBox5
            // 
            this.textBox5.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBox5.Location = new System.Drawing.Point(109, 321);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(360, 23);
            this.textBox5.TabIndex = 116;
            // 
            // checkBox_wildcard
            // 
            this.checkBox_wildcard.AutoSize = true;
            this.checkBox_wildcard.Location = new System.Drawing.Point(3, 374);
            this.checkBox_wildcard.Name = "checkBox_wildcard";
            this.checkBox_wildcard.Size = new System.Drawing.Size(224, 21);
            this.checkBox_wildcard.TabIndex = 117;
            this.checkBox_wildcard.Text = "启用MQTT及Websocket主题通配符";
            this.checkBox_wildcard.UseVisualStyleBackColor = true;
            // 
            // checkBox_resetNull
            // 
            this.checkBox_resetNull.AutoSize = true;
            this.checkBox_resetNull.Location = new System.Drawing.Point(3, 397);
            this.checkBox_resetNull.Name = "checkBox_resetNull";
            this.checkBox_resetNull.Size = new System.Drawing.Size(183, 21);
            this.checkBox_resetNull.TabIndex = 118;
            this.checkBox_resetNull.Text = "当读取失败的时候重置为空值";
            this.checkBox_resetNull.UseVisualStyleBackColor = true;
            // 
            // label_location
            // 
            this.label_location.AutoSize = true;
            this.label_location.Location = new System.Drawing.Point(0, 352);
            this.label_location.Name = "label_location";
            this.label_location.Size = new System.Drawing.Size(68, 17);
            this.label_location.TabIndex = 119;
            this.label_location.Text = "安装位置：";
            // 
            // textBox_location
            // 
            this.textBox_location.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBox_location.Location = new System.Drawing.Point(109, 349);
            this.textBox_location.Name = "textBox_location";
            this.textBox_location.Size = new System.Drawing.Size(360, 23);
            this.textBox_location.TabIndex = 120;
            // 
            // label_gps
            // 
            this.label_gps.AutoSize = true;
            this.label_gps.Location = new System.Drawing.Point(226, 398);
            this.label_gps.Name = "label_gps";
            this.label_gps.Size = new System.Drawing.Size(34, 17);
            this.label_gps.TabIndex = 121;
            this.label_gps.Text = "GPS:";
            // 
            // textBox_gps
            // 
            this.textBox_gps.Location = new System.Drawing.Point(266, 395);
            this.textBox_gps.Name = "textBox_gps";
            this.textBox_gps.ReadOnly = true;
            this.textBox_gps.Size = new System.Drawing.Size(203, 23);
            this.textBox_gps.TabIndex = 122;
            // 
            // ServerInfo
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(215)))), ((int)(((byte)(218)))), ((int)(((byte)(233)))));
            this.Controls.Add(this.label_gps);
            this.Controls.Add(this.textBox_gps);
            this.Controls.Add(this.label_location);
            this.Controls.Add(this.textBox_location);
            this.Controls.Add(this.checkBox_resetNull);
            this.Controls.Add(this.checkBox_wildcard);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.textBox5);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.textBox4);
            this.Controls.Add(this.checkBox2);
            this.Controls.Add(this.textBox3);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.hslPanelText1);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "ServerInfo";
            this.Size = new System.Drawing.Size(480, 421);
            this.hslPanelText1.ResumeLayout(false);
            this.hslPanelText1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.TextBox textBox_adminPassword;
		private System.Windows.Forms.Label label11;
		private System.Windows.Forms.TextBox textBox_admin;
		private System.Windows.Forms.CheckBox checkBox2;
		private System.Windows.Forms.TextBox textBox3;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox textBox1;
		private System.Windows.Forms.Label label1;
		private HslControls.HslPanelText hslPanelText1;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.TextBox textBox4;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.TextBox textBox5;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.CheckBox checkBox_wildcard;
		private System.Windows.Forms.CheckBox checkBox_resetNull;
		private System.Windows.Forms.DataGridView dataGridView1;
		private System.Windows.Forms.DataGridViewTextBoxColumn Column_name;
		private System.Windows.Forms.DataGridViewTextBoxColumn Column_pwd;
		private System.Windows.Forms.DataGridViewCheckBoxColumn Column_login;
		private System.Windows.Forms.DataGridViewCheckBoxColumn Column_modify_edge;
		private System.Windows.Forms.DataGridViewCheckBoxColumn Column_close_start;
		private System.Windows.Forms.DataGridViewCheckBoxColumn Column_device_setting;
		private System.Windows.Forms.DataGridViewCheckBoxColumn Column_write_data;
		private System.Windows.Forms.DataGridViewCheckBoxColumn Column_call_method;
        private System.Windows.Forms.Label label_location;
        private System.Windows.Forms.TextBox textBox_location;
        private System.Windows.Forms.Label label_gps;
        private System.Windows.Forms.TextBox textBox_gps;
    }
}
