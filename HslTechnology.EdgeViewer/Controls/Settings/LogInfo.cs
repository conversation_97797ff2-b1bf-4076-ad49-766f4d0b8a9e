using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Config;
using HslCommunication;

namespace HslTechnology.EdgeViewer.Controls.Settings
{
	public partial class LogInfo : IConfigControl
	{
		public LogInfo( )
		{
			InitializeComponent( );
		}

		private void LogInfo_Load( object sender, EventArgs e )
		{
			comboBox1.DataSource = SoftBasic.GetEnumValues<HslCommunication.LogNet.GenerateMode>( );
			comboBox2.DataSource = SoftBasic.GetEnumValues<HslCommunication.LogNet.HslMessageDegree>( );
		}

		/// <inheritdoc/>
		public override void RenderParameter( JToken paraJson )
		{
			token = paraJson;
			string logMode = paraJson[nameof( LogInfoConfig.LogMode )].Value<string>( );
			if      (logMode == "None")        radioButton4.Checked = true;
			else if (logMode == "SingleFile")  radioButton1.Checked = true;
			else if (logMode == "FileSize")    radioButton2.Checked = true;
			else if (logMode == "DateTime")    radioButton3.Checked = true;

			textBox1.Text = ConfigHelper.GetConfig( paraJson, nameof( LogInfoConfig.LogFileSize ), 1024 ).ToString( );
			textBox2.Text = ConfigHelper.GetConfig( paraJson, nameof( LogInfoConfig.LogFileSizeCount ), 10 ).ToString( );
			comboBox1.SelectedItem = (HslCommunication.LogNet.GenerateMode)paraJson[nameof( LogInfoConfig.LogDateTimeMode )].Value<int>( );
			textBox3.Text = paraJson[nameof( LogInfoConfig.LogDateTimeFileCount )].Value<int>( ).ToString( );
			comboBox2.SelectedItem = (HslCommunication.LogNet.HslMessageDegree)paraJson[nameof( LogInfoConfig.MessageDegree )].Value<int>( );
		}

		public override void SetParameterToJToken( )
		{
			if (token == null) return;
			string logMode = "None";
			if      (radioButton1.Checked) logMode = "SingleFile";
			else if (radioButton2.Checked) logMode = "FileSize";
			else if (radioButton3.Checked) logMode = "DateTime";
			else if (radioButton4.Checked) logMode = "None";

			token[nameof( LogInfoConfig.LogMode )] = logMode;
			token[nameof( LogInfoConfig.LogFileSize )] = int.Parse( textBox1.Text );
			token[nameof( LogInfoConfig.LogFileSizeCount )] = int.Parse( textBox2.Text );
			token[nameof( LogInfoConfig.LogDateTimeMode )] = (int)(HslCommunication.LogNet.GenerateMode)comboBox1.SelectedItem;
			token[nameof( LogInfoConfig.LogDateTimeFileCount )] = int.Parse( textBox3.Text );
			token[nameof( LogInfoConfig.MessageDegree )] = (int)(HslCommunication.LogNet.HslMessageDegree)comboBox2.SelectedItem;

		}

		private JToken token;

		/// <inheritdoc/>
		public override OperateResult CheckInputLegal( )
		{
			if (!int.TryParse( textBox1.Text, out int logFileSize )) return new OperateResult( "文件大小必须是整型" );
			if (logFileSize < 10) return new OperateResult( "文件大小必须大于10" );
			if (!int.TryParse( textBox2.Text, out int logFileSizeCount )) return new OperateResult( "文件数量必须是整型" );
			if (logFileSizeCount < 3) return new OperateResult( "文件大小必须大于3" );

			if (!int.TryParse( textBox3.Text, out logFileSizeCount )) return new OperateResult( "文件数量必须是整型" );
			if (logFileSizeCount < 3) return new OperateResult( "文件大小必须大于3" );
			return OperateResult.CreateSuccessResult( );
		}
	}
}
