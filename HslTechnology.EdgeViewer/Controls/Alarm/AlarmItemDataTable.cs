using HslTechnology.Edge.Node.Alarm;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls.Alarm
{
	public partial class AlarmItemDataTable : UserControl
	{
		public AlarmItemDataTable( )
		{
			InitializeComponent( );
		}

		protected override void OnSizeChanged( EventArgs e )
		{
			base.OnSizeChanged( e );
			if (this.Width > 100)
			{
				dataGridView1.Columns[0].Width = (this.dataGridView1.Width - 20) * 60 / 530;
				dataGridView1.Columns[1].Width = (this.dataGridView1.Width - 20) * 100 / 530;
				dataGridView1.Columns[2].Width = (this.dataGridView1.Width - 20) * 120 / 530;
				dataGridView1.Columns[3].Width = (this.dataGridView1.Width - 20) * 60 / 530;
				dataGridView1.Columns[4].Width = (this.dataGridView1.Width - 20) * 80 / 530;
				dataGridView1.Columns[5].Width = (this.dataGridView1.Width - 20) * 110 / 530;
			}
		}

		public DataGridView DataGridView => dataGridView1;

		public void ShowList( List<AlarmDefinitionNode> alarmMarkItems )
		{
			HslTechnology.Edge.Controls.HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, alarmMarkItems.Count );
			for (int i = 0; i < alarmMarkItems.Count; i++)
			{
				dataGridView1.Rows[i].Cells[0].Value = alarmMarkItems[i].Code.ToString( );
				dataGridView1.Rows[i].Cells[1].Value = alarmMarkItems[i].Name;
				dataGridView1.Rows[i].Cells[2].Value = alarmMarkItems[i].Description;
				dataGridView1.Rows[i].Cells[3].Value = alarmMarkItems[i].Degree.ToString( );
				dataGridView1.Rows[i].Cells[4].Value = alarmMarkItems[i].Delay.ToString( );
				dataGridView1.Rows[i].Cells[5].Value = alarmMarkItems[i].Conditions;
				dataGridView1.Rows[i].Tag = alarmMarkItems[i];
			}
		}


	}
}
