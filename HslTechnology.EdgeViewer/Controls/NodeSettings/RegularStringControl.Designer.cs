
namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	partial class RegularStringControl
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.radioButton_format1 = new System.Windows.Forms.RadioButton();
			this.radioButton_format2 = new System.Windows.Forms.RadioButton();
			this.radioButton_format3 = new System.Windows.Forms.RadioButton();
			this.radioButton_format4 = new System.Windows.Forms.RadioButton();
			this.radioButton_format5 = new System.Windows.Forms.RadioButton();
			this.panel1 = new System.Windows.Forms.Panel();
			this.radioButton_format7 = new System.Windows.Forms.RadioButton();
			this.radioButton_format6 = new System.Windows.Forms.RadioButton();
			this.label1 = new System.Windows.Forms.Label();
			this.panel2 = new System.Windows.Forms.Panel();
			this.radioButton_encode_gb2312 = new System.Windows.Forms.RadioButton();
			this.radioButton_encode_utf16big = new System.Windows.Forms.RadioButton();
			this.radioButton_encode_utf16 = new System.Windows.Forms.RadioButton();
			this.radioButton_encode_utf8 = new System.Windows.Forms.RadioButton();
			this.radioButton_encode_ansi = new System.Windows.Forms.RadioButton();
			this.radioButton_encode_ascii = new System.Windows.Forms.RadioButton();
			this.edgeLabel3 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.label2 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.label4 = new System.Windows.Forms.Label();
			this.textBox_string_offset = new System.Windows.Forms.TextBox();
			this.label5 = new System.Windows.Forms.Label();
			this.panel3 = new System.Windows.Forms.Panel();
			this.radioButton_order_reverse = new System.Windows.Forms.RadioButton();
			this.radioButton_order_wordreverse = new System.Windows.Forms.RadioButton();
			this.radioButton_order_default = new System.Windows.Forms.RadioButton();
			this.checkBox_string_endwith0 = new System.Windows.Forms.CheckBox();
			this.checkBox_stringSourceLengthToEven = new System.Windows.Forms.CheckBox();
			this.panel1.SuspendLayout();
			this.panel2.SuspendLayout();
			this.panel3.SuspendLayout();
			this.SuspendLayout();
			// 
			// radioButton_format1
			// 
			this.radioButton_format1.AutoSize = true;
			this.radioButton_format1.Checked = true;
			this.radioButton_format1.Location = new System.Drawing.Point(3, 3);
			this.radioButton_format1.Name = "radioButton_format1";
			this.radioButton_format1.Size = new System.Drawing.Size(93, 21);
			this.radioButton_format1.TabIndex = 80;
			this.radioButton_format1.TabStop = true;
			this.radioButton_format1.Text = "定长 [格式1]";
			this.radioButton_format1.UseVisualStyleBackColor = true;
			// 
			// radioButton_format2
			// 
			this.radioButton_format2.AutoSize = true;
			this.radioButton_format2.Location = new System.Drawing.Point(3, 27);
			this.radioButton_format2.Name = "radioButton_format2";
			this.radioButton_format2.Size = new System.Drawing.Size(93, 21);
			this.radioButton_format2.TabIndex = 81;
			this.radioButton_format2.TabStop = true;
			this.radioButton_format2.Text = "变长 [格式2]";
			this.radioButton_format2.UseVisualStyleBackColor = true;
			// 
			// radioButton_format3
			// 
			this.radioButton_format3.AutoSize = true;
			this.radioButton_format3.Location = new System.Drawing.Point(3, 52);
			this.radioButton_format3.Name = "radioButton_format3";
			this.radioButton_format3.Size = new System.Drawing.Size(93, 21);
			this.radioButton_format3.TabIndex = 82;
			this.radioButton_format3.TabStop = true;
			this.radioButton_format3.Text = "变长 [格式3]";
			this.radioButton_format3.UseVisualStyleBackColor = true;
			// 
			// radioButton_format4
			// 
			this.radioButton_format4.AutoSize = true;
			this.radioButton_format4.Location = new System.Drawing.Point(3, 77);
			this.radioButton_format4.Name = "radioButton_format4";
			this.radioButton_format4.Size = new System.Drawing.Size(93, 21);
			this.radioButton_format4.TabIndex = 83;
			this.radioButton_format4.TabStop = true;
			this.radioButton_format4.Text = "变长 [格式4]";
			this.radioButton_format4.UseVisualStyleBackColor = true;
			// 
			// radioButton_format5
			// 
			this.radioButton_format5.AutoSize = true;
			this.radioButton_format5.Location = new System.Drawing.Point(3, 102);
			this.radioButton_format5.Name = "radioButton_format5";
			this.radioButton_format5.Size = new System.Drawing.Size(93, 21);
			this.radioButton_format5.TabIndex = 84;
			this.radioButton_format5.TabStop = true;
			this.radioButton_format5.Text = "变长 [格式5]";
			this.radioButton_format5.UseVisualStyleBackColor = true;
			// 
			// panel1
			// 
			this.panel1.Controls.Add(this.radioButton_format7);
			this.panel1.Controls.Add(this.radioButton_format6);
			this.panel1.Controls.Add(this.radioButton_format1);
			this.panel1.Controls.Add(this.radioButton_format5);
			this.panel1.Controls.Add(this.radioButton_format2);
			this.panel1.Controls.Add(this.radioButton_format4);
			this.panel1.Controls.Add(this.radioButton_format3);
			this.panel1.Location = new System.Drawing.Point(4, 46);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(94, 176);
			this.panel1.TabIndex = 85;
			// 
			// radioButton_format7
			// 
			this.radioButton_format7.AutoSize = true;
			this.radioButton_format7.Location = new System.Drawing.Point(3, 152);
			this.radioButton_format7.Name = "radioButton_format7";
			this.radioButton_format7.Size = new System.Drawing.Size(93, 21);
			this.radioButton_format7.TabIndex = 86;
			this.radioButton_format7.TabStop = true;
			this.radioButton_format7.Text = "变长 [格式7]";
			this.radioButton_format7.UseVisualStyleBackColor = true;
			// 
			// radioButton_format6
			// 
			this.radioButton_format6.AutoSize = true;
			this.radioButton_format6.Location = new System.Drawing.Point(3, 127);
			this.radioButton_format6.Name = "radioButton_format6";
			this.radioButton_format6.Size = new System.Drawing.Size(93, 21);
			this.radioButton_format6.TabIndex = 85;
			this.radioButton_format6.TabStop = true;
			this.radioButton_format6.Text = "变长 [格式6]";
			this.radioButton_format6.UseVisualStyleBackColor = true;
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(3, 6);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(44, 17);
			this.label1.TabIndex = 86;
			this.label1.Text = "编码：";
			// 
			// panel2
			// 
			this.panel2.Controls.Add(this.radioButton_encode_gb2312);
			this.panel2.Controls.Add(this.radioButton_encode_utf16big);
			this.panel2.Controls.Add(this.radioButton_encode_utf16);
			this.panel2.Controls.Add(this.radioButton_encode_utf8);
			this.panel2.Controls.Add(this.radioButton_encode_ansi);
			this.panel2.Controls.Add(this.radioButton_encode_ascii);
			this.panel2.Controls.Add(this.label1);
			this.panel2.Location = new System.Drawing.Point(3, 225);
			this.panel2.Name = "panel2";
			this.panel2.Size = new System.Drawing.Size(408, 50);
			this.panel2.TabIndex = 87;
			// 
			// radioButton_encode_gb2312
			// 
			this.radioButton_encode_gb2312.AutoSize = true;
			this.radioButton_encode_gb2312.Location = new System.Drawing.Point(43, 28);
			this.radioButton_encode_gb2312.Name = "radioButton_encode_gb2312";
			this.radioButton_encode_gb2312.Size = new System.Drawing.Size(71, 21);
			this.radioButton_encode_gb2312.TabIndex = 92;
			this.radioButton_encode_gb2312.TabStop = true;
			this.radioButton_encode_gb2312.Text = "GB2312";
			this.radioButton_encode_gb2312.UseVisualStyleBackColor = true;
			// 
			// radioButton_encode_utf16big
			// 
			this.radioButton_encode_utf16big.AutoSize = true;
			this.radioButton_encode_utf16big.Location = new System.Drawing.Point(305, 5);
			this.radioButton_encode_utf16big.Name = "radioButton_encode_utf16big";
			this.radioButton_encode_utf16big.Size = new System.Drawing.Size(86, 21);
			this.radioButton_encode_utf16big.TabIndex = 91;
			this.radioButton_encode_utf16big.TabStop = true;
			this.radioButton_encode_utf16big.Text = "UTF16-Big";
			this.radioButton_encode_utf16big.UseVisualStyleBackColor = true;
			// 
			// radioButton_encode_utf16
			// 
			this.radioButton_encode_utf16.AutoSize = true;
			this.radioButton_encode_utf16.Location = new System.Drawing.Point(232, 5);
			this.radioButton_encode_utf16.Name = "radioButton_encode_utf16";
			this.radioButton_encode_utf16.Size = new System.Drawing.Size(62, 21);
			this.radioButton_encode_utf16.TabIndex = 90;
			this.radioButton_encode_utf16.TabStop = true;
			this.radioButton_encode_utf16.Text = "UTF16";
			this.radioButton_encode_utf16.UseVisualStyleBackColor = true;
			// 
			// radioButton_encode_utf8
			// 
			this.radioButton_encode_utf8.AutoSize = true;
			this.radioButton_encode_utf8.Location = new System.Drawing.Point(170, 5);
			this.radioButton_encode_utf8.Name = "radioButton_encode_utf8";
			this.radioButton_encode_utf8.Size = new System.Drawing.Size(55, 21);
			this.radioButton_encode_utf8.TabIndex = 89;
			this.radioButton_encode_utf8.TabStop = true;
			this.radioButton_encode_utf8.Text = "UTF8";
			this.radioButton_encode_utf8.UseVisualStyleBackColor = true;
			// 
			// radioButton_encode_ansi
			// 
			this.radioButton_encode_ansi.AutoSize = true;
			this.radioButton_encode_ansi.Location = new System.Drawing.Point(106, 5);
			this.radioButton_encode_ansi.Name = "radioButton_encode_ansi";
			this.radioButton_encode_ansi.Size = new System.Drawing.Size(55, 21);
			this.radioButton_encode_ansi.TabIndex = 88;
			this.radioButton_encode_ansi.TabStop = true;
			this.radioButton_encode_ansi.Text = "ANSI";
			this.radioButton_encode_ansi.UseVisualStyleBackColor = true;
			// 
			// radioButton_encode_ascii
			// 
			this.radioButton_encode_ascii.AutoSize = true;
			this.radioButton_encode_ascii.Checked = true;
			this.radioButton_encode_ascii.Location = new System.Drawing.Point(43, 5);
			this.radioButton_encode_ascii.Name = "radioButton_encode_ascii";
			this.radioButton_encode_ascii.Size = new System.Drawing.Size(57, 21);
			this.radioButton_encode_ascii.TabIndex = 87;
			this.radioButton_encode_ascii.TabStop = true;
			this.radioButton_encode_ascii.Text = "ASCII";
			this.radioButton_encode_ascii.UseVisualStyleBackColor = true;
			// 
			// edgeLabel3
			// 
			this.edgeLabel3.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel3.ForeColor = System.Drawing.Color.White;
			this.edgeLabel3.Location = new System.Drawing.Point(3, 1);
			this.edgeLabel3.Name = "edgeLabel3";
			this.edgeLabel3.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel3.Size = new System.Drawing.Size(408, 22);
			this.edgeLabel3.TabIndex = 79;
			this.edgeLabel3.Text = "字符串格式选择";
			this.edgeLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.ForeColor = System.Drawing.Color.DodgerBlue;
			this.label2.Location = new System.Drawing.Point(100, 26);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(234, 17);
			this.label2.TabIndex = 88;
			this.label2.Text = "字符串例子 “123456”，显示和编码无关";
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.ForeColor = System.Drawing.Color.Red;
			this.label3.Location = new System.Drawing.Point(4, 278);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(389, 17);
			this.label3.TabIndex = 89;
			this.label3.Text = "绿色为长度信息，格式5和格式7表示双字节长度，格式6表示四字节长度";
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(3, 301);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(128, 17);
			this.label4.TabIndex = 90;
			this.label4.Text = "每个字节减去固定值：";
			// 
			// textBox_string_offset
			// 
			this.textBox_string_offset.Location = new System.Drawing.Point(132, 298);
			this.textBox_string_offset.Name = "textBox_string_offset";
			this.textBox_string_offset.Size = new System.Drawing.Size(111, 23);
			this.textBox_string_offset.TabIndex = 91;
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(3, 6);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(80, 17);
			this.label5.TabIndex = 92;
			this.label5.Text = "字符串顺序：";
			// 
			// panel3
			// 
			this.panel3.Controls.Add(this.radioButton_order_reverse);
			this.panel3.Controls.Add(this.radioButton_order_wordreverse);
			this.panel3.Controls.Add(this.radioButton_order_default);
			this.panel3.Controls.Add(this.label5);
			this.panel3.Location = new System.Drawing.Point(2, 323);
			this.panel3.Name = "panel3";
			this.panel3.Size = new System.Drawing.Size(407, 30);
			this.panel3.TabIndex = 93;
			// 
			// radioButton_order_reverse
			// 
			this.radioButton_order_reverse.AutoSize = true;
			this.radioButton_order_reverse.Location = new System.Drawing.Point(313, 5);
			this.radioButton_order_reverse.Name = "radioButton_order_reverse";
			this.radioButton_order_reverse.Size = new System.Drawing.Size(50, 21);
			this.radioButton_order_reverse.TabIndex = 95;
			this.radioButton_order_reverse.TabStop = true;
			this.radioButton_order_reverse.Text = "倒序";
			this.radioButton_order_reverse.UseVisualStyleBackColor = true;
			// 
			// radioButton_order_wordreverse
			// 
			this.radioButton_order_wordreverse.AutoSize = true;
			this.radioButton_order_wordreverse.Location = new System.Drawing.Point(171, 5);
			this.radioButton_order_wordreverse.Name = "radioButton_order_wordreverse";
			this.radioButton_order_wordreverse.Size = new System.Drawing.Size(122, 21);
			this.radioButton_order_wordreverse.TabIndex = 94;
			this.radioButton_order_wordreverse.TabStop = true;
			this.radioButton_order_wordreverse.Text = "每两个为单位颠倒";
			this.radioButton_order_wordreverse.UseVisualStyleBackColor = true;
			// 
			// radioButton_order_default
			// 
			this.radioButton_order_default.AutoSize = true;
			this.radioButton_order_default.Checked = true;
			this.radioButton_order_default.Location = new System.Drawing.Point(89, 5);
			this.radioButton_order_default.Name = "radioButton_order_default";
			this.radioButton_order_default.Size = new System.Drawing.Size(74, 21);
			this.radioButton_order_default.TabIndex = 93;
			this.radioButton_order_default.TabStop = true;
			this.radioButton_order_default.Text = "默认顺序";
			this.radioButton_order_default.UseVisualStyleBackColor = true;
			// 
			// checkBox_string_endwith0
			// 
			this.checkBox_string_endwith0.AutoSize = true;
			this.checkBox_string_endwith0.Location = new System.Drawing.Point(6, 356);
			this.checkBox_string_endwith0.Name = "checkBox_string_endwith0";
			this.checkBox_string_endwith0.Size = new System.Drawing.Size(215, 21);
			this.checkBox_string_endwith0.TabIndex = 94;
			this.checkBox_string_endwith0.Text = "字符串是否碰到 \\0 作为结束标记？";
			this.checkBox_string_endwith0.UseVisualStyleBackColor = true;
			// 
			// checkBox_stringSourceLengthToEven
			// 
			this.checkBox_stringSourceLengthToEven.AutoSize = true;
			this.checkBox_stringSourceLengthToEven.Location = new System.Drawing.Point(233, 356);
			this.checkBox_stringSourceLengthToEven.Name = "checkBox_stringSourceLengthToEven";
			this.checkBox_stringSourceLengthToEven.Size = new System.Drawing.Size(153, 21);
			this.checkBox_stringSourceLengthToEven.TabIndex = 95;
			this.checkBox_stringSourceLengthToEven.Text = "原始字节扩充偶数长度?";
			this.checkBox_stringSourceLengthToEven.UseVisualStyleBackColor = true;
			// 
			// RegularStringControl
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.AliceBlue;
			this.Controls.Add(this.checkBox_stringSourceLengthToEven);
			this.Controls.Add(this.checkBox_string_endwith0);
			this.Controls.Add(this.panel3);
			this.Controls.Add(this.textBox_string_offset);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.panel2);
			this.Controls.Add(this.panel1);
			this.Controls.Add(this.edgeLabel3);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "RegularStringControl";
			this.Size = new System.Drawing.Size(414, 380);
			this.Paint += new System.Windows.Forms.PaintEventHandler(this.panel1_Paint);
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.panel2.ResumeLayout(false);
			this.panel2.PerformLayout();
			this.panel3.ResumeLayout(false);
			this.panel3.PerformLayout();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private EdgeLabel edgeLabel3;
		private System.Windows.Forms.RadioButton radioButton_format1;
		private System.Windows.Forms.RadioButton radioButton_format2;
		private System.Windows.Forms.RadioButton radioButton_format3;
		private System.Windows.Forms.RadioButton radioButton_format4;
		private System.Windows.Forms.RadioButton radioButton_format5;
		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Panel panel2;
		private System.Windows.Forms.RadioButton radioButton_encode_gb2312;
		private System.Windows.Forms.RadioButton radioButton_encode_utf16big;
		private System.Windows.Forms.RadioButton radioButton_encode_utf16;
		private System.Windows.Forms.RadioButton radioButton_encode_utf8;
		private System.Windows.Forms.RadioButton radioButton_encode_ansi;
		private System.Windows.Forms.RadioButton radioButton_encode_ascii;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.RadioButton radioButton_format6;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.RadioButton radioButton_format7;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.TextBox textBox_string_offset;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Panel panel3;
		private System.Windows.Forms.RadioButton radioButton_order_reverse;
		private System.Windows.Forms.RadioButton radioButton_order_wordreverse;
		private System.Windows.Forms.RadioButton radioButton_order_default;
		private System.Windows.Forms.CheckBox checkBox_string_endwith0;
		private System.Windows.Forms.CheckBox checkBox_stringSourceLengthToEven;
	}
}
