using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Render;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication;

namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	public partial class PropertyConfigControl : UserControl
	{
		public PropertyConfigControl( )
		{
			InitializeComponent( );
			//toolTip = new ToolTip( );

			SetStyle( ControlStyles.UserPaint | ControlStyles.SupportsTransparentBackColor, true );
			SetStyle( ControlStyles.ResizeRedraw, true );
			SetStyle( ControlStyles.OptimizedDoubleBuffer, true );
			SetStyle( ControlStyles.AllPaintingInWmPaint, true );
		}

		private void PropertyConfigControl_Paint( object sender, PaintEventArgs e )
		{
			Graphics g = e.Graphics;
			if (Width < 2 || Height < 2) return;

			g.DrawRectangle( Pens.Gray, new Rectangle( 0, 0, Width - 1, Height - 1 ) );
			g.DrawLine( Pens.Gray, 150, 0, 150, Height );
			g.DrawLine( Pens.Gray, 230, 0, 230, Height );
			g.DrawLine( Pens.Gray, 430, 0, 430, Height );
		}

		public void SetNodePropertyConfig( NodePropertyConfig propertyConfig)
		{
			this.propertyConfig = propertyConfig.DeepClone( );
			label_column1.Text = string.IsNullOrEmpty( propertyConfig.DisplayName ) ? propertyConfig.Name : propertyConfig.DisplayName;
			label_column2.Text = propertyConfig.DataType.ToString( );
			label_column3.Visible = false;
			if(propertyConfig.DataType == DataType.Bool)
			{
				CheckBox checkBox = new CheckBox( );
				checkBox.AutoSize = false;
				checkBox.Size = new Size( 196, label_column1.Height );
				checkBox.Parent = this;
				checkBox.Location = new Point( 233, 5 );
				checkBox.Checked = bool.Parse( propertyConfig.DefaultValue );
				this.valueInputControl = checkBox;
			}
			else if (propertyConfig.DataType == DataType.Enum)
			{
				ComboBox comboBox = new ComboBox( );
				comboBox.AutoSize = false;
				comboBox.Size = new Size( 199, Height );
				comboBox.Parent = this;
				comboBox.Location = new Point( 231, 1 );
				comboBox.DropDownStyle = ComboBoxStyle.DropDownList;
				comboBox.DataSource = propertyConfig.EnumValues;
				comboBox.SelectedItem = propertyConfig.DefaultValue;
				this.valueInputControl = comboBox;
			}
			else
			{
				TextBox textBox = new TextBox( );
				textBox.Size = new Size( 196, label_column1.Height );
				textBox.Parent = this;
				textBox.BorderStyle = BorderStyle.None;
				textBox.Location = new Point( 233, 4 );
				textBox.Text = propertyConfig.DefaultValue;
				this.valueInputControl = textBox;

				if (propertyConfig.Name == nameof( DeviceNode.PluginsType ) ||
					propertyConfig.Name == nameof( DeviceNode.CreateTime ))
				{
					textBox.ReadOnly = true;
					textBox.BackColor = Color.White;
				}
			}

			label_column4.Text = propertyConfig.Description;
		}

		private OperateResult ParseTextBox<T>( Func<string, T> converter, string tagName, DataType dataType )
		{
			TextBox textBox = (TextBox)valueInputControl;
			try
			{
				propertyConfig.DefaultValue = converter( textBox.Text )?.ToString( );
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				return new OperateResult( $"数据名[{tagName}] 值输入错误，需要输入类型为[{dataType}]的数据，错误原因：" + Environment.NewLine +
					ex.Message );
			}
		}

		public OperateResult UpdatePluginsValue( )
		{
			if (propertyConfig.Name == nameof( DeviceNode.PluginsType ) ||
				propertyConfig.Name == nameof( DeviceNode.CreateTime ))
			{
				return OperateResult.CreateSuccessResult( );
			}

			if (propertyConfig.Name == nameof( GroupNode.Name ) )
			{
				TextBox textBox = (TextBox)valueInputControl;
				if (string.IsNullOrEmpty( textBox.Text ) || textBox.Text.Contains("/"))
				{
					return new OperateResult( "节点名称不能为空，不能包含特殊字符！" );
				}
			}

			switch (propertyConfig.DataType)
			{
				case DataType.Bool:
					{
						CheckBox checkBox = (CheckBox)valueInputControl;
						propertyConfig.DefaultValue = checkBox.Checked.ToString( );
						return OperateResult.CreateSuccessResult( );
					}
				case DataType.Byte:     return ParseTextBox( byte.Parse,   label_column1.Text, DataType.Byte );
				case DataType.Int16:    return ParseTextBox( short.Parse,  label_column1.Text, DataType.Int16 );
				case DataType.UInt16:   return ParseTextBox( ushort.Parse, label_column1.Text, DataType.UInt16 );
				case DataType.Int32:    return ParseTextBox( int.Parse,    label_column1.Text, DataType.Int32 );
				case DataType.UInt32:   return ParseTextBox( uint.Parse,   label_column1.Text, DataType.UInt32 );
				case DataType.Int64:    return ParseTextBox( long.Parse,   label_column1.Text, DataType.Int64 );
				case DataType.UInt64:   return ParseTextBox( ulong.Parse,  label_column1.Text, DataType.UInt64 );
				case DataType.Float:    return ParseTextBox( float.Parse,  label_column1.Text, DataType.Float );
				case DataType.Double:   return ParseTextBox( double.Parse, label_column1.Text, DataType.Double );
				case DataType.Hex:
				case DataType.String:   return ParseTextBox( m => m, label_column1.Text, DataType.String );
				case DataType.DateTime: return ParseTextBox( DateTime.Parse, label_column1.Text, DataType.DateTime );
				case DataType.Guid:     return ParseTextBox( Guid.Parse, label_column1.Text, DataType.Guid );
				case DataType.Enum:
					{
						ComboBox comboBox = (ComboBox)valueInputControl;
						propertyConfig.DefaultValue = comboBox.SelectedItem.ToString( );
						return OperateResult.CreateSuccessResult( );
					}
				default: return new OperateResult( $"Not supported DataType:[{propertyConfig.DataType}]" );
			}
		}

		/// <summary>
		/// 获取当前的配置信息
		/// </summary>
		public NodePropertyConfig GetNodePropertyConfig => propertyConfig;


		//private ToolTip toolTip;
		private Control valueInputControl;
		private NodePropertyConfig propertyConfig;
	}
}
