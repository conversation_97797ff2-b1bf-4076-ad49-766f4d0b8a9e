using HslCommunication;
using HslTechnology.Edge.Node.Regular;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	public partial class RegularBCDControl : UserControl
	{
		public RegularBCDControl( )
		{
			InitializeComponent( );
			DoubleBuffered = true;
			stringFormat = new StringFormat( );
			stringFormat.Alignment = StringAlignment.Center;
			stringFormat.LineAlignment = StringAlignment.Center;
			fontMini = new Font( Font.FontFamily, 7f );
		}

		/// <summary>
		/// 显示节点的基本信息
		/// </summary>
		/// <param name="scalarNode">节点信息</param>
		public void ShowRegularScalarNode( RegularScalarNode scalarNode )
		{
			switch (scalarNode.BCDFormat)
			{
				case Edge.Node.BCDFormat.C8421: radioButton_bcd_8421.Checked = true; break;
				case Edge.Node.BCDFormat.C5421: radioButton_bcd_5421.Checked = true; break;
				case Edge.Node.BCDFormat.C2421: radioButton_bcd_2421.Checked = true; break;
				case Edge.Node.BCDFormat.C3: radioButton_bcd_3.Checked = true; break;
				case Edge.Node.BCDFormat.Gray: radioButton_bcd_gray.Checked = true; break;
			}
			checkBox_string_endwith0.Checked = scalarNode.StringEndwithZero;
		}

		/// <summary>
		/// 获取节点的信息
		/// </summary>
		/// <param name="scalarNode">节点基本信息</param>
		/// <returns>是否成功赋值</returns>
		public OperateResult GetRegularScalarNode( RegularScalarNode scalarNode )
		{
			try
			{
				if (radioButton_bcd_8421.Checked)  scalarNode.BCDFormat = Edge.Node.BCDFormat.C8421;
				if (radioButton_bcd_5421.Checked)  scalarNode.BCDFormat = Edge.Node.BCDFormat.C5421;
				if (radioButton_bcd_2421.Checked)  scalarNode.BCDFormat = Edge.Node.BCDFormat.C2421;
				if (radioButton_bcd_3.Checked)     scalarNode.BCDFormat = Edge.Node.BCDFormat.C3;
				if (radioButton_bcd_gray.Checked)  scalarNode.BCDFormat = Edge.Node.BCDFormat.Gray;
				scalarNode.StringEndwithZero = checkBox_string_endwith0.Checked;
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				return new OperateResult( ex.Message );
			}
		}

		private void RegularBCDControl_Paint( object sender, PaintEventArgs e )
		{
			// 绘制相关的示例界面
			Graphics g = e.Graphics;
			g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
			int lineHeight = 48;
			// 0123456789
			// 8421编码
			DrawStringFormat( g, 80, lineHeight, "0123456789" );
			lineHeight += 25;
			// 5421编码
			DrawStringFormat( g, 80, lineHeight, "0123489ABC" );
			lineHeight += 25;
			// 2421编码
			DrawStringFormat( g, 80, lineHeight, "01234BCDEF" );
			lineHeight += 25;
			// 余3编码
			DrawStringFormat( g, 80, lineHeight, "3456789ABC" );
			lineHeight += 25;
			// 格雷码
			DrawStringFormat( g, 80, lineHeight, "01326754C8" );
			lineHeight += 25;

		}

		private void DrawRectangleString( Graphics g, int x, int y, Font font, Brush brush, Brush back, string content )
		{
			Rectangle rectangle = new Rectangle( x, y, 25, 20 );
			g.FillRectangle( back, rectangle );
			g.DrawRectangle( Pens.LightGray, rectangle );
			g.DrawString( content, font, brush, rectangle, stringFormat );
		}

		private void DrawStringFormat( Graphics g, int x, int y, string content )
		{
			for (int i = 0; i < 5; i++)
			{
				DrawRectangleString( g, x + 30 * i, y, Font, Brushes.DodgerBlue, Brushes.White, content.Substring( i * 2, 2 ) );
			}
		}


		private StringFormat stringFormat;
		private Font fontMini;
	}
}
