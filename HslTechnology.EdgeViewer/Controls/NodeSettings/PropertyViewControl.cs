using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	public partial class PropertyViewControl : UserControl
	{
		public PropertyViewControl( )
		{
			InitializeComponent( );
		}

		/// <summary>
		/// 设置当前的显示的属性节点
		/// </summary>
		/// <param name="obj">对象信息</param>
		public void SetObjectShow( object obj )
		{
			this.propertyGrid1.SetSelectedObject( obj );
		}
	}
}
