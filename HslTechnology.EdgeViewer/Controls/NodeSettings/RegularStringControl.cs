using HslCommunication;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node.Regular;

namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	public partial class RegularStringControl : UserControl
	{
		public RegularStringControl( )
		{
			InitializeComponent( );
			DoubleBuffered = true;
			stringFormat = new StringFormat( );
			stringFormat.Alignment = StringAlignment.Center;
			stringFormat.LineAlignment = StringAlignment.Center;
			fontMini = new Font( Font.FontFamily, 7f );
		}

		private void DrawRectangleString( Graphics g, int x, int y, Font font, Brush brush, Brush back, string content )
		{
			Rectangle rectangle = new Rectangle( x, y, 25, 20 );
			g.FillRectangle( back, rectangle );
			g.DrawRectangle( Pens.LightGray, rectangle );
			g.DrawString( content, font, brush, rectangle, stringFormat );
		}

		private void DrawStringFormat( Graphics g, int x, int y )
		{
			for (int i = 0; i < 6; i++)
			{
				DrawRectangleString( g, x + 30 * i, y, Font, Brushes.DodgerBlue, Brushes.White, (31 + i).ToString( ) );
			}
		}

		private void panel1_Paint( object sender, PaintEventArgs e )
		{
			// 绘制相关的示例界面
			Graphics g = e.Graphics;
			g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
			int lineHeight = 48;
			// 格式一
			DrawStringFormat( g, 100, lineHeight );
			lineHeight += 25;
			// 格式二
			DrawRectangleString( g, 100, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "06" );
			DrawStringFormat( g, 130, lineHeight );
			lineHeight += 25;
			// 格式三
			DrawRectangleString( g, 100, lineHeight, fontMini, Brushes.DodgerBlue, Brushes.LavenderBlush, "Max" );
			DrawRectangleString( g, 130, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "06" );
			DrawStringFormat( g, 160, lineHeight );
			lineHeight += 25;
			// 格式四
			DrawRectangleString( g, 100, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "06" );
			DrawRectangleString( g, 130, lineHeight, fontMini, Brushes.DodgerBlue, Brushes.LavenderBlush, "Max" );
			DrawStringFormat( g, 160, lineHeight );
			lineHeight += 25;
			// 格式五
			DrawRectangleString( g, 100, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "06" );
			DrawRectangleString( g, 130, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "00" );
			DrawStringFormat( g, 160, lineHeight );
			lineHeight += 25;
			// 格式六
			DrawRectangleString( g, 100, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "06" );
			DrawRectangleString( g, 130, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "00" );
			DrawRectangleString( g, 160, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "00" );
			DrawRectangleString( g, 190, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "00" );
			DrawStringFormat( g, 220, lineHeight );
			lineHeight += 25;
			// 格式七
			DrawRectangleString( g, 100, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "-" );
			DrawRectangleString( g, 130, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "-" );
			DrawRectangleString( g, 160, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "06" );
			DrawRectangleString( g, 190, lineHeight, Font, Brushes.DodgerBlue, Brushes.LightGreen, "00" );
			DrawStringFormat( g, 220, lineHeight );
			lineHeight += 25;

		}


		/// <summary>
		/// 显示节点的基本信息
		/// </summary>
		/// <param name="scalarNode">节点信息</param>
		public void ShowRegularScalarNode( RegularScalarNode scalarNode )
		{
			switch (scalarNode.StringFormat)
			{
				case Edge.Node.StringFormat.Format1: radioButton_format1.Checked = true; break;
				case Edge.Node.StringFormat.Format2: radioButton_format2.Checked = true; break;
				case Edge.Node.StringFormat.Format3: radioButton_format3.Checked = true; break;
				case Edge.Node.StringFormat.Format4: radioButton_format4.Checked = true; break;
				case Edge.Node.StringFormat.Format5: radioButton_format5.Checked = true; break;
				case Edge.Node.StringFormat.Format6: radioButton_format6.Checked = true; break;
				case Edge.Node.StringFormat.Format7: radioButton_format7.Checked = true; break;
			}
			switch (scalarNode.StringEncoding)
			{
				case Edge.Node.StringEncoding.ASCII:    radioButton_encode_ascii.Checked    = true; break;
				case Edge.Node.StringEncoding.ANSI:     radioButton_encode_ansi.Checked     = true; break;
				case Edge.Node.StringEncoding.UTF8:     radioButton_encode_utf8.Checked     = true; break;
				case Edge.Node.StringEncoding.UTF16:    radioButton_encode_utf16.Checked    = true; break;
				case Edge.Node.StringEncoding.UTF16Big: radioButton_encode_utf16big.Checked = true; break;
				case Edge.Node.StringEncoding.GB2312:   radioButton_encode_gb2312.Checked   = true; break;
			}

			textBox_string_offset.Text = scalarNode.StringOffset.ToString( );
			switch (scalarNode.StringOrder)
			{
				case Edge.Node.StringOrder.Default: radioButton_order_default.Checked = true; break;
				case Edge.Node.StringOrder.Reverse: radioButton_order_reverse.Checked = true; break;
				case Edge.Node.StringOrder.WordReverse: radioButton_order_wordreverse.Checked = true; break;
			}
			checkBox_string_endwith0.Checked = scalarNode.StringEndwithZero;
			checkBox_stringSourceLengthToEven.Checked = scalarNode.StringSourceLengthToEven;
		}

		/// <summary>
		/// 获取节点的信息
		/// </summary>
		/// <param name="scalarNode">规则节点信息</param>
		/// <returns>是否设置成功</returns>
		public OperateResult GetRegularScalarNode( RegularScalarNode scalarNode )
		{
			try
			{
				if (radioButton_format1.Checked) scalarNode.StringFormat = Edge.Node.StringFormat.Format1;
				if (radioButton_format2.Checked) scalarNode.StringFormat = Edge.Node.StringFormat.Format2;
				if (radioButton_format3.Checked) scalarNode.StringFormat = Edge.Node.StringFormat.Format3;
				if (radioButton_format4.Checked) scalarNode.StringFormat = Edge.Node.StringFormat.Format4;
				if (radioButton_format5.Checked) scalarNode.StringFormat = Edge.Node.StringFormat.Format5;
				if (radioButton_format6.Checked) scalarNode.StringFormat = Edge.Node.StringFormat.Format6;
				if (radioButton_format7.Checked) scalarNode.StringFormat = Edge.Node.StringFormat.Format7;

				if (radioButton_encode_ascii.Checked)    scalarNode.StringEncoding = Edge.Node.StringEncoding.ASCII;
				if (radioButton_encode_ansi.Checked)     scalarNode.StringEncoding = Edge.Node.StringEncoding.ANSI;
				if (radioButton_encode_utf8.Checked)     scalarNode.StringEncoding = Edge.Node.StringEncoding.UTF8;
				if (radioButton_encode_utf16.Checked)    scalarNode.StringEncoding = Edge.Node.StringEncoding.UTF16;
				if (radioButton_encode_utf16big.Checked) scalarNode.StringEncoding = Edge.Node.StringEncoding.UTF16Big;
				if (radioButton_encode_gb2312.Checked)   scalarNode.StringEncoding = Edge.Node.StringEncoding.GB2312;

				int offset = Convert.ToInt32( textBox_string_offset.Text );
				if (offset < -256 || offset > 255) throw new Exception( "字符串的偏移值只能在 -256 到 255 之间，使用 10 进制的表示方式。" );

				scalarNode.StringOffset = offset;
				if (radioButton_order_default.Checked)     scalarNode.StringOrder = Edge.Node.StringOrder.Default;
				if (radioButton_order_wordreverse.Checked) scalarNode.StringOrder = Edge.Node.StringOrder.WordReverse;
				if (radioButton_order_reverse.Checked)     scalarNode.StringOrder = Edge.Node.StringOrder.Reverse;
				scalarNode.StringEndwithZero = checkBox_string_endwith0.Checked;
				scalarNode.StringSourceLengthToEven = checkBox_stringSourceLengthToEven.Checked;
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				return new OperateResult( ex.Message );
			}
		}

		private StringFormat stringFormat;
		private Font fontMini;
	}
}
