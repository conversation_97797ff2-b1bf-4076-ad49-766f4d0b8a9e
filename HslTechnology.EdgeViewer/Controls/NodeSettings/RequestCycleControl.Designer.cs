
namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	partial class RequestCycleControl
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.textBox5 = new System.Windows.Forms.TextBox();
			this.label8 = new System.Windows.Forms.Label();
			this.numericUpDown1 = new System.Windows.Forms.NumericUpDown();
			this.label7 = new System.Windows.Forms.Label();
			this.label6 = new System.Windows.Forms.Label();
			this.panel1 = new System.Windows.Forms.Panel();
			this.radioButton2 = new System.Windows.Forms.RadioButton();
			this.radioButton1 = new System.Windows.Forms.RadioButton();
			this.label1 = new System.Windows.Forms.Label();
			this.textBox_condition = new System.Windows.Forms.TextBox();
			this.comboBox_condition_type = new System.Windows.Forms.ComboBox();
			this.edgeLabel3 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).BeginInit();
			this.panel1.SuspendLayout();
			this.SuspendLayout();
			// 
			// textBox5
			// 
			this.textBox5.Location = new System.Drawing.Point(277, 57);
			this.textBox5.Name = "textBox5";
			this.textBox5.Size = new System.Drawing.Size(121, 23);
			this.textBox5.TabIndex = 80;
			this.textBox5.Text = "1";
			// 
			// label8
			// 
			this.label8.AutoSize = true;
			this.label8.Location = new System.Drawing.Point(203, 60);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(68, 17);
			this.label8.TabIndex = 79;
			this.label8.Text = "请求次数：";
			// 
			// numericUpDown1
			// 
			this.numericUpDown1.Location = new System.Drawing.Point(78, 28);
			this.numericUpDown1.Maximum = new decimal(new int[] {
            1000000000,
            0,
            0,
            0});
			this.numericUpDown1.Name = "numericUpDown1";
			this.numericUpDown1.Size = new System.Drawing.Size(77, 23);
			this.numericUpDown1.TabIndex = 77;
			this.numericUpDown1.ThousandsSeparator = true;
			this.numericUpDown1.Value = new decimal(new int[] {
            1000,
            0,
            0,
            0});
			// 
			// label7
			// 
			this.label7.AutoSize = true;
			this.label7.ForeColor = System.Drawing.Color.Gray;
			this.label7.Location = new System.Drawing.Point(163, 30);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(92, 17);
			this.label7.TabIndex = 76;
			this.label7.Text = "（单位：毫秒）";
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.Location = new System.Drawing.Point(4, 30);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(68, 17);
			this.label6.TabIndex = 75;
			this.label6.Text = "请求间隔：";
			// 
			// panel1
			// 
			this.panel1.Controls.Add(this.radioButton2);
			this.panel1.Controls.Add(this.radioButton1);
			this.panel1.Location = new System.Drawing.Point(0, 56);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(191, 26);
			this.panel1.TabIndex = 81;
			// 
			// radioButton2
			// 
			this.radioButton2.AutoSize = true;
			this.radioButton2.Location = new System.Drawing.Point(71, 2);
			this.radioButton2.Name = "radioButton2";
			this.radioButton2.Size = new System.Drawing.Size(74, 21);
			this.radioButton2.TabIndex = 1;
			this.radioButton2.Text = "指定次数";
			this.radioButton2.UseVisualStyleBackColor = true;
			// 
			// radioButton1
			// 
			this.radioButton1.AutoSize = true;
			this.radioButton1.Checked = true;
			this.radioButton1.Location = new System.Drawing.Point(5, 2);
			this.radioButton1.Name = "radioButton1";
			this.radioButton1.Size = new System.Drawing.Size(62, 21);
			this.radioButton1.TabIndex = 0;
			this.radioButton1.TabStop = true;
			this.radioButton1.Text = "无限次";
			this.radioButton1.UseVisualStyleBackColor = true;
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(4, 88);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(68, 17);
			this.label1.TabIndex = 82;
			this.label1.Text = "附加条件：";
			// 
			// textBox_condition
			// 
			this.textBox_condition.Location = new System.Drawing.Point(71, 85);
			this.textBox_condition.Name = "textBox_condition";
			this.textBox_condition.Size = new System.Drawing.Size(200, 23);
			this.textBox_condition.TabIndex = 83;
			// 
			// comboBox_condition_type
			// 
			this.comboBox_condition_type.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_condition_type.FormattingEnabled = true;
			this.comboBox_condition_type.Items.AddRange(new object[] {
            "不使用附加条件",
            "上升沿有效",
            "下降沿有效",
            "满足时持续有效"});
			this.comboBox_condition_type.Location = new System.Drawing.Point(277, 84);
			this.comboBox_condition_type.Name = "comboBox_condition_type";
			this.comboBox_condition_type.Size = new System.Drawing.Size(122, 25);
			this.comboBox_condition_type.TabIndex = 84;
			// 
			// edgeLabel3
			// 
			this.edgeLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.edgeLabel3.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel3.ForeColor = System.Drawing.Color.White;
			this.edgeLabel3.Location = new System.Drawing.Point(3, 2);
			this.edgeLabel3.Name = "edgeLabel3";
			this.edgeLabel3.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel3.Size = new System.Drawing.Size(397, 22);
			this.edgeLabel3.TabIndex = 78;
			this.edgeLabel3.Text = "请求次数周期";
			this.edgeLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// RequestCycleControl
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.AliceBlue;
			this.Controls.Add(this.comboBox_condition_type);
			this.Controls.Add(this.textBox_condition);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.panel1);
			this.Controls.Add(this.textBox5);
			this.Controls.Add(this.label8);
			this.Controls.Add(this.edgeLabel3);
			this.Controls.Add(this.numericUpDown1);
			this.Controls.Add(this.label7);
			this.Controls.Add(this.label6);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "RequestCycleControl";
			this.Size = new System.Drawing.Size(402, 111);
			this.Load += new System.EventHandler(this.RequestDataAndCycleControl_Load);
			this.Paint += new System.Windows.Forms.PaintEventHandler(this.RequestDataAndCycleControl_Paint);
			((System.ComponentModel.ISupportInitialize)(this.numericUpDown1)).EndInit();
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion
		private System.Windows.Forms.TextBox textBox5;
		private System.Windows.Forms.Label label8;
		private EdgeLabel edgeLabel3;
		private System.Windows.Forms.NumericUpDown numericUpDown1;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.RadioButton radioButton2;
		private System.Windows.Forms.RadioButton radioButton1;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TextBox textBox_condition;
		private System.Windows.Forms.ComboBox comboBox_condition_type;
	}
}
