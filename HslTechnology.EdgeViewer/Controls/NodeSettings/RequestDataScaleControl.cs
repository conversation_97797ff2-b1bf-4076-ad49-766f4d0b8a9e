using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node.Converter;
using HslTechnology.Edge.Node.Request;
using HslCommunication.BasicFramework;
using HslCommunication;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;

namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	public partial class RequestDataScaleControl : UserControl
	{
		public RequestDataScaleControl( )
		{
			InitializeComponent( );

			typeConverter = new TransformDecimalTypeConverter( );
			comboBox_type.DataSource = SoftBasic.GetEnumValues<TransformDecimalType>( ).Select(
				m => typeConverter.ConvertTo( null, null, m, null ) ).ToArray( );
			checkBox_hasPoint.CheckedChanged += CheckBox1_CheckedChanged;

			radioButton_Express.CheckedChanged += RadioButton_Express_CheckedChanged;
		}

		private void RadioButton_Express_CheckedChanged( object sender, EventArgs e )
		{
			if (radioButton_Express.Checked)
			{
				textBox_multi.Size = new Size( this.Width - 50, 23 );
				textBox_multi.Text = "2 * x + 5";
			}
			else
			{
				textBox_multi.Size = new Size( 60, 23 );
				textBox_multi.Text = "1";
			}
		}

		private void RequestDataScaleControl_Paint( object sender, PaintEventArgs e )
		{
			e.Graphics.DrawLine( Pens.LightGray, 2, 91, Width - 2, 91 );
		}

		private void RequestDataScaleControl_Load( object sender, EventArgs e )
		{
			// 初始化
			textBox_number.Visible = false;
			label1.Visible = false;
			comboBox_type.Visible = false;


		}

		private void CheckBox1_CheckedChanged( object sender, EventArgs e )
		{
			// 点击无变换
			if (!checkBox_hasPoint.Checked)
			{
				textBox_number.Visible = false;
				label1.Visible = false;
				comboBox_type.Visible = false;
			}
			else
			{
				textBox_number.Text = "0";
				textBox_number.Visible = true;
				label1.Visible = true;
				comboBox_type.Visible = true;
			}
		}

		private TransformDecimalTypeConverter typeConverter;




		/// <summary>
		/// 显示标量变换节点的基本信息
		/// </summary>
		/// <param name="transform">变换的对象</param>
		public void ShowScalarTransform( IScalarTransform transform )
		{
			if (transform.TransformType == RegularHelper.TransformType_Value)
				radioButton_Trans.Checked = true;
			else if (transform.TransformType == RegularHelper.TransformType_Not)
				radioButton_not.Checked = true;
			else if (transform.TransformType == RegularHelper.TransformType_Express)
				radioButton_Express.Checked = true;
			else
				radioButton_None.Checked = true;

			if (transform.TransformType == RegularHelper.TransformType_Value)
			{
				textBox_multi.Text = transform.TransformMultiply.ToString( );
				textBox_addition.Text = transform.TransformAddition.ToString( );
			}
			else if (transform.TransformType == RegularHelper.TransformType_Express)
			{
				textBox_multi.Text = transform.TransformExpress;
			}

			if (transform.TransformDecimal < 0)
				checkBox_hasPoint.Checked = false;
			else
				checkBox_hasPoint.Checked = true;
			textBox_number.Text = transform.TransformDecimal.ToString( );
			comboBox_type.SelectedItem = typeConverter.ConvertTo( null, null, transform.TransformDecimalType, null );
		}

		/// <summary>
		/// 获取标量变换节点的信息
		/// </summary>
		/// <param name="transform">变换的对象信息</param>
		/// <returns>是否获取或正确的数据</returns>
		public OperateResult GetScalarTransform( IScalarTransform transform )
		{
			try
			{
				int transformType = RegularHelper.TransformType_None;
				if (radioButton_None.Checked)
					transformType = RegularHelper.TransformType_None;
				else if (radioButton_Trans.Checked)
					transformType = RegularHelper.TransformType_Value;
				else if (radioButton_Express.Checked)
					transformType = RegularHelper.TransformType_Express;
				else
					transformType = RegularHelper.TransformType_Not;

				if (transformType == RegularHelper.TransformType_Express)
				{
					if (string.IsNullOrEmpty( textBox_multi.Text )) return new OperateResult( "当前选择了脚本，但是脚本内容为空！" );
					transform.TransformExpress = textBox_multi.Text;
				}
				else
				{
					if (!double.TryParse( textBox_multi.Text, out double multipy )) return new OperateResult( "倍数信息填写错误，无法转double" );
					if (!double.TryParse( textBox_addition.Text, out double addtion )) return new OperateResult( "加法信息填写错误，无法转double" );
					transform.TransformMultiply = multipy;
					transform.TransformAddition = addtion;
				}

				int decimalCount = 0;
				if (checkBox_hasPoint.Checked && !int.TryParse( textBox_number.Text, out decimalCount )) return new OperateResult( "小数点位数填写错误，无法转int" );
				if (radioButton_not.Checked && transform.DataTypeCode != RegularNodeTypeItem.Bool.Text) return new OperateResult( "取反的数据变化只能是bool类型变量有效" );
				transform.TransformType = transformType;
				if (!checkBox_hasPoint.Checked)
				{
					transform.TransformDecimal = -1;
				}
				else
				{
					transform.TransformDecimal = decimalCount;
					transform.TransformDecimalType = (TransformDecimalType)typeConverter.ConvertFrom( comboBox_type.SelectedItem );
				}

				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				return new OperateResult( ex.Message );
			}
		}



	}
}
