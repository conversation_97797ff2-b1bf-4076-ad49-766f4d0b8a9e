
namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	partial class RequestDataScaleControl
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.textBox_number = new System.Windows.Forms.TextBox();
			this.label1 = new System.Windows.Forms.Label();
			this.checkBox_hasPoint = new System.Windows.Forms.CheckBox();
			this.textBox_addition = new System.Windows.Forms.TextBox();
			this.label10 = new System.Windows.Forms.Label();
			this.textBox_multi = new System.Windows.Forms.TextBox();
			this.label9 = new System.Windows.Forms.Label();
			this.panel2 = new System.Windows.Forms.Panel();
			this.radioButton_not = new System.Windows.Forms.RadioButton();
			this.radioButton_Trans = new System.Windows.Forms.RadioButton();
			this.radioButton_None = new System.Windows.Forms.RadioButton();
			this.comboBox_type = new System.Windows.Forms.ComboBox();
			this.radioButton_Express = new System.Windows.Forms.RadioButton();
			this.edgeLabel4 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.panel2.SuspendLayout();
			this.SuspendLayout();
			// 
			// textBox_number
			// 
			this.textBox_number.Location = new System.Drawing.Point(135, 96);
			this.textBox_number.Name = "textBox_number";
			this.textBox_number.Size = new System.Drawing.Size(68, 23);
			this.textBox_number.TabIndex = 99;
			this.textBox_number.Text = "1";
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(211, 101);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(20, 17);
			this.label1.TabIndex = 98;
			this.label1.Text = "位";
			// 
			// checkBox_hasPoint
			// 
			this.checkBox_hasPoint.AutoSize = true;
			this.checkBox_hasPoint.Location = new System.Drawing.Point(3, 99);
			this.checkBox_hasPoint.Name = "checkBox_hasPoint";
			this.checkBox_hasPoint.Size = new System.Drawing.Size(123, 21);
			this.checkBox_hasPoint.TabIndex = 97;
			this.checkBox_hasPoint.Text = "强制小数点位数？";
			this.checkBox_hasPoint.UseVisualStyleBackColor = true;
			// 
			// textBox_addition
			// 
			this.textBox_addition.Location = new System.Drawing.Point(201, 62);
			this.textBox_addition.Name = "textBox_addition";
			this.textBox_addition.Size = new System.Drawing.Size(60, 23);
			this.textBox_addition.TabIndex = 96;
			this.textBox_addition.Text = "0";
			// 
			// label10
			// 
			this.label10.AutoSize = true;
			this.label10.Location = new System.Drawing.Point(107, 65);
			this.label10.Name = "label10";
			this.label10.Size = new System.Drawing.Size(76, 17);
			this.label10.TabIndex = 95;
			this.label10.Text = "*  [value] + ";
			// 
			// textBox_multi
			// 
			this.textBox_multi.Location = new System.Drawing.Point(41, 62);
			this.textBox_multi.Name = "textBox_multi";
			this.textBox_multi.Size = new System.Drawing.Size(60, 23);
			this.textBox_multi.TabIndex = 94;
			this.textBox_multi.Text = "1";
			// 
			// label9
			// 
			this.label9.AutoSize = true;
			this.label9.Location = new System.Drawing.Point(4, 64);
			this.label9.Name = "label9";
			this.label9.Size = new System.Drawing.Size(31, 17);
			this.label9.TabIndex = 93;
			this.label9.Text = "y = ";
			// 
			// panel2
			// 
			this.panel2.Controls.Add(this.radioButton_Express);
			this.panel2.Controls.Add(this.radioButton_not);
			this.panel2.Controls.Add(this.radioButton_Trans);
			this.panel2.Controls.Add(this.radioButton_None);
			this.panel2.Location = new System.Drawing.Point(-2, 27);
			this.panel2.Name = "panel2";
			this.panel2.Size = new System.Drawing.Size(399, 32);
			this.panel2.TabIndex = 92;
			// 
			// radioButton_not
			// 
			this.radioButton_not.AutoSize = true;
			this.radioButton_not.Location = new System.Drawing.Point(219, 4);
			this.radioButton_not.Name = "radioButton_not";
			this.radioButton_not.Size = new System.Drawing.Size(85, 21);
			this.radioButton_not.TabIndex = 2;
			this.radioButton_not.TabStop = true;
			this.radioButton_not.Text = "取反(bool)";
			this.radioButton_not.UseVisualStyleBackColor = true;
			// 
			// radioButton_Trans
			// 
			this.radioButton_Trans.AutoSize = true;
			this.radioButton_Trans.Location = new System.Drawing.Point(72, 4);
			this.radioButton_Trans.Name = "radioButton_Trans";
			this.radioButton_Trans.Size = new System.Drawing.Size(139, 21);
			this.radioButton_Trans.TabIndex = 1;
			this.radioButton_Trans.Text = "值变换 (结果double)";
			this.radioButton_Trans.UseVisualStyleBackColor = true;
			// 
			// radioButton_None
			// 
			this.radioButton_None.AutoSize = true;
			this.radioButton_None.Checked = true;
			this.radioButton_None.Location = new System.Drawing.Point(5, 4);
			this.radioButton_None.Name = "radioButton_None";
			this.radioButton_None.Size = new System.Drawing.Size(62, 21);
			this.radioButton_None.TabIndex = 0;
			this.radioButton_None.TabStop = true;
			this.radioButton_None.Text = "无变换";
			this.radioButton_None.UseVisualStyleBackColor = true;
			// 
			// comboBox_type
			// 
			this.comboBox_type.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_type.FormattingEnabled = true;
			this.comboBox_type.Location = new System.Drawing.Point(265, 97);
			this.comboBox_type.Name = "comboBox_type";
			this.comboBox_type.Size = new System.Drawing.Size(132, 25);
			this.comboBox_type.TabIndex = 100;
			// 
			// radioButton_Express
			// 
			this.radioButton_Express.AutoSize = true;
			this.radioButton_Express.Location = new System.Drawing.Point(307, 4);
			this.radioButton_Express.Name = "radioButton_Express";
			this.radioButton_Express.Size = new System.Drawing.Size(94, 21);
			this.radioButton_Express.TabIndex = 3;
			this.radioButton_Express.TabStop = true;
			this.radioButton_Express.Text = "表达式(只读)";
			this.radioButton_Express.UseVisualStyleBackColor = true;
			// 
			// edgeLabel4
			// 
			this.edgeLabel4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.edgeLabel4.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel4.ForeColor = System.Drawing.Color.White;
			this.edgeLabel4.Location = new System.Drawing.Point(1, 1);
			this.edgeLabel4.Name = "edgeLabel4";
			this.edgeLabel4.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel4.Size = new System.Drawing.Size(397, 22);
			this.edgeLabel4.TabIndex = 91;
			this.edgeLabel4.Text = "数据变换";
			this.edgeLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// RequestDataScaleControl
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.AliceBlue;
			this.Controls.Add(this.textBox_multi);
			this.Controls.Add(this.comboBox_type);
			this.Controls.Add(this.textBox_number);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.checkBox_hasPoint);
			this.Controls.Add(this.textBox_addition);
			this.Controls.Add(this.label10);
			this.Controls.Add(this.label9);
			this.Controls.Add(this.panel2);
			this.Controls.Add(this.edgeLabel4);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.MinimumSize = new System.Drawing.Size(400, 127);
			this.Name = "RequestDataScaleControl";
			this.Size = new System.Drawing.Size(400, 127);
			this.Load += new System.EventHandler(this.RequestDataScaleControl_Load);
			this.Paint += new System.Windows.Forms.PaintEventHandler(this.RequestDataScaleControl_Paint);
			this.panel2.ResumeLayout(false);
			this.panel2.PerformLayout();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.TextBox textBox_number;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.CheckBox checkBox_hasPoint;
		private System.Windows.Forms.TextBox textBox_addition;
		private System.Windows.Forms.Label label10;
		private System.Windows.Forms.TextBox textBox_multi;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.Panel panel2;
		private System.Windows.Forms.RadioButton radioButton_Trans;
		private System.Windows.Forms.RadioButton radioButton_None;
		private EdgeLabel edgeLabel4;
		private System.Windows.Forms.ComboBox comboBox_type;
		private System.Windows.Forms.RadioButton radioButton_not;
		private System.Windows.Forms.RadioButton radioButton_Express;
	}
}
