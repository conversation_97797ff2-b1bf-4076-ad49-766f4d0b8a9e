using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using HslCommunication;
using HslTechnology.Edge.Device.PLCDevice;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Node;

namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	public partial class SourceDataViewControl : UserControl
	{
		public SourceDataViewControl( )
		{
			InitializeComponent( );

			stringFormatCenter.LineAlignment = StringAlignment.Center;
			stringFormatCenter.Alignment     = StringAlignment.Center;

			stringFormatDown.LineAlignment   = StringAlignment.Far;
			stringFormatDown.Alignment       = StringAlignment.Center;

			stringFormatTop.LineAlignment    = StringAlignment.Near;
			stringFormatTop.Alignment        = StringAlignment.Center;

			font_6 = new Font( Font.FontFamily, 6f );
			font_7 = new Font( Font.FontFamily, 7f );
			font_9 = new Font( Font.FontFamily, 9f );
		}

		#region Public Method

		public void UpdateDataView( 
			DeviceNode deviceNode,
			List<RegularScalarNode> regularNodes, 
			List<RegularStructNode> regularStructs, 
			string selectedRegularItemName, 
			int byteLengthMax = -1 )
		{
			this.dataBuffer = null;
			this.checkBox2.Checked = false;

			this.deviceNode              = deviceNode;
			this.regularNodes            = regularNodes;
			this.selectedRegularItemName = selectedRegularItemName;
			this.regularStructNodes      = regularStructs;
			this.byteLengthMax           = byteLengthMax;

			this.checkBox2.Visible = this.deviceNode != null;
			UpdateRenderBitmap( );
		}

		/// <summary>
		/// 设置当前的起始地址信息
		/// </summary>
		/// <param name="address">起始地址</param>
		public void UpdateDeviceAddress( string address )
		{
			this.deviceAddressStarted = address;
		}

		#endregion

		#region Render Bitmap

		private void UpdateRenderBitmap( )
		{
			if (regularNodes == null) return;
			pictureBox1.Image?.Dispose( );
			pictureBox1.Image = GetRenderInfo( regularNodes, regularStructNodes, selectedRegularItemName );
			pictureBox2.Image?.Dispose( );
			pictureBox2.Image = GetRenderBoolInfo( regularNodes, regularStructNodes, selectedRegularItemName );
		}

		/// <summary>
		/// 获取指定数值的，针对count值的整数倍，取上限，如果是0，返回1
		/// </summary>
		/// <param name="value">给定的数值</param>
		/// <param name="count">准备整数倍的值</param>
		/// <returns>倍数关系</returns>
		private int GetNumberByUplimit( int value, int count )
		{
			if (value == 0) return 1;
			if (value % count == 0)
			{
				return value / count;
			}
			else
			{
				return value / count + 1;
			}
		}

		/// <summary>
		/// 根据最左上角的起始坐标，每行的字节数，来获取当前字节索引所在的中心点坐标
		/// </summary>
		/// <param name="paint_x">当前图形的 x 坐标</param>
		/// <param name="paint_y">当前图形的 y 坐标</param>
		/// <param name="every_line_count">每行显示的字节总数</param>
		/// <param name="index">指定的字节索引</param>
		/// <returns>指定索引的中心点坐标</returns>
		private Point CalculatePointWithByteIndex( int every_line_count, int index )
		{
			return new Point( 
				paint_x + index % every_line_count * everyByteOccupy + everyByteWidth / 2,
				paint_y + 17 + index / every_line_count * everyBytesLineHeight + everyByteWidth / 2 );
		}

		private int GetPaintYFormDataByteLineStyle( DataByteLineStyle byteLineStyle )
		{
			if (byteLineStyle == DataByteLineStyle.Up) return - everyByteWidth / 2 - 4;
			if (byteLineStyle == DataByteLineStyle.Down1) return everyByteWidth / 2 + 4;
			if (byteLineStyle == DataByteLineStyle.Down2) return everyByteWidth / 2 + 13 + 4;
			if (byteLineStyle == DataByteLineStyle.Down3) return everyByteWidth / 2 + 30 + 4;
			return 0;
		}

		private Font GetFontByteInteger( int value )
		{
			if (value < 100) return font_9;
			if (value < 1000) return font_7;
			return font_6;
		}

		private Brush GetEveryByteRectangleBackbrush( int byteIndex, Brush background )
		{
			if (byteLengthMax < 0)
			{
				// 没有最大边界的情况
				return background;
			}
			else
			{
				// 存在最大边界
				return byteIndex >= byteLengthMax ? Brushes.OrangeRed : background;
			}
		}

		private Brush GetEveryByteRectangleForebrush( int byteIndex, Brush foreground )
		{
			if (byteLengthMax < 0)
			{
				return foreground;
			}
			else
			{
				// 存在最大边界
				return byteIndex >= byteLengthMax ? Brushes.Yellow : foreground;
			}
		}

		private void PaintEveryByteRectangle(Graphics g, int every_line_count, int byteIndex, int length, Pen border, Brush background, Brush foreground, int relativeIndex, bool byteImage = true, int boolIndex = 0 )
		{
			for (int i = byteIndex; i < byteIndex + length; i++)
			{
				Point pointCenter = CalculatePointWithByteIndex( every_line_count, i );

				Rectangle rec = new Rectangle( pointCenter.X - everyByteWidth / 2, pointCenter.Y - everyByteWidth / 2, everyByteWidth, everyByteWidth );
				g.FillRectangle( (byteImage ? GetEveryByteRectangleBackbrush( i, background ) : background), rec );

				g.DrawRectangle( border, rec );

				int actualIndex = i + relativeIndex;
				string stringIndex = actualIndex.ToString( );
				if (isDataPreview && dataBuffer != null )
				{
					if (actualIndex < dataBuffer.Length)
					{
						if (byteImage)
						{
							if (radioButton_hex.Checked)
								stringIndex = dataBuffer[actualIndex].ToString( "X2" );
							else
							{
								//if (dataBuffer[actualIndex] == 0x20)
								//	stringIndex = "'" + Encoding.ASCII.GetString( dataBuffer, actualIndex, 1 ) + "'";
								//else 
								if (dataBuffer[actualIndex] >= 0x20 && dataBuffer[actualIndex] < 0x7f)
									stringIndex = "'" + Encoding.ASCII.GetString( dataBuffer, actualIndex, 1 ) + "'";
								else
									stringIndex = dataBuffer[actualIndex].ToString( "X2" );
							}
						}
					}
				}

				g.DrawString( stringIndex, GetFontByteInteger( actualIndex ), (byteImage ? GetEveryByteRectangleForebrush( i, foreground) : foreground),
					new Rectangle( pointCenter.X - everyByteWidth / 2 - 2, pointCenter.Y - everyByteWidth / 2, everyByteWidth + 5, everyByteWidth ), stringFormatCenter );
			}
		}
		private void PaintEveryByteRectangle( Graphics g, int every_line_count, int byteIndex, int length, Color backColor, int relativeIndex, bool byteImage = true, int boolIndex = 0 )
		{
			using (SolidBrush brush = new SolidBrush( backColor ))
				PaintEveryByteRectangle( g, every_line_count, byteIndex, length, Pens.Gray, brush, Brushes.Black, relativeIndex, byteImage, boolIndex );
		}

		private void PaintBackgroundColor( Graphics g, int every_line_count, int byteIndex, int length )
		{
			Point point1       = CalculatePointWithByteIndex( every_line_count, byteIndex );                   // 起始的坐标的中心点
			Point point2       = CalculatePointWithByteIndex( every_line_count, byteIndex + length - 1 );      // 结束的坐标的中心点
			Point point1_right = CalculatePointWithByteIndex( every_line_count, every_line_count - 1 );        // 第一行最右侧坐标的中心点
			Point point2_left  = CalculatePointWithByteIndex( every_line_count, 0 );                           // 最后一行最左侧的坐标的中心点

			using (Brush brush = new SolidBrush( selectBackgroundColor ))
				for (int y = point1.Y; y <= point2.Y; y += everyBytesLineHeight)
				{
					// 左右两边的X点计算
					int x1 = y == point1.Y ? point1.X - everyByteWidth / 2 - 1 : point2_left.X - everyByteWidth / 2 - 3;
					int x2 = y == point2.Y ? point2.X + everyByteWidth / 2 + 1 : point1_right.X + everyByteWidth / 2 + 3;

					// 绘制线
					Rectangle rectangle = new Rectangle( x1, y - everyByteWidth / 2 - 20, x2 - x1, everyBytesLineHeight );
					g.FillRectangle( brush, rectangle );
					g.DrawRectangle( Pens.LightGray, rectangle );
				}
		}

		private Pen GetPenFromTopOfVerticalLine( int topOfVerticalLine, bool dash = false )
		{
			if (dash)
			{
				Pen pen = topOfVerticalLine < 0 ? new Pen( Color.Chocolate ) : new Pen( Color.DimGray );
				pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Custom;
				pen.DashPattern = new float[] { 3, 3 };
				return pen;
			}
			else
			{
				return topOfVerticalLine < 0 ? new Pen( Color.Chocolate ) : new Pen( Color.DimGray );
			}
		}

		private int GetPointXByDataByteLineStyle( DataByteLineStyle dataByteLine, Point point, bool isLeft )
		{
			if (dataByteLine == DataByteLineStyle.Down3)
				return point.X + (isLeft ? -everyByteWidth / 4 : everyByteWidth / 4);
			else
				return point.X;
		}

		private void PaintDataByteLineAndInfomation( Graphics g, int every_line_count, int byteIndex, int length, string info, Brush fontBrush, DataByteLineStyle byteLineStyle, int relativeIndex = 0, bool dash = false )
		{
			Point point1 = CalculatePointWithByteIndex( every_line_count, byteIndex - relativeIndex );                   // 起始的坐标的中心点
			Point point2 = CalculatePointWithByteIndex( every_line_count, byteIndex + length - 1 - relativeIndex );      // 结束的坐标的中心点
			Point point1_right = CalculatePointWithByteIndex( every_line_count, every_line_count - 1 - relativeIndex );  // 第一行最右侧坐标的中心点
			Point point2_left  = CalculatePointWithByteIndex( every_line_count, 0 - relativeIndex );                     // 最后一行最左侧的坐标的中心点

			// 绘制小竖条
			int topOfVerticalLine = GetPaintYFormDataByteLineStyle( byteLineStyle );
			Pen linePen = GetPenFromTopOfVerticalLine( topOfVerticalLine, dash );
			g.DrawLine( linePen,
				new Point(
					GetPointXByDataByteLineStyle( byteLineStyle , point1 , true), 
					topOfVerticalLine < 0 ? point1.Y - everyByteWidth / 2 : point1.Y + everyByteWidth / 2 ), 
				new Point(
					GetPointXByDataByteLineStyle( byteLineStyle, point1, true ),
					point1.Y + topOfVerticalLine ) );
			g.DrawLine( linePen,
				new Point(
					GetPointXByDataByteLineStyle( byteLineStyle, point2, false ),
					topOfVerticalLine < 0 ? point2.Y - everyByteWidth / 2 : point2.Y + everyByteWidth / 2 ),
				new Point(
					GetPointXByDataByteLineStyle( byteLineStyle, point2, false ),
					point2.Y + topOfVerticalLine ) );

			// 先绘制选中时的状态效果
			List<int> listY = new List<int>( );
			for (int y = point1.Y; y <= point2.Y; y += everyBytesLineHeight)
			{
				// 左右两边的X点计算
				int x1 = y == point1.Y ? point1.X : point2_left.X - everyByteWidth / 2 - 3;
				int x2 = y == point2.Y ? point2.X : point1_right.X + everyByteWidth / 2 + 3;

				// 绘制线
				g.DrawLine( linePen,
					new Point( GetPointXByDataByteLineStyle( byteLineStyle, new Point( x1, 0 ), true  ), y + topOfVerticalLine ),
					new Point( GetPointXByDataByteLineStyle( byteLineStyle, new Point( x2, 0 ), false ), y + topOfVerticalLine ) );
				listY.Add( x2 - x1 );     // 添加线条宽度，方便寻找线条最宽的表示
			}

			// 寻找需要绘制的文本在哪一行
			int maxIndex = listY.LastIndexOf( listY.Max( ) );
			int row = 0;
			for (int y = point1.Y; y <= point2.Y; y += everyBytesLineHeight)
			{
				if(row == maxIndex)
				{
					// 左右两边的X点计算
					int x1 = y == point1.Y ? point1.X : point2_left.X - everyByteWidth / 2 - 3;
					int x2 = y == point2.Y ? point2.X : point1_right.X + everyByteWidth / 2 + 3;
					Rectangle textRectangle = new Rectangle( x1 - 100,
						topOfVerticalLine < 0 ? y + topOfVerticalLine - 20 : y + topOfVerticalLine,
						x2 - x1 + 200,
						20 );
					if (isShowText || topOfVerticalLine > 0) g.DrawString( info,
						Font, fontBrush, textRectangle,
						topOfVerticalLine < 0 ? stringFormatDown : stringFormatTop );
					break;
				}
				row++;
			}
			linePen.Dispose( );
		}

		private void PaintDataBoolLineAndInfomation( Graphics g, int every_line_count, int boolIndex, int length, string info, Brush fontBrush, DataByteLineStyle byteLineStyle, bool select )
		{
			if (length < 0) length = 1;
			int byteEndIndex = (boolIndex + length - 1) / 8;

			for (int i = boolIndex; i < boolIndex + length; i++)
			{
				int byteIndex = i / 8;
				int bitIndex  = i % 8;
				Point point = CalculatePointWithByteIndex( every_line_count, byteIndex );

				int x1 = bitIndex * everyByteWidth / 8 + 1 + point.X - everyByteWidth / 2;
				Pen boolPen = select ? Pens.Red : Pens.LightGray;
				if (isDataPreview && dataBuffer != null && deviceNode != null)
					boolPen = dataBuffer.GetBoolValue( byteIndex, bitIndex ) ? Pens.DarkGreen : Pens.LightGray;
				g.DrawLine( boolPen, x1, point.Y - everyByteWidth / 2, x1, point.Y - everyByteWidth / 2 - 4 );


				if (!this.markedByteIndex.Contains( byteIndex ))
				{
					this.markedByteIndex.Add( byteIndex );
					PaintEveryByteRectangle( g, every_line_count, byteIndex, 1, RegularNodeTypeItem.Bool.BackColor, 0 );
					if (isShowText) g.DrawString( "bit", Font, fontBrush,
						 new Rectangle( point.X - 100, point.Y - everyByteWidth / 2 - 4 - 20, 200, 20 ), stringFormatDown );
				}
			}
			// 横向跨度大于2个字节才显示其名称，这个考虑一下
		}

		private void PaintIndexHead( Graphics g, int every_line_count, int maxRectangle, int relativeIndex = 0 )
		{
			int line_count = GetNumberByUplimit( maxRectangle, every_line_count );
			int index = 0;
			g.DrawLine( Pens.Gray, paint_x - 5, 0, paint_x - 5, line_count * everyBytesLineHeight + 5 );

			for (int i = 0; i < line_count; i++)
			{
				int everyCount = Math.Min( maxRectangle - index, every_line_count );
				g.DrawString( $"[{index + relativeIndex:D3} - {index + relativeIndex + everyCount - 1:D3}]",
					( index + relativeIndex ) < 10000 ? Font : font_7, Brushes.DimGray, new Point( 2, paint_y + everyByteWidth + everyBytesLineHeight * i ) );
				index += everyCount;
			}

			if (!string.IsNullOrEmpty( this.deviceAddressStarted ))
			{
				Rectangle rectangle = new Rectangle( 2, 0, paint_x - 7, 23 );
				g.DrawString( $"{this.deviceAddressStarted}", Font, Brushes.DarkRed, rectangle, HslControls.HslHelper.StringFormatLeft );
			}
		}

		private string GetRegularScalarTopText( RegularScalarNode regularScalar, RegularNodeTypeItem regularNodeTypeItem, int byteIndex, bool ascii )
		{
			if (isDataPreview && dataBuffer != null && deviceNode != null)
			{
				// 关键是设备的byteTransform
				if (deviceCoreBase == null)
				{
					OperateResult<DeviceCoreBase> deviceTmp = HslTechnology.Edge.Reflection.EdgeReflectionHelper.GetDeviceCoreFrom( deviceNode.ToXmlElement( ) );
					if (!deviceTmp.IsSuccess) return "NULL";

					try
					{
						deviceTmp.Content.IniDevice( deviceNode.ToXmlElement( ), null );
					}
					catch( Exception ex )
					{
						MessageBox.Show( "IniDevice failed: " + ex.Message );
						return "NULL";
					}

					this.deviceCoreBase = deviceTmp.Content;
				}

				if (deviceCoreBase == null) return "NULL";
				if (deviceCoreBase is DeviceCore device)
				{
					// 从Byte中提取数据信息
					if (device.ByteTransform == null) return "NULL";
					if (byteIndex + regularScalar.Index >= dataBuffer.Length) return "NULL";
					return regularScalar.GetValue( dataBuffer, byteIndex, device.ByteTransform, true, ascii: ascii ).ToString( );
				}
				else
				{
					return "NULL";
				}
			}
			else
			{
				if (regularNodeTypeItem.Text == RegularNodeTypeItem.Byte.Text) return "byt";
				if (regularNodeTypeItem.Text == RegularNodeTypeItem.BoolOfByte.Text) return "bit";   // 显示 boolofByte太长了
				return regularNodeTypeItem.Text;
			}
		}

		private void DrawRegularScalarNode( Graphics g, int every_line_count, string selectedRegular, RegularScalarNode regularScalar, int actualIndex = 0, bool onlyBack = false )
		{
			RegularNodeTypeItem regularNodeTypeItem = RegularNodeTypeItem.GetDataPraseItemByCode( regularScalar.DataTypeCode );
			//if (regularScalar.DataTypeCode == RegularNodeTypeItem.Bool.Text) return;

			bool isSelected = string.IsNullOrEmpty( selectedRegular ) ? false : selectedRegular == regularScalar.Name;

			bool ascii = false;
			if (this.deviceNode is INodeAsciiFormate asciiFormate) ascii = asciiFormate.UseAsciiFormate;
			int start = regularScalar.GetStartedByteIndex( ) + actualIndex;
			int length = regularScalar.GetLengthByte( ascii ) ;

			// 如果是bool的解析配置，就会覆盖掉之前绘制的内容
			if (isSelected && onlyBack) PaintBackgroundColor( g, every_line_count, start, length );

			if (!onlyBack)
			{
				// 绘制下面的情况
				this.parityMark++;
				if (regularScalar.DataTypeCode != RegularNodeTypeItem.Bool.Text)
					PaintDataByteLineAndInfomation( g, every_line_count, start, length, regularScalar.GetSourceDisplayName( ), Brushes.Blue, this.parityMark % 2 == 1 ? DataByteLineStyle.Down1 : DataByteLineStyle.Down2 );

				// 绘制上面的数据
				if (regularScalar.DataTypeCode == RegularNodeTypeItem.Bool.Text)
				{
					PaintDataBoolLineAndInfomation( g, every_line_count, regularScalar.Index + actualIndex * 8, regularScalar.Length, regularScalar.GetSourceDisplayName( ), Brushes.Green, DataByteLineStyle.Up, isSelected );
				}
				else
				{
					// 判断数据类型的长度是否是变长，在变长的情况下，视为一个变量信息
					int len = regularNodeTypeItem.GetLength( ascii );
					if (RegularNodeTypeItem.IsDataTypeString( regularScalar.DataTypeCode ))
					{
						len = regularScalar.StringLength;
					}
					if (len != 0)
					{
						int tmp = start;
						for (int j = 0; j < length / len; j++)
						{
							PaintDataByteLineAndInfomation( g, every_line_count, tmp, len,
								GetRegularScalarTopText( regularScalar, regularNodeTypeItem, actualIndex + j * len, ascii ), Brushes.DarkGreen, DataByteLineStyle.Up );
							tmp += len;
						}
					}
					else
					{
						PaintDataByteLineAndInfomation( g, every_line_count, start, length,
							GetRegularScalarTopText( regularScalar, regularNodeTypeItem, actualIndex, ascii ), Brushes.DarkGreen, DataByteLineStyle.Up );
					}
				}

				PaintEveryByteRectangle( g, every_line_count, start, length, regularNodeTypeItem.BackColor, 0 );
			}
		}

		private void DrawRegularScalarNode( Graphics g, int every_line_count, string selectedRegular, RegularStructNode regularStruct, bool onlyBack = false )
		{
			bool isSelected = string.IsNullOrEmpty( selectedRegular ) ? false : selectedRegular == regularStruct.Name;
			if (isSelected && onlyBack)
			{
				PaintBackgroundColor( g, every_line_count, regularStruct.StructIndex, regularStruct.GetLengthByte( ) );
				PaintEveryByteRectangle( g, every_line_count, regularStruct.StructIndex, regularStruct.GetLengthByte( ), Pens.Gray, Brushes.Transparent, Brushes.Black, 0 );
			}

			if (regularStruct.RegularStructItem != null)
			{
				// 绘制多个结构体
				int structLen = regularStruct.ArrayLength < 0 ? 1 : regularStruct.ArrayLength;
				for (int i = 0; i < structLen; i++)
				{
					// 绘制结构体的名称
					if(!onlyBack) PaintDataByteLineAndInfomation( g,
						every_line_count,
						regularStruct.StructIndex + i * regularStruct.RegularStructItem.StructLength,
						regularStruct.RegularStructItem.StructLength,
						regularStruct.GetSourceDisplayName( i ), Brushes.Purple, DataByteLineStyle.Down3, 0, regularStruct.StructParseType != Edge.Node.Core.ParseType.Struct ); // (regularStruct.ArrayLength < 0 && regularStruct.Unflod)

					for (int j = 0; j < regularStruct.RegularStructItem.RegularScalarNodes.Count; j++)
					{
						DrawRegularScalarNode( g, every_line_count, string.Empty,
							regularStruct.RegularStructItem.RegularScalarNodes[j],
							actualIndex: regularStruct.StructIndex + i * regularStruct.RegularStructItem.StructLength, onlyBack );
					}
				}
			}
		}


		private Bitmap GetRenderInfo( List<RegularScalarNode> regulars, List<RegularStructNode> regularStructs, string selectedRegular )
		{
			this.markedByteIndex.Clear( );
			this.parityMark = 0;                                                                   // 清除奇偶的标记
			regulars.Sort( );
			int max_byte = 0;                                                                      // 实际占位的最大长度信息
			int max_buffer = 0;                                                                    // 理论的最大长度信息，包括所有配置的节点位置

			max_byte = regulars.Count == 0 ? 0 : regulars.Max( m => m.GetMaxEndIndex( ) );
			if (regularStructs != null && regularStructs.Count > 0) max_byte = Math.Max( max_byte, regularStructs.Max( m => m.GetMaxEndIndex( ) ) );  // 先找到最大的占位符

			max_buffer = max_byte;
			if (isDataPreview && dataBuffer != null)
			{
				max_byte = dataBuffer.Length;
				if (byteLengthMax < 0 && max_byte > max_buffer) max_buffer = max_byte;
			}
			if (byteLengthMax < 0) max_buffer += 30;

			max_byte = byteLengthMax < 0 ? max_byte : Math.Max( byteLengthMax, max_byte );
			int every_line_count = (panel3.Width - 19 - 90) / everyByteOccupy;
			if (every_line_count < 10) every_line_count = 10;
			int line_count = GetNumberByUplimit( Math.Max( max_byte, max_buffer), every_line_count );         // 如果是动态的长度，多给30个字节的信息

			Bitmap bitmap = new Bitmap( panel3.Width - 19, line_count * everyBytesLineHeight + 5 );
			// if (max_byte == 0) return bitmap;
			Graphics g = Graphics.FromImage( bitmap );
			g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
			g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
			g.Clear( BackColor );

			PaintIndexHead( g, every_line_count, Math.Max( max_byte, max_buffer ) );
			PaintEveryByteRectangle( g, every_line_count, 0, max_byte, Pens.Gray, Brushes.Transparent, Brushes.Black, 0 );

			// 绘制最大边界
			if(byteLengthMax > 0)
			{
				Point point = CalculatePointWithByteIndex( every_line_count, byteLengthMax - 1 );                   // 起始的坐标的中心点
				g.DrawLine( Pens.Red,
					point.X + everyByteWidth / 2 + 2, point.Y - everyByteWidth / 2 - 24,
					point.X + everyByteWidth / 2 + 2, point.Y - everyByteWidth / 2 - 24 + everyBytesLineHeight );
			}
			else
			{
				// 没有最大边界的情况，且没有数据预览的情况，多绘制30个框框信息
				using(Pen pen = new Pen( Color.LightGray ))
				{
					pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Custom;
					pen.DashPattern = new float[] { 3, 3 };
					PaintEveryByteRectangle( g, every_line_count, max_byte, max_buffer - max_byte, pen, Brushes.Transparent, Brushes.LightGray, 0 );
				}
			}

			// 绘制所有的选择的标签的底色信息
			for (int i = 0; i < regulars.Count; i++)
			{
				DrawRegularScalarNode( g, every_line_count, selectedRegular, regulars[i], 0, true );
			}
			if (regularStructs != null)
			{
				for (int i = 0; i < regularStructs.Count; i++)
				{
					DrawRegularScalarNode( g, every_line_count, selectedRegular, regularStructs[i], true );
				}
			}

			// 最后绘制所有的配置的节点参数信息
			for (int i = 0; i < regulars.Count; i++)
			{
				DrawRegularScalarNode( g, every_line_count, selectedRegular, regulars[i], 0 );
			}
			if (regularStructs != null)
			{
				for (int i = 0; i < regularStructs.Count; i++)
				{
					DrawRegularScalarNode( g, every_line_count, selectedRegular, regularStructs[i] );
				}
			}

			return bitmap;
		}


		private Bitmap GetRenderBoolInfo( List<RegularScalarNode> regulars, List<RegularStructNode> regularStructs, string selectedRegular )
		{
			// 先找到选定的那个bool相关的节点
			RegularScalarNode boolRegular = null;
			for (int i = 0; i < regulars.Count; i++)
			{
				if(regulars[i].DataTypeCode == RegularNodeTypeItem.Bool.Text && regulars[i].Name == selectedRegular)
				{
					boolRegular = regulars[i];
					break;
				}
			}
			if(boolRegular == null)
			{
				label4.Text = "以Bool为单位的解析";
				return new Bitmap( 20, 20 );
			}

			label4.Text = "以Bool为单位的解析: " + boolRegular.Name;
			int max_bool = boolRegular.GetLengthByte( ) * 8;
			int every_line_count = (panel3.Width - 19 - 90) / everyByteOccupy;
			if (every_line_count < 10) every_line_count = 10;
			int line_count = GetNumberByUplimit( max_bool, every_line_count );


			Bitmap bitmap = new Bitmap( panel6.Width - 19, line_count * everyBytesLineHeight + 5 );
			if (max_bool == 0) return bitmap;
			Graphics g = Graphics.FromImage( bitmap );
			g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
			g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
			g.Clear( BackColor );

			int relativeIndex = boolRegular.GetStartedByteIndex( ) * 8;
			PaintIndexHead( g, every_line_count, max_bool, relativeIndex );
			for (int i = 0; i < boolRegular.GetLengthByte( ); i++)
			{
				PaintEveryByteRectangle( g, every_line_count, i * 8, 8, Pens.Gray, Brushes.Transparent, Brushes.Black, relativeIndex: i * (-8), byteImage: false, boolIndex: boolRegular.GetStartedByteIndex( ) * 8 + i * 8 );
				PaintDataByteLineAndInfomation( g, every_line_count, i * 8, 8, $"byte[{ boolRegular.GetStartedByteIndex( ) + i}]", 
					Brushes.Purple, DataByteLineStyle.Up );
			}

			// 交替显示bool的名称值
			if (boolRegular.Length >= 0)
			{
				PaintDataByteLineAndInfomation( g, every_line_count, boolRegular.Index - relativeIndex, boolRegular.Length, $"{boolRegular.Name} * {boolRegular.Length}",
					Brushes.Purple, DataByteLineStyle.Down2 );
				for (int i = 0; i < boolRegular.Length; i++)
				{
					PaintEveryByteRectangle( g, every_line_count,
						boolRegular.Index - relativeIndex + i, 1,
						GetBoolBackColor( boolRegular.Index + i ), (boolRegular.Index - relativeIndex + i) / 8 * -8, byteImage: false, boolIndex: 0 );
				}
			}
			else
			{
				PaintDataByteLineAndInfomation( g, every_line_count, boolRegular.Index - relativeIndex, 1, $"{boolRegular.Name}",
					Brushes.Purple, DataByteLineStyle.Down2 );
				PaintEveryByteRectangle( g, every_line_count, boolRegular.Index - relativeIndex, 1, GetBoolBackColor( boolRegular.Index ), 0, byteImage: false, boolIndex: 0 );
			}


			return bitmap;
		}

		private Color GetBoolBackColor( int index )
		{
			Color boolColor = RegularNodeTypeItem.Bool.BackColor;
			if (isDataPreview && dataBuffer != null && deviceNode != null)
				boolColor = dataBuffer.GetBoolValue( index / 8, index % 8 ) ? RegularNodeTypeItem.Bool.BackColor : Color.LightGray;
			return boolColor;
		}

		#endregion

		private bool isShowText = true;                                        // 是否显示规则名信息
		private bool isDataPreview = false;                                    // 是否显示实际的解析数据结果
		private string selectedRegularItemName = string.Empty;                 // 选择的规则节点变量的名称
		private DeviceNode deviceNode;                                         // 当前的设备的基础对象
		private DeviceCoreBase deviceCoreBase;                                 // 当前的设备的实例化对象信息
		private List<RegularScalarNode> regularNodes;                          // 绑定的解析规则列表
		private List<RegularStructNode> regularStructNodes;                    // 绑定的结构体解析规则
		private List<int> markedByteIndex = new List<int>( );                  // 标记的字节绘制过bit的规则信息
		private System.Drawing.StringFormat stringFormatCenter = new System.Drawing.StringFormat( );
		private System.Drawing.StringFormat stringFormatDown = new System.Drawing.StringFormat( );
		private System.Drawing.StringFormat stringFormatTop = new System.Drawing.StringFormat( );
		private Font font_6 = null;
		private Font font_7 = null;
		private Font font_9 = null;
		private readonly int everyBytesLineHeight = 90;                         // 绘制时每层的高度
		private readonly int everyByteWidth = 20;                               // 绘制的每个小框框的宽高信息
		private readonly int everyByteOccupy = 24;
		private int byteLengthMax = -1;                                         // 当前绘制的最大长度信息，负数不限制
		private int paint_x = 85;
		private int paint_y = 2;
		private byte[] dataBuffer = null;                                        // 数据缓存对象
		private Color selectBackgroundColor = Color.FromArgb( 255, 255, 236 );   // 选择的背景色
		private int parityMark = 0;                                              // 自增的奇偶标记
		private string deviceAddressStarted = string.Empty;

		/// <summary>
		/// 数据预览的状态变化时触发
		/// </summary>
		public event EventHandler OnDataPreviewChanged;

		public void SetBufferData( byte[] data )
		{
			dataBuffer = data;
			UpdateRenderBitmap( );
		}

		private void SourceDataViewControl_Load( object sender, EventArgs e )
		{
			checkBox1.CheckedChanged += CheckBox1_CheckedChanged;
			panel3.SizeChanged       += Panel3_SizeChanged;
			checkBox2.CheckedChanged += CheckBox2_CheckedChanged;
			radioButton_hex.CheckedChanged += RadioButton_hex_CheckedChanged;
			radioButton_ascii.CheckedChanged += RadioButton_hex_CheckedChanged;
		}

		private void RadioButton_hex_CheckedChanged( object sender, EventArgs e )
		{
			if (checkBox2.Checked)
			{
				UpdateRenderBitmap( );
			}
		}

		private void CheckBox2_CheckedChanged( object sender, EventArgs e )
		{
			isDataPreview = checkBox2.Checked;
			if (isDataPreview)
			{
				// 从网关服务器加载相关的数据信息

				// 思路1，把规则及读取的数据信息，打包发送到边缘网关的服务器，返回JSON数据，然后去JSON里进行挑选数据显示。
				// 思路2，把读取的数据信息发送给边缘网关服务器，返回byte[],自行解析，但是bytetransform没法获取，尤其是插件的bytetransform

				// 插件先不管了
				OnDataPreviewChanged?.Invoke( sender, e );
			}
			else
			{
				this.dataBuffer = null;
				UpdateRenderBitmap( );
			}
		}

		private void Panel3_SizeChanged( object sender, EventArgs e )
		{
			UpdateRenderBitmap( );
		}

		private void CheckBox1_CheckedChanged( object sender, EventArgs e )
		{
			isShowText = checkBox1.Checked;
			UpdateRenderBitmap( );
		}

	}


	public enum DataByteLineStyle
	{
		Up,
		Down1,
		Down2,
		Down3,
	}
}
