using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node.Request;
using HslCommunication;

namespace HslTechnology.EdgeViewer.Controls.NodeSettings
{
	public partial class RequestCycleControl : UserControl
	{
		public RequestCycleControl( )
		{
			InitializeComponent( );
		}

		private void RequestDataAndCycleControl_Paint( object sender, PaintEventArgs e )
		{
		}

		private void RequestDataAndCycleControl_Load( object sender, EventArgs e )
		{
			label8.Visible = false;
			textBox5.Visible = false;

			radioButton1.CheckedChanged += RadioButton1_CheckedChanged;
			radioButton2.CheckedChanged += RadioButton2_CheckedChanged;

			comboBox_condition_type.SelectedIndex = 0;
		}

		private void RadioButton1_CheckedChanged( object sender, EventArgs e )
		{
			// 无限次
			if(radioButton1.Checked)
			{
				label8.Visible = false;
				textBox5.Visible = false;
			}
		}

		private void RadioButton2_CheckedChanged( object sender, EventArgs e )
		{
			// 固定次数
			if (radioButton2.Checked)
			{
				label8.Visible   = true;
				textBox5.Visible = true;
				textBox5.Text    = "1";
			}
		}

		/// <summary>
		/// 显示节点的基本信息
		/// </summary>
		/// <param name="request"></param>
		public void ShowRequestInfo( RequestBase request )
		{
			numericUpDown1.Value = request.RequestInterval;
			if (request.RequestCount < 0)
			{
				radioButton1.Checked = true;
			}
			else
			{
				radioButton2.Checked = true;
				textBox5.Text = request.RequestCount.ToString( );
			}

			textBox_condition.Text = request.AdditionalConditions;
			comboBox_condition_type.SelectedIndex = request.ConditionsEnableType;
		}

		/// <summary>
		/// 获取节点的信息
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public OperateResult GetRequestInfo( RequestBase request )
		{
			try
			{
				if (numericUpDown1.Value < 0) return new OperateResult( "请求间隔不能小于0" );
				request.RequestInterval = (int)numericUpDown1.Value;
				if (radioButton1.Checked)
					request.RequestCount = -1;
				else
				{
					if(!long.TryParse(textBox5.Text, out long count ))
					{
						return new OperateResult( "请求间隔不能小于0" );
					}
					request.RequestCount = count;
				}
				request.AdditionalConditions = textBox_condition.Text;
				request.ConditionsEnableType = comboBox_condition_type.SelectedIndex;
				return OperateResult.CreateSuccessResult( );
			}
			catch(Exception ex)
			{
				return new OperateResult( ex.Message );
			}
		}

	}
}
