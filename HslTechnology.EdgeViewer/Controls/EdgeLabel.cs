using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls
{
	public class EdgeLabel : Label
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public EdgeLabel( )
		{
			AutoSize = false;
			Size = new Size( 200, 20 );
			TextAlign = ContentAlignment.MiddleLeft;
			ForeColor = Color.White;
			BackColor = Color.DodgerBlue;
			Padding = new Padding( 5, 0, 0, 0 );
		}

		/// <inheritdoc/>
		protected override void OnPaintBackground( PaintEventArgs pevent )
		{
			//base.OnPaintBackground( pevent );
			LinearGradientBrush b = new LinearGradientBrush( new Point( 0, 20 ), new Point( Width - 1, 20 ), Color.FromArgb( 142, 196, 216 ), Color.FromArgb( 240, 240, 240 ) );
			ColorBlend colorBlend = new ColorBlend( );
			colorBlend.Positions = new float[] { 0.0f, 1.0f };
			colorBlend.Colors = new Color[] { Color.DodgerBlue, Color.LightSkyBlue };
			b.InterpolationColors = colorBlend;
			pevent.Graphics.FillRectangle( b, 0, 0, Width, Height );
			b.Dispose( );
		}
	}
}
