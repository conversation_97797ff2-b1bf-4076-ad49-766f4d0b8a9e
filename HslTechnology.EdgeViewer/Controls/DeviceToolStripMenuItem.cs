using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node;
using System.ComponentModel;
using System.Windows.Forms.Design;
using System.ComponentModel.Design.Serialization;

namespace HslTechnology.EdgeViewer.Controls
{
	[ToolStripItemDesignerAvailability( ToolStripItemDesignerAvailability.MenuStrip | ToolStripItemDesignerAvailability.ContextMenuStrip )]
	[DesignerSerializer( "System.Windows.Forms.Design.ToolStripMenuItemCodeDomSerializer, System.Design, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a", "System.ComponentModel.Design.Serialization.CodeDomSerializer, System.Design, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" )]
	public class DeviceToolStripMenuItem : ToolStripMenuItem
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public DeviceToolStripMenuItem( )
		{
			DeviceType = DeviceType.Device;
		}

		[Browsable( true )]
		[Description( "当前的设备的节点类型" )]
		[DefaultValue( typeof( DeviceType ), "Device" )]
		public DeviceType DeviceType { get; set; }

	}
}
