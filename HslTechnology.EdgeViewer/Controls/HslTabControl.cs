using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls
{
	public class HslTabControl : TabControl
	{
		public override Color BackColor
		{
			get => backColor;
			set
			{
				backColor = value;
				Invalidate( );
			}
		}

		protected override void OnPaint( PaintEventArgs e )
		{
			Rectangle tcRec = this.ClientRectangle;
			using (Brush brush = new SolidBrush( backColor ))
				e.Graphics.FillRectangle( brush, tcRec );
		}


		private Color backColor = Color.Transparent;
	}
}
