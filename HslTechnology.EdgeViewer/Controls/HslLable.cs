using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing;

namespace HslTechnology.EdgeViewer.Controls
{
	public class HslLable : Label
	{
		public HslLable( )
		{

		}

		protected override void OnMouseEnter( EventArgs e )
		{
			BackColor = mouseMoveColor;
			base.OnMouseEnter( e );
		}

		protected override void OnMouseLeave( EventArgs e )
		{
			BackColor = DefaultBackColor;
			base.OnMouseLeave( e );
		}

		protected override void OnPaint( PaintEventArgs e )
		{
			base.OnPaint( e );
			using(Pen pen = new Pen( ForeColor ))
			{
				e.Graphics.DrawRectangle( pen, 0, 0, Width - 1, Height - 1 );
			}
		}

		protected override void OnClick( EventArgs e )
		{
			base.OnClick( e );
			isSelected = !isSelected;
		}

		/// <summary>
		/// 当前的按钮是否选择
		/// </summary>
		public bool IsSelected { get => isSelected; set => isSelected = value; }

		private Color mouseMoveColor = Color.SkyBlue;    // 鼠标移动的颜色
		private bool isSelected = false;                 // 当前的控件是否选中
	}
}
