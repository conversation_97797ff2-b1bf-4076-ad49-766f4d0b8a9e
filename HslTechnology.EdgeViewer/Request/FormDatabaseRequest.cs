using HslCommunication;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.EdgeViewer.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using HslCommunication.MQTT;
using HslTechnology.Edge.Device;

namespace HslTechnology.EdgeViewer.Request
{
	public partial class FormDatabaseRequest : HslForm
	{
		public FormDatabaseRequest( EdgeServerSettings serverSettings, string nodePath, DeviceNode deviceNode, string[] databases, DatabaseRequest databaseRequest )
		{
			InitializeComponent( );

			this.edgeServerSettings = serverSettings;
			this.nodePath           = nodePath;
			this.databaseRequest    = databaseRequest ?? new DatabaseRequest( );
			this.deviceNode         = deviceNode;
			this.databases          = databases ?? new string[0];
			this.Text               = "数据库请求操作";
			this.Icon               = Util.ConvertToIcon( Util.GetImageByGroupNode( serverSettings, this.deviceNode ) );
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button_ok.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button_cancel.PerformClick( );
		}

		private void FormDatabaseRequest_Load( object sender, EventArgs e )
		{
			button_ok.Click += Button_ok_Click;

			textBox_device_name.Text = this.deviceNode?.GetDisplayName( Edge.Node.NodeDisplayMode.ShowCombine );
			comboBox_database.DataSource = this.databases;
			if (this.databaseRequest != null)
			{
				textBox_name.Text              = this.databaseRequest.Name;
				textBox_description.Text       = this.databaseRequest.Description;
				checkBox_enableRequest.Checked = this.databaseRequest.Enable;

				if (this.databases.Contains( this.databaseRequest.Address ))
					comboBox_database.SelectedItem = this.databaseRequest.Address;


				requestDataAndCycleControl1.ShowRequestInfo( this.databaseRequest );
				textBox_sql.Text = this.databaseRequest.SqlCommand;
			}

			ThreadPool.QueueUserWorkItem( new WaitCallback( ThreadPoolMethod ), null );
		}

		private async void ThreadPoolMethod( object obj )
		{
			Thread.Sleep( 100 );
			// 加载显示数据标签信息
			if (this.edgeServerSettings == null) return;
			MqttSyncClient rpc = this.edgeServerSettings.GetMqttSyncClient( );
			OperateResult<DeviceSingleAddressLabel[]> read = await rpc.ReadRpcAsync<DeviceSingleAddressLabel[]>( "Admin/GetDeviceAddressLabel", new { data = this.nodePath } );
			try
			{
				Invoke( new Action( ( ) =>
				 {
					 if (read.IsSuccess)
					 {
						 HslTechnology.Edge.Controls.HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, read.Content.Length );
						 for (int i = 0; i < read.Content.Length; i++)
						 {
							 dataGridView1.Rows[i].Cells[0].Value = read.Content[i].Name;
							 dataGridView1.Rows[i].Cells[1].Value = read.Content[i].DataType;
						 }
						 if (read.Content.Length == 0) label_status.Text = "状态：当前没有数据属性，请下载配置重启网关后，尝试";
						 else label_status.Text = $"状态：请求成功，共计数据：{read.Content.Length} 个";
					 }
					 else
					 {
						 label_status.Text = "操作失败：" + read.Message + Environment.NewLine + "请下载配置重启网关后，尝试";
					 }
				 } ) );
			}
			catch
			{

			}
		}

		private void Button_ok_Click( object sender, EventArgs e )
		{
			// 点击了确认，需要另生成一个对象处理
			if (comboBox_database.SelectedItem == null)
			{
				MessageBox.Show( "当前没有选择数据库，请先在网关里创建数据库对象信息。" );
				return;
			}

			DatabaseRequest request = new DatabaseRequest( );
			request.Name        = textBox_name.Text;
			request.Description = textBox_description.Text;
			request.Address = comboBox_database.SelectedItem.ToString( );
			request.Enable = checkBox_enableRequest.Checked;
			OperateResult write = this.requestDataAndCycleControl1.GetRequestInfo( request );
			if (!write.IsSuccess)
			{
				MessageBox.Show( write.Message );
				return;
			}

			request.SqlCommand = textBox_sql.Text;

			this.databaseRequest = request;
			DialogResult = DialogResult.OK;
		}

		public DatabaseRequest DatabaseRequestNode => this.databaseRequest;

		private EdgeServerSettings edgeServerSettings;
		private DatabaseRequest databaseRequest;
		private string[] databases;
		private DeviceNode deviceNode;
		private string nodePath = string.Empty;
	}
}
