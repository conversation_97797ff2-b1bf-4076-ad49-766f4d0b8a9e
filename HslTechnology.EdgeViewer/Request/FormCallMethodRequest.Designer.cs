
using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Request
{
	partial class FormCallMethodRequest
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
            this.panel1 = new System.Windows.Forms.Panel();
            this.label5 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.textBox_parameter = new System.Windows.Forms.TextBox();
            this.edgeLabel4 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
            this.checkBox_storeToDevice = new System.Windows.Forms.CheckBox();
            this.checkBox_enableRequest = new System.Windows.Forms.CheckBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.edgeLabel1 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
            this.label1 = new System.Windows.Forms.Label();
            this.textBox_name = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox_description = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox_address = new System.Windows.Forms.TextBox();
            this.edgeLabel2 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
            this.requestDataAndCycleControl1 = new HslTechnology.EdgeViewer.Controls.NodeSettings.RequestCycleControl();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.treeView1 = new System.Windows.Forms.TreeView();
            this.edgeLabel3 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
            this.buttonOk1 = new HslTechnology.Edge.Controls.ButtonOk();
            this.buttonCancel1 = new HslTechnology.Edge.Controls.ButtonCancel();
            this.label9 = new System.Windows.Forms.Label();
            this.textBox_dataUint = new System.Windows.Forms.TextBox();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.AutoScroll = true;
            this.panel1.Controls.Add(this.textBox_dataUint);
            this.panel1.Controls.Add(this.label9);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.label7);
            this.panel1.Controls.Add(this.label8);
            this.panel1.Controls.Add(this.textBox_parameter);
            this.panel1.Controls.Add(this.edgeLabel4);
            this.panel1.Controls.Add(this.checkBox_storeToDevice);
            this.panel1.Controls.Add(this.checkBox_enableRequest);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.label6);
            this.panel1.Controls.Add(this.edgeLabel1);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.textBox_name);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.textBox_description);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.textBox_address);
            this.panel1.Controls.Add(this.edgeLabel2);
            this.panel1.Controls.Add(this.requestDataAndCycleControl1);
            this.panel1.Location = new System.Drawing.Point(450, 9);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(559, 568);
            this.panel1.TabIndex = 80;
            // 
            // label5
            // 
            this.label5.ForeColor = System.Drawing.Color.DimGray;
            this.label5.Location = new System.Drawing.Point(77, 146);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(462, 42);
            this.label5.TabIndex = 85;
            this.label5.Text = "[签名]";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(3, 146);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(68, 17);
            this.label7.TabIndex = 84;
            this.label7.Text = "方法签名：";
            // 
            // label8
            // 
            this.label8.ForeColor = System.Drawing.Color.DimGray;
            this.label8.Location = new System.Drawing.Point(77, 188);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(462, 70);
            this.label8.TabIndex = 83;
            this.label8.Text = "[注释]";
            // 
            // textBox_parameter
            // 
            this.textBox_parameter.Location = new System.Drawing.Point(3, 453);
            this.textBox_parameter.Multiline = true;
            this.textBox_parameter.Name = "textBox_parameter";
            this.textBox_parameter.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox_parameter.Size = new System.Drawing.Size(536, 110);
            this.textBox_parameter.TabIndex = 81;
            // 
            // edgeLabel4
            // 
            this.edgeLabel4.BackColor = System.Drawing.Color.DodgerBlue;
            this.edgeLabel4.ForeColor = System.Drawing.Color.White;
            this.edgeLabel4.Location = new System.Drawing.Point(3, 423);
            this.edgeLabel4.Name = "edgeLabel4";
            this.edgeLabel4.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.edgeLabel4.Size = new System.Drawing.Size(536, 22);
            this.edgeLabel4.TabIndex = 80;
            this.edgeLabel4.Text = "参数信息";
            this.edgeLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // checkBox_storeToDevice
            // 
            this.checkBox_storeToDevice.AutoSize = true;
            this.checkBox_storeToDevice.Checked = true;
            this.checkBox_storeToDevice.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox_storeToDevice.Location = new System.Drawing.Point(6, 395);
            this.checkBox_storeToDevice.Name = "checkBox_storeToDevice";
            this.checkBox_storeToDevice.Size = new System.Drawing.Size(339, 21);
            this.checkBox_storeToDevice.TabIndex = 79;
            this.checkBox_storeToDevice.Text = "方法结果写入设备数据信息（数据名称为顶部的请求名称）";
            this.checkBox_storeToDevice.UseVisualStyleBackColor = true;
            // 
            // checkBox_enableRequest
            // 
            this.checkBox_enableRequest.AutoSize = true;
            this.checkBox_enableRequest.Checked = true;
            this.checkBox_enableRequest.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox_enableRequest.Location = new System.Drawing.Point(80, 257);
            this.checkBox_enableRequest.Name = "checkBox_enableRequest";
            this.checkBox_enableRequest.Size = new System.Drawing.Size(123, 21);
            this.checkBox_enableRequest.TabIndex = 77;
            this.checkBox_enableRequest.Text = "启用定时请求调用";
            this.checkBox_enableRequest.UseVisualStyleBackColor = true;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(3, 258);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(68, 17);
            this.label4.TabIndex = 76;
            this.label4.Text = "方法功能：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(3, 188);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(68, 17);
            this.label6.TabIndex = 75;
            this.label6.Text = "方法注释：";
            // 
            // edgeLabel1
            // 
            this.edgeLabel1.BackColor = System.Drawing.Color.DodgerBlue;
            this.edgeLabel1.ForeColor = System.Drawing.Color.White;
            this.edgeLabel1.Location = new System.Drawing.Point(3, 1);
            this.edgeLabel1.Name = "edgeLabel1";
            this.edgeLabel1.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.edgeLabel1.Size = new System.Drawing.Size(536, 22);
            this.edgeLabel1.TabIndex = 60;
            this.edgeLabel1.Text = "节点信息";
            this.edgeLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(3, 31);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(68, 17);
            this.label1.TabIndex = 47;
            this.label1.Text = "请求名称：";
            // 
            // textBox_name
            // 
            this.textBox_name.Location = new System.Drawing.Point(80, 28);
            this.textBox_name.Name = "textBox_name";
            this.textBox_name.Size = new System.Drawing.Size(459, 23);
            this.textBox_name.TabIndex = 48;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(3, 59);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(68, 17);
            this.label2.TabIndex = 49;
            this.label2.Text = "请求描述：";
            // 
            // textBox_description
            // 
            this.textBox_description.Location = new System.Drawing.Point(80, 56);
            this.textBox_description.Name = "textBox_description";
            this.textBox_description.Size = new System.Drawing.Size(459, 23);
            this.textBox_description.TabIndex = 50;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(3, 120);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(68, 17);
            this.label3.TabIndex = 51;
            this.label3.Text = "请求方法：";
            // 
            // textBox_address
            // 
            this.textBox_address.Location = new System.Drawing.Point(80, 117);
            this.textBox_address.Name = "textBox_address";
            this.textBox_address.ReadOnly = true;
            this.textBox_address.Size = new System.Drawing.Size(459, 23);
            this.textBox_address.TabIndex = 52;
            // 
            // edgeLabel2
            // 
            this.edgeLabel2.BackColor = System.Drawing.Color.DodgerBlue;
            this.edgeLabel2.ForeColor = System.Drawing.Color.White;
            this.edgeLabel2.Location = new System.Drawing.Point(3, 88);
            this.edgeLabel2.Name = "edgeLabel2";
            this.edgeLabel2.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.edgeLabel2.Size = new System.Drawing.Size(536, 22);
            this.edgeLabel2.TabIndex = 61;
            this.edgeLabel2.Text = "方法信息";
            this.edgeLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // requestDataAndCycleControl1
            // 
            this.requestDataAndCycleControl1.BackColor = System.Drawing.Color.Transparent;
            this.requestDataAndCycleControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.requestDataAndCycleControl1.Location = new System.Drawing.Point(0, 279);
            this.requestDataAndCycleControl1.Name = "requestDataAndCycleControl1";
            this.requestDataAndCycleControl1.Size = new System.Drawing.Size(539, 109);
            this.requestDataAndCycleControl1.TabIndex = 63;
            // 
            // button2
            // 
            this.button2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.button2.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Critical_16xLG_color;
            this.button2.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.button2.Location = new System.Drawing.Point(-230, 605);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(143, 43);
            this.button2.TabIndex = 77;
            this.button2.Text = " 取消";
            this.button2.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.button2.UseVisualStyleBackColor = true;
            // 
            // button1
            // 
            this.button1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.button1.BackColor = System.Drawing.Color.Transparent;
            this.button1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color;
            this.button1.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.button1.Location = new System.Drawing.Point(-230, 556);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(143, 43);
            this.button1.TabIndex = 76;
            this.button1.Text = " 确认";
            this.button1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.button1.UseVisualStyleBackColor = false;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // treeView1
            // 
            this.treeView1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.treeView1.Location = new System.Drawing.Point(12, 40);
            this.treeView1.Name = "treeView1";
            this.treeView1.Size = new System.Drawing.Size(432, 577);
            this.treeView1.TabIndex = 81;
            // 
            // edgeLabel3
            // 
            this.edgeLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.edgeLabel3.BackColor = System.Drawing.Color.DodgerBlue;
            this.edgeLabel3.ForeColor = System.Drawing.Color.White;
            this.edgeLabel3.Location = new System.Drawing.Point(12, 9);
            this.edgeLabel3.Name = "edgeLabel3";
            this.edgeLabel3.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.edgeLabel3.Size = new System.Drawing.Size(432, 22);
            this.edgeLabel3.TabIndex = 79;
            this.edgeLabel3.Text = "方法签名列表";
            this.edgeLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // buttonOk1
            // 
            this.buttonOk1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonOk1.Location = new System.Drawing.Point(585, 583);
            this.buttonOk1.Name = "buttonOk1";
            this.buttonOk1.Size = new System.Drawing.Size(121, 36);
            this.buttonOk1.TabIndex = 82;
            this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
            // 
            // buttonCancel1
            // 
            this.buttonCancel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonCancel1.Location = new System.Drawing.Point(724, 583);
            this.buttonCancel1.Name = "buttonCancel1";
            this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
            this.buttonCancel1.TabIndex = 83;
            this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
            // 
            // label9
            // 
            this.label9.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(392, 397);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(44, 17);
            this.label9.TabIndex = 86;
            this.label9.Text = "单位：";
            // 
            // textBox_dataUint
            // 
            this.textBox_dataUint.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.textBox_dataUint.Location = new System.Drawing.Point(439, 392);
            this.textBox_dataUint.Name = "textBox_dataUint";
            this.textBox_dataUint.Size = new System.Drawing.Size(100, 23);
            this.textBox_dataUint.TabIndex = 87;
            // 
            // FormCallMethodRequest
            // 
            this.AcceptButton = this.button1;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.button2;
            this.ClientSize = new System.Drawing.Size(1010, 629);
            this.Controls.Add(this.buttonCancel1);
            this.Controls.Add(this.buttonOk1);
            this.Controls.Add(this.treeView1);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.edgeLabel3);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button1);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "FormCallMethodRequest";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "方法调用请求";
            this.Load += new System.EventHandler(this.FormCallMethodRequest_Load);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);

		}

		#endregion

		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.TextBox textBox_parameter;
		private Controls.EdgeLabel edgeLabel4;
		private System.Windows.Forms.CheckBox checkBox_storeToDevice;
		private System.Windows.Forms.CheckBox checkBox_enableRequest;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Label label6;
		private Controls.EdgeLabel edgeLabel1;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TextBox textBox_name;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox textBox_description;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.TextBox textBox_address;
		private Controls.EdgeLabel edgeLabel2;
		private Controls.NodeSettings.RequestCycleControl requestDataAndCycleControl1;
		private Controls.EdgeLabel edgeLabel3;
		private System.Windows.Forms.Button button2;
		private System.Windows.Forms.Button button1;
		private System.Windows.Forms.TreeView treeView1;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label7;
		private ButtonOk buttonOk1;
		private ButtonCancel buttonCancel1;
        private System.Windows.Forms.TextBox textBox_dataUint;
        private System.Windows.Forms.Label label9;
    }
}