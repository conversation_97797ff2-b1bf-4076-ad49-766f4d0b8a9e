using HslTechnology.Edge.Controls.Basic;

namespace HslTechnology.EdgeViewer.Request
{
	partial class FormWriteTagRequest
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
			System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
			this.comboBox_dataTag = new System.Windows.Forms.ComboBox();
			this.textBox_device_name = new System.Windows.Forms.TextBox();
			this.button_ok = new System.Windows.Forms.Button();
			this.button_cancel = new System.Windows.Forms.Button();
			this.buttonCancel1 = new HslTechnology.Edge.Controls.ButtonCancel();
			this.buttonOk1 = new HslTechnology.Edge.Controls.ButtonOk();
			this.label1 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.textBox_name = new System.Windows.Forms.TextBox();
			this.label4 = new System.Windows.Forms.Label();
			this.textBox_description = new System.Windows.Forms.TextBox();
			this.label5 = new System.Windows.Forms.Label();
			this.label_status = new System.Windows.Forms.Label();
			this.requestDataAndCycleControl1 = new HslTechnology.EdgeViewer.Controls.NodeSettings.RequestCycleControl();
			this.edgeLabel2 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.edgeLabel1 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.edgeLabel3 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.column_datatype = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.column_name = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.dataGridView1 = new HslTechnology.Edge.Controls.Basic.HslDataGridView();
			this.label2 = new System.Windows.Forms.Label();
			this.textBox_formatValue = new System.Windows.Forms.TextBox();
			this.label7 = new System.Windows.Forms.Label();
			this.label8 = new System.Windows.Forms.Label();
			this.checkBox1 = new System.Windows.Forms.CheckBox();
			this.label6 = new System.Windows.Forms.Label();
			this.label9 = new System.Windows.Forms.Label();
			this.checkBox_IsAbsoluteAddress = new System.Windows.Forms.CheckBox();
			this.label10 = new System.Windows.Forms.Label();
			this.comboBox_type = new System.Windows.Forms.ComboBox();
			this.checkBox_isArray = new System.Windows.Forms.CheckBox();
			this.label11 = new System.Windows.Forms.Label();
			this.label12 = new System.Windows.Forms.Label();
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
			this.SuspendLayout();
			// 
			// comboBox_dataTag
			// 
			this.comboBox_dataTag.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.comboBox_dataTag.FormattingEnabled = true;
			this.comboBox_dataTag.Location = new System.Drawing.Point(446, 235);
			this.comboBox_dataTag.Name = "comboBox_dataTag";
			this.comboBox_dataTag.Size = new System.Drawing.Size(394, 25);
			this.comboBox_dataTag.TabIndex = 81;
			// 
			// textBox_device_name
			// 
			this.textBox_device_name.Location = new System.Drawing.Point(12, 38);
			this.textBox_device_name.Name = "textBox_device_name";
			this.textBox_device_name.ReadOnly = true;
			this.textBox_device_name.Size = new System.Drawing.Size(262, 23);
			this.textBox_device_name.TabIndex = 83;
			// 
			// button_ok
			// 
			this.button_ok.Location = new System.Drawing.Point(-417, 403);
			this.button_ok.Name = "button_ok";
			this.button_ok.Size = new System.Drawing.Size(83, 28);
			this.button_ok.TabIndex = 86;
			this.button_ok.Text = "button_ok";
			this.button_ok.UseVisualStyleBackColor = true;
			// 
			// button_cancel
			// 
			this.button_cancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button_cancel.Location = new System.Drawing.Point(-517, 404);
			this.button_cancel.Name = "button_cancel";
			this.button_cancel.Size = new System.Drawing.Size(106, 27);
			this.button_cancel.TabIndex = 87;
			this.button_cancel.Text = "button_cancel";
			this.button_cancel.UseVisualStyleBackColor = true;
			// 
			// buttonCancel1
			// 
			this.buttonCancel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonCancel1.Location = new System.Drawing.Point(585, 525);
			this.buttonCancel1.Name = "buttonCancel1";
			this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
			this.buttonCancel1.TabIndex = 89;
			this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
			// 
			// buttonOk1
			// 
			this.buttonOk1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonOk1.Location = new System.Drawing.Point(446, 525);
			this.buttonOk1.Name = "buttonOk1";
			this.buttonOk1.Size = new System.Drawing.Size(121, 36);
			this.buttonOk1.TabIndex = 88;
			this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(12, 69);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(188, 17);
			this.label1.TabIndex = 90;
			this.label1.Text = "数据列表信息 (从网关运行时请求)";
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(289, 39);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(68, 17);
			this.label3.TabIndex = 92;
			this.label3.Text = "请求名称：";
			// 
			// textBox_name
			// 
			this.textBox_name.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox_name.Location = new System.Drawing.Point(366, 36);
			this.textBox_name.Name = "textBox_name";
			this.textBox_name.Size = new System.Drawing.Size(478, 23);
			this.textBox_name.TabIndex = 93;
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(289, 67);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(68, 17);
			this.label4.TabIndex = 94;
			this.label4.Text = "请求描述：";
			// 
			// textBox_description
			// 
			this.textBox_description.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox_description.Location = new System.Drawing.Point(366, 64);
			this.textBox_description.Name = "textBox_description";
			this.textBox_description.Size = new System.Drawing.Size(478, 23);
			this.textBox_description.TabIndex = 95;
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(289, 240);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(104, 17);
			this.label5.TabIndex = 98;
			this.label5.Text = "选择的数据标签：";
			// 
			// label_status
			// 
			this.label_status.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label_status.AutoSize = true;
			this.label_status.Location = new System.Drawing.Point(12, 553);
			this.label_status.Name = "label_status";
			this.label_status.Size = new System.Drawing.Size(44, 17);
			this.label_status.TabIndex = 102;
			this.label_status.Text = "状态：";
			// 
			// requestDataAndCycleControl1
			// 
			this.requestDataAndCycleControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.requestDataAndCycleControl1.BackColor = System.Drawing.Color.Transparent;
			this.requestDataAndCycleControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.requestDataAndCycleControl1.Location = new System.Drawing.Point(286, 93);
			this.requestDataAndCycleControl1.Name = "requestDataAndCycleControl1";
			this.requestDataAndCycleControl1.Size = new System.Drawing.Size(556, 111);
			this.requestDataAndCycleControl1.TabIndex = 97;
			// 
			// edgeLabel2
			// 
			this.edgeLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.edgeLabel2.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel2.ForeColor = System.Drawing.Color.White;
			this.edgeLabel2.Location = new System.Drawing.Point(289, 9);
			this.edgeLabel2.Name = "edgeLabel2";
			this.edgeLabel2.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel2.Size = new System.Drawing.Size(553, 22);
			this.edgeLabel2.TabIndex = 96;
			this.edgeLabel2.Text = "节点信息";
			this.edgeLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// edgeLabel1
			// 
			this.edgeLabel1.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel1.ForeColor = System.Drawing.Color.White;
			this.edgeLabel1.Location = new System.Drawing.Point(12, 9);
			this.edgeLabel1.Name = "edgeLabel1";
			this.edgeLabel1.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel1.Size = new System.Drawing.Size(262, 22);
			this.edgeLabel1.TabIndex = 82;
			this.edgeLabel1.Text = "设备及数据列表";
			this.edgeLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// edgeLabel3
			// 
			this.edgeLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.edgeLabel3.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel3.ForeColor = System.Drawing.Color.White;
			this.edgeLabel3.Location = new System.Drawing.Point(288, 207);
			this.edgeLabel3.Name = "edgeLabel3";
			this.edgeLabel3.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel3.Size = new System.Drawing.Size(552, 22);
			this.edgeLabel3.TabIndex = 80;
			this.edgeLabel3.Text = "数据信息";
			this.edgeLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// column_datatype
			// 
			this.column_datatype.HeaderText = "数据类型";
			this.column_datatype.Name = "column_datatype";
			this.column_datatype.ReadOnly = true;
			this.column_datatype.Width = 90;
			// 
			// column_name
			// 
			this.column_name.HeaderText = "数据名称";
			this.column_name.Name = "column_name";
			this.column_name.ReadOnly = true;
			this.column_name.Width = 152;
			// 
			// dataGridView1
			// 
			this.dataGridView1.AllowUserToAddRows = false;
			this.dataGridView1.AllowUserToDeleteRows = false;
			this.dataGridView1.AllowUserToResizeRows = false;
			dataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(238)))), ((int)(((byte)(238)))), ((int)(((byte)(255)))));
			this.dataGridView1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle3;
			this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
			this.dataGridView1.BackgroundColor = System.Drawing.Color.White;
			this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.column_name,
            this.column_datatype});
			this.dataGridView1.Location = new System.Drawing.Point(12, 89);
			this.dataGridView1.Name = "dataGridView1";
			this.dataGridView1.ReadOnly = true;
			this.dataGridView1.RowHeadersVisible = false;
			this.dataGridView1.RowTemplate.Height = 23;
			this.dataGridView1.Size = new System.Drawing.Size(262, 461);
			this.dataGridView1.TabIndex = 101;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(290, 279);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(104, 17);
			this.label2.TabIndex = 91;
			this.label2.Text = "写入的数据信息：";
			// 
			// textBox_formatValue
			// 
			this.textBox_formatValue.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox_formatValue.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.textBox_formatValue.Location = new System.Drawing.Point(293, 300);
			this.textBox_formatValue.Multiline = true;
			this.textBox_formatValue.Name = "textBox_formatValue";
			this.textBox_formatValue.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
			this.textBox_formatValue.Size = new System.Drawing.Size(547, 107);
			this.textBox_formatValue.TabIndex = 85;
			// 
			// label7
			// 
			this.label7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label7.AutoSize = true;
			this.label7.ForeColor = System.Drawing.Color.Gray;
			this.label7.Location = new System.Drawing.Point(291, 412);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(208, 17);
			this.label7.TabIndex = 103;
			this.label7.Text = "格式1： 常量，例如 0, 10, 100, True";
			// 
			// label8
			// 
			this.label8.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label8.AutoSize = true;
			this.label8.ForeColor = System.Drawing.Color.Gray;
			this.label8.Location = new System.Drawing.Point(291, 430);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(440, 17);
			this.label8.TabIndex = 104;
			this.label8.Text = "格式2： 可选数组，例如 [1,2,5,10]，[True, False], 就会在这几个值循环写入操作";
			// 
			// checkBox1
			// 
			this.checkBox1.AutoSize = true;
			this.checkBox1.Location = new System.Drawing.Point(229, 68);
			this.checkBox1.Name = "checkBox1";
			this.checkBox1.Size = new System.Drawing.Size(51, 21);
			this.checkBox1.TabIndex = 105;
			this.checkBox1.Text = "全局";
			this.checkBox1.UseVisualStyleBackColor = true;
			// 
			// label6
			// 
			this.label6.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label6.AutoSize = true;
			this.label6.ForeColor = System.Drawing.Color.Gray;
			this.label6.Location = new System.Drawing.Point(291, 448);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(438, 17);
			this.label6.TabIndex = 106;
			this.label6.Text = "格式3： 整数范围，例如 [1-100], [500-100]，支持升序或是降序，反复循环写入";
			// 
			// label9
			// 
			this.label9.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label9.AutoSize = true;
			this.label9.ForeColor = System.Drawing.Color.Gray;
			this.label9.Location = new System.Drawing.Point(291, 484);
			this.label9.Name = "label9";
			this.label9.Size = new System.Drawing.Size(506, 17);
			this.label9.TabIndex = 107;
			this.label9.Text = "格式5： 脚本，例如 eval=(short)(50*Math.Sin(2*Math.PI*x/100) + 80)     x为1,2,3,4...累加";
			// 
			// checkBox_IsAbsoluteAddress
			// 
			this.checkBox_IsAbsoluteAddress.AutoSize = true;
			this.checkBox_IsAbsoluteAddress.Location = new System.Drawing.Point(446, 266);
			this.checkBox_IsAbsoluteAddress.Name = "checkBox_IsAbsoluteAddress";
			this.checkBox_IsAbsoluteAddress.Size = new System.Drawing.Size(99, 21);
			this.checkBox_IsAbsoluteAddress.TabIndex = 108;
			this.checkBox_IsAbsoluteAddress.Text = "使用绝对地址";
			this.checkBox_IsAbsoluteAddress.UseVisualStyleBackColor = true;
			// 
			// label10
			// 
			this.label10.AutoSize = true;
			this.label10.Location = new System.Drawing.Point(573, 267);
			this.label10.Name = "label10";
			this.label10.Size = new System.Drawing.Size(68, 17);
			this.label10.TabIndex = 109;
			this.label10.Text = "数据类型：";
			// 
			// comboBox_type
			// 
			this.comboBox_type.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_type.FormattingEnabled = true;
			this.comboBox_type.Location = new System.Drawing.Point(647, 264);
			this.comboBox_type.Name = "comboBox_type";
			this.comboBox_type.Size = new System.Drawing.Size(95, 25);
			this.comboBox_type.TabIndex = 110;
			// 
			// checkBox_isArray
			// 
			this.checkBox_isArray.AutoSize = true;
			this.checkBox_isArray.Location = new System.Drawing.Point(765, 266);
			this.checkBox_isArray.Name = "checkBox_isArray";
			this.checkBox_isArray.Size = new System.Drawing.Size(75, 21);
			this.checkBox_isArray.TabIndex = 111;
			this.checkBox_isArray.Text = "是否数组";
			this.checkBox_isArray.UseVisualStyleBackColor = true;
			// 
			// label11
			// 
			this.label11.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label11.AutoSize = true;
			this.label11.ForeColor = System.Drawing.Color.Gray;
			this.label11.Location = new System.Drawing.Point(291, 466);
			this.label11.Name = "label11";
			this.label11.Size = new System.Drawing.Size(380, 17);
			this.label11.TabIndex = 112;
			this.label11.Text = "格式4： 网关自身变量，例如使用 var=工厂1/车间A/西门子设备/温度";
			// 
			// label12
			// 
			this.label12.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label12.AutoSize = true;
			this.label12.ForeColor = System.Drawing.Color.Gray;
			this.label12.Location = new System.Drawing.Point(338, 503);
			this.label12.Name = "label12";
			this.label12.Size = new System.Drawing.Size(383, 17);
			this.label12.TabIndex = 113;
			this.label12.Text = "如果需要随机数，例如 eval=(short)(new Random( ).Next( 1, 101 ) )";
			// 
			// FormWriteTagRequest
			// 
			this.AcceptButton = this.button_ok;
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CancelButton = this.button_cancel;
			this.ClientSize = new System.Drawing.Size(853, 578);
			this.Controls.Add(this.label12);
			this.Controls.Add(this.label11);
			this.Controls.Add(this.checkBox_isArray);
			this.Controls.Add(this.label10);
			this.Controls.Add(this.comboBox_type);
			this.Controls.Add(this.checkBox_IsAbsoluteAddress);
			this.Controls.Add(this.label9);
			this.Controls.Add(this.label6);
			this.Controls.Add(this.checkBox1);
			this.Controls.Add(this.label8);
			this.Controls.Add(this.label7);
			this.Controls.Add(this.label_status);
			this.Controls.Add(this.dataGridView1);
			this.Controls.Add(this.label5);
			this.Controls.Add(this.requestDataAndCycleControl1);
			this.Controls.Add(this.edgeLabel2);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.textBox_name);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.textBox_description);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.buttonCancel1);
			this.Controls.Add(this.buttonOk1);
			this.Controls.Add(this.button_cancel);
			this.Controls.Add(this.button_ok);
			this.Controls.Add(this.textBox_formatValue);
			this.Controls.Add(this.textBox_device_name);
			this.Controls.Add(this.edgeLabel1);
			this.Controls.Add(this.comboBox_dataTag);
			this.Controls.Add(this.edgeLabel3);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "FormWriteTagRequest";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "FormWriteRequest";
			this.Load += new System.EventHandler(this.FormDatabaseRequest_Load);
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private Controls.EdgeLabel edgeLabel3;
		private System.Windows.Forms.ComboBox comboBox_dataTag;
		private Controls.EdgeLabel edgeLabel1;
		private System.Windows.Forms.TextBox textBox_device_name;
		private System.Windows.Forms.Button button_ok;
		private System.Windows.Forms.Button button_cancel;
		private Edge.Controls.ButtonCancel buttonCancel1;
		private Edge.Controls.ButtonOk buttonOk1;
		private System.Windows.Forms.Label label1;
		private Controls.EdgeLabel edgeLabel2;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.TextBox textBox_name;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.TextBox textBox_description;
		private Controls.NodeSettings.RequestCycleControl requestDataAndCycleControl1;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label_status;
		private System.Windows.Forms.DataGridViewTextBoxColumn column_datatype;
		private System.Windows.Forms.DataGridViewTextBoxColumn column_name;
		private HslDataGridView dataGridView1;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox textBox_formatValue;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.CheckBox checkBox1;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.CheckBox checkBox_IsAbsoluteAddress;
		private System.Windows.Forms.Label label10;
		private System.Windows.Forms.ComboBox comboBox_type;
		private System.Windows.Forms.CheckBox checkBox_isArray;
		private System.Windows.Forms.Label label11;
		private System.Windows.Forms.Label label12;
	}
}