using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.Edge.Config;
using System.Threading;
using HslTechnology.Edge.Reflection;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;
using HslTechnology.EdgeViewer.Core;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormScalarReadRequest : HslForm
	{
		#region Constructor

		public FormScalarReadRequest(
			EdgeServerSettings serverSettings,
			DeviceNode deviceNode, 
			ScalarReadRequest deviceRequest = null,
			List<AlarmNode> alarmNodes = null,
			List<OeeNode> oeeNodes = null
			)
		{
			InitializeComponent( );
			this.Icon           = Util.ConvertToIcon( Util.GetImageByGroupNode( serverSettings, deviceNode ) );
			this.serverSettings = serverSettings;
			this.DeviceRequest  = deviceRequest ?? new ScalarReadRequest( );
			this.deviceNode     = deviceNode;
			this.alarmNodes     = alarmNodes;
			this.oeeNodes       = oeeNodes;
		}

		#endregion

		#region Form Load
		
		protected override void OnLoad( EventArgs e )
		{
			this.Text = $"{this.Text} [{this.deviceNode?.Name}]";
			comboBox_type.DataSource = RegularNodeTypeItem.GetAllRegularNodeTypeItems( withBoolOfByte: false ).Select( m => m.Text ).ToArray( );
			
			comboBox_encoding.DataSource       = HslCommunication.BasicFramework.SoftBasic.GetEnumValues<StringEncoding>( );
			comboBox_alarm.DataSource          = this.alarmNodes;
			comboBox_oee.DataSource            = this.oeeNodes;
			comboBox_type.SelectedIndexChanged += ComboBox_type_SelectedIndexChanged;
			checkBox_isArray.CheckedChanged    += CheckBox_isArray_CheckedChanged;
			label4.Visible                     = false;
			textBox_length.Visible             = false;
			comboBox_encoding.Enabled          = false;


			comboBox_type.SelectedItem         = DeviceRequest.DataTypeCode;
			checkBox_forbidRemoteWrite.Checked = DeviceRequest.ForbidRemoteWrite;
			checkBox_subscription.Checked      = DeviceRequest.Subscription;
			checkBox_isArray.Checked           = DeviceRequest.Length >= 0;
			textBox_name.Text                  = DeviceRequest.Name;
			textBox_description.Text           = DeviceRequest.Description;
			textBox_displayName.Text           = DeviceRequest.DisplayName;
			textBox_address.Text               = DeviceRequest.Address;
			textBox_length.Text                = DeviceRequest.Length.ToString( );
			textBox_stringLength.Text          = DeviceRequest.StringLength.ToString( );
			textBox_unit.Text                  = DeviceRequest.Unit;
			checkBox_string_endwith0.Checked   = DeviceRequest.StringEndwithZero;
			if (DeviceRequest.DataTypeCode == RegularNodeTypeItem.String.Text)
				comboBox_encoding.SelectedItem = DeviceRequest.StringEncoding;
			else if(DeviceRequest.DataTypeCode == RegularNodeTypeItem.BCD.Text)
				comboBox_encoding.SelectedItem = DeviceRequest.BCDFormat;

			if (!string.IsNullOrEmpty(DeviceRequest.AlarmRelate))
			{
				AlarmNode find = this.alarmNodes.Find( m => m.Name == DeviceRequest.AlarmRelate );
				if (find != null)
				{
					checkBox_alarm.Checked = true;
					comboBox_alarm.SelectedItem = find;
				}
			}
			if (!string.IsNullOrEmpty( DeviceRequest.OeeRelate ))
			{
				OeeNode find = this.oeeNodes.Find( m => m.Name == DeviceRequest.OeeRelate );
				if (find != null)
				{
					checkBox_oee.Checked = true;
					comboBox_oee.SelectedItem = find;
				}
			}

			requestDataAndCycleControl1.ShowRequestInfo( DeviceRequest );
			requestDataScaleControl1.ShowScalarTransform( DeviceRequest );
			requestAddressExampleControl1.OnDataGridViewRowMouseDoubleClick += RequestAddressExampleControl1_OnDataGridViewRowMouseDoubleClick;
			base.OnLoad( e );
		}

		private void RequestAddressExampleControl1_OnDataGridViewRowMouseDoubleClick( DeviceAddressExample addressExample )
		{
			textBox_address.Text = addressExample.AddressExample;
			textBox_unit.Text = addressExample.Unit;
			if (addressExample.FillTagNameWithAddressType)
				textBox_name.Text = addressExample.AddressType;
			if (!string.IsNullOrEmpty( addressExample.DataType ))
			{
				// 如果指定数据类型，那么就切换类型信息。
				comboBox_type.SelectedItem = addressExample.DataType;
			}
		}

		private void CheckBox_isArray_CheckedChanged( object sender, EventArgs e )
		{
			if (checkBox_isArray.Checked)
			{
				label4.Visible = true;
				textBox_length.Visible = true;
				textBox_length.Text = "1";
			}
			else
			{
				label4.Visible = false;
				textBox_length.Visible = false;
			}
		}

		private void ComboBox_type_SelectedIndexChanged( object sender, EventArgs e )
		{
			// 当选择为字符串的时候，就显示为字符串长度
			string type = comboBox_type.SelectedItem.ToString( );
			if (RegularNodeTypeItem.IsDataTypeString( type ))
			{
				//checkBox_isArray.Visible = false;
				//label4.Text = "字符串长度";
				textBox_stringLength.Enabled = true;
				if (deviceNode.DeviceType == DeviceType.AllenBradleyCIP ||
					deviceNode.DeviceType == DeviceType.AllenBradleyMicroCIP ||
					deviceNode.DeviceType == DeviceType.OmronCipNet ||
					deviceNode.DeviceType == DeviceType.OmronConnectedCipNet ||
					deviceNode.DeviceType == DeviceType.SiemensWebApi)
				{
					textBox_stringLength.Text = "1";
				}
				else
				{
					textBox_stringLength.Text = "6";
				}
				label4.Visible = checkBox_isArray.Checked;
				textBox_length.Visible = checkBox_isArray.Checked;
				comboBox_encoding.Enabled = true;
				checkBox_string_endwith0.Enabled = true;
				if(type == RegularNodeTypeItem.BCD.Text)
				{
					comboBox_encoding.DataSource = HslCommunication.BasicFramework.SoftBasic.GetEnumValues<BCDFormat>( );
				}
				else
				{
					comboBox_encoding.DataSource = HslCommunication.BasicFramework.SoftBasic.GetEnumValues<StringEncoding>( );
				}
			}
			else
			{
				//checkBox_isArray.Visible = true;
				textBox_stringLength.Enabled = false;
				comboBox_encoding.Enabled = false;
				checkBox_string_endwith0.Enabled = false;
				label4.Visible = checkBox_isArray.Checked;
				textBox_length.Visible = checkBox_isArray.Checked;
				//label4.Text = "数组长度";
			}
		}

		protected override void OnShown( EventArgs e )
		{
			base.OnShown( e );
			ThreadPool.QueueUserWorkItem( new WaitCallback( ThreadPoolReadExampleAddress ), null );
		}

		private void ThreadPoolReadExampleAddress( object obj )
		{
			if (this.serverSettings != null)
			{
				// 从网关请求数据地址
				var rpc = this.serverSettings.GetMqttSyncClient( );
				var read = rpc.ReadRpc<DeviceAddressExample[]>( "Edge/GetDeviceExampleAddress",
					new { deviceType = this.deviceNode.DeviceType.ToString( ), pluginsType = this.deviceNode.PluginsType } );
				Invoke( new Action( ( ) =>
				 {
					 if (read.IsSuccess)
					 {
						 requestAddressExampleControl1.SetDeviceType( read.Content );
					 }
					 else
					 {
						 MessageBox.Show( read.Message );
					 }
				 } ) );
			}
			else if(this.deviceNode.DeviceType != DeviceType.Plugins)
			{
				Invoke( new Action( ( ) =>
				{
					var read = EdgeReflectionHelper.GetDeviceExampleAddress( this.deviceNode.DeviceType, this.deviceNode.PluginsType );
					if (read.IsSuccess)
					{
						requestAddressExampleControl1.SetDeviceType( read.Content );
					}
					else
					{
						MessageBox.Show( read.Message );
					}
				} ) );
			}
		}

		#endregion

		#region Button Click

		private void button1_Click( object sender, EventArgs e )
		{
			try
			{
				DeviceRequest = new ScalarReadRequest( )
				{
					Name              = textBox_name.Text,
					Description       = textBox_description.Text,
					DisplayName       = textBox_displayName.Text.Trim( ),
					Address           = textBox_address.Text,
					DataTypeCode      = comboBox_type.SelectedItem.ToString( ),
					ForbidRemoteWrite = checkBox_forbidRemoteWrite.Checked,
					Subscription      = checkBox_subscription.Checked,
					Unit              = textBox_unit.Text,
				};
				// 赋值长度信息，字符串和BCD直接赋值长度信息
				if (RegularNodeTypeItem.IsDataTypeString( DeviceRequest.DataTypeCode ))
				{
					DeviceRequest.StringEndwithZero = checkBox_string_endwith0.Checked;
					DeviceRequest.StringLength = int.Parse( textBox_stringLength.Text );


					// BCD或是字符串则判断实际的数据情况
					if (DeviceRequest.DataTypeCode == RegularNodeTypeItem.BCD.Text)
						DeviceRequest.BCDFormat = (BCDFormat)comboBox_encoding.SelectedItem;
					else
						DeviceRequest.StringEncoding = (StringEncoding)comboBox_encoding.SelectedItem;
				}
				DeviceRequest.Length = checkBox_isArray.Checked ? int.Parse( textBox_length.Text ) : -1;


				OperateResult request = requestDataAndCycleControl1.GetRequestInfo( DeviceRequest );
				if(!request.IsSuccess) { MessageBox.Show( request.Message ); return; }

				OperateResult scale = requestDataScaleControl1.GetScalarTransform( DeviceRequest );
				if (!scale.IsSuccess) { MessageBox.Show( scale.Message ); return; }

				if (checkBox_alarm.Checked)
				{
					AlarmNode select = comboBox_alarm.SelectedItem as AlarmNode;
					OperateResult setAlarm = Util.SetAlarmRelateByAlarmNode( DeviceRequest, select );
					if (!setAlarm.IsSuccess) { MessageBox.Show( setAlarm.Message ); return; }
				}
				if (checkBox_oee.Checked)
				{
					OeeNode select = comboBox_oee.SelectedItem as OeeNode;
					OperateResult setAlarm = Util.SetOeeRelateBy( DeviceRequest, select );
					if (!setAlarm.IsSuccess) { MessageBox.Show( setAlarm.Message ); return; }
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show( "数据填入失败！" + ex.Message );
				return;
			}

			DialogResult = DialogResult.OK;
		}
		#endregion

		#region Private Member

		/// <summary>
		/// 获取或设置当前设备的标量请求对象
		/// </summary>
		public ScalarReadRequest DeviceRequest { get; set; }
		private DeviceNode deviceNode;
		private List<AlarmNode> alarmNodes = null;
		private List<OeeNode> oeeNodes = null;
		private EdgeServerSettings serverSettings = null;

		#endregion

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}

		private void FormScalarReadRequest_Load( object sender, EventArgs e )
		{

		}
	}
}
