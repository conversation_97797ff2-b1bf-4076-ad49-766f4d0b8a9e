using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
    partial class FormScalarCacheRequest
	{
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose( bool disposing )
        {
            if (disposing && (components != null))
            {
                components.Dispose( );
            }
            base.Dispose( disposing );
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent( )
        {
			this.comboBox_type = new System.Windows.Forms.ComboBox();
			this.label5 = new System.Windows.Forms.Label();
			this.textBox_length = new System.Windows.Forms.TextBox();
			this.label4 = new System.Windows.Forms.Label();
			this.textBox_description = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.textBox_name = new System.Windows.Forms.TextBox();
			this.label1 = new System.Windows.Forms.Label();
			this.checkBox_isArray = new System.Windows.Forms.CheckBox();
			this.button2 = new System.Windows.Forms.Button();
			this.button1 = new System.Windows.Forms.Button();
			this.checkBox_alarm = new System.Windows.Forms.CheckBox();
			this.comboBox_alarm = new System.Windows.Forms.ComboBox();
			this.comboBox_oee = new System.Windows.Forms.ComboBox();
			this.checkBox_oee = new System.Windows.Forms.CheckBox();
			this.panel1 = new System.Windows.Forms.Panel();
			this.textBox_unit = new System.Windows.Forms.TextBox();
			this.label7 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.textBox_displayName = new System.Windows.Forms.TextBox();
			this.checkBox_subscription = new System.Windows.Forms.CheckBox();
			this.checkBox_forbidRemoteWrite = new System.Windows.Forms.CheckBox();
			this.label6 = new System.Windows.Forms.Label();
			this.edgeLabel1 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.edgeLabel4 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.requestDataScaleControl1 = new HslTechnology.EdgeViewer.Controls.NodeSettings.RequestDataScaleControl();
			this.edgeLabel2 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.buttonOk1 = new HslTechnology.Edge.Controls.ButtonOk();
			this.buttonCancel1 = new HslTechnology.Edge.Controls.ButtonCancel();
			this.panel1.SuspendLayout();
			this.SuspendLayout();
			// 
			// comboBox_type
			// 
			this.comboBox_type.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_type.FormattingEnabled = true;
			this.comboBox_type.Location = new System.Drawing.Point(80, 124);
			this.comboBox_type.Name = "comboBox_type";
			this.comboBox_type.Size = new System.Drawing.Size(101, 25);
			this.comboBox_type.TabIndex = 59;
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(3, 127);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(68, 17);
			this.label5.TabIndex = 55;
			this.label5.Text = "数据类型：";
			// 
			// textBox_length
			// 
			this.textBox_length.Location = new System.Drawing.Point(383, 125);
			this.textBox_length.Name = "textBox_length";
			this.textBox_length.Size = new System.Drawing.Size(112, 23);
			this.textBox_length.TabIndex = 54;
			this.textBox_length.Text = "1";
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(306, 128);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(68, 17);
			this.label4.TabIndex = 53;
			this.label4.Text = "数组长度：";
			// 
			// textBox_description
			// 
			this.textBox_description.Location = new System.Drawing.Point(80, 61);
			this.textBox_description.Name = "textBox_description";
			this.textBox_description.Size = new System.Drawing.Size(294, 23);
			this.textBox_description.TabIndex = 50;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(3, 64);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(68, 17);
			this.label2.TabIndex = 49;
			this.label2.Text = "请求描述：";
			// 
			// textBox_name
			// 
			this.textBox_name.Location = new System.Drawing.Point(80, 31);
			this.textBox_name.Name = "textBox_name";
			this.textBox_name.Size = new System.Drawing.Size(180, 23);
			this.textBox_name.TabIndex = 48;
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(3, 34);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(68, 17);
			this.label1.TabIndex = 47;
			this.label1.Text = "请求名称：";
			// 
			// checkBox_isArray
			// 
			this.checkBox_isArray.AutoSize = true;
			this.checkBox_isArray.Location = new System.Drawing.Point(214, 127);
			this.checkBox_isArray.Name = "checkBox_isArray";
			this.checkBox_isArray.Size = new System.Drawing.Size(75, 21);
			this.checkBox_isArray.TabIndex = 62;
			this.checkBox_isArray.Text = "是否数组";
			this.checkBox_isArray.UseVisualStyleBackColor = true;
			// 
			// button2
			// 
			this.button2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button2.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Critical_16xLG_color;
			this.button2.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
			this.button2.Location = new System.Drawing.Point(-215, 553);
			this.button2.Name = "button2";
			this.button2.Size = new System.Drawing.Size(143, 43);
			this.button2.TabIndex = 66;
			this.button2.Text = " 取消";
			this.button2.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.button2.UseVisualStyleBackColor = true;
			// 
			// button1
			// 
			this.button1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.button1.BackColor = System.Drawing.Color.Transparent;
			this.button1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color;
			this.button1.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
			this.button1.Location = new System.Drawing.Point(-215, 504);
			this.button1.Name = "button1";
			this.button1.Size = new System.Drawing.Size(143, 43);
			this.button1.TabIndex = 65;
			this.button1.Text = " 确认";
			this.button1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.button1.UseVisualStyleBackColor = false;
			this.button1.Click += new System.EventHandler(this.button1_Click);
			// 
			// checkBox_alarm
			// 
			this.checkBox_alarm.AutoSize = true;
			this.checkBox_alarm.Location = new System.Drawing.Point(2, 384);
			this.checkBox_alarm.Name = "checkBox_alarm";
			this.checkBox_alarm.Size = new System.Drawing.Size(75, 21);
			this.checkBox_alarm.TabIndex = 71;
			this.checkBox_alarm.Text = "报警分析";
			this.checkBox_alarm.UseVisualStyleBackColor = true;
			// 
			// comboBox_alarm
			// 
			this.comboBox_alarm.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_alarm.FormattingEnabled = true;
			this.comboBox_alarm.Location = new System.Drawing.Point(95, 381);
			this.comboBox_alarm.Name = "comboBox_alarm";
			this.comboBox_alarm.Size = new System.Drawing.Size(400, 25);
			this.comboBox_alarm.TabIndex = 72;
			// 
			// comboBox_oee
			// 
			this.comboBox_oee.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_oee.FormattingEnabled = true;
			this.comboBox_oee.Location = new System.Drawing.Point(95, 412);
			this.comboBox_oee.Name = "comboBox_oee";
			this.comboBox_oee.Size = new System.Drawing.Size(400, 25);
			this.comboBox_oee.TabIndex = 74;
			// 
			// checkBox_oee
			// 
			this.checkBox_oee.AutoSize = true;
			this.checkBox_oee.Location = new System.Drawing.Point(2, 415);
			this.checkBox_oee.Name = "checkBox_oee";
			this.checkBox_oee.Size = new System.Drawing.Size(75, 21);
			this.checkBox_oee.TabIndex = 73;
			this.checkBox_oee.Text = "Oee分析";
			this.checkBox_oee.UseVisualStyleBackColor = true;
			// 
			// panel1
			// 
			this.panel1.AutoScroll = true;
			this.panel1.Controls.Add(this.textBox_unit);
			this.panel1.Controls.Add(this.label7);
			this.panel1.Controls.Add(this.label3);
			this.panel1.Controls.Add(this.textBox_displayName);
			this.panel1.Controls.Add(this.checkBox_subscription);
			this.panel1.Controls.Add(this.checkBox_forbidRemoteWrite);
			this.panel1.Controls.Add(this.label6);
			this.panel1.Controls.Add(this.edgeLabel1);
			this.panel1.Controls.Add(this.comboBox_oee);
			this.panel1.Controls.Add(this.label1);
			this.panel1.Controls.Add(this.checkBox_oee);
			this.panel1.Controls.Add(this.textBox_name);
			this.panel1.Controls.Add(this.comboBox_alarm);
			this.panel1.Controls.Add(this.label2);
			this.panel1.Controls.Add(this.checkBox_alarm);
			this.panel1.Controls.Add(this.textBox_description);
			this.panel1.Controls.Add(this.edgeLabel4);
			this.panel1.Controls.Add(this.label4);
			this.panel1.Controls.Add(this.textBox_length);
			this.panel1.Controls.Add(this.label5);
			this.panel1.Controls.Add(this.comboBox_type);
			this.panel1.Controls.Add(this.requestDataScaleControl1);
			this.panel1.Controls.Add(this.edgeLabel2);
			this.panel1.Controls.Add(this.checkBox_isArray);
			this.panel1.Location = new System.Drawing.Point(9, 12);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(514, 474);
			this.panel1.TabIndex = 75;
			// 
			// textBox_unit
			// 
			this.textBox_unit.Location = new System.Drawing.Point(420, 61);
			this.textBox_unit.Name = "textBox_unit";
			this.textBox_unit.Size = new System.Drawing.Size(75, 23);
			this.textBox_unit.TabIndex = 81;
			// 
			// label7
			// 
			this.label7.AutoSize = true;
			this.label7.Location = new System.Drawing.Point(380, 64);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(44, 17);
			this.label7.TabIndex = 80;
			this.label7.Text = "单位：";
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(272, 34);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(68, 17);
			this.label3.TabIndex = 78;
			this.label3.Text = "显示别名：";
			// 
			// textBox_displayName
			// 
			this.textBox_displayName.Location = new System.Drawing.Point(349, 31);
			this.textBox_displayName.Name = "textBox_displayName";
			this.textBox_displayName.Size = new System.Drawing.Size(146, 23);
			this.textBox_displayName.TabIndex = 79;
			// 
			// checkBox_subscription
			// 
			this.checkBox_subscription.AutoSize = true;
			this.checkBox_subscription.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
			this.checkBox_subscription.Location = new System.Drawing.Point(80, 179);
			this.checkBox_subscription.Name = "checkBox_subscription";
			this.checkBox_subscription.Size = new System.Drawing.Size(195, 21);
			this.checkBox_subscription.TabIndex = 77;
			this.checkBox_subscription.Text = "启用数据发布订阅（支持数组）";
			this.checkBox_subscription.UseVisualStyleBackColor = true;
			// 
			// checkBox_forbidRemoteWrite
			// 
			this.checkBox_forbidRemoteWrite.AutoSize = true;
			this.checkBox_forbidRemoteWrite.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
			this.checkBox_forbidRemoteWrite.Location = new System.Drawing.Point(80, 159);
			this.checkBox_forbidRemoteWrite.Name = "checkBox_forbidRemoteWrite";
			this.checkBox_forbidRemoteWrite.Size = new System.Drawing.Size(231, 21);
			this.checkBox_forbidRemoteWrite.TabIndex = 76;
			this.checkBox_forbidRemoteWrite.Text = "禁止远程账户写入（包括管理员账户）";
			this.checkBox_forbidRemoteWrite.UseVisualStyleBackColor = true;
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.Location = new System.Drawing.Point(3, 160);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(68, 17);
			this.label6.TabIndex = 75;
			this.label6.Text = "数据权限：";
			// 
			// edgeLabel1
			// 
			this.edgeLabel1.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel1.ForeColor = System.Drawing.Color.White;
			this.edgeLabel1.Location = new System.Drawing.Point(3, 1);
			this.edgeLabel1.Name = "edgeLabel1";
			this.edgeLabel1.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel1.Size = new System.Drawing.Size(492, 22);
			this.edgeLabel1.TabIndex = 60;
			this.edgeLabel1.Text = "节点信息";
			this.edgeLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// edgeLabel4
			// 
			this.edgeLabel4.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel4.ForeColor = System.Drawing.Color.White;
			this.edgeLabel4.Location = new System.Drawing.Point(3, 351);
			this.edgeLabel4.Name = "edgeLabel4";
			this.edgeLabel4.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel4.Size = new System.Drawing.Size(492, 22);
			this.edgeLabel4.TabIndex = 70;
			this.edgeLabel4.Text = "附加功能";
			this.edgeLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// requestDataScaleControl1
			// 
			this.requestDataScaleControl1.BackColor = System.Drawing.Color.Transparent;
			this.requestDataScaleControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.requestDataScaleControl1.Location = new System.Drawing.Point(3, 221);
			this.requestDataScaleControl1.MinimumSize = new System.Drawing.Size(400, 127);
			this.requestDataScaleControl1.Name = "requestDataScaleControl1";
			this.requestDataScaleControl1.Size = new System.Drawing.Size(492, 127);
			this.requestDataScaleControl1.TabIndex = 64;
			// 
			// edgeLabel2
			// 
			this.edgeLabel2.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel2.ForeColor = System.Drawing.Color.White;
			this.edgeLabel2.Location = new System.Drawing.Point(3, 92);
			this.edgeLabel2.Name = "edgeLabel2";
			this.edgeLabel2.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel2.Size = new System.Drawing.Size(492, 22);
			this.edgeLabel2.TabIndex = 61;
			this.edgeLabel2.Text = "数据类型信息";
			this.edgeLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// buttonOk1
			// 
			this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonOk1.Location = new System.Drawing.Point(148, 509);
			this.buttonOk1.Name = "buttonOk1";
			this.buttonOk1.Size = new System.Drawing.Size(121, 36);
			this.buttonOk1.TabIndex = 76;
			this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
			// 
			// buttonCancel1
			// 
			this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonCancel1.Location = new System.Drawing.Point(284, 509);
			this.buttonCancel1.Name = "buttonCancel1";
			this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
			this.buttonCancel1.TabIndex = 77;
			this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
			// 
			// FormScalarCacheRequest
			// 
			this.AcceptButton = this.button1;
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CancelButton = this.button2;
			this.ClientSize = new System.Drawing.Size(543, 574);
			this.Controls.Add(this.buttonCancel1);
			this.Controls.Add(this.buttonOk1);
			this.Controls.Add(this.panel1);
			this.Controls.Add(this.button2);
			this.Controls.Add(this.button1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
			this.MaximizeBox = false;
			this.Name = "FormScalarCacheRequest";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "数据请求节点";
			this.Load += new System.EventHandler(this.FormScalarCacheRequest_Load);
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.ComboBox comboBox_type;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox textBox_length;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox textBox_description;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox textBox_name;
        private System.Windows.Forms.Label label1;
		private Controls.EdgeLabel edgeLabel1;
		private Controls.EdgeLabel edgeLabel2;
		private System.Windows.Forms.CheckBox checkBox_isArray;
		private Controls.NodeSettings.RequestDataScaleControl requestDataScaleControl1;
		private System.Windows.Forms.Button button1;
		private System.Windows.Forms.Button button2;
		private Controls.EdgeLabel edgeLabel4;
		private System.Windows.Forms.CheckBox checkBox_alarm;
		private System.Windows.Forms.ComboBox comboBox_alarm;
		private System.Windows.Forms.ComboBox comboBox_oee;
		private System.Windows.Forms.CheckBox checkBox_oee;
		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.CheckBox checkBox_forbidRemoteWrite;
		private System.Windows.Forms.CheckBox checkBox_subscription;
		private ButtonOk buttonOk1;
		private ButtonCancel buttonCancel1;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.TextBox textBox_displayName;
		private System.Windows.Forms.TextBox textBox_unit;
		private System.Windows.Forms.Label label7;
	}
}