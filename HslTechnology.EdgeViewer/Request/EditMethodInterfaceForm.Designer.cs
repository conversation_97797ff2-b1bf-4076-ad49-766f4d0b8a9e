
using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Request
{
	partial class EditMethodInterfaceForm
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
			System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
			this.label1 = new System.Windows.Forms.Label();
			this.dataGridView1 = new System.Windows.Forms.DataGridView();
			this.Column_method_name = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Column_remote_call = new System.Windows.Forms.DataGridViewCheckBoxColumn();
			this.label5 = new System.Windows.Forms.Label();
			this.label7 = new System.Windows.Forms.Label();
			this.label8 = new System.Windows.Forms.Label();
			this.label6 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.textBox_address = new System.Windows.Forms.TextBox();
			this.edgeLabel2 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.panel1 = new System.Windows.Forms.Panel();
			this.button2 = new System.Windows.Forms.Button();
			this.button1 = new System.Windows.Forms.Button();
			this.checkBox1 = new System.Windows.Forms.CheckBox();
			this.buttonOk1 = new ButtonOk();
			this.buttonCancel1 = new ButtonCancel();
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
			this.panel1.SuspendLayout();
			this.SuspendLayout();
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(12, 9);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(296, 17);
			this.label1.TabIndex = 0;
			this.label1.Text = "方法列表信息（此处设置的是给普通账户的调用权限）";
			// 
			// dataGridView1
			// 
			this.dataGridView1.AllowUserToAddRows = false;
			this.dataGridView1.AllowUserToDeleteRows = false;
			this.dataGridView1.AllowUserToResizeRows = false;
			dataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(238)))), ((int)(((byte)(238)))), ((int)(((byte)(255)))));
			this.dataGridView1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle3;
			this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
			this.dataGridView1.BackgroundColor = System.Drawing.Color.White;
			this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Column_method_name,
            this.Column_remote_call});
			this.dataGridView1.Location = new System.Drawing.Point(12, 29);
			this.dataGridView1.MultiSelect = false;
			this.dataGridView1.Name = "dataGridView1";
			this.dataGridView1.RowHeadersVisible = false;
			this.dataGridView1.RowTemplate.Height = 23;
			this.dataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
			this.dataGridView1.Size = new System.Drawing.Size(519, 588);
			this.dataGridView1.TabIndex = 1;
			// 
			// Column_method_name
			// 
			this.Column_method_name.HeaderText = "方法名称";
			this.Column_method_name.Name = "Column_method_name";
			this.Column_method_name.ReadOnly = true;
			this.Column_method_name.Width = 415;
			// 
			// Column_remote_call
			// 
			this.Column_remote_call.HeaderText = "是否公开";
			this.Column_remote_call.Name = "Column_remote_call";
			this.Column_remote_call.Width = 80;
			// 
			// label5
			// 
			this.label5.ForeColor = System.Drawing.Color.DimGray;
			this.label5.Location = new System.Drawing.Point(78, 61);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(390, 62);
			this.label5.TabIndex = 92;
			this.label5.Text = "[签名]";
			// 
			// label7
			// 
			this.label7.AutoSize = true;
			this.label7.Location = new System.Drawing.Point(4, 61);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(68, 17);
			this.label7.TabIndex = 91;
			this.label7.Text = "方法签名：";
			// 
			// label8
			// 
			this.label8.ForeColor = System.Drawing.Color.DimGray;
			this.label8.Location = new System.Drawing.Point(78, 123);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(390, 80);
			this.label8.TabIndex = 90;
			this.label8.Text = "[注释]";
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.Location = new System.Drawing.Point(4, 123);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(68, 17);
			this.label6.TabIndex = 89;
			this.label6.Text = "方法注释：";
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(4, 35);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(68, 17);
			this.label3.TabIndex = 86;
			this.label3.Text = "方法名称：";
			// 
			// textBox_address
			// 
			this.textBox_address.Location = new System.Drawing.Point(81, 32);
			this.textBox_address.Name = "textBox_address";
			this.textBox_address.ReadOnly = true;
			this.textBox_address.Size = new System.Drawing.Size(387, 23);
			this.textBox_address.TabIndex = 87;
			// 
			// edgeLabel2
			// 
			this.edgeLabel2.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel2.ForeColor = System.Drawing.Color.White;
			this.edgeLabel2.Location = new System.Drawing.Point(4, 3);
			this.edgeLabel2.Name = "edgeLabel2";
			this.edgeLabel2.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel2.Size = new System.Drawing.Size(464, 22);
			this.edgeLabel2.TabIndex = 88;
			this.edgeLabel2.Text = "方法信息";
			this.edgeLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// panel1
			// 
			this.panel1.Controls.Add(this.edgeLabel2);
			this.panel1.Controls.Add(this.label5);
			this.panel1.Controls.Add(this.textBox_address);
			this.panel1.Controls.Add(this.label7);
			this.panel1.Controls.Add(this.label3);
			this.panel1.Controls.Add(this.label8);
			this.panel1.Controls.Add(this.label6);
			this.panel1.Location = new System.Drawing.Point(537, 27);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(472, 203);
			this.panel1.TabIndex = 93;
			// 
			// button2
			// 
			this.button2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button2.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Critical_16xLG_color;
			this.button2.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
			this.button2.Location = new System.Drawing.Point(-704, 491);
			this.button2.Name = "button2";
			this.button2.Size = new System.Drawing.Size(143, 43);
			this.button2.TabIndex = 95;
			this.button2.Text = " 取消";
			this.button2.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.button2.UseVisualStyleBackColor = true;
			// 
			// button1
			// 
			this.button1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.button1.BackColor = System.Drawing.Color.Transparent;
			this.button1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color;
			this.button1.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
			this.button1.Location = new System.Drawing.Point(-544, 491);
			this.button1.Name = "button1";
			this.button1.Size = new System.Drawing.Size(143, 43);
			this.button1.TabIndex = 94;
			this.button1.Text = " 确认";
			this.button1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.button1.UseVisualStyleBackColor = false;
			this.button1.Click += new System.EventHandler(this.button1_Click);
			// 
			// checkBox1
			// 
			this.checkBox1.AutoSize = true;
			this.checkBox1.Location = new System.Drawing.Point(430, 8);
			this.checkBox1.Name = "checkBox1";
			this.checkBox1.Size = new System.Drawing.Size(51, 21);
			this.checkBox1.TabIndex = 96;
			this.checkBox1.Text = "全选";
			this.checkBox1.UseVisualStyleBackColor = true;
			// 
			// buttonOk1
			// 
			this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonOk1.Location = new System.Drawing.Point(649, 581);
			this.buttonOk1.Name = "buttonOk1";
			this.buttonOk1.Size = new System.Drawing.Size(121, 36);
			this.buttonOk1.TabIndex = 97;
			this.buttonOk1.Text = "buttonOk1";
			this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
			// 
			// buttonCancel1
			// 
			this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonCancel1.Location = new System.Drawing.Point(787, 581);
			this.buttonCancel1.Name = "buttonCancel1";
			this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
			this.buttonCancel1.TabIndex = 98;
			this.buttonCancel1.Text = "buttonCancel1";
			this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
			// 
			// EditMethodInterfaceForm
			// 
			this.AcceptButton = this.button1;
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CancelButton = this.button2;
			this.ClientSize = new System.Drawing.Size(1010, 629);
			this.Controls.Add(this.buttonCancel1);
			this.Controls.Add(this.buttonOk1);
			this.Controls.Add(this.checkBox1);
			this.Controls.Add(this.button2);
			this.Controls.Add(this.button1);
			this.Controls.Add(this.panel1);
			this.Controls.Add(this.dataGridView1);
			this.Controls.Add(this.label1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "EditMethodInterfaceForm";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "编辑当前的设备的方法接口";
			this.Load += new System.EventHandler(this.EditMethodInterfaceForm_Load);
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.DataGridView dataGridView1;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.TextBox textBox_address;
		private Controls.EdgeLabel edgeLabel2;
		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.Button button2;
		private System.Windows.Forms.Button button1;
		private System.Windows.Forms.CheckBox checkBox1;
		private System.Windows.Forms.DataGridViewTextBoxColumn Column_method_name;
		private System.Windows.Forms.DataGridViewCheckBoxColumn Column_remote_call;
		private ButtonOk buttonOk1;
		private ButtonCancel buttonCancel1;
	}
}