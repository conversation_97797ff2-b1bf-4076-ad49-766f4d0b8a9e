using HslCommunication;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.EdgeViewer.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using HslCommunication.MQTT;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.Node;

namespace HslTechnology.EdgeViewer.Request
{
	public partial class FormAlarmDatabase : HslForm
	{
		public FormAlarmDatabase( EdgeServerSettings serverSettings, string[] databases, AlarmDatabaseNode databaseRequest )
		{
			InitializeComponent( );

			this.edgeServerSettings = serverSettings;
			this.databaseRequest    = databaseRequest ?? new AlarmDatabaseNode( );
			this.databases          = databases ?? new string[0];
			this.Text               = "报警存储数据库操作";
			this.Icon               = Util.ConvertToIcon( Util.GetImageByGroupNode( serverSettings, this.databaseRequest ) );
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button_ok.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button_cancel.PerformClick( );
		}

		private void FormDatabaseRequest_Load( object sender, EventArgs e )
		{
			button_ok.Click += Button_ok_Click;

			comboBox_database.DataSource = this.databases;
			if (this.databaseRequest != null)
			{
				textBox_name.Text              = this.databaseRequest.Name;
				textBox_description.Text       = this.databaseRequest.Description;

				if (this.databases.Contains( this.databaseRequest.Database ))
					comboBox_database.SelectedItem = this.databaseRequest.Database;

				if (this.databaseRequest.ExecuteMoment == 1)
					radioButton_alarm_start.Checked = true;
				else
					radioButton_alarm_finish.Checked = true;

				if (this.databaseRequest.DegreeFormate == 1)
					radioButton_degree_number.Checked = true;
				else
					radioButton_degree_string.Checked = true;
				textBox_sql.Text = this.databaseRequest.SqlCommand;
			}

			AlarmItem alarmItem = new AlarmItem( );

			HslTechnology.Edge.Controls.HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, 9 );


			dataGridView1.Rows[0].Cells[0].Value = nameof( AlarmItem.UniqueId );
			dataGridView1.Rows[0].Cells[1].Value = DataType.Int64.ToString( );
			dataGridView1.Rows[0].Cells[2].Value = "报警的唯一ID信息";

			dataGridView1.Rows[1].Cells[0].Value = nameof( AlarmItem.AlarmCode );
			dataGridView1.Rows[1].Cells[1].Value = DataType.Int32.ToString( );
			dataGridView1.Rows[1].Cells[2].Value = "报警的代号信息";

			dataGridView1.Rows[2].Cells[0].Value = nameof( AlarmItem.AlarmContent );
			dataGridView1.Rows[2].Cells[1].Value = DataType.String.ToString( );
			dataGridView1.Rows[2].Cells[2].Value = "报警的详细内容";

			dataGridView1.Rows[3].Cells[0].Value = nameof( AlarmItem.StartTime );
			dataGridView1.Rows[3].Cells[1].Value = DataType.DateTime.ToString( );
			dataGridView1.Rows[3].Cells[2].Value = "报警开始时间";

			dataGridView1.Rows[4].Cells[0].Value = nameof( AlarmItem.FinishTime );
			dataGridView1.Rows[4].Cells[1].Value = DataType.DateTime.ToString( );
			dataGridView1.Rows[4].Cells[2].Value = "报警结束时间，报警结束状态有效";

			dataGridView1.Rows[5].Cells[0].Value = nameof( AlarmItem.Degree );
			dataGridView1.Rows[5].Cells[1].Value = DataType.Int32.ToString( );
			dataGridView1.Rows[5].Cells[2].Value = "报警等级，1:Hint,2:Warn,3:Error,4:Fatal";

			dataGridView1.Rows[6].Cells[0].Value = nameof( AlarmItem.Status );
			dataGridView1.Rows[6].Cells[1].Value = DataType.String.ToString( );
			dataGridView1.Rows[6].Cells[2].Value = "报警状态，Alarm,Finish,分别为报警中，结束";

			dataGridView1.Rows[7].Cells[0].Value = nameof( AlarmItem.DeviceName );
			dataGridView1.Rows[7].Cells[1].Value = DataType.String.ToString( );
			dataGridView1.Rows[7].Cells[2].Value = "报警关联的设备名称信息";

			dataGridView1.Rows[8].Cells[0].Value = nameof( AlarmItem.TagName );
			dataGridView1.Rows[8].Cells[1].Value = DataType.String.ToString( );
			dataGridView1.Rows[8].Cells[2].Value = "报警关联的数据标签名";


		}

		private void Button_ok_Click( object sender, EventArgs e )
		{
			// 点击了确认，需要另生成一个对象处理
			if (comboBox_database.SelectedItem == null)
			{
				MessageBox.Show( "当前没有选择数据库，请先在网关里创建数据库对象信息。" );
				return;
			}

			AlarmDatabaseNode request = new AlarmDatabaseNode( );
			request.Name        = textBox_name.Text;
			request.Description = textBox_description.Text;
			request.Database    = comboBox_database.SelectedItem.ToString( );
			request.SqlCommand = textBox_sql.Text;
			if (radioButton_alarm_start.Checked)
				request.ExecuteMoment = 1;
			else
				request.ExecuteMoment = 2;

			if (radioButton_degree_number.Checked)
				request.DegreeFormate = 1;
			else
				request.DegreeFormate = 2;

			this.databaseRequest = request;
			DialogResult = DialogResult.OK;
		}

		public AlarmDatabaseNode DatabaseRequestNode => this.databaseRequest;

		private EdgeServerSettings edgeServerSettings;
		private AlarmDatabaseNode databaseRequest;
		private string[] databases;
	}
}
