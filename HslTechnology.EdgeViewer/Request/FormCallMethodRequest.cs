using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication.Core;
using HslCommunication;
using HslCommunication.Reflection;
using HslCommunication.MQTT;
using HslTechnology.EdgeViewer.Core;
using Newtonsoft.Json.Linq;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Request
{
	public partial class FormCallMethodRequest : HslForm
	{
		public FormCallMethodRequest(
			EdgeServerSettings serverSettings,
			DeviceNode deviceNode,
			MqttRpcApiInfo[] rpcApiInfos,
			CallMethodRequest methodRequest = null )
		{
			InitializeComponent( );

			this.serverSettings = serverSettings;
			this.deviceNode     = deviceNode;
			this.rpcApiInfos    = rpcApiInfos;
			this.methodRequest  = methodRequest ?? new CallMethodRequest( );
			this.Icon = Util.ConvertToIcon( Util.GetImageByGroupNode( serverSettings, this.deviceNode ) );
		}


		private void FormCallMethodRequest_Load( object sender, EventArgs e )
		{
			treeView1.ImageList = this.serverSettings.NodeImages.DeviceImageList.ImageList;

			if (this.rpcApiInfos != null)
			{
				foreach (var rpc in this.rpcApiInfos)
				{
					TreeNode node = new TreeNode( rpc.ApiTopic );
					node.SetImageKey( Util.GetImageKeyByGroupNode( serverSettings, this.methodRequest ) );
					node.Tag = rpc;

					treeView1.Nodes.Add( node );
				}
			}

			if (this.methodRequest != null)
			{
				textBox_name.Text = this.methodRequest.Name;
				textBox_description.Text = this.methodRequest.Description;

				textBox_address.Text = this.methodRequest.Address;
				if(rpcApiInfos!=null )
				{
					MqttRpcApiInfo rpc = rpcApiInfos.FirstOrDefault( m => m.ApiTopic == this.methodRequest.Address );
					if (rpc != null)
					{
						// 显示方法签名及注释
						label8.Text = rpc.Description;
						label5.Text = rpc.MethodSignature;
						if (!string.IsNullOrEmpty( this.methodRequest.ParameterJson ))
							rpc.ExamplePayload = this.methodRequest.ParameterJson;
					}
				}

				checkBox_enableRequest.Checked = this.methodRequest.Enable;
				requestDataAndCycleControl1.ShowRequestInfo( this.methodRequest );

				checkBox_storeToDevice.Checked = this.methodRequest.StoreResultToDeviceData;
				textBox_parameter.Text = this.methodRequest.ParameterJson;

				textBox_dataUint.Text = this.methodRequest.StoreResultUnit;
			}

			treeView1.AfterSelect += TreeView1_AfterSelect;
		}

		private void TreeView1_AfterSelect( object sender, TreeViewEventArgs e )
		{
			// 选择切换了方法
			TreeNode select = treeView1.SelectedNode;
			if (select == null) return;

			if(select.Tag is MqttRpcApiInfo rpc)
			{
				textBox_address.Text = rpc.ApiTopic;
				label5.Text = rpc.MethodSignature;
				label8.Text = rpc.Description;

				textBox_parameter.Text = rpc.ExamplePayload;
			}
		}

		private void button1_Click( object sender, EventArgs e )
		{
			// 点击了确认，需要另生成一个对象处理
			if (string.IsNullOrEmpty( textBox_address.Text ))
			{
				MessageBox.Show( "当前没有选择方法，请在左侧选择需要调用的方法。" );
				return;
			}

			CallMethodRequest request = new CallMethodRequest( );
			request.Name              = textBox_name.Text;
			request.Description       = textBox_description.Text;
			request.Address           = textBox_address.Text;
			request.Enable            = checkBox_enableRequest.Checked;
			OperateResult write       = this.requestDataAndCycleControl1.GetRequestInfo( request );
			if (!write.IsSuccess)
			{
				MessageBox.Show( write.Message );
				return;
			}

			request.StoreResultToDeviceData = checkBox_storeToDevice.Checked;
			request.StoreResultUnit = textBox_dataUint.Text;
			try
			{
				if (!string.IsNullOrEmpty( textBox_parameter.Text ))
					JObject.Parse( textBox_parameter.Text );
			}
			catch
			{
				MessageBox.Show( "参数信息不是标准的json格式，请重新输入！" );
				return;
			}
			request.ParameterJson = textBox_parameter.Text;

			MethodRequest = request;
			DialogResult = DialogResult.OK;
		}





		public CallMethodRequest MethodRequest { get; set; }


		private DeviceNode deviceNode;
		private CallMethodRequest methodRequest;
		private MqttRpcApiInfo[] rpcApiInfos;
		private EdgeServerSettings serverSettings;

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}
	}
}
