using HslTechnology.Edge.Controls.Basic;

namespace HslTechnology.EdgeViewer.Request
{
	partial class FormDatabaseRequest
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
			System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
			this.comboBox_database = new System.Windows.Forms.ComboBox();
			this.textBox_device_name = new System.Windows.Forms.TextBox();
			this.textBox_sql = new System.Windows.Forms.TextBox();
			this.button_ok = new System.Windows.Forms.Button();
			this.button_cancel = new System.Windows.Forms.Button();
			this.buttonCancel1 = new HslTechnology.Edge.Controls.ButtonCancel();
			this.buttonOk1 = new HslTechnology.Edge.Controls.ButtonOk();
			this.label1 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.textBox_name = new System.Windows.Forms.TextBox();
			this.label4 = new System.Windows.Forms.Label();
			this.textBox_description = new System.Windows.Forms.TextBox();
			this.label5 = new System.Windows.Forms.Label();
			this.checkBox_enableRequest = new System.Windows.Forms.CheckBox();
			this.label6 = new System.Windows.Forms.Label();
			this.label_status = new System.Windows.Forms.Label();
			this.requestDataAndCycleControl1 = new HslTechnology.EdgeViewer.Controls.NodeSettings.RequestCycleControl();
			this.edgeLabel2 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.edgeLabel1 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.edgeLabel3 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.column_datatype = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.column_name = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.dataGridView1 = new HslTechnology.Edge.Controls.Basic.HslDataGridView();
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
			this.SuspendLayout();
			// 
			// comboBox_database
			// 
			this.comboBox_database.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.comboBox_database.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBox_database.FormattingEnabled = true;
			this.comboBox_database.Location = new System.Drawing.Point(446, 264);
			this.comboBox_database.Name = "comboBox_database";
			this.comboBox_database.Size = new System.Drawing.Size(394, 25);
			this.comboBox_database.TabIndex = 81;
			// 
			// textBox_device_name
			// 
			this.textBox_device_name.Location = new System.Drawing.Point(12, 38);
			this.textBox_device_name.Name = "textBox_device_name";
			this.textBox_device_name.ReadOnly = true;
			this.textBox_device_name.Size = new System.Drawing.Size(262, 23);
			this.textBox_device_name.TabIndex = 83;
			// 
			// textBox_sql
			// 
			this.textBox_sql.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox_sql.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.textBox_sql.Location = new System.Drawing.Point(293, 313);
			this.textBox_sql.Multiline = true;
			this.textBox_sql.Name = "textBox_sql";
			this.textBox_sql.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
			this.textBox_sql.Size = new System.Drawing.Size(547, 182);
			this.textBox_sql.TabIndex = 85;
			// 
			// button_ok
			// 
			this.button_ok.Location = new System.Drawing.Point(-417, 403);
			this.button_ok.Name = "button_ok";
			this.button_ok.Size = new System.Drawing.Size(83, 28);
			this.button_ok.TabIndex = 86;
			this.button_ok.Text = "button_ok";
			this.button_ok.UseVisualStyleBackColor = true;
			// 
			// button_cancel
			// 
			this.button_cancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button_cancel.Location = new System.Drawing.Point(-517, 404);
			this.button_cancel.Name = "button_cancel";
			this.button_cancel.Size = new System.Drawing.Size(106, 27);
			this.button_cancel.TabIndex = 87;
			this.button_cancel.Text = "button_cancel";
			this.button_cancel.UseVisualStyleBackColor = true;
			// 
			// buttonCancel1
			// 
			this.buttonCancel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonCancel1.Location = new System.Drawing.Point(585, 501);
			this.buttonCancel1.Name = "buttonCancel1";
			this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
			this.buttonCancel1.TabIndex = 89;
			this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
			// 
			// buttonOk1
			// 
			this.buttonOk1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonOk1.Location = new System.Drawing.Point(446, 501);
			this.buttonOk1.Name = "buttonOk1";
			this.buttonOk1.Size = new System.Drawing.Size(121, 36);
			this.buttonOk1.TabIndex = 88;
			this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(12, 69);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(188, 17);
			this.label1.TabIndex = 90;
			this.label1.Text = "数据列表信息 (从网关运行时请求)";
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(290, 292);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(91, 17);
			this.label2.TabIndex = 91;
			this.label2.Text = "执行的SQL语句";
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(289, 39);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(68, 17);
			this.label3.TabIndex = 92;
			this.label3.Text = "请求名称：";
			// 
			// textBox_name
			// 
			this.textBox_name.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox_name.Location = new System.Drawing.Point(366, 36);
			this.textBox_name.Name = "textBox_name";
			this.textBox_name.Size = new System.Drawing.Size(478, 23);
			this.textBox_name.TabIndex = 93;
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(289, 67);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(68, 17);
			this.label4.TabIndex = 94;
			this.label4.Text = "请求描述：";
			// 
			// textBox_description
			// 
			this.textBox_description.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox_description.Location = new System.Drawing.Point(366, 64);
			this.textBox_description.Name = "textBox_description";
			this.textBox_description.Size = new System.Drawing.Size(478, 23);
			this.textBox_description.TabIndex = 95;
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(289, 269);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(116, 17);
			this.label5.TabIndex = 98;
			this.label5.Text = "选择的数据库信息：";
			// 
			// checkBox_enableRequest
			// 
			this.checkBox_enableRequest.AutoSize = true;
			this.checkBox_enableRequest.Checked = true;
			this.checkBox_enableRequest.CheckState = System.Windows.Forms.CheckState.Checked;
			this.checkBox_enableRequest.Location = new System.Drawing.Point(365, 212);
			this.checkBox_enableRequest.Name = "checkBox_enableRequest";
			this.checkBox_enableRequest.Size = new System.Drawing.Size(123, 21);
			this.checkBox_enableRequest.TabIndex = 100;
			this.checkBox_enableRequest.Text = "启用定时请求调用";
			this.checkBox_enableRequest.UseVisualStyleBackColor = true;
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.Location = new System.Drawing.Point(288, 213);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(68, 17);
			this.label6.TabIndex = 99;
			this.label6.Text = "方法功能：";
			// 
			// label_status
			// 
			this.label_status.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label_status.AutoSize = true;
			this.label_status.Location = new System.Drawing.Point(12, 531);
			this.label_status.Name = "label_status";
			this.label_status.Size = new System.Drawing.Size(44, 17);
			this.label_status.TabIndex = 102;
			this.label_status.Text = "状态：";
			// 
			// requestDataAndCycleControl1
			// 
			this.requestDataAndCycleControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.requestDataAndCycleControl1.BackColor = System.Drawing.Color.Transparent;
			this.requestDataAndCycleControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.requestDataAndCycleControl1.Location = new System.Drawing.Point(286, 97);
			this.requestDataAndCycleControl1.Name = "requestDataAndCycleControl1";
			this.requestDataAndCycleControl1.Size = new System.Drawing.Size(556, 116);
			this.requestDataAndCycleControl1.TabIndex = 97;
			// 
			// edgeLabel2
			// 
			this.edgeLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.edgeLabel2.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel2.ForeColor = System.Drawing.Color.White;
			this.edgeLabel2.Location = new System.Drawing.Point(289, 9);
			this.edgeLabel2.Name = "edgeLabel2";
			this.edgeLabel2.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel2.Size = new System.Drawing.Size(553, 22);
			this.edgeLabel2.TabIndex = 96;
			this.edgeLabel2.Text = "节点信息";
			this.edgeLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// edgeLabel1
			// 
			this.edgeLabel1.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel1.ForeColor = System.Drawing.Color.White;
			this.edgeLabel1.Location = new System.Drawing.Point(12, 9);
			this.edgeLabel1.Name = "edgeLabel1";
			this.edgeLabel1.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel1.Size = new System.Drawing.Size(262, 22);
			this.edgeLabel1.TabIndex = 82;
			this.edgeLabel1.Text = "设备及数据列表";
			this.edgeLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// edgeLabel3
			// 
			this.edgeLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.edgeLabel3.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel3.ForeColor = System.Drawing.Color.White;
			this.edgeLabel3.Location = new System.Drawing.Point(288, 239);
			this.edgeLabel3.Name = "edgeLabel3";
			this.edgeLabel3.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel3.Size = new System.Drawing.Size(552, 22);
			this.edgeLabel3.TabIndex = 80;
			this.edgeLabel3.Text = "数据库信息";
			this.edgeLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// column_datatype
			// 
			this.column_datatype.HeaderText = "数据类型";
			this.column_datatype.Name = "column_datatype";
			this.column_datatype.ReadOnly = true;
			this.column_datatype.Width = 90;
			// 
			// column_name
			// 
			this.column_name.HeaderText = "数据名称";
			this.column_name.Name = "column_name";
			this.column_name.ReadOnly = true;
			this.column_name.Width = 152;
			// 
			// dataGridView1
			// 
			this.dataGridView1.AllowUserToAddRows = false;
			this.dataGridView1.AllowUserToDeleteRows = false;
			this.dataGridView1.AllowUserToResizeRows = false;
			dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(238)))), ((int)(((byte)(238)))), ((int)(((byte)(255)))));
			this.dataGridView1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
			this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
			this.dataGridView1.BackgroundColor = System.Drawing.Color.White;
			this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.column_name,
            this.column_datatype});
			this.dataGridView1.Location = new System.Drawing.Point(12, 89);
			this.dataGridView1.Name = "dataGridView1";
			this.dataGridView1.ReadOnly = true;
			this.dataGridView1.RowHeadersVisible = false;
			this.dataGridView1.RowTemplate.Height = 23;
			this.dataGridView1.Size = new System.Drawing.Size(262, 438);
			this.dataGridView1.TabIndex = 101;
			// 
			// FormDatabaseRequest
			// 
			this.AcceptButton = this.button_ok;
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CancelButton = this.button_cancel;
			this.ClientSize = new System.Drawing.Size(853, 556);
			this.Controls.Add(this.label_status);
			this.Controls.Add(this.dataGridView1);
			this.Controls.Add(this.checkBox_enableRequest);
			this.Controls.Add(this.label6);
			this.Controls.Add(this.label5);
			this.Controls.Add(this.requestDataAndCycleControl1);
			this.Controls.Add(this.edgeLabel2);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.textBox_name);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.textBox_description);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.buttonCancel1);
			this.Controls.Add(this.buttonOk1);
			this.Controls.Add(this.button_cancel);
			this.Controls.Add(this.button_ok);
			this.Controls.Add(this.textBox_sql);
			this.Controls.Add(this.textBox_device_name);
			this.Controls.Add(this.edgeLabel1);
			this.Controls.Add(this.comboBox_database);
			this.Controls.Add(this.edgeLabel3);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "FormDatabaseRequest";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "FormDatabaseRequest";
			this.Load += new System.EventHandler(this.FormDatabaseRequest_Load);
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private Controls.EdgeLabel edgeLabel3;
		private System.Windows.Forms.ComboBox comboBox_database;
		private Controls.EdgeLabel edgeLabel1;
		private System.Windows.Forms.TextBox textBox_device_name;
		private System.Windows.Forms.TextBox textBox_sql;
		private System.Windows.Forms.Button button_ok;
		private System.Windows.Forms.Button button_cancel;
		private Edge.Controls.ButtonCancel buttonCancel1;
		private Edge.Controls.ButtonOk buttonOk1;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Label label2;
		private Controls.EdgeLabel edgeLabel2;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.TextBox textBox_name;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.TextBox textBox_description;
		private Controls.NodeSettings.RequestCycleControl requestDataAndCycleControl1;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.CheckBox checkBox_enableRequest;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label_status;
		private System.Windows.Forms.DataGridViewTextBoxColumn column_datatype;
		private System.Windows.Forms.DataGridViewTextBoxColumn column_name;
		private HslDataGridView dataGridView1;
	}
}