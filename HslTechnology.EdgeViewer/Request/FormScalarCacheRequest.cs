using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.Edge.Config;
using System.Threading;
using HslTechnology.Edge.Reflection;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;
using HslTechnology.EdgeViewer.Core;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormScalarCacheRequest : HslForm
	{
		#region Constructor

		public FormScalarCacheRequest(
			EdgeServerSettings serverSettings,
			DeviceNode deviceNode, 
			ScalarCacheRequest deviceRequest = null,
			List<AlarmNode> alarmNodes = null,
			List<OeeNode> oeeNodes = null
			)
		{
			InitializeComponent( );
			this.Icon           = Util.ConvertToIcon( Util.GetImageByGroupNode( serverSettings, deviceNode ) );
			this.serverSettings = serverSettings;
			this.DeviceRequest  = deviceRequest ?? new ScalarCacheRequest( );
			this.deviceNode     = deviceNode;
			this.alarmNodes     = alarmNodes;
			this.oeeNodes       = oeeNodes;
		}

		#endregion

		#region Form Load
		
		protected override void OnLoad( EventArgs e )
		{
			comboBox_type.DataSource = RegularNodeTypeItem.GetAllRegularNodeTypeItems( withBoolOfByte: false ).Select( m => m.Text ).ToArray( );
			
			comboBox_alarm.DataSource          = this.alarmNodes;
			comboBox_oee.DataSource            = this.oeeNodes;
			comboBox_type.SelectedIndexChanged += ComboBox_type_SelectedIndexChanged;
			checkBox_isArray.CheckedChanged    += CheckBox_isArray_CheckedChanged;
			label4.Visible                     = false;
			textBox_length.Visible             = false;


			comboBox_type.SelectedItem         = DeviceRequest.DataTypeCode;
			checkBox_forbidRemoteWrite.Checked = DeviceRequest.ForbidRemoteWrite;
			checkBox_subscription.Checked      = DeviceRequest.Subscription;
			checkBox_isArray.Checked           = DeviceRequest.Length >= 0;
			textBox_name.Text                  = DeviceRequest.Name;
			textBox_description.Text           = DeviceRequest.Description;
			textBox_displayName.Text           = DeviceRequest.DisplayName;
			textBox_length.Text                = DeviceRequest.Length.ToString( );
			textBox_unit.Text                  = DeviceRequest.Unit;

			if (!string.IsNullOrEmpty(DeviceRequest.AlarmRelate))
			{
				AlarmNode find = this.alarmNodes.Find( m => m.Name == DeviceRequest.AlarmRelate );
				if (find != null)
				{
					checkBox_alarm.Checked = true;
					comboBox_alarm.SelectedItem = find;
				}
			}
			if (!string.IsNullOrEmpty( DeviceRequest.OeeRelate ))
			{
				OeeNode find = this.oeeNodes.Find( m => m.Name == DeviceRequest.OeeRelate );
				if (find != null)
				{
					checkBox_oee.Checked = true;
					comboBox_oee.SelectedItem = find;
				}
			}

			requestDataScaleControl1.ShowScalarTransform( DeviceRequest );
			base.OnLoad( e );
		}

		private void CheckBox_isArray_CheckedChanged( object sender, EventArgs e )
		{
			if (checkBox_isArray.Checked)
			{
				label4.Visible = true;
				textBox_length.Visible = true;
				textBox_length.Text = "1";
			}
			else
			{
				label4.Visible = false;
				textBox_length.Visible = false;
			}
		}

		private void ComboBox_type_SelectedIndexChanged( object sender, EventArgs e )
		{
			// 当选择为字符串的时候，就显示为字符串长度
			string type = comboBox_type.SelectedItem.ToString( );
			if (type == RegularNodeTypeItem.String.Text || type == RegularNodeTypeItem.BCD.Text )
			{
				checkBox_isArray.Visible = false;
				label4.Visible = false;
				textBox_length.Visible = false;
			}
			else
			{
				checkBox_isArray.Visible = true;
				label4.Visible = checkBox_isArray.Checked;
				textBox_length.Visible = checkBox_isArray.Checked;
				label4.Text = "数组长度";
			}
		}

		#endregion

		#region Button Click

		private void button1_Click( object sender, EventArgs e )
		{
			try
			{
				DeviceRequest = new ScalarCacheRequest( )
				{
					Name              = textBox_name.Text,
					Description       = textBox_description.Text,
					DisplayName       = textBox_displayName.Text.Trim( ),
					DataTypeCode      = comboBox_type.SelectedItem.ToString( ),
					ForbidRemoteWrite = checkBox_forbidRemoteWrite.Checked,
					Subscription      = checkBox_subscription.Checked,
					Unit              = textBox_unit.Text
				};
				// 赋值长度信息，字符串和BCD直接赋值长度信息
				if(DeviceRequest.DataTypeCode == RegularNodeTypeItem.String.Text || DeviceRequest.DataTypeCode == RegularNodeTypeItem.BCD.Text)
					DeviceRequest.Length = int.Parse( textBox_length.Text );
				else
					DeviceRequest.Length = checkBox_isArray.Checked ? int.Parse( textBox_length.Text ) : -1;

				OperateResult scale = requestDataScaleControl1.GetScalarTransform( DeviceRequest );
				if (!scale.IsSuccess) { MessageBox.Show( scale.Message ); return; }

				if (checkBox_alarm.Checked)
				{
					AlarmNode select = comboBox_alarm.SelectedItem as AlarmNode;
					OperateResult setAlarm = Util.SetAlarmRelateByAlarmNode( DeviceRequest, select );
					if (!setAlarm.IsSuccess) { MessageBox.Show( setAlarm.Message ); return; }
				}
				if (checkBox_oee.Checked)
				{
					OeeNode select = comboBox_oee.SelectedItem as OeeNode;
					OperateResult setAlarm = Util.SetOeeRelateBy( DeviceRequest, select );
					if (!setAlarm.IsSuccess) { MessageBox.Show( setAlarm.Message ); return; }
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show( "数据填入失败！" + ex.Message );
				return;
			}

			DialogResult = DialogResult.OK;
		}
		#endregion

		#region Private Member

		/// <summary>
		/// 获取或设置当前设备的标量请求对象
		/// </summary>
		public ScalarCacheRequest DeviceRequest { get; set; }
		private DeviceNode deviceNode;
		private List<AlarmNode> alarmNodes = null;
		private List<OeeNode> oeeNodes = null;
		private EdgeServerSettings serverSettings = null;

		#endregion

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}

		private void FormScalarCacheRequest_Load( object sender, EventArgs e )
		{

		}
	}
}
