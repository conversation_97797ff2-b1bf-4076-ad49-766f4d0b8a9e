using HslCommunication.MQTT;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Method;
using HslTechnology.Edge.Node.Request;
using HslTechnology.EdgeViewer.Controls;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Request
{
	public partial class EditMethodInterfaceForm : HslForm
	{
		public EditMethodInterfaceForm( 
			DeviceNode deviceNode,
			MqttRpcApiInfo[] rpcApiInfos,
			MethodConfigNode methodConfig = null )
		{
			InitializeComponent( );
			this.deviceNode = deviceNode;
			this.rpcApiInfos = rpcApiInfos;
			this.methodConfig = methodConfig ?? new MethodConfigNode( );
			this.Icon = Util.ConvertToIcon( Util.GetImageByGroupNode( null, deviceNode ) );
		}

		private void EditMethodInterfaceForm_Load( object sender, EventArgs e )
		{
			// 显示方法列表
			HslTechnology.Edge.Controls.HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, this.rpcApiInfos.Length );
			for (int i = 0; i < this.rpcApiInfos.Length; i++)
			{
				this.dataGridView1.Rows[i].Cells[0].Value = this.rpcApiInfos[i].ApiTopic;
				if (methodConfig.EnableAllMethod)
				{
					this.dataGridView1.Rows[i].Cells[1].Value = true;
				}
				else
				{
					MethodItemNode methodItem = this.methodConfig.MethodItems.FirstOrDefault( m => m.Name == this.rpcApiInfos[i].ApiTopic );
					if (methodItem != null)
					{
						this.dataGridView1.Rows[i].Cells[1].Value = true; 
					}
				}

				this.dataGridView1.Rows[i].Tag = this.rpcApiInfos[i];
			}

			if (methodConfig.EnableAllMethod) this.checkBox1.Checked = true;
			this.dataGridView1.SelectionChanged += DataGridView1_SelectionChanged;
			this.checkBox1.CheckStateChanged    += CheckBox1_CheckStateChanged;
		}

		private void CheckBox1_CheckStateChanged( object sender, EventArgs e )
		{
			if (checkBox1.Checked)
			{
				for (int i = 0; i < this.dataGridView1.RowCount; i++)
				{
					this.dataGridView1.Rows[i].Cells[1].Value = true;
				}
			}
			else
			{
				for (int i = 0; i < this.dataGridView1.RowCount; i++)
				{
					this.dataGridView1.Rows[i].Cells[1].Value = false;
				}
			}
		}



		private void DataGridView1_SelectionChanged( object sender, EventArgs e )
		{
			if (dataGridView1.SelectedRows.Count == 0) return;
			if(dataGridView1.SelectedRows[0].Tag is MqttRpcApiInfo rpc)
			{
				textBox_address.Text = rpc.ApiTopic;
				label5.Text = rpc.MethodSignature;
				label8.Text = rpc.Description;
			}
		}

		private DeviceNode deviceNode;
		private MqttRpcApiInfo[] rpcApiInfos;
		private MethodConfigNode methodConfig;

		public MethodConfigNode MethodConfigNode { get; set; }

		private void button1_Click( object sender, EventArgs e )
		{
			// 确认选择
			MethodConfigNode methodConfig = new MethodConfigNode( );
			methodConfig.Name = "MethodConfig";
			methodConfig.EnableAllMethod = checkBox1.Checked;
			if (!methodConfig.EnableAllMethod)
			{
				for (int i = 0; i < this.dataGridView1.RowCount; i++)
				{
					bool select = this.dataGridView1.Rows[i].Cells[1].Value == null ? false :( bool)this.dataGridView1.Rows[i].Cells[1].Value;
					if (select)
					{
						methodConfig.MethodItems.Add( new MethodItemNode( )
						{
							Name = this.dataGridView1.Rows[i].Cells[0].Value.ToString( )
						} );
					}
				}
			}
			MethodConfigNode = methodConfig;
			DialogResult = DialogResult.OK;
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}
	}
}
