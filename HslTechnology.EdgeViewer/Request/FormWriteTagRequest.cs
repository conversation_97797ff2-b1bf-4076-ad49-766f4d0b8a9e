using HslCommunication;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.EdgeViewer.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using HslCommunication.MQTT;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.Node.Regular;

namespace HslTechnology.EdgeViewer.Request
{
	public partial class FormWriteTagRequest : HslForm
	{
		public FormWriteTagRequest( EdgeServerSettings serverSettings, string nodePath, DeviceNode deviceNode, ScalarWriteRequest writeRequest )
		{
			InitializeComponent( );

			this.edgeServerSettings = serverSettings;
			this.nodePath           = nodePath;
			this.writeRequest       = writeRequest ?? new ScalarWriteRequest( );
			this.deviceNode         = deviceNode;
			this.Text               = "定时写入请求操作";
			this.Icon               = Util.ConvertToIcon( Util.GetImageByGroupNode( serverSettings, this.deviceNode ) );
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button_ok.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button_cancel.PerformClick( );
		}

		private void FormDatabaseRequest_Load( object sender, EventArgs e )
		{
			button_ok.Click += Button_ok_Click;
			checkBox_isArray.Visible = false;
			comboBox_type.DataSource = RegularNodeTypeItem.GetAllRegularNodeTypeItems( withBoolOfByte: false ).Select( m => m.Text ).ToArray( );
			comboBox_type.SelectedIndexChanged += ComboBox_type_SelectedIndexChanged;
			checkBox_IsAbsoluteAddress.CheckedChanged += CheckBox_IsAbsoluteAddress_CheckedChanged;

			textBox_device_name.Text = this.deviceNode?.GetDisplayName( Edge.Node.NodeDisplayMode.ShowCombine );
			//comboBox_database.DataSource = this.databases;
			if (this.writeRequest != null)
			{
				textBox_name.Text                  = this.writeRequest.Name;
				textBox_description.Text           = this.writeRequest.Description;
				comboBox_dataTag.Text              = this.writeRequest.Address;
				requestDataAndCycleControl1.ShowRequestInfo( this.writeRequest );
				textBox_formatValue.Text           = this.writeRequest.FormatValue;
				checkBox_IsAbsoluteAddress.Checked = this.writeRequest.IsAbsoluteAddress;
				comboBox_type.SelectedItem         = this.writeRequest.DataTypeCode;
				checkBox_isArray.Checked           = this.writeRequest.IsArray;
			}
			CheckBox_IsAbsoluteAddress_CheckedChanged( checkBox_IsAbsoluteAddress, e );
			ThreadPool.QueueUserWorkItem( new WaitCallback( ThreadPoolMethod ), null );
		}

		private void ComboBox_type_SelectedIndexChanged( object sender, EventArgs e )
		{
			if (checkBox_IsAbsoluteAddress.Checked)
			{
				if (comboBox_type.SelectedItem.ToString() == RegularNodeTypeItem.Byte.Text)
				{
					checkBox_isArray.Visible = true;
				}
				else
				{
					checkBox_isArray.Visible = false;
				}
			}
		}

		private void CheckBox_IsAbsoluteAddress_CheckedChanged( object sender, EventArgs e )
		{
			if (checkBox_IsAbsoluteAddress.Checked)
			{
				label10.Visible = true;
				comboBox_type.Visible = true;
			}
			else
			{
				label10.Visible = false;
				comboBox_type.Visible = false;
			}
		}

		private async void ThreadPoolMethod( object obj )
		{
			Thread.Sleep( 100 );
			// 加载显示数据标签信息
			if (this.edgeServerSettings == null) return;
			MqttSyncClient rpc = this.edgeServerSettings.GetMqttSyncClient( );
			OperateResult<DeviceSingleAddressLabel[]> read = await rpc.ReadRpcAsync<DeviceSingleAddressLabel[]>( "Admin/GetDeviceAddressLabel", new { data = this.nodePath } );
			try
			{
				Invoke( new Action( ( ) =>
				 {
					 if (read.IsSuccess)
					 {
						 HslTechnology.Edge.Controls.HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, read.Content.Length );
						 for (int i = 0; i < read.Content.Length; i++)
						 {
							 dataGridView1.Rows[i].Cells[0].Value = read.Content[i].Name;
							 dataGridView1.Rows[i].Cells[1].Value = read.Content[i].DataType;
						 }
						 if (read.Content.Length == 0) label_status.Text = "状态：当前没有数据属性，请下载配置重启网关后，尝试";
						 else label_status.Text = $"状态：请求成功，共计数据：{read.Content.Length} 个";

						 string oldValue = comboBox_dataTag.Text;
						 comboBox_dataTag.DataSource = read.Content.Select( x => x.Name ).ToArray( );
						 comboBox_dataTag.Text = oldValue;
					 }
					 else
					 {
						 label_status.Text = "操作失败：" + read.Message + Environment.NewLine + "请下载配置重启网关后，尝试";
					 }
				 } ) );
			}
			catch
			{

			}
		}

		private void Button_ok_Click( object sender, EventArgs e )
		{
			// 点击了确认，需要另生成一个对象处理
			if (string.IsNullOrEmpty(comboBox_dataTag.Text ))
			{
				MessageBox.Show( "当前没有选择数据标签，请输入一个数据标签。" );
				return;
			}

			ScalarWriteRequest request = new ScalarWriteRequest( );
			request.Name               = textBox_name.Text;
			request.Description        = textBox_description.Text;
			request.Address            = comboBox_dataTag.Text;
			request.IsAbsoluteAddress  = checkBox_IsAbsoluteAddress.Checked;
			if (request.IsAbsoluteAddress)
			{
				request.DataTypeCode = comboBox_type.SelectedItem.ToString( );
				request.IsArray      = checkBox_isArray.Checked;
			}
			OperateResult write        = this.requestDataAndCycleControl1.GetRequestInfo( request );
			if (!write.IsSuccess)
			{
				MessageBox.Show( write.Message );
				return;
			}

			request.FormatValue = textBox_formatValue.Text;

			this.writeRequest = request;
			DialogResult = DialogResult.OK;
		}

		public ScalarWriteRequest WriteRequest => this.writeRequest;

		private EdgeServerSettings edgeServerSettings;
		private ScalarWriteRequest writeRequest;
		private DeviceNode deviceNode;
		private string nodePath = string.Empty;
	}
}
