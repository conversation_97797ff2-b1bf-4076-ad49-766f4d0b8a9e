using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication;
using HslTechnology.Edge.Node.Device;
using System.Threading;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Config;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;
using HslTechnology.EdgeViewer.Core;

namespace HslTechnology.EdgeViewer.Forms
{
	public partial class FormSourceDataRequest : HslForm
	{
		#region Constructor

		public FormSourceDataRequest( bool createNew, string path, EdgeServerSettings serverSettings, DeviceNode device, SourceReadRequest deviceRequest = null )
		{
			InitializeComponent( );

			this.serverSettings = serverSettings;
			this.Icon = Util.ConvertToIcon( Util.GetImageByGroupNode( serverSettings, device ) );
			this.DeviceRequest = deviceRequest ?? new SourceReadRequest( );
			this.deviceNode = device;

			string head = createNew ? "新增一个" : "编辑当前";
			this.Text = $"{head}{this.Text} [{path}]";

			if (device.GetType( ) == typeof( NodeTcpFreedom ))
			{
				NodeTcpFreedom nodeTcpFreedom = device as NodeTcpFreedom;
				SetSpecialInstructions( "报文格式( " + (nodeTcpFreedom.UseAsciiFormate ? "ASCII" : "HEX") + " )" + Environment.NewLine +
				"例如: " + (nodeTcpFreedom.UseAsciiFormate ? ":01030064000197\\0D\\0A" : "00 00 00 00 00 06 01 03 00 00 00 01") );

				label5.Text = label5.Text + Environment.NewLine + Environment.NewLine + "地址中间加<split/>可以表示拆分成两次请求，数据在最后一次请求上，例如：01 03 00 C8 00 02 45 F5<split>01 03 00 64 00 01 C5 D5";
				this.Height = this.Height + 70;
			}
			else if (device.GetType( ) == typeof( NodeSerialFreedom ))
			{
				NodeSerialFreedom nodeSerialFreedom = device as NodeSerialFreedom;
				SetSpecialInstructions( "报文格式( " + (nodeSerialFreedom.UseAsciiFormate ? "ASCII" : "HEX") + " )" + Environment.NewLine +
				"例如: " + (nodeSerialFreedom.UseAsciiFormate ? ":01030064000197\\0D\\0A" : "01 03 00 64 00 01 C5 D5") );

				label5.Text = label5.Text + Environment.NewLine + Environment.NewLine + "地址中间加<split/>可以表示拆分成两次请求，数据在最后一次请求上，例如：01 03 00 C8 00 02 45 F5<split>01 03 00 64 00 01 C5 D5";
				this.Height = this.Height + 70;
			}
		}

		public void SetSpecialInstructions( string value ){
			label8.Text = "特别说明: " + value;
		}

		#endregion

		#region Form Load

		protected override void OnLoad( EventArgs e )
		{
			textBox_name.Text             = DeviceRequest.Name;
			textBox_description.Text      = DeviceRequest.Description;
			textBox_address.Text          = DeviceRequest.Address;
			textBox_length.Text           = DeviceRequest.Length.ToString( );
			requestDataAndCycleControl1.ShowRequestInfo( DeviceRequest );
			requestAddressExampleControl1.OnDataGridViewRowMouseDoubleClick += RequestAddressExampleControl1_OnDataGridViewRowMouseDoubleClick;

			if (this.deviceNode.IsSupportAddressBatchRequest( ))
			{
				label6.Visible = true;
				label7.Visible = true;
			}
			else
			{
				label6.Text = "地址可以指定网关其他设备的原始字节请求\r\n使用var=开头，例如：var=工厂1/设备一/原始字节请求";
				label6.Visible = true;
				textBox_length.Width = textBox_address.Width;
			}
			base.OnLoad( e );
		}

		private void RequestAddressExampleControl1_OnDataGridViewRowMouseDoubleClick( Edge.Node.Device.DeviceAddressExample addressExample )
		{
			textBox_address.Text = addressExample.AddressExample;
		}

		protected override void OnShown( EventArgs e )
		{
			base.OnShown( e );
			ThreadPool.QueueUserWorkItem( new WaitCallback( ThreadPoolReadExampleAddress ), null );
		}

		private void ThreadPoolReadExampleAddress( object obj )
		{
			if (this.serverSettings != null)
			{
				// 从网关请求数据地址
				var rpc = this.serverSettings.GetMqttSyncClient( );
				var read = rpc.ReadRpc<DeviceAddressExample[]>( "Edge/GetDeviceExampleAddress",
					new { deviceType = this.deviceNode.DeviceType.ToString( ), pluginsType = this.deviceNode.PluginsType } );
				try
				{
					if (IsHandleCreated) Invoke( new Action( ( ) =>
					 {
						 if (read.IsSuccess)
						 {
							 requestAddressExampleControl1.SetDeviceType( read.Content );
						 }
						 else
						 {
							 MessageBox.Show( read.Message );
						 }
					 } ) );
				}
				catch
				{

				}
			}
			else if (this.deviceNode.DeviceType != DeviceType.Plugins)
			{
				try
				{
					if (IsHandleCreated) Invoke( new Action( ( ) =>
					{
						var read = EdgeReflectionHelper.GetDeviceExampleAddress( this.deviceNode.DeviceType, this.deviceNode.PluginsType );
						if (read.IsSuccess)
						{
							requestAddressExampleControl1.SetDeviceType( read.Content );
						}
						else
						{
							MessageBox.Show( read.Message );
						}
					} ) );
				}
				catch
				{

				}
			}
		}



		#endregion

		#region Button Click

		private void button1_Click( object sender, EventArgs e )
		{
			try
			{
				if (this.deviceNode != null)
				{
					OperateResult check = this.deviceNode.CheckBatchRequest( textBox_address.Text );
					if (!check.IsSuccess)
					{
						MessageBox.Show( "地址输入检查失败:" + check.Message );
						return;
					}
				}


				DeviceRequest = new SourceReadRequest( )
				{
					Name         = textBox_name.Text,
					Description  = textBox_description.Text,
					Address      = textBox_address.Text,
					Length       = textBox_length.Text.Contains(";") ? textBox_length.Text : int.Parse( textBox_length.Text ).ToString(),
				};

				OperateResult request = requestDataAndCycleControl1.GetRequestInfo( DeviceRequest );
				if (!request.IsSuccess) { MessageBox.Show( request.Message ); return; }
			}
			catch (Exception ex)
			{
				MessageBox.Show( "数据填入失败！" + ex.Message );
				return;
			}

			DialogResult = DialogResult.OK;
		}
		#endregion

		#region Private Member

		/// <summary>
		/// 获取或设置当前设备的标量请求对象
		/// </summary>
		public SourceReadRequest DeviceRequest { get; set; }
		private DeviceNode deviceNode;
		private EdgeServerSettings serverSettings = null;

		#endregion

		private void FormSourceDataRequest_Load( object sender, EventArgs e )
		{

		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}
	}
}
