using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Forms
{
    partial class FormSourceDataRequest
	{
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose( bool disposing )
        {
            if (disposing && (components != null))
            {
                components.Dispose( );
            }
            base.Dispose( disposing );
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent( )
        {
			this.textBox_length = new System.Windows.Forms.TextBox();
			this.label4 = new System.Windows.Forms.Label();
			this.textBox_address = new System.Windows.Forms.TextBox();
			this.label3 = new System.Windows.Forms.Label();
			this.textBox_description = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.textBox_name = new System.Windows.Forms.TextBox();
			this.label1 = new System.Windows.Forms.Label();
			this.button2 = new System.Windows.Forms.Button();
			this.button1 = new System.Windows.Forms.Button();
			this.label5 = new System.Windows.Forms.Label();
			this.panel1 = new System.Windows.Forms.Panel();
			this.label8 = new System.Windows.Forms.Label();
			this.label7 = new System.Windows.Forms.Label();
			this.label6 = new System.Windows.Forms.Label();
			this.edgeLabel1 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.edgeLabel2 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.requestDataAndCycleControl1 = new HslTechnology.EdgeViewer.Controls.NodeSettings.RequestCycleControl();
			this.requestAddressExampleControl1 = new HslTechnology.Edge.Controls.Machine.RequestAddressExampleControl();
			this.buttonOk1 = new HslTechnology.Edge.Controls.ButtonOk();
			this.buttonCancel1 = new HslTechnology.Edge.Controls.ButtonCancel();
			this.edgeLabel3 = new HslTechnology.EdgeViewer.Controls.EdgeLabel();
			this.panel1.SuspendLayout();
			this.SuspendLayout();
			// 
			// textBox_length
			// 
			this.textBox_length.Location = new System.Drawing.Point(80, 206);
			this.textBox_length.Name = "textBox_length";
			this.textBox_length.Size = new System.Drawing.Size(186, 23);
			this.textBox_length.TabIndex = 54;
			this.textBox_length.Text = "1";
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(3, 209);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(68, 17);
			this.label4.TabIndex = 53;
			this.label4.Text = "地址长度：";
			// 
			// textBox_address
			// 
			this.textBox_address.Location = new System.Drawing.Point(80, 137);
			this.textBox_address.Name = "textBox_address";
			this.textBox_address.Size = new System.Drawing.Size(320, 23);
			this.textBox_address.TabIndex = 52;
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(3, 140);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(68, 17);
			this.label3.TabIndex = 51;
			this.label3.Text = "请求地址：";
			// 
			// textBox_description
			// 
			this.textBox_description.Location = new System.Drawing.Point(80, 70);
			this.textBox_description.Name = "textBox_description";
			this.textBox_description.Size = new System.Drawing.Size(320, 23);
			this.textBox_description.TabIndex = 50;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(3, 73);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(68, 17);
			this.label2.TabIndex = 49;
			this.label2.Text = "请求描述：";
			// 
			// textBox_name
			// 
			this.textBox_name.Location = new System.Drawing.Point(80, 37);
			this.textBox_name.Name = "textBox_name";
			this.textBox_name.Size = new System.Drawing.Size(320, 23);
			this.textBox_name.TabIndex = 48;
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(3, 40);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(68, 17);
			this.label1.TabIndex = 47;
			this.label1.Text = "请求名称：";
			// 
			// button2
			// 
			this.button2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button2.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Critical_16xLG_color;
			this.button2.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
			this.button2.Location = new System.Drawing.Point(-215, 529);
			this.button2.Name = "button2";
			this.button2.Size = new System.Drawing.Size(143, 43);
			this.button2.TabIndex = 66;
			this.button2.Text = " 取消";
			this.button2.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.button2.UseVisualStyleBackColor = true;
			// 
			// button1
			// 
			this.button1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.button1.BackColor = System.Drawing.Color.Transparent;
			this.button1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color;
			this.button1.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
			this.button1.Location = new System.Drawing.Point(-215, 480);
			this.button1.Name = "button1";
			this.button1.Size = new System.Drawing.Size(143, 43);
			this.button1.TabIndex = 65;
			this.button1.Text = " 确认";
			this.button1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.button1.UseVisualStyleBackColor = false;
			this.button1.Click += new System.EventHandler(this.button1_Click);
			// 
			// label5
			// 
			this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
			this.label5.ForeColor = System.Drawing.Color.Fuchsia;
			this.label5.Location = new System.Drawing.Point(3, 392);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(399, 73);
			this.label5.TabIndex = 71;
			this.label5.Text = "注意：\r\n原始数据请求不在数据界面显示，主要用来自定义及结构体数据的解析，当前的对象也可以作为其他Machine设备的数据来源";
			// 
			// panel1
			// 
			this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.panel1.Controls.Add(this.label8);
			this.panel1.Controls.Add(this.label7);
			this.panel1.Controls.Add(this.label6);
			this.panel1.Controls.Add(this.edgeLabel1);
			this.panel1.Controls.Add(this.label5);
			this.panel1.Controls.Add(this.label1);
			this.panel1.Controls.Add(this.textBox_name);
			this.panel1.Controls.Add(this.label2);
			this.panel1.Controls.Add(this.textBox_description);
			this.panel1.Controls.Add(this.label3);
			this.panel1.Controls.Add(this.textBox_address);
			this.panel1.Controls.Add(this.edgeLabel2);
			this.panel1.Controls.Add(this.label4);
			this.panel1.Controls.Add(this.textBox_length);
			this.panel1.Controls.Add(this.requestDataAndCycleControl1);
			this.panel1.Location = new System.Drawing.Point(651, 9);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(406, 465);
			this.panel1.TabIndex = 72;
			// 
			// label8
			// 
			this.label8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(64)))), ((int)(((byte)(0)))));
			this.label8.Location = new System.Drawing.Point(4, 236);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(396, 37);
			this.label8.TabIndex = 74;
			this.label8.Text = "特别说明:";
			// 
			// label7
			// 
			this.label7.AutoSize = true;
			this.label7.ForeColor = System.Drawing.Color.Gray;
			this.label7.Location = new System.Drawing.Point(272, 209);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(125, 17);
			this.label7.TabIndex = 73;
			this.label7.Text = "长度支持数组，\';\'分割";
			this.label7.Visible = false;
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.ForeColor = System.Drawing.Color.Gray;
			this.label6.Location = new System.Drawing.Point(4, 165);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(353, 34);
			this.label6.TabIndex = 72;
			this.label6.Text = "地址支持数组，\';\'分割，也可以指定网关其他设备的原始字节请求\r\n使用var=开头，例如：var=工厂1/设备一/原始字节请求";
			this.label6.Visible = false;
			// 
			// edgeLabel1
			// 
			this.edgeLabel1.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel1.ForeColor = System.Drawing.Color.White;
			this.edgeLabel1.Location = new System.Drawing.Point(3, 3);
			this.edgeLabel1.Name = "edgeLabel1";
			this.edgeLabel1.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel1.Size = new System.Drawing.Size(397, 22);
			this.edgeLabel1.TabIndex = 60;
			this.edgeLabel1.Text = "节点信息";
			this.edgeLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// edgeLabel2
			// 
			this.edgeLabel2.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel2.ForeColor = System.Drawing.Color.White;
			this.edgeLabel2.Location = new System.Drawing.Point(3, 110);
			this.edgeLabel2.Name = "edgeLabel2";
			this.edgeLabel2.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel2.Size = new System.Drawing.Size(397, 22);
			this.edgeLabel2.TabIndex = 61;
			this.edgeLabel2.Text = "地址信息";
			this.edgeLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// requestDataAndCycleControl1
			// 
			this.requestDataAndCycleControl1.BackColor = System.Drawing.Color.Transparent;
			this.requestDataAndCycleControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.requestDataAndCycleControl1.Location = new System.Drawing.Point(0, 275);
			this.requestDataAndCycleControl1.Name = "requestDataAndCycleControl1";
			this.requestDataAndCycleControl1.Size = new System.Drawing.Size(402, 119);
			this.requestDataAndCycleControl1.TabIndex = 63;
			// 
			// requestAddressExampleControl1
			// 
			this.requestAddressExampleControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.requestAddressExampleControl1.BackColor = System.Drawing.Color.AliceBlue;
			this.requestAddressExampleControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.requestAddressExampleControl1.Location = new System.Drawing.Point(11, 46);
			this.requestAddressExampleControl1.Name = "requestAddressExampleControl1";
			this.requestAddressExampleControl1.Size = new System.Drawing.Size(624, 428);
			this.requestAddressExampleControl1.TabIndex = 69;
			// 
			// buttonOk1
			// 
			this.buttonOk1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonOk1.Location = new System.Drawing.Point(391, 499);
			this.buttonOk1.Name = "buttonOk1";
			this.buttonOk1.Size = new System.Drawing.Size(121, 36);
			this.buttonOk1.TabIndex = 73;
			this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
			// 
			// buttonCancel1
			// 
			this.buttonCancel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonCancel1.Location = new System.Drawing.Point(559, 499);
			this.buttonCancel1.Name = "buttonCancel1";
			this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
			this.buttonCancel1.TabIndex = 74;
			this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
			// 
			// edgeLabel3
			// 
			this.edgeLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.edgeLabel3.BackColor = System.Drawing.Color.DodgerBlue;
			this.edgeLabel3.ForeColor = System.Drawing.Color.White;
			this.edgeLabel3.Location = new System.Drawing.Point(12, 12);
			this.edgeLabel3.Name = "edgeLabel3";
			this.edgeLabel3.Padding = new System.Windows.Forms.Padding(5, 0, 0, 0);
			this.edgeLabel3.Size = new System.Drawing.Size(623, 22);
			this.edgeLabel3.TabIndex = 70;
			this.edgeLabel3.Text = "地址示例";
			this.edgeLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// FormSourceDataRequest
			// 
			this.AcceptButton = this.button1;
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CancelButton = this.button2;
			this.ClientSize = new System.Drawing.Size(1067, 553);
			this.Controls.Add(this.buttonCancel1);
			this.Controls.Add(this.buttonOk1);
			this.Controls.Add(this.panel1);
			this.Controls.Add(this.edgeLabel3);
			this.Controls.Add(this.requestAddressExampleControl1);
			this.Controls.Add(this.button2);
			this.Controls.Add(this.button1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
			this.MaximizeBox = false;
			this.Name = "FormSourceDataRequest";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "原始数据请求节点";
			this.Load += new System.EventHandler(this.FormSourceDataRequest_Load);
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.TextBox textBox_length;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox textBox_address;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox textBox_description;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox textBox_name;
        private System.Windows.Forms.Label label1;
		private Controls.EdgeLabel edgeLabel1;
		private Controls.EdgeLabel edgeLabel2;
		private Controls.NodeSettings.RequestCycleControl requestDataAndCycleControl1;
		private System.Windows.Forms.Button button1;
		private System.Windows.Forms.Button button2;
        private Controls.EdgeLabel edgeLabel3;
        private HslTechnology.Edge.Controls.Machine.RequestAddressExampleControl requestAddressExampleControl1;
        private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Panel panel1;
		private ButtonOk buttonOk1;
		private ButtonCancel buttonCancel1;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label8;
	}
}