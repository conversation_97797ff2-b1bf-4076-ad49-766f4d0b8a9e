using HslCommunication.BasicFramework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer
{
	static class Program
	{
		/// <summary>
		/// 应用程序的主入口点。
		/// </summary>
		[STAThread]
		static void Main( )
		{
			// if (!HslControls.Authorization.SetAuthorizationCode( "Your code" )) MessageBox.Show( "HslContonls 授权失败！" );
			// if (!HslCommunication.Authorization.SetAuthorizationCode( "Your code" )) MessageBox.Show( "HslCommunication 授权失败！" );
			// if (!HslTechnology.Edge.Authorization.SetAuthorizationCode( "Your code" )) MessageBox.Show( "HslTechnology.Edge 授权失败!" );

			AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
			Util.ViewerSettings = new ViewerSettings( Util.GetFileFullPath( "ViewerSettings.txt" ) );

			Application.EnableVisualStyles( );
			Application.SetCompatibleTextRenderingDefault( false );
			Application.Run( new FormMain( ) );
		}

		private static void CurrentDomain_UnhandledException( object sender, UnhandledExceptionEventArgs e )
		{
			if (e.ExceptionObject is Exception ex)
			{
				File.WriteAllText( Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "UnhandledException.txt" ),
					SoftBasic.GetExceptionMessage( ex ), Encoding.UTF8 );
			}
		}
	}
}
