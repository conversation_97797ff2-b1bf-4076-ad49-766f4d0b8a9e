<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="refresh_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\refresh_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="efort" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\efort.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Cloud_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Cloud_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="house_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\house_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="VirtualMachine_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\VirtualMachine_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="action_create_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\action_create_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="websocket" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\websocket.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_Forward_color_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_Forward_color_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="xinje" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\xinje.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="HotSpot_10548" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\HotSpot_10548.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="beckhoff" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\beckhoff.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="inovance" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\inovance.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_up_color_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_up_color_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Refresh" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Refresh.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="zkt" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\zkt.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Property_501" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Property_501.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="save_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\save_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Operator_660" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Operator_660.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="XMLFile_828_16x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\XMLFile_828_16x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="FlagRed_16x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\FlagRed_16x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Method_636" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Method_636.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="schneider" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\schneider.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Security_Shields_Critical_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Security_Shields_Critical_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Enum_582" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Enum_582.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="JsonFile" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\JsonFile.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Event_594_exp" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Event_594_exp.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Library" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Library.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="FileSystemEditor_5852" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\FileSystemEditor_5852.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Event_594" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Event_594.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Database_node" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Database_node.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="panasonic" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\panasonic.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ClassIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\ClassIcon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="AppWindow" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\AppWindow.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="fujifilm" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\fujifilm.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Log" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Log.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="build_Selection_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\build_Selection_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="StatusAnnotations_Information_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\StatusAnnotations_Information_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="StatusAnnotations_Complete_and_ok_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\StatusAnnotations_Complete_and_ok_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="NewFile_6276" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\NewFile_6276.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="StatusAnnotations_Help_and_inconclusive_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\StatusAnnotations_Help_and_inconclusive_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Arrow_ImportOrLoad_16x_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Arrow_ImportOrLoad_16x_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="brackets_Curly_16xMD" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\brackets_Curly_16xMD.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ab" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\ab.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="library_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\library_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="debug" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\debug.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_Down_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_Down_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Play" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Play.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="graph_highPerformanceComputingChart_5170_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\graph_highPerformanceComputingChart_5170_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="siemens" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\siemens.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="toledo" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\toledo.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="StatusAnnotations_Pause_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\StatusAnnotations_Pause_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="abstr1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\abstr1.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="keyence" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\keyence.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_open_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_open_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="PencilAngled_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\PencilAngled_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="WindowsAzure_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\WindowsAzure_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="PencilAngled_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\PencilAngled_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ge" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\ge.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="xbox1Color_16x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\xbox1Color_16x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="save_server_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\save_server_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="SerialPort" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\SerialPort.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="XmlFile" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\XmlFile.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="mqtt" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\mqtt.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="oee" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\oee.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="melsec" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\melsec.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="yokogawa" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\yokogawa.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="hardware_16xLG_cancel" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\hardware_16xLG_cancel.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="glasses_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\glasses_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Security_Shields_Alert_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Security_Shields_Alert_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="yaskawa" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\yaskawa.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="kuka" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\kuka.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="action_Cancel_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\action_Cancel_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Class_489" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Class_489.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="star_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\star_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="water" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\water.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Bubble_16xMD" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Bubble_16xMD.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="vigor" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\vigor.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="redis" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\redis.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Online" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Online.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="graph_performanceChart_5171_16x_LG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\graph_performanceChart_5171_16x_LG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ExtensionManager_vsix" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\ExtensionManager_vsix.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_Previous_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_Previous_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="hardware_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\hardware_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ArrangeWindowsHS" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\ArrangeWindowsHS.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="usbcontroller" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\usbcontroller.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="AddClass" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\AddClass.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="move_to_bottom" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\move_to_bottom.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="FieldsHeader_12x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\FieldsHeader_12x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="CloudDownload" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\CloudDownload.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ShutDown" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\ShutDown.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="fanuc" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\fanuc.jpg;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="FlagSpace_16x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\FlagSpace_16x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="json" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\json.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Folder_Open" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Folder_Open.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="WindowsAzure_16xLG_Cyan" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\WindowsAzure_16xLG_Cyan.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="omron" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\omron.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="HotSpot_10548_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\HotSpot_10548_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="delta" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\delta.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="HslCommunication4" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\HslCommunication4.jpg;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Pause" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Pause.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_Up_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_Up_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Enum_582_cancel" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Enum_582_cancel.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="download" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\download.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="fatek" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\fatek.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="robot" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\robot.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="brackets_Square_16xMD" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\brackets_Square_16xMD.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Copy_6524" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Copy_6524.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="HslCommunication" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\HslCommunication.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="SpherePreview" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\SpherePreview.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="NetworkAdapter" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\NetworkAdapter.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_down_color_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_down_color_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Database_request" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Database_request.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Monitor_Screen_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Monitor_Screen_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="modbus" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\modbus.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="FileGroup_10135_16x" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\FileGroup_10135_16x.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="StatusAnnotations_Critical_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\StatusAnnotations_Critical_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="EditDocument" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\EditDocument.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="abb" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\abb.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Calculator_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Calculator_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Module_blue" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Module_blue.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="flag_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\flag_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ls" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\ls.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ASCube_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\ASCube_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="move_to_top" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\move_to_top.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_back_color_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_back_color_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="GenericVSEditor_9905" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\GenericVSEditor_9905.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="action_add_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\action_add_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="arrow_Next_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\arrow_Next_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="StatusAnnotations_Warning_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\StatusAnnotations_Warning_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Module_648" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Module_648.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Lock" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Lock.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Security_Shields_Complete_and_ok_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Security_Shields_Complete_and_ok_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Guage_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Guage_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="envelope_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\envelope_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="properties_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\properties_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="interface_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\interface_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="server_Local_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\server_Local_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="StandardRefresh_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\StandardRefresh_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="StatusAnnotations_Play_16xLG_color" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\StatusAnnotations_Play_16xLG_color.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Running_16xLG" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\Running_16xLG.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="VirtualMachineGroup" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\imgs\VirtualMachineGroup.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
</root>