using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication.MQTT;
using HslCommunication;
using Newtonsoft.Json.Linq;
using HslTechnology.Edge.Config;
using WeifenLuo.WinFormsUI.Docking;
using HslTechnology.EdgeViewer.Controls.Settings;
using HslTechnology.Edge.Controls;
using HslTechnology.EdgeViewer.Core;

namespace HslTechnology.EdgeViewer.Pages
{
	public partial class FormServerSettings : HslForm
	{
		#region Constructor

		public FormServerSettings( EdgeServerSettings serverSettings )
		{
			InitializeComponent( );

			Icon = Util.GetWinformIcon( );
			hslServerSettings = serverSettings;
			if (serverSettings != null)
				this.Text = this.Text + $" [{serverSettings.GetDisplayText( )}]";
		}

		#endregion

		#region Load Closing
		
		private void FormServerSettings_Load( object sender, EventArgs e )
		{
			treeView1.ExpandAll( );
			treeView1.SelectedNode = treeView1.Nodes[0].Nodes[0];
			treeView1.AfterSelect += TreeView1_AfterSelect;

			settingControls = new List<IConfigControl>( );

			serverInfo = new ServerInfo( );
			serverInfo.Parent = panel1;
			serverInfo.Dock = DockStyle.Fill;
			serverInfo.BackColor = Color.Transparent;
			settingControls.Add( serverInfo );

			logInfo = new LogInfo( );
			logInfo.Parent = panel1;
			logInfo.Dock = DockStyle.Fill;
			logInfo.Visible = false;
			logInfo.BackColor = Color.Transparent;
			settingControls.Add( logInfo );

			uploadInfo = new UploadInfo( );
			uploadInfo.Parent = panel1;
			uploadInfo.Dock = DockStyle.Fill;
			uploadInfo.Visible = false;
			uploadInfo.BackColor = Color.Transparent;
			settingControls.Add( uploadInfo );

			dtuConfig = new DtuInfoControl( );
			dtuConfig.Parent = panel1;
			dtuConfig.Dock = DockStyle.Fill;
			dtuConfig.Visible = false;
			dtuConfig.BackColor = Color.Transparent;
			settingControls.Add( dtuConfig );

			standbyInfo = new StandbyInfoControl( );
			standbyInfo.Parent = panel1;
			standbyInfo.Dock = DockStyle.Fill;
			standbyInfo.Visible = false;
			standbyInfo.BackColor = Color.Transparent;
			settingControls.Add( standbyInfo );

			portMappingControl = new PortMappingControl( );
			portMappingControl.Parent = panel1;
			portMappingControl.Dock = DockStyle.Fill;
			portMappingControl.Visible = false;
			portMappingControl.BackColor = Color.Transparent;
			settingControls.Add( portMappingControl );

			//hslButton1.Click += HslButton1_Click;
			//hslButton1.Enabled = false;
		}

		private void ShowUserControl( UserControl control )
		{
			for (int i = 0; i < settingControls.Count; i++)
			{
				if(ReferenceEquals(settingControls[i], control ))
				{
					settingControls[i].Visible = true;
					break;
				}
			}
			for (int i = 0; i < settingControls.Count; i++)
			{
				if (!ReferenceEquals( settingControls[i], control ))
				{
					settingControls[i].Visible = false;
				}
			}
		}

		private void TreeView1_AfterSelect( object sender, TreeViewEventArgs e )
		{
			if(e.Node.Parent != null)
			{
				if(e.Node.Text == "基本参数")
				{
					ShowUserControl( serverInfo );
				}
				else if(e.Node.Text == "日志")
				{
					ShowUserControl( logInfo );
				}
				else if (e.Node.Text == "远程上传")
				{
					ShowUserControl( uploadInfo );
				}
				else if (e.Node.Text == "DTU")
				{
					ShowUserControl( dtuConfig );
				}
				else if(e.Node.Text == "主备模式")
				{
					ShowUserControl( standbyInfo );
				}
				else if (e.Node.Text == "端口映射")
				{
					ShowUserControl( portMappingControl );
				}
			}
		}

		private void FormServerSettings_Shown( object sender, EventArgs e )
		{
			Refresh( );

			if (hslServerSettings == null) return;

			MqttSyncClient client = hslServerSettings.GetMqttSyncClient( );
			OperateResult<JObject> read = client.ReadRpc<JObject>( "Admin/ServerSettingsRequest", null );

			if (read.IsSuccess)
			{
				JObject jObject            = read.Content;
				serverInfo.RenderParameter( jObject[nameof( ServerInfoConfig )] );
				logInfo.   RenderParameter( jObject[nameof( LogInfoConfig )] );
				uploadInfo.RenderParameter( jObject[nameof( UploadInfoConfig )] );
				if (jObject.ContainsKey( nameof( DtuConfig ) ))
					dtuConfig.RenderParameter( jObject[nameof( DtuConfig )] );
				if (jObject.ContainsKey( nameof( StandbyInfoConfig ) ))
					standbyInfo.RenderParameter( jObject[nameof( StandbyInfoConfig )] );
				if (jObject.ContainsKey( nameof( PortMappingConfig ) ))
				{
					// 还要传递物理端口过去
					OperateResult<string[]> ports = client.ReadRpc<string[]>( "Edge/DeviceSerialPorts", new { needMapping = false } );
					if (ports.IsSuccess)
					{
						portMappingControl.RenderPorts( ports.Content );
					}
					portMappingControl.RenderParameter( jObject[nameof( PortMappingConfig )] );
				}
				jsonPara = jObject;
			}
			else
			{
				MessageBox.Show( "服务器数据读取失败！原因：" + read.Message );
			}
		}

		#endregion

		public EdgeServerSettings EdgeServerSettings => hslServerSettings;

		/// <summary>
		/// 获取是否修改了服务器的信息
		/// </summary>
		public bool IsUpdateServerInfo { get; private set; }

		#region Private Member

		private EdgeServerSettings hslServerSettings;
		private ServerInfo serverInfo;                           // 服务器基本信息配置
		private LogInfo logInfo;                                 // 服务器的日志配置
		private UploadInfo uploadInfo;                           // 服务器的上传信息配置
		private DtuInfoControl dtuConfig;                        // Dtu的参数信息
		private PortMappingControl portMappingControl;           // 端口映射的配置参数信息
		private StandbyInfoControl standbyInfo;                  // 备用服务器的配置信息
		private List<IConfigControl> settingControls;            // 配置控件的列表
		private JObject jsonPara;                                // 参数的JSON对象

		#endregion

		private async void button1_Click( object sender, EventArgs e )
		{
			if (jsonPara != null)
			{
				foreach (var item in settingControls)
				{
					OperateResult check = item.CheckInputLegal( );
					if (!check.IsSuccess)
					{
						ShowUserControl( item );
						MessageBox.Show( check.Message );
						return;
					}
				}

				foreach (var item in settingControls)
					item.SetParameterToJToken( );

				button1.Enabled = false;
				MqttSyncClient client = hslServerSettings.GetMqttSyncClient( );
				OperateResult<string> read = await client.ReadRpcAsync<string>( "Admin/ServerSettingsModify",
					new { data = jsonPara.ToString( ) } );
				button1.Enabled = true;

				if (read.IsSuccess)
				{
					MessageBox.Show( "数据修改成功！" );
					IsUpdateServerInfo = true;
					// 修改网关参数的相关信息
					hslServerSettings.UserName = jsonPara[nameof( ServerInfoConfig )][nameof( ServerInfoConfig.UserName )].Value<string>( );
					hslServerSettings.Password =
						HslCommunication.BasicFramework.SoftSecurity.MD5Encrypt( jsonPara[nameof( ServerInfoConfig )][nameof( ServerInfoConfig.Password )].Value<string>( ), 
						HslServerSettings.oasdjoasjdohfiasdasjd );
				}
				else
				{
					MessageBox.Show( "修改数据失败：" + read.Message );
				}
			}
			else
			{
				MessageBox.Show( "修改数据失败：当前无法进行数据修改操作，请重新打开尝试！" );
			}
		}

		private void buttonSave1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}
	}
}
