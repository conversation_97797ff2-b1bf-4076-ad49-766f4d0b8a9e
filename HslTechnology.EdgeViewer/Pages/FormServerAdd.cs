using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication.Enthernet;
using HslCommunication;
using HslCommunication.MQTT;
using HslTechnology.Edge.Config;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;
using HslTechnology.EdgeViewer.Core;
using HslCommunication.Core;

namespace HslTechnology.EdgeViewer.Pages
{
    public partial class FormServerAdd : HslForm
    {
        #region Constructor
        
        public FormServerAdd( EdgeServerSettings hslServerSettings = null )
        {
            InitializeComponent( );

            Icon = Util.GetWinformIcon( );
            this.EdgeServerSettings = hslServerSettings ?? new EdgeServerSettings( );
        }

        #endregion

        #region Public

        public EdgeServerSettings EdgeServerSettings { get; set; }

        #endregion

        private void FormServerAdd_Load( object sender, EventArgs e )
        {
            if (this.EdgeServerSettings != null)
            {
                textBox_alias.Text         = EdgeServerSettings.Alias;
                textBox_edge.Text          = EdgeServerSettings.EdgeID;
                textBox_ipaddress.Text     = EdgeServerSettings.IpAddress;
                textBox_port.Text          = EdgeServerSettings.Port.ToString( );
                textBox_name.Text          = EdgeServerSettings.UserName;
                checkBox_Encrypted.Checked = EdgeServerSettings.UseEncryptedCommunication;
                textBox_password.Text      = string.IsNullOrEmpty( EdgeServerSettings.Password ) ?
                    string.Empty : HslCommunication.BasicFramework.SoftSecurity.MD5Decrypt( EdgeServerSettings.Password, HslServerSettings.oasdjoasjdohfiasdasjd );
            }
        }

        private void hslButton1_Click( object sender, EventArgs e )
        {
            // 通讯测试
            if (!int.TryParse( textBox_port.Text, out int port ))
            {
                MessageBox.Show( "设备的Port输入错误，无法识别！" );
                return;
            }

            try
            {
                MqttSyncClient client = new MqttSyncClient( new MqttConnectionOptions( )
                {
                    IpAddress = textBox_ipaddress.Text,
                    Port = port,
                    Credentials = new MqttCredential( textBox_name.Text, textBox_password.Text ),
                } );
                client.ConnectTimeOut = 2000;

                OperateResult<string> read = client.ReadRpc<string>( "Edge/CommunicationTest", null );
                if (read.IsSuccess)
                {
                    this.EdgeServerSettings = new EdgeServerSettings( )
                    {
                        Alias = textBox_alias.Text,
                        EdgeID = read.Content,
                        IpAddress = textBox_ipaddress.Text,
                        Port = port,
                        UserName = textBox_name.Text,
                        Password = HslCommunication.BasicFramework.SoftSecurity.MD5Encrypt( textBox_password.Text, HslServerSettings.oasdjoasjdohfiasdasjd ),
                        UseEncryptedCommunication = checkBox_Encrypted.Checked,
                    };

                    textBox_edge.Text = EdgeServerSettings.EdgeID;
                    hslButton2.Enabled = true;
                    MessageBox.Show( "连接成功！" );
                }
                else
                {
                    MessageBox.Show( "连接失败！" + read.Message );
                }
            }
            catch ( Exception ex)
            {
                MessageBox.Show( "设备的IP地址输入错误，无法识别！" + ex.Message );
            }
        }

        private void hslButton2_Click( object sender, EventArgs e )
        {
            DialogResult = DialogResult.OK;
        }
    }
}
