using HslTechnology.EdgeViewer.Controls;
using HslTechnology.EdgeViewer.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.BasicFramework;

namespace HslTechnology.EdgeViewer.Pages
{
	public partial class FormPluginsInstall : HslPage
	{
		public FormPluginsInstall( EdgeServerSettings serverSettings )
		{
			InitializeComponent( );
			this.serverSettings = serverSettings;
			this.Icon = Util.ConvertToIcon( Properties.Resources.Library );
		}

		private void FormPluginsInstall_Load( object sender, EventArgs e )
		{
			button_selectfile.Click += Button_selectfile_Click;
			button_upload.Click += Button_upload_Click;
		}

		private void Button_selectfile_Click( object sender, EventArgs e )
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Multiselect = true;
			openFileDialog.Title = "选择插件文件";
			if (openFileDialog.ShowDialog( ) == DialogResult.OK)
			{
				string[] files = openFileDialog.FileNames;
				string pluginsName = string.Empty;
				foreach ( string file in files)
				{
					FileInfo fileInfo = new FileInfo( file );
					if (fileInfo.Name.EndsWith( ".dll" ))
					{
						pluginsName = fileInfo.Name;
						break;
					}
				}
				textBox1.Lines = files;
				if (string.IsNullOrEmpty( textBox2.Text ))
					textBox2.Text = pluginsName;
			}
			openFileDialog.Dispose( );
		}

		private async void Button_upload_Click( object sender, EventArgs e )
		{
			// 先检查插件是否存在
			string pluginsName = textBox2.Text;


			if (this.serverSettings == null) return;
			MqttSyncClient rpc = this.serverSettings.GetMqttSyncClient( );
			button_upload.Enabled = false;
			OperateResult<int> existCheck = await rpc.ReadRpcAsync<int>( "Plugins/CheckRegisterPluginsExist",
				new { pluginsDllName = pluginsName } );
			if (!existCheck.IsSuccess)
			{
				// 检查失败
				button_upload.Enabled = true;
				MessageBox.Show( "检查插件是否存在失败：" + existCheck.Message );
				return;
			}
			if (existCheck.Content == 1)
			{
				if (MessageBox.Show( "插件已经存在，是否进行覆盖？", "覆盖确认", MessageBoxButtons.YesNo ) == DialogResult.No)
				{
					button_upload.Enabled = true;
					return;
				}
			}

			string[] files = textBox1.Lines;
			if (files == null || files.Length == 0)
			{
				button_upload.Enabled = true;
				MessageBox.Show( "当前没有插件文件需要上传" );
				return;
			}

			progressBar1.Value = 0;
			int fileCurrent = 0;
			foreach (string file in files)
			{
				fileCurrent++;
				label_progress.Text = $"({fileCurrent}/{files.Length})";
				FileInfo fileInfo = new FileInfo( file );
				if (File.Exists( file ))
				{
					object obj = new { pluginsDllName = pluginsName, fileName = fileInfo.Name, content = SoftBasic.ByteToHexString( File.ReadAllBytes( file ) ) };
					byte[] sendContent = Encoding.UTF8.GetBytes( obj.ToJsonString( ) );
					OperateResult<string, byte[]> upload = await rpc.ReadAsync( "Plugins/UploadPluginsFile", sendContent, SendProgressReqport, null, null );
					if (!upload.IsSuccess)
					{
						button_upload.Enabled = true;
						MessageBox.Show( $"插件[{file}]上传失败，请重新上传！" );
						return;
					}
				}
			}
			button_upload.Enabled = true;

			if (existCheck.Content == 0)
			{
				// 插件新安装
				OperateResult install = await rpc.ReadRpcAsync<string>( "Plugins/LoadRegisterPlugins",
				new { pluginsDllName = pluginsName } );
				if (!install.IsSuccess)
				{
					MessageBox.Show( $"插件[{pluginsName}]上传成功，但是加载失败：" + install.Message );
				}
				else
				{
					MessageBox.Show( $"插件[{pluginsName}]上传成功，并且成功加载！" );
					// 刷新插件
				}
			}
			else
			{
				// 更新更新
				MessageBox.Show( $"插件[{pluginsName}]上传成功，重启网关服务器生效！" );
			}
		}

		private void SendProgressReqport( long already, long total )
		{
			if (InvokeRequired && IsHandleCreated)
			{
				try
				{
					Invoke( new Action<long, long>( SendProgressReqport ) );
				}
				catch { }
				return;
			}

			progressBar1.Maximum = (int)total;
			progressBar1.Value = (int)already;
		}


		private EdgeServerSettings serverSettings;

    }
}
