using HslTechnology.EdgeViewer.Controls;
using HslTechnology.EdgeViewer.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication.MQTT;
using HslCommunication;

namespace HslTechnology.EdgeViewer.Pages
{
	public partial class FormRestartEdgeServer : HslPage
	{
		public FormRestartEdgeServer( EdgeServerSettings serverSettings )
		{
			InitializeComponent( );
			this.Icon = Util.GetWinformIcon( );
			this.serverSettings = serverSettings;
		}

		private void FormRestartEdgeServer_Load( object sender, EventArgs e )
		{
			button_ok.Click     += ButtonOk1_Click;
			label1.Text = $"是否真的确认重启服务器[{this.serverSettings.GetEdgeDisplayName()}]？";
		}

		private async void ButtonOk1_Click( object sender, EventArgs e )
		{
			if (this.serverSettings == null) return;

			button_ok.Enabled     = false;
			button_cancel.Enabled = false;
			label2.ForeColor = Color.Silver;
			label3.ForeColor = Color.Silver;
			label4.ForeColor = Color.Silver;
			MqttSyncClient client = this.serverSettings.GetMqttSyncClient( );
			// 通知服务器更新了程序
			OperateResult operate = await client.ReadAsync( "Admin/ServerCloseAndRestart", null );
			if (!operate.IsSuccess)
			{
				button_ok.Enabled     = true;
				button_cancel.Enabled = true;
				MessageBox.Show( "当前操作失败：" + operate.Message );
				return;
			}
			label2.ForeColor = Color.Green;
			OperateResult<string> communication = null;
			// 先检测到失败信号为止
			for (int i = 0; i < 100; i++)
			{
				await Task.Delay( 100 );
				communication = client.ReadRpc<string>( "Edge/CommunicationTest", "{}" );
				if (!communication.IsSuccess)
				{
					break;
				}
			}
			label3.ForeColor = Color.Green;

			// 再检测是否重启好
			this.serverSettings.MqttSyncClientConnectClose( );
			client = this.serverSettings.GetMqttSyncClient( );  // 重新更新下这个client信息，如果修改或用户名密码直接自动生效

			for (int i = 0; i < 20; i++)
			{
				await Task.Delay( 100 );
				// 接口 Edge/CommunicationTest
				communication = client.ReadRpc<string>( "Edge/CommunicationTest", "{}" );
				if (communication.IsSuccess) break;
			}
			if(communication.IsSuccess == false)
			{
				label4.Text = "重启失败，请手动启动当前系统：" + communication.Message;
				label4.ForeColor = Color.Tomato;
			}
			else
			{
				label4.ForeColor = Color.Green;
			}
			button_ok.Enabled     = true;
			button_cancel.Enabled = true;
		}

		private void ButtonCancel1_Click( object sender, EventArgs e )
		{
			button_cancel.PerformClick( );
		}

		private EdgeServerSettings serverSettings;
	}
}
