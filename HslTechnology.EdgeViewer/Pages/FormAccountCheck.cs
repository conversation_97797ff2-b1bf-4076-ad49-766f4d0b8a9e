using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication;
using HslCommunication.Enthernet;
using Newtonsoft.Json.Linq;
using HslCommunication.MQTT;

namespace HslTechnology.EdgeViewer.Pages
{
    public partial class FormAccountCheck : Form
    {
        public FormAccountCheck( )
        {
            InitializeComponent( );
        }

        private async void hslButton1_Click( object sender, EventArgs e )
        {
            hslButton1.Enabled = false;
            MqttSyncClient client = new MqttSyncClient( new MqttConnectionOptions( )
            {
                IpAddress = Util.RemoteServerIp,
                Port = 52136,
                Credentials = new MqttCredential( textBox1.Text, textBox2.Text ),
                ConnectTimeout = 5000
            } );
            OperateResult connect = await client.ConnectServerAsync( );

            hslButton1.Enabled = true;
            if (!connect.IsSuccess)
            {
                MessageBox.Show( "失败！" + connect.Message );
            }
            else
            {
                client.ConnectClose( );
                DialogResult = DialogResult.OK;
            }
        }

        private void textBox2_KeyDown( object sender, KeyEventArgs e )
        {
            if(e.KeyCode == Keys.Enter)
            {
                hslButton1.PerformClick( );
            }
        }

        private void textBox1_KeyDown( object sender, KeyEventArgs e )
        {
            if (e.KeyCode == Keys.Enter)
            {
                hslButton1.PerformClick( );
            }
        }
    }
}
