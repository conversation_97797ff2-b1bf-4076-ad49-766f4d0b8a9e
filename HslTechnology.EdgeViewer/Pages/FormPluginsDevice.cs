using HslTechnology.Edge.Node.Render;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json.Linq;
using HslTechnology.Edge.Plugins;
using System.IO;
using HslTechnology.Edge.Node.Device;
using HslTechnology.EdgeViewer.Controls.NodeSettings;
using System.Threading;
using HslCommunication;
using HslTechnology.Edge.Config;
using HslCommunication.MQTT;
using System.Xml.Linq;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;
using HslTechnology.EdgeViewer.Core;

namespace HslTechnology.EdgeViewer.Pages
{
	public partial class FormPluginsDevice : HslForm
	{
		public FormPluginsDevice( EdgeServerSettings serverSettings, PluginsDefinition plugins, NodePropertyConfig[] propertyConfigs, bool createNew )
		{
			InitializeComponent( );
			this.configControls = new List<PropertyConfigControl>( );
			this.propertyConfigs = propertyConfigs;
			this.plugins = plugins;
			this.serverSettings = serverSettings;
			this.createNew = createNew;
			if (plugins.Icon16 != null)
				this.Icon = Util.ConvertToIcon( Util.GetImageFromBytes( plugins.Icon16 ) );

			if (createNew && this.propertyConfigs != null)
			{
				foreach (NodePropertyConfig propertyConfig in this.propertyConfigs)
				{
					if (propertyConfig.Name == nameof( DeviceNode.CreateTime ) ||
						propertyConfig.Name == nameof( DeviceNode.InstallationDate ))
					{
						propertyConfig.DefaultValue = DateTime.Now.ToString( "yyyy-MM-dd HH:mm:ss" );
					}
				}
			}
		}

		private void FormPluginsDevice_Load( object sender, EventArgs e )
		{
			label_plugin_company.Text = this.plugins.Company;
			NodePropertyConfig firstOrDefault = propertyConfigs.FirstOrDefault( m => m.Name == nameof( DeviceNode.PluginsType ) );

			string nodeName = firstOrDefault == null ? string.Empty : firstOrDefault.DefaultValue;

			if (plugins.DeviceDefinitions.ContainsKey( nodeName ))
			{
				if (string.IsNullOrEmpty( plugins.DeviceDefinitions[nodeName].DeviceName ))
					label_plugin_device.Text = nodeName;
				else
					label_plugin_device.Text = plugins.DeviceDefinitions[nodeName].DeviceName;
			}
			else
				label_plugin_device.Text = "[can not find device in plugin]";
			label_plugin_version.Text = this.plugins.Version.ToString( );

			int y = 0;
			foreach (NodePropertyConfig propertyConfig in propertyConfigs)
			{
				PropertyConfigControl configControl = new PropertyConfigControl( );
				configControl.Parent = panel1;
				configControl.Location = new Point( 3, y );
				configControl.Size = propertyConfigControl1.Size;
				configControl.SetNodePropertyConfig( propertyConfig );
				configControls.Add( configControl );
				y += 26;
			}
		}

		private PluginsDefinition plugins;
		private NodePropertyConfig[] propertyConfigs;
		private List<PropertyConfigControl> configControls;
		private EdgeServerSettings serverSettings;

		private void FormPluginsDevice_Shown( object sender, EventArgs e )
		{
			if (this.serverSettings == null) return;
			// 线程去请求服务器的可用的端口信息
			ThreadPool.QueueUserWorkItem( new WaitCallback( new Action<object>( ( obj ) =>
			{
				MqttSyncClient client = this.serverSettings.GetMqttSyncClient( );
				OperateResult<string[]> read = client.ReadRpc<string[]>( "Edge/DeviceSerialPorts", null );

				Invoke( new Action( ( ) =>
				{
					if (read.IsSuccess)
					{
						textBox1.Lines = read.Content;
					}
					else
					{
						textBox1.Text = "串口资源加载失败！";
					}
				} ) );
			} ) ), null );
		}

		private void button2_Click( object sender, EventArgs e )
		{
			// 点击了确认信息
			foreach (PropertyConfigControl configControl in configControls)
			{
				OperateResult update = configControl.UpdatePluginsValue( );
				if (!update.IsSuccess)
				{
					MessageBox.Show( update.Message );
					return;
				}
			}

			// 所有数据检查通过
			List<NodePropertyConfig> nodeProperties = new List<NodePropertyConfig>( );
			foreach (PropertyConfigControl configControl in configControls)
			{
				nodeProperties.Add( configControl.GetNodePropertyConfig );
			}
			XElement element = NodePropertyConfig.CreateXmlFromPluginsProperty( nodeProperties.ToArray( ) );
			DeviceNode deviceNode = new DeviceNode( element, Edge.Node.DeviceType.Plugins );
			deviceNode.Tag = nodeProperties.ToArray( );
			pluginsDeviceNode = deviceNode;
			DialogResult = DialogResult.OK;
		}

		/// <summary>
		/// 当前的插件设备的节点信息
		/// </summary>
		public DeviceNode PluginsDeviceNode => pluginsDeviceNode;
		private DeviceNode pluginsDeviceNode;
		private bool createNew = false;

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button3.PerformClick( );
		}
	}
}
