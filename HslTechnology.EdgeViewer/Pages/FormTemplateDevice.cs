using HslTechnology.Edge.Config;
using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.EdgeViewer.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace HslTechnology.EdgeViewer.Pages
{
	public partial class FormTemplateDevice : HslForm
	{
		public FormTemplateDevice( string path, EdgeServerSettings serverSettings, XElement element )
		{
			InitializeComponent( );
			this.path           = path;
			this.serverSettings = serverSettings;
			this.element        = element;
			this.SharpImageList = serverSettings == null ? Util.GroupNodeImages.DeviceImageList.ImageList : serverSettings.NodeImages.DeviceImageList.ImageList;
			this.Icon           = Util.ConvertToIcon( Properties.Resources.SpherePreview );
		}

		private void FormTemplateDevice_Load( object sender, EventArgs e )
		{
			treeView1.ImageList = this.SharpImageList;
			treeView1.Nodes[0].SetImageKey( "SpherePreview" );
			Util.RenderEdgeSettingsTreeNode( this.serverSettings, this.treeView1.Nodes[0], this.element, isRenderRequest: false, markChaneg: null, nameChanged: false );
			treeView1.ExpandAll( );

			treeView1.AfterSelect += TreeView1_AfterSelect;
			this.Text = $"{this.Text} [{path}]";
		}

		private void TreeView1_AfterSelect( object sender, TreeViewEventArgs e )
		{
			TreeNode node = e.Node;
			if (node == null) return;
			if ( node.Tag is DeviceNode groupNode)
			{
				if (formDeviceAdd == null)
				{
					groupNode.Template            = groupNode.Name;
					formDeviceAdd                 = new FormDeviceAdd( true, "", groupNode, this.serverSettings, node.Parent.Tag as GroupNode );
					formDeviceAdd.TopLevel        = false;
					formDeviceAdd.FormBorderStyle = FormBorderStyle.None;
					formDeviceAdd.Parent          = panel1;
					formDeviceAdd.Dock            = DockStyle.Fill;
					formDeviceAdd.OnClickCancel   += FormDeviceAdd_OnClickCancel;
					formDeviceAdd.OnClickOk       += FormDeviceAdd_OnClickOk;
					formDeviceAdd.Show( );
				}
				else
				{
					groupNode.Template = groupNode.Name;
					formDeviceAdd.SetGroupNode( groupNode );
				}
			}
		}

		private void FormDeviceAdd_OnClickOk( object sender, EventArgs e )
		{
			groupNode = formDeviceAdd.GetGroupNode( );
			DialogResult = DialogResult.OK;
		}

		private void FormDeviceAdd_OnClickCancel( object sender, EventArgs e )
		{
			DialogResult = DialogResult.Cancel;
		}

		/// <summary>
		/// 获取节点信息
		/// </summary>
		/// <returns>节点信息</returns>
		public GroupNode GetGroupNode( ) => this.groupNode;

		private GroupNode groupNode;
		private string path = string.Empty;
		private FormDeviceAdd formDeviceAdd;
		private ImageList SharpImageList;
		private XElement element = null;
		private EdgeServerSettings serverSettings;              // 如果从Xml进行创建
	}
}
