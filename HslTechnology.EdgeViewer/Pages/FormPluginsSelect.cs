using HslTechnology.Edge.Controls;
using HslTechnology.Edge.Plugins;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.EdgeViewer.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Pages
{
	public partial class FormPluginsSelect : HslForm
	{
		public FormPluginsSelect( PluginsDefinition[] pluginsDefinitions )
		{
			InitializeComponent( );
			this.pluginsDefinitions = pluginsDefinitions;
			this.Icon = Util.ConvertToIcon( Properties.Resources.Library );
		}

		private void FormPluginsSelect_Load( object sender, EventArgs e )
		{
			ImageList imageList = new ImageList( );
			imageList.ColorDepth = ColorDepth.Depth24Bit;
			imageList.ImageSize = new Size( 16, 16 );
			imageList.Images.Add( "__?", Properties.Resources.StatusAnnotations_Help_and_inconclusive_16xLG_color );
			treeView1.ImageList = imageList;
			if (pluginsDefinitions?.Length > 0)
			{
				foreach (PluginsDefinition pluginsDefinition in pluginsDefinitions)
				{
					if (pluginsDefinition.Icon16 != null && !imageList.Images.ContainsKey( pluginsDefinition.DllName ))
						imageList.Images.Add( pluginsDefinition.DllName, Util.GetImageFromBytes( pluginsDefinition.Icon16 ) );

					TreeNode treeNode = new TreeNode( pluginsDefinition.DllName );
					treeNode.SetImageKey( pluginsDefinition.DllName );
					treeNode.Tag = pluginsDefinition;

					if (pluginsDefinition.DeviceDefinitions?.Count > 0)
					{
						foreach (var deviceDefinition in pluginsDefinition.DeviceDefinitions)
						{
							string deviceName = string.IsNullOrEmpty( deviceDefinition.Value.DeviceName ) ?
								deviceDefinition.Key : deviceDefinition.Value.DeviceName;
							deviceDefinition.Value.Tag = deviceDefinition.Key;
							TreeNode treeDevice = new TreeNode( deviceName );
							treeDevice.SetImageKey( pluginsDefinition.DllName );
							treeDevice.Tag = deviceDefinition.Value;
							treeNode.Nodes.Add( treeDevice );
						}
					}

					treeView1.Nodes.Add( treeNode );
				}
			}
			else
			{
				label1.Text = "已安装的插件列表： 当前没有任何插件安装！";
			}

			treeView1.AfterSelect += TreeView1_AfterSelect;
			treeView1.ExpandAll( );
		}

		private void TreeView1_AfterSelect( object sender, TreeViewEventArgs e )
		{
			TreeNode treeNode = e.Node;
			if (treeNode == null) return;

			if(treeNode.Tag is PluginsDefinition plugins)
			{
				RenderPluginsDefinition( plugins );
				RenderPluginsDevice( null, null );
			}
			else if (treeNode.Tag is PluginsDeviceDefinition pluginsDevice)
			{
				if(treeNode.Parent.Tag is PluginsDefinition pluginsDef)
				{
					RenderPluginsDefinition( pluginsDef );
					RenderPluginsDevice( pluginsDevice, treeNode.Text );
				}
			}
		}

		private void RenderPluginsDefinition( PluginsDefinition plugins )
		{
			this.edgePluginsControl1.RenderPluginsDefinition( plugins );
			selectPluginsDefinition = plugins;
		}

		private void RenderPluginsDevice(PluginsDeviceDefinition pluginsDevice, string TagName )
		{
			selectPluginsDevice = pluginsDevice;
			if (pluginsDevice == null)
			{
				textBox8.Text = null;
				textBox9.Text = null;
				pictureBox2.Image = null;
			}
			else
			{
				textBox8.Text = TagName;
				textBox9.Text = pluginsDevice.Description;
				if (pluginsDevice.Image != null)
				{
					pictureBox2.Image = Util.GetImageFromBytes( pluginsDevice.Image );
				}
			}
		}

		private void button2_Click( object sender, EventArgs e )
		{
			if(selectPluginsDevice == null)
			{
				MessageBox.Show( "当前还未选择任何的设备对象, 请重新选择! " );
				return;
			}

			this.DialogResult = DialogResult.OK;
		}

		/// <summary>
		/// 获取当前选择的插件信息
		/// </summary>
		public PluginsDefinition SelectPluginsDefinition => selectPluginsDefinition;

		/// <summary>
		/// 当前选择的设备信息
		/// </summary>
		public PluginsDeviceDefinition SelectPluginsDevice => selectPluginsDevice;



		private PluginsDefinition selectPluginsDefinition = null;
		private PluginsDeviceDefinition selectPluginsDevice = null;
		private PluginsDefinition[] pluginsDefinitions;                            // 所有插件的定义信息

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button3.PerformClick( );
		}
	}
}
