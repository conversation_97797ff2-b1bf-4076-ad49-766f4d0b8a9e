using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using HslTechnology.EdgeViewer.Forms;
using HslCommunication.Enthernet;
using HslCommunication;
using HslCommunication.MQTT;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Server;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.Edge.Device.CncDevice;
using HslTechnology.Edge.Device.RobotDevice;
using HslTechnology.EdgeViewer.Controls;
using HslCommunication.LogNet;
using System.Reflection;
using HslTechnology.Edge.Reflection;
using HslCommunication.BasicFramework;
using HslTechnology.EdgeViewer.Controls.NodeSettings;
using HslCommunication.Robot.ABB;
using HslCommunication.Robot.FANUC;
using HslTechnology.EdgeViewer.Core;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.EdgeViewer.Controls.Alarm;
using HslTechnology.Edge.Node.Method;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge.Plugins;
using HslTechnology.Edge.Node.Database;
using HslTechnology.Edge.Controls.Basic;
using HslTechnology.EdgeViewer.Request;
using HslTechnology.Edge;
using HslTechnology.EdgeViewer.Properties;
using System.Net;

namespace HslTechnology.EdgeViewer.Pages
{
	public partial class FormNodeSettings : HslPage
	{
		#region Constructor

		/// <summary>
		/// 实例化一个节点配置器信息
		/// </summary>
		/// <param name="log">客户端的基本日志信息</param>
		/// <param name="serverSettings">从Xml文件加载信息</param>
		/// <param name="xmlSettings">当前的服务器配置的设备信息</param>
		public FormNodeSettings( ILogNet log, EdgeServerSettings serverSettings, XElement xmlSettings )
		{
			InitializeComponent( );
			Icon                    = Util.ConvertToIcon( Resources.EditDocument );
			SharpImageList          = serverSettings == null ? Util.GroupNodeImages.DeviceImageList.ImageList : serverSettings.NodeImages.DeviceImageList.ImageList;
			this.serverSettings     = serverSettings;
			this.logNet             = log;
			this.xmlSettings        = xmlSettings;

			Dictionary<string, ToolStripMenuItem> parentKeys = new Dictionary<string, ToolStripMenuItem>( );
			// 加载所有的设备相关的菜单
			foreach ( var item in DeviceDefinitions.Devices.Values )
			{
				if (item.DeviceObject == null && item.DeviceNode == null) continue;

				int lastIndex = item.DeviceName.LastIndexOf( "/" );
				if (lastIndex > 0)
				{
					string treeNodeKey = item.DeviceName.Substring( 0, lastIndex );
					string treeNodeText = item.DeviceName.Substring( lastIndex + 1 );

					ToolStripMenuItem parent = null;
					if (parentKeys.ContainsKey( treeNodeKey ))
						parent = parentKeys[treeNodeKey];
					else
					{
						// 没找到缓存的，就需要创建节点信息了
						string[] paths = treeNodeKey.Split( new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries );
						parent = FindParentNodeByText( this.cMS_Device.Items, paths, item.ImageKey, 0 );
						if (parent != null && !parentKeys.ContainsKey( treeNodeKey )) parentKeys.Add( treeNodeKey, parent );
					}

					if (parent != null)
					{
						ToolStripMenuItem node = new ToolStripMenuItem( treeNodeText );
						node.Image = Util.GroupNodeImages.DeviceImageList.GetImage( item.ImageKey );
						node.Tag = item;
						node.Click += 设备toolStripMenuItem_Click;
						parent.DropDownItems.Add( node );
					}
				}
			}
		}

		private ToolStripMenuItem FindParentNodeByText( ToolStripItemCollection collection, string[] paths, string imageKey, int index = 0 )
		{
			if (index >= paths.Length) return null;
			foreach (ToolStripMenuItem item in collection)
			{
				if (item.Text == paths[index])
				{
					if (index == paths.Length - 1) return item;
					return FindParentNodeByText( item.DropDownItems, paths, imageKey, index + 1 );
				}
			}

			// 没有找到匹配的节点，所以只能创建新的
			ToolStripMenuItem node = new ToolStripMenuItem( paths[index] );
			node.Image = Util.GroupNodeImages.DeviceImageList.GetImage( imageKey );
			collection.Add( node );
			if (index == paths.Length - 1) return node;
			return FindParentNodeByText( node.DropDownItems, paths, imageKey, index + 1 );
		}

		#endregion

		#region Form Load Close

		private async void FormNodeSettings_Load( object sender, EventArgs e )
		{
			panel2.Parent = this;
			panel2.Location = new Point( 0, 23);
			panel2.Size = new Size( this.Width - 18, this.Height - 60 );
			panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right | System.Windows.Forms.AnchorStyles.Bottom));
			panel2.Visible = false;

			treeView1.ImageList = SharpImageList;
			treeViewEx1.ImageList = SharpImageList;
			treeViewEx1.Nodes[0].SetImageKey( "house_16xLG" );
			treeViewEx1.Nodes[0].Tag = new GroupNode( "XmlStandby", "网关备用的配置信息文件" );
			treeViewEx1.Nodes[1].SetImageKey( "FileGroup_10135_16x" );
			treeViewEx1.Nodes[1].Tag = new GroupNode( "XmlHistory", "网关历史修改的XML文件" );
			Text = "采集信息-" + (this.serverSettings != null ? this.serverSettings.GetEdgeDisplayName( ) : "New file");

			foreach (GroupNode root in GroupNode.GetDefaultGroupNodes( ))
			{
				TreeNode treeNode = new TreeNode( root.Name );
				treeNode.SetImageKey( "VirtualMachine_16xLG" );
				treeNode.Tag = root;
				treeView1.Nodes.Add( treeNode );
			}

			// 原始字节显示控件
			controls = new List<UserControl>( );
			dataViewControl = new SourceDataViewControl( );
			dataViewControl.Visible = false;
			dataViewControl.Parent = panel1;
			dataViewControl.Dock = DockStyle.Fill;
			dataViewControl.BackColor = this.BackColor;
			controls.Add( dataViewControl );
			dataViewControl.OnDataPreviewChanged += DataViewControl_OnDataPreviewChanged;

			// 显示节点属性的控件的实例化
			propertyViewControl = new PropertyViewControl( );
			propertyViewControl.Visible = true;
			propertyViewControl.Parent = panel1;
			propertyViewControl.Dock = DockStyle.Fill;
			controls.Add( propertyViewControl );

			// 显示报警列表的控件的实例化
			alarmItemDataControl = new AlarmItemDataTable( );
			alarmItemDataControl.Visible = false;
			alarmItemDataControl.Parent = panel1;
			alarmItemDataControl.Dock = DockStyle.Fill;
			controls.Add( alarmItemDataControl );

			treeViewEx1.AfterSelect += treeView1_AfterSelect;
			treeView1.AfterSelect += treeView1_AfterSelect;
			treeView1.MouseDown   += treeView1_MouseDown;               // 鼠标按下消息
			treeView1.ItemDrag    += treeView1_ItemDrag;
			treeView1.DragEnter   += treeView1_DragEnter;
			treeView1.DragDrop    += treeView1_DragDrop;
			treeView1.KeyDown     += TreeView1_KeyDown;

			treeViewEx1.MouseDown += TreeViewEx1_MouseDown;
			addGroupToolStripMenuItem.Click              += AddGroupToolStripMenuItem_Click;
			addSerialPipeToolStripMenuItem.Click         += AddSerialPipeToolStripMenuItem_Click;
			addSocketPipeToolStripMenuItem.Click         += AddSocketPipeToolStripMenuItem_Click;
			addSingleThreadToolStripMenuItem.Click       += AddSingleThreadToolStripMenuItem_Click;
			addPluginsDeviceToolStripMenuItem.Click      += AddPluginsDeviceToolStripMenuItem_Click;
			addTemplatetoolStripMenuItem.Click           += AddTemplatetoolStripMenuItem_Click;
			sqlServerToolStripMenuItem.Click             += SqlServerToolStripMenuItem_Click;
			editDeviceNodeToolStripMenuItem.Click        += editToolStripMenuItem_Click;
			deleteDeviceNodeToolStripMenuItem.Click      += deleteToolStripMenuItem_Click;
			moveDeviceNodeUpToolStripMenuItem.Click      += MoveUpToolStripMenuItem_Click;
			moveDeviceNodeDownToolStripMenuItem.Click    += MoveDownToolStripMenuItem_Click;

			// cMs_Request 请求菜单绑定
			editMethodToolStripMenuItem.Click            += EditMethodToolStripMenuItem_Click;
			addScalarReadRequestToolStripMenuItem.Click  += AddScalarReadRequestToolStripMenuItem_Click;
			addScalarCacheRequestToolStripMenuItem.Click += AddScalarCacheRequestToolStripMenuItem_Click;
			addSourceReadRequestToolStripMenuItem.Click  += AddSourceReadRequestToolStripMenuItem_Click;
			addWriteRequestToolStripMenuItem.Click       += AddWriteRequestToolStripMenuItem_Click;
			addDatabaseRequestToolStripMenuItem.Click    += AddDatabaseRequestToolStripMenuItem_Click;
			addLocalStructToolStripMenuItem.Click        += addRegularToolStripMenuItem_Click;
			addressMappingToolStripMenuItem.Click        += AddressMappingToolStripMenuItem_Click;
			addMethodCallRequestToolStripMenuItem.Click  += AddMethodCallRequestToolStripMenuItem_Click;
			editRequestToolStripMenuItem.Click           += editToolStripMenuItem_Click;
			deleteRequestToolStripMenuItem.Click         += deleteToolStripMenuItem_Click;
			addRequestGroupToolStripMenuItem.Click       += AddRequestGroupToolStripMenuItem_Click;
			moveRequestUpToolStripMenuItem.Click         += MoveUpToolStripMenuItem_Click;
			moveRequestDownToolStripMenuItem.Click       += MoveDownToolStripMenuItem_Click;
			outputXmlToolStripMenuItem.Click             += OutputXmlToolStripMenuItem_Click;
			copyNodeToolStripMenuItem.Click              += CopyNodeToolStripMenuItem_Click;
			copyDevicePathToolStripMenuItem.Click        += CopyDevicePathToolStripMenuItem_Click;

			// cMs_EditRequest 编辑请求菜单
			deleteRequestNodeToolStripMenuItem.Click     += deleteToolStripMenuItem_Click;
			editRequestNodeToolStripMenuItem.Click       += editToolStripMenuItem_Click;
			
			// cMs_Regular
			addRegularToolStripMenuItem.Click            += addRegularToolStripMenuItem_Click;

			// cMs_Database
			editDatabaseToolStripMenuItem.Click          += editToolStripMenuItem_Click;
			deleteDatabaseToolStripMenuItem.Click        += deleteToolStripMenuItem_Click;
			outputXmlDatabaseToolStripMenuItem.Click     += OutputXmlToolStripMenuItem_Click;

			// cMs_EditRegular
			addRegularItemToolStripMenuItem.Click        += addRegularScalarItemToolStripMenuItem_Click;
			editRegularToolStripMenuItem.Click           += editToolStripMenuItem_Click;
			deleteRegularToolStripMenuItem.Click         += deleteToolStripMenuItem_Click;
			outputXmlRegularStripMenuItem.Click          += OutputXmlToolStripMenuItem_Click;
			outputJsonToolStripMenuItem.Click            += OutputJsonToolStripMenuItem_Click;


			// source data requet 原始数据读取相关
			addRegularScalarToolStripMenuItem.Click      += addRegularScalarItemToolStripMenuItem_Click;
			addRegularStructToolStripMenuItem.Click      += AddRegularStructToolStripMenuItem_Click;
			editSourceRequestToolStripMenuItem.Click     += editToolStripMenuItem_Click;
			deleteSourceRequestToolStripMenuItem.Click   += deleteToolStripMenuItem_Click;

			// 所有右键查看属性的菜单
			propertyToolStripMenuItem1.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem2.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem3.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem4.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem5.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem6.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem7.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem8.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem9.Click             += PropertyToolStripMenuItem_Click;
			propertyToolStripMenuItem10.Click            += PropertyToolStripMenuItem_Click;

			// 工具栏的菜单信息
			toolStripButton1.Click                       += 打开文件ToolStripMenuItem_Click;
			saveToolStripButton.Click                    += 另存为ToolStripMenuItem_Click;
			toolStripButton_download.Click               += 下载到设备ToolStripMenuItem_Click;
			toolStripButton4.Click                       += OneKeyCreateTestToolStripMenuItem_Click;
			toolStripButton_save_server.Click            += ToolStripButton_save_server_Click;
			toolStripButton_design.Click                 += ToolStripButton_design_Click;

			// 报警相关的
			addBoolAlarmToolStripMenuItem.Click          += AddBoolAlarmToolStripMenuItem_Click;
			addIntegerAlarmToolStripMenuItem.Click       += AddIntegerAlarmToolStripMenuItem_Click;
			addHexAlarmToolStripMenuItem.Click           += AddHexAlarmToolStripMenuItem_Click;
			addDataRangeAlarmToolStripMenuItem.Click     += AddDataRangeAlarmToolStripMenuItem_Click;
			addAlarmDatabaseToolStripMenuItem.Click      += AddAlarmDatabaseToolStripMenuItem_Click;

			// OEE相关
			addOEEToolStripMenuItem.Click                += AddOEEToolStripMenuItem_Click;

			// XML菜单配置
			xmlLoadToolStripMenuItem.Click               += XmlLoadToolStripMenuItem_Click;
			xmlDeleteToolStripMenuItem.Click             += XmlDeleteToolStripMenuItem_Click;

			// 加载所有的数据
			LoadByXml( this.xmlSettings );
			// 将向上移动和向下移动的菜单添加到设备的右键菜单里

			// 异步获取设备的名称
			if (this.serverSettings != null)
			{
				MqttSyncClient client = serverSettings.GetMqttSyncClient( );
				OperateResult<string> read = await client.ReadRpcAsync<string>( "Edge/DeviceName", "{}" );
				if (read.IsSuccess) toolStripTextBox_edgeName.Text = read.Content;

				if (this.serverSettings.MaxDevicesCount >= 0)
					toolStripLabel_edge_deviceMax.Text = this.serverSettings.MaxDevicesCount.ToString( );
				await RefreshXmlSettings( );
			}
		}

		private async void XmlDeleteToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeViewEx1.SelectedNode;
			if (node.Parent == null) return;

			await DeleteXmlFromApi( node.Parent.Text == "XmlStandby" ? "Admin/XmlSettingsStandbyDelete" : "Admin/XmlSettingsHistoryDelete", node.Text );
		}

		private async void XmlLoadToolStripMenuItem_Click( object sender, EventArgs e )
		{ 
			TreeNode node = treeViewEx1.SelectedNode;
			if (node.Parent == null) return;

			await LoadXmlFromApi( node.Parent.Text == "XmlStandby" ? "Admin/XmlSettingsStandbyRequest" : "Admin/XmlSettingsHistoryRequest", node.Text );
		}

		private async Task LoadXmlFromApi( string api, string fileName )
		{
			if (this.serverSettings != null)
			{
				MqttSyncClient client = serverSettings.GetMqttSyncClient( );
				OperateResult<string> read = await client.ReadRpcAsync<string>( api, new { fileName = fileName } );
				if (!read.IsSuccess)
				{
					MessageBox.Show( $"加载配置文件：[{fileName}] 失败，原因：" + read.Message );
				}
				else
				{
					if (isNodeSettingsModify)
					{
						if (MessageBox.Show( "当前的配置文件已经更改，是否需要丢弃当前的配置信息？", "确认", MessageBoxButtons.YesNo ) == DialogResult.No)
						{
							return;
						}
					}

					try
					{
						// 加载所有的数据
						this.xmlSettings = XElement.Parse( read.Content );
						LoadByXml( this.xmlSettings );
						MessageBox.Show( "加载完成！" );
					}
					catch (Exception ex)
					{
						MessageBox.Show( "加载失败！" + ex.Message );
					}
				}
			}
		}

		private async Task DeleteXmlFromApi( string api, string fileName )
		{
			if (this.serverSettings != null)
			{
				MqttSyncClient client = serverSettings.GetMqttSyncClient( );
				OperateResult<string> read = await client.ReadRpcAsync<string>( api, new { fileName = fileName } );
				if (!read.IsSuccess)
				{
					MessageBox.Show( $"删除配置文件：[{fileName}] 失败，原因：" + read.Message );
				}
				else
				{
					MessageBox.Show( "删除成功！" );
					await RefreshXmlSettings( );
				}
			}
		}

		private async Task RefreshXmlSettings( )
		{
			if (this.serverSettings != null)
			{
				// 获取历史变更的配置文件名信息
				OperateResult<string[]> xmlHistoryNames = await this.serverSettings.GetMqttSyncClient( ).ReadRpcAsync<string[]>( "Admin/XmlSettingsHistoryNamesRequest", "{}" );
				if (xmlHistoryNames.IsSuccess)
				{
					treeViewEx1.Nodes[1].Nodes.Clear( );
					for (int i = 0; i < xmlHistoryNames.Content.Length; i++)
					{
						string name = xmlHistoryNames.Content[i];
						TreeNode treeNode = new TreeNode( name );
						treeNode.SetImageKey( "XMLFile_828_16x" );
						treeViewEx1.Nodes[1].Nodes.Add( treeNode );
					}
				}

				// 获取备用的配置文件名列表
				OperateResult<string[]> xmlStandbyNames = await this.serverSettings.GetMqttSyncClient( ).ReadRpcAsync<string[]>( "Admin/XmlSettingsStandbyNamesRequest", "{}" );
				if (xmlStandbyNames.IsSuccess)
				{
					treeViewEx1.Nodes[0].Nodes.Clear( );
					for (int i = 0; i < xmlStandbyNames.Content.Length; i++)
					{
						string name = xmlStandbyNames.Content[i];
						TreeNode treeNode = new TreeNode( name );
						treeNode.SetImageKey( "XMLFile_828_16x" );
						treeViewEx1.Nodes[0].Nodes.Add( treeNode );
					}
				}

				treeViewEx1.ExpandAll( );
			}
		}


		private async void DataViewControl_OnDataPreviewChanged( object sender, EventArgs e )
		{
			if(sender is CheckBox checkBox)
			{
				if (checkBox.Checked)
				{
					TreeNode node = treeView1.SelectedNode;
					SourceReadRequest sourceReadRequest = null;
					if (node.Tag is SourceReadRequest) sourceReadRequest = node.Tag as SourceReadRequest;
					else if (node.Parent != null && node.Parent.Tag is SourceReadRequest)
					{
						sourceReadRequest = node.Parent.Tag as SourceReadRequest;
						node = node.Parent;
					}
					if (sourceReadRequest != null)
					{
						MqttSyncClient client = serverSettings.GetMqttSyncClient( );
						OperateResult<string> read = await client.ReadRpcAsync<string>( "Admin/ReadSourceRequest",
							new
							{
								deviceUrl = GetNodePath( node.Parent ),
								deviveXml = (node.Parent.Tag as GroupNode).ToXmlElement( ).ToString( ),
								requestXml = sourceReadRequest.ToXmlElement( ).ToString( )
							} );
						if (!read.IsSuccess)
						{
							MessageBox.Show( "Read Source request failed: " + read.Message );
						}
						else
						{
							dataViewControl.SetBufferData( Convert.FromBase64String( read.Content ) );
						}
					}
				}
			}
		}

		private void PropertyToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 当前的节点向上移动，设备节点最多移动到分类节点下面，如果是分类节点，和上一个分类节点移动
			TreeNode node = treeView1.SelectedNode;
			if (node.Parent == null) return;
			if (node.Tag is GroupNode nodeClass)
			{
				// 显示选择的节点信息
				SetControlShow( propertyViewControl );
				propertyViewControl.SetObjectShow( nodeClass );
			}
		}

		/// <summary>
		/// 标记当前的文件为修改状态
		/// </summary>
		private void MarkSettingsFileChanged( )
		{
			isNodeSettingsModify = true;
			int count = GetDeviceCount( treeView1.Nodes[0] );
			toolStripLabel_xml_deviceCount.Text = count.ToString( );
		}

		private void MoveUpToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 当前的节点向上移动，设备节点最多移动到分类节点下面，如果是分类节点，和上一个分类节点移动
			TreeNode node = treeView1.SelectedNode;
			if (node.Parent == null) return;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.DeviceNode)
				{
					if (node.PrevNode == null) return;
					if (node.PrevNode.Tag is GroupNode preNodeDevice)
					{
						if(preNodeDevice.NodeType == NodeType.DeviceNode)
						{
							// 可以进行上下调换位置
							TreeNode up = node.PrevNode;
							up.Remove( );
							int index = node.Parent.Nodes.IndexOf( node );
							node.Parent.Nodes.Insert( index + 1, up );
							MarkSettingsFileChanged( );
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.GroupNode)
				{
					if (node.PrevNode == null) return;
					if (node.PrevNode.Tag is GroupNode preNodeDevice)
					{
						if (preNodeDevice.NodeType == NodeType.GroupNode)
						{
							// 可以进行上下调换位置
							TreeNode up = node.PrevNode;
							up.Remove( );
							int index = node.Parent.Nodes.IndexOf( node );
							node.Parent.Nodes.Insert( index + 1, up );
							MarkSettingsFileChanged( );
						}
					}
				}
			}
		}

		private void MoveDownToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 当前的节点向下移动，设备节点最多移动到分类节点下面，如果是分类节点，和上一个分类节点移动
			TreeNode node = treeView1.SelectedNode;
			if (node.Parent == null) return;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.DeviceNode)
				{
					if (node.NextNode == null) return;
					if (node.NextNode.Tag is GroupNode preNodeDevice)
					{
						if (preNodeDevice.NodeType == NodeType.DeviceNode)
						{
							// 可以进行上下调换位置
							TreeNode down = node.NextNode;
							down.Remove( );
							int index = node.Parent.Nodes.IndexOf( node );
							node.Parent.Nodes.Insert( index, down );
							MarkSettingsFileChanged( );
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.GroupNode)
				{
					if (node.NextNode == null) return;
					if (node.NextNode.Tag is GroupNode preNodeDevice)
					{
						if (preNodeDevice.NodeType == NodeType.GroupNode)
						{
							// 可以进行上下调换位置
							TreeNode down = node.NextNode;
							down.Remove( );
							int index = node.Parent.Nodes.IndexOf( node );
							node.Parent.Nodes.Insert( index, down );
							MarkSettingsFileChanged( );
						}
					}
				}
			}
		}

		private void OneKeyCreateTestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.GroupNode)
				{
					foreach( var item in DeviceDefinitions.Devices.Values )
					{
						GroupNode group = EdgeReflectionHelper.CreateInstanceFrom( item );
						if (group != null)
						{
							group.Name = GetUniqueName( node, group.Name );
							group.Description = "用于测试的节点说明！";
							TreeNode nodeNew = new TreeNode( group.Name );
							nodeNew.ImageKey = item.ImageKey;
							nodeNew.SelectedImageKey = item.ImageKey;
							nodeNew.Tag = group;

							group.OnNameChanged += ( m, n ) =>
							{
								GroupNode gn = m as GroupNode;
								gn.Name = GetUniqueName( node, gn.Name );
								nodeNew.Text = gn.Name;
							};
							node.Nodes.Add( nodeNew );
							MarkSettingsFileChanged( );
						}
					}
					//foreach (DeviceType deviceType in SoftBasic.GetEnumValues<DeviceType>( ))
					//{
					//	GroupNode group = EdgeReflectionHelper.CreateInstanceFrom( deviceType );
					//	if (group != null)
					//	{
					//		group.Name = GetUniqueName( node, group.Name );
					//		group.Description = "用于测试的节点说明！";
					//		TreeNode nodeNew         = new TreeNode( group.Name );
					//		nodeNew.ImageKey         = EdgeReflectionHelper.GetImageKeyFrom( deviceType );
					//		nodeNew.SelectedImageKey = EdgeReflectionHelper.GetImageKeyFrom( deviceType );
					//		nodeNew.Tag              = group;

					//		group.OnNameChanged += ( m, n ) =>
					//		{
					//			GroupNode gn = m as GroupNode;
					//			gn.Name      = GetUniqueName( node, gn.Name );
					//			nodeNew.Text = gn.Name;
					//		};
					//		node.Nodes.Add( nodeNew );
					//		MarkSettingsFileChanged( );
					//	}
					//}
					node.Expand( );
				}
			}
		}

		private void CopyNodeToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode groupNode)
			{
				XElement element = AddTreeNode( node );
				Clipboard.SetText( element.ToString( ) );
			}
		}

		private void CopyDevicePathToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is DeviceNode)
			{
				// 获取设备的路径信息，例如 工厂一/车间一/ModbusTcp
				List<string> lists = new List<string>( );
				getDevicePath( node, lists );
				lists.Reverse( );

				Clipboard.SetText( HslTechnologyHelper.CombineEdgePath( lists.ToArray( ), false ) );
			}
		}

		private void getDevicePath( TreeNode node, List<string> paths )
		{
			if (node.ImageKey == "VirtualMachine_16xLG") return;
			if (node.Tag is NodeSerialPipe nodeSerialPipe)
			{
				if (nodeSerialPipe.UseAsGroupNode) paths.Add( nodeSerialPipe.Name );
			}
			else if (node.Tag is NodeSocketPipe nodeSocketPipe)
			{
				if (nodeSocketPipe.UseAsGroupNode) paths.Add( nodeSocketPipe.Name );
			}
			else if (node.Tag is DeviceNode device)
			{
				paths.Add( device.Name );
			}
			else if (node.Tag is NodeSingleThread nodeSingleThread)
			{
				if (nodeSingleThread.UseAsGroupNode) paths.Add( nodeSingleThread.Name );
			}
			else if (node.Tag is GroupNode groupNode)
			{
				paths.Add( groupNode.Name );
			}
			else
			{
				return;
			}

			if (node.Parent != null)
			{
				getDevicePath( node.Parent, paths );
			}
		}

		private async void 下载到设备ToolStripMenuItem_Click( object sender, EventArgs e )
		{
			if (this.serverSettings == null)
			{
				MessageBox.Show( "当前文件无法保存到服务器，或是没有进行服务器关联配置，请稍候重试！" );
				return;
			}

			// 检查设备数量是否超出最大限制
			if (this.serverSettings.MaxDevicesCount >= 0)
			{
				int count = GetDeviceCount( treeView1.Nodes[0] );
				if (count > this.serverSettings.MaxDevicesCount)
				{
					MessageBox.Show( $"当前配置的设备数量 [{count}] 超过网关允许的设备数量 [{this.serverSettings.MaxDevicesCount}] ，请删除一些设备后重试！" );
					return;
				}
			}
			string xml = string.Empty;
			try
			{
				xml = GetSaveXElement( ).ToString( );
			}
			catch( Exception ex )
			{
				MessageBox.Show( "当前的配置文件异常: " + ex.Message );
				return;
			}

			MqttSyncClient client = serverSettings.GetMqttSyncClient( );
			OperateResult<string> read = await client.ReadRpcAsync<string>( "Admin/XmlSettingsModify", new { data = xml } );
			if (read.IsSuccess)
			{
				MessageBox.Show( "下载到设备成功！" );
				isNodeSettingsModify = false;

				await RefreshXmlSettings( );
			}
			else
			{
				MessageBox.Show( "下载到设备失败！原因：" + read.Message );
			}
		}

		private void FormNodeSetting_FormClosing( object sender, FormClosingEventArgs e )
		{
			if (isNodeSettingsModify)
			{
				if (MessageBox.Show( "当前的配置信息已经修改过，但还未保存，是否直接退出并放弃？", "保存确认", MessageBoxButtons.YesNo, MessageBoxIcon.Warning ) != DialogResult.Yes)
				{
					e.Cancel = true;
				}
			}
		}

		#endregion

		#region Device Node Add

		private int GetLastGroupNodeIndex( TreeNode parent )
		{
			int index = 0;
			for (int i = 0; i < parent.Nodes.Count; i++)
			{
				if (parent.Nodes[i].Tag != null)
				{
					if (parent.Nodes[i].Tag.GetType( ) != typeof( GroupNode ))
					{
						index = i;
						break;
					}
					else
					{
						index++;
					}
				}
			}
			return index;
		}

		private void AddPluginsDeviceToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增插件设备信息
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType != NodeType.GroupNode &&
					nodeClass.NodeType != NodeType.GroupSerialPipe &&
					nodeClass.NodeType != NodeType.GroupSocketPipe &&
					nodeClass.NodeType != NodeType.GroupSingleThread) return;

				if (this.serverSettings == null)
				{
					MessageBox.Show( "当前无法添加插件设备，必须连接上网关才允许配置插件设备。" );
					return;
				}

				string path = GetNodePath( node );

				using (FormPluginsSelect form = new FormPluginsSelect( this.serverSettings.Plugins ))
				{
					if (form.ShowDialog( ) == DialogResult.OK)
					{
						PluginsDefinition plugins = form.SelectPluginsDefinition;
						if (plugins == null) return;

						string pluginsType = form.SelectPluginsDevice.Tag as string;
						if (string.IsNullOrEmpty( pluginsType )) return;

						if (!string.IsNullOrEmpty( form.SelectPluginsDevice.Template ))
						{
							XElement deviceXml = XElement.Parse( form.SelectPluginsDevice.Template );
							// 这是基于模板的设备信息
							DeviceType type = DeviceType.Device;
							try
							{
								type = SoftBasic.GetEnumFromString<DeviceType>( deviceXml.Attribute( nameof( DeviceNode.DeviceType ) ).Value );
							}
							catch (Exception ex)
							{
								MessageBox.Show( $"当前类型 [{deviceXml.Attribute( nameof( DeviceNode.DeviceType ) ).Value}] 获取失败：{ex.Message}\r\n" + $"无法进行添加设备", "错误提示" );
								return;
							}
							// 区分插件设备和内置设备，如果是内置设备，可以从类型反射加载
							DeviceNode dn = null;
							if (type == DeviceType.Plugins)
							{
								MessageBox.Show( $"当前模板是插件模板，不支持添加！", "错误提示" );
								return;
							}
							else
							{
								dn = EdgeReflectionHelper.CreateInstanceFrom( type );
								dn.LoadByXmlElement( deviceXml );
							}

							PropertyInfo propertyInfo = dn.GetType( ).GetProperty( nameof( dn.PluginsType ) );
							propertyInfo.SetValue( dn, pluginsType );

							dn.Template = pluginsType;
							FormDeviceAdd deviceAdd = new FormDeviceAdd( true, path, dn, this.serverSettings, nodeClass );
							//deviceAdd.Icon = Util.ConvertToIcon( Properties.Resources.Class_489 );
							if (deviceAdd.ShowDialog( ) == DialogResult.OK)
							{
								CreateTreeNodeByNodeType( node, deviceAdd.GetGroupNode( ) );
								MarkSettingsFileChanged( );
							}
						}
						else
						{
							if (this.serverSettings.PluginsDefinition == null)
							{
								MessageBox.Show( "当前插件资源为空，当新安装一个插件的时候，设备配置界面需要重新打开。" );
								return;
							}
							if (!this.serverSettings.PluginsDefinition.ContainsKey( pluginsType )) return;

							using (FormPluginsDevice formDevice = new FormPluginsDevice( this.serverSettings, plugins,
								this.serverSettings.PluginsDefinition[pluginsType], true ))
							{
								if (formDevice.ShowDialog( ) == DialogResult.OK)
								{
									CreateTreeNodeByNodeType( node, formDevice.PluginsDeviceNode );
									MarkSettingsFileChanged( );
								}
							}
						}
					}
				}
			}
		}

		private XElement GetTemplateDeviceXmlList( )
		{
			XElement xElement = new XElement( GroupNode.RootTemplate );
			for (int i = 0; i < treeView1.Nodes.Count; i++)
			{
				if(treeView1.Nodes[i].Text == GroupNode.RootTemplate)
				{
					for (int j = 0; j < treeView1.Nodes[i].Nodes.Count; j++)
					{
						if (treeView1.Nodes[i].Nodes[j].Tag is GroupNode groupNode)
						{
							xElement.Add( groupNode.ToXmlElement( ) );
						}
					}
					break;
				}
			}
			return xElement;
		}

		private void AddTemplatetoolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				// 新增一个基于模板的设备
				using (FormTemplateDevice form = new FormTemplateDevice( GetNodePath( node ), this.serverSettings, GetTemplateDeviceXmlList( ) ))
				{
					if (form.ShowDialog( ) == DialogResult.OK)
					{
						CreateTreeNodeByNodeType( node, form.GetGroupNode( ) );
						MarkSettingsFileChanged( );
					}
				}
			}
		}

		private void AddRequestGroupToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增请求分类的节点信息
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			// 只能在设备下面添加
			if (node.Tag is DeviceNode deviceNode)
			{
				RequestGroupNode requestGroupNode = new RequestGroupNode( );
				FormDeviceAdd deviceAdd = new FormDeviceAdd( true, GetNodePath( node ), requestGroupNode, this.serverSettings, deviceNode );
				if (deviceAdd.ShowDialog( ) == DialogResult.Cancel) return;

				CreateTreeNodeByNodeType( node, requestGroupNode );
			}
			else if (node.Tag is RequestGroupNode groupNode)
			{
				MessageBox.Show( "请求分类下面只能跟请求，不能再创建多级分类" );
			}
		}

		private async void AddSourceReadRequestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增了原始数据的节点请求
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			await AddRequestForm( node, RequestType.SourceRead );
		}

		private async void AddDatabaseRequestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增加了数据库的请求信息
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			await AddRequestForm( node, RequestType.DatabaseOperate );
		}

		private async void AddScalarReadRequestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增了标量的数据请求
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			await AddRequestForm( node, RequestType.ScalarRead );
		}

		private async void AddScalarCacheRequestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增了标量的数据请求
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			await AddRequestForm( node, RequestType.ScalarCache );
		}

		private async void AddWriteRequestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增了定时写入数据的请求
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			await AddRequestForm( node, RequestType.WriteInterval );
		}

		private async void AddMethodCallRequestToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增了方法的调用请求
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			await AddRequestForm( node, RequestType.MethodCall );
		}

		private void OutputXmlToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 将当前的设备节点导出到XML元素
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			XElement element = AddTreeNode( node );
			FormShowText.ShowText( element.ToString( ) );
		}

		private void OutputJsonToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 将当前的设备节点导出到JSON元素
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			FormShowText.ShowText( HslTechnology.Edge.HslTechnologyHelper.ConvertXmlToJsonString( AddTreeNode( node ), Newtonsoft.Json.Formatting.Indented ) );
		}

		private DeviceNode GetParentDeviceNode( TreeNode node )
		{
			if (node.Tag is DeviceNode deviceNode1) return deviceNode1;
			if (node.Tag is RequestGroupNode requestGroupNode)
			{
				if (node.Parent.Tag is DeviceNode deviceNode2)
				{
					return deviceNode2;
				}
			}
			return null;
		}

		private async Task AddRequestForm( TreeNode node, RequestType requestType )
		{
			// 允许添加请求，先获取所有的规则列表
			// 显示数据请求
			DeviceNode deviceNode = GetParentDeviceNode( node );

			if (deviceNode != null)
			{
				if (requestType == RequestType.ScalarRead)
				{
					if (!deviceNode.IsSupportAddressRequest( ))
					{
						MessageBox.Show( "当前设备暂时不支持该请求方式！" );
						return;
					}
					using (FormScalarReadRequest formNode = new FormScalarReadRequest(
						this.serverSettings,
						deviceNode, 
						new ScalarReadRequest( ), 
						GetAlarmNodes( ), 
						GetOeeNodes( ) ))
						if (formNode.ShowDialog( ) == DialogResult.OK)
							CreateTreeNodeByNodeType( node, formNode.DeviceRequest );
				}
				else if (requestType == RequestType.ScalarCache)
				{
					using (FormScalarCacheRequest formNode = new FormScalarCacheRequest(
						this.serverSettings,
						deviceNode,
						new ScalarCacheRequest( ),
						GetAlarmNodes( ),
						GetOeeNodes( ) ))
						if (formNode.ShowDialog( ) == DialogResult.OK)
							CreateTreeNodeByNodeType( node, formNode.DeviceRequest );
				}
				else if (requestType == RequestType.SourceRead)
				{
					if (!deviceNode.IsSupportAddressRequest( ))
					{
						MessageBox.Show( "当前设备暂时不支持该请求方式！" );
						return;
					}
					using (FormSourceDataRequest formNode = new FormSourceDataRequest( true, GetNodePath( node ), this.serverSettings, deviceNode, new SourceReadRequest( ) ))
						if (formNode.ShowDialog( ) == DialogResult.OK)
							CreateTreeNodeByNodeType( node, formNode.DeviceRequest );
				}
				else if (requestType == RequestType.MethodCall)
				{
					// 先去服务器进行获取API资源信息，除非是基于本地配置的，才直接获取 DeviceType
					MqttRpcApiInfo[] rpcApiInfos = await GetMqttRpcApiInfos( deviceNode );
					using (Request.FormCallMethodRequest formNode = new Request.FormCallMethodRequest( this.serverSettings, deviceNode, rpcApiInfos, null ))
						if (formNode.ShowDialog( ) == DialogResult.OK)
							CreateTreeNodeByNodeType( node, formNode.MethodRequest );
				}
				else if ( requestType == RequestType.DatabaseOperate)
				{
					using (Request.FormDatabaseRequest formNode = new Request.FormDatabaseRequest( this.serverSettings, GetNodePath( node ), deviceNode, GetDatabaseNames( node ), null ))
						if (formNode.ShowDialog( ) == DialogResult.OK)
							CreateTreeNodeByNodeType( node, formNode.DatabaseRequestNode );
				}
				else if ( requestType == RequestType.WriteInterval)
				{
					using (Request.FormWriteTagRequest formNode = new Request.FormWriteTagRequest( this.serverSettings, GetNodePath( node, true ), deviceNode, null ))
						if (formNode.ShowDialog( ) == DialogResult.OK)
							CreateTreeNodeByNodeType( node, formNode.WriteRequest );
				}
			}
		}

		private async Task<MqttRpcApiInfo[]> GetMqttRpcApiInfos( DeviceNode deviceNode )
		{
			DeviceType deviceType = deviceNode.DeviceType;
			MqttRpcApiInfo[] rpcApiInfos = null;
			if (this.serverSettings != null)
			{
				MqttSyncClient client = serverSettings.GetMqttSyncClient( );
				OperateResult<MqttRpcApiInfo[]> read = null;
				if(deviceNode.DeviceType == DeviceType.Plugins)
				{
					read = await client.ReadRpcAsync<MqttRpcApiInfo[]>( "Edge/GetMethodByDevicePlugins", new { pluginsType = deviceNode.PluginsType } );
				}
				else
				{
					read = await client.ReadRpcAsync<MqttRpcApiInfo[]>( "Edge/GetMethodByDeviceType", new { deviceType = deviceType } );
				}
				if (read.IsSuccess)
				{
					rpcApiInfos = read.Content;
				}
				else
				{
					if (deviceNode.DeviceType != DeviceType.Plugins)
					{
						MessageBox.Show( "从远程网关获取设备方法接口列表失败，切换为本地的方法接口信息！" );
						rpcApiInfos = EdgeReflectionHelper.GetMethodByDeviceType( deviceType.ToString( ) ).Content;
					}
					else
					{
						MessageBox.Show( "从远程网关获取插件设备方法接口列表失败！" );
						rpcApiInfos = null;
					}
				}
			}
			else
			{
				if (deviceNode.DeviceType != DeviceType.Plugins)
					rpcApiInfos = EdgeReflectionHelper.GetMethodByDeviceType( deviceType.ToString( ) ).Content; // 从本地加载
				else
					MessageBox.Show( "从远程网关获取插件设备方法接口列表失败！" );
			}

			return rpcApiInfos;
		}

		private async void EditMethodToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			if (node.Tag is DeviceNode deviceNode)
			{
				MqttRpcApiInfo[] rpcApiInfos = await GetMqttRpcApiInfos( deviceNode );
				// 寻找有没有方法权限节点存在，因为一个设备仅允许一个方法权限节点存在
				for (int i = 0; i < node.Nodes.Count; i++)
				{
					if(node.Nodes[i].Tag is MethodConfigNode methodConfigNode)
					{
						// 存在，则打开编辑信息
						using (Request.EditMethodInterfaceForm formNode = new Request.EditMethodInterfaceForm( deviceNode, rpcApiInfos, methodConfigNode ))
							if (formNode.ShowDialog( ) == DialogResult.OK)
							{
								node.Tag = formNode.MethodConfigNode;
								MarkSettingsFileChanged( );
							}
						return;
					}
				}

				if(rpcApiInfos == null)
				{
					MessageBox.Show( "从远程网关获取插件设备方法接口列表失败！" );
					return;
				}

				using (Request.EditMethodInterfaceForm formNode = new Request.EditMethodInterfaceForm( deviceNode, rpcApiInfos, null ))
					if (formNode.ShowDialog( ) == DialogResult.OK)
						CreateTreeNodeByNodeType( node, formNode.MethodConfigNode );
			}
		}

		private TreeNode CreateTreeNodeByNodeType( TreeNode node, GroupNode group, int index = -1 )
		{
			group.Name        = GetUniqueName( node, group.Name );
			if(group is DeviceNode deviceNode)  // 如果是插件设备，还要修改属性列表的名称信息
			{
				if(deviceNode.DeviceType == DeviceType.Plugins)
				{
					if(deviceNode.Tag is NodePropertyConfig[] propertyConfigs)
					{
						foreach (NodePropertyConfig propertyConfig in propertyConfigs)
						{
							if(propertyConfig.Name == nameof( GroupNode.Name ))
							{
								propertyConfig.DefaultValue = group.Name;
							}
						}
					}
				}
			}
			TreeNode nodeNew = Util.CreateTreeNodeFromXml( this.serverSettings, group, MarkSettingsFileChanged, NodeDisplayMode.ShowCombine );
			if (group.NodeType == NodeType.GroupNode)
				node.Nodes.Insert( GetLastGroupNodeIndex( node ), nodeNew );
			else
			{
				if (index < 0)
					node.Nodes.Add( nodeNew );
				else
					node.Nodes.Insert( index, nodeNew );
			}
			node.Expand( );
			MarkSettingsFileChanged( );
			return nodeNew;
		}

		private void AddGroupToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增分类的信息
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.GroupNode)
				{
					// 允许添加类别
					GroupNode group = new GroupNode( ) { Name = "分类名称", Description = "这是示例的分类信息" };
					FormDeviceAdd deviceAdd = new FormDeviceAdd( true, GetNodePath( node ), group, this.serverSettings, nodeClass );
					if (deviceAdd.ShowDialog( ) == DialogResult.Cancel) return;

					CreateTreeNodeByNodeType( node, group );
				}
				else if (nodeClass.NodeType == NodeType.GroupSerialPipe)
				{
					MessageBox.Show( "串口管道只能添加设备对象，无法添加分类。" );
				}
				else if (nodeClass.NodeType == NodeType.GroupSocketPipe)
				{
					MessageBox.Show( "网口管道只能添加设备对象，无法添加分类。" );
				}
				else if (nodeClass.NodeType == NodeType.GroupSingleThread)
				{
					MessageBox.Show( "单线程管道只能添加设备对象，无法添加分类。" );
				}
			}
		}

		private void AddSerialPipeToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增串口管道
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.GroupNode)
				{
					// 允许添加类别
					NodeSerialPipe group = new NodeSerialPipe( );
					FormDeviceAdd deviceAdd = new FormDeviceAdd( true, GetNodePath( node ), group, this.serverSettings, nodeClass );
					if (deviceAdd.ShowDialog( ) == DialogResult.Cancel) return;

					CreateTreeNodeByNodeType( node, group );
				}
			}
		}

		private void AddSocketPipeToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增网口管道
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.GroupNode)
				{
					// 允许添加类别
					NodeSocketPipe group    = new NodeSocketPipe( );
					FormDeviceAdd deviceAdd = new FormDeviceAdd( true, GetNodePath( node ), group, this.serverSettings, nodeClass );
					if (deviceAdd.ShowDialog( ) == DialogResult.Cancel) return;

					CreateTreeNodeByNodeType( node, group );
				}
			}
		}

		private void AddSingleThreadToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增单线程管道
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.GroupNode)
				{
					// 允许添加单线程管道
					NodeSingleThread group = new NodeSingleThread( );
					FormDeviceAdd deviceAdd = new FormDeviceAdd( true, GetNodePath( node ), group, this.serverSettings, nodeClass );
					if (deviceAdd.ShowDialog( ) == DialogResult.Cancel) return;

					CreateTreeNodeByNodeType( node, group );
				}
			}
		}

		private void 设备toolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增了设备，包括服务器的设备信息，用作纯节点使用
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.GroupNode || nodeClass.NodeType == NodeType.GroupSerialPipe || 
					nodeClass.NodeType == NodeType.GroupSocketPipe || nodeClass.NodeType == NodeType.GroupSingleThread)
				{
					// 允许添加设备
					if (sender is ToolStripMenuItem menuItem)
					{
						if (menuItem.Tag is DeviceItemDefinition definition)
						{
							GroupNode group = EdgeReflectionHelper.CreateInstanceFrom( definition );
							if (group == null)
							{
								MessageBox.Show( "当前不支持的设备操作，等待后续的更新！" );
								return;
							}
							if (nodeClass.NodeType == NodeType.GroupSerialPipe)
							{
								// TODO: 本来串口管道只能添加串口的设备，网口管道只能添加网口的，这里暂时都可以添加
							}

							FormDeviceAdd deviceAdd = new FormDeviceAdd( true, GetNodePath( node ), group, this.serverSettings, nodeClass );
							deviceAdd.Icon = Util.ConvertToIcon( menuItem.Image );
							if (deviceAdd.ShowDialog( ) == DialogResult.Cancel) return;

							CreateTreeNodeByNodeType( node, group );
						}
					}
				}
			}
		}

		private void addRegularToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
				if (nodeClass.NodeType == NodeType.GroupNode || nodeClass.NodeType == NodeType.DeviceNode)
					using (FormNodeRegular formNode = new FormNodeRegular( ))
						if (formNode.ShowDialog( ) == DialogResult.OK)
							CreateTreeNodeByNodeType( node, formNode.RegularNode );
		}

		private void AddressMappingToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;
			if (node.Tag is NodeModbusServer nodeClass)
				CreateTreeNodeByNodeType( node, new RegularAddressWriteMappingNode( ) );
		}

		private void SqlServerToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增一个微软数据库的信息
			TreeNode node = treeView1.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.GroupNode)
				{
					// 允许添加类别
					FormDeviceAdd deviceAdd = new FormDeviceAdd( true, GetNodePath( node ), new NodeSqlServer( ), this.serverSettings, nodeClass );
					deviceAdd.Text = "新增一个SQL SERVER数据库信息";
					if (deviceAdd.ShowDialog( ) == DialogResult.Cancel) return;

					CreateTreeNodeByNodeType( node, deviceAdd.GetGroupNode( ) );
				}
			}
		}


		#endregion

		#region DeviceRequestNode Add

		private string[] GetRegularsFromTreeNode( )
		{
			List<string> regularNodes = new List<string>( ) { "" };
			foreach (TreeNode item in treeView1.Nodes[2].Nodes)
			{
				if (item.Tag is RegularStructNode regularNode)
				{
					regularNodes.Add( regularNode.Name );
				}
			}
			return regularNodes.ToArray( );
		}


		private void addRegularScalarItemToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增单个的解析规则对象
			TreeNode node = treeView1.SelectedNode;
			using (FormRegularItemNode formNode = new FormRegularItemNode( null, GetAlarmNodes( ), GetOeeNodes( ) ))
				if (formNode.ShowDialog( ) == DialogResult.OK)
				{
					CreateTreeNodeByNodeType( node, formNode.RegularItemNode );
					UpdateTreeData( );
				}
		}

		private string[] GetStructNames( TreeNode treeNode )
		{
			List<string> array = new List<string>( );
			// 先寻找本地的结构体资源
			if (treeNode.Tag is SourceReadRequest && treeNode.Parent.Tag is DeviceNode)
			{
				for (int i = 0; i < treeNode.Parent.Nodes.Count; i++)
				{
					if (treeNode.Parent.Nodes[i].Tag is RegularStructItemNode structNode)
					{
						if (!array.Contains( structNode.Name ))
							array.Add( structNode.Name );
					}
				}
			}
			else if (treeNode.Tag is RegularStructNode &&
				treeNode.Parent.Tag is SourceReadRequest &&
				treeNode.Parent.Parent.Tag is DeviceNode)
			{
				for (int i = 0; i < treeNode.Parent.Parent.Nodes.Count; i++)
				{
					if (treeNode.Parent.Parent.Nodes[i].Tag is RegularStructItemNode structNode)
					{
						if (!array.Contains( structNode.Name ))
							array.Add( structNode.Name );
					}
				}
			}

			// 再寻找全局的结构体资源
			for (int i = 0; i < treeView1.Nodes.Count; i++)
			{
				if(treeView1.Nodes[i].Text == GroupNode.RootRegular)
				{
					foreach (TreeNode item in treeView1.Nodes[i].Nodes)
					{
						if (item.Tag is RegularStructItemNode structNode)
						{
							if (!array.Contains( structNode.Name ))
								array.Add( structNode.Name );
						}
					}
				}
			}
			return array.ToArray( );
		}

		private string[] GetDatabaseNames( TreeNode treeNode )
		{
			List<string> array = new List<string>( );
			// 再寻找全局的结构体资源
			for (int i = 0; i < treeView1.Nodes.Count; i++)
			{
				if (treeView1.Nodes[i].Text == GroupNode.RootDatabase)
				{
					foreach (TreeNode item in treeView1.Nodes[i].Nodes)
					{
						if (item.Tag is DatabaseNodeNet databaseNode)
						{
							if (!array.Contains( databaseNode.Name ))
								array.Add( databaseNode.Name );
						}
					}
				}
			}
			return array.ToArray( );
		}


		private List<AlarmNode> GetAlarmNodes( )
		{
			for (int i = 0; i < treeView1.Nodes.Count; i++)
			{
				if (treeView1.Nodes[i].Text == GroupNode.RootAlarm)
				{
					List<AlarmNode> array = new List<AlarmNode>( );
					foreach (TreeNode item in treeView1.Nodes[i].Nodes)
					{
						if (item.Tag is AlarmNode alarmNode)
						{
							array.Add( alarmNode );
						}
					}
					return array;
				}
			}
			return null;
		}

		private List<OeeNode> GetOeeNodes( )
		{
			for (int i = 0; i < treeView1.Nodes.Count; i++)
			{
				if (treeView1.Nodes[i].Text == GroupNode.RootOEE)
				{
					List<OeeNode> array = new List<OeeNode>( );
					foreach (TreeNode item in treeView1.Nodes[i].Nodes)
					{
						if (item.Tag is OeeNode oeeNode)
						{
							array.Add( oeeNode );
						}
					}
					return array;
				}
			}
			return null;
		}

		private RegularStructItemNode GetRegularStructItem( TreeNode regularTreeNode, RegularStructItemNode structNode )
		{
			structNode.RegularScalarNodes.Clear( );
			foreach (TreeNode scalarNode in regularTreeNode.Nodes)
			{
				if (scalarNode.Tag is RegularScalarNode regularScalar)
				{
					structNode.RegularScalarNodes.Add( regularScalar );
				}
			}
			return structNode;
		}

		private RegularStructItemNode GetRegularStructItem( string structName )
		{
			for (int i = 0; i < treeView1.Nodes.Count; i++)
			{
				if (treeView1.Nodes[i].Text == GroupNode.RootRegular)
				{
					foreach (TreeNode item in treeView1.Nodes[i].Nodes)
					{
						if (item.Tag is RegularStructItemNode structNode)
						{
							if(structNode.Name == structName)
							{
								return GetRegularStructItem( item, structNode );
							}
						}
					}
				}
			}
			return null;
		}

		private void AddRegularStructToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新增结构体的解析规则
			TreeNode node = treeView1.SelectedNode;

			using (FormRegularStruct formNode = new FormRegularStruct( null, GetStructNames( node ) ))
				if (formNode.ShowDialog( ) == DialogResult.OK)
				{
					CreateTreeNodeByNodeType( node, formNode.RegularStructNode );
					UpdateTreeData( );
				}
		}

		#endregion

		#region Alarm Add

		private void AddHexAlarmToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			FormCodeAlarms form = new FormCodeAlarms( );
			form.ShowAlarmInfo( new NodeAlarmHex( ), null );
			if (form.ShowDialog( ) == DialogResult.OK)
			{
				TreeNode tree = CreateTreeNodeByNodeType( node, form.AlarmNode );
				for (int i = 0; i < form.AlarmMarkItems.Count; i++)
				{
					CreateTreeNodeByNodeType( tree, form.AlarmMarkItems[i] );
				}
			}
			form.Dispose( );
		}

		private void AddIntegerAlarmToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			FormCodeAlarms form = new FormCodeAlarms( );
			form.ShowAlarmInfo( new NodeAlarmInteger( ), null );
			if (form.ShowDialog( ) == DialogResult.OK)
			{
				TreeNode tree = CreateTreeNodeByNodeType( node, form.AlarmNode );
				for (int i = 0; i < form.AlarmMarkItems.Count; i++)
				{
					CreateTreeNodeByNodeType( tree, form.AlarmMarkItems[i] );
				}
			}
			form.Dispose( );
		}

		private void AddBoolAlarmToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			FormCodeAlarms form = new FormCodeAlarms( );
			form.ShowAlarmInfo( new NodeAlarmBool( ), null );
			if (form.ShowDialog( ) == DialogResult.OK)
			{
				TreeNode tree = CreateTreeNodeByNodeType( node, form.AlarmNode );
				for (int i = 0; i < form.AlarmMarkItems.Count; i++)
				{
					CreateTreeNodeByNodeType( tree, form.AlarmMarkItems[i] );
				}
			}
			form.Dispose( );
		}

		private void AddDataRangeAlarmToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			FormDataRangeAlarm form = new FormDataRangeAlarm( null );
			if (form.ShowDialog( ) == DialogResult.OK)
			{
				CreateTreeNodeByNodeType( node, form.RangeAlarm );
			}
			form.Dispose( );
		}

		private void AddAlarmDatabaseToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			if (node == null) return;

			FormAlarmDatabase form = new FormAlarmDatabase( this.serverSettings, GetDatabaseNames( node ), null );
			if (form.ShowDialog( ) == DialogResult.OK)
			{
				CreateTreeNodeByNodeType( node, form.DatabaseRequestNode );
			}
			form.Dispose( );
		}

		#endregion

		#region Oee Add

		private void AddOEEToolStripMenuItem_Click( object sender, EventArgs e )
		{
			TreeNode node = treeView1.SelectedNode;
			// 新增OEE信息
			using (FormCodeOee form = new FormCodeOee( nodeDataOee: null, definitionNodes: null, timePeriods: null ))
				if(form.ShowDialog() == DialogResult.OK)
				{
					TreeNode tree = CreateTreeNodeByNodeType( node, form.NodeDataOee );
					for (int i = 0; i < form.OeeDefinitions.Length; i++)
					{
						CreateTreeNodeByNodeType( tree, form.OeeDefinitions[i] );
					}
				}
		}


		#endregion

		#region Node Edit

		private async void editToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 节点被选择的时候
			TreeNode node = treeView1.SelectedNode;
			if (node.ImageKey == "VirtualMachine_16xLG")
			{
				MessageBox.Show( "无法编辑系统节点！" );
				return;
			}
			GroupNode parent = node.Parent.Tag as GroupNode;

			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.NodeType == NodeType.GroupNode || nodeClass.NodeType == NodeType.DatabaseNode || nodeClass.NodeType == NodeType.RequestGroupNode)
				{
					// 编辑了节点
					GroupNode nodeCopy = nodeClass.Clone( );
					using (FormDeviceAdd formNode = new FormDeviceAdd( false, GetNodePath( node ), nodeCopy, serverSettings, parent ))
					{
						if (formNode.ShowDialog( ) == DialogResult.OK)
						{
							node.Text = nodeCopy.GetDisplayName( NodeDisplayMode.ShowCombine );
							node.Tag  = nodeCopy;
							MarkSettingsFileChanged( );
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.RegularStructItemNode)
				{
					if (node.Tag is RegularStructItemNode regularNode)
					{
						// 编辑了规则节点
						using (FormNodeRegular formNode = new FormNodeRegular( regularNode ))
						{
							if (formNode.ShowDialog( ) == DialogResult.OK)
							{
								node.Text = formNode.RegularNode.GetDisplayName( NodeDisplayMode.ShowCombine );
								node.Tag  = formNode.RegularNode;
								MarkSettingsFileChanged( );
							}
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.RegularStructNode)
				{
					if (node.Tag is RegularStructNode regularNode)
					{
						// 编辑了规则节点
						using (FormRegularStruct formNode = new FormRegularStruct( regularNode, GetStructNames( node ) ))
						{
							if (formNode.ShowDialog( ) == DialogResult.OK)
							{
								node.Text = formNode.RegularStructNode.GetDisplayName( NodeDisplayMode.ShowCombine );
								node.Tag  = formNode.RegularStructNode;
								MarkSettingsFileChanged( );
							}
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.RegularScalarNode)
				{
					if (node.Tag is RegularScalarNode regularItemNode)
					{
						// 编辑了规则节点
						using (FormRegularItemNode formNode = new FormRegularItemNode( regularItemNode, GetAlarmNodes( ), GetOeeNodes( ) ))
						{
							if (formNode.ShowDialog( ) == DialogResult.OK)
							{
								node.Text = formNode.RegularItemNode.GetDisplayName( NodeDisplayMode.ShowCombine );
								node.Tag  = formNode.RegularItemNode;
								MarkSettingsFileChanged( );
							}
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.RequestNode)
				{
					if (node.Tag is ScalarReadRequest scalarReadRequest)
					{
						if (scalarReadRequest.RequestType == RequestType.ScalarRead && (node.Parent.Tag is RobotNode robotNode || node.Parent.Tag is CncNode cncNode))
						{
							MessageBox.Show( "暂时不支持相关的数据请求" );
							return;
						}


						DeviceNode deviceNode = GetParentDeviceNode( node.Parent );
						if (deviceNode != null)
						{
							if (scalarReadRequest.RequestType == RequestType.ScalarRead)
							{
								using (FormScalarReadRequest formRequest = new FormScalarReadRequest(
									this.serverSettings,
									deviceNode,
									scalarReadRequest,
									GetAlarmNodes( ),
									GetOeeNodes( ) ))
								{
									if (formRequest.ShowDialog( ) == DialogResult.OK)
									{
										node.Text = formRequest.DeviceRequest.GetDisplayName( NodeDisplayMode.ShowCombine );
										node.Tag = formRequest.DeviceRequest;
										MarkSettingsFileChanged( );
									}
								}
							}
							else if (scalarReadRequest.RequestType == RequestType.ScalarCache)
							{
								using (FormScalarCacheRequest formRequest = new FormScalarCacheRequest(
									this.serverSettings,
									deviceNode,
									(ScalarCacheRequest)scalarReadRequest,
									GetAlarmNodes( ),
									GetOeeNodes( ) ))
								{
									if (formRequest.ShowDialog( ) == DialogResult.OK)
									{
										node.Text = formRequest.DeviceRequest.GetDisplayName( NodeDisplayMode.ShowCombine );
										node.Tag = formRequest.DeviceRequest;
										MarkSettingsFileChanged( );
									}
								}
							}
						}
					}
					else if (node.Tag is SourceReadRequest sourceReadRequest)
					{
						DeviceNode deviceNode = GetParentDeviceNode( node.Parent );
						if (deviceNode != null)
						{
							using (FormSourceDataRequest formRequest = new FormSourceDataRequest( false, GetNodePath( node ), this.serverSettings, deviceNode, sourceReadRequest ))
							{
								if (formRequest.ShowDialog( ) == DialogResult.OK)
								{
									node.Text = formRequest.DeviceRequest.GetDisplayName( NodeDisplayMode.ShowCombine );
									node.Tag = formRequest.DeviceRequest;
									MarkSettingsFileChanged( );
								}
							}
						}
						else
						{
							MessageBox.Show( "暂时不支持相关的数据请求" );
							return;
						}
					}
					else if (node.Tag is CallMethodRequest callMethodRequest)
					{
						if (node.Parent.Tag is DeviceNode deviceNode)
						{
							MqttRpcApiInfo[] apiInfos = await GetMqttRpcApiInfos( deviceNode );

							using (Request.FormCallMethodRequest formRequest = new Request.FormCallMethodRequest( this.serverSettings, deviceNode, apiInfos, callMethodRequest ))
							{
								if (formRequest.ShowDialog( ) == DialogResult.OK)
								{
									node.Text = formRequest.MethodRequest.GetDisplayName( NodeDisplayMode.ShowCombine );
									node.Tag = formRequest.MethodRequest;
									MarkSettingsFileChanged( );
								}
							}
						}
					}
					else if (node.Tag is DatabaseRequest databaseRequest)
					{
						if (node.Parent.Tag is DeviceNode deviceNode)
						{
							using (Request.FormDatabaseRequest formRequest = new Request.FormDatabaseRequest( this.serverSettings, GetNodePath( node ), deviceNode,
								GetDatabaseNames( node ), databaseRequest ))
							{
								if (formRequest.ShowDialog( ) == DialogResult.OK)
								{
									node.Text = formRequest.DatabaseRequestNode.GetDisplayName( NodeDisplayMode.ShowCombine );
									node.Tag = formRequest.DatabaseRequestNode;
									MarkSettingsFileChanged( );
								}
							}
						}
					}
					else if (node.Tag is ScalarWriteRequest writeRequest)
					{
						if (node.Parent.Tag is DeviceNode deviceNode)
						{
							using (Request.FormWriteTagRequest formNode = new Request.FormWriteTagRequest( this.serverSettings, GetNodePath( node, true ), deviceNode, writeRequest ))
							{
								if (formNode.ShowDialog( ) == DialogResult.OK)
								{
									node.Text = formNode.WriteRequest.GetDisplayName( NodeDisplayMode.ShowCombine );
									node.Tag = formNode.WriteRequest;
									MarkSettingsFileChanged( );
								}
							}
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.MethodConfig)
				{
					if (node.Parent.Tag is DeviceNode deviceNode)
					{
						if (node.Tag is MethodConfigNode methodConfigNode)
						{
							MqttRpcApiInfo[] rpcApiInfos = await GetMqttRpcApiInfos( deviceNode );
							using (Request.EditMethodInterfaceForm formNode = new Request.EditMethodInterfaceForm( deviceNode, rpcApiInfos, methodConfigNode ))
								if (formNode.ShowDialog( ) == DialogResult.OK)
								{
									node.Tag = formNode.MethodConfigNode;
									MarkSettingsFileChanged( );
								}
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.AlarmNode)
				{
					if (node.Tag is AlarmNode alarmNode)
					{
						if(alarmNode.AlarmType == AlarmType.Boolean || alarmNode.AlarmType == AlarmType.Integer || alarmNode.AlarmType == AlarmType.Hex)
						{
							FormCodeAlarms form = new FormCodeAlarms( );

							form.ShowAlarmInfo( alarmNode, GetAlarmDefinitionNodeListFromTreeNode( node ) );
							if (form.ShowDialog( ) == DialogResult.OK)
							{
								node.Text = form.AlarmNode.GetDisplayName( NodeDisplayMode.ShowCombine );
								node.Tag = form.AlarmNode;

								// 刷新报警的子节点信息
								Util.TreeNodeSpecifyCount( node, form.AlarmMarkItems.ToArray( ), new Action( MarkSettingsFileChanged ) );
							}
							form.Dispose( );
						}
						else if(alarmNode.AlarmType == AlarmType.DataRange)
						{
							FormDataRangeAlarm form = new FormDataRangeAlarm( (NodeDataRangeAlarm)alarmNode );
							if (form.ShowDialog( ) == DialogResult.OK)
							{
								node.Text = form.RangeAlarm.GetDisplayName( NodeDisplayMode.ShowCombine );
								node.Tag = form.RangeAlarm;
							}
							form.Dispose( );
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.AlarmDatabase)
				{
					if (node.Tag is AlarmDatabaseNode alarmDatabaseNode)
					{
						using (FormAlarmDatabase formRequest = new FormAlarmDatabase( this.serverSettings, GetDatabaseNames( node ), alarmDatabaseNode ))
						{
							if (formRequest.ShowDialog( ) == DialogResult.OK)
							{
								node.Text = formRequest.DatabaseRequestNode.GetDisplayName( NodeDisplayMode.ShowCombine );
								node.Tag = formRequest.DatabaseRequestNode;
								MarkSettingsFileChanged( );
							}
						}
					}
				}
				else if (nodeClass.NodeType == NodeType.OeeNode)
				{
					if(node.Tag is OeeNode oeeNode)
					{
						FormCodeOee form = new FormCodeOee( oeeNode, GetOeeDefinitionNodeListFromTreeNode( node ).ToArray( ), OeeTimePeriod.CreateFromXmlAttr( oeeNode.OeeTimePeriods ) );
						if (form.ShowDialog( ) == DialogResult.OK)
						{
							node.Text = form.NodeDataOee.GetDisplayName( NodeDisplayMode.ShowCombine );
							node.Tag = form.NodeDataOee;

							// 刷新OEE的子节点信息
							Util.TreeNodeSpecifyCount( node, form.OeeDefinitions.ToArray( ), new Action( MarkSettingsFileChanged ) );
						}
						form.Dispose( );
					}
				}
				else
				{
					if (node.Tag is DeviceNode device)
					{
						// 编辑了设备的节点数据
						if(device.DeviceType == DeviceType.Plugins)
						{
							if(device.Tag is XElement)
							{
								MessageBox.Show( "当前插件设备因为无法从网关获取信息，导致不可编辑，请连上网关重新尝试！" );
							}
							else if (device.Tag is NodePropertyConfig[] propertyConfigs)
							{
								if (this.serverSettings != null && this.serverSettings.Plugins != null)
								{
									PluginsDefinition plugins = this.serverSettings.Plugins.FirstOrDefault( m => m.DeviceDefinitions.ContainsKey( device.PluginsType ) );
									if (plugins != null)
									{
										using (FormPluginsDevice form = new FormPluginsDevice( this.serverSettings, plugins, propertyConfigs, false ))
										{
											if(form.ShowDialog( ) == DialogResult.OK)
											{
												node.Text = form.PluginsDeviceNode.GetDisplayName( NodeDisplayMode.ShowCombine );
												node.Tag = form.PluginsDeviceNode;
												MarkSettingsFileChanged( );
											}
										}
									}
									else
									{
										MessageBox.Show( $"当前的网关里找不到该设备[{device.PluginsType}]的插件，无法进行编辑，请重新安装该插件信息！" );
									}
								}
							}
						}
						else
						{
							GroupNode nodeCopy = EdgeReflectionHelper.CreateInstanceFrom( device.ToXmlElement( ) );
							using (FormDeviceAdd formNode = new FormDeviceAdd( false, GetNodePath( node ), nodeCopy, serverSettings, parent ))
							{
								if (formNode.ShowDialog( ) == DialogResult.OK)
								{
									node.Text = nodeCopy.GetDisplayName( NodeDisplayMode.ShowCombine );
									node.Tag = nodeCopy;
									MarkSettingsFileChanged( );
								}
							}
						}
					}
				}
			}
		}

		#endregion

		#region Node Delete

		private void deleteToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 删除节点信息
			TreeNode node = treeView1.SelectedNode;
			if (node.ImageKey == "VirtualMachine_16xLG")
			{
				MessageBox.Show( "无法删除系统节点！" );
				return;
			}

			if (node.Nodes.Count == 0)
			{
				node.Parent.Nodes.Remove( node );
				MarkSettingsFileChanged( );
			}
			else
			{
				if (MessageBox.Show( "还有子节点数据存在，是否真的删除节点及子节点信息？", "删除确认", MessageBoxButtons.YesNo, MessageBoxIcon.Warning ) == DialogResult.Yes)
				{
					node.Parent.Nodes.Remove( node );
					MarkSettingsFileChanged( );
				}
			}
		}

		#endregion

		#region Node Drag

		private void treeView1_ItemDrag( object sender, ItemDragEventArgs e )
		{
			if (e.Button == MouseButtons.Left)
			{
				TreeNode node = treeView1.SelectedNode;
				if (node == null) return;
				if (node.Parent == null) return;
				if (node.ImageKey == "VirtualMachine_16xLG") return;

				if (node.Tag is GroupNode groupNode)
				{
					if (groupNode.NodeType == NodeType.GroupNode ||
						groupNode.NodeType == NodeType.RequestGroupNode ||
						groupNode.NodeType == NodeType.GroupSerialPipe ||
						groupNode.NodeType == NodeType.GroupSocketPipe ||
						groupNode.NodeType == NodeType.GroupSingleThread ||
						groupNode.IsDeviceNode( ) ||
						groupNode.NodeType == NodeType.RequestNode ||
						groupNode.NodeType == NodeType.RegularScalarNode ||
						groupNode.NodeType == NodeType.RegularStructNode ||
						groupNode.NodeType == NodeType.MethodConfig ||
						groupNode.NodeType == NodeType.RegularWriteMapping)
						DoDragDrop( node, DragDropEffects.All );
				}
			}
		}

		private void treeView1_DragEnter( object sender, DragEventArgs e )
		{
			if (e.Data.GetDataPresent( typeof( TreeNode ) ) == true)
			{
				e.Effect = DragDropEffects.All;
			}
			else
			{
				e.Effect = DragDropEffects.None;
			}
		}

		private void treeView1_DragDrop( object sender, DragEventArgs e )
		{
			TreeNode treeNode = (TreeNode)e.Data.GetData( typeof( TreeNode ) );
			if (treeNode.Parent == null) return;
			if (treeNode.ImageKey == "VirtualMachine_16xLG") return;

			TreeNode downNode = treeView1.GetNodeAt( treeView1.PointToClient( new Point( e.X, e.Y ) ) );
			if (downNode == null) return;
			if (object.ReferenceEquals( treeNode, downNode )) return;  // 拖动到本体时，不进行任何的操作

			if (treeNode.Tag is GroupNode sourceNode)
			{
				if (sourceNode.NodeType == NodeType.DeviceNode)
				{
					// 设备的节点只能去类别节点，当去另一个设备节点时，就是插入到目标节点之前
					if (downNode.Tag is GroupNode targetNode)
					{
						if (targetNode.NodeType == NodeType.GroupNode || 
							targetNode.NodeType == NodeType.GroupSerialPipe || 
							targetNode.NodeType == NodeType.GroupSocketPipe ||
							targetNode.NodeType == NodeType.GroupSingleThread)
						{
							treeNode.Remove( );
							downNode.Nodes.Add( treeNode );
							if(!downNode.IsExpanded) downNode.Expand( );
							MarkSettingsFileChanged( );
						}
						else if (targetNode.IsDeviceNode( ))
						{
							if (downNode.Parent == null) return;
							treeNode.Remove( );
							int index = downNode.Parent.Nodes.IndexOf( downNode );
							downNode.Parent?.Nodes.Insert( index, treeNode );
							MarkSettingsFileChanged( );
						}
					}
				}
				else if (sourceNode.NodeType == NodeType.GroupNode ||
						 sourceNode.NodeType == NodeType.GroupSerialPipe ||
						 sourceNode.NodeType == NodeType.GroupSocketPipe ||
						 sourceNode.NodeType == NodeType.GroupSingleThread)
				{
					// 四种类别节点只能去类别节点，不能去其他的设备节点
					if (downNode.Tag is GroupNode targetNode)
					{
						if (targetNode.NodeType == NodeType.GroupNode)
						{
							treeNode.Remove( );
							downNode.Nodes.Insert( GetLastGroupNodeIndex( downNode ), treeNode );
							if (!downNode.IsExpanded) downNode.Expand( );
							MarkSettingsFileChanged( );
						}
					}
					else
					{
						MessageBox.Show( "分类相关的节点只能拖拽到类别节点里！" );
					}
				}
				else if (sourceNode.NodeType == NodeType.RequestNode || sourceNode.NodeType == NodeType.MethodConfig || sourceNode.NodeType == NodeType.RegularWriteMapping || sourceNode.NodeType == NodeType.RequestGroupNode)
				{
					// 请求类节点的移动暂时只能去本节点，以后实现在去相同的设备节点
					if (downNode.Tag is GroupNode targetNode)
					{
						if ((targetNode.NodeType == NodeType.RequestNode || targetNode.NodeType == NodeType.MethodConfig || targetNode.NodeType == NodeType.RegularWriteMapping) &&
							object.ReferenceEquals( downNode.Parent, treeNode.Parent ))
						{
							if (downNode.Parent == null) return;
							treeNode.Remove( );
							int index = downNode.Parent.Nodes.IndexOf( downNode );
							downNode.Parent?.Nodes.Insert( index, treeNode );
							MarkSettingsFileChanged( );
						}
						else if (targetNode.NodeType == NodeType.RequestGroupNode && object.ReferenceEquals( downNode.Parent, treeNode.Parent ))
						{
							if (downNode.Parent.Parent == null) return;
							treeNode.Remove( );
							downNode?.Nodes.Add( treeNode );
							MarkSettingsFileChanged( );
						}
						else
						{
							MessageBox.Show( "只能将该请求节点拖拽到本设备的请求节点下！" );
						}
					}
				}
				else if (sourceNode.NodeType == NodeType.RegularScalarNode || sourceNode.NodeType == NodeType.RegularStructNode)
				{
					// 规则解析的节点暂时只能去本节点
					if (downNode.Tag is GroupNode targetNode)
					{
						if (object.ReferenceEquals( downNode.Parent, treeNode.Parent ) && targetNode.NodeType == NodeType.RegularScalarNode ||
							targetNode.NodeType == NodeType.RegularStructNode)
						{
							if (downNode.Parent == null) return;
							treeNode.Remove( );
							int index = downNode.Parent.Nodes.IndexOf( downNode );
							downNode.Parent?.Nodes.Insert( index, treeNode );
							MarkSettingsFileChanged( );
						}
						else
						{
							MessageBox.Show( "只能将该请求节点拖拽到本设备的请求节点下！" );
						}
					}
				}
			}
		}

		#endregion

		#region Node Render

		private string GetNodePath( TreeNode node, bool pipHide = false )
		{
			string path = string.Empty;
			while (true)
			{
				if (node == null || node.ImageKey == "VirtualMachine_16xLG")
					return path;
				else
				{
					if (node.Tag is GroupNode group)
					{
						if (pipHide && group.NodeType == NodeType.GroupSerialPipe)
						{
							if ((group as NodeSerialPipe).UseAsGroupNode)
								path = "/" + group.Name + path;
						}
						else if (pipHide && group.NodeType == NodeType.GroupSocketPipe)
						{
							if ((group as NodeSocketPipe).UseAsGroupNode)
								path = "/" + group.Name + path;
						}
						else if (pipHide && group.NodeType == NodeType.GroupSingleThread)
						{
							if ((group as NodeSingleThread).UseAsGroupNode)
								path = "/" + group.Name + path;
						}
						else
							path = "/" + group.Name + path;
					}
					else
					{
						return path;
					}
					node = node.Parent;
				}
			}
		}

		private void treeView1_AfterSelect( object sender, TreeViewEventArgs e )
		{
			// 节点被选择的时候
			TreeView treeView = sender as TreeView;
			if (treeView == null) return;

			if (object.ReferenceEquals( treeView, treeView1 ))
				treeViewEx1.SelectedNode = null;
			else
				treeView1.SelectedNode = null;
			TreeNode node = treeView.SelectedNode;
			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.GetType( ) == typeof( SourceReadRequest ) || nodeClass.GetType( ) == typeof( ScalarReadRequest ))
					toolStripTextBox_nodePath.Text = GetNodePath( node );
				if (nodeClass.GetType( ) == typeof( RegularStructItemNode ) || nodeClass.GetType( ) == typeof( SourceReadRequest ))
				{
					treeNodeSelected = node;
					SetControlShow( dataViewControl );
					selectedRegularItemName = string.Empty;
					UpdateTreeData( );
				}
				else if (nodeClass.GetType( ) == typeof( RegularScalarNode ) || nodeClass.GetType( ) == typeof( RegularStructNode ))
				{
					toolStripTextBox_nodePath.Text = GetNodePath( node );
					treeNodeSelected = node.Parent;
					SetControlShow( dataViewControl );
					selectedRegularItemName = nodeClass.Name;
					UpdateTreeData( );
				}
				else if (nodeClass.GetType( ) == typeof( NodeAlarmBool ) || nodeClass.GetType( ) == typeof( NodeAlarmInteger ) ||
					nodeClass.GetType( ) == typeof( NodeAlarmHex ))
				{
					ShowAlarmNodeDataTable( node );
				}
				else if (nodeClass.GetType( ) == typeof( AlarmDefinitionNode ))
				{
					if (node.Parent == null) return;
					if (node.Parent.Tag is AlarmNode alarm)
					{
						if (alarm.AlarmType == AlarmType.Boolean ||
							alarm.AlarmType == AlarmType.Integer ||
							alarm.AlarmType == AlarmType.Hex)
							ShowAlarmNodeDataTable( node.Parent );
					}
				}
				else
				{
					// 显示选择的节点信息
					toolStripTextBox_nodePath.Text = GetNodePath( node );
					SetControlShow( propertyViewControl );
					// 根节点时，显示数据副本，防止被修改
					if (node.ImageKey == "VirtualMachine_16xLG" ||
						node.ImageKey == "house_16xLG" ||
						node.ImageKey == "FileGroup_10135_16x")
						propertyViewControl.SetObjectShow( new GroupNode( ) { Name = nodeClass.Name, Description = nodeClass.Description } );
					else
					{
						if(nodeClass is DeviceNode device)
						{
							if(device.DeviceType!= DeviceType.Plugins)
								propertyViewControl.SetObjectShow( nodeClass );
							else
								propertyViewControl.SetObjectShow( null );
						}
						else
							propertyViewControl.SetObjectShow( nodeClass );
					}
				}
			}
		}

		public void SetControlShow( UserControl control )
		{
			control.Visible = true;
			for (int i = 0; i < controls.Count; i++)
			{
				if(!object.ReferenceEquals(controls[i], control ))
				{
					controls[i].Visible = false;
				}
			}
		}

		private void ShowAlarmNodeDataTable( TreeNode treeNode )
		{
			toolStripTextBox_nodePath.Text = "";
			SetControlShow( alarmItemDataControl );
			alarmItemDataControl.ShowList( GetAlarmDefinitionNodeListFromTreeNode( treeNode ) );
		}

		private List<AlarmDefinitionNode> GetAlarmDefinitionNodeListFromTreeNode( TreeNode treeNode )
		{
			List<AlarmDefinitionNode> alarmDefinitions = new List<AlarmDefinitionNode>( treeNode.Nodes.Count );
			for (int i = 0; i < treeNode.Nodes.Count; i++)
			{
				if (treeNode.Nodes[i].Tag is AlarmDefinitionNode alarm)
					alarmDefinitions.Add( alarm );
			}
			return alarmDefinitions;
		}


		private List<OeeDefinitionNode> GetOeeDefinitionNodeListFromTreeNode( TreeNode treeNode )
		{
			List<OeeDefinitionNode> oeeDefinitions = new List<OeeDefinitionNode>( treeNode.Nodes.Count );
			for (int i = 0; i < treeNode.Nodes.Count; i++)
			{
				if (treeNode.Nodes[i].Tag is OeeDefinitionNode oeeDef)
					oeeDefinitions.Add( oeeDef );
			}
			return oeeDefinitions;
		}


		#endregion

		#region Render Bitmap

		private void UpdateTreeData( )
		{
			if (treeNodeSelected == null) return;
			if (treeNodeSelected.Tag is RegularStructItemNode regularNode)
			{
				if (panel1.Width < 10) return;

				List<RegularScalarNode> regularNodes = new List<RegularScalarNode>( );
				foreach (TreeNode item in treeNodeSelected.Nodes)
				{
					if (item.Tag is RegularScalarNode regular)
					{
						regularNodes.Add( regular );
					}
				}

				dataViewControl?.UpdateDeviceAddress( string.Empty );
				dataViewControl?.UpdateDataView( null, regularNodes, null, selectedRegularItemName, regularNode.StructLength );
			}
			else if (treeNodeSelected.Tag is SourceReadRequest sourceRequest)
			{
				if (panel1.Width < 10) return;

				List<RegularScalarNode> regularNodes = new List<RegularScalarNode>( );
				List<RegularStructNode> structNodes = new List<RegularStructNode>( );
				foreach (TreeNode item in treeNodeSelected.Nodes)
				{
					if (item.Tag is RegularScalarNode regular)
					{
						regularNodes.Add( regular );
					}
					else if (item.Tag is RegularStructNode structNode)
					{
						bool isLocal = false;
						// 然后去查找相关的key，此处应该先查询本地的，再查询全局的
						if (treeNodeSelected.Parent.Tag is DeviceNode)
						{
							for (int i = 0; i < treeNodeSelected.Parent.Nodes.Count; i++)
							{
								if (treeNodeSelected.Parent.Nodes[i].Tag is RegularStructItemNode localStructNode)
								{
									if (localStructNode.Name == structNode.StructName)
									{
										structNode.RegularStructItem = GetRegularStructItem( treeNodeSelected.Parent.Nodes[i], localStructNode );
										isLocal = true;
										break;
									}
								}
							}
						}
						// 如果本地没有相关的结构体资源，则去全局的资源里查找
						if (!isLocal) structNode.RegularStructItem = GetRegularStructItem( structNode.StructName );
						structNodes.Add( structNode );
					}
				}

				int max = -1;

				DeviceNode deviceNode = GetParentDeviceNode( treeNodeSelected.Parent );
				if (deviceNode != null)
				{
					if (deviceNode.DeviceType == DeviceType.Plugins)
					{
						if (this.serverSettings == null)
						{
							max = sourceRequest.GetLength( -1 );  // 一般来说，是不会为空的
						}
						else
						{
							// 基于插件的模式
							PluginsDefinition pluginsDefinition = this.serverSettings.Plugins.First( m => m.DeviceDefinitions.ContainsKey( deviceNode.PluginsType ) );
							if (pluginsDefinition != null)
							{
								PluginsDeviceDefinition pluginsDeviceDefinition = pluginsDefinition.DeviceDefinitions[deviceNode.PluginsType];
								// 根据传入的地址不同，去网关服务器的接口信息里，去拿取对应的值信息
								OperateResult<int> read = this.serverSettings.GetMqttSyncClient( ).ReadRpc<int>( "Plugins/EveryAddressOccupyByte",
									new { pluginsType = deviceNode.PluginsType, address = sourceRequest.Address } );
								int value = read.IsSuccess ? read.Content : -1;
								max = sourceRequest.GetLength( value );
							}
						}
					}
					else
					{
						max = sourceRequest.GetLength( deviceNode.GetEveryAddressOccupyByte( sourceRequest.Address ) );
					}
				}

				dataViewControl?.UpdateDeviceAddress( sourceRequest.Address );
				dataViewControl?.UpdateDataView( treeNodeSelected.Parent.Tag as DeviceNode, regularNodes, structNodes, selectedRegularItemName, max );
			}
		}

		#endregion

		#region ContextMenu Show


		private void TreeView1_KeyDown( object sender, KeyEventArgs e )
		{
			if (e.KeyCode == Keys.C && e.Control )
			{
				// 按下了复制键
				TreeNode node = treeView1.SelectedNode;
				if (node == null) return;

				if (node.ImageKey == "VirtualMachine_16xLG")
				{
					MessageBox.Show( "不能对根节点进行复制！" );
					return;
				}

				if (node.Tag is GroupNode groupNode)
				{
					groupNodeCopy = groupNode;
					XElement element = AddTreeNode( node );
					Clipboard.SetText( element.ToString( ) );
					e.Handled = true;
					e.SuppressKeyPress = true;
					//FormShowText.ShowText( element.ToString( ) );
				}
			}
			else if (e.KeyCode == Keys.V && e.Control)
			{
				// 按下了粘贴键
				TreeNode downNode = treeView1.SelectedNode;
				if (downNode == null) return;

				XElement elementCopy = null;
				string clip = Clipboard.GetText( );
				try
				{
					elementCopy = XElement.Parse( clip );
				}
				catch( Exception ex )
				{
					MessageBox.Show( "非法的XML数据，无法进行粘贴操作！" + ex.Message );
					return;
				}
				GroupNode sourceNode = Util.CreateGroupNodeFromXml( this.serverSettings, elementCopy, out bool isExpand );
				if (sourceNode == null)
				{
					MessageBox.Show( "非法的数据节点，解析失败！" );
					return;
				}

				e.Handled = true;
				e.SuppressKeyPress = true;
				if (downNode.Tag is GroupNode targetNode)
				{
					if (sourceNode.NodeType == NodeType.DeviceNode)
					{
						// 设备的节点只能粘贴到类别节点，当粘贴另一个设备节点时，就是插入到目标节点之前
						if (targetNode.IsGroup( ))
						{
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode, sourceNode ), elementCopy, true, MarkSettingsFileChanged );
						}
						// 目标也是个设备的话，就去平级的目录
						else if (targetNode.IsDeviceNode( ))
						{
							if (downNode.Parent == null) return;
							int index = downNode.Parent.Nodes.IndexOf( downNode );
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode.Parent, sourceNode, index ), elementCopy, true, MarkSettingsFileChanged );
						}
					}
					else if (sourceNode.IsGroup( ))
					{
						// 四种类别节点粘贴到类别节点，不能去其他的设备节点
						if (targetNode.NodeType == NodeType.GroupNode)
						{
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode, sourceNode ), elementCopy, true, MarkSettingsFileChanged );
						}
						else
						{
							MessageBox.Show( "当前复制的节点只能粘贴到分类节点！" );
						}
					}
					else if (sourceNode.NodeType == NodeType.RequestNode || sourceNode.NodeType == NodeType.MethodConfig)
					{
						// 如果请求类节点粘贴到请求节点的话，就去平级目录
						if (targetNode.NodeType == NodeType.RequestNode || targetNode.NodeType == NodeType.MethodConfig)
						{
							if (downNode.Parent == null) return;
							int index = downNode.Parent.Nodes.IndexOf( downNode );
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode.Parent, sourceNode, index ), elementCopy, true, MarkSettingsFileChanged );
						}
						// 请求类节点粘贴到设备节点下面
						else if (targetNode.IsDeviceNode( ))
						{
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode, sourceNode ), elementCopy, true, MarkSettingsFileChanged );
						}
					}
					else if (sourceNode.NodeType == NodeType.RegularScalarNode)
					{
						// 如果解析节点粘贴到原始字节请求
						if (targetNode.NodeType == NodeType.RequestNode)
						{
							if (targetNode is SourceReadRequest sourceReadRequest)
							{
								Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode, sourceNode ), elementCopy, true, MarkSettingsFileChanged );
							}
						}
						// 如果粘贴到结构体定义中去
						else if (targetNode.NodeType == NodeType.RegularStructItemNode)
						{
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode, sourceNode ), elementCopy, true, MarkSettingsFileChanged );
						}
						// 如果粘贴到解析节点，就去平级目录
						else if (targetNode.NodeType == NodeType.RegularScalarNode || targetNode.NodeType == NodeType.RegularStructNode)
						{
							if (downNode.Parent == null) return;
							int index = downNode.Parent.Nodes.IndexOf( downNode );
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode.Parent, sourceNode, index ), elementCopy, true, MarkSettingsFileChanged );
						}
					}
					else if (sourceNode.NodeType == NodeType.RegularStructNode)
					{
						// 如果结构体解析的粘贴到请求节点
						if (targetNode.NodeType == NodeType.RequestNode)
						{
							if (targetNode is SourceReadRequest sourceReadRequest)
							{
								Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode, sourceNode ), elementCopy, true, MarkSettingsFileChanged );
							}
						}
						// 如果粘贴到解析节点，就去平级目录
						else if (targetNode.NodeType == NodeType.RegularScalarNode || targetNode.NodeType == NodeType.RegularStructNode)
						{
							if (downNode.Parent == null) return;
							int index = downNode.Parent.Nodes.IndexOf( downNode );
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode.Parent, sourceNode, index ), elementCopy, true, MarkSettingsFileChanged );
						}
					}
					else if (sourceNode.NodeType == NodeType.RegularStructItemNode)
					{
						// 如果结构体定义的粘贴到结构体定义
						if (targetNode.NodeType == NodeType.RegularStructItemNode)
						{
							if (downNode.Parent == null) return;
							int index = downNode.Parent.Nodes.IndexOf( downNode );
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode.Parent, sourceNode, index ), elementCopy, true, MarkSettingsFileChanged );
						}
						// 如果粘贴到设备，变成子项
						else if (targetNode.IsDeviceNode( ))
						{
							Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode, sourceNode ), elementCopy, true, MarkSettingsFileChanged );
						}
						// 如果粘贴到设备的子项，就去平级目录
						else
						{
							if (downNode.Parent == null) return;
							if (downNode.Parent.Tag is GroupNode groupParent)
							{
								if (groupParent.IsDeviceNode( ))
								{
									int index = downNode.Parent.Nodes.IndexOf( downNode );
									Util.RenderEdgeSettingsTreeNode( serverSettings, CreateTreeNodeByNodeType( downNode.Parent, sourceNode, index ), elementCopy, true, MarkSettingsFileChanged );
								}
							}
						}
					}
				}
			}
		}

		private void TreeViewDeviceNodeClick( MouseEventArgs e, DeviceNode deviceNode )
		{
			bool supportAddress = true;
			if (deviceNode.DeviceType == DeviceType.Plugins)
			{
				if (this.serverSettings?.Plugins != null)
				{
					PluginsDefinition plugins = this.serverSettings.Plugins.FirstOrDefault( m => m.DeviceDefinitions.ContainsKey( deviceNode.PluginsType ) );
					if (plugins != null) supportAddress = plugins.DeviceDefinitions[deviceNode.PluginsType].IsSupportAddressRequest;
				}
			}
			else
				supportAddress = deviceNode.IsSupportAddressRequest( );
			if (supportAddress)
			{
				addScalarReadRequestToolStripMenuItem.Enabled = true;
				addSourceReadRequestToolStripMenuItem.Enabled = true;
			}
			else
			{
				addScalarReadRequestToolStripMenuItem.Enabled = false;
				addSourceReadRequestToolStripMenuItem.Enabled = false;
			}
			addressMappingToolStripMenuItem.Enabled = false;
			cMS_Request.Show( treeView1, e.Location );
		}

		private void treeView1_MouseDown( object sender, MouseEventArgs e )
		{
			if (e.Button == MouseButtons.Right)
			{
				treeView1.SelectedNode = treeView1.GetNodeAt( e.Location );
				// 右键了控件
				TreeNode node = treeView1.SelectedNode;
				if (node == null) return;

				// 右键了Regular节点
				if (node.Text == GroupNode.RootRegular && node.ImageKey == "VirtualMachine_16xLG")
				{
					cMS_Regular_Add.Show( treeView1, e.Location );
					return;
				}

				// 右击了Alarm节点
				if (node.Text == GroupNode.RootAlarm && node.ImageKey == "VirtualMachine_16xLG")
				{
					cMs_Alarm_Add.Show( treeView1, e.Location );
					return;
				}

				// 右击了OEE节点
				if (node.Text == GroupNode.RootOEE && node.ImageKey == "VirtualMachine_16xLG")
				{
					cMs_Oee_Add.Show( treeView1, e.Location );
					return;
				}

				// 右击了Database节点
				if (node.Text == GroupNode.RootDatabase && node.ImageKey == "VirtualMachine_16xLG")
				{
					cMs_Database_Add.Show( treeView1, e.Location );
					return;
				}

				// 右击了Templete节点
				if (node.Text == GroupNode.RootTemplate && node.ImageKey == "VirtualMachine_16xLG")
				{
					addGroupToolStripMenuItem.Enabled = false;
					addPipeToolStripMenuItem1.Enabled = false;
					cMS_Device.Show( treeView1, e.Location );
					return;
				}

				addGroupToolStripMenuItem.Enabled = true;
				addPipeToolStripMenuItem1.Enabled = true;

				if (node.Tag == null) return;
				if      (node.Tag.GetType( ) == typeof( GroupNode ))        cMS_Device.Show( treeView1, e.Location );
				else if (node.Tag.GetType( ) == typeof( NodeSerialPipe ))   cMS_Device.Show( treeView1, e.Location );
				else if (node.Tag.GetType( ) == typeof( NodeSocketPipe ))   cMS_Device.Show( treeView1, e.Location );
				else if (node.Tag.GetType( ) == typeof( NodeSingleThread )) cMS_Device.Show( treeView1, e.Location );
				else if (node.Tag.GetType( ) == typeof( NodeModbusServer ))
				{
					addressMappingToolStripMenuItem.Enabled = true;            // 仅仅 Modbus 服务器开启地址映射功能
					cMS_Request.Show( treeView1, e.Location );
				}
				else if (node.Tag is RequestGroupNode requestGroupNode)
				{
					if (node.Parent.Tag is DeviceNode deviceNode)
					{
						TreeViewDeviceNodeClick( e, deviceNode );
					}
				}
				else if (node.Tag is DeviceNode deviceNode)
				{
					TreeViewDeviceNodeClick( e, deviceNode );
				}
				else if (node.Tag is ScalarReadRequest)              cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示标量请求右键菜单
				else if (node.Tag is SourceReadRequest)              cMS_EditSourceDataRequest.Show( treeView1, e.Location );  // 显示原始字节请求右键菜单
				else if (node.Tag is CallMethodRequest)              cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示方法调用时右键菜单[只有编辑，删除]
				else if (node.Tag is ScalarWriteRequest)             cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示方法调用时右键菜单[只有编辑，删除]
				else if (node.Tag is RegularStructItemNode)          cMS_EditRegular.Show(           treeView1, e.Location );  // 显示结构体规则右键菜单
				else if (node.Tag is RegularScalarNode)              cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示标量规则右键菜单[只有编辑，删除]
				else if (node.Tag is RegularStructNode)              cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示结构体节点右键菜单[只有编辑，删除]
				else if (node.Tag is AlarmNode)                      cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示报警配置点右键菜单[只有编辑，删除]
				else if (node.Tag is OeeNode)                        cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示OEE配置点右键菜单[只有编辑，删除]
				else if (node.Tag is MethodConfigNode)               cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 方法接口编辑右键菜单[只有编辑，删除]
				else if (node.Tag is DatabaseRequest)                cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 数据库请求的编辑右键菜单[只有编辑，删除]
				else if (node.Tag is DatabaseNodeNet)                cMs_Database_Edit.Show(         treeView1, e.Location );  // 数据库右键操作
				else if (node.Tag is AlarmDatabaseNode)              cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示报警存储数据库时右键菜单[只有编辑，删除]
				else if (node.Tag is RegularAddressWriteMappingNode) cMs_EditScalarRequest.Show(     treeView1, e.Location );  // 显示地址映射节点时右键菜单[只有编辑，删除]
			}
			else if (e.Button == MouseButtons.Left)
			{
				treeView1.SelectedNode = treeView1.GetNodeAt( e.Location );
			}
		}

		private void TreeViewEx1_MouseDown( object sender, MouseEventArgs e )
		{
			if (e.Button == MouseButtons.Right)
			{
				treeViewEx1.SelectedNode = treeViewEx1.GetNodeAt( e.Location );
				// 右键了控件
				TreeNode node = treeViewEx1.SelectedNode;
				if (node == null) return;

				if (node.Parent == null) return;
				// 右键了配置文件节点
				cMs_EditXml.Show( treeViewEx1, e.Location );
			}
		}
		#endregion

		#region Node Save

		private int GetDeviceCount( TreeNode node )
		{
			int count = 0;

			if (node.Tag is GroupNode nodeClass)
			{
				if (nodeClass.IsDeviceNode( )) count++;
				foreach (TreeNode item in node.Nodes)
				{
					count += GetDeviceCount( item );
				}
			}
			return count;
		}

		private XElement AddTreeNode( TreeNode node )
		{
			if (node.Tag is GroupNode nodeClass)
			{
				XElement element = null;

				if (nodeClass is DeviceNode deviceNode)
				{
					if(deviceNode.DeviceType == DeviceType.Plugins)
					{
						if (deviceNode.Tag is NodePropertyConfig[] propertyConfig)
							element = NodePropertyConfig.CreateXmlFromPluginsProperty( propertyConfig );
						else if (deviceNode.Tag is XElement ele)
							element = ele;
					}
					else if (nodeClass is NodeSerialPipe nodeSerialPipe)
					{
						if (string.IsNullOrEmpty( nodeSerialPipe.PortName ))
						{
							throw new Exception( $"串口管道资源[{nodeSerialPipe.Name}] 的串口不能为空，请重新设置！" );
						}
					}
				}

				if(element == null) element = nodeClass.ToXmlElement( );
				foreach (TreeNode item in node.Nodes)
				{
					element.Add( AddTreeNode( item ) );
				}
				return element;
			}

			return null;
		}

		private XElement GetSaveXElement( )
		{
			XElement element = new XElement( "Settings" );
			element.SetAttributeValue( "XmlVersion", System.Reflection.Assembly.GetExecutingAssembly( ).GetName( ).Version.ToString( ) );
			element.SetAttributeValue( "XmlDate", DateTime.Now.ToString( "yyyy-MM-dd HH:mm:ss" ) );
			foreach (TreeNode item in treeView1.Nodes)
			{
				element.Add( AddTreeNode( item ) );
			}

			return element;
		}

		private void SaveNodes( string fileName )
		{
			try
			{
				XElement element = GetSaveXElement( );


				if (!string.IsNullOrEmpty( fileName ))
				{
					if (fileName.EndsWith( ".json" ))
					{
						System.Xml.XmlDocument xmlDocument = new System.Xml.XmlDocument( );
						xmlDocument.Load( element.CreateReader( ) );
						System.IO.File.WriteAllText( fileName, Newtonsoft.Json.JsonConvert.SerializeXmlNode( xmlDocument, Newtonsoft.Json.Formatting.Indented ), Encoding.UTF8 );
					}
					else
					{
						element.Save( fileName );
					}
					MessageBox.Show( "保存成功！" );
				}
				else
				{
					DialogResult = DialogResult.OK;
					return;
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show( "保存失败！原因：" + ex.Message );
			}
		}

		
		private void 另存为ToolStripMenuItem_Click( object sender, EventArgs e )
		{
			using (SaveFileDialog dialog = new SaveFileDialog( ))
			{
				if (this.serverSettings != null)
				{
					string name = string.IsNullOrEmpty( this.serverSettings.Alias ) ? this.serverSettings.EdgeID : this.serverSettings.Alias;
					dialog.FileName = name.Replace( ':', '_' );
				}
				dialog.Filter = "xml文件|*.xml|json文件|*.json";
				if (dialog.ShowDialog( ) == DialogResult.OK)
				{
					SaveNodes( dialog.FileName );
					isNodeSettingsModify = false;
				}
			}
		}

		private async void ToolStripButton_save_server_Click( object sender, EventArgs e )
		{
			if (serverSettings == null)
			{
				MessageBox.Show( "当前没有连接到网关，无法进行相关的操作。" );
				return;
			}
			string xml = string.Empty;
			try
			{
				xml = GetSaveXElement( ).ToString( );
			}
			catch (Exception ex)
			{
				MessageBox.Show( "当前的配置文件异常: " + ex.Message );
				return;
			}

			using (FormInputText inputText = new FormInputText( "请输入存储在网关设备的配置名称，不需要带后缀" ))
			{
				if (inputText.ShowDialog() == DialogResult.OK)
				{
					string name = inputText.InputText;
					if (string.IsNullOrEmpty( name ))
					{
						MessageBox.Show( "文件的名称不能为空" );
					}
					else
					{
						MqttSyncClient client = serverSettings.GetMqttSyncClient( );
						OperateResult<string> read = await client.ReadRpcAsync<string>( "Admin/XmlSettingsStandbyAdd", new { fileName = name, data = xml } );
						if (read.IsSuccess)
						{
							MessageBox.Show( "下载到设备的备用文件列表成功！" );
							isNodeSettingsModify = false;

							await RefreshXmlSettings( );
						}
						else
						{
							MessageBox.Show( "下载到设备的备用文件失败！原因：" + read.Message );
						}
					}
				}
			}
		}

		private bool design_xml = false;
		private void ToolStripButton_design_Click( object sender, EventArgs e )
		{
			if (!design_xml)
			{
				design_xml = true;
				// 显示XML编辑界面
				toolStripButton_design.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
				panel2.Visible = true;
				panel2.BringToFront( );

				textBox_design.Text = GetSaveXElement( ).ToString( );
			}
			else
			{
				design_xml = false;
				// 显示属性设计器界面s
				toolStripButton_design.Image = global::HslTechnology.EdgeViewer.Properties.Resources.XmlFile;
				panel2.Visible = false;
			}
		}

		#endregion

		#region Node Load

		private void button_xml_modify_Click( object sender, EventArgs e )
		{
			// 保存修改的XML文本
			if (!string.IsNullOrEmpty( textBox_design.Text ))
			{
				if ( LoadByXml( XElement.Parse( textBox_design.Text ) ) )
				{
					MarkSettingsFileChanged( );
					MessageBox.Show( "修改成功，请切换回可视化界面查看，或是本界面继续修改。" );
				}
			}
		}


		private void LoadByFile( string fileName )
		{
			if (!System.IO.File.Exists( fileName )) return;
			LoadByXml( XElement.Load( fileName ) );
		}

		private bool LoadByXml( XElement xml )
		{
			try
			{
				foreach (TreeNode treeNode in treeView1.Nodes)
				{
					treeNode.Nodes.Clear( );
				}

				XElement element = xml;
				if (element.Name != "Settings") return false;

				XElement[] elementChilds = element.Elements( ).ToArray( );
				// 提取Devices节点数据
				if (elementChilds.Length > 0) Util.RenderEdgeSettingsTreeNode( this.serverSettings, treeView1.Nodes[0], elementChilds[0], true, MarkSettingsFileChanged );
				if (elementChilds.Length > 1) Util.RenderEdgeSettingsTreeNode( this.serverSettings, treeView1.Nodes[1], elementChilds[1], true, MarkSettingsFileChanged );
				if (elementChilds.Length > 2) Util.RenderEdgeSettingsTreeNode( this.serverSettings, treeView1.Nodes[2], elementChilds[2], true, MarkSettingsFileChanged );
				if (elementChilds.Length > 3) Util.RenderEdgeSettingsTreeNode( this.serverSettings, treeView1.Nodes[3], elementChilds[3], true, MarkSettingsFileChanged );
				if (elementChilds.Length > 4) Util.RenderEdgeSettingsTreeNode( this.serverSettings, treeView1.Nodes[4], elementChilds[4], true, MarkSettingsFileChanged );
				if (elementChilds.Length > 5) Util.RenderEdgeSettingsTreeNode( this.serverSettings, treeView1.Nodes[5], elementChilds[5], true, MarkSettingsFileChanged );

				treeView1.Nodes[0].Expand( );
				toolStripLabel_xml_deviceCount.Text = GetDeviceCount( treeView1.Nodes[0] ).ToString( );
				return true;
			}
			catch (Exception ex)
			{
				MessageBox.Show( "加载文件失败，请确认是否系统生成的标准文件！原因：" + ex.Message );
				return false;
			}
		}

		private void 打开文件ToolStripMenuItem_Click( object sender, EventArgs e )
		{
			using (OpenFileDialog fileDialog = new OpenFileDialog( ))
			{
				fileDialog.Filter = "Xml File|*.xml";
				fileDialog.Multiselect = false;
				if (fileDialog.ShowDialog( ) == DialogResult.OK)
				{
					LoadByFile( fileDialog.FileName );
					MarkSettingsFileChanged( );
				}
			}
		}

		#endregion

		#region NodeName Update

		private static bool IsNodeSameNodeExist( TreeNode node, string name )
		{
			bool result = false;
			foreach (TreeNode item in node.Nodes)
			{
				if (item.Tag is GroupNode groupNode)
				{
					if(groupNode.Name == name)
					{
						result = true;
						break;
					}
				}
				else if (item.Text == name)
				{
					result = true;
					break;
				}
			}

			return result;
		}

		public static string GetUniqueName( TreeNode node, string name )
		{
			if (!IsNodeSameNodeExist( node, name )) return name;

			int index = 1;
			while (IsNodeSameNodeExist( node, name + index.ToString( ) ))
			{
				index++;
			}
			return name + index;
		}

		private static bool IsNodeSameGroupNodeExist( TreeNode node, string name )
		{
			bool result = false;
			foreach (TreeNode item in node.Nodes)
			{
				if(item.Tag != null)
				{
					if(item.Tag.GetType() == typeof( GroupNode ))
					{
						if (item.Text == name)
						{
							result = true;
							break;
						}
					}
				}
			}

			return result;
		}

		public static string GetUniqueGroupName( TreeNode node, string name )
		{
			if (!IsNodeSameGroupNodeExist( node, name )) return name;

			int index = 1;
			while (IsNodeSameGroupNodeExist( node, name + index ))
			{
				index++;
			}
			return name + index;
		}

		#endregion

		#region Private Member

		private ImageList SharpImageList;
		private bool isNodeSettingsModify = false;             // 指示系统的节点是否已经被编辑过
		private EdgeServerSettings serverSettings;              // 如果从Xml进行创建
		private TreeNode treeNodeSelected = null;              // 当前选择的树节点
		private string selectedRegularItemName = string.Empty; // 选择的规则节点变量的名称
		private ILogNet logNet;                                // 客户端的日志信息
		private SourceDataViewControl dataViewControl;         // 用于查看原始的字节排序的控件
		private PropertyViewControl propertyViewControl;       // 用于属性查看及修改的控件
		private AlarmItemDataTable alarmItemDataControl;       // 用于报警信息显示的控件
		private List<UserControl> controls;                    // 用于管理所有的界面控件内容
		private XElement xmlSettings;                          // 当前配置的设备信息
		private GroupNode groupNodeCopy;                       // 用于复制的节点信息

		#endregion

	}
}
