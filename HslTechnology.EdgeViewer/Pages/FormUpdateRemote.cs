using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Threading;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication;
using HslTechnology.Edge.Config;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;
using HslTechnology.EdgeViewer.Core;

namespace HslTechnology.EdgeViewer.Pages
{
	public partial class FormUpdateRemote : HslForm
	{
		public FormUpdateRemote( EdgeServerSettings serverSettings )
		{
			InitializeComponent( );
			this.serverSettings = serverSettings;
			this.Icon           = Util.GetWinformIcon( );
		}

		private void FormUpdateRemote_Load( object sender, EventArgs e )
		{

		}

		private EdgeServerSettings serverSettings;
		private bool isUpload = false;


		public void ThreadUploadFile( )
		{
			string[] files = Directory.GetFiles( textBox1.Text );
			List<string> filter = new List<string>( files );
			for (int i = filter.Count - 1; i >= 0; i--)
			{
				if (filter[i].EndsWith( ".txt" ) || filter[i].EndsWith( ".xml" ) || filter[i].EndsWith( ".bat" ) || filter[i].EndsWith( ".zip" ))
				{
					filter.RemoveAt( i );
				}
			}
			files = filter.ToArray( );

			IntegrationFileClient fileClient = serverSettings.GetServicesFileClient( );
			MqttSyncClient client = serverSettings.GetMqttSyncClient( );

			// 更新前先通知服务器清楚原先的缓存文件
			OperateResult operate = client.Read( "Admin/UndoUpdateRemoteProgram", null );
			if (!operate.IsSuccess)
			{
				Invoke( new Action( ( ) =>
				{
					textBox1.Enabled   = true;
					hslButton1.Enabled = true;
					hslButton2.Enabled = true;
					isUpload           = false;
					MessageBox.Show( "连接服务器失败，请稍候重试。" );
				} ) );
				return;
			}

			Invoke( new Action( ( ) =>
			 {
				 progressBar1.Maximum = files.Length;
				 label4.Text = "进度说明：服务器确认成功！";
			 } ) );
			Thread.Sleep( 1000 );

			for (int i = 0; i < files.Length; i++)
			{
				FileInfo fileInfo = new FileInfo( files[i] );

				Invoke( new Action( ( ) =>
				{
					progressBar1.Value = i + 1;
					label4.Text = "进度说明：上传文件(" + fileInfo.Name + ")";
					label2.Text = "总进度：   总文件数：" + files.Length.ToString( ).PadLeft( 3, ' ' ) + "   当前第" + (i + 1).ToString( ).PadLeft( 3, ' ' ) + "个文件";
				} ) );
				OperateResult upload = fileClient.UploadFile( files[i], fileInfo.Name, "", "", "", "", "", new Action<long, long>( ( long report, long max ) =>
				  {
					  Invoke( new Action( ( ) =>
					  {
						  progressBar2.Maximum = (int)max;
						  progressBar2.Value = (int)report;
					  } ) );
				  } ) );
				if (files.Length < 15) Thread.Sleep( 200 );
				if (!upload.IsSuccess)
				{
					MessageBox.Show( upload.Message );
					return;
				}
			}

			Invoke( new Action( ( ) =>
			{
				label4.Text = "等待服务器采集停止！";
			} ) );
			Thread.Sleep( 1000 );

			// 通知服务器更新了程序
			operate = client.Read( "Admin/UpdateRemoteProgram", null );
			if (!operate.IsSuccess)
			{
				Invoke( new Action( ( ) =>
				{
					textBox1.Enabled = true;
					hslButton1.Enabled = true;
					hslButton2.Enabled = true;
					isUpload = false;
					MessageBox.Show( "连接服务器失败，请稍候重试。" +operate.Message );
				} ) );
				return;
			}
			Invoke( new Action( ( ) =>
			{
				label4.Text = "已经通知网关重启！";
			} ) );

			Thread.Sleep( 1000 );
			OperateResult<string> communication = null;
			// 先检测到失败信号为止
			for (int i = 0; i < 100; i++)
			{
				Thread.Sleep( 100 );
				communication = client.ReadRpc<string>( "Edge/CommunicationTest", "{}" );
				if (!communication.IsSuccess) break;
			}
			// 再检测是否重启好
			for (int i = 0; i < 20; i++)
			{
				Thread.Sleep( 1000 );
				// 接口 Edge/CommunicationTest
				communication = client.ReadRpc<string>( "Edge/CommunicationTest", "{}" );
				if (communication.IsSuccess) break;
			}

			Invoke( new Action( ( ) =>
			{
				if (communication.IsSuccess)
					label4.Text = "程序更新完成！网关重启成功！";
				else
					label4.Text = "程序更新完成！但是超过30秒仍然没有检测到网关重启信号。";
				textBox1.Enabled = true;
				hslButton1.Enabled = true;
				hslButton2.Enabled = true;
				isUpload = false;
			} ) );
		}

		private void hslButton1_Click( object sender, EventArgs e )
		{
			// 启动上传
			if (string.IsNullOrEmpty( textBox1.Text ))
			{
				MessageBox.Show( this, "文件夹路径不能为空", "提示" );
				return;
			}

			if (!Directory.Exists( textBox1.Text ))
			{
				MessageBox.Show( this, "文件夹路径不存在", "提示" );
				return;
			}

			textBox1.Enabled = false;
			hslButton1.Enabled = false;
			hslButton2.Enabled = false;
			isUpload = true;

			Thread thread = new Thread( new ThreadStart( ThreadUploadFile ) );
			thread.IsBackground = true;
			thread.Start( );
		}

		private void hslButton2_Click( object sender, EventArgs e )
		{
			// 选择目录
			FolderBrowserDialog dialog = new FolderBrowserDialog( );
			dialog.Description = "请选择更新文件所在的文件夹";
			if (dialog.ShowDialog( ) == DialogResult.OK)
			{
				if (string.IsNullOrEmpty( dialog.SelectedPath ))
				{
					MessageBox.Show( this, "文件夹路径不能为空", "提示" );
					return;
				}
				textBox1.Text = dialog.SelectedPath;
			}
		}
	}
}
