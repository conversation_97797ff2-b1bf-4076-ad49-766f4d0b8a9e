using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Pages
{
    partial class FormServerSettings
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose( bool disposing )
        {
            if (disposing && (components != null))
            {
                components.Dispose( );
            }
            base.Dispose( disposing );
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent( )
        {
            System.Windows.Forms.TreeNode treeNode1 = new System.Windows.Forms.TreeNode("基本参数");
            System.Windows.Forms.TreeNode treeNode2 = new System.Windows.Forms.TreeNode("日志");
            System.Windows.Forms.TreeNode treeNode3 = new System.Windows.Forms.TreeNode("端口映射");
            System.Windows.Forms.TreeNode treeNode4 = new System.Windows.Forms.TreeNode("远程上传");
            System.Windows.Forms.TreeNode treeNode5 = new System.Windows.Forms.TreeNode("DTU");
            System.Windows.Forms.TreeNode treeNode6 = new System.Windows.Forms.TreeNode("主备模式");
            System.Windows.Forms.TreeNode treeNode7 = new System.Windows.Forms.TreeNode("选项", new System.Windows.Forms.TreeNode[] {
            treeNode1,
            treeNode2,
            treeNode3,
            treeNode4,
            treeNode5,
            treeNode6});
            this.treeView1 = new System.Windows.Forms.TreeView();
            this.panel1 = new System.Windows.Forms.Panel();
            this.button1 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.buttonSave1 = new HslTechnology.Edge.Controls.ButtonSave();
            this.buttonCancel1 = new HslTechnology.Edge.Controls.ButtonCancel();
            this.SuspendLayout();
            // 
            // treeView1
            // 
            this.treeView1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.treeView1.FullRowSelect = true;
            this.treeView1.HideSelection = false;
            this.treeView1.Location = new System.Drawing.Point(7, 12);
            this.treeView1.Name = "treeView1";
            treeNode1.Name = "节点10";
            treeNode1.Text = "基本参数";
            treeNode2.Name = "节点11";
            treeNode2.Text = "日志";
            treeNode3.Name = "节点12";
            treeNode3.Text = "端口映射";
            treeNode4.Name = "节点13";
            treeNode4.Text = "远程上传";
            treeNode5.Name = "节点14";
            treeNode5.Text = "DTU";
            treeNode6.Name = "节点15";
            treeNode6.Text = "主备模式";
            treeNode7.Name = "节点0";
            treeNode7.Text = "选项";
            this.treeView1.Nodes.AddRange(new System.Windows.Forms.TreeNode[] {
            treeNode7});
            this.treeView1.ShowLines = false;
            this.treeView1.Size = new System.Drawing.Size(173, 428);
            this.treeView1.TabIndex = 0;
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.AutoScroll = true;
            this.panel1.Location = new System.Drawing.Point(186, 12);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(493, 428);
            this.panel1.TabIndex = 1;
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(-212, 341);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(104, 35);
            this.button1.TabIndex = 2;
            this.button1.Text = "保存";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // button2
            // 
            this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.button2.Location = new System.Drawing.Point(-222, 340);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(104, 35);
            this.button2.TabIndex = 3;
            this.button2.Text = "关闭";
            this.button2.UseVisualStyleBackColor = true;
            // 
            // buttonSave1
            // 
            this.buttonSave1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonSave1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonSave1.Location = new System.Drawing.Point(427, 450);
            this.buttonSave1.Name = "buttonSave1";
            this.buttonSave1.Size = new System.Drawing.Size(121, 36);
            this.buttonSave1.TabIndex = 4;
            this.buttonSave1.Text = "buttonSave1";
            this.buttonSave1.Click += new System.EventHandler(this.buttonSave1_Click);
            // 
            // buttonCancel1
            // 
            this.buttonCancel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonCancel1.Location = new System.Drawing.Point(554, 450);
            this.buttonCancel1.Name = "buttonCancel1";
            this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
            this.buttonCancel1.TabIndex = 5;
            this.buttonCancel1.Text = "buttonCancel1";
            this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
            // 
            // FormServerSettings
            // 
            this.AcceptButton = this.button1;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.button2;
            this.ClientSize = new System.Drawing.Size(689, 498);
            this.Controls.Add(this.buttonCancel1);
            this.Controls.Add(this.buttonSave1);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.treeView1);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FormServerSettings";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "服务器参数设置";
            this.Load += new System.EventHandler(this.FormServerSettings_Load);
            this.Shown += new System.EventHandler(this.FormServerSettings_Shown);
            this.ResumeLayout(false);

        }

		#endregion

		private System.Windows.Forms.TreeView treeView1;
		private System.Windows.Forms.Panel panel1;
		private System.Windows.Forms.Button button1;
		private System.Windows.Forms.Button button2;
		private ButtonSave buttonSave1;
		private ButtonCancel buttonCancel1;
	}
}