using HslCommunication;
using HslCommunication.MQTT;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Config;
using System.Threading;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;
using System.Reflection;
using HslTechnology.EdgeViewer.Core;
using HslTechnology.Edge.Reflection;

namespace HslTechnology.EdgeViewer.Pages
{
	/// <summary>
	/// 新增加或是编辑一个设备
	/// </summary>
	public partial class FormDeviceAdd : HslForm
	{
		/// <summary>
		/// 实例化一个对象，新增或是编辑节点信息
		/// </summary>
		/// <param name="createNew">是否新增</param>
		/// <param name="path">路径信息</param>
		/// <param name="node">新增或是编辑的节点</param>
		/// <param name="serverSettings">服务器的配置资源对象</param>
		/// <param name="parent">父节点信息</param>
		public FormDeviceAdd( bool createNew, string path, GroupNode node, EdgeServerSettings serverSettings, GroupNode parent )
		{
			InitializeComponent( );
			this.Icon           = Util.ConvertToIcon( Util.GetImageByGroupNode( serverSettings, node ) );
			this.groupNode      = node;
			this.serverSettings = serverSettings;

			string head = createNew ? "添加新的" : "编辑当前的";

			if      (node.NodeType == NodeType.DatabaseNode)      this.Text = head + $"SQL SERVER数据库信息 [{path}]";
			else if (node.NodeType == NodeType.GroupSingleThread) this.Text = head + $"单线程管道，控制管道所有设备的单线程采集 [{path}]";
			else if (node.NodeType == NodeType.GroupSocketPipe)   this.Text = head + $"网口管道，管道下的网口设备将共用一个网络通道 [{path}]";
			else if (node.NodeType == NodeType.GroupSerialPipe)   this.Text = head + $"串口管道，管道下的串口设备将共用一个串口信息 [{path}]";
			else if (node.NodeType == NodeType.GroupNode)         this.Text = head + $"分类节点信息 [{path}]";
			else if (node.NodeType == NodeType.RequestGroupNode)  this.Text = head + $"设备请求分类信息 [{path}]";
			else
			{
				this.Text = head + $"设备 [{path}]";

			}
			this.groupParent    = parent;
			this.path = path;
		}

		/// <summary>
		/// 设置新的节点对象信息
		/// </summary>
		/// <param name="node">节点对象</param>
		public void SetGroupNode( GroupNode node )
		{
			this.groupNode = node;
			DeviceAdd_Load( null, new EventArgs( ) );
		}

		/// <summary>
		/// 获取当前的分类节点信息
		/// </summary>
		/// <returns>节点信息</returns>
		public GroupNode GetGroupNode( ) => this.groupNode;

		private void DeviceAdd_Load( object sender, EventArgs e )
		{
			// 请求网关的串口信息
			if (this.serverSettings != null)
			{
				MqttSyncClient client = this.serverSettings.GetMqttSyncClient( );
				OperateResult<string[]> read = client.ReadRpc<string[]>( "Edge/DeviceSerialPorts", new { needMapping = true } );
				if (read.IsSuccess)
				{
					textBox1.Lines = read.Content;
					SerialPortConverter.StandardValues = read.Content;
				}
				else
				{
					textBox1.Text = "串口资源加载失败！";
				}
			}

			//if (groupParent is NodeSerialPipe nodeSerialPipe)
			//{
			//	if (groupNode is DeviceNodeSerial nodeSerial)
			//	{
			//		// 底部的几个属性需要禁用
			//		SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.PortName ), true );
			//		SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.BaudRate ), true );
			//		SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.DataBits ), true );
			//		SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.StopBits ), true );
			//		SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.Parity ), true );
			//		SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.RtsEnable ), true );
			//	}
			//}
			//else
			//{
			//	if (groupNode is DeviceNodeSerial nodeSerial)
			//	{
			//		// 底部的几个属性需要禁用
			//		//SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.PortName ), false );
			//		//SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.BaudRate ), false );
			//		//SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.DataBits ), false );
			//		//SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.StopBits ), false );
			//		//SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.Parity ), false );
			//		//SetPropertyReadOnly( nodeSerial, nameof( DeviceNodeSerial.RtsEnable ), false );
			//	}
			//}

			this.propertyGrid1.SetSelectedObject( this.groupNode );
			if (this.groupNode is DeviceNode device)
			{
				label1.Text = "设备类型：" + device.DeviceType.ToString( );
			}
			if(this.serverSettings == null)
			{
				button1.Enabled = false;
			}
		}

		private void FormDeviceAdd_Shown( object sender, EventArgs e )
		{
			if (this.serverSettings == null) return;
		}

		private void button2_Click( object sender, EventArgs e )
		{
			OperateResult check = GroupNode.CheckGroupNameLegal( groupNode.Name );
			if (!check.IsSuccess)
			{
				MessageBox.Show( $"节点名称(Name)[{groupNode.Name}] 异常：{check.Message}" );
				return;
			}
			OnClickOk?.Invoke( sender, e );
			DialogResult = DialogResult.OK;
		}

		private void button3_Click( object sender, EventArgs e )
		{
			OnClickCancel?.Invoke( sender, e );
			DialogResult = DialogResult.Cancel;
		}

		private void SetPropertyReadOnly( object obj, string propertyName, bool isReadOnly )
		{
			try
			{
				Type type = typeof( ReadOnlyAttribute );
				PropertyDescriptorCollection props = TypeDescriptor.GetProperties( obj );
				AttributeCollection attrs = props[propertyName].Attributes;

				FieldInfo fld = type.GetField( "isReadOnly", BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.CreateInstance );
				fld.SetValue(attrs[type], isReadOnly);
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.ToString());
			}
		} 

		private async void button1_Click( object sender, EventArgs e )
		{
			// 通信测试
			if(this.serverSettings == null)
			{
				MessageBox.Show( "当前服务器不可用，无法进行测试，请通过服务器打开！" );
				return;
			}
			button1.Enabled = false;
			MqttSyncClient client = this.serverSettings.GetMqttSyncClient( );
			OperateResult<string> read = await client.ReadRpcAsync<string>( "Edge/DeviceCommunicationTest", 
				new { xmlDevice = this.groupNode.ToXmlElement( ).ToString( ) } );

			button1.Enabled = true;
			if (read.IsSuccess)
			{
				MessageBox.Show( "测试成功" );
			}
			else
			{
				MessageBox.Show( "测试失败！原因：" + read.Message );
			}
		}

		#region Event Handle

		/// <summary>
		/// 点击了OK的按钮
		/// </summary>
		public event EventHandler OnClickOk;

		/// <summary>
		/// 点击了取消的按钮
		/// </summary>
		public event EventHandler OnClickCancel;

		#endregion

		#region Private Member

		private GroupNode groupNode;
		private EdgeServerSettings serverSettings;
		private GroupNode groupParent = null;
		private string path = string.Empty;

		#endregion

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button3.PerformClick( );
		}
	}
}
