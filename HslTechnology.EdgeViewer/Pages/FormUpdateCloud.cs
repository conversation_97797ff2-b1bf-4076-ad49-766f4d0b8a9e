using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using HslCommunication.Enthernet;
using HslCommunication;
using HslTechnology.EdgeViewer.Controls;
using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Pages
{
    public partial class FormUpdateCloud : HslForm
    {
        public FormUpdateCloud( )
        {
            InitializeComponent( );
        }

        private void FormUpdateCloud_Load( object sender, EventArgs e )
        {
            simplifyClient = new NetSimplifyClient( Util.RemoteServerIp, 10153 );

            // 获取远程的版本号信息
            OperateResult<NetHandle, string> read = simplifyClient.ReadCustomerFromServer( 1, "" );
            if (read.IsSuccess)
            {
                textBox3.Text = read.Content2;
            }
        }



        private Guid token = Guid.NewGuid( );

        private void hslButton1_Click( object sender, EventArgs e )
        {
            try
            {
                HslCommunication.BasicFramework.SystemVersion version = new HslCommunication.BasicFramework.SystemVersion( textBox3.Text );
            }
            catch
            {
                MessageBox.Show( "版本号输入错误！" );
                return;
            }

            // 开始上传数据了
            textBox1.Enabled = false;
            textBox2.Enabled = false;
            textBox3.Enabled = false;
            hslButton1.Enabled = false;

            Thread thread = new Thread( new ThreadStart( ThreadUploadFile ) );
            thread.IsBackground = true;
            thread.Start( );
        }

        private void EndUI(string info )
        {
            Invoke( new Action( ( ) =>
            {
                textBox1.Enabled = true;
                textBox2.Enabled = true;
                textBox3.Enabled = true;
                hslButton1.Enabled = true;
                label_info.Text = info;

                MessageBox.Show( info );
            } ) );
            return;
        }
        private NetSimplifyClient simplifyClient;

        private void ThreadUploadFile( )
        {

            Invoke( new Action( ( ) =>
            {
                label_info.Text = "正在获取文件的列表...";
            } ) );

            string[] windowsFiles = new string[0];
            if (Directory.Exists( textBox1.Text ))
            {
                windowsFiles = Directory.GetFiles( textBox1.Text );
            }
            string[] linuxFiles = new string[0];
            if (Directory.Exists( textBox2.Text ))
            {
                linuxFiles = Directory.GetFiles( textBox2.Text );
            }

            IntegrationFileClient fileClient = new IntegrationFileClient( Util.RemoteServerIp, 10152 );
            fileClient.Token = this.token;

            Invoke( new Action( ( ) =>
            {
                progressBar1.Maximum = windowsFiles.Length + linuxFiles.Length;
            } ) );

            OperateResult<NetHandle,string> clearWindows = simplifyClient.ReadCustomerFromServer( 3, "" );
            if (!clearWindows.IsSuccess)
            {
                EndUI( "服务器连接失败..." );
                return;
            }

            if(clearWindows.Content1 != 1)
            {
                EndUI( $"服务器操作失败({clearWindows.Content2})..." );
                return;
            }

            OperateResult<NetHandle, string> clearLinux = simplifyClient.ReadCustomerFromServer( 4, "" );
            if (!clearLinux.IsSuccess)
            {
                EndUI( "服务器连接失败..." );
                return;
            }

            if (clearLinux.Content1 != 1)
            {
                EndUI( $"服务器操作失败({clearLinux.Content2})..." );
                return;
            }

            int uploadCount = 0;
            for (int i = 0; i < windowsFiles.Length; i++)
            {
                uploadCount++;
                Invoke( new Action( ( ) =>
                {
                    progressBar1.Value = uploadCount;
                    label_info.Text = "正在上传 " + windowsFiles[i];
                } ) );
                fileClient.UploadFile( windowsFiles[i], "Windows", "", "",
                    ( long already, long total ) =>
                    {
                        Invoke( new Action( ( ) =>
                         {
                             progressBar2.Maximum = (int)total;
                             progressBar2.Value = (int)already;
                         } ) );
                    } );
            }


            for (int i = 0; i < linuxFiles.Length; i++)
            {
                uploadCount++;
                Invoke( new Action( ( ) =>
                {
                    progressBar1.Value = uploadCount;
                    label_info.Text = "正在上传 " + linuxFiles[i];
                } ) );
                fileClient.UploadFile( linuxFiles[i], "Linux", "", "",
                    ( long already, long total ) =>
                    {
                        Invoke( new Action( ( ) =>
                        {
                            progressBar2.Maximum = (int)total;
                            progressBar2.Value = (int)already;
                        } ) );
                    } );
            }

            // 更新版本号
            OperateResult<NetHandle, string> updateversion = simplifyClient.ReadCustomerFromServer( 5, textBox3.Text );
            if (!clearLinux.IsSuccess)
            {
                EndUI( "更新版本号失败，请稍候重试。" );
                return;
            }

            EndUI( "更新成功" );
        }

        private void button1_Click( object sender, EventArgs e )
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog( );
            dialog.Description = "请选择更新文件所在的文件夹";
            if (dialog.ShowDialog( ) == DialogResult.OK)
            {
                if (string.IsNullOrEmpty( dialog.SelectedPath ))
                {
                    MessageBox.Show( this, "文件夹路径不能为空", "提示" );
                    return;
                }
                textBox1.Text = dialog.SelectedPath;
            }
        }

        private void button2_Click( object sender, EventArgs e )
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog( );
            dialog.Description = "请选择更新文件所在的文件夹";
            if (dialog.ShowDialog( ) == DialogResult.OK)
            {
                if (string.IsNullOrEmpty( dialog.SelectedPath ))
                {
                    MessageBox.Show( this, "文件夹路径不能为空", "提示" );
                    return;
                }
                textBox2.Text = dialog.SelectedPath;
            }
        }
    }
}
