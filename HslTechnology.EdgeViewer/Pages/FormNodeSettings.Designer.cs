using HslTechnology.EdgeViewer.Controls;

namespace HslTechnology.EdgeViewer.Pages
{
    partial class FormNodeSettings
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose( bool disposing )
        {
            if (disposing && (components != null))
            {
                components.Dispose( );
            }
            base.Dispose( disposing );
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent( )
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.TreeNode treeNode3 = new System.Windows.Forms.TreeNode("XmlStandby");
            System.Windows.Forms.TreeNode treeNode4 = new System.Windows.Forms.TreeNode("XmlHistory");
            this.cMS_Device = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addGroupToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addPipeToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.addSerialPipeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addSocketPipeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addSingleThreadToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addRobotToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addPlcToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addCncToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addSpecialToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addPluginsDeviceToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addTemplatetoolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editDeviceNodeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.deleteDeviceNodeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.moveDeviceNodeUpToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.moveDeviceNodeDownToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.propertyToolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMS_Request = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addRequestGroupToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addScalarReadRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addScalarCacheRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addSourceReadRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addWriteRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addMethodCallRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addDatabaseRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addLocalStructToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addressMappingToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editMethodToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.copyDevicePathToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.outputXmlToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.outputJsonToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.deleteRequestToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.copyNodeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.moveRequestUpToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.moveRequestDownToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.propertyToolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMs_EditScalarRequest = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.editRequestNodeToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.deleteRequestNodeToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.propertyToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMS_Regular_Add = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addRegularToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.propertyToolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMS_EditRegular = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addRegularItemToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.editRegularToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.deleteRegularToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.outputXmlRegularStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.propertyToolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripButton1 = new System.Windows.Forms.ToolStripButton();
            this.saveToolStripButton = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton_save_server = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton_download = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton4 = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton_design = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripTextBox_edgeName = new System.Windows.Forms.ToolStripTextBox();
            this.toolStripTextBox_nodePath = new System.Windows.Forms.ToolStripTextBox();
            this.toolStripLabel2 = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel_xml_deviceCount = new System.Windows.Forms.ToolStripLabel();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripLabel_edge_deviceMax = new System.Windows.Forms.ToolStripLabel();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.splitContainer2 = new System.Windows.Forms.SplitContainer();
            this.treeView1 = new HslTechnology.EdgeViewer.TreeViewEx();
            this.label1 = new System.Windows.Forms.Label();
            this.treeViewEx1 = new HslTechnology.EdgeViewer.TreeViewEx();
            this.label2 = new System.Windows.Forms.Label();
            this.panel1 = new System.Windows.Forms.Panel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.button_xml_modify = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.textBox_design = new System.Windows.Forms.TextBox();
            this.cMS_EditSourceDataRequest = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addRegularScalarToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.addRegularStructToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editSourceRequestToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.deleteSourceRequestToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.propertyToolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMs_Alarm_Add = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addBoolAlarmToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addIntegerAlarmToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addHexAlarmToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addDataRangeAlarmToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addAlarmDatabaseToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.propertyToolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMs_Oee_Add = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addOEEToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.propertyToolStripMenuItem8 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMs_Database_Add = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addDatabaseToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.sqlServerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.propertyToolStripMenuItem9 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMs_Database_Edit = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.editDatabaseToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.deleteDatabaseToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.outputXmlDatabaseToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.propertyToolStripMenuItem10 = new System.Windows.Forms.ToolStripMenuItem();
            this.cMs_EditXml = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.xmlLoadToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.xmlDeleteToolStripMenuItem = new HslTechnology.EdgeViewer.Controls.DeviceToolStripMenuItem();
            this.cMS_Device.SuspendLayout();
            this.cMS_Request.SuspendLayout();
            this.cMs_EditScalarRequest.SuspendLayout();
            this.cMS_Regular_Add.SuspendLayout();
            this.cMS_EditRegular.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).BeginInit();
            this.splitContainer2.Panel1.SuspendLayout();
            this.splitContainer2.Panel2.SuspendLayout();
            this.splitContainer2.SuspendLayout();
            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.cMS_EditSourceDataRequest.SuspendLayout();
            this.cMs_Alarm_Add.SuspendLayout();
            this.cMs_Oee_Add.SuspendLayout();
            this.cMs_Database_Add.SuspendLayout();
            this.cMs_Database_Edit.SuspendLayout();
            this.cMs_EditXml.SuspendLayout();
            this.SuspendLayout();
            // 
            // cMS_Device
            // 
            this.cMS_Device.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addGroupToolStripMenuItem,
            this.addPipeToolStripMenuItem1,
            this.addRobotToolStripMenuItem,
            this.addPlcToolStripMenuItem,
            this.addCncToolStripMenuItem,
            this.addSpecialToolStripMenuItem,
            this.addPluginsDeviceToolStripMenuItem,
            this.addTemplatetoolStripMenuItem,
            this.editDeviceNodeToolStripMenuItem,
            this.deleteDeviceNodeToolStripMenuItem,
            this.moveDeviceNodeUpToolStripMenuItem,
            this.moveDeviceNodeDownToolStripMenuItem,
            this.propertyToolStripMenuItem2});
            this.cMS_Device.Name = "contextMenuStrip1";
            this.cMS_Device.Size = new System.Drawing.Size(181, 312);
            // 
            // addGroupToolStripMenuItem
            // 
            this.addGroupToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.AddClass;
            this.addGroupToolStripMenuItem.Name = "addGroupToolStripMenuItem";
            this.addGroupToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.addGroupToolStripMenuItem.Text = "新增分类";
            // 
            // addPipeToolStripMenuItem1
            // 
            this.addPipeToolStripMenuItem1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addSerialPipeToolStripMenuItem,
            this.addSocketPipeToolStripMenuItem,
            this.addSingleThreadToolStripMenuItem});
            this.addPipeToolStripMenuItem1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addPipeToolStripMenuItem1.Name = "addPipeToolStripMenuItem1";
            this.addPipeToolStripMenuItem1.Size = new System.Drawing.Size(180, 22);
            this.addPipeToolStripMenuItem1.Text = "新增管道";
            // 
            // addSerialPipeToolStripMenuItem
            // 
            this.addSerialPipeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.SerialPort;
            this.addSerialPipeToolStripMenuItem.Name = "addSerialPipeToolStripMenuItem";
            this.addSerialPipeToolStripMenuItem.Size = new System.Drawing.Size(196, 22);
            this.addSerialPipeToolStripMenuItem.Text = "SerialPipe[串口]";
            // 
            // addSocketPipeToolStripMenuItem
            // 
            this.addSocketPipeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.NetworkAdapter;
            this.addSocketPipeToolStripMenuItem.Name = "addSocketPipeToolStripMenuItem";
            this.addSocketPipeToolStripMenuItem.Size = new System.Drawing.Size(196, 22);
            this.addSocketPipeToolStripMenuItem.Text = "SocketPipe[网口]";
            // 
            // addSingleThreadToolStripMenuItem
            // 
            this.addSingleThreadToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.ClassIcon;
            this.addSingleThreadToolStripMenuItem.Name = "addSingleThreadToolStripMenuItem";
            this.addSingleThreadToolStripMenuItem.Size = new System.Drawing.Size(196, 22);
            this.addSingleThreadToolStripMenuItem.Text = "SingleThread[单线程]";
            // 
            // addRobotToolStripMenuItem
            // 
            this.addRobotToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addRobotToolStripMenuItem.Name = "addRobotToolStripMenuItem";
            this.addRobotToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.addRobotToolStripMenuItem.Text = "新增Robot";
            // 
            // addPlcToolStripMenuItem
            // 
            this.addPlcToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addPlcToolStripMenuItem.Name = "addPlcToolStripMenuItem";
            this.addPlcToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.addPlcToolStripMenuItem.Text = "新增PLC等设备";
            // 
            // addCncToolStripMenuItem
            // 
            this.addCncToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addCncToolStripMenuItem.Name = "addCncToolStripMenuItem";
            this.addCncToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.addCncToolStripMenuItem.Text = "新增Cnc";
            // 
            // addSpecialToolStripMenuItem
            // 
            this.addSpecialToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addSpecialToolStripMenuItem.Name = "addSpecialToolStripMenuItem";
            this.addSpecialToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.addSpecialToolStripMenuItem.Text = "新增特殊设备";
            // 
            // addPluginsDeviceToolStripMenuItem
            // 
            this.addPluginsDeviceToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Library;
            this.addPluginsDeviceToolStripMenuItem.Name = "addPluginsDeviceToolStripMenuItem";
            this.addPluginsDeviceToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.addPluginsDeviceToolStripMenuItem.Text = "新增插件设备";
            // 
            // addTemplatetoolStripMenuItem
            // 
            this.addTemplatetoolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.SpherePreview;
            this.addTemplatetoolStripMenuItem.Name = "addTemplatetoolStripMenuItem";
            this.addTemplatetoolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.addTemplatetoolStripMenuItem.Text = "新增模板设备";
            // 
            // editDeviceNodeToolStripMenuItem
            // 
            this.editDeviceNodeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.PencilAngled_16xLG_color;
            this.editDeviceNodeToolStripMenuItem.Name = "editDeviceNodeToolStripMenuItem";
            this.editDeviceNodeToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.editDeviceNodeToolStripMenuItem.Text = "编辑节点";
            // 
            // deleteDeviceNodeToolStripMenuItem
            // 
            this.deleteDeviceNodeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_Cancel_16xLG;
            this.deleteDeviceNodeToolStripMenuItem.Name = "deleteDeviceNodeToolStripMenuItem";
            this.deleteDeviceNodeToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.deleteDeviceNodeToolStripMenuItem.Text = "删除节点";
            // 
            // moveDeviceNodeUpToolStripMenuItem
            // 
            this.moveDeviceNodeUpToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.arrow_up_color_16xLG;
            this.moveDeviceNodeUpToolStripMenuItem.Name = "moveDeviceNodeUpToolStripMenuItem";
            this.moveDeviceNodeUpToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.moveDeviceNodeUpToolStripMenuItem.Text = "向上移动";
            // 
            // moveDeviceNodeDownToolStripMenuItem
            // 
            this.moveDeviceNodeDownToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.arrow_down_color_16xLG;
            this.moveDeviceNodeDownToolStripMenuItem.Name = "moveDeviceNodeDownToolStripMenuItem";
            this.moveDeviceNodeDownToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.moveDeviceNodeDownToolStripMenuItem.Text = "向下移动";
            // 
            // propertyToolStripMenuItem2
            // 
            this.propertyToolStripMenuItem2.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem2.Name = "propertyToolStripMenuItem2";
            this.propertyToolStripMenuItem2.Size = new System.Drawing.Size(180, 22);
            this.propertyToolStripMenuItem2.Text = "节点属性";
            // 
            // cMS_Request
            // 
            this.cMS_Request.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addRequestGroupToolStripMenuItem,
            this.addRequestToolStripMenuItem,
            this.addLocalStructToolStripMenuItem,
            this.addressMappingToolStripMenuItem,
            this.editMethodToolStripMenuItem,
            this.copyDevicePathToolStripMenuItem,
            this.outputXmlToolStripMenuItem,
            this.outputJsonToolStripMenuItem,
            this.editRequestToolStripMenuItem,
            this.deleteRequestToolStripMenuItem,
            this.copyNodeToolStripMenuItem,
            this.moveRequestUpToolStripMenuItem,
            this.moveRequestDownToolStripMenuItem,
            this.propertyToolStripMenuItem3});
            this.cMS_Request.Name = "contextMenuStrip2";
            this.cMS_Request.Size = new System.Drawing.Size(161, 312);
            // 
            // addRequestGroupToolStripMenuItem
            // 
            this.addRequestGroupToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.AddClass;
            this.addRequestGroupToolStripMenuItem.Name = "addRequestGroupToolStripMenuItem";
            this.addRequestGroupToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.addRequestGroupToolStripMenuItem.Text = "新增请求分类";
            // 
            // addRequestToolStripMenuItem
            // 
            this.addRequestToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addScalarReadRequestToolStripMenuItem,
            this.addScalarCacheRequestToolStripMenuItem,
            this.addSourceReadRequestToolStripMenuItem,
            this.addWriteRequestToolStripMenuItem,
            this.addMethodCallRequestToolStripMenuItem,
            this.addDatabaseRequestToolStripMenuItem});
            this.addRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addRequestToolStripMenuItem.Name = "addRequestToolStripMenuItem";
            this.addRequestToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.addRequestToolStripMenuItem.Text = "新增Request";
            // 
            // addScalarReadRequestToolStripMenuItem
            // 
            this.addScalarReadRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Enum_582;
            this.addScalarReadRequestToolStripMenuItem.Name = "addScalarReadRequestToolStripMenuItem";
            this.addScalarReadRequestToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.addScalarReadRequestToolStripMenuItem.Text = "新增标量数据读取";
            // 
            // addScalarCacheRequestToolStripMenuItem
            // 
            this.addScalarCacheRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Enum_582;
            this.addScalarCacheRequestToolStripMenuItem.Name = "addScalarCacheRequestToolStripMenuItem";
            this.addScalarCacheRequestToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.addScalarCacheRequestToolStripMenuItem.Text = "新增标量缓存数据";
            // 
            // addSourceReadRequestToolStripMenuItem
            // 
            this.addSourceReadRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.hardware_16xLG;
            this.addSourceReadRequestToolStripMenuItem.Name = "addSourceReadRequestToolStripMenuItem";
            this.addSourceReadRequestToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.addSourceReadRequestToolStripMenuItem.Text = "新增原始字节读取";
            // 
            // addWriteRequestToolStripMenuItem
            // 
            this.addWriteRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Method_636;
            this.addWriteRequestToolStripMenuItem.Name = "addWriteRequestToolStripMenuItem";
            this.addWriteRequestToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.addWriteRequestToolStripMenuItem.Text = "新增定时写入请求";
            // 
            // addMethodCallRequestToolStripMenuItem
            // 
            this.addMethodCallRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Method_636;
            this.addMethodCallRequestToolStripMenuItem.Name = "addMethodCallRequestToolStripMenuItem";
            this.addMethodCallRequestToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.addMethodCallRequestToolStripMenuItem.Text = "新增方法调用请求";
            // 
            // addDatabaseRequestToolStripMenuItem
            // 
            this.addDatabaseRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Database_request;
            this.addDatabaseRequestToolStripMenuItem.Name = "addDatabaseRequestToolStripMenuItem";
            this.addDatabaseRequestToolStripMenuItem.Size = new System.Drawing.Size(184, 22);
            this.addDatabaseRequestToolStripMenuItem.Text = "新增数据库调用请求";
            // 
            // addLocalStructToolStripMenuItem
            // 
            this.addLocalStructToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addLocalStructToolStripMenuItem.Name = "addLocalStructToolStripMenuItem";
            this.addLocalStructToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.addLocalStructToolStripMenuItem.Text = "新增本地结构体";
            // 
            // addressMappingToolStripMenuItem
            // 
            this.addressMappingToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addressMappingToolStripMenuItem.Name = "addressMappingToolStripMenuItem";
            this.addressMappingToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.addressMappingToolStripMenuItem.Text = "新增写地址映射";
            // 
            // editMethodToolStripMenuItem
            // 
            this.editMethodToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.ASCube_16xLG;
            this.editMethodToolStripMenuItem.Name = "editMethodToolStripMenuItem";
            this.editMethodToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.editMethodToolStripMenuItem.Text = "编辑方法接口";
            // 
            // copyDevicePathToolStripMenuItem
            // 
            this.copyDevicePathToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.ClassIcon;
            this.copyDevicePathToolStripMenuItem.Name = "copyDevicePathToolStripMenuItem";
            this.copyDevicePathToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.copyDevicePathToolStripMenuItem.Text = "复制设备路径";
            // 
            // outputXmlToolStripMenuItem
            // 
            this.outputXmlToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.XmlFile;
            this.outputXmlToolStripMenuItem.Name = "outputXmlToolStripMenuItem";
            this.outputXmlToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.outputXmlToolStripMenuItem.Text = "导出XML";
            // 
            // outputJsonToolStripMenuItem
            // 
            this.outputJsonToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.JsonFile;
            this.outputJsonToolStripMenuItem.Name = "outputJsonToolStripMenuItem";
            this.outputJsonToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.outputJsonToolStripMenuItem.Text = "导出JSON";
            // 
            // editRequestToolStripMenuItem
            // 
            this.editRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.PencilAngled_16xLG_color;
            this.editRequestToolStripMenuItem.Name = "editRequestToolStripMenuItem";
            this.editRequestToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.editRequestToolStripMenuItem.Text = "编辑节点";
            // 
            // deleteRequestToolStripMenuItem
            // 
            this.deleteRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_Cancel_16xLG;
            this.deleteRequestToolStripMenuItem.Name = "deleteRequestToolStripMenuItem";
            this.deleteRequestToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.deleteRequestToolStripMenuItem.Text = "删除节点";
            // 
            // copyNodeToolStripMenuItem
            // 
            this.copyNodeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Copy_6524;
            this.copyNodeToolStripMenuItem.Name = "copyNodeToolStripMenuItem";
            this.copyNodeToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.copyNodeToolStripMenuItem.Text = "复制节点";
            this.copyNodeToolStripMenuItem.Visible = false;
            // 
            // moveRequestUpToolStripMenuItem
            // 
            this.moveRequestUpToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.arrow_up_color_16xLG;
            this.moveRequestUpToolStripMenuItem.Name = "moveRequestUpToolStripMenuItem";
            this.moveRequestUpToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.moveRequestUpToolStripMenuItem.Text = "向上移动";
            // 
            // moveRequestDownToolStripMenuItem
            // 
            this.moveRequestDownToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.arrow_down_color_16xLG;
            this.moveRequestDownToolStripMenuItem.Name = "moveRequestDownToolStripMenuItem";
            this.moveRequestDownToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.moveRequestDownToolStripMenuItem.Text = "向下移动";
            // 
            // propertyToolStripMenuItem3
            // 
            this.propertyToolStripMenuItem3.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem3.Name = "propertyToolStripMenuItem3";
            this.propertyToolStripMenuItem3.Size = new System.Drawing.Size(160, 22);
            this.propertyToolStripMenuItem3.Text = "节点属性";
            // 
            // cMs_EditScalarRequest
            // 
            this.cMs_EditScalarRequest.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.editRequestNodeToolStripMenuItem,
            this.deleteRequestNodeToolStripMenuItem,
            this.propertyToolStripMenuItem1});
            this.cMs_EditScalarRequest.Name = "contextMenuStrip3";
            this.cMs_EditScalarRequest.Size = new System.Drawing.Size(125, 70);
            // 
            // editRequestNodeToolStripMenuItem
            // 
            this.editRequestNodeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.PencilAngled_16xLG_color;
            this.editRequestNodeToolStripMenuItem.Name = "editRequestNodeToolStripMenuItem";
            this.editRequestNodeToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.editRequestNodeToolStripMenuItem.Text = "编辑节点";
            // 
            // deleteRequestNodeToolStripMenuItem
            // 
            this.deleteRequestNodeToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_Cancel_16xLG;
            this.deleteRequestNodeToolStripMenuItem.Name = "deleteRequestNodeToolStripMenuItem";
            this.deleteRequestNodeToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.deleteRequestNodeToolStripMenuItem.Text = "删除节点";
            // 
            // propertyToolStripMenuItem1
            // 
            this.propertyToolStripMenuItem1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem1.Name = "propertyToolStripMenuItem1";
            this.propertyToolStripMenuItem1.Size = new System.Drawing.Size(124, 22);
            this.propertyToolStripMenuItem1.Text = "节点属性";
            // 
            // cMS_Regular_Add
            // 
            this.cMS_Regular_Add.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addRegularToolStripMenuItem,
            this.propertyToolStripMenuItem4});
            this.cMS_Regular_Add.Name = "contextMenuStrip3";
            this.cMS_Regular_Add.Size = new System.Drawing.Size(146, 48);
            // 
            // addRegularToolStripMenuItem
            // 
            this.addRegularToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addRegularToolStripMenuItem.Name = "addRegularToolStripMenuItem";
            this.addRegularToolStripMenuItem.Size = new System.Drawing.Size(145, 22);
            this.addRegularToolStripMenuItem.Text = "新增Regular";
            // 
            // propertyToolStripMenuItem4
            // 
            this.propertyToolStripMenuItem4.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem4.Name = "propertyToolStripMenuItem4";
            this.propertyToolStripMenuItem4.Size = new System.Drawing.Size(145, 22);
            this.propertyToolStripMenuItem4.Text = "节点属性";
            // 
            // cMS_EditRegular
            // 
            this.cMS_EditRegular.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addRegularItemToolStripMenuItem,
            this.editRegularToolStripMenuItem,
            this.deleteRegularToolStripMenuItem,
            this.outputXmlRegularStripMenuItem,
            this.propertyToolStripMenuItem5});
            this.cMS_EditRegular.Name = "contextMenuStrip1";
            this.cMS_EditRegular.Size = new System.Drawing.Size(172, 114);
            // 
            // addRegularItemToolStripMenuItem
            // 
            this.addRegularItemToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addRegularItemToolStripMenuItem.Name = "addRegularItemToolStripMenuItem";
            this.addRegularItemToolStripMenuItem.Size = new System.Drawing.Size(171, 22);
            this.addRegularItemToolStripMenuItem.Text = "新增RegularItem";
            // 
            // editRegularToolStripMenuItem
            // 
            this.editRegularToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.PencilAngled_16xLG_color;
            this.editRegularToolStripMenuItem.Name = "editRegularToolStripMenuItem";
            this.editRegularToolStripMenuItem.Size = new System.Drawing.Size(171, 22);
            this.editRegularToolStripMenuItem.Text = "编辑Regular";
            // 
            // deleteRegularToolStripMenuItem
            // 
            this.deleteRegularToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_Cancel_16xLG;
            this.deleteRegularToolStripMenuItem.Name = "deleteRegularToolStripMenuItem";
            this.deleteRegularToolStripMenuItem.Size = new System.Drawing.Size(171, 22);
            this.deleteRegularToolStripMenuItem.Text = "删除Regular";
            // 
            // outputXmlRegularStripMenuItem
            // 
            this.outputXmlRegularStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.XMLFile_828_16x;
            this.outputXmlRegularStripMenuItem.Name = "outputXmlRegularStripMenuItem";
            this.outputXmlRegularStripMenuItem.Size = new System.Drawing.Size(171, 22);
            this.outputXmlRegularStripMenuItem.Text = "导出XML";
            // 
            // propertyToolStripMenuItem5
            // 
            this.propertyToolStripMenuItem5.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem5.Name = "propertyToolStripMenuItem5";
            this.propertyToolStripMenuItem5.Size = new System.Drawing.Size(171, 22);
            this.propertyToolStripMenuItem5.Text = "节点属性";
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripButton1,
            this.saveToolStripButton,
            this.toolStripButton_save_server,
            this.toolStripButton_download,
            this.toolStripButton4,
            this.toolStripButton_design,
            this.toolStripSeparator1,
            this.toolStripLabel1,
            this.toolStripTextBox_edgeName,
            this.toolStripTextBox_nodePath,
            this.toolStripLabel2,
            this.toolStripLabel_xml_deviceCount,
            this.toolStripSeparator2,
            this.toolStripLabel_edge_deviceMax});
            this.toolStrip1.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.Flow;
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(1020, 23);
            this.toolStrip1.TabIndex = 8;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripButton1
            // 
            this.toolStripButton1.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.toolStripButton1.Image = global::HslTechnology.EdgeViewer.Properties.Resources.FileSystemEditor_5852;
            this.toolStripButton1.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton1.Name = "toolStripButton1";
            this.toolStripButton1.Size = new System.Drawing.Size(23, 20);
            this.toolStripButton1.Text = "toolStripButton1";
            this.toolStripButton1.ToolTipText = "打开本地配置文件";
            // 
            // saveToolStripButton
            // 
            this.saveToolStripButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.saveToolStripButton.Image = global::HslTechnology.EdgeViewer.Properties.Resources.save_16xLG;
            this.saveToolStripButton.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.saveToolStripButton.Name = "saveToolStripButton";
            this.saveToolStripButton.Size = new System.Drawing.Size(23, 20);
            this.saveToolStripButton.Text = "                      ";
            this.saveToolStripButton.ToolTipText = "配置文件另存为本地";
            // 
            // toolStripButton_save_server
            // 
            this.toolStripButton_save_server.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.toolStripButton_save_server.Image = global::HslTechnology.EdgeViewer.Properties.Resources.save_server_16xLG;
            this.toolStripButton_save_server.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton_save_server.Name = "toolStripButton_save_server";
            this.toolStripButton_save_server.Size = new System.Drawing.Size(23, 20);
            this.toolStripButton_save_server.Text = "toolStripButton5";
            this.toolStripButton_save_server.ToolTipText = "配置文件保存至网关备份";
            // 
            // toolStripButton_download
            // 
            this.toolStripButton_download.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.toolStripButton_download.Image = global::HslTechnology.EdgeViewer.Properties.Resources.build_Selection_16xLG;
            this.toolStripButton_download.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton_download.Name = "toolStripButton_download";
            this.toolStripButton_download.Size = new System.Drawing.Size(23, 20);
            this.toolStripButton_download.Text = "下载配置文件到设备";
            // 
            // toolStripButton4
            // 
            this.toolStripButton4.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.toolStripButton4.Image = global::HslTechnology.EdgeViewer.Properties.Resources.debug;
            this.toolStripButton4.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton4.Name = "toolStripButton4";
            this.toolStripButton4.Size = new System.Drawing.Size(23, 20);
            this.toolStripButton4.Text = "一键批量创建测试节点";
            // 
            // toolStripButton_design
            // 
            this.toolStripButton_design.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.toolStripButton_design.Image = global::HslTechnology.EdgeViewer.Properties.Resources.XmlFile;
            this.toolStripButton_design.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton_design.Name = "toolStripButton_design";
            this.toolStripButton_design.Size = new System.Drawing.Size(23, 20);
            this.toolStripButton_design.Text = "toolStripButton2";
            this.toolStripButton_design.ToolTipText = "点击可以在XML界面和设计器界面切换";
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 23);
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(116, 17);
            this.toolStripLabel1.Text = "当前选择设备路径：";
            // 
            // toolStripTextBox_edgeName
            // 
            this.toolStripTextBox_edgeName.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.toolStripTextBox_edgeName.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.toolStripTextBox_edgeName.Name = "toolStripTextBox_edgeName";
            this.toolStripTextBox_edgeName.ReadOnly = true;
            this.toolStripTextBox_edgeName.Size = new System.Drawing.Size(150, 23);
            // 
            // toolStripTextBox_nodePath
            // 
            this.toolStripTextBox_nodePath.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.toolStripTextBox_nodePath.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.toolStripTextBox_nodePath.Name = "toolStripTextBox_nodePath";
            this.toolStripTextBox_nodePath.ReadOnly = true;
            this.toolStripTextBox_nodePath.Size = new System.Drawing.Size(400, 23);
            // 
            // toolStripLabel2
            // 
            this.toolStripLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.toolStripLabel2.Name = "toolStripLabel2";
            this.toolStripLabel2.Size = new System.Drawing.Size(68, 17);
            this.toolStripLabel2.Text = "设备数量：";
            // 
            // toolStripLabel_xml_deviceCount
            // 
            this.toolStripLabel_xml_deviceCount.Name = "toolStripLabel_xml_deviceCount";
            this.toolStripLabel_xml_deviceCount.Size = new System.Drawing.Size(15, 17);
            this.toolStripLabel_xml_deviceCount.Text = "0";
            this.toolStripLabel_xml_deviceCount.ToolTipText = "当前配置文件所有的设备数量";
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(6, 23);
            // 
            // toolStripLabel_edge_deviceMax
            // 
            this.toolStripLabel_edge_deviceMax.AutoToolTip = true;
            this.toolStripLabel_edge_deviceMax.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.toolStripLabel_edge_deviceMax.Name = "toolStripLabel_edge_deviceMax";
            this.toolStripLabel_edge_deviceMax.Size = new System.Drawing.Size(33, 17);
            this.toolStripLabel_edge_deviceMax.Text = "Max";
            this.toolStripLabel_edge_deviceMax.ToolTipText = "当前网关支持的最大数量的设备";
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 23);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.splitContainer2);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.panel1);
            this.splitContainer1.Size = new System.Drawing.Size(1020, 612);
            this.splitContainer1.SplitterDistance = 320;
            this.splitContainer1.TabIndex = 9;
            // 
            // splitContainer2
            // 
            this.splitContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer2.Location = new System.Drawing.Point(0, 0);
            this.splitContainer2.Name = "splitContainer2";
            this.splitContainer2.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer2.Panel1
            // 
            this.splitContainer2.Panel1.Controls.Add(this.treeView1);
            this.splitContainer2.Panel1.Controls.Add(this.label1);
            // 
            // splitContainer2.Panel2
            // 
            this.splitContainer2.Panel2.Controls.Add(this.treeViewEx1);
            this.splitContainer2.Panel2.Controls.Add(this.label2);
            this.splitContainer2.Size = new System.Drawing.Size(320, 612);
            this.splitContainer2.SplitterDistance = 417;
            this.splitContainer2.TabIndex = 0;
            // 
            // treeView1
            // 
            this.treeView1.AllowDrop = true;
            this.treeView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.treeView1.Location = new System.Drawing.Point(4, 24);
            this.treeView1.Name = "treeView1";
            this.treeView1.Size = new System.Drawing.Size(311, 405);
            this.treeView1.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(1, 4);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(283, 17);
            this.label1.TabIndex = 1;
            this.label1.Text = "系统的树节点信息:(右键操作，支持拖拽，复制粘贴)";
            // 
            // treeViewEx1
            // 
            this.treeViewEx1.AllowDrop = true;
            this.treeViewEx1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.treeViewEx1.Location = new System.Drawing.Point(4, 24);
            this.treeViewEx1.Name = "treeViewEx1";
            treeNode3.Name = "节点0";
            treeNode3.Text = "XmlStandby";
            treeNode3.ToolTipText = "网关备用的配置信息文件";
            treeNode4.Name = "节点1";
            treeNode4.Text = "XmlHistory";
            treeNode4.ToolTipText = "网关历史修改的XML文件";
            this.treeViewEx1.Nodes.AddRange(new System.Windows.Forms.TreeNode[] {
            treeNode3,
            treeNode4});
            this.treeViewEx1.Size = new System.Drawing.Size(311, 170);
            this.treeViewEx1.TabIndex = 3;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(4, 4);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(200, 17);
            this.label2.TabIndex = 2;
            this.label2.Text = "网关备用及历史变更配置文件列表：";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.panel2);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(696, 612);
            this.panel1.TabIndex = 0;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.button_xml_modify);
            this.panel2.Controls.Add(this.label3);
            this.panel2.Controls.Add(this.textBox_design);
            this.panel2.Location = new System.Drawing.Point(24, 113);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(660, 448);
            this.panel2.TabIndex = 3;
            // 
            // button_xml_modify
            // 
            this.button_xml_modify.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.button_xml_modify.Location = new System.Drawing.Point(564, 1);
            this.button_xml_modify.Name = "button_xml_modify";
            this.button_xml_modify.Size = new System.Drawing.Size(94, 27);
            this.button_xml_modify.TabIndex = 2;
            this.button_xml_modify.Text = "保存修改";
            this.button_xml_modify.UseVisualStyleBackColor = true;
            this.button_xml_modify.Click += new System.EventHandler(this.button_xml_modify_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(3, 7);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(430, 17);
            this.label3.TabIndex = 1;
            this.label3.Text = "以下是当前所有配置信息的XML格式文档，可以手动修改点击右侧保存来生效。";
            // 
            // textBox_design
            // 
            this.textBox_design.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.textBox_design.BackColor = System.Drawing.Color.Honeydew;
            this.textBox_design.Location = new System.Drawing.Point(3, 30);
            this.textBox_design.Multiline = true;
            this.textBox_design.Name = "textBox_design";
            this.textBox_design.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox_design.Size = new System.Drawing.Size(654, 415);
            this.textBox_design.TabIndex = 0;
            // 
            // cMS_EditSourceDataRequest
            // 
            this.cMS_EditSourceDataRequest.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addRegularScalarToolStripMenuItem,
            this.addRegularStructToolStripMenuItem,
            this.editSourceRequestToolStripMenuItem,
            this.deleteSourceRequestToolStripMenuItem,
            this.propertyToolStripMenuItem6});
            this.cMS_EditSourceDataRequest.Name = "contextMenuStrip1";
            this.cMS_EditSourceDataRequest.Size = new System.Drawing.Size(173, 114);
            // 
            // addRegularScalarToolStripMenuItem
            // 
            this.addRegularScalarToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addRegularScalarToolStripMenuItem.Name = "addRegularScalarToolStripMenuItem";
            this.addRegularScalarToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.addRegularScalarToolStripMenuItem.Text = "新增单标量解析";
            // 
            // addRegularStructToolStripMenuItem
            // 
            this.addRegularStructToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addRegularStructToolStripMenuItem.Name = "addRegularStructToolStripMenuItem";
            this.addRegularStructToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.addRegularStructToolStripMenuItem.Text = "新增结构体解析";
            // 
            // editSourceRequestToolStripMenuItem
            // 
            this.editSourceRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.PencilAngled_16xLG_color;
            this.editSourceRequestToolStripMenuItem.Name = "editSourceRequestToolStripMenuItem";
            this.editSourceRequestToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.editSourceRequestToolStripMenuItem.Text = "编辑原始数据请求";
            // 
            // deleteSourceRequestToolStripMenuItem
            // 
            this.deleteSourceRequestToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_Cancel_16xLG;
            this.deleteSourceRequestToolStripMenuItem.Name = "deleteSourceRequestToolStripMenuItem";
            this.deleteSourceRequestToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.deleteSourceRequestToolStripMenuItem.Text = "删除原始数据请求";
            // 
            // propertyToolStripMenuItem6
            // 
            this.propertyToolStripMenuItem6.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem6.Name = "propertyToolStripMenuItem6";
            this.propertyToolStripMenuItem6.Size = new System.Drawing.Size(172, 22);
            this.propertyToolStripMenuItem6.Text = "节点属性";
            // 
            // cMs_Alarm_Add
            // 
            this.cMs_Alarm_Add.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addBoolAlarmToolStripMenuItem,
            this.addIntegerAlarmToolStripMenuItem,
            this.addHexAlarmToolStripMenuItem,
            this.addDataRangeAlarmToolStripMenuItem,
            this.addAlarmDatabaseToolStripMenuItem,
            this.propertyToolStripMenuItem7});
            this.cMs_Alarm_Add.Name = "cMs_Alarm_Add";
            this.cMs_Alarm_Add.Size = new System.Drawing.Size(173, 136);
            // 
            // addBoolAlarmToolStripMenuItem
            // 
            this.addBoolAlarmToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addBoolAlarmToolStripMenuItem.Name = "addBoolAlarmToolStripMenuItem";
            this.addBoolAlarmToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.addBoolAlarmToolStripMenuItem.Text = "新增Bool报警";
            // 
            // addIntegerAlarmToolStripMenuItem
            // 
            this.addIntegerAlarmToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addIntegerAlarmToolStripMenuItem.Name = "addIntegerAlarmToolStripMenuItem";
            this.addIntegerAlarmToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.addIntegerAlarmToolStripMenuItem.Text = "新增Integer报警";
            // 
            // addHexAlarmToolStripMenuItem
            // 
            this.addHexAlarmToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addHexAlarmToolStripMenuItem.Name = "addHexAlarmToolStripMenuItem";
            this.addHexAlarmToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.addHexAlarmToolStripMenuItem.Text = "新增Hex报警";
            // 
            // addDataRangeAlarmToolStripMenuItem
            // 
            this.addDataRangeAlarmToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addDataRangeAlarmToolStripMenuItem.Name = "addDataRangeAlarmToolStripMenuItem";
            this.addDataRangeAlarmToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.addDataRangeAlarmToolStripMenuItem.Text = "新增数值范围报警";
            // 
            // addAlarmDatabaseToolStripMenuItem
            // 
            this.addAlarmDatabaseToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.Database_request;
            this.addAlarmDatabaseToolStripMenuItem.Name = "addAlarmDatabaseToolStripMenuItem";
            this.addAlarmDatabaseToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.addAlarmDatabaseToolStripMenuItem.Text = "新增数据库操作";
            // 
            // propertyToolStripMenuItem7
            // 
            this.propertyToolStripMenuItem7.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem7.Name = "propertyToolStripMenuItem7";
            this.propertyToolStripMenuItem7.Size = new System.Drawing.Size(172, 22);
            this.propertyToolStripMenuItem7.Text = "节点属性";
            // 
            // cMs_Oee_Add
            // 
            this.cMs_Oee_Add.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addOEEToolStripMenuItem,
            this.propertyToolStripMenuItem8});
            this.cMs_Oee_Add.Name = "cMs_Oee_Add";
            this.cMs_Oee_Add.Size = new System.Drawing.Size(125, 48);
            // 
            // addOEEToolStripMenuItem
            // 
            this.addOEEToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addOEEToolStripMenuItem.Name = "addOEEToolStripMenuItem";
            this.addOEEToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.addOEEToolStripMenuItem.Text = "新增OEE";
            // 
            // propertyToolStripMenuItem8
            // 
            this.propertyToolStripMenuItem8.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem8.Name = "propertyToolStripMenuItem8";
            this.propertyToolStripMenuItem8.Size = new System.Drawing.Size(124, 22);
            this.propertyToolStripMenuItem8.Text = "节点属性";
            // 
            // cMs_Database_Add
            // 
            this.cMs_Database_Add.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addDatabaseToolStripMenuItem,
            this.propertyToolStripMenuItem9});
            this.cMs_Database_Add.Name = "cMs_Oee_Add";
            this.cMs_Database_Add.Size = new System.Drawing.Size(156, 48);
            // 
            // addDatabaseToolStripMenuItem
            // 
            this.addDatabaseToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.sqlServerToolStripMenuItem});
            this.addDatabaseToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_add_16xLG;
            this.addDatabaseToolStripMenuItem.Name = "addDatabaseToolStripMenuItem";
            this.addDatabaseToolStripMenuItem.Size = new System.Drawing.Size(155, 22);
            this.addDatabaseToolStripMenuItem.Text = "新增Database";
            // 
            // sqlServerToolStripMenuItem
            // 
            this.sqlServerToolStripMenuItem.Name = "sqlServerToolStripMenuItem";
            this.sqlServerToolStripMenuItem.Size = new System.Drawing.Size(131, 22);
            this.sqlServerToolStripMenuItem.Text = "SqlServer";
            // 
            // propertyToolStripMenuItem9
            // 
            this.propertyToolStripMenuItem9.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem9.Name = "propertyToolStripMenuItem9";
            this.propertyToolStripMenuItem9.Size = new System.Drawing.Size(155, 22);
            this.propertyToolStripMenuItem9.Text = "节点属性";
            // 
            // cMs_Database_Edit
            // 
            this.cMs_Database_Edit.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.editDatabaseToolStripMenuItem,
            this.deleteDatabaseToolStripMenuItem,
            this.outputXmlDatabaseToolStripMenuItem,
            this.propertyToolStripMenuItem10});
            this.cMs_Database_Edit.Name = "contextMenuStrip1";
            this.cMs_Database_Edit.Size = new System.Drawing.Size(156, 92);
            // 
            // editDatabaseToolStripMenuItem
            // 
            this.editDatabaseToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.PencilAngled_16xLG_color;
            this.editDatabaseToolStripMenuItem.Name = "editDatabaseToolStripMenuItem";
            this.editDatabaseToolStripMenuItem.Size = new System.Drawing.Size(155, 22);
            this.editDatabaseToolStripMenuItem.Text = "编辑Database";
            // 
            // deleteDatabaseToolStripMenuItem
            // 
            this.deleteDatabaseToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_Cancel_16xLG;
            this.deleteDatabaseToolStripMenuItem.Name = "deleteDatabaseToolStripMenuItem";
            this.deleteDatabaseToolStripMenuItem.Size = new System.Drawing.Size(155, 22);
            this.deleteDatabaseToolStripMenuItem.Text = "删除Database";
            // 
            // outputXmlDatabaseToolStripMenuItem
            // 
            this.outputXmlDatabaseToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.XMLFile_828_16x;
            this.outputXmlDatabaseToolStripMenuItem.Name = "outputXmlDatabaseToolStripMenuItem";
            this.outputXmlDatabaseToolStripMenuItem.Size = new System.Drawing.Size(155, 22);
            this.outputXmlDatabaseToolStripMenuItem.Text = "导出XML";
            // 
            // propertyToolStripMenuItem10
            // 
            this.propertyToolStripMenuItem10.Image = global::HslTechnology.EdgeViewer.Properties.Resources.properties_16xLG;
            this.propertyToolStripMenuItem10.Name = "propertyToolStripMenuItem10";
            this.propertyToolStripMenuItem10.Size = new System.Drawing.Size(155, 22);
            this.propertyToolStripMenuItem10.Text = "节点属性";
            // 
            // cMs_EditXml
            // 
            this.cMs_EditXml.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.xmlLoadToolStripMenuItem,
            this.xmlDeleteToolStripMenuItem});
            this.cMs_EditXml.Name = "contextMenuStrip3";
            this.cMs_EditXml.Size = new System.Drawing.Size(149, 48);
            // 
            // xmlLoadToolStripMenuItem
            // 
            this.xmlLoadToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.PencilAngled_16xLG_color;
            this.xmlLoadToolStripMenuItem.Name = "xmlLoadToolStripMenuItem";
            this.xmlLoadToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.xmlLoadToolStripMenuItem.Text = "编辑配置文件";
            // 
            // xmlDeleteToolStripMenuItem
            // 
            this.xmlDeleteToolStripMenuItem.Image = global::HslTechnology.EdgeViewer.Properties.Resources.action_Cancel_16xLG;
            this.xmlDeleteToolStripMenuItem.Name = "xmlDeleteToolStripMenuItem";
            this.xmlDeleteToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.xmlDeleteToolStripMenuItem.Text = "删除配置文件";
            // 
            // FormNodeSettings
            // 
            this.AllowDrop = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Honeydew;
            this.ClientSize = new System.Drawing.Size(1020, 635);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.toolStrip1);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.Name = "FormNodeSettings";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "采集信息";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormNodeSetting_FormClosing);
            this.Load += new System.EventHandler(this.FormNodeSettings_Load);
            this.cMS_Device.ResumeLayout(false);
            this.cMS_Request.ResumeLayout(false);
            this.cMs_EditScalarRequest.ResumeLayout(false);
            this.cMS_Regular_Add.ResumeLayout(false);
            this.cMS_EditRegular.ResumeLayout(false);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.splitContainer2.Panel1.ResumeLayout(false);
            this.splitContainer2.Panel1.PerformLayout();
            this.splitContainer2.Panel2.ResumeLayout(false);
            this.splitContainer2.Panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).EndInit();
            this.splitContainer2.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.cMS_EditSourceDataRequest.ResumeLayout(false);
            this.cMs_Alarm_Add.ResumeLayout(false);
            this.cMs_Oee_Add.ResumeLayout(false);
            this.cMs_Database_Add.ResumeLayout(false);
            this.cMs_Database_Edit.ResumeLayout(false);
            this.cMs_EditXml.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.ContextMenuStrip cMS_Device;
        private System.Windows.Forms.ToolStripMenuItem addPlcToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem editDeviceNodeToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem deleteDeviceNodeToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip cMS_Request;
        private System.Windows.Forms.ToolStripMenuItem addRequestToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem editRequestToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem deleteRequestToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip cMs_EditScalarRequest;
        private DeviceToolStripMenuItem editRequestNodeToolStripMenuItem;
        private DeviceToolStripMenuItem deleteRequestNodeToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip cMS_Regular_Add;
        private DeviceToolStripMenuItem addRegularToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip cMS_EditRegular;
        private DeviceToolStripMenuItem addRegularItemToolStripMenuItem;
        private DeviceToolStripMenuItem editRegularToolStripMenuItem;
        private DeviceToolStripMenuItem deleteRegularToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addRobotToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addCncToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem copyNodeToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addGroupToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem moveDeviceNodeUpToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem moveDeviceNodeDownToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem moveRequestUpToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem moveRequestDownToolStripMenuItem;
		private System.Windows.Forms.ToolStrip toolStrip1;
		private System.Windows.Forms.ToolStripButton toolStripButton1;
		private System.Windows.Forms.ToolStripButton saveToolStripButton;
		private System.Windows.Forms.ToolStripButton toolStripButton_download;
		private System.Windows.Forms.ToolStripButton toolStripButton4;
		private System.Windows.Forms.SplitContainer splitContainer1;
		private TreeViewEx treeView1;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.ToolStripMenuItem addScalarReadRequestToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addSourceReadRequestToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip cMS_EditSourceDataRequest;
        private DeviceToolStripMenuItem addRegularScalarToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addRegularStructToolStripMenuItem;
        private DeviceToolStripMenuItem editSourceRequestToolStripMenuItem;
        private DeviceToolStripMenuItem deleteSourceRequestToolStripMenuItem;
		private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
		private System.Windows.Forms.ToolStripLabel toolStripLabel1;
		private System.Windows.Forms.ToolStripTextBox toolStripTextBox_nodePath;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem2;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem3;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem1;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem4;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem5;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem6;
		private System.Windows.Forms.ContextMenuStrip cMs_Alarm_Add;
		private System.Windows.Forms.ToolStripMenuItem addBoolAlarmToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addIntegerAlarmToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addHexAlarmToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addDataRangeAlarmToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem7;
		private System.Windows.Forms.ContextMenuStrip cMs_Oee_Add;
		private System.Windows.Forms.ToolStripMenuItem addOEEToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem8;
		private System.Windows.Forms.ToolStripTextBox toolStripTextBox_edgeName;
		private System.Windows.Forms.ToolStripMenuItem addMethodCallRequestToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem editMethodToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addPluginsDeviceToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addSpecialToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addScalarCacheRequestToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addPipeToolStripMenuItem1;
		private System.Windows.Forms.ToolStripMenuItem addSerialPipeToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addSocketPipeToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addTemplatetoolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem outputXmlToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem outputXmlRegularStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addLocalStructToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addSingleThreadToolStripMenuItem;
		private System.Windows.Forms.ContextMenuStrip cMs_Database_Add;
		private System.Windows.Forms.ToolStripMenuItem addDatabaseToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem9;
		private System.Windows.Forms.ToolStripMenuItem sqlServerToolStripMenuItem;
		private System.Windows.Forms.ContextMenuStrip cMs_Database_Edit;
		private DeviceToolStripMenuItem editDatabaseToolStripMenuItem;
		private DeviceToolStripMenuItem deleteDatabaseToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem outputXmlDatabaseToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem propertyToolStripMenuItem10;
		private System.Windows.Forms.ToolStripMenuItem addDatabaseRequestToolStripMenuItem;
		private System.Windows.Forms.SplitContainer splitContainer2;
		private TreeViewEx treeViewEx1;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.ToolStripButton toolStripButton_save_server;
		private System.Windows.Forms.ContextMenuStrip cMs_EditXml;
		private DeviceToolStripMenuItem xmlLoadToolStripMenuItem;
		private DeviceToolStripMenuItem xmlDeleteToolStripMenuItem;
		private System.Windows.Forms.ToolStripLabel toolStripLabel2;
		private System.Windows.Forms.ToolStripLabel toolStripLabel_xml_deviceCount;
		private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
		private System.Windows.Forms.ToolStripLabel toolStripLabel_edge_deviceMax;
		private System.Windows.Forms.ToolStripMenuItem addWriteRequestToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem outputJsonToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addAlarmDatabaseToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addressMappingToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem copyDevicePathToolStripMenuItem;
		private System.Windows.Forms.ToolStripMenuItem addRequestGroupToolStripMenuItem;
		private System.Windows.Forms.ToolStripButton toolStripButton_design;
		private System.Windows.Forms.Panel panel2;
		private System.Windows.Forms.TextBox textBox_design;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Button button_xml_modify;
	}
}