
using HslTechnology.Edge.Controls;

namespace HslTechnology.EdgeViewer.Pages
{
	partial class FormPluginsSelect
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
			this.label1 = new System.Windows.Forms.Label();
			this.treeView1 = new System.Windows.Forms.TreeView();
			this.hslPanelText2 = new HslControls.HslPanelText();
			this.pictureBox2 = new System.Windows.Forms.PictureBox();
			this.textBox9 = new System.Windows.Forms.TextBox();
			this.label11 = new System.Windows.Forms.Label();
			this.textBox8 = new System.Windows.Forms.TextBox();
			this.label10 = new System.Windows.Forms.Label();
			this.button3 = new System.Windows.Forms.Button();
			this.button2 = new System.Windows.Forms.Button();
			this.edgePluginsControl1 = new HslTechnology.Edge.Controls.Machine.EdgePluginsControl();
			this.buttonOk1 = new ButtonOk();
			this.buttonCancel1 = new ButtonCancel();
			this.hslPanelText2.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
			this.SuspendLayout();
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(12, 9);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(95, 17);
			this.label1.TabIndex = 0;
			this.label1.Text = "已安装插件列表:";
			// 
			// treeView1
			// 
			this.treeView1.Location = new System.Drawing.Point(12, 29);
			this.treeView1.Name = "treeView1";
			this.treeView1.Size = new System.Drawing.Size(273, 501);
			this.treeView1.TabIndex = 1;
			// 
			// hslPanelText2
			// 
			this.hslPanelText2.Controls.Add(this.pictureBox2);
			this.hslPanelText2.Controls.Add(this.textBox9);
			this.hslPanelText2.Controls.Add(this.label11);
			this.hslPanelText2.Controls.Add(this.textBox8);
			this.hslPanelText2.Controls.Add(this.label10);
			this.hslPanelText2.Location = new System.Drawing.Point(291, 313);
			this.hslPanelText2.Name = "hslPanelText2";
			this.hslPanelText2.Size = new System.Drawing.Size(543, 166);
			this.hslPanelText2.TabIndex = 3;
			this.hslPanelText2.Text = "设备信息";
			this.hslPanelText2.TextOffect = 20;
			this.hslPanelText2.ThemeColor = System.Drawing.Color.Gray;
			// 
			// pictureBox2
			// 
			this.pictureBox2.BackColor = System.Drawing.Color.White;
			this.pictureBox2.Location = new System.Drawing.Point(405, 26);
			this.pictureBox2.Name = "pictureBox2";
			this.pictureBox2.Size = new System.Drawing.Size(128, 128);
			this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox2.TabIndex = 6;
			this.pictureBox2.TabStop = false;
			// 
			// textBox9
			// 
			this.textBox9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox9.Location = new System.Drawing.Point(85, 59);
			this.textBox9.Multiline = true;
			this.textBox9.Name = "textBox9";
			this.textBox9.ReadOnly = true;
			this.textBox9.Size = new System.Drawing.Size(313, 95);
			this.textBox9.TabIndex = 5;
			// 
			// label11
			// 
			this.label11.AutoSize = true;
			this.label11.Location = new System.Drawing.Point(14, 62);
			this.label11.Name = "label11";
			this.label11.Size = new System.Drawing.Size(68, 17);
			this.label11.TabIndex = 4;
			this.label11.Text = "设备描述：";
			// 
			// textBox8
			// 
			this.textBox8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox8.Location = new System.Drawing.Point(85, 26);
			this.textBox8.Name = "textBox8";
			this.textBox8.ReadOnly = true;
			this.textBox8.Size = new System.Drawing.Size(313, 23);
			this.textBox8.TabIndex = 3;
			// 
			// label10
			// 
			this.label10.AutoSize = true;
			this.label10.Location = new System.Drawing.Point(14, 29);
			this.label10.Name = "label10";
			this.label10.Size = new System.Drawing.Size(68, 17);
			this.label10.TabIndex = 2;
			this.label10.Text = "设备名称：";
			// 
			// button3
			// 
			this.button3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
			this.button3.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button3.Location = new System.Drawing.Point(-241, 489);
			this.button3.Name = "button3";
			this.button3.Size = new System.Drawing.Size(116, 41);
			this.button3.TabIndex = 15;
			this.button3.Text = "关闭";
			this.button3.UseVisualStyleBackColor = true;
			// 
			// button2
			// 
			this.button2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
			this.button2.Location = new System.Drawing.Point(-226, 488);
			this.button2.Name = "button2";
			this.button2.Size = new System.Drawing.Size(109, 42);
			this.button2.TabIndex = 14;
			this.button2.Text = "确认添加";
			this.button2.UseVisualStyleBackColor = true;
			this.button2.Click += new System.EventHandler(this.button2_Click);
			// 
			// edgePluginsControl1
			// 
			this.edgePluginsControl1.BackColor = System.Drawing.Color.Transparent;
			this.edgePluginsControl1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.edgePluginsControl1.Location = new System.Drawing.Point(291, 19);
			this.edgePluginsControl1.MaximumSize = new System.Drawing.Size(543, 288);
			this.edgePluginsControl1.MinimumSize = new System.Drawing.Size(543, 288);
			this.edgePluginsControl1.Name = "edgePluginsControl1";
			this.edgePluginsControl1.Size = new System.Drawing.Size(543, 288);
			this.edgePluginsControl1.TabIndex = 16;
			// 
			// buttonOk1
			// 
			this.buttonOk1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonOk1.Location = new System.Drawing.Point(432, 491);
			this.buttonOk1.Name = "buttonOk1";
			this.buttonOk1.Size = new System.Drawing.Size(121, 36);
			this.buttonOk1.TabIndex = 17;
			this.buttonOk1.Text = "buttonOk1";
			this.buttonOk1.Click += new System.EventHandler(this.buttonOk1_Click);
			// 
			// buttonCancel1
			// 
			this.buttonCancel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.buttonCancel1.Location = new System.Drawing.Point(570, 491);
			this.buttonCancel1.Name = "buttonCancel1";
			this.buttonCancel1.Size = new System.Drawing.Size(121, 36);
			this.buttonCancel1.TabIndex = 18;
			this.buttonCancel1.Text = "buttonCancel1";
			this.buttonCancel1.Click += new System.EventHandler(this.buttonCancel1_Click);
			// 
			// FormPluginsSelect
			// 
			this.AcceptButton = this.button2;
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CancelButton = this.button3;
			this.ClientSize = new System.Drawing.Size(846, 542);
			this.Controls.Add(this.buttonCancel1);
			this.Controls.Add(this.buttonOk1);
			this.Controls.Add(this.edgePluginsControl1);
			this.Controls.Add(this.button3);
			this.Controls.Add(this.button2);
			this.Controls.Add(this.hslPanelText2);
			this.Controls.Add(this.treeView1);
			this.Controls.Add(this.label1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.MaximizeBox = false;
			this.MaximumSize = new System.Drawing.Size(862, 581);
			this.MinimizeBox = false;
			this.MinimumSize = new System.Drawing.Size(862, 581);
			this.Name = "FormPluginsSelect";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "插件设备选择";
			this.Load += new System.EventHandler(this.FormPluginsSelect_Load);
			this.hslPanelText2.ResumeLayout(false);
			this.hslPanelText2.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TreeView treeView1;
		private HslControls.HslPanelText hslPanelText2;
		private System.Windows.Forms.PictureBox pictureBox2;
		private System.Windows.Forms.TextBox textBox9;
		private System.Windows.Forms.Label label11;
		private System.Windows.Forms.TextBox textBox8;
		private System.Windows.Forms.Label label10;
		private System.Windows.Forms.Button button3;
		private System.Windows.Forms.Button button2;
		private Edge.Controls.Machine.EdgePluginsControl edgePluginsControl1;
		private ButtonOk buttonOk1;
		private ButtonCancel buttonCancel1;
	}
}