using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.EdgeViewer.Pages;
using HslCommunication.Enthernet;
using Newtonsoft.Json.Linq;
using System.Xml.Linq;
using System.Threading;
using System.Reflection;
using System.IO;
using HslCommunication.MQTT;
using HslCommunication.LogNet;
using HslCommunication;
using HslTechnology.EdgeViewer.Viewer;
using HslTechnology.Edge.Node;
using HslTechnology.EdgeViewer.Core;
using WeifenLuo.WinFormsUI.ThemeVS2015;

namespace HslTechnology.EdgeViewer
{
	public partial class FormMain : Form
	{
		#region Constructor
		
		public FormMain( )
		{
			InitializeComponent( );
			this.Icon = Util.GetWinformIcon( );
		}

		#endregion

		#region Form Load Close

		private void FormMain_Load( object sender, EventArgs e )
		{
			if (eventLoaded == false)
			{
				eventLoaded = true;

				// 客户端的事务日志
				logNet = new LogNetSingle( "" );
				logNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;

				// 菜单事件绑定
				captureFileOpenToolStripMenuItem.Click       += CaptureFileOpenToolStripMenuItem_Click;
				captureFileNewToolStripMenuItem.Click        += CaptureFileNewToolStripMenuItem_Click;
				edgeServerToolStripMenuItem.Click            += EdgeServerToolStripMenuItem_Click;
				aboutToolStripMenuItem.Click                 += aboutToolStripMenuItem_Click;
				startMqttServerToolStripMenuItem.Click       += startMqttServerToolStripMenuItem_Click;
				FormClosing                                  += FormMain_FormClosing;
				toolStripButton_stopAndContinue.Click        += ToolStripButton_stopAndContinue_Click;
				toolStripComboBox1.SelectedIndexChanged      += ToolStripComboBox1_SelectedIndexChanged;
				toolStripButton_restartEdge.Click            += ToolStripButton_restartEdge_Click;
				// 主题菜单
				blueThemeToolStripMenuItem.Click             += BlueThemeToolStripMenuItem_Click;
				lightThemeToolStripMenuItem.Click            += LightThemeToolStripMenuItem_Click;
				deepThemeToolStripMenuItem.Click             += DeepThemeToolStripMenuItem_Click;
				createGUIDToolStripMenuItem.Click            += CreateGUIDToolStripMenuItem_Click;

				// 主Dock界面设置
				dockPanel1.Dock = DockStyle.Fill;
				switch (Util.ViewerSettings.Theme)
				{
					case "Light": dockPanel1.Theme = vS2015LightTheme1; lightThemeToolStripMenuItem.Checked = true; break;
					case "Dark":  dockPanel1.Theme = vS2015DarkTheme1;  deepThemeToolStripMenuItem.Checked  = true; break;
					case "Blue":  dockPanel1.Theme = vS2015BlueTheme1;  blueThemeToolStripMenuItem.Checked  = true; break;
					default: dockPanel1.Theme = vS2015BlueTheme1; break;
				}
				Util.Theme = dockPanel1.Theme as VS2015ThemeBase;
				HslTechnology.Edge.Controls.HslTechnologyControlHelper.ThemeKey = Util.ViewerSettings.Theme;
				HslTechnology.Edge.Controls.HslTechnologyControlHelper.Theme = dockPanel1.Theme;
				if (Util.ViewerSettings.Theme == "Dark")
				{
					this.menuStrip1.BackColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Background;
					this.menuStrip1.ForeColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Text;
					this.aboutToolStripMenuItem.BackColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Background;
					this.aboutToolStripMenuItem.ForeColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Text;
					this.toolStrip1.BackColor = Util.Theme.ColorPalette.ToolWindowTabUnselected.Background;
					this.toolStrip1.ForeColor = Util.Theme.ColorPalette.ToolWindowTabUnselected.Text;
					this.updateToolStripMenuItem.BackColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Background;
					this.updateToolStripMenuItem.ForeColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Text;
					this.startMqttServerToolStripMenuItem.BackColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Background;
					this.startMqttServerToolStripMenuItem.ForeColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Text;

					//this.fileToolStripMenuItem.DropDown.BackColor = Util.Theme.ColorPalette.CommandBarMenuPopupHovered.ItemBackground;
					//this.fileToolStripMenuItem.DropDown.ForeColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Text;
					//this.fileToolStripMenuItem.DropDown.
				}
				//this.menuStrip1.BackColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Background;
				//this.menuStrip1.ForeColor = Util.Theme.ColorPalette.CommandBarMenuDefault.Text;
				//this.fileToolStripMenuItem.BackColor = Util.Theme.ColorPalette.CommandBarMenuPopupHovered.ItemBackground;
				//this.fileToolStripMenuItem.ForeColor = Util.Theme.ColorPalette.CommandBarMenuPopupHovered.Text;
				//this.toolStrip1.BackColor = Util.Theme.ColorPalette.ToolWindowTabUnselected.Background;
				//this.toolStrip1.ForeColor = Util.Theme.ColorPalette.ToolWindowTabUnselected.Text;


				// 单实例的设备监控界面
				deviceMonitor = new Forms.FormDeviceMonitor( this.logNet, this );
				deviceMonitor.Show( dockPanel1 );
				deviceMonitor.UpdateEdgeStatus = UpdateEdgeStatus;

				// 单实例的客户端事务日志
				clientLogView = new Forms.FormLogView( this.logNet );
				clientLogView.Show( dockPanel1, WeifenLuo.WinFormsUI.Docking.DockState.DockBottomAutoHide );

				// 单实例的左侧服务器
				edgeServers                   = new Forms.FormEdgeServers( this.logNet );
				edgeServers.DockPanelMain     = dockPanel1;
				edgeServers.FormDeviceMonitor = deviceMonitor;
				edgeServers.FormMain          = this;
				edgeServers.Show( dockPanel1, WeifenLuo.WinFormsUI.Docking.DockState.DockLeft );

				// 底部的状态栏信息
				this.deviceToolStripInfo             = Util.CreatIconImage( "StatusInfo", this.statusStrip1.BackColor );
				this.deviceToolStripOk               = Util.CreatIconImage( "StatusOk", this.statusStrip1.BackColor );
				this.deviceToolStripError            = Util.CreatIconImage( "StatusError", this.statusStrip1.BackColor );
				this.statusStrip1.LayoutStyle        = ToolStripLayoutStyle.HorizontalStackWithOverflow;
				this.toolStripStatusLabel1.Image     = Util.CreatIconImage( "Message", this.statusStrip1.BackColor );
				this.toolStripStatusLabel3.Alignment = ToolStripItemAlignment.Right;
				this.toolStripStatusLabel3.Image     = Util.CreatIconImage( "Clock", this.statusStrip1.BackColor );
				this.toolStripStatusLabel4.Image     = this.deviceToolStripInfo;

				SecondMethod( );

				// 启动一个主线程
				threadMain = new Thread( new ThreadStart( ThreadBackground ) );
				threadMain.IsBackground = true;
				threadMain.Start( );

				System.Threading.ThreadPool.QueueUserWorkItem( new System.Threading.WaitCallback( ThreadPoolCheckVersion ), null );
			}
		}

		private void CreateGUIDToolStripMenuItem_Click( object sender, EventArgs e )
		{
			Forms.FormGuidCreate form = new Forms.FormGuidCreate( );
			form.ShowDialog( );
		}

		private void ToolStripComboBox1_SelectedIndexChanged( object sender, EventArgs e )
		{
			if (toolStripComboBox1.SelectedItem is EdgeServerSettings select)
			{
				toolStripButton_stopAndContinue.Image = select.EdgeAllDeivcePause ?
					Properties.Resources.Play :
					Properties.Resources.Pause;
			}
		}

		private void ThreadPoolCheckVersion( object obj )
		{
			System.Threading.Thread.Sleep( 100 );
			HslCommunication.Enthernet.NetSimplifyClient simplifyClient = new HslCommunication.Enthernet.NetSimplifyClient( "*************", 18467 );
			HslCommunication.OperateResult<HslCommunication.NetHandle, string> read = simplifyClient.ReadCustomerFromServer( 700, System.Reflection.Assembly.GetExecutingAssembly( ).GetName( ).Version.ToString( ) );
		}


		private void FormMain_FormClosing( object sender, FormClosingEventArgs e )
		{
			if (MessageBox.Show( "是否确认退出系统？", "退出确认", MessageBoxButtons.YesNo, MessageBoxIcon.Warning ) == DialogResult.No)
			{
				e.Cancel = true;
				return;
			}

			// 关闭线程
			this.threadMain.Abort( );
		}

		#endregion

		#region Menu Click

		private void DeepThemeToolStripMenuItem_Click( object sender, EventArgs e )
		{
			Util.ViewerSettings.Theme = "Dark";
			Util.ViewerSettings.SaveFile( );
			MessageBox.Show( "重启生效！" );
		}

		private void LightThemeToolStripMenuItem_Click( object sender, EventArgs e )
		{
			Util.ViewerSettings.Theme = "Light";
			Util.ViewerSettings.SaveFile( );
			MessageBox.Show( "重启生效！" );
		}

		private void BlueThemeToolStripMenuItem_Click( object sender, EventArgs e )
		{
			Util.ViewerSettings.Theme = "Blue";
			Util.ViewerSettings.SaveFile( );
			MessageBox.Show( "重启生效！" );
		}

		private void EdgeServerToolStripMenuItem_Click( object sender, EventArgs e )
		{
			if(edgeServers.IsHidden || edgeServers.HideOnClose)
				edgeServers.Show( dockPanel1, WeifenLuo.WinFormsUI.Docking.DockState.DockLeft );
		}

		private void CaptureFileNewToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 新建一个XML设备配置的文档
			FormNodeSettings form = new FormNodeSettings( this.logNet, null, GroupNode.CreateDefaultXmlSettings( ) );
			form.Show( dockPanel1, WeifenLuo.WinFormsUI.Docking.DockState.Document );
		}

		private void CaptureFileOpenToolStripMenuItem_Click( object sender, EventArgs e )
		{
			// 打开一个XML设备配置的文档
			using (OpenFileDialog ofd = new OpenFileDialog( ))
			{
				ofd.Filter = "(*.xml)|*.xml";
				if(ofd.ShowDialog() == DialogResult.OK)
				{
					XElement element = null;
					try
					{
						element = XElement.Load( ofd.FileName );
					}
					catch(Exception ex)
					{
						MessageBox.Show( "当前的文件不是XML格式的文件！加载失败：" + ex.Message );
						return;
					}

					FormNodeSettings form = new FormNodeSettings( this.logNet, null, element );
					form.Show( dockPanel1, WeifenLuo.WinFormsUI.Docking.DockState.Document );
				}
			}
		}

		private void startMqttServerToolStripMenuItem_Click( object sender, EventArgs e )
		{
			try
			{
				System.Diagnostics.Process.Start( "MqttServerSample.exe" );
			}
			catch (Exception ex)
			{
				MessageBox.Show( ex.Message );
			}
		}

		private void aboutToolStripMenuItem_Click( object sender, EventArgs e )
		{
			using(FormAbout about = new FormAbout( ))
			{
				about.Icon = this.Icon;
				about.ShowDialog( );
			}
		}

		private void toolStripMenuItem1_Click( object sender, EventArgs e )
		{
			using (FormAccountCheck accountCheck = new FormAccountCheck( ))
				if (accountCheck.ShowDialog( ) == DialogResult.OK)
				{
					using (FormUpdateCloud update = new FormUpdateCloud( ))
					{
						update.ShowDialog( );
					}
				}
		}

		private async void ToolStripButton_stopAndContinue_Click( object sender, EventArgs e )
		{
			if (toolStripComboBox1.SelectedItem is EdgeServerSettings serverSettings)
			{
				if(serverSettings.EdgeAllDeivcePause)
				{
					MqttSyncClient rpc = serverSettings.GetMqttSyncClient( );
					OperateResult<string> read = await rpc.ReadRpcAsync<string>( "Edge/DeviceContinueRequest", null );
					if (!read.IsSuccess)
					{
						MessageBox.Show( "向服务器请求数据失败！原因：" + read.Message );
						return;
					}
					else
					{
						toolStripButton_stopAndContinue.Image = Properties.Resources.Pause;
						serverSettings.EdgeAllDeivcePause = false;
						MessageBox.Show( "通知所有采集继续成功！" );
					}
				}
				else
				{
					MqttSyncClient rpc = serverSettings.GetMqttSyncClient( );
					OperateResult<string> read = await rpc.ReadRpcAsync<string>( "Edge/DeviceStopRequest", null );
					if (!read.IsSuccess)
					{
						MessageBox.Show( "向服务器请求数据失败！原因：" + read.Message );
						return;
					}
					else
					{
						toolStripButton_stopAndContinue.Image = Properties.Resources.Play;
						serverSettings.EdgeAllDeivcePause = true;
						MessageBox.Show( "通知所有采集暂停成功！" );
					}
				}
			}
		}

		private void ToolStripButton_restartEdge_Click( object sender, EventArgs e )
		{
			// 重启边缘网关
			if (toolStripComboBox1.SelectedItem is EdgeServerSettings serverSettings)
			{
				EdgeServerHelper.RestartEdgeServer( serverSettings );
			}
		}

		#endregion

		private void UpdateEdgeStatus( string edge, bool online, string message )
		{
			try
			{
				if (Handle != IntPtr.Zero && IsHandleCreated) Invoke( new Action( ( ) =>
				   {
					   toolStripStatusLabel4.Text = " 设备:" + edge;
					   if (online)
					   {
						   deviceToolStatus = 1;
						   toolStripStatusLabel4.ToolTipText = "设备连接成功！";
					   }
					   else
					   {
						   deviceToolStatus = 2;
						   toolStripStatusLabel4.ToolTipText = message;
					   }
				   } ) );
			}
			catch
			{

			}
		}

		private void LogNet_BeforeSaveToFile( object sender, HslEventArgs e )
		{
			Invoke( new Action( ( ) =>
			 {
				 toolStripStatusLabel2.Text = $"[{e.HslMessage.Time:HH:mm:ss}] {e.HslMessage.Text}";
			 } ) );
		}

		#region Thread Timer 使用线程来定时执行的一些功能方法

		private void ThreadBackground( )
		{
			int seconds = DateTime.Now.Second;
			int halfSecond = DateTime.Now.Millisecond / 500;
			Action secondsAction = new Action( SecondMethod );
			Action halfSecondsAction = new Action( HalfSecondMethod );
			while (true)
			{
				Thread.Sleep( 20 );
				DateTime now = DateTime.Now;

				if (seconds != now.Second) { Invoke( secondsAction ); seconds = now.Second; } // 每秒执行
				if (halfSecond != now.Millisecond / 500) { Invoke( halfSecondsAction ); halfSecond = now.Millisecond / 500; } // 每500ms执行
			}
		}

		private void HalfSecondMethod( )
		{
			if (deviceToolStatus == 0)
			{
				toolStripStatusLabel4.Image = this.deviceToolStripInfo;
			}
			else if (deviceToolStatus == 1)
			{
				if (toolStripStatusLabel4.Image == this.deviceToolStripOk)
					toolStripStatusLabel4.Image = this.deviceToolStripInfo;
				else
					toolStripStatusLabel4.Image = this.deviceToolStripOk;
			}
			else if (deviceToolStatus == 2)
			{
				if (toolStripStatusLabel4.Image == this.deviceToolStripError)
					toolStripStatusLabel4.Image = this.deviceToolStripInfo;
				else
					toolStripStatusLabel4.Image = this.deviceToolStripError;
			}
		}

		private void SecondMethod( )
		{
			toolStripStatusLabel3.Text = DateTime.Now.ToString( "yyyy-MM-dd HH:mm:ss" );
			if (deviceMonitor != null) deviceMonitor.EveryBoolTick( DateTime.Now.Second % 2 == 0 );
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 将指定的窗体在本主窗体界面显示出来
		/// </summary>
		/// <param name="form">窗体信息</param>
		public void ShowDockPanel( WeifenLuo.WinFormsUI.Docking.DockContent form )
		{
			form.Show( dockPanel1, WeifenLuo.WinFormsUI.Docking.DockState.Document );
		}

		public void ShowEdgeListAndSelected( List<EdgeServerSettings> serverSettings, EdgeServerSettings select )
		{
			toolStripComboBox1.ComboBox.DataSource = serverSettings;
			if (select != null)
			{
				toolStripComboBox1.ComboBox.SelectedItem = select;
				toolStripButton_stopAndContinue.Image = select.EdgeAllDeivcePause ? 
					Properties.Resources.Play : 
					Properties.Resources.Pause;
			}
		}

		#endregion

		#region Private Member

		private ILogNet logNet;                                      // 客户端自身的事务日志记录
		private Forms.FormDeviceMonitor deviceMonitor;               // 监视的设备界面，单实例的模式
		private Forms.FormEdgeServers edgeServers;                   // 服务器列表的界面
		private Forms.FormLogView clientLogView;                     // 客户端的日志查看器
		private Thread threadMain;                                   // 后台运行监测状态的

		private Bitmap deviceToolStripInfo;                          // 普通消息的小图标
		private Bitmap deviceToolStripOk;                            // OK消息的小图标
		private Bitmap deviceToolStripError;                         // 错误消息的小图标
		private int deviceToolStatus = 0;                            // 实时的设备状态信息，用于闪烁功能显示
		private bool eventLoaded = false;

		#endregion

	}
}
