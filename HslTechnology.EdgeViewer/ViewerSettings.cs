using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace HslTechnology.EdgeViewer
{
	/// <summary>
	/// 客户端的配置类信息
	/// </summary>
	public class ViewerSettings
	{
		#region Constructor

		/// <summary>
		/// 指定文件的存储路径来实例化一个对象
		/// </summary>
		/// <param name="path">路径信息</param>
		public ViewerSettings( string path )
		{
			this.filePath = path;
			LoadFile( );
		}


		#endregion


		/// <summary>
		/// 获取或设置当前的主题信息
		/// </summary>
		public string Theme { get; set; } = "Blue";


		public void LoadFile( )
		{
			if (!string.IsNullOrEmpty( filePath ))
			{
				try
				{
					JObject json = JObject.Parse( System.IO.File.ReadAllText( filePath, Encoding.UTF8 ) );
					Theme = HslCommunication.BasicFramework.SoftBasic.GetValueFromJsonObject( json, nameof( Theme ), Theme );
				}
				catch ( Exception)
				{

				}
			}
		}

		public void SaveFile( )
		{
			if (!string.IsNullOrEmpty( filePath ))
			{
				try
				{
					JObject json = new JObject( );
					json.Add(nameof( Theme ), Theme );
					System.IO.File.WriteAllText( filePath, json.ToString( ) );
				}
				catch (Exception)
				{

				}
			}
		}



		private string filePath = string.Empty;
	}
}
