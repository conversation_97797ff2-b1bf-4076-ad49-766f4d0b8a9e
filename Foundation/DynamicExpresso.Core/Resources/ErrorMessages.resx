<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AmbiguousIndexerInvocation" xml:space="preserve">
    <value>Ambiguous invocation of indexer in type '{0}'</value>
  </data>
  <data name="AmbiguousMethodInvocation" xml:space="preserve">
    <value>Ambiguous invocation of method '{0}' in type '{1}'</value>
  </data>
  <data name="ArgsIncompatibleWithDelegate" xml:space="preserve">
    <value>Argument list incompatible with delegate expression</value>
  </data>
  <data name="ArgsIncompatibleWithLambda" xml:space="preserve">
    <value>Argument list incompatible with lambda expression</value>
  </data>
  <data name="BothTypesConvertToOther" xml:space="preserve">
    <value>Both of the types '{0}' and '{1}' convert to the other</value>
  </data>
  <data name="CannotConvertValue" xml:space="preserve">
    <value>A value of type '{0}' cannot be converted to type '{1}'</value>
  </data>
  <data name="CloseBracketOrCommaExpected" xml:space="preserve">
    <value>']' or ',' expected</value>
  </data>
  <data name="CloseParenOrCommaExpected" xml:space="preserve">
    <value>')' or ',' expected</value>
  </data>
  <data name="CloseParenOrOperatorExpected" xml:space="preserve">
    <value>')' or operator expected</value>
  </data>
  <data name="ColonExpected" xml:space="preserve">
    <value>':' expected</value>
  </data>
  <data name="DigitExpected" xml:space="preserve">
    <value>Digit expected</value>
  </data>
  <data name="DotOrOpenParenExpected" xml:space="preserve">
    <value>'.' or '(' expected</value>
  </data>
  <data name="ExpressionExpected" xml:space="preserve">
    <value>Expression expected</value>
  </data>
  <data name="ExpressionMustBeWritable" xml:space="preserve">
    <value>Expression must be writable</value>
  </data>
  <data name="FirstExprMustBeBool" xml:space="preserve">
    <value>The first expression must be of type 'Boolean'</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>{0} (at index {1}).</value>
  </data>
  <data name="IdentifierExpected" xml:space="preserve">
    <value>Identifier expected</value>
  </data>
  <data name="IncompatibleOperand" xml:space="preserve">
    <value>Operator '{0}' incompatible with operand type '{1}'</value>
  </data>
  <data name="IncompatibleOperands" xml:space="preserve">
    <value>Operator '{0}' incompatible with operand types '{1}' and '{2}'</value>
  </data>
  <data name="IncorrectNumberOfIndexes" xml:space="preserve">
    <value>Incorrect number of indexes</value>
  </data>
  <data name="InvalidCharacter" xml:space="preserve">
    <value>Syntax error '{0}'</value>
  </data>
  <data name="InvalidCharacterLiteral" xml:space="preserve">
    <value>Character literal must contain exactly one character</value>
  </data>
  <data name="InvalidEscapeSequence" xml:space="preserve">
    <value>Invalid character escape sequence</value>
  </data>
  <data name="InvalidIndex" xml:space="preserve">
    <value>Array index must be an integer expression</value>
  </data>
  <data name="InvalidIntegerLiteral" xml:space="preserve">
    <value>Invalid integer literal '{0}'</value>
  </data>
  <data name="InvalidMethodCall" xml:space="preserve">
    <value>No applicable method exists in type '{0}'</value>
  </data>
  <data name="InvalidRealLiteral" xml:space="preserve">
    <value>Invalid real literal '{0}'</value>
  </data>
  <data name="NeitherTypeConvertsToOther" xml:space="preserve">
    <value>Neither of the types '{0}' and '{1}' converts to the other</value>
  </data>
  <data name="NoApplicableConstructor" xml:space="preserve">
    <value>No applicable constructor exists in type '{0}'</value>
  </data>
  <data name="NoApplicableIndexer" xml:space="preserve">
    <value>No applicable indexer exists in type '{0}'</value>
  </data>
  <data name="OpenParenExpected" xml:space="preserve">
    <value>'(' expected</value>
  </data>
  <data name="SyntaxError" xml:space="preserve">
    <value>Syntax error</value>
  </data>
  <data name="TypeHasNoNullableForm" xml:space="preserve">
    <value>Type '{0}' has no nullable form</value>
  </data>
  <data name="TypeIdentifierExpected" xml:space="preserve">
    <value>Type identifier expected</value>
  </data>
  <data name="TypeofRequiresAType" xml:space="preserve">
    <value>The 'typeof' keyword requires a type as an argument</value>
  </data>
  <data name="TypeofRequiresOneArg" xml:space="preserve">
    <value>The 'typeof' keyword requires 1 argument</value>
  </data>
  <data name="UnknownPropertyOrField" xml:space="preserve">
    <value>No property or field '{0}' exists in type '{1}'</value>
  </data>
  <data name="UnterminatedStringLiteral" xml:space="preserve">
    <value>Unterminated string literal</value>
  </data>
  <data name="InvalidOperation" xml:space="preserve">
    <value>Invalid Operation</value>
  </data>
  <data name="AmbiguousBinaryOperatorInvocation" xml:space="preserve">
    <value>Ambiguous invocation of user defined operator '{0}' in types '{1}' and '{2}'</value>
  </data>
  <data name="AmbiguousUnaryOperatorInvocation" xml:space="preserve">
    <value>Ambiguous invocation of user defined operator '{0}' in type '{1}'</value>
  </data>
  <data name="AmbiguousDelegateInvocation" xml:space="preserve">
    <value>Ambiguous invocation of delegate (multiple overloads found)</value>
  </data>
  <data name="CloseCurlyBracketExpected" xml:space="preserve">
    <value>'}}' expected</value>
  </data>
  <data name="OpenCurlyBracketExpected" xml:space="preserve">
    <value>'{{' expected</value>
  </data>
  <data name="EqualExpected" xml:space="preserve">
    <value>'=' expected</value>
  </data>
  <data name="CloseTypeArgumentListExpected" xml:space="preserve">
    <value>'&gt;' expected</value>
  </data>
  <data name="MethodTypeParametersCantBeInferred" xml:space="preserve">
    <value>The type arguments for method '{0}' cannot be inferred from the usage.</value>
  </data>
  <data name="ParamsArrayTypeNotAnArray" xml:space="preserve">
    <value>Params array type is not an array, element not found</value>
  </data>
  <data name="AmbiguousConstructorInvocation" xml:space="preserve">
    <value>Ambiguous invocation of constructor in type '{0}'</value>
  </data>
  <data name="UnsupportedMultidimensionalArrays" xml:space="preserve">
    <value>Multidimensional arrays are not supported</value>
  </data>
  <data name="InvalidInitializerMemberDeclarator" xml:space="preserve">
    <value>Invalid initializer member declarator</value>
  </data>
  <data name="CollectionInitializationNotSupported" xml:space="preserve">
    <value>Cannot initialize type '{0}' with a collection initializer because it does not implement '{1}'</value>
  </data>
  <data name="UnableToFindAppropriateAddMethod" xml:space="preserve">
    <value>The best overloaded Add method '{0}.Add' for the collection initializer has some invalid arguments</value>
  </data>
</root>