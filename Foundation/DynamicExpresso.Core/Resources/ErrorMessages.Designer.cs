//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DynamicExpresso.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ErrorMessages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ErrorMessages() {
        }

		/// <summary>
		///   Returns the cached ResourceManager instance used by this class.
		/// </summary>
		[global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DynamicExpresso.Resources.ErrorMessages", typeof(ErrorMessages).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
		}

		/// <summary>
		///   Looks up a localized string similar to Ambiguous invocation of indexer in type &apos;{0}&apos;.
		/// </summary>
		internal static string AmbiguousIndexerInvocation {
            get {
                return ResourceManager.GetString("AmbiguousIndexerInvocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ambiguous invocation of method &apos;{0}&apos; in type &apos;{1}&apos;.
        /// </summary>
        internal static string AmbiguousMethodInvocation {
            get {
                return ResourceManager.GetString("AmbiguousMethodInvocation", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Ambiguous invocation of user defined operator &apos;{0}&apos; in type &apos;{1}&apos;.
        /// </summary>
        internal static string AmbiguousUnaryOperatorInvocation
        {
            get
            {
                return ResourceManager.GetString("AmbiguousUnaryOperatorInvocation", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Ambiguous invocation of user defined operator &apos;{0}&apos; in types &apos;{1}&apos; and &apos;{2}&apos;.
        /// </summary>
        internal static string AmbiguousBinaryOperatorInvocation
        {
            get
            {
                return ResourceManager.GetString("AmbiguousBinaryOperatorInvocation", resourceCulture);
            }
        }

        /// <summary>
		/// Looks up a localized string similar to Ambiguous invocation of delegate (multiple overloads found).
        /// </summary>
        internal static string AmbiguousDelegateInvocation
		{
			get
			{
				return ResourceManager.GetString("AmbiguousDelegateInvocation", resourceCulture);
			}
		}

        /// <summary>
        ///   Looks up a localized string similar to Argument list incompatible with delegate expression.
        /// </summary>
        internal static string ArgsIncompatibleWithDelegate {
            get {
                return ResourceManager.GetString("ArgsIncompatibleWithDelegate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Argument list incompatible with lambda expression.
        /// </summary>
        internal static string ArgsIncompatibleWithLambda {
            get {
                return ResourceManager.GetString("ArgsIncompatibleWithLambda", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both of the types &apos;{0}&apos; and &apos;{1}&apos; convert to the other.
        /// </summary>
        internal static string BothTypesConvertToOther {
            get {
                return ResourceManager.GetString("BothTypesConvertToOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A value of type &apos;{0}&apos; cannot be converted to type &apos;{1}&apos;.
        /// </summary>
        internal static string CannotConvertValue {
            get {
                return ResourceManager.GetString("CannotConvertValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;]&apos; or &apos;,&apos; expected.
        /// </summary>
        internal static string CloseBracketOrCommaExpected {
            get {
                return ResourceManager.GetString("CloseBracketOrCommaExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;)&apos; or &apos;,&apos; expected.
        /// </summary>
        internal static string CloseParenOrCommaExpected {
            get {
                return ResourceManager.GetString("CloseParenOrCommaExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;)&apos; or operator expected.
        /// </summary>
        internal static string CloseParenOrOperatorExpected {
            get {
                return ResourceManager.GetString("CloseParenOrOperatorExpected", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;=&apos; expected.
        /// </summary>
        internal static string EqualExpected
        {
            get
            {
                return ResourceManager.GetString("EqualExpected", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;:&apos; expected.
        /// </summary>
        internal static string ColonExpected {
            get {
                return ResourceManager.GetString("ColonExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digit expected.
        /// </summary>
        internal static string DigitExpected {
            get {
                return ResourceManager.GetString("DigitExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;.&apos; or &apos;(&apos; expected.
        /// </summary>
        internal static string DotOrOpenParenExpected {
            get {
                return ResourceManager.GetString("DotOrOpenParenExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expression expected.
        /// </summary>
        internal static string ExpressionExpected {
            get {
                return ResourceManager.GetString("ExpressionExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expression must be writable.
        /// </summary>
        internal static string ExpressionMustBeWritable {
            get {
                return ResourceManager.GetString("ExpressionMustBeWritable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The first expression must be of type &apos;Boolean&apos;.
        /// </summary>
        internal static string FirstExprMustBeBool {
            get {
                return ResourceManager.GetString("FirstExprMustBeBool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} (at index {1})..
        /// </summary>
        internal static string Format {
            get {
                return ResourceManager.GetString("Format", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identifier expected.
        /// </summary>
        internal static string IdentifierExpected {
            get {
                return ResourceManager.GetString("IdentifierExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operator &apos;{0}&apos; incompatible with operand type &apos;{1}&apos;.
        /// </summary>
        internal static string IncompatibleOperand {
            get {
                return ResourceManager.GetString("IncompatibleOperand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operator &apos;{0}&apos; incompatible with operand types &apos;{1}&apos; and &apos;{2}&apos;.
        /// </summary>
        internal static string IncompatibleOperands {
            get {
                return ResourceManager.GetString("IncompatibleOperands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect number of indexes.
        /// </summary>
        internal static string IncorrectNumberOfIndexes {
            get {
                return ResourceManager.GetString("IncorrectNumberOfIndexes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Syntax error &apos;{0}&apos;.
        /// </summary>
        internal static string InvalidCharacter {
            get {
                return ResourceManager.GetString("InvalidCharacter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Character literal must contain exactly one character.
        /// </summary>
        internal static string InvalidCharacterLiteral {
            get {
                return ResourceManager.GetString("InvalidCharacterLiteral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid character escape sequence.
        /// </summary>
        internal static string InvalidEscapeSequence {
            get {
                return ResourceManager.GetString("InvalidEscapeSequence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Array index must be an integer expression.
        /// </summary>
        internal static string InvalidIndex {
            get {
                return ResourceManager.GetString("InvalidIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid integer literal &apos;{0}&apos;.
        /// </summary>
        internal static string InvalidIntegerLiteral {
            get {
                return ResourceManager.GetString("InvalidIntegerLiteral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No applicable method exists in type &apos;{0}&apos;.
        /// </summary>
        internal static string InvalidMethodCall {
            get {
                return ResourceManager.GetString("InvalidMethodCall", resourceCulture);
            }
        }

		/// <summary>
		/// Looks up a localized string similar to Params array type is not an array, element not found
		/// </summary>
		internal static string ParamsArrayTypeNotAnArray
		{
			get
			{
				return ResourceManager.GetString("ParamsArrayTypeNotAnArray", resourceCulture);
			}
		}

		/// <summary>
		/// Looks up a localized string similar to The type arguments for method '{0}' cannot be inferred from the usage.
		/// </summary>
		internal static string MethodTypeParametersCantBeInferred
		{
			get
			{
				return ResourceManager.GetString("MethodTypeParametersCantBeInferred", resourceCulture);
			}
		}

		/// <summary>
		///   Looks up a localized string similar to Invalid real literal &apos;{0}&apos;.
		/// </summary>
		internal static string InvalidRealLiteral {
            get {
                return ResourceManager.GetString("InvalidRealLiteral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neither of the types &apos;{0}&apos; and &apos;{1}&apos; converts to the other.
        /// </summary>
        internal static string NeitherTypeConvertsToOther {
            get {
                return ResourceManager.GetString("NeitherTypeConvertsToOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No applicable constructor exists in type &apos;{0}&apos;.
        /// </summary>
        internal static string NoApplicableConstructor {
            get {
                return ResourceManager.GetString("NoApplicableConstructor", resourceCulture);
            }
		}

		/// <summary>
		///   Looks up a localized string similar to Ambiguous invocation of constructor in type &apos;{0}&apos;.
		/// </summary>
		internal static string AmbiguousConstructorInvocation
		{
			get
			{
				return ResourceManager.GetString("AmbiguousConstructorInvocation", resourceCulture);
			}
		}

		/// <summary>
		///   Looks up a localized string similar to No applicable indexer exists in type &apos;{0}&apos;.
		/// </summary>
		internal static string NoApplicableIndexer {
            get {
                return ResourceManager.GetString("NoApplicableIndexer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;(&apos; expected.
        /// </summary>
        internal static string OpenParenExpected {
            get {
                return ResourceManager.GetString("OpenParenExpected", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;{&apos; expected.
        /// </summary>
        internal static string OpenCurlyBracketExpected
        {
            get
            {
                return ResourceManager.GetString("OpenCurlyBracketExpected", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;}&apos; expected.
        /// </summary>
        internal static string CloseCurlyBracketExpected
        {
            get
            {
                return ResourceManager.GetString("CloseCurlyBracketExpected", resourceCulture);
            }
        }

		/// <summary>
		///   Looks up a localized string similar to &apos;>&apos; expected.
		/// </summary>
		internal static string CloseTypeArgumentListExpected
		{
			get
			{
				return ResourceManager.GetString("CloseTypeArgumentListExpected", resourceCulture);
			}
		}
		
        /// <summary>
        ///   Looks up a localized string similar to Syntax error.
        /// </summary>
        internal static string SyntaxError {
            get {
                return ResourceManager.GetString("SyntaxError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type &apos;{0}&apos; has no nullable form.
        /// </summary>
        internal static string TypeHasNoNullableForm {
            get {
                return ResourceManager.GetString("TypeHasNoNullableForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type identifier expected.
        /// </summary>
        internal static string TypeIdentifierExpected {
            get {
                return ResourceManager.GetString("TypeIdentifierExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The &apos;typeof&apos; keyword requires a type as an argument.
        /// </summary>
        internal static string TypeofRequiresAType {
            get {
                return ResourceManager.GetString("TypeofRequiresAType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The &apos;typeof&apos; keyword requires 1 argument.
        /// </summary>
        internal static string TypeofRequiresOneArg {
            get {
                return ResourceManager.GetString("TypeofRequiresOneArg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No property or field &apos;{0}&apos; exists in type &apos;{1}&apos;.
        /// </summary>
        internal static string UnknownPropertyOrField {
            get {
                return ResourceManager.GetString("UnknownPropertyOrField", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unterminated string literal.
        /// </summary>
        internal static string UnterminatedStringLiteral {
            get {
                return ResourceManager.GetString("UnterminatedStringLiteral", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Unterminated string literal.
        /// </summary>
        internal static string InvalidOperation {
            get {
                return ResourceManager.GetString("InvalidOperation", resourceCulture);
            }
        }

		/// <summary>
		///   Looks up a localized string similar to Multidimensional arrays are not supported.
		/// </summary>
		internal static string UnsupportedMultidimensionalArrays
		{
			get
			{
				return ResourceManager.GetString("UnsupportedMultidimensionalArrays", resourceCulture);
			}
		}

		/// <summary>
		///   Looks up a localized string similar to Invalid initializer member declarator.
		/// </summary>
		internal static string InvalidInitializerMemberDeclarator {
			get {
				return ResourceManager.GetString("InvalidInitializerMemberDeclarator", resourceCulture);
			}
		}

		/// <summary>
		///   Looks up a localized string similar to Cannot initialize type '{0}' with a collection initializer because it does not implement '{1}'.
		/// </summary>
		internal static string CollectionInitializationNotSupported {
			get {
				return ResourceManager.GetString("CollectionInitializationNotSupported", resourceCulture);
			}
		}

		/// <summary>
		///   Looks up a localized string similar to The best overloaded Add method '{0}.Add' for the collection initializer has some invalid arguments.
		/// </summary>
		internal static string UnableToFindAppropriateAddMethod { 
			get {
				return ResourceManager.GetString("UnableToFindAppropriateAddMethod", resourceCulture);
			}
		}

	}
}
