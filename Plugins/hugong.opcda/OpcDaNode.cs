using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace hugong.opcda
{
	public class OpcDaNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public OpcDaNode( )
		{
			ProgID = "Kepware.KEPServerEX.V6";
			NodeID = "127.0.0.1";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public OpcDaNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public void LoadByXmlElement( XElement element )
		{
			ProgID = GetXmlValue( element, nameof( ProgID ), ProgID, m => m );
			NodeID = GetXmlValue( element, nameof( NodeID ), NodeID, m => m );
		}

		/// <inheritdoc/>
		public XElement ToXmlElement( XElement element )
		{
			element.SetAttributeValue( nameof( ProgID ), ProgID );
			element.SetAttributeValue( nameof( NodeID ), NodeID );
			return element;
		}

		#endregion


		/// <summary>
		/// 获取或设置唯一的标识信息
		/// </summary>
		[Category( "OPC信息" )]
		[DisplayName( "ProgID" )]
		[Description( "ProgID" )]
		[DefaultValue( "Kepware.KEPServerEX.V6" )]
		public string ProgID { get; set; }

		/// <summary>
		/// 节点信息
		/// </summary>
		[Category( "OPC信息" )]
		[DisplayName( "NodeID" )]
		[Description( "NodeID" )]
		[DefaultValue( "127.0.0.1" )]
		public string NodeID { get; set; }

		public string PluginsType { get; set; } = "hugong.opcda";

		/// <inheritdoc/>
		public override string ToString( ) => $"[OpcDaNode]";



		#region Static

		/// <summary>
		/// 从XElement中提取相关的属性信息，如果不存在，就返回默认值
		/// </summary>
		/// <typeparam name="T">最终的类型信息</typeparam>
		/// <param name="element">元素内容</param>
		/// <param name="name">属性的名称</param>
		/// <param name="defaultValue">默认提供的值</param>
		/// <param name="trans">转换方式</param>
		/// <returns>最终的值</returns>
		public static T GetXmlValue<T>( XElement element, string name, T defaultValue, Func<string, T> trans )
		{
			XAttribute attribute = element.Attribute( name );
			if (attribute == null) return defaultValue;

			try
			{
				return trans( attribute.Value );
			}
			catch
			{
				return defaultValue;
			}
		}

		/// <inheritdoc cref="GetXmlValue{T}(XElement, string, T, Func{string, T})"/>
		public static T GetXmlEnum<T>( XElement element, string name, T defaultValue ) where T : struct
		{
			XAttribute attribute = element.Attribute( name );
			if (attribute == null) return defaultValue;

			try
			{
				return (T)Enum.Parse( typeof( T ), attribute.Value );
			}
			catch
			{
				return defaultValue;
			}
		}

		#endregion
	}
}
