using OPCAutomation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace hugong.opcda
{
	public class DeviceOpcDa
	{
		public DeviceOpcDa( XElement element ) { }

		/// <summary>
		/// 初始化的内容
		/// </summary>
		/// <param name="element">初始化的XML配置信息</param>
		public void IniDevice( XElement element )
		{
			this.node = new OpcDaNode( element );
			this.myServer = new OPCServer( );
			this.dictOpcItems = new Dictionary<string, OPCItem>( );
		}

		#region Read Support

		private Task<T> ReadValueHelper<T>( string address )
		{
			OPCItem item = GetOpcItem( address );
			item.Read( 1, out object value, out object qua, out object timestamp2 );
			if ((short)qua == (int)OPCQuality.OPCQualityGood)
			{
				throw new Exception( "Read failed, Qos: " + qua.ToString( ) );
			}
			return Task.FromResult( (T)value );
		}

		public async Task<bool> ReadBool( string address ) => await ReadValueHelper<bool>( address );

		public async Task<bool[]> ReadBool( string address, int length ) => await ReadValueHelper<bool[]>( address );

		public async Task<byte[]> Read( string address, int length ) => await ReadValueHelper<byte[]>( address );

		public async Task<byte> ReadByte( string address ) => await ReadValueHelper<byte>( address );

		public async Task<short> ReadInt16( string address ) => await ReadValueHelper<short>( address );

		public async Task<short[]> ReadInt16( string address, int length ) => await ReadValueHelper<short[]>( address );

		public async Task<ushort> ReadUInt16( string address ) => await ReadValueHelper<ushort>( address );

		public async Task<ushort[]> ReadUInt16( string address, int length ) => await ReadValueHelper<ushort[]>( address );

		public async Task<int> ReadInt32( string address ) => await ReadValueHelper<int>( address );

		public async Task<int[]> ReadInt32( string address, int length ) => await ReadValueHelper<int[]>( address );

		public async Task<uint> ReadUInt32( string address ) => await ReadValueHelper<uint>( address );

		public async Task<uint[]> ReadUInt32( string address, int length ) => await ReadValueHelper<uint[]>( address );

		public async Task<long> ReadInt64( string address ) => await ReadValueHelper<long>( address );

		public async Task<long[]> ReadInt64( string address, int length ) => await ReadValueHelper<long[]>( address );

		public async Task<ulong> ReadUInt64( string address ) => await ReadValueHelper<ulong>( address );

		public async Task<ulong[]> ReadUInt64( string address, int length ) => await ReadValueHelper<ulong[]>( address );

		public async Task<float> ReadFloat( string address ) => await ReadValueHelper<float>( address );

		public async Task<float[]> ReadFloat( string address, int length ) => await ReadValueHelper<float[]>( address );

		public async Task<double> ReadDouble( string address ) => await ReadValueHelper<double>( address );

		public async Task<double[]> ReadDouble( string address, int length ) => await ReadValueHelper<double[]>( address );

		public async Task<string> ReadString( string address ) => await ReadValueHelper<string>( address );

		public async Task<string[]> ReadString( string address, int length ) => await ReadValueHelper<string[]>( address );


		#endregion

		#region Write Support

		public async Task<int> WriteBool( string address, bool value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteBool( string address, bool[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteByte( string address, byte value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> Write( string address, byte[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteInt16( string address, short value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteInt16( string address, short[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteUInt16( string address, ushort value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteUInt16( string address, ushort[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteInt32( string address, int value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteInt32( string address, int[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteUInt32( string address, uint value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteUInt32( string address, uint[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteInt64( string address, long value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteInt64( string address, long[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteUInt64( string address, ulong value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteUInt64( string address, ulong[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteFloat( string address, float value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteFloat( string address, float[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteDouble( string address, double value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteDouble( string address, double[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteString( string address, string value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}

		public async Task<int> WriteString( string address, string[] value )
		{
			GetOpcItem( address ).Write( value ); return await Task.FromResult( 1 );
		}


		#endregion

		#region Protect Override

		/// <summary>
		/// 开始执行请求之前，需要执行的方法
		/// </summary>
		public void BeforStart( )
		{
			try
			{
				this.myServer?.Disconnect( );
				this.myServer.Connect( this.node.ProgID, this.node.NodeID );
				this.myGroup = myServer.OPCGroups.Add( "group" );
				this.myGroup.IsActive = true;
				this.myItems = myGroup.OPCItems;
				lock (dictLock)
				{
					this.dictOpcItems.Clear( );
				}
			}
			catch (Exception ex)
			{

			}
		}

		public void AfterClose( )
		{
			myServer?.Disconnect( );
		}

		///// <inheritdoc/>
		//public override OperateResult<string> CheckDeviceStatus( )
		//{
		//	try
		//	{
		//		if (myServer == null)
		//			myServer = new OPCServer( );
		//		myServer.Connect( this.node.ProgID, this.node.NodeID );
		//		if (myServer.ServerState != (int)OPCServerState.OPCRunning)
		//		{
		//			return new OperateResult<string>( "连接失败！" );
		//		}
		//		else
		//		{
		//			return OperateResult.CreateSuccessResult( "" );
		//		}
		//	}
		//	catch (Exception ex)
		//	{
		//		return new OperateResult<string>( ex.Message );
		//	}
		//}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[OpcDa] [{node.ProgID}:{node.NodeID}]";

		#endregion



		private OPCItem GetOpcItem( string tag )
		{
			lock (dictLock)
			{
				if (dictOpcItems.ContainsKey( tag )) return dictOpcItems[tag];
				OPCItem item = myItems.AddItem( tag, 1 );
				dictOpcItems.Add( tag, item );
				return item;
			}
		}

		private Dictionary<string, OPCItem> dictOpcItems;

		private OpcDaNode node;
		private OPCServer myServer = null;        // 分组实例
		private OPCGroup myGroup;                 // 分组的TAG节点
		private OPCItems myItems;                 // 数据点信息
		private object dictLock = new object( );  // 词典信息
	}
}
