using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace hugong.opcda
{
	/// <summary>
	/// 插件助手，定义了插件的基本信息，和注册基本的
	/// </summary>
	public class PluginsHelper
	{
		/// <summary>
		/// 公司的名称信息
		/// </summary>
		public static string Company { get; set; } = "福建省天正信息资讯工程有限公司";

		/// <summary>
		/// 插件的网址信息
		/// </summary>
		public static string Http { get; set; }

		/// <summary>
		/// 当前的插件的版本
		/// </summary>
		public static string Version { get; set; } = "1.0.0";

		/// <summary>
		/// 当前公司的基本介绍信息
		/// </summary>
		public static string Description { get; set; }

		/// <summary>
		/// 当前插件的框架信息
		/// </summary>
		public static string Framework { get; set; } = "net461";

		/// <summary>
		/// 获取或设置当前系统的语言信息，0：中文，1：英文，2：法语，3，俄语，4：阿拉伯语，5：西班牙语，6：日语<br />
		/// Get or set the language information of the current system, 0: Chinese, 1: English, 2: French, 3, Russian, 4: Arabic, 5: Spanish, 6: Japanese
		/// </summary>
		/// <remarks>
		/// 这个值将会由网关进行设置，传递给插件，插件在赋值数据及注释时，根据语言信息来完善当前的内容<br />
		/// This value will be set by the gateway and passed to the plug-in. The plug-in will complete the current content according to the language information when assigning data and comments.
		/// </remarks>
		public static int Language { get; set; }

		/// <summary>
		/// 当前插件的发布日期
		/// </summary>
		public static DateTime ReleaseDate { get; set; } = new DateTime( 2021, 8, 14 );

		/// <summary>
		/// 当前的品牌的自定义的图标信息，需要为一个16*16大小的png,jpg格式的图片的内容
		/// </summary>
		public static byte[] Icon16 { get; set; } = StringToHexBytes( @"89 50 4E 47 0D 0A 1A 0A 00 00 00 0D 49 48 44 52 00 00 00 10 00 00 00 10 08 06 00 00 00 1F F3 FF 
61 00 00 00 01 73 52 47 42 00 AE CE 1C E9 00 00 00 04 67 41 4D 41 00 00 B1 8F 0B FC 61 05 00 00 
00 09 70 48 59 73 00 00 0E C3 00 00 0E C3 01 C7 6F A8 64 00 00 01 C9 49 44 41 54 38 4F C5 92 BF 
6B 53 51 14 C7 CF 7D 09 42 2D 15 DD 2D B5 BF 96 6A 84 A6 69 C0 41 32 B5 4D 06 29 42 4B 28 08 5A 
44 17 87 2E 4E E2 EC E6 D4 A1 A5 5B 41 27 21 B6 4B 5F 3A F4 C7 AB B4 D4 FC 68 A7 60 C7 88 88 62 
D5 49 50 88 B9 D7 CF 79 7D 06 D2 7F 20 5F F8 BC 73 EE 3D 87 73 EF 3D E7 49 C7 65 22 2B 99 6C A5 
5F C4 75 59 13 3B 79 E7 27 2D EB B8 88 BD A0 31 67 BC 3F 7B 7E CA AA 7F 3B 77 E4 79 CE 0E B0 DB 
CD 7E CD D3 4D 92 27 30 FB D4 AB 12 7C 99 C9 95 B5 F0 B4 88 F7 41 31 4E B6 C8 E9 D1 5C E2 8F 31 
EF C9 2D 19 E7 F2 86 C0 15 36 76 E0 12 FC 84 51 18 83 11 92 96 38 E9 18 FF 16 3C 85 02 1C C2 6F 
F8 0E CB 7A 83 04 0C C1 5D 98 82 3A DC 07 E4 3E 3B 71 93 38 BB 30 0C 0F 41 9F 92 21 06 52 D0 02 
49 A8 D3 8D 5A 50 4C FD C0 5F 87 EB 10 CA 88 6B 62 4E CF 56 72 03 36 C8 FB 12 14 C7 1B D8 5F 5A 
A0 0F 6A 81 9F FA AB 19 E8 1B 44 CD 35 BC DB 68 31 3D 5D 8B 5F 83 8F D0 52 D8 44 E4 22 7B 4E EE 
2A 05 CA 38 83 B0 09 9A AF 7D 6A 49 37 B4 21 BD E1 EA 4C 97 21 1C 19 FA 0A 8B F0 04 AA D0 00 ED 
59 4B 3A 85 3B D8 55 18 E3 E2 9F B8 CB 36 7E 25 E2 39 B5 12 41 31 DD CC 64 4B 3C CB D3 BC 34 DC 
14 E3 6C CC C4 9A 7A 83 03 D0 27 BC E2 BB 8C 4D 81 0F 6D A2 88 E6 BC 05 9D D8 6B FE AE 17 4D 6B 
F2 5E D4 F9 67 A0 B3 7E 00 6B 54 DF C2 6A F7 DB 65 9C 4E 48 99 81 05 6E 77 31 6C A2 35 DE 0A 46 
E7 3D 0B 8F 02 7F DC 72 DC 06 FE 3D 6B E2 FF FB 21 D1 FE 1C EE 3C F0 DF B8 37 61 A0 83 12 F9 07 
38 43 97 21 5B AA 26 65 00 00 00 00 49 45 4E 44 AE 42 60 82" );

		/// <summary>
		/// 当前插件中定义的设备信息
		/// </summary>
		/// <returns>定义的插件设备列表</returns>
		public static List<object> DeviceDefinitions( )
		{
			return new List<object>( )
			{
				new
				{
					DeviceName = "OPC DA设备",
					Description = "设备的一些注释信息",
					DeviceNode = typeof( OpcDaNode ),
					DeviveObject = typeof( DeviceOpcDa ),
				}
			};
		}

		#region Helper Method

		public static byte[] StringToHexBytes( string hex )
		{
			MemoryStream ms = new MemoryStream( );

			for (int i = 0; i < hex.Length; i++)
			{
				if ((i + 1) < hex.Length)
				{
					if (GetHexCharIndex( hex[i] ) >= 0 && GetHexCharIndex( hex[i + 1] ) >= 0)
					{
						// 这是一个合格的字节数据
						ms.WriteByte( (byte)(GetHexCharIndex( hex[i] ) * 16 + GetHexCharIndex( hex[i + 1] )) );
						i++;
					}
				}
			}

			byte[] result = ms.ToArray( );
			ms.Dispose( );
			return result;
		}


		private static int GetHexCharIndex( char ch )
		{
			switch (ch)
			{
				case '0': return 0;
				case '1': return 1;
				case '2': return 2;
				case '3': return 3;
				case '4': return 4;
				case '5': return 5;
				case '6': return 6;
				case '7': return 7;
				case '8': return 8;
				case '9': return 9;
				case 'A':
				case 'a': return 10;
				case 'B':
				case 'b': return 11;
				case 'C':
				case 'c': return 12;
				case 'D':
				case 'd': return 13;
				case 'E':
				case 'e': return 14;
				case 'F':
				case 'f': return 15;
				default: return -1;
			}
		}
		#endregion
	}
}
