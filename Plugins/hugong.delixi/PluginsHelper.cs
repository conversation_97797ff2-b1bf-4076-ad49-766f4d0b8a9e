using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace hugong.delixi
{
	public class PluginsHelper
	{
		
		/// <summary>
		/// 公司的名称信息
		/// </summary>
		public static string Company { get; set; } = "福建省天正信息资讯工程有限公司";

		/// <summary>
		/// 插件的网址信息
		/// </summary>
		public static string Http { get; set; }

		/// <summary>
		/// 当前的插件的版本
		/// </summary>
		public static string Version { get; set; } = "1.0.0";

		/// <summary>
		/// 当前公司的基本介绍信息
		/// </summary>
		public static string Description { get; set; } = "德力西设备的插件信息，型号为DTSU6606的RTU通信方式的设备。";
		/// <summary>
		/// 当前插件的框架信息
		/// </summary>
#if NETSTANDARD2_0_OR_GREATER
		public static string Framework { get; set; } = "standard2.1";
#else
		public static string Framework { get; set; } = "net462";
#endif
		/// <summary>
		/// 获取或设置当前系统的语言信息，0：中文，1：英文，2：法语，3，俄语，4：阿拉伯语，5：西班牙语，6：日语<br />
		/// Get or set the language information of the current system, 0: Chinese, 1: English, 2: French, 3, Russian, 4: Arabic, 5: Spanish, 6: Japanese
		/// </summary>
		/// <remarks>
		/// 这个值将会由网关进行设置，传递给插件，插件在赋值数据及注释时，根据语言信息来完善当前的内容<br />
		/// This value will be set by the gateway and passed to the plug-in. The plug-in will complete the current content according to the language information when assigning data and comments.
		/// </remarks>
		public static int Language { get; set; }

		/// <summary>
		/// 当前版本插件的发布日期<br />
		/// The release date of the current version of the plugin
		/// </summary>
		public static DateTime ReleaseDate { get; set; } = new DateTime( 2021, 8, 9 );

		/// <summary>
		/// 当前的品牌的自定义的图标信息，需要为一个16*16大小的png,jpg格式的图片的内容
		/// </summary>
		public static byte[] Icon16 { get; set; } = HexStringToBytes( @"89 50 4E 47 0D 0A 1A 0A 00 00 00 0D 49 48 44 52 00 00 00 10 00 00 00 10 08 06 00 00 00 1F F3 FF 
61 00 00 00 09 70 48 59 73 00 00 0E C4 00 00 0E C4 01 95 2B 0E 1B 00 00 00 20 63 48 52 4D 00 00 
7A 25 00 00 80 83 00 00 F9 FF 00 00 80 E9 00 00 75 30 00 00 EA 60 00 00 3A 98 00 00 17 6F 92 5F 
C5 46 00 00 00 A7 49 44 41 54 78 DA C4 93 DB 09 C3 30 0C 45 8F 8A 7F 0D DD A0 19 C1 1B 24 C6 93 
74 94 4E D2 0E 12 48 47 E8 08 1D 21 64 01 F5 47 81 3C 9A C4 E0 42 F5 25 7C A5 03 92 AF 44 55 29 
09 07 20 22 79 D5 ED 10 80 0A 08 C0 45 93 BF 8A AA CE 01 ED D0 58 D6 00 0F E0 6E 4D D5 92 A7 C9 
8B B3 A6 CE A8 E7 45 CD D3 40 FB 23 1C 15 ED C5 89 C2 28 06 B8 2F 6F 2F A0 B7 BC B7 3D 1C 02 E2 
86 FE 06 6E 39 80 6E 43 8F 3B 1A 80 FC 7F 89 3F FB 85 38 31 53 6D 8E 0C 39 80 F5 2D CC 8F 67 04 
8D 36 AF A7 CE D5 E4 45 4A CF F9 33 00 19 BC 24 DE 26 F6 3B 63 00 00 00 00 49 45 4E 44 AE 42 60 
82" );

		#region Device Info

		// =========================================================================================================================================↓
		// 这部分的内容根据不同的插件类型有所区别，此处是一个模板插件的例子

		/// <summary>
		/// 返回模板插件信息
		/// </summary>
		/// <returns>XML的消息</returns>
		public static XElement[] PluginsTemplateDefinition( )
		{
			// 此处可以从项目资源文件加载，也可以从本地文件加载  等等都是可以的。
			StreamReader sr = new StreamReader( System.Reflection.Assembly.GetExecutingAssembly( ).GetManifestResourceStream( "hugong.delixi.Template1.xml" ) );
			return new XElement[]
			{
				XElement.Parse(sr.ReadToEnd()),
			};
		}

		// =========================================================================================================================================↑

		#endregion


		private static int GetHexCharIndex( char ch )
		{
			switch (ch)
			{
				case '0': return 0;
				case '1': return 1;
				case '2': return 2;
				case '3': return 3;
				case '4': return 4;
				case '5': return 5;
				case '6': return 6;
				case '7': return 7;
				case '8': return 8;
				case '9': return 9;
				case 'A':
				case 'a': return 10;
				case 'B':
				case 'b': return 11;
				case 'C':
				case 'c': return 12;
				case 'D':
				case 'd': return 13;
				case 'E':
				case 'e': return 14;
				case 'F':
				case 'f': return 15;
				default: return -1;
			}
		}

		/// <summary>
		/// 将16进制的字符串转化成Byte数据，将检测每2个字符转化，也就是说，中间可以是任意字符<br />
		/// Converts a 16-character string into byte data, which will detect every 2 characters converted, that is, the middle can be any character
		/// </summary>
		/// <param name="hex">十六进制的字符串，中间可以是任意的分隔符</param>
		/// <returns>转换后的字节数组</returns>
		/// <remarks>参数举例：AA 01 34 A8</remarks>
		/// <example>
		/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\BasicFramework\SoftBasicExample.cs" region="HexStringToBytesExample" title="HexStringToBytes示例" />
		/// </example>
		private static byte[] HexStringToBytes( string hex )
		{
			MemoryStream ms = new MemoryStream( );

			for (int i = 0; i < hex.Length; i++)
			{
				if ((i + 1) < hex.Length)
				{
					if (GetHexCharIndex( hex[i] ) >= 0 && GetHexCharIndex( hex[i + 1] ) >= 0)
					{
						// 这是一个合格的字节数据
						ms.WriteByte( (byte)(GetHexCharIndex( hex[i] ) * 16 + GetHexCharIndex( hex[i + 1] )) );
						i++;
					}
				}
			}

			byte[] result = ms.ToArray( );
			ms.Dispose( );
			return result;
		}


	}
}
