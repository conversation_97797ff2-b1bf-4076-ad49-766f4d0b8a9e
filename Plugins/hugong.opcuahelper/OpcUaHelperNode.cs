using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace hugong.opcuahelper
{
	public class OpcUaHelperNode
	{
		public OpcUaHelperNode(  )
		{

		}

		public OpcUaHelperNode( XElement element )
		{
			LoadByXmlElement( element );
		}

		[Category( "OpcUa" )]
		[Description( "远程服务器的终结点信息" )]
		public string Url { get; set; }


		#region Xml Interface

		/// <summary>
		/// 从配置的XML信息里加载
		/// </summary>
		/// <param name="element">XML元素</param>
		public void LoadByXmlElement( XElement element )
		{
			Url = GetXmlValue( element, nameof( Url ), Url, m => m );
		}

		/// <summary>
		/// 将信息存储到这个XML信息里去
		/// </summary>
		/// <param name="element">等待存储的xml元素</param>
		/// <returns>最新存储后的内容</returns>
		public XElement ToXmlElement( XElement element )
		{
			element.SetAttributeValue( nameof( Url ), Url );
			return element;
		}

		#endregion

		#region Interface 一些功能支持例子

		/// <summary>
		/// 获取当前的设备信息，是否支持地址的请求，默认为支持，如果不支持，返回 false
		/// </summary>
		/// <returns></returns>
		public bool IsSupportAddressRequest( )
		{
			return true;
		}

		/// <summary>
		/// 如果需要支持示例地址，就写这个方法，返回地址数组信息
		/// </summary>
		/// <returns></returns>
		public List<object> GetDeviceAddressExamples( )
		{
			return new List<object>( )
			{
				new
				{
					AddressExample = "A1",     // 地址的一个示例
					AddressType = "标签地址",  // 地址的类型描述，例如保持型寄存器，D寄存器
					BitEnable = true,          // 该地址是否支持位读取
					WordEnable = true,         // 该地址是否支持字读取
					Mark = "一个标签地址",      // 备注信息，可以用来备注地址范围

					// 以下如果不需要，可以不设置，这样精简一下
					IsHeader = false,                   // 如果设置为true，本行不是地址，而是一行标题样式
					FillTagNameWithAddressType = false, // 是否使用名称信息填充数据标签名
					Unit = "",                          // 数据的单位信息，例如 m/s
					DataType = "",                      // 默认使用的数据类型，有些数据标签使用固定的读写类型信息时，可以指定 bool, byte, short, ushort, int
				},
				new
				{
					AddressExample = "A2",     // 地址的一个示例
					AddressType = "标签地址",  // 地址的类型描述，例如保持型寄存器，D寄存器
					BitEnable = true,          // 该地址是否支持位读取
					WordEnable = true,         // 该地址是否支持字读取
					Mark = "一个标签很给里的地址",      // 备注信息，可以用来备注地址范围

					// 以下如果不需要，可以不设置，这样精简一下
					IsHeader = false,                   // 如果设置为true，本行不是地址，而是一行标题样式
					FillTagNameWithAddressType = false, // 是否使用名称信息填充数据标签名
					Unit = "",                          // 数据的单位信息，例如 m/s，用户双击地址行时，单位信息默认填入请求信息里
					DataType = "",                      // 默认使用的数据类型，有些数据标签使用固定的读写类型信息时，可以指定 bool, byte, short, ushort, int
				},
			};
		}

		#endregion


		#region Static

		/// <summary>
		/// 从XElement中提取相关的属性信息，如果不存在，就返回默认值
		/// </summary>
		/// <typeparam name="T">最终的类型信息</typeparam>
		/// <param name="element">元素内容</param>
		/// <param name="name">属性的名称</param>
		/// <param name="defaultValue">默认提供的值</param>
		/// <param name="trans">转换方式</param>
		/// <returns>最终的值</returns>
		public static T GetXmlValue<T>( XElement element, string name, T defaultValue, Func<string, T> trans )
		{
			XAttribute attribute = element.Attribute( name );
			if (attribute == null) return defaultValue;

			try
			{
				return trans( attribute.Value );
			}
			catch
			{
				return defaultValue;
			}
		}

		/// <inheritdoc cref="GetXmlValue{T}(XElement, string, T, Func{string, T})"/>
		public static T GetXmlEnum<T>( XElement element, string name, T defaultValue ) where T : struct
		{
			XAttribute attribute = element.Attribute( name );
			if (attribute == null) return defaultValue;

			try
			{
				return (T)Enum.Parse( typeof( T ), attribute.Value );
			}
			catch
			{
				return defaultValue;
			}
		}

		#endregion


	}
}
