using OpcUaHelper;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace hugong.opcuahelper
{
	public class DeviceOpcUaHelper
	{
		public DeviceOpcUaHelper( )
		{ 

		}

		/// <summary>
		/// 初始化的内容
		/// </summary>
		/// <param name="element"></param>
		public void IniDevice( XElement element )
		{
			this.node = new OpcUaHelperNode( element );
			this.opcUaClient = new OpcUaClient( );

			this.opcUaClient.OpcStatusChange += OpcUaClient_OpcStatusChange;

		}

		private void OpcUaClient_OpcStatusChange( object sender, OpcUaStatusEventArgs e )
		{

		}

		/// <summary>
		/// 连接之前调用的方法
		/// </summary>
		public void BeforStart( )
		{
			try
			{
				this.opcUaClient.ConnectServer( this.node.Url );
			}
			catch
			{

			}
		}

		#region Read Support

		public async Task<bool> ReadBool( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<bool>( address );
		}

		public async Task<bool[]> ReadBool( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<bool[]>( address );
		}

		public async Task<byte[]> Read( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<byte[]>( address );
		}

		public async Task<byte> ReadByte( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<byte>( address );
		}

		public async Task<short> ReadInt16( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<short>( address );
		}

		public async Task<short[]> ReadInt16( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<short[]>( address );
		}

		public async Task<ushort> ReadUInt16( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<ushort>( address );
		}

		public async Task<ushort[]> ReadUInt16( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<ushort[]>( address );
		}

		public async Task<int> ReadInt32( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<int>( address );
		}

		public async Task<int[]> ReadInt32( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<int[]>( address );
		}

		public async Task<uint> ReadUInt32( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<uint>( address );
		}

		public async Task<uint[]> ReadUInt32( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<uint[]>( address );
		}

		public async Task<long> ReadInt64( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<long>( address );
		}

		public async Task<long[]> ReadInt64( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<long[]>( address );
		}

		public async Task<float> ReadFloat( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<float>( address );
		}

		public async Task<float[]> ReadFloat( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<float[]>( address );
		}

		public async Task<double> ReadDouble( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<double>( address );
		}

		public async Task<double[]> ReadDouble( string address, int length )
		{
			return await this.opcUaClient.ReadNodeAsync<double[]>( address );
		}

		public async Task<string> ReadString( string address )
		{
			return await this.opcUaClient.ReadNodeAsync<string>( address );
		}

		private int CreateResult( bool success )
		{
			if (success) return 1;
			else throw new Exception( "Write value failed" );
		}

		public async Task<int> WriteBool( string address, bool value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteBool( string address, bool[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteByte( string address, byte value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> Write( string address, byte[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteInt16( string address, short value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteInt16( string address, short[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteUInt16( string address, ushort value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteUInt16( string address, ushort[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteInt32( string address, int value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteInt32( string address, int[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteUInt32( string address, uint value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteUInt32( string address, uint[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteInt64( string address, long value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteInt64( string address, long[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteUInt64( string address, ulong value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteUInt64( string address, ulong[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteFloat( string address, float value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteFloat( string address, float[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteDouble( string address, double value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteDouble( string address, double[] value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		public async Task<int> WriteString( string address, string value )
		{
			return CreateResult( await this.opcUaClient.WriteNodeAsync( address, value ) );
		}

		#endregion

		#region BuildIn Request

//		/// <summary>
//		/// 如果设备启动的时候需要支持内置的一些请求信息，用户不需要配置就存在的数据
//		/// </summary>
//		/// <returns></returns>
//		public XElement BuildInRequestRescources( )
//		{
//			return XElement.Parse(
//@"
//<DeviceNode>
//  <RequestNode Name=""测试数据"" Description=""一次完整的数据请求"" RequestInterval=""1000"" Address=""ns=2;s=Machines/Machine A/TestValueInt"" RequestType=""ScalarRead"" DataTypeCode=""int"" />
//  <RequestNode Name=""方法数据请求"" Description=""一次方法的调用信息"" RequestInterval=""1000"" Address=""启动设备"" RequestType=""MethodCall"" ParameterJson=""{&#xD;&#xA;  &quot;mode&quot;: 1234&#xD;&#xA;}"" StoreResultToDeviceData=""true"" />
//  <RequestNode Name=""方法数据请求1"" Description=""一次方法的调用信息"" RequestInterval=""1000"" Address=""StopDevice"" RequestType=""MethodCall"" ParameterJson=""{&#xD;&#xA;  &quot;mode&quot;: 1&#xD;&#xA;}"" StoreResultToDeviceData=""true"" />
//</DeviceNode>
//" );
//		}

		#endregion

		#region Record Telegrame

		/// <summary>
		/// 这是一个由外界调用的记录报文日志的功能方法，LoggerTelegram( "发", byte[], true ); // 参数分别是关键字，报文对象，是否二进制(false 就是ascii格式记录)
		/// </summary>
		public Action<string, byte[], bool> LoggerTelegram { get; set; }

		#endregion

		#region Interface Method

		/// <summary>
		/// 如果有公开的方法接口，这里需要创建一个 ApiMethods 属性，列举需要公开方法的名称
		/// </summary>
		public string[] ApiMethods {
			get
			{
				return new string[] { "StartDevice", "StopDevice" };
			}
		}

		[DisplayName( "启动设备" )]
		[Description( "启动一个设备" )]
		public int StartDevice( int mode )
		{
			return mode + 1;
		}


		[Description( "停止一个设备" )]
		public Task<bool> StopDevice( int mode )
		{
			return Task.FromResult( mode % 2 == 0 );
		}

		#endregion

		public override string ToString( ) => "OpcUa";




		private OpcUaHelperNode node;
		private OpcUaClient opcUaClient;
	}
}
