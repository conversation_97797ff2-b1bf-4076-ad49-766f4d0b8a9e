using HslTechnology.Edge.Device.Base;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Enthernet;
using System.Threading.Tasks;
using HslCommunication.Reflection;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using System.Text.RegularExpressions;
using HslCommunication.Profinet.Melsec.Helper;
using HslCommunication.Core.Address;
using HslTechnology.Edge;

namespace hugong.vulcanizer
{
	public class DeviceVulcanizer : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个永宏编程口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceVulcanizer( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node           = new VulcanizerNode( element );
			this.plc            = new MelsecMcNet( );
			this.plc.IpAddress  = this.node.IpAddress;
			this.plc.Port       = this.node.Port;
			this.SetDeviceInfo( deviceResources, node, this.plc );  // 此处会设置，名称，注释，连接超时，接收超时信息
		}

		public override void AnalysisByBussiness( BusinessEngine business )
		{
			// 此处举例需要添加本设备专用的报警信息，先实例化本地报警资源信息，如果担心相同的设备重复实例化报警资源，
			// 可以将下面的报警节点定义为静态对象，同种设备共享一个对象
			this.localAlarmNodes = new Dictionary<string, AlarmNode>( );
			// 添加一个整数的报警信息示例
			this.localAlarmNodes.Add( "报警", new NodeAlarmInteger( )
			{
				Name = "报警",
				AlarmDefinitions = new AlarmDefinitionNode[] {
				new AlarmDefinitionNode(){ Code = 1, Degree = AlarmDegree.Hint, Name = "设备电流过载"},
				new AlarmDefinitionNode(){ Code = 2, Degree = AlarmDegree.Hint, Name = "温度过低"},
				}
			} );

			// 也可以从XML的资源解析 this.localRegularStruct = RegularStructItemNode.ParesRegular( XElement.Parse( "XML的资源对象" ) );
			this.localRegularStruct = new Dictionary<string, RegularStructItemNode>( );
			this.localRegularStruct.Add( "Struct1", new RegularStructItemNode(
				new List<RegularScalarNode>( ) {
					new RegularScalarNode( "速度", "描述", 0, RegularNodeTypeItem.Int16.Text, -1 ),
					new RegularScalarNode( "温度", "描述", 2, RegularNodeTypeItem.Float.Text, -1 ),
				} )
			{
				Name = "Struct1",
				Description = "普通的数据解析对象",
				StructLength = 10
			} );


			Requests.Add( new ScalarReadRequest( )
			{
				Name = "报警",
				Description = "报警信息",
				Address = "D100",
				Length = -1,
				AlarmRelate = "报警",                                   // 关联上面的报警信息
				DataTypeCode = RegularNodeTypeItem.Int16.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarReadRequest( )
			{
				Name = "温度",
				Description = "当前设备的温度信息",
				Address = "D101",
				Length = 5,
				DataTypeCode = RegularNodeTypeItem.Int16.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );

			// 自定义的结构体信息
			SourceReadRequest sourceReadRequest = new SourceReadRequest( )
			{
				Name = "SourceRead1",
				Description = "批量读取的原始数据信息",
				Address = "D0",
				Length = "20",
				RequestCount = -1,                             // 无限次请求
				RequestInterval = 1000,                        // 1秒请求一次
			};
			sourceReadRequest.RegularScalarNodes.Add( new RegularScalarNode( "速度", "描述", 0, RegularNodeTypeItem.Int16.Text, -1 ) );
			sourceReadRequest.RegularStructNodes.Add( new RegularStructNode( "AA", "描述0", "Struct1", 10, -1, HslTechnology.Edge.Node.Core.ParseType.Struct ) );
			Requests.Add( sourceReadRequest );

			// 继续进行商业逻辑的解析操作
			base.AnalysisByBussiness( business );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => this.plc?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => this.plc?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.plc == null) return new OperateResult<string>( HslTechnology.Edge.Resources.EdgeStringResource.DeviceNullException );

			OperateResult connect = this.plc.ConnectServer( );
			if (connect.IsSuccess) this.plc.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest( SourceReadRequest sourceReadRequest, int byteOffset, int index, IScalarTransform scalarTransform )
		{
			// 三菱PLC原始请求的解析对bool变量，byte变量无效
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
				return string.Empty;

			// 对于特殊的地址暂时不支持
			string address = sourceReadRequest.Address;
			if (address.StartsWith( "s=" ) || address.StartsWith( "S=" ) || Regex.IsMatch( address, "ext=[0-9]+;", RegexOptions.IgnoreCase ) ||
				Regex.IsMatch( address, "mem=", RegexOptions.IgnoreCase ) || Regex.IsMatch( address, "module=[0-9]+;", RegexOptions.IgnoreCase ))
				return string.Empty;


			// 如果不是三菱的地址格式，就直接返回
			OperateResult<McAddressData> addressResult = this.plc.McAnalysisAddress( address, (ushort)sourceReadRequest.GetLength( ), false );
			if (!addressResult.IsSuccess) return string.Empty;

			// 三菱目前只支持D,W,R,ZE的批量解析写入分析操作
			if (addressResult.Content.McDataType == MelsecMcDataType.D ||
				addressResult.Content.McDataType == MelsecMcDataType.W ||
				addressResult.Content.McDataType == MelsecMcDataType.R ||
				addressResult.Content.McDataType == MelsecMcDataType.ZR)
			{
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text && this.plc.EnableWriteBitToWordRegister)
				{
					// 如果配置了针对字寄存器的bool解析位，那么就提供bool索引信息
					int bitIndex = index % 16;
					index = index / 16;
					if (byteOffset % 2 == 1) return string.Empty;

					if (address.StartsWith( "ZR" )) return address.Substring( 0, 2 ) + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" ) + $".{bitIndex}";

					return address.Substring( 0, 1 ) + (addressResult.Content.McDataType == MelsecMcDataType.W ?
						(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" ) :
						(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( )) + $".{bitIndex}";
				}
				else
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					if (address.StartsWith( "ZR" )) return address.Substring( 0, 2 ) + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" );

					return address.Substring( 0, 1 ) + (addressResult.Content.McDataType == MelsecMcDataType.W ?
						(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" ) :
						(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( ));
				}
			}
			else
			{
				return string.Empty;
			}
		}

		// 提供几个测试的方法
		[HslMqttApi( Description = "启动设备生产" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> StartProduction( )
		{
			return await this.plc.WriteAsync( "D200", 5 );
		}

		[HslMqttApi( Description = "停止设备生产" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> StopProduction( )
		{
			return await this.plc.WriteAsync( "D200", 6 );
		}

		[HslMqttApi( Description = "读取速度信息" )]
		[RpcExtensionInfo( DataType = DataType.Int16 )]
		public async Task<OperateResult<short>> ReadSpeed( )
		{
			return await this.plc.ReadInt16Async( "D200" );
		}

		[HslMqttApi( Description = "下载配方操作\r\ncode: 配方代码\r\n配方名称" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> DownloadRecipe( ushort code, string name )
		{
			OperateResult<short> read = plc.ReadInt16( "D100" );
			if (!read.IsSuccess) return read;

			if (read.Content != 0x00) return new OperateResult( "当前不允许下发配方" );

			return await this.plc.WriteAsync( "D200", new short[] { 1, 2, 3, 4, 5 } );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[DeviceVulcanizer] [{GetDeviceNameWithPath( )}] [{plc.IpAddress}:{plc.Port}]";

		#endregion

		#region Private

		private MelsecMcNet plc;                // 核心交互对象
		private VulcanizerNode node;            // 节点信息

		#endregion

	}
}
