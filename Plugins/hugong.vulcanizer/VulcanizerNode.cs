using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Xml.Linq;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Node;

namespace hugong.vulcanizer
{
	/// <summary>
	/// 硫化的节点信息，假设就只需要输入IP,端口，设备编号
	/// </summary>
	public class VulcanizerNode : DeviceNodeNet // 从网络设备继承，就有默认的IP，端口，连接超时属性了
	{
		public VulcanizerNode( )
		{
			// 标识基于插件的设备信息
			DeviceType = SoftBasic.GetEnumFromString<DeviceType>( "Plugins" ); // 不直接赋值，使用强转间接赋值，可以在网关升级的时候，提供更强的兼容性
			DeviceNumber = "100";
			PluginsType = "hugong.vulcanizer.VulcanizerNode";   // 唯一的插件设备标识，一般来说需要携带 公司名.设备分类.XXX设备节点
			Name = "硫化机设备";   // 给一个默认的名称
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public VulcanizerNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 设备编号信息
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "设备编号" )]
		[DefaultValue( "100" )]
		[Description( "设备的编号信息" )]
		public string DeviceNumber { get; set; }

		[Category( "设备信息" )]
		[DisplayName( "设备型号" )]
		[DefaultValue( typeof( DeviceModle ), "A" )]
		[Description( "设备型号信息，每个型号不一样" )]
		public DeviceModle Modle { get; set; } = DeviceModle.A;

		#region Xml Interface

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			DeviceNumber = GetXmlValue( element, nameof( DeviceNumber ), DeviceNumber, m => m );
			Modle        = GetXmlEnum( element, nameof( Modle ), Modle );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( DeviceNumber ), DeviceNumber );
			element.SetAttributeValue( nameof( Modle ),        Modle );
			return element;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return HslTechnology.Edge.Node.Device.Melsec.NodeMelsecHelper.GetMcAddress( ); // 在PLC地址示例里，使用相关的
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"VulcanizerNode[{Name}]";

		#endregion
	}

	/// <summary>
	/// 设备的型号信息
	/// </summary>
	public enum DeviceModle
	{
		A,
		B,
		C
	}
}
