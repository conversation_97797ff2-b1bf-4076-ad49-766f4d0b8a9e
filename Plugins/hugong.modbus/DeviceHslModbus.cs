using hugong.modbus.Helper;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace hugong.modbus
{
	public class DeviceHslModbus
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceHslModbus( )
		{
			this.byteTransform = new RegularByteTransform( DataFormat.CDAB );
		}

		/// <summary>
		/// 初始化的内容
		/// </summary>
		/// <param name="element">初始化的XML配置信息</param>
		public void IniDevice( XElement element )
		{
			// 初始化的代码写真实的通信对象
			this.node = new HslModbusNode( element );

			// 连接设备的ip及端口
			try
			{
				this.socket = new Socket( AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp );
				this.socket.Connect( new IPEndPoint( IPAddress.Parse( node.IpAddress ), node.Port ) );
			}
			catch
			{

			}
		}

		/// <summary>
		/// 如果需要修改原始字节请求的时候，解析操作的时候，关联的数据规则信息，可以定义这个方法返回
		/// </summary>
		/// <returns>返回 ABCD, BADC, CDAB, DCBA</returns>
		public string GetDataFormat( )
		{
			return "CDAB";
		}

		//public override void AnalysisByBussiness( BusinessEngine business )
		//{
		//	// 继续进行商业逻辑的解析操作

		//	// 此处新增一个数据标签信息
		//	scalarCacheRequest1 = new ScalarCacheRequest( )
		//	{
		//		Name = "AAA",
		//		DataTypeCode = RegularNodeTypeItem.Int16.Text,
		//		Unit = "℃",
		//		Description = "温度值",
		//		ForbidRemoteWrite = true,
		//	};
		//	this.Requests.Add( scalarCacheRequest1 );

		//	ScalarReadRequest readRequest = new ScalarReadRequest( )
		//	{
		//		Name = "BBB",
		//		Address = "100",
		//		DataTypeCode = RegularNodeTypeItem.Int16.Text,
		//		Unit = "℃",
		//		Description = "湿度值",
		//	};
		//	this.Requests.Add( readRequest );


		//	base.AnalysisByBussiness( business );
		//}

		#endregion

		#region Command Build

		private byte[] BuildReadCommand( string address, int length )
		{
			ushort add = ushort.Parse( address );
			byte[] cmd = new byte[12];
			cmd[0] = 0x00;
			cmd[1] = 0x00;
			cmd[2] = 0x00;
			cmd[3] = 0x00;
			cmd[4] = 0x00;
			cmd[5] = 0x06;
			cmd[6] = 0x01;
			cmd[7] = 0x03;
			cmd[8] = BitConverter.GetBytes( add )[1];
			cmd[9] = BitConverter.GetBytes( add )[0];
			if (length < 0)
			{
				cmd[10] = 0x00;
				cmd[11] = 0x01;
			}
			else
			{
				cmd[10] = BitConverter.GetBytes( length )[1];
				cmd[11] = BitConverter.GetBytes( length )[0];
			}
			return cmd;
		}


		public byte[] BuildWriteCommand( string address, byte[] data )
		{
			ushort add = ushort.Parse( address );
			byte[] cmd = new byte[13 + data.Length];
			cmd[5] = (byte)(cmd.Length - 6);
			cmd[6] = 0x01;
			cmd[7] = 0x10;
			cmd[8] = BitConverter.GetBytes( add )[1];
			cmd[9] = BitConverter.GetBytes( add )[0];
			cmd[10] = BitConverter.GetBytes( data.Length / 2 )[1];
			cmd[11] = BitConverter.GetBytes( data.Length / 2 )[0];
			cmd[12] = (byte)data.Length;
			data.CopyTo( cmd, 13 );

			return cmd;
		}

		#endregion

		#region Private Member

		private byte[] readMessage( byte[] data )
		{
			try
			{
				LoggerTelegram?.Invoke( "发", data, true );  // 记录发送电文

				socket.Send( data );
				byte[] buffer = new byte[1024];
				int len = socket.Receive( buffer );
				byte[] recv = SelectBegin( buffer, len );

				LoggerTelegram?.Invoke( "收", recv, true );   // 记录接收电文
				return recv;
			}
			catch (Exception ex)
			{
				// 重新连接
				try
				{
					this.socket = new Socket( AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp );
					this.socket.Connect( new IPEndPoint( IPAddress.Parse( node.IpAddress ), node.Port ) );
				}
				catch
				{

				}

				throw new Exception( "read failed: " + ex.Message );
			}
		}
		private byte[] SelectBegin( byte[] data, int length )
		{
			byte[] result = new byte[length];
			Array.Copy( data, result, length );
			return result;
		}
		private byte[] RemoveBegin( byte[] data, int length )
		{
			byte[] result = new byte[data.Length - length];
			Array.Copy( data, length, result, 0, result.Length );
			return result;
		}

		#endregion

		public async Task<byte[]> Read( string address, int length )
		{
			byte[] cmd = BuildReadCommand( address, length );
			byte[] read = readMessage( cmd );
			// 发送 00 00 00 00 00 06 01 03 00 00 00 01
			// 接收 00 00 00 00 00 05 01 03 02 04 D2
			if (read[8] + 9 == read.Length)
			{
				// 长度符合预期，接下来判断
				return await Task.FromResult( RemoveBegin( read, 9 ) );
			}
			throw new Exception( "接收数据不足！" + read.Length );
		}

		public async Task<short> ReadInt16( string address )
		{
			byte[] read = await Read( address, 1 );
			return this.byteTransform.TransInt16( read, 0 );
		}

		public async Task<short[]> ReadInt16( string address, int length )
		{
			byte[] read = await Read( address, length );
			return this.byteTransform.TransInt16( read, 0, length );
		}

		public async Task<ushort> ReadUInt16( string address )
		{
			byte[] read = await Read( address, 1 );
			return this.byteTransform.TransUInt16( read, 0 );
		}

		public async Task<ushort[]> ReadUInt16( string address, int length )
		{
			byte[] read = await Read( address, length );
			return this.byteTransform.TransUInt16( read, 0, length );
		}

		public async Task<int> ReadInt32( string address )
		{
			byte[] read = await Read( address, 2 );
			return this.byteTransform.TransInt32( read, 0 );
		}

		public async Task<int[]> ReadInt32( string address, int length )
		{
			byte[] read = await Read( address, length * 2 );
			return this.byteTransform.TransInt32( read, 0, length );
		}

		public async Task<uint> ReadUInt32( string address )
		{
			byte[] read = await Read( address, 2 );
			return this.byteTransform.TransUInt32( read, 0 );
		}

		public async Task<uint[]> ReadUInt32( string address, int length )
		{
			byte[] read = await Read( address, 2 * length);
			return this.byteTransform.TransUInt32( read, 0, length );
		}

		public async Task<long> ReadInt64( string address )
		{
			byte[] read = await Read( address, 4 );
			return this.byteTransform.TransInt64( read, 0 );
		}

		public async Task<long[]> ReadInt64( string address, int length )
		{
			byte[] read = await Read( address, 4 * length);
			return this.byteTransform.TransInt64( read, 0, length );
		}

		public async Task<float> ReadFloat( string address )
		{
			byte[] read = await Read( address, 2 );
			return this.byteTransform.TransSingle( read, 0 );
		}

		public async Task<float[]> ReadFloat( string address, int length )
		{
			byte[] read = await Read( address, 2 * length);
			return this.byteTransform.TransSingle( read, 0, length );
		}

        public async Task<double> ReadDouble( string address )
        {
            byte[] read = await Read( address, 4 );
            return this.byteTransform.TransDouble( read, 0 );
        }

        public async Task<double[]> ReadDouble( string address, int length )
        {
            byte[] read = await Read( address, 4 * length );
            return this.byteTransform.TransDouble( read, 0, length );
        }


		public async Task<int> WriteBool( string address, bool value )
		{
			return 1;
		}

        public async Task<int> Write( string address, byte[] data )
		{
			byte[] cmd = BuildWriteCommand( address, data );
			byte[] read = readMessage( cmd );
			return await Task.FromResult( 1 );
		}

		public async Task<int> WriteInt16( string address, short value )
		{
			return await Write( address, this.byteTransform.TransByte( value ) );
		}

		public async Task<int> WriteInt16( string address, short[] value )
		{
			return await Write( address, this.byteTransform.TransByte( value ) );
		}

		public async Task<int> WriteUInt16( string address, ushort value )
		{
			return await Write( address, this.byteTransform.TransByte( value ) );
		}

		public async Task<int> WriteUInt16( string address, ushort[] value )
		{
			return await Write( address, this.byteTransform.TransByte( value ) );
		}

		public async Task<int> WriteInt32(  string address, int value )
		{
			return await Write( address, this.byteTransform.TransByte( value ) );
		}

		public async Task<int> WriteInt32( string address, int[] value )
		{
			return await Write( address, this.byteTransform.TransByte( value ) );
		}

		public async Task<int> WriteFloat( string address, float value )
		{
			return await Write( address, this.byteTransform.TransByte( value ) );
		}

		public async Task<int> WriteFloat( string address, float[] value )
		{
			return await Write( address, this.byteTransform.TransByte( value ) );
		}

		#region BuildIn Request

		/// <summary>
		/// 如果设备启动的时候需要支持内置的一些请求信息，用户不需要配置就存在的数据
		/// </summary>
		/// <returns></returns>
		public XElement BuildInRequestRescources( )
		{
			return XElement.Parse(
@"
<DeviceNode>
  <RequestNode Name=""Tick"" Description=""每秒自增的变量数据"" RequestInterval=""1000"" Address="""" RequestType=""ScalarCache"" DataTypeCode=""int"" />
</DeviceNode>
" );
		}

		#endregion

		#region Record Telegrame

		/// <summary>
		/// 这是一个由外界实现的记录报文日志的功能方法，此处可以调用该方法，LoggerTelegram( "发", byte[], true ); // 参数分别是关键字，报文对象，是否二进制(false 就是ascii格式记录)
		/// </summary>
		public Action<string, byte[], bool> LoggerTelegram { get; set; }

		/// <summary>
		/// 这是一个由外界实现的写数据到设备对象里的方法，此处可以调用该方法实现特殊的操作，SetJsonValue( "A", 1.23f, false ); // 参数分别为数据名，数据对象，是否数组，数组则为true
		/// </summary>
		public Action<string, object, bool> SetJsonValue { get; set; }

		#endregion

		#region Access Control

		/// <summary>
		/// 编写这个方法针对标量请求时，传入地址及读取类型时，返回是否只读，或是可读写的结果，例如 Read, ReadWrite
		/// </summary>
		/// <param name="address">地址数据信息</param>
		/// <param name="dataType">类型，例如 short,ushort,int,uint,float</param>
		/// <returns>只读或是可读写 Read, ReadWrite</returns>
		public string GetAccessLevelFromRequest( string address, string dataType )
		{
			return "ReadWrite";
		}

		/// <summary>
		/// 编写这个方法针对原始数据请求时，用户配置了解析节点后，怎么根据配置信息和初始地址，反解出实际的解析节点对应的物理地址，例如起始地址100，偏移2个字节，实际地址为 101
		/// </summary>
		/// <param name="address">请求的首地址</param>
		/// <param name="byteOffset">起始的偏移地址，如果是结构体，就是结构体的起始偏移字节，按照字节为单位</param>
		/// <param name="index">起始索引位置，如果是bool就是位索引，否则就是字节索引</param>
		/// <param name="dataType">类型，例如 short,ushort,int,uint,float</param>
		/// <returns>返回真实的地址，如果只读操作，返回空字符串</returns>
		public string CalculatePhysicalAddressFromSourceReadRequest( string address, int byteOffset, int index, string dataType )
		{
			// 地址偏移量为奇数的也不能写入
			if (byteOffset % 2 == 1) return string.Empty;
			if (index % 2 == 1) return string.Empty;

			// 这里就简单处理一下，实际逻辑非常复杂
			return (int.Parse( address ) + (byteOffset + index) / 2).ToString( );
		}

		#endregion

		#region Every Seconds

		private int tick = 0;

		public async Task EverySecondsExecuteMethod( int second )
		{
			SetJsonValue?.Invoke( "Tick", System.Threading.Interlocked.Increment( ref tick ), false );
			await Task.FromResult( 0 );
		}

		public async Task EveryMinuteExecuteMethod( int minute )
		{
			await Task.FromResult( 0 );
		}

		public async Task EveryHourExecuteMethod( int hour )
		{
			await Task.FromResult( 0 );
		}

		public async Task EveryDayExecuteMethod( int day )
		{
			await Task.FromResult( 0 );
		}

		#endregion



		public override string ToString( )
		{
			return $"HslModbus[{node.IpAddress}:{node.Port}]";
		}

		private HslModbusNode node = null;
		private Socket socket = null;
		private IByteTransform byteTransform = null;
	}
}
