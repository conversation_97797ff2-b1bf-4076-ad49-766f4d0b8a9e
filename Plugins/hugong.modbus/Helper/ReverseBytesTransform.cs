using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

/**********************************************************************************************
 * 
 *    说明：一般的转换类
 *    日期：2018年3月14日 17:05:30
 * 
 **********************************************************************************************/

namespace hugong.modbus.Helper
{
	/// <summary>
	/// 大端顺序的字节的转换类，字节的顺序和C#的原生字节的顺序是完全相反的，高字节在前，低字节在后。<br />
	/// In the reverse byte order conversion class, the byte order is completely opposite to the native byte order of C#, 
	/// with the high byte first and the low byte following.
	/// </summary>
	/// <remarks>
	/// 适用西门子PLC的S7协议的数据转换
	/// </remarks>
	public class ReverseBytesTransform : RegularByteTransform
	{
		#region Constructor

		/// <inheritdoc cref="RegularByteTransform( )"/>
		public ReverseBytesTransform( ) 
		{
			DataFormat = DataFormat.ABCD;
		}

		/// <inheritdoc cref="RegularByteTransform(DataFormat)"/>
		public ReverseBytesTransform( DataFormat dataFormat ) : base( dataFormat ) { }

		#endregion

		/// <inheritdoc cref="IByteTransform.CreateByDateFormat(DataFormat)"/>
		public override IByteTransform CreateByDateFormat( DataFormat dataFormat ) => new ReverseBytesTransform( dataFormat ) { IsStringReverseByteWord = this.IsStringReverseByteWord };

		#region Object Override

		///<inheritdoc/>
		public override string ToString( ) => $"ReverseBytesTransform[{DataFormat}]";

		#endregion
	}
}
