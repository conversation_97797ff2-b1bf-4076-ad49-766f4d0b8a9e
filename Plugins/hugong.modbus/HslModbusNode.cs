using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Text;
using System.Xml.Linq;

namespace hugong.modbus
{
	public class HslModbusNode
	{

		public HslModbusNode( )
		{
			//PluginsType = "hugong.hslmodbus";   // 唯一的插件设备标识，一般来说需要携带 公司名.设备分类.XXX设备节点
			//Name = "自定义Modbus设备";   // 给一个默认的名称
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public HslModbusNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		[Category( "设备信息" )]
		[DisplayName( "站号" )]
		[DefaultValue( "1" )]
		[Description( "设备的站号信息" )]
		public byte Station { get; set; } = 1;


		[Category( "设备信息" )]
		[DisplayName( "IP地址" )]
		[DefaultValue( "127.0.0.1" )]
		[Description( "设备的ip信息" )]
		public string IpAddress
		{
			get;
			set;
		} = "127.0.0.1";

		[Category( "设备信息" )]
		[DisplayName( "端口号信息" )]
		[DefaultValue( 502 )]
		[Description( "设备的端口信息" )]
		public int Port { get; set; } = 502;

		public string PluginsType { get; set; } = "hugong.hslmodbus";

		#region Xml Interface

		/// <summary>
		/// 是否支持输入地址的请求操作，如果确定插件只能以方法调用接口，则直接返回 false
		/// </summary>
		/// <returns></returns>
		public bool IsSupportAddressRequest( ) => true;

		/// <summary>
		/// 获取每个地址占用的字节长度，方便配置原始字节请求的时候，清晰的定位长度返回的字节长度数据，如果为不定长，返回 -1
		/// </summary>
		/// <param name="address"></param>
		/// <returns></returns>
		public int GetEveryAddressOccupyByte( string address )
		{
			return 2;
		}

		/// <inheritdoc/>
		public void LoadByXmlElement( XElement element )
		{
			Station    = GetXmlValue( element, nameof( Station ),     Station,   byte.Parse );
			IpAddress  = GetXmlValue( element, nameof( IpAddress ),   IpAddress, m => m );
			Port       = GetXmlValue( element, nameof( Port ),        Port,      int.Parse );
		}

		/// <inheritdoc/>
		public XElement ToXmlElement( XElement element )
		{
			element.SetAttributeValue( nameof( Station ),  Station );
			element.SetAttributeValue( nameof( IpAddress), IpAddress );
			element.SetAttributeValue( nameof( Port ),     Port );
			return element;
		}

		/// <inheritdoc/>
		//public override DeviceAddressExample[] GetDeviceAddressExamples( )
		//{
		//	return HslTechnology.Edge.Node.Device.Melsec.NodeMelsecHelper.GetMcAddress( ); // 在PLC地址示例里，使用相关的
		//}

		#endregion

		#region Address

		/// <summary>
		/// 如果需要支持示例地址，就写这个方法，返回地址数组信息
		/// </summary>
		/// <returns></returns>
		public List<object> GetDeviceAddressExamples( )
		{
			return new List<object>( )
			{
				new
				{
					AddressExample = "100",                  // 地址的一个示例
					AddressType = "保持型寄存器地址",          // 地址的类型描述，例如保持型寄存器，D寄存器
					BitEnable = false,                      // 该地址是否支持位读取
					WordEnable = true,                      // 该地址是否支持字读取
					Mark = "使用03功能码读，10功能码写",      // 备注信息，可以用来备注地址范围
				},
				new
				{
					AddressExample = "200",                  // 地址的一个示例
					AddressType = "保持型寄存器地址",          // 地址的类型描述，例如保持型寄存器，D寄存器
					BitEnable = false,                      // 该地址是否支持位读取
					WordEnable = true,                      // 该地址是否支持字读取
					Mark = "使用03功能码读，10功能码写",      // 备注信息，可以用来备注地址范围
				},
			};
		}
		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"HslModbusNode";

		#endregion



		#region Static

		/// <summary>
		/// 从XElement中提取相关的属性信息，如果不存在，就返回默认值
		/// </summary>
		/// <typeparam name="T">最终的类型信息</typeparam>
		/// <param name="element">元素内容</param>
		/// <param name="name">属性的名称</param>
		/// <param name="defaultValue">默认提供的值</param>
		/// <param name="trans">转换方式</param>
		/// <returns>最终的值</returns>
		public static T GetXmlValue<T>( XElement element, string name, T defaultValue, Func<string, T> trans )
		{
			XAttribute attribute = element.Attribute( name );
			if (attribute == null) return defaultValue;

			try
			{
				return trans( attribute.Value );
			}
			catch
			{
				return defaultValue;
			}
		}

		/// <inheritdoc cref="GetXmlValue{T}(XElement, string, T, Func{string, T})"/>
		public static T GetXmlEnum<T>( XElement element, string name, T defaultValue ) where T : struct
		{
			XAttribute attribute = element.Attribute( name );
			if (attribute == null) return defaultValue;

			try
			{
				return (T)Enum.Parse( typeof( T ), attribute.Value );
			}
			catch
			{
				return defaultValue;
			}
		}

		#endregion
	}
}
