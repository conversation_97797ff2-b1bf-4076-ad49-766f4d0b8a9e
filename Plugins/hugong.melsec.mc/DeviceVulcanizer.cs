using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Dynamic;

namespace hugong.melsec.mc
{
	public class DeviceVulcanizer
	{

		/// <summary>
		/// 初始化的内容
		/// </summary>
		/// <param name="element"></param>
		public void IniDevice( XElement element )
		{
			// 初始化的代码写真实的通信对象

		}

		private dynamic device = new ExpandoObject( );
		public void SetDevice( dynamic device )
		{
			this.device = device;
		}

		#region BuildIn Request

		/// <summary>
		/// 如果设备启动的时候需要支持内置的一些请求信息，用户不需要配置就存在的数据
		/// </summary>
		/// <returns></returns>
		public XElement BuildInRequestRescources( )
		{
			return XElement.Parse(
@"
<DeviceNode>
  <RequestNode Name=""Tick"" Description=""每秒自增的变量数据"" RequestInterval=""1000"" Address="""" RequestType=""ScalarCache"" DataTypeCode=""int"" />
</DeviceNode>
" );
		}

		#endregion


		#region Interface Method

		/// <summary>
		/// 如果有公开的方法接口，这里需要创建一个 ApiMethods 属性，列举需要公开方法的名称
		/// </summary>
		public string[] ApiMethods
		{
			get
			{
				return new string[] { "StartDevice", "StopDevice" };
			}
		}

		[Description( "启动一个设备" )]
		public int StartDevice( int mode )
		{
			var result = device.ReadInt16( "D100" );
			if (result.IsSuccess == false) throw new Exception( result.Message );

			if (result.Content == 100) return 0;
			return (int)result.Content;
		}

		[Description( "停止一个设备" )]
		public Task<bool> StopDevice( int mode )
		{
			dynamic result = device.Write( "D200", (short)1234 );
			return Task.FromResult( (bool)result.IsSuccess );
		}



		#endregion


		#region Every Seconds

		private int tick = 0;

		/// <summary>
		/// 这是一个由外界实现的写数据到设备对象里的方法，此处可以调用该方法实现特殊的操作，SetJsonValue( "A", 1.23f, false ); // 参数分别为数据名，数据对象，是否数组，数组则为true
		/// </summary>
		public Action<string, object, bool> SetJsonValue { get; set; }



		public async Task EverySecondsExecuteMethod( int second )
		{
			SetJsonValue?.Invoke( "Tick", System.Threading.Interlocked.Increment( ref tick ), false );
			await Task.FromResult( 0 );
		}

		public async Task EveryMinuteExecuteMethod( int minute )
		{
			await Task.FromResult( 0 );
		}

		public async Task EveryHourExecuteMethod( int hour )
		{
			await Task.FromResult( 0 );
		}

		public async Task EveryDayExecuteMethod( int day )
		{
			await Task.FromResult( 0 );
		}

		#endregion
	}
}
