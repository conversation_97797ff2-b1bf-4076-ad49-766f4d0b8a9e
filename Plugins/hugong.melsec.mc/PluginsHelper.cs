using System;
using System.Collections.Generic;
using System.IO;

namespace hugong.melsec.mc
{
	public class PluginsHelper
	{

		/// <summary>
		/// 公司的名称信息
		/// </summary>
		public static string Company { get; set; } = "福建省天正信息资讯工程有限公司";

		/// <summary>
		/// 插件的网址信息
		/// </summary>
		public static string Http { get; set; } = "Http://www.hslcommunication.cn";

		/// <summary>
		/// 当前的插件的版本
		/// </summary>
		public static string Version { get; set; } = "1.0.0";

		/// <summary>
		/// 当前公司的基本介绍信息
		/// </summary>
		public static string Description { get; set; } = "天正信息";

		/// <summary>
		/// 当前插件的框架信息
		/// </summary>
#if NETSTANDARD2_0_OR_GREATER
		public static string Framework { get; set; } = "standard2.1";
#else
		public static string Framework { get; set; } = "net462";
#endif


		/// <summary>
		/// 获取或设置当前系统的语言信息，0：中文，1：英文，2：法语，3，俄语，4：阿拉伯语，5：西班牙语，6：日语<br />
		/// Get or set the language information of the current system, 0: Chinese, 1: English, 2: French, 3, Russian, 4: Arabic, 5: Spanish, 6: Japanese
		/// </summary>
		/// <remarks>
		/// 这个值将会由网关进行设置，传递给插件，插件在赋值数据及注释时，根据语言信息来完善当前的内容<br />
		/// This value will be set by the gateway and passed to the plug-in. The plug-in will complete the current content according to the language information when assigning data and comments.
		/// </remarks>
		public static int Language { get; set; }

		/// <summary>
		/// 当前版本插件的发布日期<br />
		/// The release date of the current version of the plugin
		/// </summary>
		public static DateTime ReleaseDate { get; set; } = new DateTime( 2023, 5, 19 );

		/// <summary>
		/// 当前的品牌的自定义的图标信息，需要为一个16*16大小的png,jpg格式的图片的内容
		/// </summary>
		public static byte[] Icon16 { get; set; } = StringToHexBytes( @"89 50 4E 47 0D 0A 1A 0A 00 00 00 0D 49 48 44 52 00 00 00 10 00 00 00 10 08 02 00 00 00 90 91 68 
36 00 00 00 01 73 52 47 42 00 AE CE 1C E9 00 00 00 04 67 41 4D 41 00 00 B1 8F 0B FC 61 05 00 00 
00 09 70 48 59 73 00 00 0E C3 00 00 0E C3 01 C7 6F A8 64 00 00 01 B7 49 44 41 54 38 4F 8D 52 4D 
28 44 51 18 FD EE BD 6F 3C 34 94 21 46 28 43 6C 14 22 C6 42 C9 4F 29 6C 64 41 36 8C 30 B2 C6 52 
59 4C 16 16 4A 84 44 16 A8 59 28 1B 84 95 9F 85 44 B1 B1 B0 90 2C A5 86 26 CD EF 7B D7 77 DF 7D 
CF A0 49 4E DF E2 DC 73 DF B9 DF B9 DF 7D 64 BA 2E C2 81 00 70 10 40 82 F8 83 13 0A 60 4B CB 44 
A6 10 B0 11 50 B2 F2 51 37 79 76 01 71 14 52 47 21 21 40 71 29 45 34 80 BB 97 94 D6 C7 8D 33 A0 
7B 86 13 AA 49 DE E3 D3 5A BD BC D5 0B AE 5A 29 08 08 83 00 C1 26 18 4D 06 30 81 AB A3 05 2C FA 
FA 8C 99 78 5E 79 14 45 45 EE 75 4E 92 50 40 47 39 A7 18 15 D9 81 3F DD B0 8E 09 1D 97 CE 72 BA 
E7 0B 37 0F AB 9B 63 96 61 7F 0E 1E AF 05 19 5A C5 63 98 A1 91 E3 45 24 82 37 8D C4 B8 BC 76 22 
12 EE 30 0A 1A D5 35 56 D5 8E 1F 89 6C 36 55 57 54 8E F5 ED 2B AB 03 22 A7 88 37 F4 6B FE 29 DE 
3E CE DC 7D DA 95 9F 06 03 F1 C6 01 F3 E4 FB 33 9C 8A 8A 24 61 90 08 BD A7 EC CD 82 9A A1 B5 0C 
83 BB 96 5E EE D0 BB 03 F4 90 24 91 0C 98 72 24 A8 1E CE 2B 2B 83 D1 5C 17 1F DD 8A 54 77 E2 D5 
4D FC 32 FC 1C 6B 3C FD 64 89 AD 79 C0 EE 0C 8F 6D 47 B9 B1 FB 23 92 B3 8C 56 76 99 2F F8 85 87 
D3 94 8B 75 7A BE 11 F6 2C 8B FE 89 0E EF 2F E4 6A 57 67 8C 1B 85 43 93 04 1F 5E E6 91 B3 96 06 
2E A4 54 3B 89 7E 80 55 FC 8B 97 D4 F1 8A B6 58 45 5B 5C C6 C5 BF 15 1F 58 57 D3 29 B0 58 4D 97 
35 8B 64 08 BD D1 DB 03 6A 18 FE 0F 80 4F 28 50 99 60 25 36 03 32 00 00 00 00 49 45 4E 44 AE 42 
60 82" );

		/// <summary>
		/// 当前插件中定义的设备信息
		/// </summary>
		/// <returns>定义的插件设备列表</returns>
		public static List<object> DeviceDefinitions( )
		{
			return new List<object>
			{
				new
				{
					DeviceName   = "硫化机设备",
					Description  = "自定义的mc协议插件信息",
					DeviceNode   = "MelsecMcQna3E",               // 指定使用网关里的什么设备类型
					DeviveObject = typeof( DeviceVulcanizer ),
					PluginsType  = "hugong.melsec.vulcanizer"     // 指定该设备关联的插件类型标识是什么
				}
			};
		}


		public static byte[] StringToHexBytes( string hex )
		{
			MemoryStream ms = new MemoryStream( );

			for (int i = 0; i < hex.Length; i++)
			{
				if ((i + 1) < hex.Length)
				{
					if (GetHexCharIndex( hex[i] ) >= 0 && GetHexCharIndex( hex[i + 1] ) >= 0)
					{
						// 这是一个合格的字节数据
						ms.WriteByte( (byte)(GetHexCharIndex( hex[i] ) * 16 + GetHexCharIndex( hex[i + 1] )) );
						i++;
					}
				}
			}

			byte[] result = ms.ToArray( );
			ms.Dispose( );
			return result;
		}


		private static int GetHexCharIndex( char ch )
		{
			switch (ch)
			{
				case '0': return 0;
				case '1': return 1;
				case '2': return 2;
				case '3': return 3;
				case '4': return 4;
				case '5': return 5;
				case '6': return 6;
				case '7': return 7;
				case '8': return 8;
				case '9': return 9;
				case 'A':
				case 'a': return 10;
				case 'B':
				case 'b': return 11;
				case 'C':
				case 'c': return 12;
				case 'D':
				case 'd': return 13;
				case 'E':
				case 'e': return 14;
				case 'F':
				case 'f': return 15;
				default: return -1;
			}
		}
	}
}
