using HslTechnology.Edge.Plugins;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Plugins
{
	public partial class PluginsDetailsControl : UserControl
	{
		public PluginsDetailsControl( )
		{
			InitializeComponent( );
		}

		private void PluginsDetailsControl_Load( object sender, EventArgs e )
		{
			linkLabel_url.MouseClick += LinkLabel_url_MouseClick;
			button_unload.Click += Button_unload_Click;
		}

		private void Button_unload_Click( object sender, EventArgs e )
		{
			if(this.pluginsDefinition!=null)
				PluginUnloadEvent?.Invoke( this.pluginsDefinition );
		}

		private void LinkLabel_url_MouseClick( object sender, MouseEventArgs e )
		{
			if (this.pluginsDefinition == null) return;
			if (string.IsNullOrEmpty( this.pluginsDefinition.Http )) return;
			try
			{
				System.Diagnostics.Process.Start( this.pluginsDefinition.Http );
			}
			catch (Exception ex)
			{
				MessageBox.Show( ex.Message );
			}
		}

		public void SetPluginsDefinition( PluginsDefinition pluginsDefinition )
		{
			this.pluginsDefinition = pluginsDefinition;
			if (this.pluginsDefinition == null) return;

			label_name.Text              = this.pluginsDefinition.DllName;
			textBox_install_version.Text = this.pluginsDefinition.Version.ToString( );
			textBox_desc.Text            = this.pluginsDefinition.Description;
			label_author.Text            = this.pluginsDefinition.Company;
			label_date.Text              = this.pluginsDefinition.ReleaseData.ToString( "yyyy-MM-dd" );
			label_version.Text           = this.pluginsDefinition.Version.ToString( );
			linkLabel_url.Text           = this.pluginsDefinition.Http;
		}


		private PluginsDefinition pluginsDefinition = null;
		public delegate void PluginOperateDelegate( PluginsDefinition pluginsDefinition );

		/// <summary>
		/// 插件卸载的事件
		/// </summary>
		public event PluginOperateDelegate PluginUnloadEvent;
	}
}
