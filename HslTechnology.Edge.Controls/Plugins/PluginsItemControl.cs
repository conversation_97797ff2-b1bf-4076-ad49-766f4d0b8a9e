using HslTechnology.Edge.Plugins;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Plugins
{
	public partial class PluginsItemControl : UserControl
	{
		public PluginsItemControl( )
		{
			InitializeComponent( );


			SetStyle( ControlStyles.UserPaint | ControlStyles.SupportsTransparentBackColor, true );
			SetStyle( ControlStyles.ResizeRedraw, true );
			SetStyle( ControlStyles.OptimizedDoubleBuffer, true );
			SetStyle( ControlStyles.AllPaintingInWmPaint, true );
		}

		public void SetPluginsDefinition( PluginsDefinition pluginsDefinition )
		{
			this.pluginsDefinition = pluginsDefinition;
			Invalidate( );
		}

		public PluginsDefinition GetPluginsDefinition( )
		{
			return this.pluginsDefinition;
		}

		protected override void OnPaint( PaintEventArgs e )
		{
			base.OnPaint( e );

			if (this.pluginsDefinition == null) return;
			int leftWidth = 30;
			int rightWidth = 50;

			Graphics g = e.Graphics;
			g.DrawImage( HslTechnologyControlHelper.GetImageFromBytes( pluginsDefinition.Icon16 ), new Rectangle( 10, 10, 16, 16 ) );
			float pluginsNameWidth = 0f;
			Brush fontBrush = new SolidBrush( ForeColor );
			using (Font font1 = new Font( Font.FontFamily, 12f, FontStyle.Bold ))
			{
				pluginsNameWidth = g.MeasureString( pluginsDefinition.DllName, font1 ).Width + 5;
				g.DrawString( pluginsDefinition.DllName, font1, fontBrush, new Point( leftWidth, 5 ) );
			}
			g.DrawString( "作者 " + pluginsDefinition.Company, Font, fontBrush, new PointF( leftWidth + pluginsNameWidth, 9 ) );
			Rectangle rectangleDesc = new Rectangle( leftWidth, 25, Width - leftWidth - rightWidth, Height - 30 );
			g.DrawString( pluginsDefinition.Description, Font, fontBrush, rectangleDesc );

			Brush back = new SolidBrush( BackColor );
			g.FillRectangle( back, new RectangleF( Width - rightWidth, 0, rightWidth, Height ) );
			g.DrawString( pluginsDefinition.Version.ToString( ), Font, fontBrush, new PointF( Width - rightWidth + 5, 10 ) );

			g.DrawRectangle( Pens.DimGray, new Rectangle( 0, 0, Width - 1, Height - 1 ) );
			back.Dispose( );
			fontBrush.Dispose( );
		}


		private PluginsDefinition pluginsDefinition;
	}
}
