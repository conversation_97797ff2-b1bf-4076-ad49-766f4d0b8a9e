using HslControls;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls
{
	[DefaultEvent( "Click" )]
	public class ButtonOk : UserControl
	{
		/// <summary>
		/// 实例化一个徽章的对象
		/// </summary>
		public ButtonOk( )
		{
			InitializeComponent( );

			format_center = new StringFormat( );
			format_center.Alignment = StringAlignment.Center;
			format_center.LineAlignment = StringAlignment.Center;

			SetStyle( ControlStyles.UserPaint | ControlStyles.SupportsTransparentBackColor, true );
			SetStyle( ControlStyles.ResizeRedraw, true );
			SetStyle( ControlStyles.OptimizedDoubleBuffer, true );
			SetStyle( ControlStyles.AllPaintingInWmPaint, true );

			ForeColor = Color.White;

			this.rightText = "确认";
			this.rightBackground = System.Drawing.Color.SeaGreen;
			AutoSize = false;
			Text = "";
			this.useButtonHoverStyle = true;
		}


		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.SuspendLayout( );
			// 
			// HslBadge
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.Transparent;
			this.Font = new System.Drawing.Font( "微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)) );
			this.Name = "HslBadge";
			this.Size = new System.Drawing.Size( 121, 36 );
			this.ResumeLayout( false );

		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 获取或设置控件的背景色
		/// </summary>
		[Browsable( true )]
		[Description( "获取或设置控件的背景色" )]
		[Category( "HslControls" )]
		[DefaultValue( typeof( Color ), "Transparent" )]
		[EditorBrowsable( EditorBrowsableState.Always )]
		public override Color BackColor
		{
			get => base.BackColor;
			set => base.BackColor = value;
		}

		/// <summary>
		/// 获取或设置当前控件的大小是否自动
		/// </summary>
		[Browsable( true )]
		[Description( "获取或设置当前控件的大小是否自动" )]
		[Category( "HslControls" )]
		[EditorBrowsable( EditorBrowsableState.Always )]
		[DefaultValue( false )]
		public override bool AutoSize
		{
			get => base.AutoSize;
			set => base.AutoSize = value;
		}

		/// <summary>
		/// 获取或设置当前控件的文本
		/// </summary>
		[Browsable( true )]
		[Description( "获取或设置当前控件的文本" )]
		[Category( "HslControls" )]
		[EditorBrowsable( EditorBrowsableState.Always )]
		[Bindable( true )]
		[DesignerSerializationVisibility( DesignerSerializationVisibility.Visible )]
		public override string Text
		{
			get
			{
				return base.Text;
			}
			set
			{
				base.Text = value;
				Invalidate( );
			}
		}

		/// <summary>
		/// 获取或设置控件的前景色
		/// </summary>
		[Browsable( true )]
		[Description( "获取或设置控件的前景色" )]
		[Category( "HslControls" )]
		[DefaultValue( typeof( Color ), "White" )]
		[EditorBrowsable( EditorBrowsableState.Always )]
		public override Color ForeColor
		{
			get => base.ForeColor;
			set => base.ForeColor = value;
		}

		#endregion

		#region Override Method

		/// <inheritdoc/>
		protected override void OnMouseEnter( EventArgs e )
		{
			base.OnMouseEnter( e );
			if (useButtonHoverStyle)
			{
				isMouseOver = true;
				Invalidate( );
			}
		}

		/// <inheritdoc/>
		protected override void OnMouseLeave( EventArgs e )
		{
			base.OnMouseLeave( e );
			if (useButtonHoverStyle)
			{
				isMouseOver = false;
				Invalidate( );
			}
		}

		/// <inheritdoc/>
		protected override void OnPaint( PaintEventArgs e )
		{
			Graphics g = e.Graphics;
			g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
			g.PixelOffsetMode = pixelOffsetMode;
			g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

			PaintHslControls( g, Width, Height );

			base.OnPaint( e );
		}

		/// <inheritdoc cref="HslArrow.PaintHslControls(Graphics, float, float)"/>
		public void PaintHslControls( Graphics g, int width, int height )
		{
			// 先计算应该的控件长度信息
			int leftWidth = 0;
			int rightWidth = 0;
			if(false) // (!string.IsNullOrEmpty( Text ))
			{
				leftWidth += (int)g.MeasureString( Text, Font ).Width;
				leftWidth += spaceWidth * 2;
			}
			if (!string.IsNullOrEmpty( rightText ))
			{
				rightWidth += (int)g.MeasureString( rightText, Font ).Width;
				rightWidth += spaceWidth * 2;
			}

			int widthCalcu = leftWidth + rightWidth;
			if (widthCalcu < 3) widthCalcu = 3;

			if (AutoSize)
			{
				if (widthCalcu != width)
				{
					Width = widthCalcu;
					return;
				}
			}

			GraphicsPath path = new GraphicsPath( );
			if (leftWidth > 0)
			{
				if (rightWidth > 0)
				{
					path = HslHelper.GetRoundRectange( new Rectangle( 0, 0, leftWidth + 1, height ), roundCorner, true, false, false, true );
				}
				else
				{
					path = HslHelper.GetRoundRectange( new Rectangle( 0, 0, leftWidth, height ), roundCorner, true, true, true, true );
				}
				using (LinearGradientBrush leftBrush = new LinearGradientBrush( new PointF( 0, height - 1 ), new PointF( 0, 0 ), leftBackground, HslHelper.GetColorOffset( leftBackground, height - 1 ) ))
					g.FillPath( leftBrush, path );
				using (Brush foreBrush = new SolidBrush( ForeColor ))
					g.DrawString( Text, Font, foreBrush, new Rectangle( 0, 0, leftWidth + 1, height - 1 ), format_center );
			}

			path.Dispose( );
			if (rightWidth > 0)
			{
				if (leftWidth > 0)
				{
					path = HslHelper.GetRoundRectange( new Rectangle( leftWidth, 0, width - leftWidth, height ), roundCorner, false, true, true, false );
				}
				else
				{
					path = HslHelper.GetRoundRectange( new Rectangle( leftWidth, 0, width - leftWidth, height ), roundCorner, true, true, true, true );
				}

				Color paintColor = (useButtonHoverStyle && isMouseOver) ? HslHelper.GetColorOffset( rightBackground, 20 ) : rightBackground;
				using (LinearGradientBrush rightBrush = new LinearGradientBrush( new PointF( 0, height - 1 ), new PointF( 0, 0 ), paintColor, HslHelper.GetColorOffset( paintColor, height - 1 ) ))
					g.FillPath( rightBrush, path );
				using (Brush foreBrush = new SolidBrush( rightForeground ))
					g.DrawString( rightText, Font, foreBrush, new Rectangle( leftWidth, 0, width - leftWidth - 1, height - 1 ), format_center );
			}
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 触发一次点击的事件
		/// </summary>
		public void PerformClick( )
		{
			OnClick( new EventArgs( ) );
		}

		#endregion



		#region Private Member

		protected StringFormat format_center = null;              // 中间对齐的文本
		protected string rightText = "v1.0.0";
		protected int spaceWidth = 3;
		protected int roundCorner = 3;
		
		protected Color leftBackground = Color.FromArgb( 79, 79, 79 );
		protected Color rightBackground = Color.FromArgb( 2, 115, 179 );
		protected Color rightForeground = Color.White;
		protected PixelOffsetMode pixelOffsetMode = PixelOffsetMode.None;
		protected bool isMouseOver = false;
		protected bool useButtonHoverStyle = false;

		#endregion
	}
}
