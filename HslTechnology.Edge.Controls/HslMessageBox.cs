using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls
{
	public partial class HslMessageBox : HslForm
	{
		public HslMessageBox( string text )
		{
			InitializeComponent( );
			label1.Text = text;
		}

		private void button1_Click( object sender, EventArgs e )
		{
			DialogResult = DialogResult.OK;
		}

		private void HslMessageBox_Load( object sender, EventArgs e )
		{

		}


		public static void Show(string message)
		{
			using (HslMessageBox messageBox = new HslMessageBox( message ))
				messageBox.ShowDialog( );
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1.PerformClick( );
		}

		private void buttonCancel1_Click( object sender, EventArgs e )
		{
			button2.PerformClick( );
		}
	}
}
