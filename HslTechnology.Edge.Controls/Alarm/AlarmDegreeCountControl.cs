using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.Node.Alarm;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Alarm
{
	public partial class AlarmDegreeCountControl : UserControl
	{
		public AlarmDegreeCountControl( )
		{
			InitializeComponent( );
		}

		public void RenderAlarmCountByList( List<AlarmItem> list )
		{
			int hintCount = 0;
			int warnCount = 0;
			int errorCount = 0;
			int fatalCount = 0;
			for (int i = 0; i < list.Count; i++)
			{
				if (list[i].Degree == AlarmDegree.Hint) hintCount++;
				else if (list[i].Degree == AlarmDegree.Warn) warnCount++;
				else if (list[i].Degree == AlarmDegree.Error) errorCount++;
				else fatalCount++;
			}
			label_hint.Text = "      " + hintCount.ToString( );
			label_warn.Text = "      " + warnCount.ToString( );
			label_error.Text = "      " + errorCount.ToString( );
			label_fatal.Text = "      " + fatalCount.ToString( );
		}

		public void RenderAlarmCountByDevice( JObject json )
		{
			JObject jsonAlarm = (JObject)json["__alarmCount"];

			int hintCount = jsonAlarm.Value<int>( nameof( AlarmDegree.Hint ) );
			int warnCount = jsonAlarm.Value<int>( nameof( AlarmDegree.Warn ) );
			int errorCount = jsonAlarm.Value<int>( nameof( AlarmDegree.Error ) );
			int fatalCount = jsonAlarm.Value<int>( nameof( AlarmDegree.Fatal ) );
			label_hint.Text = "      " + hintCount.ToString( );
			label_warn.Text = "      " + warnCount.ToString( );
			label_error.Text = "      " + errorCount.ToString( );
			label_fatal.Text = "      " + fatalCount.ToString( );
			alarmCount = hintCount + warnCount + errorCount + fatalCount;
		}

		private void AlarmDegreeCountControl_Load( object sender, EventArgs e )
		{
			toolTip1.SetToolTip( label_hint, "提示性报警[Hint]数量" );
			toolTip1.SetToolTip( label_warn, "警告性报警[Warn]数量" );
			toolTip1.SetToolTip( label_error, "错误性报警[Error]数量" );
			toolTip1.SetToolTip( label_fatal, "致命性报警[Fatal]数量" );
		}

		/// <summary>
		/// 获得当前设备的报警总数
		/// </summary>
		public long AlarmCount => alarmCount;

		private ToolTip toolTip1 = new ToolTip( );
		private long alarmCount = 0;

		private void AlarmDegreeCountControl_MouseClick( object sender, MouseEventArgs e )
		{
			this.OnMouseClick( e );
		}
	}
}
