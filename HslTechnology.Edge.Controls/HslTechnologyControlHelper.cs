using HslTechnology.Edge.Node.Render;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI.Docking;

namespace HslTechnology.Edge.Controls
{
	public class HslTechnologyControlHelper
	{
		/// <summary>
		/// 主题的关键字信息
		/// </summary>
		public static string ThemeKey { get; set; }

		/// <summary>
		/// 当前的主题信息
		/// </summary>
		public static ThemeBase Theme { get; set; }

		/// <summary>
		/// 将原始字节的数据转为实际的图片资源
		/// </summary>
		/// <param name="buffer">原始字节数据</param>
		/// <returns>图片资源</returns>
		public static Image GetImageFromBytes( byte[] buffer )
		{
			MemoryStream ms = new MemoryStream( buffer );
			Image image = Image.FromStream( ms );
			ms.Dispose( );
			return image;
		}

		/// <summary>
		/// 将 <see cref="DataGridView"/> 的行数控制在指定的行数
		/// </summary>
		/// <param name="dataGridView1">图标控件</param>
		/// <param name="row">指定的行数信息</param>
		public static void DataGridSpecifyRowCount( DataGridView dataGridView1, int row )
		{
			if (dataGridView1.AllowUserToAddRows)
			{
				row++;
			}
			if (dataGridView1.RowCount < row)
			{
				// 扩充
				dataGridView1.Rows.Add( row - dataGridView1.RowCount );
			}
			else if (dataGridView1.RowCount > row)
			{
				int deleteRows = dataGridView1.RowCount - row;
				for (int i = 0; i < deleteRows; i++)
				{
					dataGridView1.Rows.RemoveAt( dataGridView1.Rows.Count - 1 );
				}
			}
			if (row > 0)
			{
				dataGridView1.Rows[0].Cells[0].Selected = false;
			}
		}

		/// <summary>
		/// 空值的文本信息
		/// </summary>
		public static readonly string NullValue = "(None)";

		/// <summary>
		/// 将指定的 <see cref="ScalarDataNode"/> 数据节点赋值到 <see cref="DataGridViewRow"/> 表格行进行显示出来。
		/// </summary>
		/// <param name="row">表格行信息</param>
		/// <param name="dataNode">数据节点</param>
		public static void ShowDataGridViewRowDataNode( DataGridViewRow row, ScalarDataNode dataNode )
		{
			row.Cells[0].Value = dataNode.GetDisplayName( );
			row.Cells[1].Value = string.Empty;
			row.Cells[2].Value = dataNode.Unit;
			row.Cells[3].Value = dataNode.GetDataTypeText( );
			row.Cells[4].Value = dataNode.AccessLevel.ToString( );
			row.Cells[5].Value = dataNode.Description;
			row.Tag = dataNode;
		}

		/// <summary>
		/// 将json对象，按照<see cref="ScalarDataNode"/>数据节点对象定义，赋值到指定的 <see cref="DataGridView"/> 表格里面
		/// </summary>
		/// <param name="dataGridView">等待显示的数据表</param>
		/// <param name="json">json对象</param>
		public static void RenderJObjectToDataGrid( DataGridView dataGridView, JObject json )
		{
			if (json == null) return;
			for (int i = 0; i < dataGridView.Rows.Count; i++)
			{
				if (dataGridView.Rows[i].Tag is ScalarDataNode scalarDataNode)
				{
					if (json.ContainsKey( scalarDataNode.Name ))
					{
						JToken jToken = json.Property( scalarDataNode.Name ).Value;
						if(jToken.Type != JTokenType.Null)
						{
							if (scalarDataNode.TransDispalyFunction == null)
								dataGridView.Rows[i].Cells[1].Value = jToken;
							else
								dataGridView.Rows[i].Cells[1].Value = scalarDataNode.TransDispalyFunction( jToken );
						}
						else
						{
							dataGridView.Rows[i].Cells[1].Value = HslTechnologyControlHelper.NullValue;
						}
					}
					else
						dataGridView.Rows[i].Cells[1].Value = HslTechnologyControlHelper.NullValue;
				}
			}
		}


		[DllImport( "user32" )]
		private static extern int SendMessage( IntPtr hwnd, int wMsg, int wParam, IntPtr lParam );

		public static void Paint( Control control, bool paint )
		{
			if (paint)
			{
				SendMessage( control.Handle, 0x0B, 1, IntPtr.Zero );
				SendMessage( control.Handle, 0x0F, 0, IntPtr.Zero );
				//control.Invalidate( );
			}
			else
			{
				SendMessage( control.Handle, 0x0B, 0, IntPtr.Zero );
			}
		}



	}
}
