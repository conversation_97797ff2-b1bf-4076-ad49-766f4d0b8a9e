using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json.Linq;
using HslCommunication.MQTT;
using System.Runtime.InteropServices;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class DeviceStatusControl : UserControl
	{
		/// <summary>
		/// 实例化一个设备状态的控件，传入主题信息
		/// </summary>
		/// <param name="theme">主题</param>
		public DeviceStatusControl( string theme )
		{
			InitializeComponent( );
			this.Theme = theme;
		}

		/// <summary>
		/// 获取或设置当前的主题信息
		/// </summary>
		public string Theme { get; set; } = "Blue";

		/// <summary>
		/// 当前控件的设备名称
		/// </summary>
		public string DeviceFullName { get; set; }

		/// <summary>
		/// 获得当前设备的报警总数，该总数来源于统计
		/// </summary>
		public long AlarmCount => this.alarmDegreeCountControl1.AlarmCount;

		public event EventHandler<DeviceEventArgs> OnDeviceRequestChangeClick;
		private bool requestEnable = true;

		public void LoadDeviceData( JObject json, ToolTip toolTip )
		{
			if (json == null) return;
			if (json.ContainsKey( "__displayName" ))
				label1.Text = "设备名称：" + json["__displayName"].Value<string>( );
			else
				label1.Text = "设备名称：" + json["__name"].Value<string>( );
			label4.Text = "活动时间：" + json["__activeTime"].Value<string>( );
			label5.Text = "采集耗时：" + json["__captureSpendTime"].Value<double>( ).ToString( "F0" ) + " ms";
			label6.Text = "成功次数：" + json["__success"].Value<int>( );
			label7.Text = "失败次数：" + json["__failed"].Value<int>( );

			bool deviceStatus = json["__deviceStatus"].Value<bool>( );
			// 提取设备的消息
			if (json.ContainsKey( "__deviceMessage" ))
			{
				this.deviceMessage = json["__deviceMessage"].Value<string>( );
				if (!string.IsNullOrEmpty( this.deviceMessage ))
				{
					label_device_message.Visible = true;
				}
			}
			else
			{
				this.deviceMessage = string.Empty;
				this.label_device_message.Visible = false;
			}
			this.label_device_message.Text = this.deviceMessage;

			toolTip.SetToolTip( label1, "设备描述：" + json["__description"].Value<string>( ) );
			requestEnable = json.ContainsKey( "__requestEnable" ) ? json["__requestEnable"].Value<bool>( ) : true;
			this.alarmDegreeCountControl1.RenderAlarmCountByDevice( json );


			if (json.ContainsKey( "__statusType" ) && json["__statusType"].Value<string>( ) == "OnStop")
			{
				hslLanternSimple1.LanternBackground = Color.DimGray;
				label2.Text = "停用";
			}
			else
			{
				if (!requestEnable)
				{
					hslLanternSimple1.LanternBackground = Color.DodgerBlue;
				}
				else
				{
					hslLanternSimple1.LanternBackground = json["__deviceStatus"].Value<bool>( ) ? Color.LimeGreen : Color.Tomato;
				}

				if (deviceStatus)
				{
					// 如果因为暂停导致了时间差，也要显示出来
					TimeSpan ts = DateTime.Now - Convert.ToDateTime( json["__activeTime"].Value<string>( ) );
					if (ts.TotalSeconds > 10)
						label2.Text = HslCommunication.BasicFramework.SoftBasic.GetTimeSpanDescription( ts );
					else
						label2.Text = string.Empty;
				}
				else
				{
					TimeSpan ts = DateTime.Now - Convert.ToDateTime( json["__activeTime"].Value<string>( ) );
					label2.Text = HslCommunication.BasicFramework.SoftBasic.GetTimeSpanDescription( ts );
				}
			}

		}

		public void EveryBoolTick( bool tickStatus )
		{
			if (!string.IsNullOrEmpty( this.deviceMessage ))
			{
				label_device_message.BackColor = tickStatus ? Color.Tomato : Color.Transparent;
			}
		}

		public void SetSelected( bool select )
		{
			if (select)
			{
				if (Theme == "Dark")
				{
					this.BackColor = Color.FromArgb( 64, 64, 64 );
				}
				else
				{
					this.BackColor = Color.LightYellow;
				}
			}
			else
			{
				this.BackColor = Color.Transparent;
			}
		}

		protected override void OnPaint( PaintEventArgs e )
		{
			base.OnPaint( e );
			e.Graphics.DrawRectangle( Pens.LightGray, 0, 0, Width - 1, Height - 1 );
		}


		private void label7_MouseClick( object sender, MouseEventArgs e )
		{
			OnMouseClick( e );
		}

		private void hslLanternSimple1_MouseClick( object sender, MouseEventArgs e )
		{
			// 点击切换采集状态
			OnDeviceRequestChangeClick?.Invoke( this, new DeviceEventArgs( requestEnable ) );
		}


		private void hslLanternSimple1_Paint( object sender, PaintEventArgs e )
		{
			Graphics g = e.Graphics;
			if (!requestEnable)
			{
				g.FillRectangle( Brushes.White, 15, 11, 4, 22 );
				g.FillRectangle( Brushes.White, 25, 11, 4, 22 );
			}
		}

		private string deviceMessage = string.Empty;
		private string deviceName = string.Empty;

		private void DeviceStatusControl_Load( object sender, EventArgs e )
		{
			label_device_message.ForeColor = this.ForeColor;
			if (HslTechnologyControlHelper.ThemeKey == "Dark" && HslTechnologyControlHelper.Theme != null)
			{
				label2.ForeColor = Color.LightSkyBlue;
			}
		}
	}

	public class DeviceEventArgs : EventArgs
	{
		public DeviceEventArgs( bool requestEnable )
		{
			RequestEnable = requestEnable;
		}

		public bool RequestEnable { get; set; }
	}
}
