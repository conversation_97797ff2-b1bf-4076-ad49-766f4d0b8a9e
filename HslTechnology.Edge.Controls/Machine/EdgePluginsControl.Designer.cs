
namespace HslTechnology.Edge.Controls.Machine
{
	partial class EdgePluginsControl
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.hslPanelText1 = new HslControls.HslPanelText();
			this.textBox_plugins_desc = new System.Windows.Forms.TextBox();
			this.label9 = new System.Windows.Forms.Label();
			this.textBox_plugins_http = new System.Windows.Forms.TextBox();
			this.label8 = new System.Windows.Forms.Label();
			this.pictureBox_plugins_image = new System.Windows.Forms.PictureBox();
			this.label7 = new System.Windows.Forms.Label();
			this.textBox_plugins_framework = new System.Windows.Forms.TextBox();
			this.label6 = new System.Windows.Forms.Label();
			this.textBox_plugins_releaseDate = new System.Windows.Forms.TextBox();
			this.label5 = new System.Windows.Forms.Label();
			this.textBox_plugins_version = new System.Windows.Forms.TextBox();
			this.label4 = new System.Windows.Forms.Label();
			this.textBox_plugins_company = new System.Windows.Forms.TextBox();
			this.label3 = new System.Windows.Forms.Label();
			this.textBox_plugins_name = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.hslPanelText1.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox_plugins_image)).BeginInit();
			this.SuspendLayout();
			// 
			// hslPanelText1
			// 
			this.hslPanelText1.Controls.Add(this.textBox_plugins_desc);
			this.hslPanelText1.Controls.Add(this.label9);
			this.hslPanelText1.Controls.Add(this.textBox_plugins_http);
			this.hslPanelText1.Controls.Add(this.label8);
			this.hslPanelText1.Controls.Add(this.pictureBox_plugins_image);
			this.hslPanelText1.Controls.Add(this.label7);
			this.hslPanelText1.Controls.Add(this.textBox_plugins_framework);
			this.hslPanelText1.Controls.Add(this.label6);
			this.hslPanelText1.Controls.Add(this.textBox_plugins_releaseDate);
			this.hslPanelText1.Controls.Add(this.label5);
			this.hslPanelText1.Controls.Add(this.textBox_plugins_version);
			this.hslPanelText1.Controls.Add(this.label4);
			this.hslPanelText1.Controls.Add(this.textBox_plugins_company);
			this.hslPanelText1.Controls.Add(this.label3);
			this.hslPanelText1.Controls.Add(this.textBox_plugins_name);
			this.hslPanelText1.Controls.Add(this.label2);
			this.hslPanelText1.Location = new System.Drawing.Point(0, 0);
			this.hslPanelText1.Name = "hslPanelText1";
			this.hslPanelText1.Size = new System.Drawing.Size(543, 288);
			this.hslPanelText1.TabIndex = 3;
			this.hslPanelText1.Text = "插件信息";
			this.hslPanelText1.TextOffect = 20;
			this.hslPanelText1.ThemeColor = System.Drawing.Color.Gray;
			// 
			// textBox_plugins_desc
			// 
			this.textBox_plugins_desc.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox_plugins_desc.Location = new System.Drawing.Point(85, 173);
			this.textBox_plugins_desc.Multiline = true;
			this.textBox_plugins_desc.Name = "textBox_plugins_desc";
			this.textBox_plugins_desc.ReadOnly = true;
			this.textBox_plugins_desc.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
			this.textBox_plugins_desc.Size = new System.Drawing.Size(445, 105);
			this.textBox_plugins_desc.TabIndex = 15;
			// 
			// label9
			// 
			this.label9.AutoSize = true;
			this.label9.Location = new System.Drawing.Point(14, 176);
			this.label9.Name = "label9";
			this.label9.Size = new System.Drawing.Size(68, 17);
			this.label9.TabIndex = 14;
			this.label9.Text = "插件介绍：";
			// 
			// textBox_plugins_http
			// 
			this.textBox_plugins_http.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox_plugins_http.Location = new System.Drawing.Point(85, 143);
			this.textBox_plugins_http.Name = "textBox_plugins_http";
			this.textBox_plugins_http.ReadOnly = true;
			this.textBox_plugins_http.Size = new System.Drawing.Size(445, 23);
			this.textBox_plugins_http.TabIndex = 13;
			// 
			// label8
			// 
			this.label8.AutoSize = true;
			this.label8.Location = new System.Drawing.Point(14, 146);
			this.label8.Name = "label8";
			this.label8.Size = new System.Drawing.Size(68, 17);
			this.label8.TabIndex = 12;
			this.label8.Text = "插件网址：";
			// 
			// pictureBox_plugins_image
			// 
			this.pictureBox_plugins_image.BackColor = System.Drawing.Color.White;
			this.pictureBox_plugins_image.Location = new System.Drawing.Point(457, 118);
			this.pictureBox_plugins_image.Name = "pictureBox_plugins_image";
			this.pictureBox_plugins_image.Size = new System.Drawing.Size(16, 16);
			this.pictureBox_plugins_image.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox_plugins_image.TabIndex = 11;
			this.pictureBox_plugins_image.TabStop = false;
			// 
			// label7
			// 
			this.label7.AutoSize = true;
			this.label7.Location = new System.Drawing.Point(386, 117);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(68, 17);
			this.label7.TabIndex = 10;
			this.label7.Text = "插件图标：";
			// 
			// textBox_plugins_framework
			// 
			this.textBox_plugins_framework.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox_plugins_framework.Location = new System.Drawing.Point(85, 114);
			this.textBox_plugins_framework.Name = "textBox_plugins_framework";
			this.textBox_plugins_framework.ReadOnly = true;
			this.textBox_plugins_framework.Size = new System.Drawing.Size(284, 23);
			this.textBox_plugins_framework.TabIndex = 9;
			// 
			// label6
			// 
			this.label6.AutoSize = true;
			this.label6.Location = new System.Drawing.Point(14, 117);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(68, 17);
			this.label6.TabIndex = 8;
			this.label6.Text = "插件框架：";
			// 
			// textBox_plugins_releaseDate
			// 
			this.textBox_plugins_releaseDate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox_plugins_releaseDate.Location = new System.Drawing.Point(318, 84);
			this.textBox_plugins_releaseDate.Name = "textBox_plugins_releaseDate";
			this.textBox_plugins_releaseDate.ReadOnly = true;
			this.textBox_plugins_releaseDate.Size = new System.Drawing.Size(212, 23);
			this.textBox_plugins_releaseDate.TabIndex = 7;
			// 
			// label5
			// 
			this.label5.AutoSize = true;
			this.label5.Location = new System.Drawing.Point(247, 87);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(68, 17);
			this.label5.TabIndex = 6;
			this.label5.Text = "发布日期：";
			// 
			// textBox_plugins_version
			// 
			this.textBox_plugins_version.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox_plugins_version.Location = new System.Drawing.Point(85, 84);
			this.textBox_plugins_version.Name = "textBox_plugins_version";
			this.textBox_plugins_version.ReadOnly = true;
			this.textBox_plugins_version.Size = new System.Drawing.Size(147, 23);
			this.textBox_plugins_version.TabIndex = 5;
			// 
			// label4
			// 
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(14, 87);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(68, 17);
			this.label4.TabIndex = 4;
			this.label4.Text = "插件版本：";
			// 
			// textBox_plugins_company
			// 
			this.textBox_plugins_company.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox_plugins_company.Location = new System.Drawing.Point(85, 54);
			this.textBox_plugins_company.Name = "textBox_plugins_company";
			this.textBox_plugins_company.ReadOnly = true;
			this.textBox_plugins_company.Size = new System.Drawing.Size(445, 23);
			this.textBox_plugins_company.TabIndex = 3;
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(14, 57);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(68, 17);
			this.label3.TabIndex = 2;
			this.label3.Text = "公司名称：";
			// 
			// textBox_plugins_name
			// 
			this.textBox_plugins_name.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
			this.textBox_plugins_name.Location = new System.Drawing.Point(85, 25);
			this.textBox_plugins_name.Name = "textBox_plugins_name";
			this.textBox_plugins_name.ReadOnly = true;
			this.textBox_plugins_name.Size = new System.Drawing.Size(445, 23);
			this.textBox_plugins_name.TabIndex = 1;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(14, 28);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(68, 17);
			this.label2.TabIndex = 0;
			this.label2.Text = "插件名称：";
			// 
			// EdgePluginsControl
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.Transparent;
			this.Controls.Add(this.hslPanelText1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.MaximumSize = new System.Drawing.Size(543, 288);
			this.MinimumSize = new System.Drawing.Size(543, 288);
			this.Name = "EdgePluginsControl";
			this.Size = new System.Drawing.Size(543, 288);
			this.hslPanelText1.ResumeLayout(false);
			this.hslPanelText1.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox_plugins_image)).EndInit();
			this.ResumeLayout(false);

		}

		#endregion

		private HslControls.HslPanelText hslPanelText1;
		private System.Windows.Forms.TextBox textBox_plugins_desc;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.TextBox textBox_plugins_http;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.PictureBox pictureBox_plugins_image;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.TextBox textBox_plugins_framework;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.TextBox textBox_plugins_releaseDate;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.TextBox textBox_plugins_version;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.TextBox textBox_plugins_company;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.TextBox textBox_plugins_name;
		private System.Windows.Forms.Label label2;
	}
}
