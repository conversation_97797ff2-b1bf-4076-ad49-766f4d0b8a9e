using HslCommunication;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class DeviceLogControl : UserControl
	{
		public DeviceLogControl( )
		{
			InitializeComponent( );
		}

		public void ThemeDark( )
		{
			this.textBox_log.BackColor = Color.FromArgb( 64, 64, 64 );
			this.textBox_log.ForeColor = Color.WhiteSmoke;

			this.button_stop.BackColor = Color.FromArgb( 64, 64, 64 );
			this.button_clear.BackColor = Color.FromArgb( 64, 64, 64 );

			this.button_stop.FlatStyle = FlatStyle.Flat;
			this.button_clear.FlatStyle = FlatStyle.Flat;
			this.textBox_log.BorderStyle = BorderStyle.FixedSingle;
			this.textBox1.BorderStyle = BorderStyle.FixedSingle;
		}

		/// <summary>
		/// 显示设备的路径信息
		/// </summary>
		/// <param name="device">设备唯一ID</param>
		public async Task RenderDevicePath( string device, Func<MqttClient> func )
		{
			if (this.deviceFullName != device)
			{
				this.deviceFullName = device;
				textBox1.Text = device;

				mqtt?.ConnectClose( );
				mqtt = func( );
				mqtt.OnMqttMessageReceived += MqttClient_OnMqttMessageReceived;
				OperateResult connect = await mqtt.ConnectServerAsync( );
				if (connect.IsSuccess)
				{
					textBox_log.Clear( );
					if (radioButton_run.Checked)
					{
						mqtt.SubscribeMessage( new string[] { "__log:" + this.deviceFullName } );
					}
					else if (radioButton_telegram.Checked)
					{
						mqtt.SubscribeMessage( new string[] { "__log.Telegram:" + this.deviceFullName } );
					}
				}
			}
		}

		public void RenderClose( )
		{
			if (!string.IsNullOrEmpty( this.deviceFullName ))
			{
				this.deviceFullName = string.Empty;
				this.mqtt?.ConnectClose( );
			}
		}

		private void MqttClient_OnMqttMessageReceived( MqttClient client, MqttApplicationMessage message)
		{
			this.RenderLog( message.Topic, Encoding.UTF8.GetString( message.Payload ) );
		}


		private void RenderLog( string topic, string msg )
		{
			if (string.IsNullOrEmpty( msg )) return;
			if (renderStop == false)
			{
				if (InvokeRequired)
				{
					Invoke( new Action<string, string>( RenderLog ), topic, msg );
					return;
				}

				if (topic.StartsWith( "__log.Telegram:" ))
				{
					if (radioButton_telegram.Checked)
					{
						textBox_log.AppendText( msg );
						textBox_log.AppendText( Environment.NewLine );
					}
				}
				else if (topic.StartsWith( "__log:" ))
				{
					if (radioButton_run.Checked)
					{
						textBox_log.AppendText( msg );
						textBox_log.AppendText( Environment.NewLine );
					}
				}
			}
		}

		private void button_stop_Click( object sender, EventArgs e )
		{
			if ( renderStop == false)
			{
				renderStop = true;
				button_stop.Text = "继续";
			}
			else
			{
				renderStop = false;
				button_stop.Text = "暂停";
			}
		}

		private void button_clear_Click( object sender, EventArgs e )
		{
			textBox_log.Clear( );
		}


		private string deviceFullName;       // 设备唯一的路径信息
		private bool renderStop = false;     // 是否暂停显示的操作
		private MqttClient mqtt;             // 订阅的客户端操作

		private void DeviceLogControl_Load( object sender, EventArgs e )
		{
			this.radioButton_run.CheckedChanged += RadioButton_run_CheckedChanged;
			this.radioButton_telegram.CheckedChanged += RadioButton_telegram_CheckedChanged;
		}

		private void RadioButton_run_CheckedChanged( object sender, EventArgs e )
		{
			if (mqtt != null && radioButton_run.Checked)
			{
				mqtt.UnSubscribeMessage( new string[] { "__log.Telegram:" + this.deviceFullName } );
				mqtt.SubscribeMessage(   new string[] { "__log:" + deviceFullName } );
			}
		}

		private void RadioButton_telegram_CheckedChanged( object sender, EventArgs e )
		{
			if (mqtt != null && radioButton_telegram.Checked)
			{
				mqtt.UnSubscribeMessage( new string[] { "__log:" + this.deviceFullName } );
				mqtt.SubscribeMessage(   new string[] { "__log.Telegram:" + this.deviceFullName } );
			}
		}
	}
}
