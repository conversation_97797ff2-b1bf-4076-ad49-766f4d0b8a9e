using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.Node.Alarm;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class DeviceAlarmControl : UserControl
	{
		public DeviceAlarmControl( )
		{
			InitializeComponent( );
		}

		public void ThemeDark( )
		{
			this.textBox1.BorderStyle = BorderStyle.FixedSingle;
			this.dataGridView2.EnableHeadersVisualStyles = false;
			this.dataGridView2.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb( 64, 64, 64 );
			this.dataGridView2.ColumnHeadersDefaultCellStyle.ForeColor = Color.Cyan;
			this.comboBox1.FlatStyle = FlatStyle.Flat;
			this.comboBox1.BackColor = Color.FromArgb( 64, 64, 64 );
			this.comboBox1.ForeColor = Color.WhiteSmoke;
		}

		private void DeviceAlarmControl_Load( object sender, EventArgs e )
		{
			BackColor = Color.Transparent;
			comboBox1.DataSource = HslTechnologyHelper.GetAlarmDegreeWithAll( );
			comboBox1.SelectedIndex = 0;
			comboBox1.SelectedIndexChanged += ComboBox1_SelectedIndexChanged;
		}

		private void ComboBox1_SelectedIndexChanged( object sender, EventArgs e )
		{
			if(this.alarmItems != null)
			{
				RenderAlarmItems( this.alarmItems );
			}
		}

		public void RenderDevicePath(string device )
		{
			textBox1.Text = device;
		}

		public void RenderAlarm( JArray array )
		{
			try
			{
				List<AlarmItem> list = array.ToObject<List<AlarmItem>>( );
				RenderAlarm( list );
			}
			catch(Exception ex)
			{
				HslCommunication.BasicFramework.SoftBasic.ShowExceptionMessage( ex );
			}
		}

		public void RenderAlarm( List<AlarmItem> list )
		{
			this.alarmItems = list;
			RenderAlarmItems( list );
		}

		private void RenderAlarmItems( List<AlarmItem> list )
		{
			this.alarmDegreeCountControl1.RenderAlarmCountByList( list );
			string selectDegree = comboBox1.SelectedItem == null ? "全部" : comboBox1.SelectedItem.ToString( );
			if (selectDegree == AlarmDegree.Hint.ToString( )) list = list.FindAll( m => m.Degree == AlarmDegree.Hint );
			else if (selectDegree == AlarmDegree.Warn.ToString( )) list = list.FindAll( m => m.Degree == AlarmDegree.Warn );
			else if (selectDegree == AlarmDegree.Error.ToString( )) list = list.FindAll( m => m.Degree == AlarmDegree.Error );
			else if (selectDegree == AlarmDegree.Fatal.ToString( )) list = list.FindAll( m => m.Degree == AlarmDegree.Fatal );
			HslTechnologyControlHelper.DataGridSpecifyRowCount( this.dataGridView2, list.Count );
			for (int i = 0; i < list.Count; i++)
			{
				if (renderMode == 0)
					this.dataGridView2.Rows[i].Cells[0].Value = list[i].UniqueId;
				else if (renderMode == 1)
					this.dataGridView2.Rows[i].Cells[0].Value = GetMachineName( list[i].DeviceName );
				else
					this.dataGridView2.Rows[i].Cells[0].Value = list[i].DeviceName;

				this.dataGridView2.Rows[i].Cells[1].Value = list[i].AlarmCode;
				this.dataGridView2.Rows[i].Cells[2].Value = list[i].AlarmContent;
				this.dataGridView2.Rows[i].Cells[3].Value = list[i].StartTime.ToString( "yyyy-MM-dd HH:mm:ss" );
				this.dataGridView2.Rows[i].Cells[4].Value = HslTechnologyHelper.GetTimeSpanText( list[i].FinishTime - list[i].StartTime );
				this.dataGridView2.Rows[i].Cells[5].Value = list[i].Degree.ToString( );
				this.dataGridView2.Rows[i].Cells[6].Value = list[i].Status.ToString( );
				this.dataGridView2.Rows[i].Cells[7].Value = list[i].Checked;
				if (list[i].Degree == AlarmDegree.Hint)
					this.dataGridView2.Rows[i].DefaultCellStyle.BackColor = this.dataGridView2.DefaultCellStyle.BackColor;
				else if (list[i].Degree == AlarmDegree.Warn)
					this.dataGridView2.Rows[i].DefaultCellStyle.BackColor = Color.Orange;
				else if (list[i].Degree == AlarmDegree.Error)
					this.dataGridView2.Rows[i].DefaultCellStyle.BackColor = Color.OrangeRed;
				else
					this.dataGridView2.Rows[i].DefaultCellStyle.BackColor = Color.Red;
			}
		}

		private List<AlarmItem> alarmItems;
		private string GetMachineName( string input )
		{
			if (string.IsNullOrEmpty( input )) return string.Empty;
			if (input.EndsWith( "/" )) return input;
			int index = input.LastIndexOf( '/' );
			if (index < 0) return input;
			return input.Substring( index + 1 );
		}

		/// <summary>
		/// 显示模式，0表示单个设备查看的，1表示查看指定路径的设备报警，2表示查看所有路径的报警信息
		/// </summary>
		[Category( "Appearance" )]
		[Description( "显示模式，0表示单个设备查看的，1表示查看指定路径的设备报警，2表示查看所有路径的报警信息" )]
		[DefaultValue( 0 )]
		public int RenderMode 
		{ 
			get => this.renderMode;
			set
			{
				this.renderMode = value;
				if (this.renderMode == 0)
					dataGridView2.Columns[0].HeaderText = "UniqueID";
				else
					dataGridView2.Columns[0].HeaderText = "Device";
			}
		}

		/// <summary>
		/// 显示模式，0表示单个设备查看的，1表示查看指定路径的设备报警，2表示查看所有路径的报警信息
		/// </summary>
		private int renderMode = 0;
	}
}
