using HslTechnology.Edge.Node.Render;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.DataBusiness.Time;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class EdgeInfoControl : UserControl
	{
		public EdgeInfoControl( )
		{
			InitializeComponent( );
		}

		private void DeviceInfoControl_Load( object sender, EventArgs e )
		{
			// 新增系统的数据内容
			List<ScalarDataNode> scalarDataNodes = new List<ScalarDataNode>( );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__name",
				DisplayName = "网关名称",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关的配置名称"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__company",
				DisplayName = "公司名称",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关的公司信息"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__version",
				DisplayName = "版本号",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关的服务程序的版本"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__osInfo",
				DisplayName = "操作系统",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关的操作系统信息"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__clr",
				DisplayName = "运行时",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关的系统的运行时环境版本"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__build",
				DisplayName = "编译版本",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关服务器的编译时环境"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__location",
				DisplayName = "安装位置",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关安装的地址"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__startTime",
				DisplayName = "启动时间",
				DataType = Node.DataType.DateTime,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关启动的时间"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__ramUse",
				DisplayName = "内存使用",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "实时内存使用信息",
				TransDispalyFunction = RamUseTranslate
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__activeTime",
				DisplayName = "活动时间",
				DataType = Node.DataType.DateTime,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "网关服务的主线程的基本信息"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__timeout",
				DisplayName = "超时检测数",
				DataType = Node.DataType.Int32,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "当前等待检测超时操作的数量"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__threadCount",
				DisplayName = "线程使用数量",
				DataType = Node.DataType.Int32,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "当前的网关系统使用的线程数量"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__standbyStatus",
				DisplayName = "备用服务器状态",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "当前的服务器的备用服务器信息"
			} );

			HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, scalarDataNodes.Count );
			for (int i = 0; i < scalarDataNodes.Count; i++)
			{
				HslTechnologyControlHelper.ShowDataGridViewRowDataNode( dataGridView1.Rows[i], scalarDataNodes[i] );
			}

			itemControls = new List<RuntimeItemControl>( );
			for (int i = 0; i < 10; i++)
			{
				RuntimeItemControl runtimeItemControl = new RuntimeItemControl( );
				runtimeItemControl.Size = new System.Drawing.Size( panel1.Width - 5, 26 );
				panel1.Controls.Add( runtimeItemControl );
				runtimeItemControl.Anchor = AnchorStyles.Left | AnchorStyles.Top | AnchorStyles.Right;
				runtimeItemControl.Location = new System.Drawing.Point( 2, i * 29 + 3 );

				itemControls.Add( runtimeItemControl );
			}


			if (HslTechnologyControlHelper.ThemeKey == "Dark" && HslTechnologyControlHelper.Theme != null)
			{
				//dataGridView1.DefaultCellStyle.BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				//dataGridView1.DefaultCellStyle.ForeColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Text;
				dataGridView1.BackgroundColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				dataGridView1.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb( 64, 64, 78 );
				dataGridView1.RowsDefaultCellStyle.BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				dataGridView1.ColumnHeadersDefaultCellStyle.BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				//for (int i = 0; i < dataGridView1.Columns.Count; i++)
				//{
				//	dataGridView1.Columns[i].HeaderCell.Style.BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;

				//}
			}
		}


		private string RamUseTranslate( JToken token )
		{
			return SoftBasic.GetSizeDescription( token.Value<long>( ) );
		}


		/// <summary>
		/// 初始化显示ram使用信息的数据
		/// </summary>
		/// <param name="ramUseHistory">历史数值</param>
		public void RenderRamUseHistory( float[] ramUseHistory, float[] threadUseHistory )
		{
			hslCurve1.SetCurve( "ram", 0, ramUseHistory, Color.DodgerBlue, 1f, HslControls.CurveStyle.LineSegment );
			if (valueMin1 < 0 && ramUseHistory?.Length > 0)
			{
				valueMin1 = (float)Math.Floor( ramUseHistory.Min( ) );
				valueMax1 = (float)Math.Ceiling( ramUseHistory.Max( ) );
				if (valueMax1 - valueMin1 < 0.5f)
					valueMax1 = valueMin1 + 1;

				hslCurve1.ReferenceAxisLeft.Max = valueMax1;
				hslCurve1.ReferenceAxisLeft.Min = valueMin1;
			}

			if (threadUseHistory != null)
			{
				hslCurve1.SetCurve( "thread", 1, threadUseHistory, Color.Orange, 1f, HslControls.CurveStyle.LineSegment );
				if (valueMin2 < 0 && threadUseHistory?.Length > 0)
				{
					valueMin2 = (float)Math.Floor( threadUseHistory.Min( ) );
					valueMax2 = (float)Math.Ceiling( threadUseHistory.Max( ) );
					if (valueMax2 - valueMin2 < 0.5f)
						valueMax2 = valueMin2 + 1;


					hslCurve1.ReferenceAxisRight.Max = valueMax2 < valueMin2 + hslCurve1.ValueSegment ? valueMin2 + hslCurve1.ValueSegment : valueMin2;
					hslCurve1.ReferenceAxisRight.Min = valueMin2;
				}
			}
		}

		/// <summary>
		/// 将实际的数据显示到表格里
		/// </summary>
		/// <param name="json">实际的设备数据的JSON格式</param>
		public void RenderMachineData( JObject json )
		{
			HslTechnologyControlHelper.RenderJObjectToDataGrid( dataGridView1, json ); // 显示全部的数据信息


			if (json.ContainsKey( "__ramUse" ) && json.ContainsKey( "__threadCount" ))
			{
				long ramUse = json["__ramUse"].Value<long>( );
				float ram = (float)(ramUse / 1024d / 1024d);

				if (valueMin1 < 0)
				{
					valueMin1 = (float)Math.Floor( ram );
					valueMax1 = (float)Math.Ceiling( ram );
					if (valueMax1 - valueMin1 < 0.5f)
						valueMax1 = valueMin1 + 1;
				}
				if (valueMin1 > ram) valueMin1 = (float)Math.Floor( ram );
				if (valueMax1 < ram) valueMax1 = (float)Math.Ceiling( ram );

				hslCurve1.ReferenceAxisLeft.Max = valueMax1;
				hslCurve1.ReferenceAxisLeft.Min = valueMin1;
				hslCurve1.ReferenceAxisRight.Max = valueMax1;
				hslCurve1.ReferenceAxisRight.Min = valueMin1;

				int count = json["__threadCount"].Value<int>( );

				if (valueMin2 > count) valueMin2 = count;
				if (valueMax2 < count) valueMax2 = count;

				hslCurve1.ReferenceAxisRight.Max = valueMax2 < valueMin2 + hslCurve1.ValueSegment ? valueMin2 + hslCurve1.ValueSegment : valueMin2;
				hslCurve1.ReferenceAxisRight.Min = valueMin2;

				hslCurve1.AddCurveData( new string[] { "ram", "thread", }, new float[] { ram, count } );

			}
			else if (json.ContainsKey( "__ramUse" ))
			{
				long ramUse = json["__ramUse"].Value<long>( );
				float ram = (float)(ramUse / 1024d / 1024d);
				hslCurve1.AddCurveData( "ram", ram );

				if (valueMin1 < 0)
				{
					valueMin1 = (float)Math.Floor( ram );
					valueMax1 = (float)Math.Ceiling( ram );
					if (valueMax1 - valueMin1 < 0.5f)
						valueMax1 = valueMin1 + 1;
				}
				if (valueMin1 > ram) valueMin1 = (float)Math.Floor( ram );
				if (valueMax1 < ram) valueMax1 = (float)Math.Ceiling( ram );

				hslCurve1.ReferenceAxisLeft.Max  = valueMax1;
				hslCurve1.ReferenceAxisLeft.Min  = valueMin1;
				hslCurve1.ReferenceAxisRight.Max = valueMax1;
				hslCurve1.ReferenceAxisRight.Min = valueMin1;
			}
		}

		/// <summary>
		/// 显示网关的运行记录信息
		/// </summary>
		/// <param name="edgeTimes">运行时间信息</param>
		public void RenderEdgeTime( EdgeTimeConsume[] edgeTimes )
		{
			if (edgeTimes == null) return;
			for (int i = 0; i < itemControls.Count; i++)
			{
				if (i < edgeTimes.Length)
				{
					itemControls[i].RenderRuntime( edgeTimes[i] );
				}
				else
				{
					itemControls[i].RenderRuntime( null );
				}
			}
		}

		/// <summary>
		/// 显示错误的文本消息
		/// </summary>
		/// <param name="message">消息内容</param>
		public void RenderMessage( string message )
		{
			textBox2.Text = message;
		}

		public void RenderIniLog( string log )
		{
			textBox1.Text = log;
		}

		/// <summary>
		/// 显示异常列表
		/// </summary>
		/// <param name="ex"></param>
		public void RenderExceptions( string[] ex )
		{
			textBox3.Clear( );

			if (ex == null || ex.Length == 0)
			{
				textBox3.Text = "无";
				return;
			}
			for (int i = 0; i < ex.Length; i++)
			{
				textBox3.AppendText( ex[i] );
				textBox3.AppendText( Environment.NewLine );
			}
		}

		private float valueMin1 = -1;
		private float valueMax1 = -1;
		private float valueMin2 = -1;
		private float valueMax2 = -1;
		private List<RuntimeItemControl> itemControls;
	}
}
