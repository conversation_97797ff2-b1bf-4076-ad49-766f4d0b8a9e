using HslTechnology.Edge.Node.Render;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class DeviceInfoControl : UserControl
	{
		public DeviceInfoControl( )
		{
			InitializeComponent( );
		}

		public void ThemeDark( )
		{
			this.textBox1.BorderStyle = BorderStyle.FixedSingle;
			this.dataGridView1.EnableHeadersVisualStyles = false;
			this.dataGridView1.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb( 64, 64, 64 );
			this.dataGridView1.ColumnHeadersDefaultCellStyle.ForeColor = Color.Cyan;
		}

		private void DeviceInfoControl_Load( object sender, EventArgs e )
		{
			BackColor = Color.Transparent;
			// 新增系统的数据内容
			List<ScalarDataNode> scalarDataNodes = new List<ScalarDataNode>( );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__name",
				DisplayName = "设备名称",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "唯一的设备路径名称"
			} ); 
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__displayName",
				DisplayName = "设备别名",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "用于在主界面显示的设备别名信息"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__description",
				DisplayName = "设备描述",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "设备的实时状态信息",
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__failedMsg",
				DisplayName = "设备错误消息",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__startTime",
				DisplayName = "开始采集时间",
				DataType = Node.DataType.DateTime,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "可以理解为网关启动时间"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__config",
				DisplayName = "设备配置信息",
				DataType = Node.DataType.String,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__deviceStatus",
				DisplayName = "设备是否在线",
				DataType = Node.DataType.Bool,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__onlineTime",
				DisplayName = "设备上线时间",
				DataType = Node.DataType.DateTime,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__activeTime",
				DisplayName = "设备活动时间",
				DataType = Node.DataType.DateTime,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read,
				Description = "最后一次活动的时间"
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__success",
				DisplayName = "采集成功次数",
				DataType = Node.DataType.Int64,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read
			} );
			scalarDataNodes.Add( new ScalarDataNode( )
			{
				Name = "__failed",
				DisplayName = "采集失败次数",
				DataType = Node.DataType.Int64,
				DataDimension = Node.DataDimension.Scalar,
				AccessLevel = Node.AccessLevel.Read
			} );

			HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, scalarDataNodes.Count );
			for (int i = 0; i < scalarDataNodes.Count; i++)
			{
				HslTechnologyControlHelper.ShowDataGridViewRowDataNode( dataGridView1.Rows[i], scalarDataNodes[i] );
			}

			this.dataGridView1.SizeChanged += DataGridView1_SizeChanged;
		}

		private void DataGridView1_SizeChanged( object sender, EventArgs e )
		{
			int offset = this.dataGridView1.Width - 935;
			this.dataGridView1.Columns[1].Width = 350 + 2 * offset / 3;
			this.dataGridView1.Columns[5].Width = 150 + offset / 3;
		}

		public void RenderDevicePath( string device )
		{
			textBox1.Text = device;
		}

		/// <summary>
		/// 将实际的数据显示到表格里
		/// </summary>
		/// <param name="json">实际的设备数据的JSON格式</param>
		public void RenderMachineData( JObject json )
		{
			HslTechnologyControlHelper.RenderJObjectToDataGrid( dataGridView1, json ); // 显示全部的数据信息
		}
	}
}
