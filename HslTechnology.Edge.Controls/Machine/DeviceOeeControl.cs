using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.Edge.DataBusiness.Oee;
using Newtonsoft.Json.Linq;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class DeviceOeeControl : UserControl
	{
		public DeviceOeeControl( )
		{
			InitializeComponent( );
		}

		protected override void OnResize( EventArgs e )
		{
			base.OnResize( e );
			int width = (this.Width - 15) / 2;
			if (width < 30) return;

			hslPanelTextBack1.Width = width;
			hslPanelTextBack2.Width = width;
			hslPanelTextBack2.Location = new Point(
				this.Width - width - 5, hslPanelTextBack2.Location.Y );
		}

		public void ThemeDark( )
		{
			this.textBox1.BorderStyle = BorderStyle.FixedSingle;
		}

		private void DeviceOeeControl_Load( object sender, EventArgs e )
		{
			BackColor = Color.Transparent;
			comboBox1.SelectedIndexChanged += ComboBox1_SelectedIndexChanged;
			comboBox2.SelectedIndex = 0;
			comboBox2.SelectedIndexChanged += ComboBox2_SelectedIndexChanged;

			if (HslTechnologyControlHelper.ThemeKey == "Dark" && HslTechnologyControlHelper.Theme != null)
			{
				hslPanelTextBack1.PanelTextBackColor = Color.DarkOrange;
				hslPanelTextBack2.PanelTextBackColor = Color.DarkGreen;

				hslBarChart1.ForeColor = Color.White;
				hslBarChart2.ForeColor = Color.White;
				hslBarChart1.ColorLinesAndText = Color.Cyan;
				hslBarChart2.ColorLinesAndText = Color.Cyan;
				hslProgress1.BackColor = Color.FromArgb( 0x42, 0x42, 0x42 );
				hslProgress1.ProgressColor = Color.Green;
			}
		}

		private void ComboBox2_SelectedIndexChanged( object sender, EventArgs e )
		{
			if (this.oeeItems != null)
			{
				RenderOeeItems( this.oeeItems );
			}
		}

		private void ComboBox1_SelectedIndexChanged( object sender, EventArgs e )
		{
			if (this.oeeItems != null)
			{
				RenderOeeItems( this.oeeItems );
			}
		}

		public void RenderDevicePath( string device )
		{
			textBox1.Text = device;
		}

		public void RenderOee( string device, JArray array, bool isChangeDevice )
		{
			if (textBox1.Text != device) isChangeDevice = true;
			textBox1.Text = device;
			try
			{
				List<OEEItem> list = array.ToObject<List<OEEItem>>( );
				if(list.Count > 0)
				{
					if (isChangeDevice)
					{
						comboBox1.DataSource = list.Select( m => m.TagName ).ToArray( );
						comboBox1.SelectedIndex = 0;
					}
				}
				else
				{
					comboBox1.DataSource = null;
				}
				RenderOeeItems( list );
			}
			catch (Exception ex)
			{
				HslCommunication.BasicFramework.SoftBasic.ShowExceptionMessage( ex );
			}
		}

		public void RenderOee( List<OEEItem> list )
		{
			this.oeeItems = list;
			if (list.Count > 0)
			{
				comboBox1.DataSource = list.Select( m => m.TagName ).ToArray( );
				comboBox1.SelectedIndex = 0;
			}
			else
			{
				comboBox1.DataSource = null;
			}
			RenderOeeItems( list );
		}

		private void RenderOeeItems( List<OEEItem> list )
		{
			if (list == null || list.Count == 0)
			{
				hslProgress1.Value = 0;
				label4.Text = "0/0";
				label5.Text = "设备状态：";

				Dictionary<string, double[]> dict = null;
				hslBarChart1.SetDataSource( dict, null, null );
				hslBarChart2.SetDataSource( dict, null, null );
				return;
			}

			string selectDegree = comboBox1.SelectedItem == null ? "" : comboBox1.SelectedItem.ToString( );
			if (string.IsNullOrEmpty( selectDegree )) return;

			OEEItem item = list.Find( m => m.TagName == selectDegree );
			if (item == null) return;

			hslProgress1.Value = Convert.ToInt32( item.OeePercent * 100 );
			label4.Text = $"{Convert.ToInt32( item.WorkingSecondsCount )}/{Convert.ToInt32( item.SecondsCount )}";
			if (item.StatusConsumes?.Length > 0)
			{
				ItemStatusConsume consume = item.StatusConsumes.FirstOrDefault( m => m.StatusCode == item.OeeStatus );
				if (consume != null)
					label5.Text = "设备状态：" + consume.Name;
			}

			if (comboBox2.SelectedIndex == 0)
			{
				if (item.StatusConsumes?.Length > 0)
				{
					int[] values = item.StatusConsumes.Select( m => Convert.ToInt32( m.Consume ) ).ToArray( );
					string[] texts = item.StatusConsumes.Select( m => m.Name ).ToArray( );
					if (hslBarChart1.ShowBarValueFormat != "{0} 秒") hslBarChart1.ShowBarValueFormat = "{0} 秒";
					hslBarChart1.SetDataSource( values, texts );
				}
				if (item.LastStatusConsumes?.Length > 0)
				{
					int[] values = item.LastStatusConsumes.Select( m => Convert.ToInt32( m.Consume ) ).ToArray( );
					string[] texts = item.LastStatusConsumes.Select( m => m.Name ).ToArray( );
					if (hslBarChart2.ShowBarValueFormat != "{0} 秒") hslBarChart2.ShowBarValueFormat = "{0} 秒";
					hslBarChart2.SetDataSource( values, texts );
				}
			}
			else
			{
				if (item.ShiftConsumes?.Length > 0)
				{
					int[] values = item.ShiftConsumes.Select( m => m.GetWorkingPercent( ) ).ToArray( );
					string[] texts = item.ShiftConsumes.Select( m => m.GetWorkName( GetBarChartPaintWidth( item.ShiftConsumes.Length ) ) ).ToArray( );
					if (hslBarChart1.ShowBarValueFormat != "{0} %") hslBarChart1.ShowBarValueFormat = "{0} %";
					hslBarChart1.SetDataSource( values, texts );
				}
				if (item.LastShiftConsumes?.Length > 0)
				{
					int[] values = item.LastShiftConsumes.Select( m => m.GetWorkingPercent( ) ).ToArray( );
					string[] texts = item.LastShiftConsumes.Select( m => m.GetWorkName( GetBarChartPaintWidth( item.ShiftConsumes.Length ) ) ).ToArray( );
					if (hslBarChart2.ShowBarValueFormat != "{0} %") hslBarChart2.ShowBarValueFormat = "{0} %";
					hslBarChart2.SetDataSource( values, texts );
				}
			}
		}

		private int GetBarChartPaintWidth( int length )
		{
			int width = hslBarChart1.Width > 60 ? hslBarChart1.Width : 60;
			if (length <= 0) length = 1;
			return (width - 60) / length;
		}

		private List<OEEItem> oeeItems;

	}
}
