using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node.Render;
using Newtonsoft.Json.Linq;
using HslCommunication;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class DataInfomationControl : UserControl
	{
		public DataInfomationControl( )
		{
			InitializeComponent( );
		}

		public void DataGridSpecifyRowCount( int row )
		{
			HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, row );
		}

		public void ThemeDark( )
		{
			this.textBox2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
			this.dataGridView1.EnableHeadersVisualStyles = false;
			this.dataGridView1.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb( 64, 64, 64 );
			this.dataGridView1.ColumnHeadersDefaultCellStyle.ForeColor = Color.Cyan;
		}

		#region Public Data Render

		/// <summary>
		/// 通常是用来切换选择的节点，针对当前的设备的情况下，快速切换显示其他节点
		/// </summary>
		/// <param name="device">设备数据的唯一ID信息</param>
		/// <param name="selected">当前选择的节点信息</param>
		/// <param name="arrayIndex">如果是结构体列表，指示选择的索引</param>
		public void RenderDataNodes( string device, ScalarDataNode selected, int arrayIndex = -1 )
		{
			RenderDataNodes( device, this.scalarDataNodes, selected, arrayIndex );
		}

		/// <summary>
		/// 显示一个设备的数据，可能是全部数据，也可以是某个节点，切换显示相关的数据
		/// </summary>
		/// <param name="deviceUrl">设备本身的唯一路径</param>
		/// <param name="device">设备数据的唯一ID信息</param>
		/// <param name="scalarDatas">所有显示的节点信息</param>
		/// <param name="selected">当前选择的节点信息</param>
		/// <param name="arrayIndex">如果是结构体列表，指示选择的索引</param>
		public void RenderDataNodes( string device, ScalarDataNode[] scalarDatas, ScalarDataNode selected = null, int arrayIndex = -1 )
		{
			this.scalarDataNodes = scalarDatas;
			this.selectDataNode = selected;
			this.arrayIndex = arrayIndex;
			this.deviceUrl = device;
			if (selected == null)
			{
				// 选择查看了整个数据
				this.textBox2.Text = device;
				HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, scalarDatas.Length );
				for (int i = 0; i < scalarDatas.Length; i++)
				{
					HslTechnologyControlHelper.ShowDataGridViewRowDataNode( dataGridView1.Rows[i], scalarDatas[i] );
				}
			}
			else
			{
				// 选择查看了某个数据信息
				this.textBox2.Text = device + "/" + selected.Name;
				if (selected.DataDimension == Node.DataDimension.Scalar)
				{
					// 结构体和类显示不一样，还没有处理
					if (selected.DataType == Node.DataType.Struct)
					{
						if (selected.StructNodes != null)
						{
							HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, selected.StructNodes.Length );
							for (int i = 0; i < selected.StructNodes.Length; i++)
							{
								HslTechnologyControlHelper.ShowDataGridViewRowDataNode( dataGridView1.Rows[i], selected.StructNodes[i] );
								dataGridView1.Rows[i].Cells[0].Value = selected.GetDisplayName( ) + "." + selected.StructNodes[i].GetDisplayName( );
								selected.StructNodes[i].DataUrl = selected.Name + "." + selected.StructNodes[i].Name;
							}
						}
					}
					else
					{
						HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, 1 );
						HslTechnologyControlHelper.ShowDataGridViewRowDataNode( dataGridView1.Rows[0], selected );
					}
				}
				else if (selected.DataDimension == Node.DataDimension.One)
				{
					// 结构体数组
					if (selected.DataType == Node.DataType.Struct && this.arrayIndex >= 0)
					{
						if (selected.StructNodes != null)
						{
							HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, selected.StructNodes.Length );
							for (int i = 0; i < selected.StructNodes.Length; i++)
							{
								HslTechnologyControlHelper.ShowDataGridViewRowDataNode( dataGridView1.Rows[i], selected.StructNodes[i] );
								dataGridView1.Rows[i].Cells[0].Value = selected.GetDisplayName( ) +
									$"[{this.arrayIndex}].{selected.StructNodes[i].GetDisplayName( )}";
								selected.StructNodes[i].DataUrl = selected.Name + $"[{this.arrayIndex}].{selected.StructNodes[i].Name}";
							}
						}
					}
					// 普通的数组的情况
					else
					{
						HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, selected.ArrayLength );
						for (int i = 0; i < selected.ArrayLength; i++)
						{
							dataGridView1.Rows[i].Cells[0].Value = selected.GetDisplayName( ) + $"[{i}]";
							dataGridView1.Rows[i].Cells[1].Value = string.Empty;
							dataGridView1.Rows[i].Cells[2].Value = selected.Unit;
							dataGridView1.Rows[i].Cells[3].Value = selected.DataType.ToString( );
							dataGridView1.Rows[i].Cells[4].Value = selected.AccessLevel.ToString( );
							dataGridView1.Rows[i].Cells[5].Value = selected.Description;
							dataGridView1.Rows[i].Tag = selected;
						}
					}
				}
				else // 二维及以上数组不进行显示
				{
					HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, 1 );
					HslTechnologyControlHelper.ShowDataGridViewRowDataNode( dataGridView1.Rows[0], selected );
				}
			}
		}

		/// <summary>
		/// 将实际的数据显示到表格里
		/// </summary>
		/// <param name="json">实际的设备数据的JSON格式</param>
		public void RenderMachineData( JObject json )
		{
			if (selectDataNode == null)
			{
				HslTechnologyControlHelper.RenderJObjectToDataGrid( dataGridView1, json ); // 显示全部的数据信息
			}
			else if (selectDataNode.DataDimension == Node.DataDimension.Scalar)
			{
				if (dataGridView1.RowCount == 0) return;
				if (selectDataNode.DataType == Node.DataType.Struct) // 处理结构体
				{
					if (json.ContainsKey( selectDataNode.Name ))
					{
						JObject token = (JObject)json[selectDataNode.Name];
						HslTechnologyControlHelper.RenderJObjectToDataGrid( dataGridView1, token );
					}
					else
					{
						// 如果选择的标签找不到，则直接把表格所有行设置为空
						for (int i = 0; i < dataGridView1.Rows.Count; i++)
						{
							dataGridView1.Rows[i].Cells[1].Value = HslTechnologyControlHelper.NullValue;
						}
					}
				}
				else
				{
					RenderJObjectToDataGrid( json, selectDataNode.Name ); // 显示除结构体之外的
				}
			}
			else if (selectDataNode.DataDimension == Node.DataDimension.One)
			{
				if (json.ContainsKey( selectDataNode.Name ))
				{
					if (selectDataNode.DataType == Node.DataType.Byte && json[selectDataNode.Name] is JValue jValue)
					{
						if (jValue.Type != JTokenType.Null)
						{
							byte[] value = jValue.Value<string>( ).ToHexBytes( );
							RenderJArrayToDataGrid( value ); // 普通的数组
						}
						else
						{
							byte[] array = null;
							RenderJArrayToDataGrid( array );
						}
					}
					else
					{
						JArray jArray = json[selectDataNode.Name] as JArray;
						if (jArray == null)
						{
							// 如果当前的标签为空的情况
							byte[] array = null;
							RenderJArrayToDataGrid( array );
							return;
						}

						if (selectDataNode.DataType == Node.DataType.Struct && this.arrayIndex >= 0) // 结构体数组
						{
							if (this.arrayIndex >= jArray.Count) return;
							HslTechnologyControlHelper.RenderJObjectToDataGrid( dataGridView1, jArray[this.arrayIndex] as JObject );
						}
						else
						{
							RenderJArrayToDataGrid( jArray ); // 普通的数组
						}
					}
				}
				else
				{
					for (int i = 0; i < dataGridView1.RowCount; i++)
					{
						dataGridView1.Rows[i].Cells[1].Value = HslTechnologyControlHelper.NullValue;
					}
				}
			}
			else
			{
				RenderJObjectToDataGrid( json, selectDataNode.Name ); // 二维数据暂时按照标量处理
			}
		}

		#endregion

		#region Private Method

		private void RenderJObjectToDataGrid( JObject json, string dataName )
		{
			if (dataGridView1.RowCount > 0)
			{
				if (json.ContainsKey( dataName ))
				{
					if (json.Property( dataName ).Value.Type == JTokenType.Null)
					{
						dataGridView1.Rows[0].Cells[1].Value = HslTechnologyControlHelper.NullValue;
					}
					else
					{
						dataGridView1.Rows[0].Cells[1].Value = json.Property( dataName ).Value;
					}
				}
				else
					dataGridView1.Rows[0].Cells[1].Value = HslTechnologyControlHelper.NullValue;
			}
		}

		private void RenderJArrayToDataGrid( JArray array )
		{
			if (array.Type != JTokenType.Null)
			{
				if (dataGridView1.RowCount != array.Count)
				{
					// 如果两者行数不一致，则重新调整显示的列表信息
					HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, array.Count );
					for (int i = 0; i < array.Count; i++)
					{
						dataGridView1.Rows[i].Cells[0].Value = this.selectDataNode.GetDisplayName( ) + $"[{i}]";
						dataGridView1.Rows[i].Cells[1].Value = string.Empty;
						dataGridView1.Rows[i].Cells[2].Value = this.selectDataNode.Unit;
						dataGridView1.Rows[i].Cells[3].Value = this.selectDataNode.DataType.ToString( );
						dataGridView1.Rows[i].Cells[4].Value = this.selectDataNode.AccessLevel.ToString( );
						dataGridView1.Rows[i].Cells[5].Value = this.selectDataNode.Description;
						dataGridView1.Rows[i].Tag = this.selectDataNode;
					}
				}
				for (int i = 0; i < dataGridView1.RowCount; i++)
				{
					if (i < array.Count)
					{
						dataGridView1.Rows[i].Cells[1].Value = array[i];
					}
				}
			}
			else
			{
				for (int i = 0; i < dataGridView1.RowCount; i++)
				{
					dataGridView1.Rows[i].Cells[1].Value = HslTechnologyControlHelper.NullValue;
				}
			}
		}

		private void RenderJArrayToDataGrid( byte[] array )
		{
			if (array == null)
			{
				for (int i = 0; i < dataGridView1.RowCount; i++)
				{
					dataGridView1.Rows[i].Cells[1].Value = HslTechnologyControlHelper.NullValue;
				}
				return;
			}
			if (dataGridView1.RowCount != array.Length)
			{
				// 如果两者行数不一致，则重新调整显示的列表信息
				HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, array.Length );
				for (int i = 0; i < array.Length; i++)
				{
					dataGridView1.Rows[i].Cells[0].Value = this.selectDataNode.GetDisplayName( ) + $"[{i}]";
					dataGridView1.Rows[i].Cells[1].Value = string.Empty;
					dataGridView1.Rows[i].Cells[2].Value = this.selectDataNode.Unit;
					dataGridView1.Rows[i].Cells[3].Value = this.selectDataNode.DataType.ToString( );
					dataGridView1.Rows[i].Cells[4].Value = this.selectDataNode.AccessLevel.ToString( );
					dataGridView1.Rows[i].Cells[5].Value = this.selectDataNode.Description;
					dataGridView1.Rows[i].Tag = this.selectDataNode;
				}
			}
			for (int i = 0; i < dataGridView1.RowCount; i++)
			{
				if (i < array.Length)
				{
					dataGridView1.Rows[i].Cells[1].Value = array[i];
				}
			}
		}

		private void DataInfomationControl_Load( object sender, EventArgs e )
		{
			if (eventLoaded == false)
			{
				eventLoaded = true;
				
				this.dataGridView1.CellMouseDoubleClick += DataGridView1_CellMouseDoubleClick;
				// 控件总宽度935    915
				this.dataGridView1.Columns[0].Width = 155;
				this.dataGridView1.Columns[1].Width = 350;
				this.dataGridView1.Columns[2].Width = 60;
				this.dataGridView1.Columns[3].Width = 100;
				this.dataGridView1.Columns[4].Width = 100;
				this.dataGridView1.Columns[5].Width = 150;
				this.dataGridView1.SizeChanged += DataGridView1_SizeChanged;

				if (HslTechnologyControlHelper.ThemeKey == "Dark" && HslTechnologyControlHelper.Theme != null)
				{
					BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				}
			}
		}

		private void DataGridView1_SizeChanged( object sender, EventArgs e )
		{
			int offset = this.dataGridView1.Width - 935;
			this.dataGridView1.Columns[1].Width = 350 + 2 * offset / 3;
			this.dataGridView1.Columns[5].Width = 150 + offset / 3;
		}

		private void DataGridView1_CellMouseDoubleClick( object sender, DataGridViewCellMouseEventArgs e )
		{
			// 当点击了数据值的时候，支持进行数据的写入操作
			if(e.ColumnIndex == 1 && e.RowIndex >= 0)
			{
				if (dataGridView1.Rows[e.RowIndex].Tag is ScalarDataNode scalarDataNode)
				{
					if(scalarDataNode.AccessLevel != Node.AccessLevel.ReadWrite)
					{
						MessageBox.Show( "当前的值数据无法写入，不具备写入的权限！" );
						return;
					}

					string valueNode = deviceUrl + "/" + (string.IsNullOrEmpty( scalarDataNode.DataUrl ) ? scalarDataNode.Name : scalarDataNode.DataUrl);
					onValueCellMouseDoubleClick?.Invoke( scalarDataNode, valueNode, dataGridView1.Rows[e.RowIndex].Cells[1].Value.ToString( ) );
				}
			}
			else if (e.ColumnIndex == 0 && e.RowIndex >= 0)
			{
				if (dataGridView1.Rows[e.RowIndex].Tag is ScalarDataNode scalarDataNode)
				{
					string valueNode = deviceUrl + "/" + (string.IsNullOrEmpty( scalarDataNode.DataUrl ) ? scalarDataNode.Name : scalarDataNode.DataUrl);
					onValueNameCellMouseDoubleClick?.Invoke( scalarDataNode, valueNode, dataGridView1.Rows[e.RowIndex].Cells[1].Value.ToString( ) );
				}
			}
		}

		#endregion

		public delegate void OnValueCellMouseDoubleClickDelegate( ScalarDataNode scalarDataNode, string valueNode, string valueOld );

		/// <summary>
		/// 当值内容双击之后触发
		/// </summary>
		public event OnValueCellMouseDoubleClickDelegate onValueCellMouseDoubleClick;

		/// <summary>
		/// 当数据名称内容双击之后触发
		/// </summary>
		public event OnValueCellMouseDoubleClickDelegate onValueNameCellMouseDoubleClick;

		private ScalarDataNode[] scalarDataNodes;
		private ScalarDataNode selectDataNode;
		private string deviceUrl = string.Empty;
		private int arrayIndex = -1;                                                // 当为结构体数组时，队列的索引值
		private bool eventLoaded = false;
	}
}
