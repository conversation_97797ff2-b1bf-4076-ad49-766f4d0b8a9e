
using HslTechnology.Edge.Controls.Basic;

namespace HslTechnology.Edge.Controls.Machine
{
	partial class DataInfomationControl
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.label7 = new System.Windows.Forms.Label();
			this.dataGridView1 = new HslTechnology.Edge.Controls.Basic.HslDataGridView();
			this.textBox2 = new HslTechnology.Edge.Controls.Basic.HslTextBox();
			this.ValueName = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Value = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.单位 = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.DataType = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Access = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Description = new System.Windows.Forms.DataGridViewTextBoxColumn();
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
			this.SuspendLayout();
			// 
			// label7
			// 
			this.label7.AutoSize = true;
			this.label7.Location = new System.Drawing.Point(4, 5);
			this.label7.Name = "label7";
			this.label7.Size = new System.Drawing.Size(68, 17);
			this.label7.TabIndex = 8;
			this.label7.Text = "节点名称：";
			// 
			// dataGridView1
			// 
			this.dataGridView1.AllowUserToAddRows = false;
			this.dataGridView1.AllowUserToDeleteRows = false;
			this.dataGridView1.AllowUserToResizeRows = false;
			this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.dataGridView1.BackgroundColor = System.Drawing.Color.White;
			this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ValueName,
            this.Value,
            this.单位,
            this.DataType,
            this.Access,
            this.Description});
			this.dataGridView1.Location = new System.Drawing.Point(0, 31);
			this.dataGridView1.Name = "dataGridView1";
			this.dataGridView1.ReadOnly = true;
			this.dataGridView1.RowHeadersVisible = false;
			this.dataGridView1.RowTemplate.Height = 23;
			this.dataGridView1.Size = new System.Drawing.Size(805, 242);
			this.dataGridView1.TabIndex = 10;
			// 
			// textBox2
			// 
			this.textBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox2.BackColor = System.Drawing.Color.White;
			this.textBox2.Location = new System.Drawing.Point(73, 2);
			this.textBox2.Name = "textBox2";
			this.textBox2.ReadOnly = true;
			this.textBox2.Size = new System.Drawing.Size(732, 23);
			this.textBox2.TabIndex = 9;
			// 
			// ValueName
			// 
			this.ValueName.HeaderText = "数据名称";
			this.ValueName.Name = "ValueName";
			this.ValueName.ReadOnly = true;
			this.ValueName.Width = 155;
			// 
			// Value
			// 
			this.Value.HeaderText = "值(Value)";
			this.Value.Name = "Value";
			this.Value.ReadOnly = true;
			this.Value.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
			this.Value.Width = 350;
			// 
			// 单位
			// 
			this.单位.HeaderText = "Unit";
			this.单位.Name = "单位";
			this.单位.ReadOnly = true;
			this.单位.Width = 60;
			// 
			// DataType
			// 
			this.DataType.HeaderText = "类型";
			this.DataType.Name = "DataType";
			this.DataType.ReadOnly = true;
			// 
			// Access
			// 
			this.Access.HeaderText = "权限";
			this.Access.Name = "Access";
			this.Access.ReadOnly = true;
			// 
			// Description
			// 
			this.Description.HeaderText = "描述";
			this.Description.Name = "Description";
			this.Description.ReadOnly = true;
			this.Description.Width = 150;
			// 
			// DataInfomationControl
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.White;
			this.Controls.Add(this.label7);
			this.Controls.Add(this.dataGridView1);
			this.Controls.Add(this.textBox2);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "DataInfomationControl";
			this.Size = new System.Drawing.Size(805, 273);
			this.Load += new System.EventHandler(this.DataInfomationControl_Load);
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.Label label7;
        private HslDataGridView dataGridView1;
		private HslTextBox textBox2;
		private System.Windows.Forms.DataGridViewTextBoxColumn ValueName;
		private System.Windows.Forms.DataGridViewTextBoxColumn Value;
		private System.Windows.Forms.DataGridViewTextBoxColumn 单位;
		private System.Windows.Forms.DataGridViewTextBoxColumn DataType;
		private System.Windows.Forms.DataGridViewTextBoxColumn Access;
		private System.Windows.Forms.DataGridViewTextBoxColumn Description;
	}
}
