using HslTechnology.Edge.Plugins;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class EdgePluginsControl : UserControl
	{
		public EdgePluginsControl( )
		{
			InitializeComponent( );
		}

		/// <summary>
		/// 显示当前的插件的定义信息
		/// </summary>
		/// <param name="plugins">插件定义信息</param>
		public void RenderPluginsDefinition( PluginsDefinition plugins )
		{
			textBox_plugins_name.Text = plugins.DllName;
			textBox_plugins_company.Text = plugins.Company;
			textBox_plugins_version.Text = plugins.Version.ToString( );
			textBox_plugins_releaseDate.Text = plugins.ReleaseData.ToString( );
			textBox_plugins_framework.Text = plugins.Framework;
			if (plugins.Icon16 != null)
				pictureBox_plugins_image.Image = GetImageFromBytes( plugins.Icon16 );
			textBox_plugins_http.Text = plugins.Http;
			textBox_plugins_desc.Text = plugins.Description;
		}

		public static Image GetImageFromBytes( byte[] buffer )
		{
			MemoryStream ms = new MemoryStream( buffer );
			Image image = Image.FromStream( ms );
			ms.Dispose( );
			return image;
		}
	}
}
