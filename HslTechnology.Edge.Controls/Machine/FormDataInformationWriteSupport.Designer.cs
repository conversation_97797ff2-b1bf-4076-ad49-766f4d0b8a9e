
namespace HslTechnology.Edge.Controls.Machine
{
	partial class FormDataInformationWriteSupport
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent( )
		{
			this.label1 = new System.Windows.Forms.Label();
			this.textBox1 = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.textBox2 = new System.Windows.Forms.TextBox();
			this.label3 = new System.Windows.Forms.Label();
			this.button1 = new System.Windows.Forms.Button();
			this.hslBadge1 = new HslControls.HslBadge();
			this.SuspendLayout();
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(12, 18);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(68, 17);
			this.label1.TabIndex = 0;
			this.label1.Text = "数据节点：";
			// 
			// textBox1
			// 
			this.textBox1.BackColor = System.Drawing.Color.White;
			this.textBox1.Location = new System.Drawing.Point(86, 15);
			this.textBox1.Name = "textBox1";
			this.textBox1.ReadOnly = true;
			this.textBox1.Size = new System.Drawing.Size(407, 23);
			this.textBox1.TabIndex = 1;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(12, 51);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(56, 17);
			this.label2.TabIndex = 2;
			this.label2.Text = "数据值：";
			// 
			// textBox2
			// 
			this.textBox2.Location = new System.Drawing.Point(86, 48);
			this.textBox2.Name = "textBox2";
			this.textBox2.Size = new System.Drawing.Size(407, 23);
			this.textBox2.TabIndex = 3;
			// 
			// label3
			// 
			this.label3.AutoSize = true;
			this.label3.ForeColor = System.Drawing.Color.Green;
			this.label3.Location = new System.Drawing.Point(83, 75);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(324, 17);
			this.label3.TabIndex = 4;
			this.label3.Text = "如果是数组的话，使用[]，数据之间用 \',\' 分割，例如 [1,2,3]";
			// 
			// button1
			// 
			this.button1.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button1.Location = new System.Drawing.Point(-215, 112);
			this.button1.Name = "button1";
			this.button1.Size = new System.Drawing.Size(131, 33);
			this.button1.TabIndex = 5;
			this.button1.Text = "写入";
			this.button1.UseVisualStyleBackColor = true;
			// 
			// hslBadge1
			// 
			this.hslBadge1.AutoSize = false;
			this.hslBadge1.ButtonHoverStyle = true;
			this.hslBadge1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.hslBadge1.LeftBackground = System.Drawing.Color.FromArgb(((int)(((byte)(79)))), ((int)(((byte)(79)))), ((int)(((byte)(79)))));
			this.hslBadge1.Location = new System.Drawing.Point(182, 112);
			this.hslBadge1.Name = "hslBadge1";
			this.hslBadge1.RightBackground = System.Drawing.Color.FromArgb(((int)(((byte)(2)))), ((int)(((byte)(115)))), ((int)(((byte)(179)))));
			this.hslBadge1.RightText = "写入";
			this.hslBadge1.Size = new System.Drawing.Size(138, 33);
			this.hslBadge1.TabIndex = 6;
			this.hslBadge1.Click += new System.EventHandler(this.hslBadge1_Click);
			// 
			// FormDataInformationWriteSupport
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CancelButton = this.button1;
			this.ClientSize = new System.Drawing.Size(506, 157);
			this.Controls.Add(this.hslBadge1);
			this.Controls.Add(this.button1);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.textBox2);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.textBox1);
			this.Controls.Add(this.label1);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "FormDataInformationWriteSupport";
			this.ShowIcon = false;
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "数据写入操作";
			this.Load += new System.EventHandler(this.FormDataInformationWriteSupport_Load);
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TextBox textBox1;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox textBox2;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Button button1;
		private HslControls.HslBadge hslBadge1;
	}
}