
using HslTechnology.Edge.Controls.Basic;

namespace HslTechnology.Edge.Controls.Machine
{
	partial class DeviceOfflineControl
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.textBox1 = new HslTextBox( );
			this.label12 = new System.Windows.Forms.Label();
			this.label1 = new System.Windows.Forms.Label();
			this.textBox2 = new HslTextBox( );
			this.textBox3 = new HslTextBox( );
			this.label2 = new System.Windows.Forms.Label();
			this.dataGridView1 = new HslDataGridView( );
			this.ValueName = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Value = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.DataType = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Access = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Description = new System.Windows.Forms.DataGridViewTextBoxColumn();
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
			this.SuspendLayout();
			// 
			// textBox1
			// 
			this.textBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox1.Location = new System.Drawing.Point(69, 3);
			this.textBox1.Name = "textBox1";
			this.textBox1.Size = new System.Drawing.Size(763, 23);
			this.textBox1.TabIndex = 26;
			// 
			// label12
			// 
			this.label12.AutoSize = true;
			this.label12.Location = new System.Drawing.Point(4, 6);
			this.label12.Name = "label12";
			this.label12.Size = new System.Drawing.Size(59, 17);
			this.label12.TabIndex = 25;
			this.label12.Text = "设备url：";
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(4, 35);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(68, 17);
			this.label1.TabIndex = 27;
			this.label1.Text = "离线次数：";
			// 
			// textBox2
			// 
			this.textBox2.Location = new System.Drawing.Point(69, 32);
			this.textBox2.Name = "textBox2";
			this.textBox2.Size = new System.Drawing.Size(140, 23);
			this.textBox2.TabIndex = 28;
			// 
			// textBox3
			// 
			this.textBox3.Location = new System.Drawing.Point(312, 32);
			this.textBox3.Name = "textBox3";
			this.textBox3.Size = new System.Drawing.Size(182, 23);
			this.textBox3.TabIndex = 30;
			// 
			// label2
			// 
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(228, 35);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(80, 17);
			this.label2.TabIndex = 29;
			this.label2.Text = "离线总时长：";
			// 
			// dataGridView1
			// 
			this.dataGridView1.AllowUserToAddRows = false;
			this.dataGridView1.AllowUserToDeleteRows = false;
			this.dataGridView1.AllowUserToResizeRows = false;
			this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.dataGridView1.BackgroundColor = System.Drawing.Color.White;
			this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ValueName,
            this.Value,
            this.DataType,
            this.Access,
            this.Description});
			this.dataGridView1.Location = new System.Drawing.Point(3, 61);
			this.dataGridView1.Name = "dataGridView1";
			this.dataGridView1.ReadOnly = true;
			this.dataGridView1.RowHeadersVisible = false;
			this.dataGridView1.RowTemplate.Height = 23;
			this.dataGridView1.Size = new System.Drawing.Size(829, 253);
			this.dataGridView1.TabIndex = 32;
			// 
			// ValueName
			// 
			this.ValueName.HeaderText = "索引";
			this.ValueName.Name = "ValueName";
			this.ValueName.ReadOnly = true;
			this.ValueName.Width = 155;
			// 
			// Value
			// 
			this.Value.HeaderText = "开始时间";
			this.Value.Name = "Value";
			this.Value.ReadOnly = true;
			this.Value.Width = 210;
			// 
			// DataType
			// 
			this.DataType.HeaderText = "结束时间";
			this.DataType.Name = "DataType";
			this.DataType.ReadOnly = true;
			this.DataType.Width = 200;
			// 
			// Access
			// 
			this.Access.HeaderText = "持续时间";
			this.Access.Name = "Access";
			this.Access.ReadOnly = true;
			this.Access.Width = 200;
			// 
			// Description
			// 
			this.Description.HeaderText = "备注";
			this.Description.Name = "Description";
			this.Description.ReadOnly = true;
			this.Description.Width = 150;
			// 
			// DeviceOfflineControl
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.White;
			this.Controls.Add(this.dataGridView1);
			this.Controls.Add(this.textBox3);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.textBox2);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.textBox1);
			this.Controls.Add(this.label12);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "DeviceOfflineControl";
			this.Size = new System.Drawing.Size(835, 317);
			this.Load += new System.EventHandler(this.DeviceOfflineControl_Load);
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private HslTextBox textBox1;
		private System.Windows.Forms.Label label12;
		private System.Windows.Forms.Label label1;
		private HslTextBox textBox2;
		private HslTextBox textBox3;
		private System.Windows.Forms.Label label2;
		private HslDataGridView dataGridView1;
		private System.Windows.Forms.DataGridViewTextBoxColumn ValueName;
		private System.Windows.Forms.DataGridViewTextBoxColumn Value;
		private System.Windows.Forms.DataGridViewTextBoxColumn DataType;
		private System.Windows.Forms.DataGridViewTextBoxColumn Access;
		private System.Windows.Forms.DataGridViewTextBoxColumn Description;
	}
}
