using HslTechnology.Edge.DataBusiness.Time;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class DeviceOfflineControl : UserControl
	{
		public DeviceOfflineControl( )
		{
			InitializeComponent( );
		}

		public void ThemeDark( )
		{
			this.textBox1.BorderStyle = BorderStyle.FixedSingle;
			this.textBox2.BorderStyle = BorderStyle.FixedSingle;
			this.textBox3.BorderStyle = BorderStyle.FixedSingle;
			this.dataGridView1.EnableHeadersVisualStyles = false;
			this.dataGridView1.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb( 64, 64, 64 );
			this.dataGridView1.ColumnHeadersDefaultCellStyle.ForeColor = Color.Cyan;
		}

		private void DeviceOfflineControl_Load( object sender, EventArgs e )
		{
			BackColor = Color.Transparent;
		}


		public void RenderDevicePath( string device )
		{
			textBox1.Text = device;
		}

		private string dateTimeFormat = "yyyy-MM-dd HH:mm:ss";

		/// <summary>
		/// 将实际的数据显示到表格里
		/// </summary>
		/// <param name="json">实际的设备数据的JSON格式</param>
		public void RenderMachineOffline( TimeConsume[] consumes )
		{
			if (consumes == null) consumes = new TimeConsume[0];
			textBox2.Text = consumes.Length.ToString( );
			HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, consumes.Length );

			double seconds = 0;
			for (int i = 0; i < consumes.Length; i++)
			{
				dataGridView1.Rows[i].Cells[0].Value = (i + 1).ToString( );
				dataGridView1.Rows[i].Cells[1].Value = consumes[i].StartTime.ToString( dateTimeFormat );
				dataGridView1.Rows[i].Cells[2].Value = consumes[i].FinishTime.ToString( dateTimeFormat );
				dataGridView1.Rows[i].Cells[3].Value = HslTechnologyHelper.GetTimeSpanText( consumes[i].FinishTime - consumes[i].StartTime );
				seconds += (consumes[i].FinishTime - consumes[i].StartTime).TotalSeconds;
			}
			textBox3.Text = seconds.ToString( "F0" );
		}
	}
}
