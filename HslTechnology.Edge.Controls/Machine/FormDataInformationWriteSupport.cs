using HslTechnology.Edge.Node.Render;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class FormDataInformationWriteSupport : HslForm
	{
		public FormDataInformationWriteSupport( )
		{
			InitializeComponent( );
		}

		public void SetDataNode( ScalarDataNode scalarDataNode, string valueNode, string valueOld )
		{
			this.scalarDataNode = scalarDataNode;
			this.valueNode = valueNode;
			textBox1.Text = valueNode;
			textBox2.Text = valueOld;
		}

		public string GetDataValue( ) => textBox2.Text;




		private ScalarDataNode scalarDataNode;
		private string valueNode;

		private void FormDataInformationWriteSupport_Load( object sender, EventArgs e )
		{
			Shown += FormDataInformationWriteSupport_Shown;
		}

		private void FormDataInformationWriteSupport_Shown( object sender, EventArgs e )
		{
			textBox2.Focus( );
		}

		private void hslBadge1_Click( object sender, EventArgs e )
		{
			// 根据类型，检测输入是否合法
			try
			{
				scalarDataNode.CheckInputStringLegal( textBox2.Text );
				DialogResult = DialogResult.OK;
			}
			catch (Exception ex)
			{
				MessageBox.Show( $"当前的输入不合法，需要输入字符串可转换类型 {scalarDataNode.GetDataTypeText( )} 的值！" + Environment.NewLine +
					"原因：" + ex.Message, "写入异常", MessageBoxButtons.OK, MessageBoxIcon.Error );
			}
		}
	}
}
