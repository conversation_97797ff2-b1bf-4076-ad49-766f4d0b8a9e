using HslTechnology.Edge.DataBusiness.Time;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class RuntimeItemControl : UserControl
	{
		public RuntimeItemControl( )
		{
			InitializeComponent( );
		}

		private void RuntimeItemControl_Load( object sender, EventArgs e )
		{

		}

		public void RenderRuntime( EdgeTimeConsume edgeTimeConsume )
		{
			if ( edgeTimeConsume == null)
			{
				label_start.ForeColor  = Color.LightGray;
				label_finish.ForeColor = Color.LightGray;
				label_keep.ForeColor   = Color.LightGray;
				label_status.BackColor = Color.LightGray;
				return;
			}

			label_start.Text = "开始：" + edgeTimeConsume.StartTime.ToEdgeString( );
			label_message.Text = "消息：" + edgeTimeConsume.Message;
			if (edgeTimeConsume.Quit != 2)
			{
				label_finish.Text = "结束：" + edgeTimeConsume.FinishTime.ToEdgeString( );
				label_keep.Text = "运行：" + GetTimeSpan( edgeTimeConsume.FinishTime - edgeTimeConsume.StartTime);
				if (edgeTimeConsume.Quit == 0)
				{
					label_status.Text       = "ABORT";
					label_status.BackColor  = Color.Tomato;
					label_start.ForeColor   = Color.DimGray;
					label_finish.ForeColor  = Color.DimGray;
					label_keep.ForeColor    = Color.DimGray;
					label_message.ForeColor = Color.DimGray;
				}
				else
				{
					label_status.Text       = "MANUAL";
					label_status.BackColor  = Color.DodgerBlue;
					label_start.ForeColor   = Color.DimGray;
					label_finish.ForeColor  = Color.DimGray;
					label_keep.ForeColor    = Color.DimGray;
					label_message.ForeColor = Color.DimGray;
				}
			}
			else
			{
				label_keep.Text         = "运行：" + GetTimeSpan( edgeTimeConsume.FinishTime - edgeTimeConsume.StartTime);
				label_status.Text       = "RUNNING";
				label_status.BackColor  = Color.MediumSeaGreen;
				label_start.ForeColor   = Color.Black;
				label_finish.ForeColor  = Color.Black;
				label_keep.ForeColor    = Color.Black;
				label_message.ForeColor = Color.Black;
			}
		}

		private string GetTimeSpan( TimeSpan ts )
		{
			if (ts == null) return string.Empty;
			if (ts.TotalDays < 1) return ts.ToString( );
			return ts.Days + " 天 " + (ts - TimeSpan.FromDays( ts.Days )).ToString( );
		}

		private void RuntimeItemControl_Paint( object sender, PaintEventArgs e )
		{
			e.Graphics.DrawRectangle(Pens.LightGray, 0, 0, Width - 1, Height - 1);
		}
	}
}
