
using HslTechnology.Edge.Controls.Basic;

namespace HslTechnology.Edge.Controls.Machine
{
	partial class DeviceInfoControl
	{
		/// <summary> 
		/// 必需的设计器变量。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 清理所有正在使用的资源。
		/// </summary>
		/// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
		protected override void Dispose( bool disposing )
		{
			if (disposing && (components != null))
			{
				components.Dispose( );
			}
			base.Dispose( disposing );
		}

		#region 组件设计器生成的代码

		/// <summary> 
		/// 设计器支持所需的方法 - 不要修改
		/// 使用代码编辑器修改此方法的内容。
		/// </summary>
		private void InitializeComponent( )
		{
			this.textBox1 = new HslTechnology.Edge.Controls.Basic.HslTextBox();
			this.label12 = new System.Windows.Forms.Label();
			this.dataGridView1 = new HslTechnology.Edge.Controls.Basic.HslDataGridView();
			this.ValueName = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Value = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Column_unit = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.DataType = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Access = new System.Windows.Forms.DataGridViewTextBoxColumn();
			this.Description = new System.Windows.Forms.DataGridViewTextBoxColumn();
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
			this.SuspendLayout();
			// 
			// textBox1
			// 
			this.textBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.textBox1.Location = new System.Drawing.Point(66, 3);
			this.textBox1.Name = "textBox1";
			this.textBox1.Size = new System.Drawing.Size(736, 23);
			this.textBox1.TabIndex = 24;
			// 
			// label12
			// 
			this.label12.AutoSize = true;
			this.label12.Location = new System.Drawing.Point(3, 6);
			this.label12.Name = "label12";
			this.label12.Size = new System.Drawing.Size(59, 17);
			this.label12.TabIndex = 23;
			this.label12.Text = "设备url：";
			// 
			// dataGridView1
			// 
			this.dataGridView1.AllowUserToAddRows = false;
			this.dataGridView1.AllowUserToDeleteRows = false;
			this.dataGridView1.AllowUserToResizeRows = false;
			this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.dataGridView1.BackgroundColor = System.Drawing.Color.White;
			this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
			this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ValueName,
            this.Value,
            this.Column_unit,
            this.DataType,
            this.Access,
            this.Description});
			this.dataGridView1.Location = new System.Drawing.Point(2, 31);
			this.dataGridView1.Name = "dataGridView1";
			this.dataGridView1.ReadOnly = true;
			this.dataGridView1.RowHeadersVisible = false;
			this.dataGridView1.RowTemplate.Height = 23;
			this.dataGridView1.Size = new System.Drawing.Size(801, 242);
			this.dataGridView1.TabIndex = 26;
			// 
			// ValueName
			// 
			this.ValueName.HeaderText = "数据名称";
			this.ValueName.Name = "ValueName";
			this.ValueName.ReadOnly = true;
			this.ValueName.Width = 155;
			// 
			// Value
			// 
			this.Value.HeaderText = "值(Value)";
			this.Value.Name = "Value";
			this.Value.ReadOnly = true;
			this.Value.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
			this.Value.Width = 350;
			// 
			// Column_unit
			// 
			this.Column_unit.HeaderText = "Unit";
			this.Column_unit.Name = "Column_unit";
			this.Column_unit.ReadOnly = true;
			this.Column_unit.Width = 60;
			// 
			// DataType
			// 
			this.DataType.HeaderText = "类型";
			this.DataType.Name = "DataType";
			this.DataType.ReadOnly = true;
			// 
			// Access
			// 
			this.Access.HeaderText = "权限";
			this.Access.Name = "Access";
			this.Access.ReadOnly = true;
			// 
			// Description
			// 
			this.Description.HeaderText = "描述";
			this.Description.Name = "Description";
			this.Description.ReadOnly = true;
			this.Description.Width = 150;
			// 
			// DeviceInfoControl
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.BackColor = System.Drawing.Color.White;
			this.Controls.Add(this.dataGridView1);
			this.Controls.Add(this.textBox1);
			this.Controls.Add(this.label12);
			this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
			this.Name = "DeviceInfoControl";
			this.Size = new System.Drawing.Size(805, 273);
			this.Load += new System.EventHandler(this.DeviceInfoControl_Load);
			((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private HslTextBox textBox1;
		private System.Windows.Forms.Label label12;
		private HslDataGridView dataGridView1;
		private System.Windows.Forms.DataGridViewTextBoxColumn ValueName;
		private System.Windows.Forms.DataGridViewTextBoxColumn Value;
		private System.Windows.Forms.DataGridViewTextBoxColumn Column_unit;
		private System.Windows.Forms.DataGridViewTextBoxColumn DataType;
		private System.Windows.Forms.DataGridViewTextBoxColumn Access;
		private System.Windows.Forms.DataGridViewTextBoxColumn Description;
	}
}
