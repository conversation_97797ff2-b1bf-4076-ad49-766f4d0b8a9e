using HslTechnology.Edge.Reflection;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class DeviceMethodControl : UserControl
	{
		public DeviceMethodControl( )
		{
			InitializeComponent( );
		}


		public void ThemeDark( )
		{
			this.textBox1.BorderStyle = BorderStyle.FixedSingle;
			this.dataGridView1.EnableHeadersVisualStyles = false;
			this.dataGridView1.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb( 64, 64, 64 );
			this.dataGridView1.ColumnHeadersDefaultCellStyle.ForeColor = Color.Cyan;
		}

		public void RenderDevicePath( string device )
		{
			textBox1.Text = device;
		}

		/// <summary>
		/// 将实际的数据显示到表格里
		/// </summary>
		/// <param name="json">实际的设备数据的JSON格式</param>
		public void RenderMachineMethod( MethodRpcInfo[] methodRpcInfos )
		{
			HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, methodRpcInfos.Length );
			for(int i = 0; i < methodRpcInfos.Length; i++)
			{
				MethodRpcInfo methodRpc = methodRpcInfos[i];
				dataGridView1.Rows[i].Cells[0].Value = methodRpc.RpcApiInfo.ApiTopic;
				dataGridView1.Rows[i].Cells[1].Value = methodRpc.RpcApiInfo.MethodSignature;
				dataGridView1.Rows[i].Cells[2].Value = methodRpc.RpcApiInfo.Description;
				dataGridView1.Rows[i].Tag = methodRpc;
			}
		}



		private void DeviceMethodControl_Load( object sender, EventArgs e )
		{
			BackColor = Color.Transparent;
			this.dataGridView1.CellMouseDoubleClick += DataGridView1_CellMouseDoubleClick;
		}

		private void DataGridView1_CellMouseDoubleClick( object sender, DataGridViewCellMouseEventArgs e )
		{
			if(dataGridView1.SelectedRows.Count > 0)
			{
				if(dataGridView1.SelectedRows[0].Tag is MethodRpcInfo rpc)
				{
					onMethodCellMouseDoubleClick?.Invoke( rpc );
				}
			}
		}


		public delegate void OnMethodCellMouseDoubleClickDelegate( MethodRpcInfo rpc );

		/// <summary>
		/// 当值内容双击之后触发
		/// </summary>
		public event OnMethodCellMouseDoubleClickDelegate onMethodCellMouseDoubleClick;

	}
}
