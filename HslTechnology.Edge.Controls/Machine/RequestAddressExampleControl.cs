using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Device;

namespace HslTechnology.Edge.Controls.Machine
{
	public partial class RequestAddressExampleControl : UserControl
	{
		public RequestAddressExampleControl( )
		{
			InitializeComponent( );

			if (emptyImage == null)
			{
				emptyImage = new Bitmap( 16, 16 );
				Graphics g = Graphics.FromImage( emptyImage );
				g.<PERSON>( Color.Transparent );
				g.Dispose( );
			}
		}

		private void RequestAddressExampleControl_Load( object sender, EventArgs e )
		{
			DataGridView1_SizeChanged( sender, e );
			dataGridView1.CellMouseDoubleClick += DataGridView1_CellMouseDoubleClick;
			dataGridView1.SizeChanged += DataGridView1_SizeChanged;
		}

		private void DataGridView1_SizeChanged( object sender, EventArgs e )
		{
			dataGridView1.Columns[0].Width = 130;
			dataGridView1.Columns[1].Width = 165;
			dataGridView1.Columns[2].Width = 35;
			dataGridView1.Columns[3].Width = 35;
			dataGridView1.Columns[4].Width = dataGridView1.Width - 365 - 20;
		}

		private void DataGridView1_CellMouseDoubleClick( object sender, DataGridViewCellMouseEventArgs e )
		{
			if(e.RowIndex >= 0)
			{
				if(dataGridView1.Rows[e.RowIndex].Tag is DeviceAddressExample example)
				{
					OnDataGridViewRowMouseDoubleClick?.Invoke( example );
				}
			}
		}

		public delegate void OnDataGridViewRowMouseDoubleClickDelegate( DeviceAddressExample addressExample );

		/// <summary>
		/// 当用户双击了地址的某一行之后触发的事件
		/// </summary>
		public event OnDataGridViewRowMouseDoubleClickDelegate OnDataGridViewRowMouseDoubleClick;

		/// <summary>
		/// 设置当前的示例地址
		/// </summary>
		/// <param name="addressExamples">示例的地址信息</param>
		public void SetDeviceType( DeviceAddressExample[] addressExamples )
		{
			if (addressExamples == null) addressExamples = new DeviceAddressExample[0];

			HslTechnologyControlHelper.DataGridSpecifyRowCount( dataGridView1, addressExamples.Length );
			for (int i = 0; i < addressExamples.Length; i++)
			{
				dataGridView1.Rows[i].Cells[0].Value = addressExamples[i].AddressExample;
				if (!addressExamples[i].IsHeader)
				{
					if (string.IsNullOrEmpty( addressExamples[i].Unit ))
						dataGridView1.Rows[i].Cells[1].Value = addressExamples[i].AddressType;
					else
						dataGridView1.Rows[i].Cells[1].Value = $"{addressExamples[i].AddressType}({addressExamples[i].Unit})";
					dataGridView1.Rows[i].Cells[2].Value = addressExamples[i].BitEnable ? Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color : Properties.Resources.action_Cancel_16xLG;
					dataGridView1.Rows[i].Cells[3].Value = addressExamples[i].WordEnable ? Properties.Resources.StatusAnnotations_Complete_and_ok_16xLG_color : Properties.Resources.action_Cancel_16xLG;
					dataGridView1.Rows[i].Cells[4].Value = addressExamples[i].Mark;
					dataGridView1.Rows[i].Tag = addressExamples[i];
				}
				else
				{
					dataGridView1.Rows[i].Cells[2].Value = emptyImage;
					dataGridView1.Rows[i].Cells[3].Value = emptyImage;
					dataGridView1.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb( 160, 160, 160 );//Color.FromArgb(0xff, 0xa5, 0x50);
					dataGridView1.Rows[i].Cells[0].Style.Font = new Font( this.Font, FontStyle.Bold );
				}
			}
		}


		private static Bitmap emptyImage = null;
	}
}
