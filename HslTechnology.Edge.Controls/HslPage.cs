using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.EdgeViewer.Controls
{
	public class HslPage : WeifenLuo.WinFormsUI.Docking.DockContent
	{
		public HslPage( )
		{
			Load += HslPage_Load;
		}

		private void HslPage_Load( object sender, EventArgs e )
		{
			BackColor = Color.Honeydew;
		}

		/// <inheritdoc/>
		[Browsable( false )]
		[Description( "获取或设置控件的背景色" )]
		[DefaultValue( typeof( Color ), "Honeydew" )]
		[EditorBrowsable( EditorBrowsableState.Always )]
		public override Color BackColor
		{
			get => base.BackColor;
			set => base.BackColor = value;
		}

	}
}
