using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Drawing;

namespace HslTechnology.Edge.Controls.Basic
{
	public class HslDataGridView : DataGridView
	{

		protected override void InitLayout( )
		{
			base.InitLayout( );


			System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle( );
			dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb( 238, 238, 255 );
			this.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;

			if (HslTechnologyControlHelper.ThemeKey == "Dark" && HslTechnologyControlHelper.Theme != null)
			{
				BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				BackgroundColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				DefaultCellStyle.BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb( 64, 64, 74 );
				RowsDefaultCellStyle.BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				RowHeadersDefaultCellStyle.BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				//RowHeadersDefaultCellStyle.ForeColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Text;
			}


		}
	}
}
