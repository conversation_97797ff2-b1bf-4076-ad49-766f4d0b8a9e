using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Drawing;

namespace HslTechnology.Edge.Controls.Basic
{
	public class HslTextBox : TextBox
	{
		protected override void InitLayout( )
		{
			base.InitLayout( );

			if (HslTechnologyControlHelper.ThemeKey == "Dark" && HslTechnologyControlHelper.Theme != null)
			{
				BackColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Background;
				ForeColor = HslTechnologyControlHelper.Theme.ColorPalette.TabUnselected.Text;
				//BorderStyle = BorderStyle.None;
			}
		}

		protected override void OnPaint( PaintEventArgs e )
		{
			base.OnPaint( e );
			//if (HslTechnologyControlHelper.ThemeKey == "Dark" && HslTechnologyControlHelper.Theme != null)
			//{
			//	e.Graphics.DrawRectangle( Pens.<PERSON>mGray, 0, 0, Width - 1, Height - 1 );
			//}

		}
	}
}
