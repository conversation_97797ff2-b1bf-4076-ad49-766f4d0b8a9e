using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls.Basic
{
	public partial class FormInputText : HslForm
	{
		public FormInputText( string title, string text = null )
		{
			InitializeComponent( );
			this.Text = title;
			this.textBox1.Text = text;
		}

		/// <summary>
		/// 获取输入的文本信息
		/// </summary>
		public string InputText
		{
			get
			{
				return this.textBox1.Text;
			}
		}

		private void button1_Click( object sender, EventArgs e )
		{
			DialogResult = DialogResult.OK;
		}

		private void buttonOk1_Click( object sender, EventArgs e )
		{
			button1_Click( sender, e );
		}

		private void FormInputText_Load( object sender, EventArgs e )
		{

		}
	}
}
