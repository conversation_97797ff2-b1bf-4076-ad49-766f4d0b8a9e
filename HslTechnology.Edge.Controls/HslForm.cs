using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HslTechnology.Edge.Controls
{
	public class HslForm : Form
	{
		public HslForm( )
		{
			BackColor = Color.FromArgb( 255, 255, 236 );
		}

		/// <inheritdoc/>
		[Browsable( false )]
		[Description( "获取或设置控件的背景色" )]
		[DefaultValue( typeof( Color ), "[255, 255, 236]" )]
		[EditorBrowsable( EditorBrowsableState.Always )]
		public override Color BackColor
		{
			get => base.BackColor;
			set => base.BackColor = value;
		}


	}
}
