using System;
using HslCommunication;
using HslCommunication.Profinet.Melsec;
using System.Threading;

/**************************************************************************************
 * 
 *  构建虚拟的PLC环境，动态的数据变化，来模拟时间的场景，方便进行测试，开发调整
 * 
 * 启动2个虚拟的PLC，一个是三菱，一个是西门子
 * 
 *************************************************************************************/

namespace VirtualTestPlc
{

	class Program
	{

		static void Main( string[] args )
		{
			Console.WriteLine( "Hello World!" );

			MelsecMcNet plc = new MelsecMcNet( "118.24.36.220", 6000 );
			plc.SetPersistentConnection( );
			short i = 0;
			while (true)
			{
				Thread.Sleep( 1000 );
				plc.Write( "D0", i );
				i++;
			}
		}

	}
}
