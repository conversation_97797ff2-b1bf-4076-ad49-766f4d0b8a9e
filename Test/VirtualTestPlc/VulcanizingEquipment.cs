using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VirtualTestPlc
{
	/*******************************************************
	 * 
	 *  M0: 外温过低报警
	 *  M1: 内温过高报警
	 *  M2: 外压过低报警
	 *  M3: 内压过高报警
	 *  M4: 开始硫化15分钟未达到180℃报警
	 *  D0: 10倍存储的外温 波动范围 800-1200
	 *  D1: 10倍存储的内温 波动范围 1000 - 2000
	 *  D2: 100倍存储的外压 波动范围 0-300
	 *  D3: 100倍存储的内压 波动范围 200-500
	 *  D4: 设备状态 0:未硫化  1：硫化中 2：计划停机 3：故障停机 4：维修停机
	 *  D5: 产量，一般自增，每次硫化中结束时自增1
	 *  D6-D10: 规格 共计5个Word，一共10个字符
	 *  D11: 锁定，默认为0，可以生产，当上位机写入1时，设备锁定，无法生产，如果当前正在生产中，就等待生产完成后再锁定
	 *  
	 *******************************************************/


	/// <summary>
	/// 虚拟的硫化机设备
	/// </summary>
	public class VulcanizingEquipment
	{

	}
}
