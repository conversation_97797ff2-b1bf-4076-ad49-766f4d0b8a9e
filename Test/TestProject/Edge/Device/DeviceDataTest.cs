using HslTechnology.Edge.Device;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestProject.Edge.Device
{
	[TestClass]
	public class DeviceDataTest
	{
		[TestMethod]
		public void UnitTest1()
		{
			DeviceData deviceData = new DeviceData( );
			deviceData.SetSingleValue( "A", 123 );
			deviceData.SetSingleValue( "B", 1.1f );
			deviceData.SetSingleValue( "C", "ABC" );
			deviceData.SetArrayValue( "AA", new short[] { 1, 2, 3 } );
			deviceData.SetJsonValue( "AB", new double[] { 10, 20, 30 }, true );
			deviceData.StartStoreTemp( );
			deviceData.SetSingleValue( "A", 555 );
			deviceData.SetJsonValue( "B", 2.2f, isArray: false );
			deviceData.SetArrayValue( "AA", new short[] { 11, 12, 13 } );
			deviceData.FinishStoreTemp( );

			deviceData.RemoveValue( "A" );
			deviceData.StartStoreTemp( );
			deviceData.SetSingleValue( "A", 555 );
			deviceData.FinishStoreTemp( );
			int a = 1;
			Assert.AreEqual( 1, a );
		}
	}
}
