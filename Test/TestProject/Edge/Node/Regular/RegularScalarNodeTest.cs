using HslTechnology.Edge.Node.Regular;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestProject.Edge.Node.Regular
{
	[TestClass]
	public class RegularScalarNodeTest
	{
		[TestMethod]
		public void BcdTest( )
		{
			string[] bcds = new string[] { "0123456789", "712366", "86438642" };
			foreach (var bcd in bcds)
			{
				byte[] buffer = RegularScalarNode.GetBytesFromBCD( bcd, HslTechnology.Edge.Node.BCDFormat.C8421 );
				Assert.AreEqual( bcd, RegularScalarNode.GetBCDValue( buffer, HslTechnology.Edge.Node.BCDFormat.C8421 ) );

				buffer = RegularScalarNode.GetBytesFromBCD( bcd, HslTechnology.Edge.Node.BCDFormat.C5421 );
				Assert.AreEqual( bcd, RegularScalarNode.GetBCDValue( buffer, HslTechnology.Edge.Node.BCDFormat.C5421 ) );

				buffer = RegularScalarNode.GetBytesFromBCD( bcd, HslTechnology.Edge.Node.BCDFormat.C2421 );
				Assert.AreEqual( bcd, RegularScalarNode.GetBCDValue( buffer, HslTechnology.Edge.Node.BCDFormat.C2421 ) );

				buffer = RegularScalarNode.GetBytesFromBCD( bcd, HslTechnology.Edge.Node.BCDFormat.C3 );
				Assert.AreEqual( bcd, RegularScalarNode.GetBCDValue( buffer, HslTechnology.Edge.Node.BCDFormat.C3 ) );

				buffer = RegularScalarNode.GetBytesFromBCD( bcd, HslTechnology.Edge.Node.BCDFormat.Gray );
				Assert.AreEqual( bcd, RegularScalarNode.GetBCDValue( buffer, HslTechnology.Edge.Node.BCDFormat.Gray ) );
			}
		}
	}
}
