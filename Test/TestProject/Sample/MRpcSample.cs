using HslCommunication;
using HslCommunication.MQTT;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace TestProject.Sample
{
	class MRpcSample
	{
		public void ReadRpcSample1( )
		{
			MqttSyncClient mqtt = new MqttSyncClient( new MqttConnectionOptions( )
			{
				IpAddress = "127.0.0.1",
				Port = 521,
				Credentials = new MqttCredential( )
				{
					UserName = "admin",
					Password = "123456"
				},
				ConnectTimeout = 2000
			} );
			mqtt.SetPersistentConnection( ); // 设置长连接
			OperateResult<JObject> read = mqtt.ReadRpc<JObject>(
					"Edge/DeviceData", new { data = "工厂一/车间A/设备A01" } );
			if (read.IsSuccess) Console.WriteLine( read.Content.ToString( ) );

			mqtt.ConnectClose( );
		}
		public void ReadRpcSample2( )
		{
			MqttSyncClient mqtt = new MqttSyncClient( new MqttConnectionOptions( )
			{
				IpAddress = "127.0.0.1",
				Port = 521,
				Credentials = new MqttCredential( )
				{
					UserName = "admin",
					Password = "123456"
				},
				ConnectTimeout = 2000
			} );
			mqtt.SetPersistentConnection( ); // 设置长连接
			OperateResult<int> read = mqtt.ReadRpc<int>(
					"Edge/DeviceData", new { data = "工厂一/车间A/设备A01/产量" } );
			if (read.IsSuccess) Console.WriteLine( read.Content.ToString( ) );

			mqtt.ConnectClose( );
		}


		public void ReadRpcSample3( )
		{
			MqttSyncClient mqtt = new MqttSyncClient( new MqttConnectionOptions( )
			{
				IpAddress = "127.0.0.1",
				Port = 521,
				Credentials = new MqttCredential( )
				{
					UserName = "admin",
					Password = "123456"
				},
				ConnectTimeout = 2000
			} );
			mqtt.SetPersistentConnection( ); // 设置长连接
			OperateResult<string> read = mqtt.ReadRpc<string>(
					"Edge/WriteData", new { data = "工厂一/车间A/设备A01/产量", value = "100" } );
			if (read.IsSuccess) Console.WriteLine( read.Content.ToString( ) );

			mqtt.ConnectClose( );
		}

		public void ReadRpcSample4( )
		{
			MqttSyncClient mqtt = new MqttSyncClient( new MqttConnectionOptions( )
			{
				IpAddress = "127.0.0.1",
				Port = 521,
				Credentials = new MqttCredential( )
				{
					UserName = "admin",
					Password = "123456"
				},
				ConnectTimeout = 2000
			} );
			mqtt.SetPersistentConnection( ); // 设置长连接
			OperateResult<JArray> read = mqtt.ReadRpc<JArray>( "Business/Alarm/GetAlarms", new { data = "" } );
			if (read.IsSuccess) Console.WriteLine( read.Content.ToString( ) );

			mqtt.ConnectClose( );
		}


		public void ReadRpcSample5( )
		{
			MqttSyncClient mqtt = new MqttSyncClient( new MqttConnectionOptions( )
			{
				IpAddress = "127.0.0.1",
				Port = 521,
				Credentials = new MqttCredential( )
				{
					UserName = "admin",
					Password = "123456"
				},
				ConnectTimeout = 2000
			} );
			mqtt.SetPersistentConnection( ); // 设置长连接
			OperateResult<JObject> read = mqtt.ReadRpc<JObject>( "Edge/CallDeviceMethod", 
				new { data = "工厂一/车间A/Fanuc机器人设备", method = "ReadFanucData", parameterJson = "" } );
			if (read.IsSuccess) Console.WriteLine( read.Content.ToString( ) );

			mqtt.ConnectClose( );
		}
	}
}
