<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>WebApi测试教程</title>
    <script src="https://cdn.staticfile.org/jquery/1.10.2/jquery.min.js"></script>
</head>
<body>
    <h1>测试Webapi的内容</h1>
    <p>
        点击下方的按钮即可
    </p>
    <button onclick="GetPlcValue()">测试</button>
    <p>结果如下：</p>
    <p id="test1"></p>
    <script type="text/javascript">
        function GetPlcValue() {
            $.ajax({
                headers: {
                    'Authorization': 'Basic YWRtaW46MTIzNDU2'
                },
                type: 'post',
                async: false,
                url: 'http://127.0.0.1:522/工厂一/车间A/设备A01/ReadInt16',
                dataType: 'JSON',
                data: JSON.stringify({
                    'address': 'D0',
                }),
                success: function (result) {
                    console.log(result);
                    //$("#test1").text("Read Success, value is : " + result);
                    if (result.IsSuccess) {
                        $("#test1").text("Read Success, value is : " + result.Content);
                    }
                    else {
                        $("#test1").text("Read Failed, Message: " + result.Message);
                    }
                }
            });
        }
    </script>
</body>
</html>