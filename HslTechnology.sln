
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32014.148
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "HslTechnology.WebServer", "HslTechnology.WebServer\HslTechnology.WebServer.csproj", "{481366F8-BAE0-431D-AB38-A038C50B2CD9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{DA184F23-B345-493B-B9A9-3D6730F9131D}"
	ProjectSection(SolutionItems) = preProject
		Infomation.Md = Infomation.Md
		Plan.MD = Plan.MD
		question.txt = question.txt
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "HslTechnology.EdgeServer", "HslTechnology.EdgeServer\HslTechnology.EdgeServer.csproj", "{2AE31AE1-010D-445C-8672-2747A0AAC8B7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "HslTechnology.Edge", "HslTechnology.Edge\HslTechnology.Edge.csproj", "{4CF9A3FE-53E3-4A38-AC9E-E18D63E19CEB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HslTechnology.EdgeViewer", "HslTechnology.EdgeViewer\HslTechnology.EdgeViewer.csproj", "{34BF58E1-2FF8-416B-89DE-216138131FC2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttServerSample", "MqttServerSample\MqttServerSample.csproj", "{15576734-C724-4511-9D81-84C2E8E2F69B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Test", "Test", "{74A26776-465F-4287-AAD1-11FD8D15B422}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HslTechnology.Edge.Controls", "HslTechnology.Edge.Controls\HslTechnology.Edge.Controls.csproj", "{70246F24-52A6-472C-94C3-65880817D7B1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VirtualTestPlc", "Test\VirtualTestPlc\VirtualTestPlc.csproj", "{E63A380C-F44D-4C78-AE73-BB8F7A77B627}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "HslTechnology.EdgeAssist", "HslTechnology.EdgeAssist\HslTechnology.EdgeAssist.csproj", "{95AD5F73-8676-45B5-B53B-34093D80E916}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Plugins", "Plugins", "{8E090CCB-230C-4C61-B906-29F91C391FF0}"
	ProjectSection(SolutionItems) = preProject
		Info.txt = Info.txt
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "hugong.vulcanizer", "Plugins\hugong.vulcanizer\hugong.vulcanizer.csproj", "{013BCA71-4F74-4224-B921-600310858CFA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "hugong.opcda", "Plugins\hugong.opcda\hugong.opcda.csproj", "{823C39A8-5EFF-43AC-953C-B01314386A90}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TestProject", "Test\TestProject\TestProject.csproj", "{3D46F793-1CB9-419A-BF52-108072FFD74E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "hugong.delixi", "Plugins\hugong.delixi\hugong.delixi.csproj", "{04445830-8A5E-4D68-934B-C3E23914E11D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "hugong.modbus", "Plugins\hugong.modbus\hugong.modbus.csproj", "{E0BD1BC3-8BDC-483C-BD39-3D25037956B9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Foundation", "Foundation", "{0D63B686-**************-FC339BB1B4F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DynamicExpresso.Core", "Foundation\DynamicExpresso.Core\DynamicExpresso.Core.csproj", "{4CEA478D-74BF-46E0-B5DD-278AC418E244}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "hugong.opcuahelper", "Plugins\hugong.opcuahelper\hugong.opcuahelper.csproj", "{FF07B468-882C-43D4-906A-C4F5D62310CF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "hugong.melsec.mc", "Plugins\hugong.melsec.mc\hugong.melsec.mc.csproj", "{6AE9AEF0-A3F8-44CD-8FD3-2F8B232F186D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "tzedgeview", "tzedgeview\tzedgeview.csproj", "{5FE31F96-CFC6-48FE-AA64-34795076905E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{481366F8-BAE0-431D-AB38-A038C50B2CD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{481366F8-BAE0-431D-AB38-A038C50B2CD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{481366F8-BAE0-431D-AB38-A038C50B2CD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{481366F8-BAE0-431D-AB38-A038C50B2CD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{2AE31AE1-010D-445C-8672-2747A0AAC8B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2AE31AE1-010D-445C-8672-2747A0AAC8B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2AE31AE1-010D-445C-8672-2747A0AAC8B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2AE31AE1-010D-445C-8672-2747A0AAC8B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{4CF9A3FE-53E3-4A38-AC9E-E18D63E19CEB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4CF9A3FE-53E3-4A38-AC9E-E18D63E19CEB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4CF9A3FE-53E3-4A38-AC9E-E18D63E19CEB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4CF9A3FE-53E3-4A38-AC9E-E18D63E19CEB}.Release|Any CPU.Build.0 = Release|Any CPU
		{34BF58E1-2FF8-416B-89DE-216138131FC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34BF58E1-2FF8-416B-89DE-216138131FC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34BF58E1-2FF8-416B-89DE-216138131FC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34BF58E1-2FF8-416B-89DE-216138131FC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{15576734-C724-4511-9D81-84C2E8E2F69B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{15576734-C724-4511-9D81-84C2E8E2F69B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{15576734-C724-4511-9D81-84C2E8E2F69B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{15576734-C724-4511-9D81-84C2E8E2F69B}.Release|Any CPU.Build.0 = Release|Any CPU
		{70246F24-52A6-472C-94C3-65880817D7B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70246F24-52A6-472C-94C3-65880817D7B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70246F24-52A6-472C-94C3-65880817D7B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70246F24-52A6-472C-94C3-65880817D7B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{E63A380C-F44D-4C78-AE73-BB8F7A77B627}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E63A380C-F44D-4C78-AE73-BB8F7A77B627}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E63A380C-F44D-4C78-AE73-BB8F7A77B627}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E63A380C-F44D-4C78-AE73-BB8F7A77B627}.Release|Any CPU.Build.0 = Release|Any CPU
		{95AD5F73-8676-45B5-B53B-34093D80E916}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{95AD5F73-8676-45B5-B53B-34093D80E916}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{95AD5F73-8676-45B5-B53B-34093D80E916}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{95AD5F73-8676-45B5-B53B-34093D80E916}.Release|Any CPU.Build.0 = Release|Any CPU
		{013BCA71-4F74-4224-B921-600310858CFA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{013BCA71-4F74-4224-B921-600310858CFA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{013BCA71-4F74-4224-B921-600310858CFA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{013BCA71-4F74-4224-B921-600310858CFA}.Release|Any CPU.Build.0 = Release|Any CPU
		{823C39A8-5EFF-43AC-953C-B01314386A90}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{823C39A8-5EFF-43AC-953C-B01314386A90}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{823C39A8-5EFF-43AC-953C-B01314386A90}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{823C39A8-5EFF-43AC-953C-B01314386A90}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D46F793-1CB9-419A-BF52-108072FFD74E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D46F793-1CB9-419A-BF52-108072FFD74E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D46F793-1CB9-419A-BF52-108072FFD74E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D46F793-1CB9-419A-BF52-108072FFD74E}.Release|Any CPU.Build.0 = Release|Any CPU
		{04445830-8A5E-4D68-934B-C3E23914E11D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04445830-8A5E-4D68-934B-C3E23914E11D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04445830-8A5E-4D68-934B-C3E23914E11D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04445830-8A5E-4D68-934B-C3E23914E11D}.Release|Any CPU.Build.0 = Release|Any CPU
		{E0BD1BC3-8BDC-483C-BD39-3D25037956B9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0BD1BC3-8BDC-483C-BD39-3D25037956B9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0BD1BC3-8BDC-483C-BD39-3D25037956B9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0BD1BC3-8BDC-483C-BD39-3D25037956B9}.Release|Any CPU.Build.0 = Release|Any CPU
		{4CEA478D-74BF-46E0-B5DD-278AC418E244}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4CEA478D-74BF-46E0-B5DD-278AC418E244}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4CEA478D-74BF-46E0-B5DD-278AC418E244}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4CEA478D-74BF-46E0-B5DD-278AC418E244}.Release|Any CPU.Build.0 = Release|Any CPU
		{FF07B468-882C-43D4-906A-C4F5D62310CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF07B468-882C-43D4-906A-C4F5D62310CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF07B468-882C-43D4-906A-C4F5D62310CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF07B468-882C-43D4-906A-C4F5D62310CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{6AE9AEF0-A3F8-44CD-8FD3-2F8B232F186D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6AE9AEF0-A3F8-44CD-8FD3-2F8B232F186D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6AE9AEF0-A3F8-44CD-8FD3-2F8B232F186D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6AE9AEF0-A3F8-44CD-8FD3-2F8B232F186D}.Release|Any CPU.Build.0 = Release|Any CPU
		{5FE31F96-CFC6-48FE-AA64-34795076905E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5FE31F96-CFC6-48FE-AA64-34795076905E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5FE31F96-CFC6-48FE-AA64-34795076905E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5FE31F96-CFC6-48FE-AA64-34795076905E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E63A380C-F44D-4C78-AE73-BB8F7A77B627} = {74A26776-465F-4287-AAD1-11FD8D15B422}
		{013BCA71-4F74-4224-B921-600310858CFA} = {8E090CCB-230C-4C61-B906-29F91C391FF0}
		{823C39A8-5EFF-43AC-953C-B01314386A90} = {8E090CCB-230C-4C61-B906-29F91C391FF0}
		{3D46F793-1CB9-419A-BF52-108072FFD74E} = {74A26776-465F-4287-AAD1-11FD8D15B422}
		{04445830-8A5E-4D68-934B-C3E23914E11D} = {8E090CCB-230C-4C61-B906-29F91C391FF0}
		{E0BD1BC3-8BDC-483C-BD39-3D25037956B9} = {8E090CCB-230C-4C61-B906-29F91C391FF0}
		{4CEA478D-74BF-46E0-B5DD-278AC418E244} = {0D63B686-**************-FC339BB1B4F5}
		{FF07B468-882C-43D4-906A-C4F5D62310CF} = {8E090CCB-230C-4C61-B906-29F91C391FF0}
		{6AE9AEF0-A3F8-44CD-8FD3-2F8B232F186D} = {8E090CCB-230C-4C61-B906-29F91C391FF0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {302F47D7-3DD6-4EE2-B4A6-6C249D0981BD}
	EndGlobalSection
EndGlobal
