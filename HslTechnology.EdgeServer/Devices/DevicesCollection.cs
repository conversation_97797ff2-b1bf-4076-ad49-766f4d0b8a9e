using System;
using System.Collections.Generic;
using System.Text;
using HslCommunication.ModBus;
using System.Xml.Linq;
using HslCommunication.LogNet;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Device.RobotDevice;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Node.Server;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Device.CncDevice;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Device;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Device.ServerDevice;
using HslTechnology.Edge.Node.Request;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.DataBusiness.Oee;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge;
using HslTechnology.Edge.Reflection;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.DataBusiness.Database;
using HslTechnology.Edge.DataBusiness.Log;
using System.IO;
using HslTechnology.Edge.Resources;
using HslCommunication.WebSocket;
using System.Threading;
using System.Security.Policy;
using HslTechnology.Edge.Config;

namespace HslTechnology.EdgeServer
{
	/// <summary>
	/// 针对Xml配置文档进行解析的所有的设备集合对象
	/// </summary>
	public class DevicesCollection
	{
		#region Constructor

		/// <summary>
		/// 通过指定日志对象，和服务器的参数配置信息来实例化一个的对象
		/// </summary>
		/// <param name="logNet">当前系统的日志信息</param>
		/// <param name="settingsServer">当前服务器的参数信息</param>
		/// <param name="getDeviceData">获取设备数据的方法信息</param>
		/// <param name="getSourceRequestCahce">获取原始字节请求的缓存数据的方法</param>
		/// <param name="mqttServer">Mqtt服务器对象</param>
		/// <param name="httpServer">Http服务器的对象</param>
		/// <param name="webSocketServer">Websocket服务器对象</param>
		public DevicesCollection( EdgeDeviceResources edgeResources )
		{
			this.edgeResources                            = edgeResources;
			this.deviceExtInfos                           = new DeviceExtInfoList( );
			this.dtuDevices                               = new Dictionary<string, DeviceCoreBase>( );
			this.serverDevices                            = new List<IServerDevice>( );
			this.threadExecuteControls                    = new List<DeviceThreadExecuteControl>( );
			this.logNet                                   = edgeResources.EdgeServices.EdgeLogNet;
			this.settingsServer                           = edgeResources.EdgeSettings;
			XElement element                              = this.settingsServer.GetXmlSettings( );
			this.regularkeyValuePairs                     = RegularStructItemNode.ParesRegular( element );
			BusinessEngine.Business.AlarmResource         = AlarmResource.PraseFromRootXml( element );
			if (BusinessEngine.Business.AlarmResource == null)
				logNet?.WriteWarn( string.Empty, string.Empty, "当前的报警节点配置文件找不到，将不启用任何基于全局报警的内容功能！" );
			BusinessEngine.Business.OeeResource           = OeeResource.PraseFromRootXml( element );
			if (BusinessEngine.Business.OeeResource == null)
				logNet?.WriteWarn( string.Empty, string.Empty, "当前的OEE节点配置文件找不到，将不启用任何基于全局OEE的内容功能！" );
			BusinessEngine.Business.DatabaseResource      = DatabaseResource.PraseFromRootXml( element );

			foreach (var item in DeviceHelper.GetDeviceTemplate( element ))
			{
				BusinessEngine.Business.AddTemplates( item.Key, item.Value );
				logNet?.WriteInfo( string.Empty, string.Empty, "发现模板设备信息：" + item.Key );
			}
			RegularStructItemNode.DictionaryRegularStruct = this.regularkeyValuePairs;

			this.deviceName                   = this.settingsServer.ServerInfo.DeviceName;


			this.deviceSettings = HideDeviceNodeItem( element );

			foreach (var item in element.Elements( ))
			{
				if (item.Name == nameof(GroupNode) && item.Attribute( nameof( GroupNode.Name ) ) != null)
				{
					string deviceName = item.Attribute( nameof( GroupNode.Name ) ).Value;
					if (deviceName == GroupNode.RootDevices)
					{
						this.ParseNodeItem( item, null );
						break;
					}
				}
			}

			// 执行下剩余的初始化信息，如果有的话
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				this.deviceExtInfos[i].Device?.ExecuteAfterBussiness( this.deviceExtInfos );
			}
		}

		#endregion

		#region Private Method

		private void ParseNodeItem( XElement nodeClass, DeviceThreadExecuteControl threadExecuteControl )
		{
			// 解析每个设备的节点信息，分析出设备的基本情况，相关的点位进行数据化数据操作
			foreach (var xmlNode in nodeClass.Elements( ))
			{
				if (xmlNode.Name == NodeType.GroupNode.ToString( ))
				{
					ParseNodeItem( xmlNode, null );                   // 继续解析子项的内容
				}
				else if (xmlNode.Name == NodeType.GroupSerialPipe.ToString( ))
				{
					// 共享的串口对象
					NodeSerialPipe nodeSerialPipe = new NodeSerialPipe( xmlNode );
					nodeSerialPipe.PortName = this.settingsServer.PortMapping.GetSourcePort( nodeSerialPipe.PortName );
					EdgePipeSerial edgePipe;
					try
					{
						edgePipe = nodeSerialPipe.CreatePipeSerial( );
						this.edgeResources.EdgeSerials.Add( DeviceHelper.GetUniquePath( xmlNode, true ), edgePipe );
					}
					catch( Exception ex )
					{
						// 串口名称不存在的话，将会引发异常，此处需要提醒，并且标记所有的管道设备有这个错误信息
						logNet?.WriteError( string.Empty, nodeSerialPipe.Name, $"创建管道资源时异常，端口名：[{nodeSerialPipe.PortName}] -> {ex.Message}" );
						continue;
					}

					DeviceThreadExecuteControl control = new DeviceThreadExecuteControl( DeviceHelper.GetUniquePath( xmlNode, true ) );
					control.SetEdgePipe( edgePipe );
					control.ChangeDeviceWhenError      = nodeSerialPipe.ChangeDeviceWhenError;
					control.CloseWhenChangeDevice      = nodeSerialPipe.CloseWhenChangeDevice;
					control.DeviceStatus               = nodeSerialPipe.StatusType;
					control.SleepTimeBetweenDevices    = nodeSerialPipe.SleepTimeBetweenDevices;
					this.threadExecuteControls.Add( control );
					ParseNodeItem( xmlNode, control );                   // 继续解析子项的内容
				}
				else if (xmlNode.Name == NodeType.GroupSocketPipe.ToString( ))
				{
					// 共享的网口对象
					NodeSocketPipe nodeSocketPipe = new NodeSocketPipe( xmlNode );
					this.edgeResources.EdgeSocket.Add( DeviceHelper.GetUniquePath( xmlNode, true ), nodeSocketPipe.CreatePipeSocket( ) );

					DeviceThreadExecuteControl control = new DeviceThreadExecuteControl( DeviceHelper.GetUniquePath( xmlNode, true ) );
					control.ChangeDeviceWhenError      = nodeSocketPipe.ChangeDeviceWhenError;
					control.DeviceStatus               = nodeSocketPipe.StatusType;
					control.SleepTimeBetweenDevices    = nodeSocketPipe.SleepTimeBetweenDevices;
					this.threadExecuteControls.Add( control );
					ParseNodeItem( xmlNode, control );                   // 继续解析子项的内容
				}
				else if (xmlNode.Name == NodeType.GroupSingleThread.ToString( ))
				{
					// 纯粹的单线程控制对象
					NodeSingleThread nodeSingleThread  = new NodeSingleThread( xmlNode );
					DeviceThreadExecuteControl control = new DeviceThreadExecuteControl( DeviceHelper.GetUniquePath( xmlNode, true ) );
					control.ChangeDeviceWhenError      = nodeSingleThread.ChangeDeviceWhenError;
					control.SleepTimeBetweenDevices    = nodeSingleThread.SleepTimeBetweenDevices;
					control.DeviceStatus               = nodeSingleThread.StatusType;
					this.threadExecuteControls.Add( control );
					ParseNodeItem( xmlNode, control );                   // 继续解析子项的内容
				}
				else if (
					xmlNode.Name == NodeType.DeviceNode.ToString( ) ||
					xmlNode.Name == NodeType.RobotNode.ToString( ) ||
					xmlNode.Name == NodeType.CncNode.ToString( ))
				{
					AddDeviceCoreBase( xmlNode, threadExecuteControl );
				}
			}
		}

		private XElement HideDeviceNodeItem( XElement nodeClass )
		{
			XElement element = new XElement( nodeClass.Name );
			if (nodeClass.Name == nameof( GroupNode ) || nodeClass.Name == nameof( NodeType.GroupSerialPipe ) || nodeClass.Name == nameof( NodeType.GroupSocketPipe ) || nodeClass.Name == nameof( NodeType.GroupSingleThread ))
			{
				if (nodeClass.Name == nameof( NodeType.GroupSerialPipe ) || nodeClass.Name == nameof( NodeType.GroupSocketPipe ) || nodeClass.Name == nameof( NodeType.GroupSingleThread ))
				{
					bool useAsGroupNode = GroupNode.GetXmlValue( nodeClass, nameof( NodeSerialPipe.UseAsGroupNode ), true, bool.Parse );
					if (useAsGroupNode) element = new XElement( nameof( GroupNode ) );

					if (nodeClass.Attribute( nameof( NodeSerialPipe.UseAsGroupNode ) ) != null)
						element.SetAttributeValue( nameof( NodeSerialPipe.UseAsGroupNode ), nodeClass.Attribute( nameof( NodeSerialPipe.UseAsGroupNode ) ).Value );
				}
				element.SetAttributeValue( nameof( GroupNode.Name ), nodeClass.Attribute( nameof( GroupNode.Name ) ).Value );
				if (nodeClass.Attribute( nameof( GroupNode.Description ) ) != null) element.SetAttributeValue( nameof( GroupNode.Description ), nodeClass.Attribute( nameof( GroupNode.Description ) ).Value );
				if (nodeClass.Attribute( nameof( GroupNode.DisplayName ) ) != null) element.SetAttributeValue( nameof( GroupNode.DisplayName ), nodeClass.Attribute( nameof( GroupNode.DisplayName ) ).Value );
			}
			
			foreach (var xmlNode in nodeClass.Elements( ))
			{
				if (xmlNode.Name == NodeType.GroupNode.ToString( ) || 
					xmlNode.Name == NodeType.GroupSerialPipe.ToString( ) || 
					xmlNode.Name == NodeType.GroupSocketPipe.ToString( ) ||
					xmlNode.Name == NodeType.GroupSingleThread.ToString( )
					)
				{
					element.Add( HideDeviceNodeItem( xmlNode ) );                   // 继续解析子项的内容
				}
				else if (
					xmlNode.Name == NodeType.DeviceNode.ToString( ) ||
					xmlNode.Name == NodeType.RobotNode.ToString( ) ||
					xmlNode.Name == NodeType.CncNode.ToString( ))
				{
					try
					{
						DeviceType type = SoftBasic.GetEnumFromString<DeviceType>( xmlNode.Attribute( nameof( DeviceType ) ).Value );
						DeviceNode deviceNode = new DeviceNode( xmlNode, type );
						if (deviceNode.StatusType != Edge.Node.Core.DeviceStatusType.StopAndHide)
						{
							element.Add( deviceNode.ToXmlElement( ) );
						}
					}
					catch
					{
						continue;  // 未找到相关的设备类型数据信息
					}
				}
			}
			return element;
		}

		private void AddDeviceCoreBase( XElement deviceXml, DeviceThreadExecuteControl threadExecuteControl )
		{
			OperateResult<DeviceCoreBase> deviceReal = EdgeReflectionHelper.GetDeviceCoreFrom( deviceXml );
			if(deviceReal == null)
			{
				string log = "发现不支持当前解析的设备信息：" + deviceXml.Attribute( nameof( DeviceNode.DeviceType ) ).Value;
				if(deviceXml.Attribute( nameof( DeviceNode.PluginsType ) )!= null)
				{
					log += "  插件类型：" + deviceXml.Attribute( nameof( DeviceNode.PluginsType ) ).Value;
				}
				this.logNet?.WriteWarn( string.Empty, string.Empty, log );
				return;
			}
			if(!deviceReal.IsSuccess)
			{
				this.logNet?.WriteWarn( string.Empty, string.Empty, $"创建设备类型为[{deviceXml.Attribute( nameof( DeviceNode.DeviceType ) ).Value}]，名称为[{deviceXml.Attribute( nameof( GroupNode.Name ) )}]失败，原因：" + deviceReal.Message );
				return;
			}
			if (deviceReal.Content != null)
			{
				if (threadExecuteControl != null && threadExecuteControl.DeviceStatus != Edge.Node.Core.DeviceStatusType.OnWork) deviceReal.Content.StatusType = threadExecuteControl.DeviceStatus;          // 如果有管道并且管道配置了非工作状态，则所有的设备都强制更改状态
				if (deviceReal.Content.StatusType == Edge.Node.Core.DeviceStatusType.StopAndHide)
				{
					this.logNet?.WriteInfo( string.Empty, string.Empty, $"设备类型为[{deviceXml.Attribute( nameof( DeviceNode.DeviceType ) ).Value}]，名称为[{deviceXml.Attribute( nameof( GroupNode.Name ) )}] 跳过添加，配置为隐藏设备。" );
					return;
				}

				if ( this.settingsServer.DtuInfo.UseDtuServer && !string.IsNullOrEmpty( deviceReal.Content.DTU ))
				{
					if (!dtuDevices.ContainsKey( deviceReal.Content.DTU ))
						dtuDevices.Add( deviceReal.Content.DTU, deviceReal.Content );
					else
						logNet?.WriteError( string.Empty, string.Empty, $"Devive[{deviceReal.Content.GetDeviceNameWithPath()}] 添加到DTU设备列表时，dtuID重复：" );
				}
				// 日志显示
				deviceReal.Content.LogNet = this.logNet;

				// 初始化XML信息及管道信息
				deviceReal.Content.LoadDeviceXmlContent( deviceXml );
				try
				{
					deviceReal.Content.IniDevice( deviceXml, this.edgeResources );
				}
				catch (Exception ex)
				{
					deviceReal.Content.SetDeviceForceMessgae( EdgeStringResource.StringGather_DeviceIni, EdgeStringResource.DeviceIniFailed + ex.Message );
				}

				deviceReal.Content.AnalysisByBussiness( BusinessEngine.Business );
				deviceReal.Content.OnDeviceValueChanged += Content_OnDeviceValueChanged;
				deviceReal.Content.OnDeviceStatusChanged += Content_OnDeviceStatusChanged;
				deviceReal.Content.SetResetNullIfFailed( this.settingsServer.ServerInfo.ResetNull );                  // 设置读取失败的时候，是否清空本来的数据

				if (threadExecuteControl != null)
				{
					threadExecuteControl.AddDevices( deviceReal.Content );                          // 添加到顺序执行线程控制里去
					deviceReal.Content.SetOtherThreadControl( threadExecuteControl );               // 设备交出当前的线程控制权
				}
				
				if (this.settingsServer.MaxDeviceCount >= 0 && deviceExtInfos.Count > this.settingsServer.MaxDeviceCount)
				{
					this.logNet?.WriteError( string.Empty, string.Empty, "发现设备：" + deviceReal.Content.ToString( ) + $", 但是超出网关限制[{this.settingsServer.MaxDeviceCount}]，设备添加失败！" );
				}
				else
				{
					this.logNet?.WriteInfo( string.Empty, string.Empty, "发现设备：" + deviceReal.Content.ToString( ) );
					deviceExtInfos.Add( new DeviceExtInfo( deviceReal.Content ) );                      // 添加到设备的列表去
				}
			}
		}

		private void Content_OnDeviceStatusChanged( DeviceCoreBase device, bool status )
		{
			OnDeviceStatusChanged?.Invoke( device, status );
		}

		private void Content_OnDeviceValueChanged( DeviceCoreBase device, DeviceSingleAddressLabel addressLabel, JToken value )
		{
			OnDeviceValueChanged?.Invoke( device, addressLabel, value );
		}

		#endregion

		/// <summary>
		/// 数据值变化的事件
		/// </summary>
		public event DeviceCoreBase.DelegateOnDeviceValueChanged OnDeviceValueChanged;

		/// <summary>
		/// 设备状态变化的事件
		/// </summary>
		public event DeviceCoreBase.DelegateOnDeviceStatusChanged OnDeviceStatusChanged;

		#region Public Method

		/// <summary>
		/// 获取所有的设备名称信息
		/// </summary>
		/// <returns>名称的数据列表</returns>
		public List<string> GetDevicesName( )
		{
			List<string> deviceList = new List<string>( );
			this.deviceExtInfos.ForEach( m => deviceList.Add( m.Device.Name ) );
			return deviceList;
		}

		/// <summary>
		/// 获取所有设备的工作状态
		/// </summary>
		/// <returns>工作状态的Array</returns>
		public JArray GetDevicesStatus( )
		{
			JArray array = new JArray( );
			for (int i = 0; i < deviceExtInfos.Count; i++)
			{
				JObject json = new JObject( );
				json.Add( "__name",         new JValue( deviceExtInfos[i].Device.GetDeviceNameWithPath( ) ) );
				json.Add( "__deviceStatus", new JValue( deviceExtInfos[i].Device.DeviceStatus ) );
				json.Add( "__threadActive", new JValue( (DateTime.Now - deviceExtInfos[i].Device.ThreadActiveTime).TotalSeconds < 60 ) );
				array.Add( json );
			}
			return array;
		}

		/// <summary>
		/// 依次启动所有的设备对象，去读取设备信息
		/// </summary>
		public void DevicesStart( )
		{
			for (int i = 0; i < deviceExtInfos.Count; i++)
			{
				this.deviceExtInfos[i].Device.Start( );
			}

			// 启动控制线程
			foreach(var deviceControl in threadExecuteControls)
			{
				deviceControl.Start( );
			}
		}

		/// <summary>
		/// 并发停止所有的设备对象，最后一个设备关闭连接的时候返回
		/// </summary>
		public void DevicesClose( )
		{
			Task[] task = new Task[deviceExtInfos.Count];
			for (int i = 0; i < deviceExtInfos.Count; i++)
			{
				DeviceCoreBase deviceCore = this.deviceExtInfos[i].Device;
				task[i] = Task.Factory.StartNew( deviceCore.Close );
			}
			Task.WaitAll( task );
			if (threadExecuteControls.Count > 0) HslTechnologyHelper.Sleep( 100 );         // 简单的延时，等待所有的
		}

		public bool CheckDevicesWork( )
		{
			if (!HslTechnology.Edge.Authorization.asdniasnfaksndiqwhawfskhfaiw( )) return true;

			// 检查所有的设备的工作状态，线程是否还处于工作的中
			bool isAllThreadNormal = true;
			for (int i = 0; i < deviceExtInfos.Count; i++)
			{
				DeviceCoreBase deviceCoreBase = deviceExtInfos[i].Device;
				if (deviceCoreBase.StatusType != Edge.Node.Core.DeviceStatusType.OnWork) continue;           // 停用的设备跳过线程假死的检测

				ThreadState threadState = deviceCoreBase.WorkThread != null ? deviceCoreBase.WorkThread.ThreadState : ThreadState.Running;
				DeviceExtInfo deviceExtInfo   = deviceExtInfos[i];
				double timeoutSeconds = (HslTechnologyHelper.GetDateTimeNow( ) - deviceCoreBase.ThreadActiveTime).TotalSeconds;
				// 超过60秒后，每10秒提醒一次，超过150秒后，每秒提醒一次
				if (timeoutSeconds < 60)
				{
					deviceExtInfo.DeviceCheckStatus = 0;
				}
				else if (timeoutSeconds < 120)
				{
					// 掉线60秒，简单的提醒一下
					if (deviceExtInfo.DeviceCheckStatus != 1)
					{
						deviceExtInfo.DeviceCheckStatus = 1;
						logNet?.WriteWarn( deviceCoreBase.GetDeviceNameWithPath( ), deviceCoreBase.ToString( ), $"线程假死超过{(int)timeoutSeconds}秒。" );
					}
				}
				else if (timeoutSeconds < 150)
				{
					// 掉线120秒，再次提醒一下
					if (deviceExtInfo.DeviceCheckStatus != 2)
					{
						deviceExtInfo.DeviceCheckStatus = 2;
						logNet?.WriteWarn( deviceCoreBase.GetDeviceNameWithPath( ), deviceCoreBase.ToString( ), $"线程假死超过{(int)timeoutSeconds}秒。" );
					}
				}
				else if (timeoutSeconds < 180)
				{
					// 掉线150-180秒，每秒提醒
					if (deviceExtInfo.DeviceCheckStatus != 3)
					{
						deviceExtInfo.DeviceCheckStatus = 3;
					}
					logNet?.WriteError( deviceCoreBase.GetDeviceNameWithPath( ), deviceCoreBase.ToString( ), $"线程[{threadState}]假死超过{(int)timeoutSeconds}秒，持续270秒即将重启线程。" );
				}
				else
				{
					PipeBase pipe = deviceCoreBase.GetPipeLock( );
					int lockTick = -1;
					if (pipe != null)
					{
						lockTick = pipe.LockingTick;
						//if (pipe.LockingTick > 0)
						//{
						//	// 尝试下离开锁看看
						//	pipe.PipeLockLeave( );
						//}
					}
					string threadText = lockTick < 0 ? $"线程[{threadState}]假死超过{(int)timeoutSeconds}秒" : $"线程[{threadState}:{lockTick}]假死超过{(int)timeoutSeconds}秒";

					if (timeoutSeconds < 240)  // timeoutSeconds < 240
					{
						// 掉线180秒后，尝试检查设备自身的锁情况
						if (deviceExtInfo.DeviceCheckStatus != 4)
						{
							deviceExtInfo.DeviceCheckStatus = 4;
						
							logNet?.WriteError( deviceCoreBase.GetDeviceNameWithPath( ), deviceCoreBase.ToString( ), $"{threadText}，当前尝试恢复设备的锁状态。" );
						}

						// TODO 暂停当前所有的采集
						// DeviceStopRequest( string.Empty );
						// 测试输出
						//if (timeoutSeconds % 30 == 0)
						//{
						//	logNet?.WriteError( deviceCoreBase.GetDeviceNameWithPath( ), deviceCoreBase.ToString( ), $"线程[{threadState}]假死超过{(int)timeoutSeconds}秒，当前尝试恢复设备的锁状态。" );
						//}
					}
					else if (timeoutSeconds < 270)
					{
						// 掉线240秒后，尝试重启线程
						if (deviceExtInfo.DeviceCheckStatus != 5)
						{
							deviceExtInfo.DeviceCheckStatus = 5;
							deviceCoreBase.Start( );
							deviceExtInfo.RestartThreadTick++;
							logNet?.WriteError( deviceCoreBase.GetDeviceNameWithPath( ), deviceCoreBase.ToString( ), $"{threadText}，当前尝试线程重启，60秒内没有成功即将系统重启。" );
						}
					}
					else if (timeoutSeconds < 300)
					{
						// 掉线270秒后，再次提醒重启线程
						if (deviceExtInfo.DeviceCheckStatus != 6)
						{
							deviceExtInfo.DeviceCheckStatus = 6;
							logNet?.WriteError( deviceCoreBase.GetDeviceNameWithPath( ), deviceCoreBase.ToString( ), $"{threadText}，当前尝试线程重启，30秒内没有成功即将系统重启。" );
						}
					}
					else
					{
						// 300秒后，设备仍然掉线，此处选择重启线程的操作吗？
						logNet?.WriteFatal( deviceCoreBase.GetDeviceNameWithPath( ), deviceCoreBase.ToString( ), $"{threadText}，尝试重启系统中..." );
						deviceExtInfo.DeviceCheckStatus = 0;
						isAllThreadNormal = false;
					}
				}
			}
			return isAllThreadNormal;
		}

		private OperateResult CheckDeviceCoreBase( DeviceCore deviceCore, DeviceNode deviceNode )
		{
			if (deviceCore == null)                 return new OperateResult( "当前的设备数据无法创建，请稍候重试！" );
			if (deviceCore.DeviceNode == null)      return new OperateResult( "当前的设备的[DeviceNode]无法创建，请稍候重试！" );
			if (deviceCore.DeviceNode.DeviceType != deviceNode.DeviceType || deviceCore.DeviceNode.PluginsType != deviceNode.PluginsType)
													return new OperateResult( "当前的设备的类型和服务器的类型不一致，无法读取数据，请先下载配置到网关先！" );
			if (deviceCore.ReadWriteDevice == null) return new OperateResult( "当前的设备数据无法读取原始字节，请稍候重试！" );
			return OperateResult.CreateSuccessResult( );
		}

		private OperateResult<string[]> CheckDevicePathLegal( string path )
		{
			bool isPath = path.IsDevicePath( );
			if (isPath) return new OperateResult<string[]>( "当前的设备信息不存在！无法指定一个路径来进行当前的操作。" );
			string[] nodePath = path.SplitDeviceNodes( );

			if (nodePath.Length == 0) return new OperateResult<string[]>( "当前的设备信息不存在！不能指定网关本身进行操作。" );
			else if (nodePath[0].Equals( "__status", StringComparison.OrdinalIgnoreCase )) return new OperateResult<string[]>( "当前的设备信息不存在！不能指定状态信息。" );

			return OperateResult.CreateSuccessResult( nodePath );
		}

		public async Task<OperateResult<string>> ReadSourceRequest( string path, string deviveXml, string requestXml )
		{
			OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
			if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<string>( checkPath );

			bool isCreateNew = false;
			XElement deviceXEle = XElement.Parse( deviveXml );
			if (deviceXEle.Attribute( nameof( DeviceNode.DeviceType ) ) == null) return new OperateResult<string>( $"当前的[deviveXml]不包含[DeviceType]属性，不是标准的设备对象！" );

			DeviceType deviceType = SoftBasic.GetEnumFromString<DeviceType>( deviceXEle.Attribute( nameof( DeviceType ) ).Value );
			DeviceNode deviceNode = EdgeReflectionHelper.CreateInstanceFrom( deviceXEle );

			// 先寻找该设备是否在已经运行的设备列表里
			DeviceCoreBase device = null;
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
				{
					if(this.deviceExtInfos[i].Device is DeviceCore device1)
					{
						OperateResult check1 = CheckDeviceCoreBase( device1, deviceNode );
						if(check1.IsSuccess) device = this.deviceExtInfos[i].Device;
					}
				}
			}

			if (device == null)
			{
				OperateResult<DeviceCoreBase> create = EdgeReflectionHelper.GetDeviceCoreFrom( deviceXEle );
				if (!create.IsSuccess) return OperateResult.CreateFailedResult<string>( create );

				device = create.Content;
				isCreateNew = true;
			}

			DeviceCore deviceCore = device as DeviceCore;
			OperateResult check = CheckDeviceCoreBase( deviceCore, deviceNode );
			if (!check.IsSuccess) return OperateResult.CreateFailedResult<string>( check );

			SourceReadRequest sourceReadRequest = new SourceReadRequest( XElement.Parse( requestXml ), device );
			OperateResult<byte[]> read = await deviceCore.ReadActualSourceRequest( sourceReadRequest );
			if (!read.IsSuccess) return new OperateResult<string>( "Read data failed: " + read.Message );

			if (isCreateNew) deviceCore.Close( );
			return OperateResult.CreateSuccessResult( Convert.ToBase64String( read.Content ) );
		}

		/// <summary>
		/// 根据路径获取到设备的数据，有可能是设备的所有的数据，有可能是一个子数据信息
		/// </summary>
		/// <param name="path">数据的路径信息</param>
		/// <param name="isPath">指示当前的节点数组是否是路径，如果不是路径，那就是真实的设备信息</param>
		/// <returns>最终的数据值</returns>
		public OperateResult<JToken> GetDataByNodePath( string[] nodePath, bool isPath )
		{
			if (isPath)
			{
				JObject json = new JObject( );
				for (int i = 0; i < this.deviceExtInfos.Count; i++)
				{
					// 添加所有符合路径访问的设备对象
					if (this.deviceExtInfos[i].Device.InSpecifiedPath( nodePath ))
						json[this.deviceExtInfos[i].Device.GetDeviceNameWithPath( )] = this.deviceExtInfos[i].Device.JsonObject;
				}
				return OperateResult.CreateSuccessResult( (JToken)json );
			}
			else
			{
				for (int i = 0; i < this.deviceExtInfos.Count; i++)
				{
					if (this.deviceExtInfos[i].Device.IsCurrentDevice( nodePath ))
					{
						return OperateResult.CreateSuccessResult( this.deviceExtInfos[i].Device.GetValueByName( nodePath ) );
					}
				}
				return new OperateResult<JToken>( $"当前的设备[{HslTechnologyHelper.CombineEdgePath( nodePath, isPath )}]不存在！" );
			}
		}

		/// <summary>
		/// 根据设备的唯一路径信息，获取设备的核心对象信息
		/// </summary>
		/// <param name="path">唯一路径信息</param>
		/// <returns>是否DeviceCore对象信息</returns>
		public OperateResult<DeviceCore> GetDeviceCoreByPath( string path )
		{
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( path.SplitDeviceNodes( ) ))
				{
					return OperateResult.CreateSuccessResult( this.deviceExtInfos[i].Device as DeviceCore );
				}
			}
			return null;
		}

		/// <summary>
		/// 获取当前所有的设备中，处于异常的情况。
		/// </summary>
		/// <returns>异常信息</returns>
		public OperateResult<DeviceExceptionMessage[]> GetExceptionDeviceList( int status )
		{
			List<DeviceExceptionMessage> list = new List<DeviceExceptionMessage>( );
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (status == 1)
				{
					if (this.deviceExtInfos[i].Device.RequestEnable == false) list.Add( this.deviceExtInfos[i].Device.GetExceptionInfo( ) );
				}
				else if (status == 0)
				{
					if (this.deviceExtInfos[i].Device.DeviceStatus) list.Add( this.deviceExtInfos[i].Device.GetExceptionInfo( ) );
				}
				else
				{
					if (this.deviceExtInfos[i].Device.DeviceStatus == false) list.Add( this.deviceExtInfos[i].Device.GetExceptionInfo( ) );
				}
			}
			return OperateResult.CreateSuccessResult( list.ToArray( ) );
		}

		/// <summary>
		/// 根据路径信息获取到指定设备的原始字节信息，如果原始字节数据不存在，
		/// </summary>
		/// <param name="nodePath">设备的节点信息</param>
		/// <returns>原始字节结果数据</returns>
		public OperateResult<byte[]> GetSourceRequestCahceByNodePath( string[] nodePath )
		{
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( nodePath ))
				{
					return this.deviceExtInfos[i].Device.GetSourceRequestCahce( nodePath );
				}
			}
			return new OperateResult<byte[]>( $"当前的设备[{HslTechnologyHelper.CombineEdgePath( nodePath, false )}]不存在！" );
		}

		/// <summary>
		/// 根据路径信息获取指定的设备所有的地址标签信息，主要用于客户端Viewer查看设备的数据节点
		/// </summary>
		/// <param name="path">设备的节点信息</param>
		/// <returns>地址标签的数组信息</returns>
		public OperateResult<DeviceSingleAddressLabel[]> GetDeviceAddressLabel( string path )
		{
			OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
			if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<DeviceSingleAddressLabel[]>( checkPath );

			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
				{
					return OperateResult.CreateSuccessResult( this.deviceExtInfos[i].Device.GetDeviceSingleAddresses( ) );
				}
			}
			return new OperateResult<DeviceSingleAddressLabel[]>( $"当前的设备[{path}]不存在！" );
		}

		/// <summary>
		/// 根据地址标签的路径获取到指定标签的详细信数据信息，主要用于客户端Viewer双击单个的标签显示的数据
		/// </summary>
		/// <param name="path">标签的路径信息</param>
		/// <returns>设备单个的标签对象信息</returns>
		public OperateResult<DeviceSingleAddressLabel> GetAddressLabelByTagName( string path )
		{
			OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
			if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<DeviceSingleAddressLabel>( checkPath );

			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
				{
					return this.deviceExtInfos[i].Device.GetAddressLabelByTagName( checkPath.Content );
				}
			}
			return new OperateResult<DeviceSingleAddressLabel>( $"当前的设备[{path}]不存在！" );
		}

		/// <summary>
		/// 把值写入到指定的标签的路径信息中去，如果路径信息不存在，则写入失败
		/// </summary>
		/// <param name="path">标签的路径信息</param>
		/// <param name="value">值信息，字符串的表示形式，需要可以转换成实际的值</param>
		/// <returns>是否写入成功</returns>
		public OperateResult WriteDataByNodePath( string path, string value )
		{
			OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
			if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<DeviceSingleAddressLabel>( checkPath );

			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
				{
					return this.deviceExtInfos[i].Device.WriteValueByName( checkPath.Content, value );
				}
			}
			return new OperateResult( "找不到当前节点的设备信息，无法写入相关的数据。" );
		}

		/// <summary>
		/// 获取所有设备的实时数据信息
		/// </summary>
		/// <param name="isPath">指示当前的节点数组是否是路径，如果不是路径，那就是真实的设备信息</param>
		/// <returns>json格式的数据信息</returns>
		public JObject GetAllDevicesData( string[] nodePath, bool isPath )
		{
			JObject json = new JObject( );
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (isPath)
				{
					// 添加所有顶级路径访问的设备对象
					if (this.deviceExtInfos[i].Device.InSpecifiedPath( nodePath ))
						json[this.deviceExtInfos[i].Device.GetDeviceNameWithPath( )] = this.deviceExtInfos[i].Device.JsonObject;
				}
				else
					json[this.deviceExtInfos[i].Device.GetDeviceNameWithPath( )] = this.deviceExtInfos[i].Device.JsonObject;
			}
			return json;
		}

		/// <summary>
		/// 浏览指定设备的所有的数据节点信息，用于服务器的数据查看
		/// </summary>
		/// <param name="nodePath">设备节点信息</param>
		/// <returns>节点数据定义</returns>
		public OperateResult<ScalarDataNode[]> BrowseDeviceDataNodes( string[] nodePath )
		{
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( nodePath ))
					return OperateResult.CreateSuccessResult( this.deviceExtInfos[i].Device.DataNodes );
			}
			return new OperateResult<ScalarDataNode[]>( $"当前的设备信息 {nodePath.ToArrayString()} 不存在！" );
		}

		/// <summary>
		/// 获取所有设备的实时数据信息
		/// </summary>
		/// <returns>json格式的数据信息</returns>
		public JObject GetAllDevicesJsonData( )
		{
			JObject json = new JObject( );
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				json[this.deviceExtInfos[i].Device.Name] = JObject.Parse( this.deviceExtInfos[i].Device.JsonData );
			}
			return json;
		}

		/// <summary>
		/// 获取指定结构体的数据节点列表，用于浏览服务器的数据节点信息
		/// </summary>
		/// <param name="structName">结构体的名称信息</param>
		/// <returns>结构体的定义信息</returns>
		public OperateResult<ScalarDataNode[]> GetStructDataNodes( string structName = "A" )
		{
			if (regularkeyValuePairs.ContainsKey( structName ))
				return OperateResult.CreateSuccessResult( regularkeyValuePairs[structName].DataNodes );
			else
				return new OperateResult<ScalarDataNode[]>( "当前的结构体信息不存在，请重新输入结构体信息" );
		}

		/// <summary>
		/// 根据当前的传入的索引，获取到设备的<see cref="DeviceCoreBase"/>通信对象信息
		/// </summary>
		/// <param name="index">索引ID信息</param>
		/// <returns>设备对象</returns>
		public DeviceCoreBase this[int index] => this.deviceExtInfos[index].Device;

		/// <summary>
		/// 如果设备是DTU的设备，那么就重新设置连接
		/// </summary>
		/// <param name="session">通信会话</param>
		public void DtuServerOnClientConnected( AlienSession session )
		{
			if(dtuDevices.ContainsKey( session.DTU ))
			{
				dtuDevices[session.DTU].SetDtuSession( session );
			}
			else
			{
				logNet?.WriteError( string.Empty, string.Empty, $"收到不存在的DTU连接，DTU[{session.DTU}] 未找到" );
				session.Socket?.Close( );
			}
		}

		/// <summary>
		/// 根据给定的设备的ID路径信息，然后获取这个设备的方法接口列表
		/// </summary>
		/// <param name="path">方法的ID信息</param>
		/// <returns>方法接口列表</returns>
		public OperateResult<MethodRpcInfo[]> GetMethodInfoByDeviceID( string path )
		{
			OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
			if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<MethodRpcInfo[]>( checkPath );

			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
				{
					return this.deviceExtInfos[i].Device.GetMethodInfoByDeviceID( );
				}
			}
			return new OperateResult<MethodRpcInfo[]>( $"当前的设备[{path}]不存在！" );
		}

		/// <summary>
		/// 检测当前所有的设备中是否有数据发生了变化，然后清除所有的变化标记
		/// </summary>
		/// <returns>数据是否变化</returns>
		public bool MarkDataChangeCheckAndReset( )
		{
			bool changed = false;
			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				bool tmp = this.deviceExtInfos[i].Device.MarkDataChangeCheckAndReset( );
				if (!changed && tmp) changed = tmp;
			}

			return changed;
		}

		internal async Task<object> CallDeviceMethod( bool admin, string path, string method, string parameterJson )
		{
			OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
			if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<MethodRpcInfo[]>( checkPath );

			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
				{
					return await this.deviceExtInfos[i].Device.CallDeviceMethod( admin, method, parameterJson );
				}
			}
			return new OperateResult( $"当前的设备[{path}]不存在！" );
		}

		internal OperateResult<string> DeviceContinueRequest( string path )
		{
			if (string.IsNullOrEmpty( path ))
			{
				for (int i = 0; i < this.deviceExtInfos.Count; i++)
				{
					this.deviceExtInfos[i].Device.DeviceContinueRequest( );
				}
				return OperateResult.CreateSuccessResult( "All Continue Success" );
			}

			if (path.IsDevicePath( ))
			{
				// 如果是路径的话，就暂时路径下的所有的设备
				string[] paths = path.SplitDeviceNodes( );
				for (int i = 0; i < this.deviceExtInfos.Count; i++)
				{
					if (this.deviceExtInfos[i].Device.InSpecifiedPath( paths, exactMatch: false ))
					{
						this.deviceExtInfos[i].Device.DeviceContinueRequest( );
					}
				}
				return OperateResult.CreateSuccessResult( "All Continue Success" );
			}
			else
			{
				OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
				if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<string>( checkPath );

				for (int i = 0; i < this.deviceExtInfos.Count; i++)
				{
					if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
					{
						string name = this.deviceExtInfos[i].Device.GetDeviceChildInfo( checkPath.Content );
						this.deviceExtInfos[i].Device.DeviceContinueRequest( name );
						return OperateResult.CreateSuccessResult( $"Continue Device {this.deviceExtInfos[i].Device.GetDeviceNameWithPath( )} Success" );
					}
				}
				return new OperateResult<string>( $"当前的设备[{path}]不存在！" );
			}
		}

		internal OperateResult<string> DeviceStopRequest( string path )
		{
			if (string.IsNullOrEmpty( path ))     // 全部设备暂停采集的情况
			{
				for (int i = 0; i < this.deviceExtInfos.Count; i++)
				{
					this.deviceExtInfos[i].Device.DeviceStopRequest( );
				}
				return OperateResult.CreateSuccessResult( "All Stop Success" );
			}

			if (path.IsDevicePath( ))
			{
				// 如果是路径的话，就暂时路径下的所有的设备
				string[] paths = path.SplitDeviceNodes( );
				for (int i = 0; i < this.deviceExtInfos.Count; i++)
				{
					if (this.deviceExtInfos[i].Device.InSpecifiedPath( paths, exactMatch: false ))
					{
						this.deviceExtInfos[i].Device.DeviceStopRequest( );
					}
				}
				return OperateResult.CreateSuccessResult( "All Stop Success" );
			}
			else
			{
				OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
				if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<string>( checkPath );

				for (int i = 0; i < this.deviceExtInfos.Count; i++)
				{
					if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
					{
						string name = this.deviceExtInfos[i].Device.GetDeviceChildInfo( checkPath.Content );
						this.deviceExtInfos[i].Device.DeviceStopRequest( name );
						return OperateResult.CreateSuccessResult( $"Stop Device {this.deviceExtInfos[i].Device.GetDeviceNameWithPath( )} Success" );
					}
				}
				return new OperateResult<string>( $"当前的设备[{path}]不存在！" );
			}
		}

		internal OperateResult<long> DeviceRequestCount( string path )
		{
			if (string.IsNullOrEmpty( path )) return new OperateResult<long>( $"当前的设备[{path}]不存在！" );

			OperateResult<string[]> checkPath = CheckDevicePathLegal( path );
			if (!checkPath.IsSuccess) return OperateResult.CreateFailedResult<long>( checkPath );

			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
				{
					string name = this.deviceExtInfos[i].Device.GetDeviceChildInfo( checkPath.Content );
					return this.deviceExtInfos[i].Device.DeviceRequestCount( name );
				}
			}
			return new OperateResult<long>( $"当前的设备[{path}]不存在！" );
		}


		internal OperateResult DealWithPublishTopic( string topic, byte[] payload, string payload2 )
		{
			OperateResult<string[]> checkPath = CheckDevicePathLegal( topic );
			if (!checkPath.IsSuccess) return checkPath;

			for (int i = 0; i < this.deviceExtInfos.Count; i++)
			{
				if (this.deviceExtInfos[i].Device.IsCurrentDevice( checkPath.Content ))
				{
					return this.deviceExtInfos[i].Device.DealWithPublishTopic( checkPath.Content, payload, payload2 );
				}
			}
			//this.logNet?.WriteWarn(string.Empty, "Mqtt")
			return new OperateResult( $"Publish Topic[{topic}] failed: 当前的设备路径未找到" );
		}

		#endregion

		#region Upload DeviceData

		/// <summary>
		/// 按照设备为单位上传数据到指定的<see cref="MqttClient"/>中去
		/// </summary>
		/// <param name="mqttClient">用于远程上传的MQTT客户端信息</param>
		/// <param name="dataChangePublish">数据变化模式的时候，没有数据变化的时候，是否开启强制上传</param>
		/// <exception cref="Exception">如果上传失败，将引发异常信息</exception>
		public void UploadByDevice( MqttClient mqttClient, bool dataChangePublish )
		{
			for (int i = 0; i < this.DeviceCount; i++)
			{
				if (!this.edgeResources.EdgeServices.IsRunning) return;                           // 如果不是运行中，直接返回
				bool ifPublish = true;
				if (this.settingsServer.UploadInfo.UploadWhenDataChange)      // 检测是否配置了数据变化才上传的情况
				{
					ifPublish = this[i].MarkDataChangeCheckAndReset( );       // 设备数据有变化才进行发布
					if (dataChangePublish) ifPublish = true;                  // 管他变化不变化，都上传
				}

				if (this[i].RequestEnable == false || this[i].StatusType == Edge.Node.Core.DeviceStatusType.OnStop) ifPublish = false;

				if (ifPublish)
				{
					if (this.edgeResources.EdgeSettings.UploadInfo.MqttType == UploadInfoConfig.MqttTypeNormal) // 普通的MQTT
					{
						OperateResult publish = mqttClient.PublishMessage( new MqttApplicationMessage( )
						{
							Topic = this[i].GetMqttTopic( ),
							Payload = Encoding.UTF8.GetBytes( this[i].JsonData ),
							QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
							Retain = this.settingsServer.UploadInfo.MqttRetain,
						} );
						if (!publish.IsSuccess) throw new Exception( publish.Message );
					}
					else if (this.edgeResources.EdgeSettings.UploadInfo.MqttType == UploadInfoConfig.MqttTypeJetLinks) // JetLinks 的mqtt
					{
						// 当设备状态有问题的时候，直接跳过上传
						if (!this[i].DeviceStatus) continue;

						JObject json = new JObject( );
						json["deviceId"] = this[i].Name;
						json["properties"] = this[i].JsonObject;
						OperateResult publish = mqttClient.PublishMessage( new MqttApplicationMessage( )
						{
							Topic = this[i].GetMqttTopic( ) + "/properties/report",
							Payload = Encoding.UTF8.GetBytes( json.ToString( ) ),
							QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
							Retain = this.settingsServer.UploadInfo.MqttRetain,
						} );

						// logNet?.WriteWarn( this[i].GetDeviceNameWithPath( ), "MQTT", "发布主题: " + "/" + this[i].GetDeviceNameWithPath( ) + "/properties/report" );
						if (!publish.IsSuccess) throw new Exception( publish.Message );
					}
				}
			}
		}

		/// <summary>
		/// 按照网关为单位上传数据到指定的<see cref="MqttClient"/>中去
		/// </summary>
		/// <param name="mqttClient">用于远程上传的MQTT客户端信息</param>
		/// <param name="status">网关状态信息数据</param>
		/// <param name="dataChangePublish">数据变化模式的时候，没有数据变化的时候，是否开启强制上传</param>
		/// <param name="mqttType">上传的MQTT方式</param>
		/// <exception cref="Exception">如果上传失败，将引发异常信息<</exception>
		public void UploadByEdge( MqttClient mqttClient, JObject status, bool dataChangePublish, int mqttType )
		{
			if (mqttType == UploadInfoConfig.MqttTypeNormal) // 普通的MQTT
			{
				JObject json = this.GetAllDevicesJsonData( );
				json["__status"] = status;

				bool ifPublish = true;
				if (this.settingsServer.UploadInfo.UploadWhenDataChange)      // 检测是否配置了数据变化才上传的情况
				{
					ifPublish = this.MarkDataChangeCheckAndReset( );          // 设备数据有变化才进行发布
					if (dataChangePublish) ifPublish = true;                  // 管他变化不变化，都上传
				}
				if (ifPublish)
				{
					OperateResult publish = mqttClient.PublishMessage( new MqttApplicationMessage( )
					{
						Topic = DeviceName,
						Payload = Encoding.UTF8.GetBytes( json.ToString( Newtonsoft.Json.Formatting.None ) ),
						QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
						Retain = this.settingsServer.UploadInfo.MqttRetain,
					} );
					if (!publish.IsSuccess) throw new Exception( publish.Message );
				}
			}
			else if (mqttType == UploadInfoConfig.MqttTypeJetLinks) // JetLinks 的mqtt 暂时不支持上传整个网关的功能
			{

			}
		}

		/// <summary>
		/// 订阅远程操作的相关的主题信息
		/// </summary>
		/// <param name="mqttClient">Mqtt的客户端</param>
		public void MqttSubscrib( MqttClient mqttClient, int mqttType )
		{
			// 订阅设备的相关主题
			if (mqttType == UploadInfoConfig.MqttTypeNormal) // 普通的MQTT
			{
				mqttClient.SubscribeMessage( DeviceName + "/WriteData" );
			}
			else
			{
				string[] topics = new string[this.DeviceCount];
				for (int i = 0; i < this.DeviceCount; i++)
				{
					topics[i] = this[i].GetMqttTopic( ) + HslTechnologyExtension.DeviceDefaultSplit + "#";
				}
				mqttClient.SubscribeMessage( topics );
			}
		}

		/// <inheritdoc cref="DeviceSingleAddressLabel.RaiseValueChangeEvent"/>
		public void RaiseValueChangeEvent( )
		{
			for (int i = 0; i < this.DeviceCount; i++)
			{
				if (!this.edgeResources.EdgeServices.IsRunning) return;                           // 如果不是运行中，直接返回
				this[i].RaiseValueChangeEvent( );
			}
		}

		#endregion

		#region Public Properties
		/// <summary>
		/// 获取网关设备的名称信息
		/// </summary>
		public string DeviceName => deviceName;

		/// <summary>
		/// 获取设备的描述信息
		/// </summary>
		public string DeviceDescription => deviceDescription;

		/// <summary>
		/// 获取设备的总数量
		/// </summary>
		public int DeviceCount => this.deviceExtInfos.Count;

		/// <summary>
		/// 获取当前服务器所有的设备的基本配置信息
		/// </summary>
		public XElement XmlDeviceNodes => this.deviceSettings;

		/// <summary>
		/// 获取当前在线的设备的总数量
		/// </summary>
		public int DeviceOnlineCount
		{
			get
			{
				int count = 0;
				for (int i = 0; i < deviceExtInfos.Count; i++)
				{
					if (deviceExtInfos[i].Device.DeviceStatus) count++;
				}
				return count;
			}
		}

		/// <summary>
		/// 获取当前的暂停的设备的总数量
		/// </summary>
		public int DeviceStopCount
		{
			get
			{
				int count = 0;
				for (int i = 0; i < deviceExtInfos.Count; i++)
				{
					if (!deviceExtInfos[i].Device.RequestEnable) count++;
				}
				return count;
			}
		}

		#endregion

		#region Private Member

		private EdgeDeviceResources edgeResources;                                         // 当前网关的资源信息
		private SettingsServer settingsServer;                                             // 服务器的配置信息
		private EdgeLog logNet;                                                            // 中间日志信息
		private Dictionary<string, RegularStructItemNode> regularkeyValuePairs;            // 所有的解析规则列表的对象
		private List<IServerDevice> serverDevices;                                         // 所有的ModbusTcp服务器
		private DeviceExtInfoList deviceExtInfos;                                        // 所有的设备额外的属性信息
		private string deviceName = string.Empty;                                          // 设备的名称信息，从配置文件里获取得到
		private string deviceDescription = string.Empty;                                   // 设备的描述信息，从配置文件里获取得到
		private XElement deviceSettings;                                                   // 精简过的设备配置信息
		private Dictionary<string, DeviceCoreBase> dtuDevices;                             // 所有的DTU设备数据
		//private Dictionary<string, EdgePipeSerial> edgeSerials;                            // 当前的共享的串口的管道资源
		//private Dictionary<string, EdgePipeSocket> edgeSocket;                             // 当前的共享的网口的管道资源
		private List<DeviceThreadExecuteControl> threadExecuteControls;                    // 当前的线程控制信息

		#endregion

	}

}
