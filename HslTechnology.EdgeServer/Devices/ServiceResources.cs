using HslCommunication.Enthernet;
using HslCommunication;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Text;
using HslCommunication.MQTT;

namespace HslTechnology.EdgeServer.Devices
{
	/// <summary>
	/// 服务相关的资源属性
	/// </summary>
	public class ServiceResources
	{
		/// <summary>
		/// 根据数据的URL信息，获取到设备的单点数据的方法
		/// </summary>
		public Func<string, OperateResult<JToken>> GetDeviceData { get; set; }

		/// <summary>
		/// 数据原始字节请求的名字，获取到该请求的原始字节信息
		/// </summary>
		public Func<string, OperateResult<byte[]>> GetSourceRequestCahce { get; set; }

		/// <summary>
		/// 系统的Mqtt服务器对象
		/// </summary>
		public MqttServer MqttServer { get; set; }

		/// <summary>
		/// 系统的Http服务器对象
		/// </summary>
		public HttpServer HttpServer { get; set; }

	}
}
