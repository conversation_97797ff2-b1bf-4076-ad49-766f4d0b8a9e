using System;
using System.Collections.Generic;
using System.Text;

namespace HslTechnology.EdgeServer
{
    /// <summary>
    /// 历史版本记录信息
    /// </summary>
    /// <revisionHistory>
    ///     <revision date="2021-08-22" version="1.0.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>采用CS架构，服务器端分多个版本，支持linux，windows，linux-arm</item>
    ///             <item>支持对采集的设备进行动态配置操作，不需要手写代码</item>
    ///             <item>支持配置PLC的数据的不同地址，支持地址提示功能</item>
    ///             <item>支持配置读取原始字节数据，支持配置动态的解析信息</item>
    ///             <item>支持配置结构体定义，可以使用结构体去解析原始字节数据信息</item>
    ///             <item>支持配置方法的定时调用，部分设备（机器人，fanuc机床）使用方法请求</item>
    ///             <item>支持配置报警信息，可以把数据绑定报警信息</item>
    ///             <item>支持配置OEE状态信息，可以把数据绑定OEE，进而统计状态信息</item>
    ///             <item>支持数据远程上传，支持MQTT,REDIS，可以订阅网关数据</item>
    ///             <item>支持webapi接口，来获取设备的数据，或是调用方法</item>
    ///             <item>支持网关的状态监视操作，所有设备的状态监视操作</item>
    ///             <item>支持账户权限控制，可以设置普通账户，仅可以浏览服务器数据，查看数据</item>
    ///             <item>服务器端支持日志配置，是否存储文件</item>
    ///             <item>设备支持插件扩展，windows版的服务器，自带了opcda的插件，需要注册com组件</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2021-09-06" version="1.0.1" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>修复边缘网关服务器在加载XML时，如果遇到不存在 DeviceType 类型会发生异常的bug。</item>
    ///             <item>边缘网关服务器的插件机制调整，改为目录的机制，一个插件一个目录，一个主dll文件，允许携带额外的文件。</item>
    ///             <item>边缘网关服务器优化采集时间和上传时间，使得时间更加的准确，比如设置1秒采集1次，或是1秒上传一次，更加的准确。</item>
    ///             <item>边缘网关客户端在查看网关数据时，修复点击设备数据表值列表标题导致异常奔溃的bug。</item>
    ///             <item>修复边缘网关服务器在 linux 下运行时，从客户端点击重启失败的问题，现在可以成功重启，重启后没有界面显示，在后台运行。</item>
    ///             <item>修复边缘网关windows版本的服务器在中文安装路径下重启不成功的bug。</item>
    ///             <item>边缘网关客户端的设备节点可以设置是否支持地址请求的功能，在客户端配置相关操作时，会禁用相关的菜单按钮。</item>
    ///             <item>修复边缘网关服务器优化了一个线程资源，改由主线程来执行服务程序一些非阻塞的定时代码，比如更新网关的状态数据信息。</item>
    ///             <item>边缘网关系统支持了读取Redis实时数据库的功能，使用调用方法的机制实现。</item>
    ///             <item>边缘网关系统支持了串口转网口的功能，需要输入串口的相关参数以及网口的基本端口信息即可。客户端可以查看该设备的一些状态数据。</item>
    ///             <item>修复边缘网关服务器支持配置为DTU模式，在客户端程序的网关参数里进行配置，可以配置是否支持注册包回复报文。</item>
    ///             <item>一些基于网口的设备支持配置为DTU模式，只需要将设备节点信息的DTU的唯一识别码设置为一个固定值即可。</item>
    ///             <item>边缘网关系统支持了串口转DTU的功能，可以将本地的串口转接到云服务器上，云服务器可以直接进行读写操作。</item>
    ///             <item>边缘网关系统支持了网口转DTU的功能，可以将本地局域网的设备转接到云服务器上，云服务器可以直接进行读写操作。</item>
    ///             <item>其他的功能修复和优化。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2021-09-23" version="1.0.2" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>更新了HslCommunication的通信库，MQTT的RPC服务器支持了加密模式，Viewer客户端使用加密模式访问，防止中间抓包破解账户。</item>
    ///             <item>边缘网系统修复字符串参数配置保存失败的bug，引用了System.Text.Encoding.CodePages支持GB2312编码。</item>
    ///             <item>边缘网关服务器支持了主备模式选择，如果选择为备用服务器，则会监视主服务器状态，主服务器掉线后一定时间后，立即切换为主服务器运行。</item>
    ///             <item>边缘网关客户端在编辑设备参数里进行配置相关的主备信息。</item>
    ///             <item>边缘网关服务器的系统状态数据里，支持查看当前的备用网关的基本状态，是否注册过，是否运行中。</item>
    ///             <item>新增基于MQTT-RPC的客户端设备，可以配置读取另一个网关的任意节点数据，当写入节点操作时，调用远程网关的写入方法。</item>
    ///             <item>基于数值范围的报警修复下限报警失败的异常，并增加了第二个警戒范围的设置，现在有两个档次的报警范围，且二个报警等级都可以自定义。</item>
    ///             <item>修复边缘网关客户端界面查看OEE信息时，多个OEE选择切换失败的bug，现在可以随意的切换显示。</item>
    ///             <item>边缘网关客户端的一些界面优化，报警信息的统计及显示，修复运行一会就假死的bug。以及其他的代码优化和改进。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2021-10-13" version="1.0.3" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>更新了HslCommunication的通信库到最新版，解决了异步锁的问题，Viewer的客户端界面解决了假死的bug。</item>
    ///             <item>在客户端主界面点击重启网关的操作，增加是否确认的操作步骤，防止误触。</item>
    ///             <item>客户端的主界面监视某个路径的所有设备时，点击报警数量时，支持查看该路径下所有的报警列表的功能。</item>
    ///             <item>客户端的界面颜色调整，所有弹出的界面的背景色统一，原始字节配置界面显示字节的小框框面积增大，不同数据类型的背景色调整。</item>
    ///             <item>在客户端的配置设备的原始字节采集时，支持进行数据预览操作，方便看到当前配置对应的数据是否正确。</item>
    ///             <item>服务器的用于客户端远程写入数据的接口，增加对数组的长度判断，不能超过原本的定义的数组长度，否则，返回失败。</item>
    ///             <item>边缘网关系统基础的数据类型增加sbyte类型，有些设备支持写入操作，比如西门子，有些设备不支持写入，例如三菱，具体视PLC而定。</item>
    ///             <item>边缘网关系统增加BCD码的数据类型，支持8421码，5421码，2421码，余3码，格雷码，标量请求和原始字节请求都支持。</item>
    ///             <item>基础设备类对象支持本地的结构体资源，这样在插件里支持自带结构体定义资源，实现更灵活的解析。</item>
    ///             <item>客户端主界面监视网关数据时，支持点击不同路径的设备时，连带路径一起切换，并找到对应的设备监视。</item>
    ///             <item>修复边缘网关客户端在配置标量请求的界面上配置字符串读取时，修复长度设置无效的BUG。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2021-10-29" version="1.0.4" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>修复服务器程序的MQTT发布设备主题数据时，设备主题不携带网关名称的BUG，之前重复了两次的名称。</item>
    ///             <item>服务器端程序检查设备采集线程宕机的功能完善，分时段提醒，宕机60-150秒每隔10秒提醒，宕机150秒以上每秒提醒。</item>
    ///             <item>修复服务器端西门子PLC的配置节点，配置了rack，slot，但是值保存不正确的bug。</item>
    ///             <item>服务器端西门子PLC和AB的CIP协议的读取字符串功能优化，长度为0的时候，使用原生的匹配自动长度的字符串。</item>
    ///             <item>服务器端的MQTT服务器的主题信息支持配置是否启用通配符，默认启用，可以在客户端配置不启用。</item>
    ///             <item>客户端节点名称不允许输入一些特殊的字符，属性控件显示的效果优化，波特率支持可选，属性顺序调整，属性分类顺序重新优化。</item>
    ///             <item>客户端配置结构体的界面增加空选择的判断，当编译时结构体资源不存在时，也进行相关的提示。</item>
    ///             <item>服务器的设备采集线程支持暂停继续操作，提供API接口，在客户端的监控界面点击状态按钮即可启停设备采集状态。</item>
    ///             <item>客户端的插件的属性也进行了排序显示，按照普遍的规则，节点属性最前面进行排序操作。</item>
    ///             <item>服务器端程序修复上传远程服务器MQTT或是Redis间隔时间设置失败的bug，之前无论设置多少，均为1秒。</item>
    ///             <item>服务器修复单标量字符串请求后解析实际字符串在某些情况下会触发的bug，使用bytetransform来解析字符串，这样欧姆龙就可以获取正确的字符串。</item>
    ///             <item>边缘网关系统的数据标记的ScalarDataNode不从GourpNode继承，因为json序列化会异常，直接添加Name及Description属性。</item>
    ///             <item>其他的代码优化，客户端界面的细节调整。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2021-12-20" version="1.0.5" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>客户端配置请求数据变换的控件 RequestDataScaleControl: 在配置小数据时，修复配置失败的bug。</item>
    ///             <item>服务器的欧姆龙设备增加了配置字符串是否翻转的功能，使用FinsTcp，FinsUdp，HostLink等。</item>
    ///             <item>服务器端支持台达的AS系列，添加AB-cip协议的PCCC格式的通信设备，添加丰炜PLC的通信配置功能。</item>
    ///             <item>服务器端的所有设备支持了缓存标签的添加操作，客户端支持添加缓存的标签信息，和PLC无关，但是可以通过API接口读写，修改内存数据。</item>
    ///             <item>服务器端创建设备对象时，代码重新设计优化，对创建设备进行错误捕获，防止IP地址，端口等输错导致服务器系统崩溃的bug。</item>
    ///             <item>服务器端修复网关日志设置为日期时间时，但是无效，不生成日志的bug。</item>
    ///             <item>服务器串口相关的设备显示的设备信息包含串口信息增加数据位，校验，停止位，例如 COM1:9600-8-N-1</item>
    ///             <item>服务器AB的PLC的CIP协议的字符串的标量请求进行优化，自动解析动态长度信息。</item>
    ///             <item>服务器修复三菱A1E协议的站号默认值，修改为255，使用默认值无法进行通信的bug。</item>
    ///             <item>服务器的所有设备对象增加异常消息属性，设备实例化异常，或是插件不存在，等提示出来，再客户端界面上进行红色闪烁报警。</item>
    ///             <item>服务器端优化对线程假死的监测机制，优先尝试重启线程操作，如果30秒内线程仍然不活动，再尝试重启程序。</item>
    ///             <item>串口转DTU，网口转DTU测试时发现会无响应数据，不再连接DTU的bug，现在添加数据活动监测，超过60秒不活动则重连DTU。</item>
    ///             <item>三菱的编程口协议支持激活选择，设为True之后，和设备通信之前会进行Atcive操作，尝试三次。</item>
    ///             <item>客户端界面的其他的一些优化，比如插件的图标优化，项目增加.net6.0的支持。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2022-1-5" version="1.1.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>服务器端修复AllenBradleyPCCC协议在读取字符串的时候，但是解析不正确的bug，之前会存在乱码。</item>
    ///             <item>整个边缘网关系统更新到最新的hslcommunication版本，支持了管道（包括串口和网口管道）的配置，管道下可以挂设备，下面的设备使用同一管道进行通信。</item>
    ///             <item>边缘网关系统支持了模板设备的配置，例如同一种设备，PLC类型一致，且采集点位一致，只是IP不一样，就可以使用模板来简化重复的配置信息。</item>
    ///             <item>边缘网关系统支持了基于模板的插件，可设置图标，这个插件DLL不依赖任何的其他dll，支持兼容升级，主要由XML元素组合而成，适用于基于网关现有PLC协议的设备。</item>
    ///             <item>边缘网关系统针对标量请求节点和标量解析节点生成XML进行简化，未启用的功能则不生成到XML，降低XML文件问价，提高加载和传送速度。和老版本的XML依然兼容。</item>
    ///             <item>客户端的配置界面的设备节点右键菜单列表，新增导出XML数据的功能，可以直接复制该XML文本。</item>
    ///             <item>修复客户端配置了中间缓存变量，下载到网关，但是从网关加载时候，却不显示之前已经配置的中间缓存变量的bug。</item>
    ///             <item>边缘网关系统的标量请求以及标量规则解析支持显示别名设置，再客户端上监视时候，如果设置了别名，就使用别名显示。</item>
    ///             <item>边缘网关客户端支持一键全部暂停和全部启动设备继续采集的功能，可以方便的调试某个设备的采集数据信息。</item>
    ///             <item>客户端增加网关的连接对象时，支持别名的设置，如果设置了别名，则显示别名，否则显示网关的唯一ID信息。</item>
    ///             <item>客户端监视边缘网关的设备数据时，双击数据名称显示数据地址的功能，显示的文本调整为可复制。</item>
    ///             <item>客户端界面的图标资源跟随网关对象 EdgeServerSettings，不同的网关的图片列表可以共同存在，是否一键启停也根据网关对象。</item>
    ///             <item>边缘网关服务器加载插件的代码优化，对错误捕获，即使某个插件加载异常，不影响其他插件。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision><revision date="2022-2-7" version="1.1.1" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关客户端不绑定网关服务器，修复直接创建本地设备配置文件时提示空指针的异常。</item>
    ///             <item>边缘网关服务器修复某些特殊的情况下导致设备掉线，在线恢复后，设备状态不会更新的bug。</item>
    ///             <item>边缘网关服务器新增记录最近10次的运行开始停止情况，在客户端查看"网关信息监控"来确认，区分异常关闭和手动关闭，附带操作消息。</item>
    ///             <item>边缘网关系统更新hslcommunication到最新的10.5.0版本，AB的cip协议支持路由设定，支持访问其他站点数据。</item>
    ///             <item>边缘网关客户端修复在配置设备采集信息时，插件的图片显示不正确的bug，此前显示为一个叉。</item>
    ///             <item>边缘网关服务器调整了默认日志等级为INFO，不再是DEBUG了，如果需要显示调试信息，则可以从客户端修改日志等级。</item>
    ///             <item>边缘网关客户端新增一个guid的测试界面，支持生成不同格式的字符串信息，支持大小写，支持历史记录30个数据值。</item>
    ///             <item>边缘网关客户端新增了主题的设置，初步定为浅色，蓝色，深色，其中初步搞了深色的主界面，还需要优化。</item>
    ///             <item>边缘网关服务器支持了从客户端进行更新服务器采集程序的功能，windows及linux均支持，优化了linux的重启操作。</item>
    ///             <item>边缘网关服务器支持了从客户端进行安装，更新，删除服务器插件的操作，安装新插件不需要重启，立即生效，其余需要重启服务器生效。</item>
    ///             <item>边缘网关客户端支持配置设备的本地结构体资源，该结构体资源只能用于当前得设备，如果和全局结构体名称冲突，则优先选择本地的资源。</item>
    ///             <item>边缘网关系统进一步精简优化请求节点和规则解析节点生成XML文件的内容，请求信息编辑界面支持针对结构体导出XML文件。</item>
    ///             <item>其他的一些小优化。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     </revision><revision date="2022-3-11" version="1.2.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关客户端修复打开网关的插件管理界面的时候，某些特殊情况(插件定义的图片不是标准的16*16)，显示插件的图标不正常的bug。</item>
    ///             <item>边缘网关客户端显示原始请求配置自定义解析的界面，显示的数据信息分为上下交替显示，防止名词过长，全部挤在一起。</item>
    ///             <item>边缘网关服务器修复结构体关联报警，在原始字节请求解析了结构体（结构体属性关联了报警），但是报警不触发的bug，原因来自报警资源关联错误。</item>
    ///             <item>边缘网关系统服务器修复上传远程MQTT时，ClientID设置无效的bug，并且默认值修改为空，默认使用网关的服务器名称，当然也可以自定义设置。</item>
    ///             <item>边缘网关系统的ModbusTcp设备配置新增一个属性，是否增加对设备返回消息ID的一致性校验操作，适配某些特殊设备。</item>
    ///             <item>边缘网关系统服务器调整MQTT上传的clientid，topic信息(topic不再携带网关名，直接是设备唯一路径信息)，支持retain配置。注释优化，</item>
    ///             <item>边缘网关系统在串口转dtu和网口转dtu的特殊设备里，支持了注册包消息的自定义操作，允许连接自定义的dtu服务器。</item>
    ///             <item>边缘网关系统全面支持了所有节点的别名设置，在Viewer上监视时显示别名操作，在配置显示及API请求上一律使用真实Name进行操作。</item>
    ///             <item>边缘网关服务器修复设备在初始化失败的情况下，然后继续采集导致设备在线状态频繁跳变在线，不在线状态的bug。</item>
    ///             <item>边缘网关服务器针对核心设备采集功能代码优化，提炼功能类，删除重复的activetime标记，采集代码优化。增加了设备线程状态机的机制。</item>
    ///             <item>边缘网关服务器增加DeviceThreadExecuteControl类，用于设备采集线程调度控制的对象，可以用来控制多个设备采集在一个线程下执行，也即是顺序执行的操作。</item>
    ///             <item>边缘网关服务器采集设备新增CurrentThreadControlsEnable属性，支持线程控制权设置，可以由自己控制调度采集，或是其他线程来控制调度采集。</item>
    ///             <item>边缘网关服务器的串口管道和网口管道的调度机制调整为单线程调度，新增单纯的单线程调度的分类节点，对该节点下设备实行单线程调度采集。</item>
    ///             <item>边缘网关系统增加了对SQL Server数据库的支持，目前初级阶段，通过设备增加数据库请求来实现定时的执行SQL语句，可以将设备的数据作为参数传入。</item>
    ///             <item>边缘网关系统的API接口支持对数据库的执行操作，可由外部传入SQL语句进行直接的调用操作，方便自己扩展小工具。</item>
    ///             <item>本边缘网关系统可以配合Node-Red软件来实现一些脚本化的流程操作，具体可以参考手册文档。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2022-4-18" version="1.2.1" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关服务器状态数据中的运行时环境和编译时环境调整为自动获取，获取更加方便识别的字符串信息，自动获取X86还是X64环境。</item>
    ///             <item>边缘网关服务器优化检测设备线程活动状态的机制，检测所有的设备情况，而不是有设备掉线立即返回。</item>
    ///             <item>边缘网关客户端修复在修改了网关密码后，连接连不上的bug，现在可以正确的更新界面的参数，还支持二次编辑连接参数功能。</item>
    ///             <item>边缘网关客户端修复在没有连接网关服务器时，直接点击新建本地配置文件发生空引用异常的bug。</item>
    ///             <item>边缘网关服务器在写入数据的接口中，也即是设备对象的WriteValueByName接口里，当配置为bool数据时，本文支持，'1','0','True','False','On','Off'表示方法。</item>
    ///             <item>边缘网关服务器设备采集时，修复当未连接成功时，且请求的地址有输入错误的情况下，导致掉线时间不增加，一直为2秒的bug。</item>
    ///             <item>边缘网关系统更新hslcommunication到最新的10.6.1版本，增加信捷串口及网口的PLC，优化倍福及欧姆龙FinsUdp的配置信息。</item>
    ///             <item>边缘网关系统的RegularNodeTypeItem: 在配置解析的界面中，增加BoolOfByte类型，实际是bool值，但是实际是按照字节为单位的解析的。用于特殊场景的bool，例如倍福的PLC</item>
    ///             <item>边缘网关服务器的原始字节请求的时候支持批量的地址请求，例如西门子和倍福支持输入批量的地址及长度信息，获取结果数据，然后进行原始字节解析。</item>
    ///             <item>边缘网关服务器的fanuc机床支持批量读取寄存器数据，然后按照原始字节请求配置解析，支持写入数据操作。</item>
    ///             <item>边缘网关客户端的SourceDataViewControl: 在配置原始字节请求的时候，如果是动态的字节长度，本来只显示最大配置的字节信息，现在多显示30个字节，边框及字颜色淡灰色。</item>
    ///             <item>边缘网关服务器的SettingsServer增加一个允许的设备数上限属性，默认50，客户端配置时获取该值，并对配置文件的设备数量进行校验，配置界面支持实时显示设备数量。</item>
    ///             <item>边缘网关服务器的报警开始和结束，服务器自身的MQTT服务器，或是发布的远程MQTT都进行发布操作，主题为 __alarm (两个下划线)，方便实现自定义操作，配置界面可以自由配置是否发布。</item>
    ///             <item>边缘网关服务器的DeviceCoreBase 的请求时间的优化，在配置类似1000的请求间歇时，会自动修正请求时间偏差，现在更加准确的按照间歇执行，即便存在微小的时间波动。</item>
    ///             <item>边缘网关服务器支持了多个配置文件存储，同时也支持了连续10次配置文件变更的历史记录，方便大家切换不同的配置文件调试，已经根据历史配置文件恢复采集。</item>
    ///             <item>边缘网关客户端在配置设备采集界面上，支持了节点的复制粘贴操作，选中节点后（支持整个分类，设备，单个节点），再选中别的节点进行粘贴操作，按键为ctrl+c/v。</item>
    ///             <item>边缘网关服务器和客户端的代码优化，精简，提升了一些稳定性。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2022-6-28" version="1.3.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关客户端禁用Edge网关客户端的网关信息界面的列（值）的排序功能，因为这将导致排序奔溃。</item>
    ///             <item>边缘网关客户端软件修复设备新增界面刚打开，就立即关闭导致不可恢复的异常的bug。</item>
    ///             <item>边缘网关客户端修复网关信息监视界面，在网关服务器断开程序的情况下，在关闭的时候，发生死锁导致程序无期限卡死的bug。</item>
    ///             <item>边缘网关客户端，在双击网关节点监视网关数据的时候，增加标记禁用频繁快速双击操作，修复可能会造成无法响应的bug。</item>
    ///             <item>DLT645配置节点新增加一个属性ReadAddressAfterOpen，用来表示是否在初始化时读取站号信息，新增三个功能方法的接口。</item>
    ///             <item>边缘网关系统串口的节点基类里，新增属性Sleeptime属性，默认为负数，即不设置，表示串口接收数据的最小循环时间，在波特率很低时，可以设置大一点的值，比如100</item>
    ///             <item>边缘网关客户端新增设备或是编辑串口设备时，串口号信息也可以根据服务器的资源进行直接选择的操作。</item>
    ///             <item>边缘网关客户端在添加网关的对象的时候，修复ip地址输入错误导致的异常奔溃。</item>
    ///             <item>NodeDlt645OverTcp: 修复DLT645-2007 OverTcp节点配置后，但是查看节点仍然是 DLT RTU的bug。</item>
    ///             <item>边缘网关系统在网关服务器启动之后，如果客户端立即连接上来的话，增加启动状态延时的操作，防止客户端过快的连接导致解析失败的bug。</item>
    ///             <item>边缘网关服务器修复了当上传的MQTT地址配置为网址时，服务器启动时网络不通，过会网络再通时，MQTT连接无法恢复的bug。</item>
    ///             <item>边缘网关客户端添加新的网关信息时，修复当地址输入域名时报异常错误的bug，现在支持了域名的输入。</item>
    ///             <item>边缘网关服务器系统支持了定时写入的请求(可写入变量及绝对地址)，支持写入恒定的数值，或是写入可选的数组，以及范围数组信息，网关其他点位，具体参考手册。</item>
    ///             <item>边缘网关服务器系统定时写入的方法支持了eval脚本的形式，例如输入：eval=(short)(50*sin(2*PI*x/100) + 80)   可以获取连续变化的正弦曲线数据，x为请求累计次数。</item>
    ///             <item>边缘网关系统在原始字节请求里，当配置解析为字符串的情况之下，支持了所有字节偏移二次计算及字节顺序调整的功能。</item>
    ///             <item>边缘网关系统串口转网口的设备类对象，支持选择转换模式，目前支持透传，以及modbusrtu转modbus tcp协议。</item>
    ///             <item>边缘网关系统服务器如果当前的设备配置为模板设备的时候，当前实例设备下面挂了额外的请求信息，那么现在也进行解析操作，需要注意的是，如果请求重名，可能会发生数据覆盖</item>
    ///             <item>更新HslCommunication到最新版，MelsecFxSerialOverTcp支持了GOT透传的属性设置，添加了ab-plc的CIP的设备，增加欧姆的cip及基于连接的cip协议实现。</item>
    ///             <item>边缘网关系统新增加虚拟CIP服务器，虚拟的PCCC服务器，可以直接从服务端创建并写入标签数据。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2022-8-18" version="1.4.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关服务器端设备配置文件的API接口新增支持JSON格式的文件获取及配置，网关客户端的设备的导出也支持导出json格式文本。</item>
    ///             <item>边缘网关系统客户端修复查看设备的OEE数据，结果发生失败异常的bug。</item>
    ///             <item>Fanuc机床修复设备采集报警信息等中文情况下，显示中文乱码的bug，原因来自默认编码获取异常。</item>
    ///             <item>Database: 数据库优化，SQLServer支持了多实例，以及再客户端配置界面支持数据库的测试连接操作。</item>
    ///             <item>边缘网关系统支持了汇川的串口，网口设备，支持了DLT698的设备。</item>
    ///             <item>边缘网关系统的大量的设备重写实现了原始字节请求时，解析各个子节点的真实物理地址的功能代码，包括三菱，富士，永宏，松下，西门子，丰炜，GE，基恩士，横河等</item>
    ///             <item>边缘网关系统的NodeAlarmHex修复了对BOOL变量无效的bug，增加了一个属性：RevserveByWord，对hex变量解析时支持按字反转数据。</item>
    ///             <item>边缘网关客户端修复创建HEX报警资源时，无法绑定bool类型数据或是byte[]类型数据的bug，现在可以正常绑定了。</item>
    ///             <item>边缘网关服务器设备日志部分代码优化，隔离了EdgeLog出来，边缘网关服务器端支持订阅某个设备的日志操作。增加发布电文日志功能。</item>
    ///             <item>边缘网关客户端的界面支持显示当前查看设备的日志信息，支持查看通信的报文信息，支持随意的切换，并优化了点暗黑主体下的显示效果。</item>
    ///             <item>边缘网关服务器新增一个配置属性，是否在读取失败的时候，清空缓存的数据值，如果配置为True，则所有的数据，包括数组，结构体都会设置为null。</item>
    ///             <item>边缘网关服务器修复模板设备解析不正确的bug，导致无法加载相关的请求信息。</item>
    ///             <item>边缘网关服务器修复配置了三菱MCR设备但是再网关解析失败的bug，之前会自动解析成MC设备并因为节点不存在而发生异常。</item>
    ///             <item>边缘网关系统新增了TCP转TCP的功能设备，支持多连接，可以从网关追踪所有的报文通信交互细节。</item>
    ///             <item>边缘网关系统新增加串口转MQTT，以及网口转MQTT的特殊功能设备，从串口或是网口收到数据后即从MQTT发布出来，如果第三方客户端发布回该主题，则自动发数据给设备。</item>
    ///             <item>其他的串口转网口，串口转DTU，网口转DTU设备进行优化，支持显示数据通信字节数量，数据请求总数等等信息。</item>
    ///             <item>边缘网关DLT645设备支持配置是否初始化激活，如果配置为True，则连接设备成功之后，立即发送 FE FE FE FE</item>
    ///             <item>其他的代码优化。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2022-11-19" version="1.5.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关服务器的SQL SERVER数据库部分增加锁，防止多线程调用时发生竞争的异常。</item>
    ///             <item>边缘网关服务器的DeviceSerialToTcp串口转网口的设备类，修复串口休息时间设置无效的bug。</item>
    ///             <item>边缘网关服务器修复汇川PLC的节点参数的DataFormate，地址是否从0开始，字符串是否反转等参数设置不生效的bug。</item>
    ///             <item>边缘网关服务器的DLT645节点，新增属性EnableCodeFE，用来设置DLT每次报文自动追加前导 FE FE FE FE字节数据。</item>
    ///             <item>边缘网关服务器的设备基类DeviceCoreBase: 修复设备使用本地结构体对象时，在配置原始字节数据解析设置展开操作时，无法关联本地结构体的bug。</item>
    ///             <item>边缘网关服务器修复当设备挂到管道下面时，设备自身的串口信息或是网口信息从管道信息不继承得问题，不继承的话会在控制台显示错误的串口或是网口信息</item>
    ///             <item>边缘网关服务器设备的基础初始化方法再度优化，使用设备统一关联资源来传参，更加易于扩展和实现，交由设备集合统一管理，大量的代码更新。</item>
    ///             <item>边缘网关服务器串口管道，网口管道，单线程管道，新增属性UseAsGroupNode，默认为true，当做路径使用，当设置false时，则对客户端表现为隐藏该中间路径信息。</item>
    ///             <item>边缘网关的标量的请求，以及规则解析数据支持单位的设置，当使用网关客户端设置了单位信息后，在网关客户端上即显示了单位信息。</item>
    ///             <item>边缘网关客户端修复在修改网关的设备配置的时候，节点显示改变为仅名称的bug，现在正确显示名称和别名的组合。</item>
    ///             <item>边缘网关系统支持服务器端的串口映射功能，例如服务器的串口COM1映射为RTS485-1，在硬件网关时可以和硬件实际的名称匹配，方便用户配置。</item>
    ///             <item>边缘网关系统的标量请求以及规则解析节点新增属性StringEndwithZero，针对字符串变量，可以配置是否按照 0x00 字符结尾。</item>
    ///             <item>边缘网关系统的原始字节请求中，地址支持了输入本网关其他设备的原始字节请求信息，方便关联解析。修复请求次数设置0，实际会请求1次的问题。</item>
    ///             <item>边缘网关系统支持了CJT188协议，客户端配置定时写入的时候，修复名称列表不显示的bug。</item>
    ///             <item>边缘网关服务器的modbus服务器新增每个站号数据是否隔离的属性配置，StationDataIsolation属性可以设置相关的功能，配置为true时占100M内存。</item>
    ///             <item>边缘网关系统新增串口转远程MQTT的设备，网口转远程MQTT的设备，现在可以从云服务器的MQTT的Topic实现读写PLC数据。</item>
    ///             <item>边缘网关服务器支持了websocket的订阅操作，地址为 ws:\\127.0.0.1:525，使用格式化的JSON字符串来订阅取消订阅，具体参考手册</item>
    ///             <item>边缘网关服务器修复串口类节点的基类DeviceNodeSerial对RtsEnable属性设置存储解析无效的bug。</item>
    ///             <item>边缘网关系统的HslCommunication组件更新到最新版。Modbus支持指定写入的功能码。修复CJT188的通信异常的bug，新增DLT1997设备。</item>
    ///             <item>其他的代码优化。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2022-11-20" version="1.5.1" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>DeviceMelsecMc3E: 修复三菱的3E协议的初始化设备资源失败的bug。</item>
    ///             <item>边缘网关系统修复边缘网关系统的设备测试通信是否成功，但是发生异常的问题。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2023-3-1" version="1.6.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关系统中，当网关配置了字符串的标量或是结构体解析时，支持编码为非ASCII码的数据写入操作。</item>
    ///             <item>网关相关的库更新到最新版，DLT的地址支持reverse=false;00-00-00-00的指令，用来适配极少数的翻转，websocket也支持了通配符。</item>
    ///             <item>网关系统的RegularNode: 在原始字节请求配置字符串解析操作时，支持中文等其他编写的读取和写入操作，会自动根据配置的信息反解出字节数组，然后写入。</item>
    ///             <item>网关系统新增自由的TCP协议设备FreedomTcp: 可以配置消息的完整性检查，配置解析的高地位数据信息，然后配置自定义解析，支持配置ASCII格式解析。</item>
    ///             <item>网关系统新增自由的串口设备FreedomSerial: 可以配置消息的完整性检查，配置解析的高地位数据信息，然后配置自定义解析，支持配置ASCII格式解析。</item>
    ///             <item>网关客户端: 在配置原始字节请求时，修复原始字节数据预览时显示空的异常，现在还支持显示原始的十六进制的数据信息，以及切换ASCII数据查看。</item>
    ///             <item>边缘网关服务器在解析原始字节数据配置了 BoolOfByte 的时候，对True条件判断不正确的bug。</item>
    ///             <item>网关系统新增基础的数据类型 intOfString 和 doubleOfString，基于字符串格式的整数和浮点数，例如"0012"字符串会转成 int 类型的 12。</item>
    ///             <item>网关系统服务器的设备不在线的时候，尝试使用rpc接口或是webapi接口来实现写入设备数据的话，会直接返回设备不在线的错误结果。</item>
    ///             <item>网关服务器的MQTTServer及WebsocketServer支持其他客户端根据数据标签发布的操作，会自动写入到设备中去，也就是支持MQTT客户端发布topic写入设备数据。</item>
    ///             <item>网关服务器的WebsocketServer支持了发布报警信息主题，当报警开始和结束的时候，将会自动发布主题为"__alarm"的JSON数据信息。</item>
    ///             <item>网关系统的所有数据标签订阅的功能支持了数组的标签，无论是bool[],short[]等等，无论是来自配置的单个的标量请求，还是来自原始字节配置的解析的数组都支持。</item>
    ///             <item>网关系统的服务器，发布第三方的MQTT服务器的功能支持了设备数据变化再上报MQTT，影响范围包括单网关上传，还是单设备上传模式，有效降低无用数据上传。</item>
    ///             <item>网关所有设备的使能状态(DeviceEnable)改为DeviceStatusType枚举，支持配置在用，停用，停用并隐藏。客户端显示停用设备为深灰色。隐藏时则客户端看不到设备。</item>
    ///             <item>网关系统的报警开始或是结束支持配置数据库存储操作，数据库配置在报警资源节点下配置，可以选择对应的数据库资源，然后执行SQL语言存储到数据库。</item>
    ///             <item>网关系统客户端的配置界面，显示原始字节解析的可视化界面针对bool变量显示优化，选中当前的bool标签时，修复同字节其他其他bool标签显示会丢失的bug。</item>
    ///             <item>网关服务器支持记录初始运行状态，然后网关客户端的网关信息监控菜单可以查看这个初始化的消息内容，更加便捷的进行调试。</item>
    ///             <item>网关服务器在启动核心的521端口（可修改）服务器之前，增加一次对这个端口是否打开的检测，如果已经被使用，则不启动当前的网关服务，避免未知的意外情况发生。</item>
    ///             <item>网关系统新增OpenProtocol设备的支持，目前仅支持一个API接口的调用及测试，网关系统增加对线程不活动时状态记录。</item>
    ///             <item>其他的代码优化。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2023-6-5" version="1.7.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关服务器修复当使用自定义的请求实例化对象时，指定请求间隔无效的bug，通常发生于自定义的插件开发的时候。</item>
    ///             <item>边缘网关系统的RegularScalarNode规则解析类修复当配置了字符串\0结尾，重新写入设备时，不追加\0的bug，还支持了偶数长度的自动扩充实现。</item>
    ///             <item>边缘网关系统优化在写入字符串到字符串的标量节点时，写入长度为奇数长度时，自动扩充到配置时完整的数据长度，解决字符串粘上旧数据的bug。</item>
    ///             <item>边缘网关系统的三菱plc设备的MC协议，MCUDP协议新增属性EnableWriteBitToWord，当开启true之后，即使配置字寄存器的bool，也可以正确的写入数据。</item>
    ///             <item>网关客户端在配置服务器的采集设备XML信息时，当点击保存的时候，自动生成本网关对象的别名，如果别名为空，就网关名的XML名字文件。</item>
    ///             <item>网关客户端和服务器通信时新增加配置是否使用加密，客户端再连接时支持自由选择，在性能比较差的服务器，使用不加密可以流畅很多。</item>
    ///             <item>网关系统修复网关在同一个目录下，配置了两个及以上管道对象且都配置为不使用路径的情况下，网关服务器触发异常的bug。</item>
    ///             <item>网关三菱的3E协议下，当配置的原始字节请求时，支持了M,L,F,V,S,B等位地址时，也可以正确解析偏移地址解析。</item>
    ///             <item>网关系统针对bool的标量请求及解析的情况，支持启用数据转换，bool值的false, true自动转为0，1，然后参数计算，生成double结果。</item>
    ///             <item>Modbus服务器支持新增地址映射其他设备的功能，当远程客户端写入指定区域的地址时，网关会尝试写入绑定设备的指定地址。</item>
    ///             <item>网关客户端的采集信息配置界面，鼠标右键设备菜单里新增复制设备当前路径的子菜单，点击复制到粘贴板，例如: 工厂一/车间一/设备A</item>
    ///             <item>边缘网关客户端启动时，所有的已经保存的网关列表将后台连接一次操作，如果发现可以连接，图标将标记可以连接的状态(显示带有绿色标记)。</item>
    ///             <item>网关服务器在使用插件的功能时，修复插件类属性中，设置了Byte属性时，但是配置界面无法配置成功的bug。</item>
    ///             <item>网关服务器的定时写入的使用的脚本从CSharp.Scripting更改为DynamicExpresso.Core组件，直接执行表达式，支持Math函数，支持使用当前设备的点位数据信息计算。</item>
    ///             <item>网关服务器端重新设计网关资源对象EdgeDeviceResources，设备类对象可以拿到这个信息，从而拿到所有的网关配置信息，方便进行其他操作，数据变化上传MQTT优化。</item>
    ///             <item>网关优化了数据订阅部分的功能信息，支持了结构体点位也同时支持数据点变化的情况推送，修复单数据点位推送（强制定时推送）异常的bug。</item>
    ///             <item>网关服务器针对当前运行的线程数量也增加记录数组缓存，客户端监视网关信息界面支持监视线程曲线。</item>
    ///             <item>网关在配置新增结构体解析的时候，原先的支持结构体数据是否展开修改为三种方式，数据不展开，数据展开（结构体对象+属性命名），展开（属性名）。</item>
    ///             <item>网关服务器支持了字符串数组的读写，以及字符串数组的规则解析功能，以及正确的反写回设备，BCD码也支持了数组的配置操作，支持数据反写。</item>
    ///             <item>网关系统的fanuc机床修复当只配置一个系统状态读取时，始终读取不到数据的bug，opcda组件支持读取字符串数组。</item>
    ///             <item>边缘网关系统上传数据到第三方MQTT服务器功能支持配置证书，以便实现数据安全加密上传，需要使用客户端配置证书</item>
    ///             <item>边缘网关系统的串口映射功能，如果映射了空的字符串，那么就隐藏该串口名称信息。其他线程相关的代码优化，客户端一些小问题的修复。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2023-6-13" version="1.7.1" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>网关优化反写byte数组的数据到设备的实现，如果检测到hex内容，则直接转字节数组写入，如果使用[1,2,3]数组表示形式，则使用整数数组转换写入操作。</item>
    ///             <item>网关的BCD节点的请求方式，以及从原始字节配置解析的BCD，现在支持了数据转换功能，当进行反写数据时，又会根据之前的配置信息，反向转换写入原始数据信息。</item>
    ///             <item>NodeOmronCipNet以及NodeAllenBradleyCIP支持了使用ReadBatch( string[] address, ushort[] length )接口来批量读取地址，然后配置解析的功能。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2023-11-17" version="1.7.2" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>网关客户端修复添加模板设备的时候，当设备名称没有重新修改的时候，会引发模板名称也变化的bug。</item>
    ///             <item>边缘网关服务器端修复倍福ADS协议的plc设置目标网络节点后，但是实际会跳变到ip地址的bug，现在可以正确的设置节点号。</item>
    ///             <item>边缘网关服务器的自由TcpFreedom: 新增远程客户端写入的功能方法 WriteAsync, 并且出发报文级别的通信。</item>
    ///             <item>边缘网关所有的网络类的设备节点支持设置这个HexBeforeSend属性，表示数据发送前额外的报文信息，可以用来解决lora的报文头问题。</item>
    ///             <item>边缘网关修复串口管道里串口名称设置为空的时候，服务器启动异常的bug，网关客户端在配置空串口名称的管道时，修改为无法下载到服务器。</item>
    ///             <item>边缘网关客户端软件在编辑网关的配置参数的时候，立即请求一次服务器的串口资源，初始化可选的属性列表。</item>
    ///             <item>边缘网关所有的Modbus设备开放三个方法接口ChangeDataFormat,ChangeIsStringReverse,ChangeAddressStartWithZero可以供客户端监视数据时实时调用方法修改modbus的这些属性信息。</item>
    ///             <item>边缘网关系统采集的数据的变换操作新增支持bool反转（仅对bool有效），以及表达式变换(可以自由变换)，表达式变换后数据权限变为只读。</item>
    ///             <item>边缘网关服务器的DeviceThreadExecuteControl: 设备单线程调度的类里增加属性SleepTimeBetweenDevices，设置大于0就在设备切换时，进行指定时间的休眠。</item>
    ///             <item>边缘网关服务器接口EdgeServices: 新增批量读取点位的接口DeviceDataArray，以及新增批量写入的接口WriteDataArray，方便快速的批量处理。</item>
    ///             <item>边缘网关的三菱3E协议修复M点位原始字节请求时，配置bool解析后数据变成只读的bug，现在可以正确的解析实际地址，1E协议也实现了这样的功能。</item>
    ///             <item>边缘网关系统在配置了上传网关数据到普通的MQTT服务器的时候，自动订阅 网关名+"/WriteData" 主题，支持了远程反写的操作。</item>
    ///             <item>边缘网关系统上传MQTT的功能支持配置连接JetLinks物联网平台，从平台上操作数据标签的写入操作，获取数据，以及功能调用，具体参考手册。</item>
    ///             <item>网关系统的设备节点类新增PreTopic属性，再上传到MQTT服务器时，可以额外指定主题前置信息，在上传JetLinks物联网平台时需要用到该属性。</item>
    ///             <item>边缘网关系统支持了安川机器人YRC1000TcpNet及YRCHighEthernet的数据采集，采用方法调用的方式来实现的，可以操作机器人的基本信息。</item>
    ///             <item>网关系统修复了DLT645-2007和DLT645-1997，和DLT698在读取字符串的时候解析异常的bug，反写字符串的数据的时候，写入的值不正确的bug。。</item>
    ///             <item>边缘网关系统的客户端在新增网关设备的旁边，添加了一个刷新网关列表中在线状态的菜单，随时可以查看最新的网关是否在线。</item>
    ///             <item>边缘网关客户端主界面上，点击了在线设备数量，或是暂停数量，或是异常数量，将会显示一个新的列表，显示当前所有设备的细节信息。</item>
    ///             <item>网关系统客户端界面上，网关节点右键菜单支持直接关闭，部分的图标优化，网关客户端双击写入操作修复在某些异常情况下重复弹窗的bug。</item>
    ///             <item>边缘网关系统的服务器新增加记录连续的10次的关于采集的重大异常信息记录，并在客户端上网关信息里显示出来。</item>
    ///             <item>边缘网关服务器的采集设备基类新增切换设备采集线程执行方法的功能，针对远程写入操作执行线程切换，从而让设备几乎所有操作都在设备线程上执行。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2023-12-1" version="1.7.3" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关系统优化了串口管道，网口管道，单线程管道里针对有一个设备不在线时的调度逻辑，设备线程活动时间取共享线程的时间。</item>
    ///             <item>边缘网关系统服务器上传MQTT到JetLinks时，针对用户名密码的时间搓获取，使用本地的起始时间。</item>
    ///             <item>边缘网关系统所有的标量节点或是解析节点，当配置了值变换，或是表达式变换时，指定0个小数时，数据类型强制为 long 类型。</item>
    ///             <item>边缘网关服务器修复在串口及UDP设备中，读取字符串的请求时，读取长度识别错误的bug，之前会读取超长地址，现在长度正确了。</item>
    ///             <item>边缘网关系统新增设备分身功能，添加一个富设备的时候可以关联其他实体的PLC，关联之后就变成了PLC的分身设备，详细看文档。</item>
    ///             <item>网关服务器的AllenBradleyMicroCip设备当点位配置了字符串读取的情况下，再执行远程写入操作的时候判断0xDA的字符串的话，正确的反解出byte[]然后进行写入。</item>
    ///             <item>边缘网关服务器基于共享串口的单线程管道里，支持设置设备切换的时候进行关闭的操作。</item>
    ///             <item>修复网关系统float类型的数据，进行值变换后，类型仍然为float的小问题，现在修改为double。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2023-12-12" version="1.7.4" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>边缘网关系统服务器调用设备的方法的接口CallDeviceMethod的HTTP方法从GET调整为POST</item>
    ///             <item>边缘网关服务器的 DeviceAllenBradleyMicroCip 设备修复字符串标签写入时，因为线程任务的二次调度导致死锁，然后引发超时的bug修复。</item>
    ///             <item>网关系统的设备单数据标签对象 DeviceSingleAddressLabel: 修复配置了读取失败重置为空的功能下，采集经失败恢复成功后，解析数据报异常的bug。</item>
    ///             <item>修复当边缘网关调用方法请求的时候，请求失败且网关系统配置了重置为NULL的情况下，修复设备数据不会重置NULL的bug。</item>
    ///             <item>网关系统的 jetlinks: 支持了jetlinks的设备上线，下线的逻辑响应。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2024-08-19" version="1.7.5" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>网关服务器Websocket服务器在收到了错误的命令，或是解析json字符串异常时，均返回错误消息给客户端，用于方便查询错误。</item>
    ///             <item>网关服务器里的Websocket服务器针对客户端远程publish功能，执行写入失败的情况，返回一串失败的信息给客户端。</item>
    ///             <item>网关服务器设备基类DeviceCoreBase: 修复串口转TCP，TCP转TCP，转MQTT等相关类的请求关联设备为空的bug.</item>
    ///             <item>网关的Websocket服务器在客户端第一次订阅的时候，立即返回一次数据信息，然后新增Read指令，可以用来定时读网关数据</item>
    ///             <item>网关服务器修复网关运行到期后，设备线程退出，但是检查设备线程状态仍然不停止的bug。</item>
    ///             <item>网关服务器修复西门子的S7协议，当配置字符串且长度为自适应的0时，通过远程接口写入数据不生效的bug。</item>
    ///             <item>网关服务器设备DeviceSerialToTcp: 修复串口转TCP时，配置RTU转TCP模式的情况下，收到rtu报文不完整转TCP后不正确的bug。</item>
    ///             <item>网关服务器当设备配置请求停止后，或是设备状态处于停用的时候，不再上传设备的数据到云服务器上去。</item>
    ///             <item>DeviceNodeNet: 基于TCP的设备支持设置冗余的IP地址，设备请求IP地址连续10次网络异常则自动切换冗余设备，如果设置了的话。</item>
    ///             <item>修复网关客户端在配置原始字节请求的时候，点击数据预览，但是报错找不到对象的bug。</item>
    ///             <item>网关客户端修复原始字节请求里，配置了标量解析后，使用了表达式的数据变换无效的bug，原因来自表达式存储时丢失了。</item>
    ///             <item>网关的Modbus设备的原始字节请求，支持输入x=1;100这种线圈地址，并且可以正确的解析出某个bool点位，然后支持写入操作。</item>
    ///             <item>网关系统的设备节点基类DeviceNode新增原始字节请求的地址检查操作，并在三菱A1E协议里检查位地址的偏移是否16倍数，不是则提示。</item>
    ///             <item>网口和串口的自由协议里，如果地址里使用了<split>进行分割的话，就拆分的多个报文分别通信，以此解决特殊的设备需要两次交互的问题。</item>
    ///             <item>网关数据库Sqlserver连接配置里新增属性MultipleActiveResultSets，某些特殊情况下配置true可以解决数据库异常。</item>
    ///             <item>网关的OEE功能里，新增加班组的概念，在客户端配置界面配置了班组信息后，就会根据不同的时间断计算有效工作占比。</item>
    ///             <item>网关客户端在配置采集设备信息的界面时，新增一个小图标，可以切换当前的配置信息为XML文本，然后进行编辑，保存操作，方便有些情况下快速配置。</item>
    ///             <item>网关的请求支持添加分类节点，可以统一控制请求使能，请求间隔，还可以控制请求同步，数据同步操作，强制分类下所有请求一起操作。</item>
    ///             <item>网关服务器的设备标签对象，修复在脚本功能里，获取变量数据的时候，如果遇到数组的数据的时候会报错的bug，现在可以正确的解析数组信息。</item>
    ///             <item>网关的设备请求，新增属性，附加请求条件，可以根据实际情况来添加脚本表达式，让本次请求在满足一定条件才进行执行，支持上升沿，下降沿，持续触发之间选择。</item>
    ///             <item>网关服务器的接口WriteDataArray新增参数parallel表示是否使用并行写入，默认false，返回结果调整为bool[]，表示每个地址是否写入成功。</item>
    ///             <item>网关服务器在数据上传MQTT时，当配置基于变化的上传的时候，新增设置是否同时激活定时上传，并可以设置时间间隔。</item>
    ///             <item>其他的细节优化，增加DLT698的串口转网口，TCP的协议，支持配置属性是否安全模式，更新hslcommunication到v11.8.2版本。</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    ///     <revision date="2025-02-08" version="1.8.0" author="Richard.Hu">
    ///         <list type="bullet">
    ///             <item>优化客户端的网关列表显示的功能，修复可能在某些特殊的情况下切换网关导致异常的bug。</item>
    ///             <item>边缘网关系统支持上传网关数据到远程MQTT的功能，可以配置两个MQTT服务器同时传送，可以在客户端上进行配置操作。</item>
    ///             <item>边缘网关客户端修复在修改用户名或是密码的情况下检测重启网关服务器后提示用户名密码错误的bug。</item>
    ///             <item>边缘网关账户升级，支持自定义的账户信息，各种自定义的权限控制，webapi及websocket及mqtt发布模式权限也支持控制。</item>
    ///             <item>边缘网关的对外的数据接口http服务及websocket服务，也增加了网关账户的权限校验功能，需要使用登录命令登录账户名及密码。</item>
    ///             <item>边缘网关客户端修复当模板设备设置了别名后，新增一个模板设备后导致模板关联不正确的bug。</item>
    ///             <item>边缘网关服务器检测到当管道（串口，网口，单线程）配置为停用或是隐藏时，将对管道下的所有设备应用一样的操作。</item>
    ///             <item>网关客户端配置定时写入的请求里，使用eval=的脚本时，支持使用随机数，例如 eval=new Random().Next(1, 1000);</item>
    ///             <item>网关的信息里新增安装地址以及坐标信息，安装地址可以从客户端修改，客户端查看信息，坐标信息暂时预留。</item>
    ///             <item>边缘网关优化设备类型信息，改用字典注册设备类型及其设备类对象，客户端的添加设备的菜单自动根据设备字典动态创建。</item>
    ///             <item>边缘网关优化fanuc的机床部分的内容新增加一个方法接口ReadFeedRate2，用于读取进给倍率的备选方法。</item>
    ///             <item>网关数据库的请求类新增属性ExecuteWhenOffline，可以用来设置设备掉线时是否还进行存储数据库操作。</item>
    ///             <item>边缘网关修复定时写入的请求功能里，当配置了bool数组写入的时候，写入数据解析不正确的bug。</item>
    ///             <item>边缘网关系统添加请求方法时，如果存储结果到数据的话，支持了单位信息配置操作，可以配置字符串的信息。</item>
    ///             <item>边缘网关定时写入的请求里，当使用eval开头的脚本表达式的时候，支持使用其他设备的数据，例如 eval={设备1/温度}*10+外温</item>
    ///             <item>网关服务器暂停设备请求和继续设备请求的接口同时支持指定某个请求暂时或继续，新增一个接口DeviceRequestCount获取请求实际执行次数。</item>
    ///             <item>修复边缘网关系统同时配置上传2个MQTT服务器并且处于变化加定时上传的时候，第二个MQTT的定时上传失效的BUG。</item>
    ///             <item>边缘网关服务器的请求暂停继续接口支持了对路径操作，将会强制路径下所有设备的暂停和继续请求。</item>
    ///             <item>边缘网关客户端里，在监视网关数据的主界面上，支持路径及设备右键弹出菜单，然后暂停继续设备请求的操作。</item>
	///             <item>边缘网关服务器当配置了管道，管道下携带很多设备的情况下，从其他客户端调用API进行写入某个设备数据的时候，机制调整，现在可以很快的执行写入操作。</item>
	///             <item>边缘网关的串口管道，网口管道新增属性‘线程休眠时间’，大于0时表示切换设备采集的时候，线程强制休眠的时间，单位毫秒。</item>
	///             <item>边缘网关的设备基类节点新增属性RequestIntervalSleep，表示'连续请求间歇时间'，设置大于0的话，任何的请求执行之后都会休眠指定值时间。</item>
	///             <item>边缘网关服务器的写入数据的接口里，当设备处于离线的时候，每隔30秒将会生效一次写入操作，写入设备成功后，设备切换为在线状态。</item>
    ///             <item>网关插件功能里支持一种全自定义的插件开发，支持内置请求，报文记录，支持读写操作，支持原始字节配置，支持方法接口定义，支持设置内部数据。</item>
    ///             <item>网关插件功能里支持一种扩展插件，针对网关现有的设备协议进行扩展操作，内置请求，扩展方法接口等操作。</item>
    ///             <item>插件开发的部分的手册参考在线为开发文档：http://www.hsltechnology.cn/EdgeDoc/HslTechnology</item>
    ///             <item>本软件已经申请软件著作权，软著登记号：2021SR1315022，任何盗用软件，破解软件，未经正式合同授权而商业使用均视为侵权。</item>
    ///         </list>
    ///     </revision>
    /// </revisionHistory>
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute( )]
	public class NamespaceDoc
	{
	}



    // QQ: 2427521993   客户端运行出现bug
    // 在客户端操作暂时请求时，服务端重启，暂停状态就消失了
    // 3C_over_tcp协议，plc断电了，设备还一直在线
    // Modbus验证时从离线转正常时，mqtt报文"__deviceStatus"并不能从false更新为true  微信：wakenhh

    // TODO: mysql数据库数据持久化；
    // TODO: 北向（上行数据）通讯打包格式支持插件形式
    // TODO: 在使用MRPC接口写入字符串数据时，只能写入ASCII的，不能写入UTF8的
    // TODO: 网关测支持可配置TCP采集时，使用同步采集还是异步采集
    // TODO: 网关服务器启动后，MQTT客户端订阅多个主题时，有些主题就会触发异常：上海万澄环保科技有限公司开发
    // TODO: 网关客户端发送桌面快捷方式后，启动在桌面生成txt配置文件的问题：zhanggw1122
    // TODO: 所有的数据增加单位信息，来源主要是1. 用户自己配置的单位信息  2. 来自于系统自动根据一些关键字匹配的单位
    // TODO: 通过Modbus服务器构建的总站，采集了不同的客户端，汇总之后，目前不支持远程写入的操作，写入服务器后，转发写入实际的设备
    // TODO: V2.0 版本需要支持网关云，支持对所有的网关的在线情况，采集情况进行监视，对配置文件进行管理，并支持远程下载配置文件，进行一些命令的控制
    // TODO: V2.0 版本支持基于报文级别的完全自定义的设备，并支持接受注册API接口信息。
    // TODO: 新增网关的设备时，尽量避免重启网关。
    // TODO: 数据库支持MYSQL，目前使用MRPC反写，最好能支持通过MQTT反写，支持服务端隐藏界面启动配置参数,避免被误操作启动
    // TODO: 按照设备为单位，控制全部数据的订阅开启和关闭功能                                                                                                            
    // TODO: 网关配置了大量的设备后，Viewer客户端同时监视大量设备的时候，直接卡死了，计划客户端显示设备信息的界面重写，采用自定义的绘制实现
    // TODO: DLT645设备的数据写入，应该是HEX写入模式是有效的
    // TODO: 节点的分类属性里新增是否激活(默认激活)，是否开启关闭订阅(无效操作，关闭订阅，开启订阅)，按组或是设备。


    // TODO: 自定义的TCP通信支持配置消息格式，支持配置应答模式，还是异步通知模式，应答模式传入报文，接收报文，解析，异步通知模式，匹配报文，解析，支持3种格式  len=10;match=XX 01;send=01 02 03(只是发送)
    // TODO: 配置的解析分为三种，大端(西门子)，小端（C#）,字反转(modbus，支持DataFormate)，配置的时候决定
    // TODO: 网关自身的MQTTServer 没有网关状态的 __status 主题数据，需要发布下这个数据信息

}
