using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.MQTT;
using HslTechnology.Edge;
using HslTechnology.Edge.Config;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.EdgeServer.CloudPlatform
{
	public class MqttJetLinks
	{

		public static async Task Mqtt_OnMqttMessageReceived( EdgeServices edgeServices, MqttClient client, string topic, byte[] payload )
		{
			// 收到服务器发布的消息
			if (topic.EndsWith( "/properties/report" ) || topic.EndsWith( "/properties/write/reply" ) ||
				topic.EndsWith( "/properties/read/reply" ) || topic.EndsWith( "/function/invoke/reply" ) ||
				topic.EndsWith( "/offline" ) || topic.EndsWith( "/online" ))
			{
				// 这些是反馈回服务器的主题消息，不用管了
			}
			else if (topic.EndsWith( "/properties/write" ))
			{
				PropertiesWrite( edgeServices, client, topic, payload );
			}
			else if (topic.EndsWith( "/properties/read" ))
			{
				PropertiesRead( edgeServices, client, topic, payload );
			}
			else if (topic.EndsWith( "/function/invoke" ))
			{
				await FunctionInvoke( edgeServices, client, topic, payload );
			}
			else
			{
				edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), $" [{client.ConnectionOptions.IpAddress}]暂时不支持的Topic：" + topic + "   数据：" + Encoding.UTF8.GetString( payload ) );
			}
		}

		public static void PropertiesWrite( EdgeServices edgeServices, MqttClient client, string topic, byte[] payload )
		{
			string device = topic.Substring( 0, topic.Length - 17 );
			string messageId = string.Empty;
			JObject properties = null;
			OperateResult writeResult = OperateResult.CreateSuccessResult( );

			try
			{
				JObject json = JObject.Parse( Encoding.UTF8.GetString( payload ) );
				messageId    = json["messageId"].Value<string>( );
				properties   = json["properties"] as JObject;

				foreach (var item in properties.Properties( ))
				{
					string tagName = device + "/" + item.Name;
					string value = item.Value.ToString( );

					for (int i = 0; i < edgeServices.DevicesCollection.DeviceCount; i++)
					{
						if(edgeServices.DevicesCollection[i].GetMqttTopic() == device)
						{
							OperateResult write = edgeServices.DevicesCollection[i].WriteValueByName( new string[] { item.Name }, value );
							if (!write.IsSuccess)
							{
								edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), $"WriteData failed, [{tagName}] reason: {write.Message}" );
								if (writeResult.IsSuccess) writeResult = write;
							}
							else edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), $"WriteData Success, [{tagName}] value: {value}" );
							break;
						}
					}
				}
			}
			catch( Exception ex )
			{
				edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), $"JetLinks PropertiesWrite failed, [{SoftBasic.GetExceptionMessage( ex )}" );
				return;
			}

			// 返回服务器，写成功消息
			if (client != null)
			{
				JObject jsonBack = new JObject( );
				jsonBack.Add( "timestamp", edgeServices.EdgeResources.EdgeSettings.UploadInfo.GetTimeStamp( ) );
				jsonBack.Add( "messageId", messageId );
				jsonBack.Add( "properties", properties );
				if (writeResult.IsSuccess)
				{
					jsonBack.Add( "success", true );
				}
				else
				{
					jsonBack.Add( "success", false );
					jsonBack.Add( "code", writeResult.ErrorCode );
					jsonBack.Add( "message", writeResult.Message );
				}

				try
				{
					OperateResult publish = client.PublishMessage( new MqttApplicationMessage( )
					{
						Topic = topic + HslTechnologyExtension.DeviceDefaultSplit + "reply",
						Payload = Encoding.UTF8.GetBytes( jsonBack.ToString( ) ),
						QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
						Retain = edgeServices.EdgeResources.EdgeSettings.UploadInfo.MqttRetain
					} );
					if (!publish.IsSuccess) throw new Exception( publish.Message );
				}
				catch (Exception ex)
				{
					client?.ConnectClose( );
					edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), " MQTT上传失败：" + ex.Message );
				}
			}
		}

		public static void PropertiesRead( EdgeServices edgeServices, MqttClient client, string topic, byte[] payload )
		{
			string device = topic.Substring( 0, topic.Length - 16 );
			OperateResult status = OperateResult.CreateSuccessResult( );
			string messageId = string.Empty;
			string[] properties = null;
			string deviceId = string.Empty;
			JObject data = new JObject( );

			try
			{
				JObject json = JObject.Parse( Encoding.UTF8.GetString( payload ) );
				messageId    = json["messageId"]. Value<string>( );
				properties   = json["properties"].Values<string>( ).ToArray( );
				deviceId     = json["deviceId"].  Value<string>( );

				for (int i = 0; i < properties.Length; i++)
				{
					string tagName = device + "/" + properties[i];
					for (int j = 0; j < edgeServices.DevicesCollection.DeviceCount; j++)
					{
						if (edgeServices.DevicesCollection[j].GetMqttTopic( ) == device)
						{
							OperateResult<JToken> read = OperateResult.CreateSuccessResult( edgeServices.DevicesCollection[j].GetValueByTagName( properties[i] ) );
							if (read.IsSuccess)
							{
								edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), $"ReadData success, [{tagName}] data: {read.Content}" );
								data.Add( properties[i], read.Content );
							}
							else
							{
								edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), $"ReadData failed, [{tagName}] reason: {read.Message}" );
								if (status.IsSuccess) status = read;
							}
							break;
						}
					}
				}
			}
			catch(Exception ex)
			{
				edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), $"JetLinks PropertiesRead failed, [{SoftBasic.GetExceptionMessage( ex )}" );
				return;
			}
			// 返回服务器，写成功消息
			if (client != null)
			{
				JObject jsonBack = new JObject( );
				jsonBack.Add( "timestamp", edgeServices.EdgeResources.EdgeSettings.UploadInfo.GetTimeStamp( ) );
				jsonBack.Add( "messageId", messageId );
				if (status.IsSuccess)
				{
					jsonBack.Add( "properties", data );
					jsonBack.Add( "success", true );
				}
				else
				{
					jsonBack.Add( "success", false );
					jsonBack.Add( "code", status.ErrorCode );
					jsonBack.Add( "message", status.Message );
				}

				try
				{
					OperateResult publish = client.PublishMessage( new MqttApplicationMessage( )
					{
						Topic = topic + HslTechnologyExtension.DeviceDefaultSplit + "reply",
						Payload = Encoding.UTF8.GetBytes( jsonBack.ToString( ) ),
						QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
						Retain = edgeServices.EdgeResources.EdgeSettings.UploadInfo.MqttRetain
					} );
					if (!publish.IsSuccess) throw new Exception( publish.Message );
				}
				catch (Exception ex)
				{
					client?.ConnectClose( );
					edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), " MQTT上传失败：" + ex.Message );
				}
			}
		}

		public static async Task FunctionInvoke( EdgeServices edgeServices, MqttClient client, string topic, byte[] payload )
		{
			string device = topic.Substring( 0, topic.Length - 16 );
			JObject data = new JObject( );
			string messageId = string.Empty;
			string functionName = string.Empty;
			string deviceId = string.Empty;
			JObject para = new JObject( );

			try
			{
				JObject json = JObject.Parse( Encoding.UTF8.GetString( payload ) );
				messageId = json["messageId"].Value<string>( );
				functionName = json["functionId"].Value<string>( );
				deviceId = json["deviceId"].Value<string>( );

				IEnumerable<JToken> inputs = json["inputs"].Values<JToken>( );
				foreach (var item in inputs)
				{
					para.Add( item["name"].Value<string>( ), item["value"] );
				}
			}
			catch(Exception ex)
			{
				edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), $"JetLinks FunctionInvoke failed, [{SoftBasic.GetExceptionMessage( ex )}" );
				return;
			}

			for (int j = 0; j < edgeServices.DevicesCollection.DeviceCount; j++)
			{
				if (edgeServices.DevicesCollection[j].GetMqttTopic( ) == device)
				{
					var result = await edgeServices.DevicesCollection[j].CallDeviceMethod( true, functionName, para.ToString( ) );

					// 返回服务器，写成功消息
					if (client != null)
					{
						JObject jsonBack = new JObject( );
						jsonBack.Add( "timestamp", edgeServices.EdgeResources.EdgeSettings.UploadInfo.GetTimeStamp( ) );
						jsonBack.Add( "messageId", messageId );

						if (result is OperateResult opResult)
						{
							if (opResult.IsSuccess)
							{
								PropertyInfo property = opResult.GetType( ).GetProperty( "Content" );
								jsonBack.Add( "success", true );
								if (property != null)
								{
									jsonBack.Add( "output", JObject.FromObject( opResult )["Content"] );
								}
								//this.logNet?.WriteError( this.ToString( ) + " MQTT方法调用结果：" + JObject.FromObject( opResult )["Content"].ToString() );
							}
							else
							{
								jsonBack.Add( "success", false );
								jsonBack.Add( "code", opResult.ErrorCode );
								jsonBack.Add( "message", opResult.Message );
							}
						}
						else
						{
							jsonBack.Add( "success", true );
							jsonBack.Add( "output", new JValue( result ) );
						}

						try
						{
							OperateResult publish = client.PublishMessage( new MqttApplicationMessage( )
							{
								Topic = topic + HslTechnologyExtension.DeviceDefaultSplit + "reply",
								Payload = Encoding.UTF8.GetBytes( jsonBack.ToString( ) ),
								QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
								Retain = edgeServices.EdgeResources.EdgeSettings.UploadInfo.MqttRetain
							} );
							if (!publish.IsSuccess) throw new Exception( publish.Message );
						}
						catch (Exception ex)
						{
							client?.ConnectClose( );
							edgeServices.EdgeLogNet?.WriteWarn( string.Empty, edgeServices.ToString( ), " MQTT上传失败：" + ex.Message );
						}
					}
					break;
				}
			}

		}
	}
}
