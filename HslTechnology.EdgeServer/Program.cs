using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.Core.Net;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslTechnology.Edge;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.DataBusiness.Time;
using HslTechnology.Edge.Device.Base;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace HslTechnology.EdgeServer
{
	class Program
	{
		/// <summary>
		/// 网关的运行时间分析
		/// </summary>
		private static EdgeRunTimeAnalysis RunTimeAnalysis = null;

		/// <summary>
		/// 主函数
		/// </summary>
		/// <param name="args">可能存在的启动参数信息</param>
		/// <returns>异步的空返回信息</returns>
		static async Task Main( string[] args )
		{
			#region 一些系统初始化执行的操作代码

			Program.RunTimeAnalysis = new EdgeRunTimeAnalysis( Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "SysLogs.txt" ) );   // 运行时的时间记录对象
			AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;                                              // 未捕获的异常发生的时候
			AppDomain.CurrentDomain.ProcessExit        += CurrentDomain_ProcessExit;                                                     // 程序退出的时候
			ILogNet lognet = new LogNetSingle( "" );                                                                                     // 实例化主程序日志
			lognet.ConsoleOutput = true;
			lognet.SetMessageDegree( HslMessageDegree.INFO );
			HslCommunication.Core.HslHelper.UseAsyncLock = false;                                                                        // HslCommunication组件优化参数设置
			ThreadPool.SetMaxThreads( 2000, 500 );                                                                                       // 设置当前线程池的容量

			#endregion

            // 该字体获取方式 Visual Studio Code -> ASCIIDecorator -> Georgia11 Font
            Console.WriteLine(@" ");
            Console.WriteLine(@"TTTTTTTTTTTTTTTTTTTTTTTZZZZZZZZZZZZZZZZZZZXXXXXXX       XXXXXXXXXXXXXX       XXXXXXX");
            Console.WriteLine(@"T:::::::::::::::::::::TZ:::::::::::::::::ZX:::::X       X:::::XX:::::X       X:::::X");
            Console.WriteLine(@"T:::::::::::::::::::::TZ:::::::::::::::::ZX:::::X       X:::::XX:::::X       X:::::X");
            Console.WriteLine(@"T:::::TT:::::::TT:::::TZ:::ZZZZZZZZ:::::Z X::::::X     X::::::XX::::::X     X::::::X");
            Console.WriteLine(@"T:::::TT:::::::TT:::::TZ:::ZZZZZZZZ:::::Z X::::::X     X::::::XX::::::X     X::::::X");
            Console.WriteLine(@"TTTTTT  T:::::T  TTTTTTZZZZZ     Z:::::Z  XXX:::::X   X:::::XXXXXX:::::X   X:::::XXX");
            Console.WriteLine(@"        T:::::T                Z:::::Z       X:::::X X:::::X      X:::::X X:::::X   ");
            Console.WriteLine(@"        T:::::T               Z:::::Z         X:::::X:::::X        X:::::X:::::X    ");
            Console.WriteLine(@"        T:::::T              Z:::::Z           X:::::::::X          X:::::::::X     ");
            Console.WriteLine(@"        T:::::T             Z:::::Z            X:::::::::X          X:::::::::X     ");
            Console.WriteLine(@"        T:::::T            Z:::::Z            X:::::X:::::X        X:::::X:::::X    ");
            Console.WriteLine(@"        T:::::T           Z:::::Z            X:::::X X:::::X      X:::::X X:::::X   ");
            Console.WriteLine(@"        T:::::T        ZZZ:::::Z     ZZZZZXXX:::::X   X:::::XXXXXX:::::X   X:::::XXX");
            Console.WriteLine(@"      TT:::::::TT      Z::::::ZZZZZZZZ:::ZX::::::X     X::::::XX::::::X     X::::::X");
            Console.WriteLine(@"      T:::::::::T      Z:::::::::::::::::ZX:::::X       X:::::XX:::::X       X:::::X");
            Console.WriteLine(@"      T:::::::::T      Z:::::::::::::::::ZX:::::X       X:::::XX:::::X       X:::::X");
            Console.WriteLine(@"      TTTTTTTTTTT      ZZZZZZZZZZZZZZZZZZZXXXXXXX       XXXXXXXXXXXXXX       XXXXXXX");
            Console.WriteLine(@"     " + UserInfo.Company);

			#region Api 运行确认信息

			#endregion

			// 加载配置信息
			lognet.WriteInfo( $"正在加载配置文件。" );
			HslTechnologyHelper.Sleep( 100 );
			EdgeDeviceResources edgeResources = new EdgeDeviceResources( );                                                         // 实例化网关的核心资源信息
			edgeResources.SetEdgeSettings( new SettingsServer( args, lognet ) );
			edgeResources.EdgeSettings.LoadSettings( );
			lognet = edgeResources.EdgeSettings.LogInfo.GetLogNet( );
			lognet.BeforeSaveToFile += Lognet_BeforeSaveToFile;
			edgeResources.SetLogNet( lognet );

			// 检测是否是主服务器，是的话，直接启动，并监视备用服务器，如果是辅助服务器，则根据需要从主服务器下载配置文件，并实时监视主服务器的状态
			// 此处设计的机制是，一旦备用服务器切换为运行状态，就和主服务器是一样了
			if (edgeResources.EdgeSettings.StandbyInfo.StandbyEnable) await edgeResources.EdgeSettings.StandbyInfo.RunAsStandby( edgeResources.EdgeSettings, lognet );

			// 启动当前网关的核心服务
			EdgeServices edgeServies = new EdgeServices( edgeResources );
			edgeServies.RunTimeAnalysis = Program.RunTimeAnalysis;

			if ( edgeServies.ServerStart( ) == false)
			{
				// 核心服务启动失败，这时候，服务不应该启动
				lognet?.WriteError( $"当前主服务器端口已经被使用了，无法启动网关的服务，请尝试更换端口或是关闭之前的服务器，然后稍候重试！" );
				Console.ReadLine( );
				return;
			}

			lognet?.WriteInfo( "Server Start!" );
			HslTechnologyHelper.Sleep( 100 );
			lognet.BeforeSaveToFile -= Lognet_BeforeSaveToFile;
			try
			{
				File.WriteAllText( Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "initialize.txt" ), StringBuilderIni.ToString( ), Encoding.UTF8 );
			}
			finally
			{
				StringBuilderIni.Clear( );
				StringBuilderIni = null;
			}

            int myCount = 0;
            int timeCount = 0;
			DateTime dateTime = DateTime.Now;

			// 执行主线程的循环任务，主要是执行一些定时的任务，
			while (edgeServies.IsRunning)
			{
				HslTechnologyHelper.Sleep( 20 );
				DateTime newTime = HslTechnologyHelper.GetDateTimeNow( );

				if (dateTime.Hour != newTime.Hour)
				{
					// 每个小时触发
					if (newTime.Hour == 0) // 每天的0点触发
					{
						
					}
				}
				if (dateTime.Minute != newTime.Minute)
				{
					// 每分钟触发一次
					if (newTime.Minute % 10 == 0)
					{
						// 每十分钟计算一下是否需要重置OEE
						BusinessEngine.Business?.ResetOee( );
					}
				}

				// 每秒执行的一些操作信息
				if (dateTime.Second != newTime.Second)
				{
					edgeServies.UpdateServerStatus( );
					edgeServies.GuardTimerCheck( );
					Program.RunTimeAnalysis.UpdateCache( );          // 更新当前的运行状态时间信息

                    if (!edgeServies.TZXXCheckOK)
                    {
                        myCount++;
                        if (myCount > edgeServies.RunCount)
                        {
                            Environment.Exit(Environment.ExitCode);
                        }
                    }
                }
				// 每分钟执行的
				if (dateTime.Minute != newTime.Minute)
				{
					Program.RunTimeAnalysis.UpdateFile( );
				}

				timeCount++;
				if (timeCount > 500)
				{
					timeCount = 0;
					if (!edgeServies.TZXXCheckOK)
					{
                        lognet.WriteWarn(UserInfo.WelcomeTrial);
                    }
				}

				dateTime = newTime;                  // 刷新时间
			}

			lognet?.WriteInfo( "Server Close!" );
			if (edgeServies.IsNewVersion) lognet?.WriteInfo( "New Version, prepare to upgrade!" );
			if (edgeServies.ReStarted)
			{
				RestartServer( edgeResources.EdgeSettings, edgeServies.IsNewVersion );
			}
			Environment.Exit( Environment.ExitCode );
			// BeginRestarted( devicesServer.ReStarted, devicesServer.IsNewVersion, devicesServer.Port );
		}

		private static void Lognet_BeforeSaveToFile( object sender, HslEventArgs e )
		{
			StringBuilderIni.AppendLine( e.HslMessage.ToString( ) );
		}

		/// <summary>
		/// 初始化系统时候的字符串日志信息
		/// </summary>
		public static StringBuilder StringBuilderIni = new StringBuilder( );

		/// <summary>
		/// 获取或设置退出消息
		/// </summary>
		public static string QuitMessage { get; set; }

		private static void CurrentDomain_ProcessExit( object sender, EventArgs e )
		{
			// 网关的关闭记录，进行记录
			RunTimeAnalysis.Quit( string.IsNullOrEmpty( QuitMessage ) ? "手动关闭了网关服务器" : QuitMessage );
		}

		public static void CurrentDomain_UnhandledException( object sender, UnhandledExceptionEventArgs e )
		{
			if (e.ExceptionObject is Exception ex)
			{
				QuitMessage = "边缘网关服务器发生异常，请查看服务器存留的详细信息。";
				for (int i = 0; i < 3; i++)  // 如果失败，尝试3次的数据写入
				{
					try
					{
						File.WriteAllText( Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "UnhandledException.txt" ),
							SoftBasic.GetExceptionMessage( ex ), Encoding.UTF8 );
						break;
					}
					catch
					{
					}
				}
			}
		}

		public static void RestartServer( SettingsServer settingsServer, bool upgrade )
		{
#if NETCOREAPP3_1_OR_GREATER
			if (RuntimeInformation.IsOSPlatform( OSPlatform.Windows ))
			{
#endif
				// 传统windows
				string fileName = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "restart.bat" );
				StringBuilder sb = new StringBuilder( );
				if(upgrade)
				{
					try
					{
						string[] files = System.IO.Directory.GetFiles( Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "Upgrade" ) );
						foreach( string file in files)
						{
							FileInfo fileInfo = new FileInfo( file );
							string newName = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, fileInfo.Name );
							sb.Append( "if exist \"" );
							sb.Append( newName );
							sb.Append( "\" (" );
							sb.Append( Environment.NewLine );
							sb.Append( "del \"" );
							sb.Append( newName );
							sb.Append( "\"" );
							sb.Append( Environment.NewLine );
							sb.Append( ")" );
							sb.Append( Environment.NewLine );
							sb.Append( "move \"" );
							sb.Append( file );
							sb.Append( "\" \"" );
							sb.Append( newName );
							sb.Append( "\"" );
							sb.Append( Environment.NewLine );
						}
					}
					catch
					{
						throw;
					}
				}
				sb.Append( $"start /d \"{AppDomain.CurrentDomain.BaseDirectory}\" HslTechnology.EdgeServer.exe" );
				File.WriteAllText( fileName, sb.ToString( ), Encoding.Default );
				System.Diagnostics.Process.Start( fileName );
#if NETCOREAPP3_1_OR_GREATER
			}
			else if (RuntimeInformation.IsOSPlatform( OSPlatform.Linux ))
			{
				string fileName = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "HslTechnology.EdgeServer" );
				// sudo /home/<USER>/Documents/linux-64/HslTechnoloty.EdgeServer
				StringBuilder sb = new StringBuilder( );
				if (upgrade)
				{
					sb.Append( $"sudo mv -f {Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "Upgrade", "*" )} {AppDomain.CurrentDomain.BaseDirectory};" );
				}
				sb.Append( "sudo chmod 777 " + fileName + ";sudo " + fileName );
				ExecLinuxCmd( sb.ToString( ) );
				// 这个针对的是树莓派的情况
				//string cmd = "#!/bin/bash" + Environment.NewLine + "chmod 777 " + fileName + Environment.NewLine + fileName;
			}
			else if (RuntimeInformation.IsOSPlatform( OSPlatform.FreeBSD ))
			{

			}
#endif
		}

		public static void ExecLinuxCmd( string cmd )
		{
			cmd = "#!/bin/bash" + Environment.NewLine + cmd;
			string args = cmd.Replace( "\"", "\\\"" );
			System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo( )
			{
				FileName = "/bin/bash",
				Arguments = $"-c \"{args}\"",
				RedirectStandardOutput = true,
				UseShellExecute = false,
				CreateNoWindow = false,
			};
			System.Diagnostics.Process.Start( startInfo );
		}

		/****
		 *  还有一种启动的方式，新建一个 restart.sh 文件，输入
		 *  !/bin/sh
		 *  chmod 777 [HslTechnology.EdgeServer完整路径]
		 *  sudo [HslTechnology.EdgeServer完整路径]
		 *  
		 *  
		 *  然后上述的ExecLinuxCmd修改为
		 *  System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo( )
			{
				FileName = "[restart.sh的完整路径]",
				RedirectStandardOutput = true,
				UseShellExecute = false,
				CreateNoWindow = false,
			};
		 */
	}
}
