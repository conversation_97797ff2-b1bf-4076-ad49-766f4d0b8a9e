using HslCommunication.Core.Net;
using HslCommunication.Enthernet;
using HslCommunication.LogNet;
using HslCommunication.ModBus;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml.Linq;
using System.Threading;
using HslCommunication.Enthernet.Redis;
using HslCommunication;
using Newtonsoft.Json.Linq;
using HslCommunication.BasicFramework;
using System.Net;
using System.Linq;
using HslCommunication.MQTT;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Runtime.InteropServices;
using HslTechnology.Edge.Device;
using HslCommunication.Reflection;
using HslCommunication.Core;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.Resources;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Plugins;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.DataBusiness.Time;
using HslTechnology.Edge.DataBusiness.Database;
using HslTechnology.Edge.DataBusiness.Log;
using HslCommunication.WebSocket;
using System.Text.RegularExpressions;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.EdgeServer.CloudPlatform;
using System.Security.Cryptography;

namespace HslTechnology.EdgeServer
{
	/// <summary>
	/// 所有的设备类集合的服务器，负责解析所有的XML设备信息，并生成相应的设备对象，然后进行启动设备
	/// </summary>
	public class EdgeServices : IEdgeServices
	{
		#region Constructor

		/// <summary>
		/// 实例化一个对象，需要传入配置文件的路径，根据配置文件的信息即可创建一个节点服务器
		/// </summary>
		/// <param name="logNet">日志对象信息</param>
		/// <param name="settingsServer">服务器的配置参数信息</param>
		public EdgeServices( EdgeDeviceResources edgeResources )
		{
			BusinessEngine.Business   = new BusinessEngine( this.logNet );
			BusinessEngine.Business.OnAlarmStartEvent  += Business_OnAlarmStartEvent;
			BusinessEngine.Business.OnAlarmFinishEvent += Business_OnAlarmFinishEvent;
			this.random                       = new Random( );
			this.logNet                       = edgeResources.LogNet;
			this.edgeResources                = edgeResources;
			this.serverData                   = new DeviceData( );
			this.ramUseHistory                = new SharpList<float>( 1024 );
			this.threadUseHistory             = new SharpList<float>( 1024 );
			this.process                      = Process.GetCurrentProcess( );
			// 初始化日志
			this.edgeLog                      = new EdgeLog( );
			this.edgeLog.EdgeLogger           += EdgeLog_EdgeLogger;
			this.edgeLog.DeviceTelegramLogger += EdgeLog_DeviceTelegramLogger;

			// 创建插件的目录，并加载插件的资源
			if (!Directory.Exists( PluginsHelper.PluginsDirectory( ) )) Directory.CreateDirectory( PluginsHelper.PluginsDirectory( ) );
			PluginsHelper.Plugins = PluginsHelper.LoadPlugins( logNet, PluginsHelper.PluginsDirectory( ) );

			this.logNet?.WriteInfo( $"运行的目录位置：{AppDomain.CurrentDomain.BaseDirectory}" );
			this.logNet?.WriteInfo( $"运行的系统信息：{Environment.OSVersion}" );
			this.logNet?.WriteInfo( $"运行的CLR信息：{Environment.Version}" );
			this.logNet?.WriteInfo( $"运行的计算机信息：{Environment.MachineName}" );
			this.logNet?.WriteInfo( $"运行的程序版本号：" + this.edgeResources.EdgeSettings.Version.ToString( ) );
			if (this.edgeResources.EdgeSettings.UploadInfo.UseRedisServer)
				logNet?.WriteInfo( $"已配置redis上传服务器，Ip:{this.edgeResources.EdgeSettings.UploadInfo.RedisIpAddress} Port:{this.edgeResources.EdgeSettings.UploadInfo.RedisPort}" );
			if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer)
				logNet?.WriteInfo( $"已配置mqtt上传服务器，Ip:{this.edgeResources.EdgeSettings.UploadInfo.MqttIpAddress} Port:{this.edgeResources.EdgeSettings.UploadInfo.MqttPort}" );

			this.port = this.edgeResources.EdgeSettings.ServerInfo.ServerPort;
			edgeResources.HttpServer.LogNet = logNet;
			edgeResources.SetIEdgeServices( this );


			this.devicesCollection = new DevicesCollection( this.edgeResources );
			this.devicesCollection.OnDeviceValueChanged += DevicesCollection_OnDeviceValueChanged;
			this.devicesCollection.OnDeviceStatusChanged += DevicesCollection_OnDeviceStatusChanged;
			this.serverData.SetSingleValue( "__name",            this.edgeResources.EdgeSettings.ServerInfo.DeviceName );
			this.serverData.SetSingleValue( "__version",         this.edgeResources.EdgeSettings.Version.ToString( ) );
			this.serverData.SetSingleValue( "__startTime",       HslTechnologyHelper.GetDateTimeNow( ).ToEdgeString( ) );
			this.serverData.SetSingleValue( "__standbyStatus",   "" );
			this.serverData.UpdateJsonTmp( );
			this.logNet?.WriteInfo( "加载配置文件完成，共计设备：" + this.devicesCollection.DeviceCount.ToString( ) + "台" );

			// 专门用于7天测试的证书
			OperateResult active = HslCommunication.Authorization.SetHslCertificate( Convert.FromBase64String(
				"tAEAAKIAMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCoKsX100Id/uNzOuR9GhBiaXBfMqYBzyr9tGF55ZdP6Wsm51yizuiiEKK8tV5nTDo6mtx3O7gyNLECDHsO2O" +
				"MxorF4cNNMCME5Mc7pzsryDNOtGJrULpTi6WWcXGK33W3Tx2gsvLTIP+eFKSfk3/hkBOjQiVlBN9tV/pgVL2zG9QIDAQABOADmna3lt57og6Hlt6XnianogZTnp5HmioDm" +
				"nInpmZDlhazlj7gNCjkxMzMwMTEwTUEySjBOSFcwMzgA5p2t5bee6IOh5bel54mp6IGU56eR5oqA5pyJ6ZmQ5YWs5Y+4DQo5MTMzMDExME1BMkowTkhXMDMIAADAysfNgdw" +
				"ICAAAQEwfPaXhCAgAAEADtBi/3AgQAEhzbENvbW11bmljYXRpb24AAKgAAAACAAEAQScA5LuF55So5LqO572R5YWz55qE5rWL6K+V6aKB5Y+R55qE6K+B5LmmAQBCMwDku4Xn" +
				"lKjkuo7mna3lt57og6Hlt6XnianogZTnp5HmioDmnInpmZDlhazlj7jkvb/nlKiAAI+GVt6JGHesnKXkq9fuOxXFcn2J6A/fFWMxUhdD/+0xnZDVlW7XM0GNCXXTalwskZ/OD/h" +
				"t0FF0VQtO10LgUyupxQE2XlxfVB1mh7vR1SCDZnhn48yeBPg+2V0mkND2EG4nHnPd7Lq6gXUuI+BCDY4Gagg878ZvP4PYzI2nsvrc" ) );
			if (!active.IsSuccess) 
			{
				logNet?.WriteError( "HslCommunication Authorization Failed: " + active.Message ); 
			}
			if (!HslTechnology.Edge.Authorization.SetAuthorizationCode( "" )) { logNet?.WriteError( "HslTechnology.Edge Authorization Failed" ); }

            TZXXCheckOK = CheckRegistration();
            if (TZXXCheckOK)
            {
                this.logNet?.WriteInfo($"Application Authentication Succes!");
            }
            else
            {
                RunCount = 8 * 3600;
                this.logNet?.WriteInfo($"Application Authentication Failt!");
            }

            this.UpdateServerStatus( );
			//this.mqttClient = new MqttClient( new MqttConnectionOptions( )
			//{
			//	IpAddress = "*************",
			//	Port = 2345,
			//	ClientId = Environment.MachineName,
			//	ConnectTimeout = 3000,
			//	Credentials = new MqttCredential( "hsl", "aoiwhdasdnhaiwd213HI" )
			//} );
			//if (this.mqttClient.ConnectServer( ).IsSuccess)
			//{
			//	this.mqttClient.PublishMessage( new MqttApplicationMessage( )
			//	{
			//		Topic = this.edgeResources.EdgeSettings.ServerInfo.DeviceName,
			//		Payload = Encoding.UTF8.GetBytes( this.serverData.DeviceJsonTemp )
			//	} );
			//}

		}

        private Boolean CheckRegistration()
        {
            string file = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TZXX.XML");
            if (File.Exists(file))
            {
                StreamReader sw1 = new StreamReader(file);
                string s = sw1.ReadToEnd();
                sw1.Close();
                MD5 md5 = MD5.Create();
                byte[] t = md5.ComputeHash(Encoding.Unicode.GetBytes(SettingsServer.CPUID()));
                md5.Clear();
                StringBuilder sb = new StringBuilder();
                for (int a = 0; a < t.Length; a++)
                {
                    sb.Append(a.ToString("X2"));
                }
                var str = sb.ToString();
                if (str == s.Trim())
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;

		}


		private async Task<bool> Business_AlarmSaveDatabase( string deviceName, AlarmItem alarmItem )
		{
			bool saveSuccess = false;
			List<AlarmDatabaseNode> alarmDatabases = BusinessEngine.Business.AlarmResource.GetAlarmDatabases( );
			if (alarmDatabases?.Count > 0)
			{
				foreach (var alarmDatabase in alarmDatabases)
				{
					bool exeSave = false;
					if (alarmItem.Status == AlarmStatus.Alarm && alarmDatabase.ExecuteMoment == 1) exeSave = true;
					if (alarmItem.Status == AlarmStatus.Finish && alarmDatabase.ExecuteMoment == 2) exeSave = true;

					if (!exeSave) continue;

					MatchEvaluator matchEvaluator = match =>
					{
						string name = match.Value.Substring( 1, match.Value.Length - 2 );
						if (name.Equals( "UniqueId",     StringComparison.OrdinalIgnoreCase )) return alarmItem.UniqueId.ToString( );
						if (name.Equals( "AlarmCode",    StringComparison.OrdinalIgnoreCase )) return alarmItem.AlarmCode.ToString( );
						if (name.Equals( "AlarmContent", StringComparison.OrdinalIgnoreCase )) return $"'{alarmItem.AlarmContent}'";
						if (name.Equals( "StartTime",    StringComparison.OrdinalIgnoreCase )) return $"'{alarmItem.StartTime:yyyy-MM-dd HH:mm:ss}'";
						if (name.Equals( "FinishTime",   StringComparison.OrdinalIgnoreCase )) return $"'{alarmItem.FinishTime:yyyy-MM-dd HH:mm:ss}'";
						if (alarmDatabase.DegreeFormate == 1)
						{
							if (name.Equals( "Degree",   StringComparison.OrdinalIgnoreCase )) return $"{(int)alarmItem.Degree}";
						}
						else
						{
							if (name.Equals( "Degree",   StringComparison.OrdinalIgnoreCase )) return $"{alarmItem.Degree.ToString( )}";
						}

						if (name.Equals( "Status",       StringComparison.OrdinalIgnoreCase )) return $"'{alarmItem.Status.ToString( )}'";
						if (name.Equals( "DeviceName",   StringComparison.OrdinalIgnoreCase )) return $"'{alarmItem.DeviceName}'";
						if (name.Equals( "TagName",      StringComparison.OrdinalIgnoreCase )) return $"'{alarmItem.TagName}'";
						return "NULL";
					};
					string sql = Regex.Replace( alarmDatabase.SqlCommand, @"\{[^\}]+\}", matchEvaluator );
					DatabaseCore databaseCore = BusinessEngine.Business.DatabaseResource.GetDatabaseCore( alarmDatabase.Database );
					if (databaseCore != null)
					{
						OperateResult exe = await databaseCore.ExecuteCommand( sql );
						if (!exe.IsSuccess)
						{
							// 数据库存储失败
							this.edgeLog?.WriteError( deviceName, ToString( ), $"存储数据库[{alarmDatabase.Database}] 时发生失败：{exe.Message}" );
						}
						else
						{
							// 数据库存储成功
							saveSuccess = true;
						}
					}
					else
					{
						this.edgeLog?.WriteError( deviceName, ToString( ), $"存储数据库[{alarmDatabase.Database}] 时发生失败：未找到这个数据库对象" );
					}
				}
			}
			return saveSuccess;
		}

		private async void Business_OnAlarmStartEvent( string deviceName, AlarmItem alarm )
		{
			bool save = await Business_AlarmSaveDatabase( deviceName, alarm );
			this.edgeLog?.WriteError( deviceName, string.Empty, $"[{alarm.DeviceName}] [{alarm.TagName}] [{alarm.Degree}] 发生了报警{(save ? "[已存储数据库]" : "")}：" + alarm.AlarmContent );
			this.edgeResources.MqttServer?.PublishTopicPayload( "__alarm", Encoding.UTF8.GetBytes( alarm.ToJsonString( ) ), false );
			this.edgeResources.WebSocketServer?.PublishClientPayload( "__alarm", alarm.ToJsonString( ), false );

			if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer && this.mqttConnectionOptions != null && this.hslMqttClient != null && this.edgeResources.EdgeSettings.UploadInfo.UploadAlarm)
			{
				this.hslMqttClient.PublishMessage( new MqttApplicationMessage( )
				{
					Topic = "__alarm",
					Payload = Encoding.UTF8.GetBytes( alarm.ToJsonString( ) ),
				} );
			}


			if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer2 && this.mqttConnectionOptions2 != null && this.hslMqttClient2 != null && this.edgeResources.EdgeSettings.UploadInfo.UploadAlarm)
			{
				this.hslMqttClient2.PublishMessage( new MqttApplicationMessage( )
				{
					Topic = "__alarm",
					Payload = Encoding.UTF8.GetBytes( alarm.ToJsonString( ) ),
				} );
			}
		}

		private async void Business_OnAlarmFinishEvent( string deviceName, AlarmItem alarm )
		{
			bool save = await Business_AlarmSaveDatabase( deviceName, alarm );
			this.edgeLog?.WriteWarn( deviceName, string.Empty, $"[{alarm.DeviceName}] [{alarm.TagName}] [{alarm.Degree}] 结束了报警{(save ? "[已存储数据库]" : "")}：{alarm.AlarmContent} 持续:[{SoftBasic.GetTimeSpanDescription( alarm.FinishTime - alarm.StartTime )}]" );
			this.edgeResources.MqttServer?.PublishTopicPayload( "__alarm", Encoding.UTF8.GetBytes( alarm.ToJsonString( ) ), false );
			this.edgeResources.WebSocketServer?.PublishClientPayload( "__alarm", alarm.ToJsonString( ), false );

			if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer && this.mqttConnectionOptions != null && this.hslMqttClient != null && this.edgeResources.EdgeSettings.UploadInfo.UploadAlarm)
			{
				this.hslMqttClient.PublishMessage( new MqttApplicationMessage( )
				{
					Topic = "__alarm",
					Payload = Encoding.UTF8.GetBytes( alarm.ToJsonString( ) ),
				} );
			}

			if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer2 && this.mqttConnectionOptions2 != null && this.hslMqttClient2 != null && this.edgeResources.EdgeSettings.UploadInfo.UploadAlarm)
			{
				this.hslMqttClient2.PublishMessage( new MqttApplicationMessage( )
				{
					Topic = "__alarm",
					Payload = Encoding.UTF8.GetBytes( alarm.ToJsonString( ) ),
				} );
			}
		}

		#endregion

		#region Server Start Close

		private bool CheckServerPortOpen( )
		{
			NetworkDoubleBase network = new NetworkDoubleBase( );
			network.IpAddress         = "127.0.0.1";
			network.Port              = this.port;
			network.ConnectTimeOut    = 1000;

			OperateResult connect     = network.ConnectServer( );
			if (connect.IsSuccess)
			{
				network.ConnectClose( );
				return true;
			}
			return false;
		}

		/// <summary>
		/// 启动服务器的后台存储
		/// </summary>
		public bool ServerStart( )
		{
			// 更新状态
			this.serverData.SetSingleValue( "__deviceCount",     this.devicesCollection.DeviceCount );
			this.serverData.SetSingleValue( "__deviceStopCount", this.devicesCollection.DeviceStopCount );
			this.serverData.SetSingleValue( "__timeout",         HslTimeOut.TimeOutCheckCount );
			this.serverData.SetSingleValue( "__build",           RuntimeInformation.ProcessArchitecture.ToString( ) );
			this.serverData.SetSingleValue( "__osInfo",          RuntimeInformation.OSDescription ); // Environment.OSVersion.VersionString
			this.serverData.SetSingleValue( "__clr",             RuntimeInformation.FrameworkDescription );  // Environment.Version.ToString( )
			this.serverData.SetSingleValue( "__company",         UserInfo.Company );
			this.serverData.SetSingleValue( "__computerInfo",    Environment.MachineName );
			this.serverData.SetSingleValue( "__uniqueId",        this.edgeResources.EdgeSettings.ServerInfo.UniqueId );
			if (!string.IsNullOrEmpty( this.edgeResources.EdgeSettings.ServerInfo.Location))
				this.serverData.SetSingleValue( "__location", this.edgeResources.EdgeSettings.ServerInfo.Location );
			this.serverData.UpdateJsonTmp( );

			if (CheckServerPortOpen( )) return false;               // 检测这个端口是否已经被打开了

			// 启动设备采集
			this.devicesCollection.DevicesStart( );

			// 启动上传线程
			this.uploadServerThread = new Thread( new ThreadStart( ThreadUploadServer ) );
			this.uploadServerThread.IsBackground = true;
			this.uploadServerThread.Priority = ThreadPriority.AboveNormal;
			this.uploadServerThread.Start( );

			// 启动DTU服务器
			if (this.edgeResources.EdgeSettings.DtuInfo.UseDtuServer)
			{
				this.dtuServer = new NetworkAlienClient( );
				this.dtuServer.SetPassword( Encoding.ASCII.GetBytes( this.edgeResources.EdgeSettings.DtuInfo.Password ) );
				this.dtuServer.IsResponseAck = this.edgeResources.EdgeSettings.DtuInfo.ResponseAck;
				this.dtuServer.ServerStart( this.edgeResources.EdgeSettings.DtuInfo.Port );
				this.dtuServer.OnClientConnected += DtuServer_OnClientConnected;
			}

			// 启动检测更新的线程
			//this.checkVersionThread = new Thread( new ThreadStart( ThreadCheckVersion ) );
			//this.checkVersionThread.IsBackground = true;
			//this.checkVersionThread.Priority = ThreadPriority.AboveNormal;
			//this.checkVersionThread.Start( );

			// 休眠一小会再启动核心服务器
			HslTechnologyHelper.Sleep( 200 );
			// 启动核心网络服务器
			try
			{
				this.edgeResources.MqttServer.LogNet = logNet;
				this.edgeResources.MqttServer.ClientVerification += MqttServer_ClientVerification;
				this.edgeResources.MqttServer.TopicWildcard = this.edgeResources.EdgeSettings.ServerInfo.MqttWildcard;
				this.edgeResources.MqttServer.OnClientApplicationMessageReceive += MqttServer_OnClientApplicationMessageReceive;
				this.edgeResources.MqttServer.RegisterMqttRpcApi( string.Empty, this );
				this.edgeResources.MqttServer.RegisterMqttRpcApi( "Business", BusinessEngine.Business );
				this.edgeResources.MqttServer.ServerStart( this.port );
				if (this.logNet != null) this.logNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;
				this.logNet?.WriteInfo( ToString( ), "核心服务器加载成功！" );
			}
			catch (Exception ex)
			{
				this.logNet?.WriteError( ToString( ), "核心服务器加载失败！" + ex.Message );
				return false;
			}

			// 启动web的服务器
			try
			{
				this.edgeResources.HttpServer.RegisterHttpRpcApi( string.Empty, this );
				this.edgeResources.HttpServer.RegisterHttpRpcApi( "Business", BusinessEngine.Business );
				this.edgeResources.HttpServer.HandleRequestFunc = ( HttpListenerRequest request, HttpListenerResponse response, string data ) =>
				{
					response.StatusCode = 404;
					return "Wrong Page";
				};
				this.edgeResources.EdgeSettings.SetHttpLoginAccount( this.edgeResources.HttpServer );
				this.edgeResources.HttpServer.Start( this.port + 1 );
				this.logNet?.WriteInfo( ToString( ), "Web服务器加载成功！" );
			}
			catch (Exception ex)
			{
				this.logNet?.WriteError( ToString( ), "Web服务器加载失败！" + ex.Message );
			}

			// 启动websocket
			try
			{
				this.edgeResources.WebSocketServer.OnClientApplicationMessageReceive += WebSocketServer_OnClientApplicationMessageReceive;
				this.edgeResources.WebSocketServer.OnClientConnected += WebSocketServer_OnClientConnected;
				this.edgeResources.WebSocketServer.ServerStart( this.port + 4 );
				this.edgeResources.WebSocketServer.TopicWildcard = this.edgeResources.EdgeSettings.ServerInfo.MqttWildcard;
				this.logNet?.WriteInfo( ToString( ), "WebSocket服务器加载成功！" );
			}
			catch(Exception ex)
			{
				this.logNet?.WriteError( ToString( ), "WebSocket服务器加载失败！" + ex.Message );
			}

			// 启动更新文件的服务器
			try
			{
				UpdateServerIni( this.port + 2 );
				this.logNet?.WriteInfo( ToString( ), "远程更新服务器加载成功！" );
			}
			catch (Exception ex)
			{
				this.logNet?.WriteError( ToString( ), "远程更新服务器加载失败！" + ex.Message );
			}

			try
			{
				if (this.edgeResources.EdgeSettings.UploadInfo.UseRedisServer)
				{
					this.redisClient = new RedisClient(
						this.edgeResources.EdgeSettings.UploadInfo.RedisIpAddress,
						this.edgeResources.EdgeSettings.UploadInfo.RedisPort,
						this.edgeResources.EdgeSettings.UploadInfo.RedisPassword );
					this.redisClient.SetPersistentConnection( );
					this.redisClient.SelectDB( this.edgeResources.EdgeSettings.UploadInfo.RedisDBNumber );
				}
			}
			catch (Exception ex)
			{
				this.logNet?.WriteError( ToString( ), $"用于上传远程[{this.edgeResources.EdgeSettings.UploadInfo.RedisIpAddress}] Redis客户端创建失败，无法上传Redis数据！" + ex.Message );
			}

			try
			{
				if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer)
				{
					this.mqttConnectionOptions = this.edgeResources.EdgeSettings.CreateHslMqttClientOption( );
				}
			}
			catch (Exception ex)
			{
				this.mqttConnectionOptions = null;
				this.logNet?.WriteError( ToString( ), $"用于上传远程[{this.edgeResources.EdgeSettings.UploadInfo.MqttIpAddress}] MQTT客户端创建失败，无法上传MQTT数据！" + ex.Message );
			}

			try
			{
				if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer2)
				{
					this.mqttConnectionOptions2 = this.edgeResources.EdgeSettings.CreateHslMqttClientOption2( );
				}
			}
			catch (Exception ex)
			{
				this.mqttConnectionOptions2 = null;
				this.logNet?.WriteError( ToString( ), $"用于上传远程[{this.edgeResources.EdgeSettings.UploadInfo.MqttIpAddress2}] MQTT2客户端创建失败，无法上传MQTT数据！" + ex.Message );
			}


			// 整个系统的状态标记为启动中
			this.isRunning = 1;
			return true;
		}

		private void WebSocketServer_OnClientConnected( WebSocketSession session )
		{
			session.Url = string.Empty; // 该参数暂时用来存储登录信息
		}

		private void DealWebsocketErrorBack( WebSocketSession session, string command, string topic, string error )
		{
			JObject json = new JObject( );
			json["Command"] = command;
			json["Topic"] = topic;
			json["Error"] = error;

			this.edgeResources.WebSocketServer.SendClientPayload( session, json.ToString( ) );
		}

		private void WebSocketServer_OnClientApplicationMessageReceive( WebSocketSession session, WebSocketMessage message )
		{
			string command = string.Empty;
			string topic   = string.Empty; 
			try
			{
				// { "Topic": "A", "Command": "Subscribe/UnSubscribe/Publish/Read/Login", "Payload": "" }
				JObject para = JObject.Parse( Encoding.UTF8.GetString( message.Payload ) );

				command = para["Command"].Value<string>( );
				topic   = para["Topic"].Value<string>( );

				if (command.Equals( "Login", StringComparison.OrdinalIgnoreCase ))
				{
					// 使用账户密码登录到当前的网关里
					// { "Topic": "", "Command": "Login", "Payload": { "Name": "user1", "Password": "123456" } }
					string name = para["Payload"]["Name"].Value<string>( );
					string password = para["Payload"]["Password"].Value<string>( );

					if (this.edgeResources.EdgeSettings.CheckAccount( name, password ))
					{
						session.Url = name;
						return;
					}
					else
					{
						DealWebsocketErrorBack( session, command, topic, "当前的用户名或是密码错误（The current user name or password is incorrect）" );
						return;
					}
				}
				else
				{
					if ( string.IsNullOrEmpty(session.Url) )
					{
						DealWebsocketErrorBack( session, command, topic, "当前没有账户登录(No account login)" );
						return;
					}
				}

				if (command.Equals( "Subscribe", StringComparison.OrdinalIgnoreCase ))
				{
					session.AddTopic( topic );
					this.logNet?.WriteWarn( ToString( ), $"Websocket 客户端[{session.Remote}] 订阅了主题：[{topic}]！" );

					// 如果该主题存在数据，则直接返回一次订阅信息
					OperateResult<JToken> read = this.DeviceData( topic );
					if (read.IsSuccess)
					{
						JObject json = new JObject( );
						json["Topic"] = topic;
						json["Value"] = read.Content;

						this.edgeResources.WebSocketServer.SendClientPayload( session, json.ToString( ) );
					}
					else
					{
						DealWebsocketErrorBack( session, command, topic, "当前订阅的主题不存在或是数据没有初始化，请重新确认，如果确实是写错了，则再次调用取消订阅指令[UnSubscribe]" );
					}
				}
				else if (command.Equals( "UnSubscribe", StringComparison.OrdinalIgnoreCase ))
				{
					session.RemoveTopic( topic );
					this.logNet?.WriteWarn( ToString( ), $"Websocket 客户端[{session.Remote}] 取消订阅了主题：[{topic}]！" );
				}
				else if (command.Equals( "Publish", StringComparison.OrdinalIgnoreCase ))
				{
					if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountWritePermission( session.Url ))
					{
						DealWebsocketErrorBack( session, command, topic, EdgeStringResource.NoWritePermision );
					}
					else
					{
						// 发布则表示进行数据反写的操作
						this.logNet?.WriteWarn( ToString( ), $"Websocket 客户端[{session.Remote}] 发布数据[{topic}]指令！" );
						// this.webSocketServer.PublishClientPayload( topic, para["Payload"].Value<string>( ) );
						string payload = para.ContainsKey( "Payload" ) ? para["Payload"].Value<string>( ) : null;
						OperateResult result = this.devicesCollection.DealWithPublishTopic( topic, null, payload );
						if (!result.IsSuccess)
						{
							DealWebsocketErrorBack( session, command, topic, result.Message );
						}
					}
				}
				else if (command.Equals( "Read", StringComparison.OrdinalIgnoreCase ))
				{
					// 读取指定主题的数据信息
					OperateResult<JToken> read = this.DeviceData( topic );
					if (read.IsSuccess)
					{
						JObject json    = new JObject( );
						json["Topic"]   = topic;
						json["Command"] = command;
						json["Value"]   = read.Content;

						this.edgeResources.WebSocketServer.SendClientPayload( session, json.ToString( ) );
					}
					else
					{
						DealWebsocketErrorBack( session, command, topic, read.Message );
					}
				}
				else
				{
					DealWebsocketErrorBack( session, command, topic, $"Websocket 接收到无效的指令(例如是否多出空格):{command} 指令应该为 [Subscribe/UnSubscribe/Publish/Read/Login] 一种。" );
					this.logNet?.WriteError( ToString( ), $"Websocket 接收到无效的指令：" + topic );
				}
			}
			catch (Exception ex)
			{
				// 如果异常，则回发一条错误信息给对方
				try
				{
					DealWebsocketErrorBack( session, command, topic, ex.Message + Environment.NewLine + @"一个例子格式: { ""Topic"": ""电表 2/电压"", ""Command"": ""Subscribe/UnSubscribe/Publish/Read/Login"", ""Payload"": """" }" );
				}
				catch
				{

				}
				this.logNet?.WriteError( ToString( ), $"Websocket 在接收客户端的数据时发生了异常：" + ex.Message );
			}
		}

		private void MqttServer_OnClientApplicationMessageReceive( MqttSession session, MqttClientApplicationMessage message )
		{
			if (session.Protocol == "HUSL")
			{
				this.edgeResources.MqttServer.ReportOperateResult( session, "当前的API在本版本网关服务器中不支持！" );
			}
			else if (session.Protocol == "MQTT")
			{
				// 屏蔽转发操作
				message.IsCancelPublish = true;

				// 判断当前的账户是否有反写的权限
				if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountWritePermission( session.UserName ))
				{
					this.logNet?.WriteDebug( this.edgeResources.MqttServer.ToString( ), $"{session} {EdgeStringResource.NoWritePermision}！" );
					return;
				}

				// 检查Topic是否是唯一设备信息，如果是的，则进行反写数据的操作
				this.devicesCollection.DealWithPublishTopic( message.Topic, message.Payload ?? new byte[0], null );
			}
		}

		private void DtuServer_OnClientConnected( AlienSession session )
		{
			this.devicesCollection.DtuServerOnClientConnected( session );
		}

		private int MqttServer_ClientVerification( MqttSession session, string clientId, string userName, string password )
		{
			AwaitSystemRunning( );                  // 等待服务器启动完成，才校验当前的账户密码

			if (this.edgeResources.EdgeSettings.CheckAccount( userName, password )) return 0;
			this.logNet?.WriteDebug( this.edgeResources.MqttServer.ToString( ), $"{session} 账户密码验证失败，本次登录失败！" );
			return 4;
		}

		/// <summary>
		/// 服务器程序关闭
		/// </summary>
		public void Close( )
		{
			this.logNet?.WriteWarn( "Close", "准备退出服务器线程。" );
			this.devicesCollection.DevicesClose( );
			this.edgeResources.MqttServer?.ServerClose( );
			//this.guirdClient?.ConnectClose( );
			this.isRunning = 2;
		}

		#endregion

		#region UpdateServer

		public void UpdateServerIni( int port )
		{
			fileServer = new AdvancedFileServer( );
			fileServer.FilesDirectoryPath          = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "Upgrade" );
			fileServer.FilesDirectoryPathTemp      = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "Tmp" );

			if (!Directory.Exists( fileServer.FilesDirectoryPath ))     Directory.CreateDirectory( fileServer.FilesDirectoryPath );
			if (!Directory.Exists( fileServer.FilesDirectoryPathTemp )) Directory.CreateDirectory( fileServer.FilesDirectoryPathTemp );
			fileServer.ServerStart( port );
		}

		private void ClearUpdateServerPath( )
		{
			if (Directory.Exists( fileServer.FilesDirectoryPath ))
			{
				string[] files = Directory.GetFiles( fileServer.FilesDirectoryPath );
				for (int i = 0; i < files.Length; i++)
				{
					if (File.Exists( files[i] )) File.Delete( files[i] );
				}
			}
		}

		#endregion

		#region LogNet Push

		/// <summary>
		/// 日志存储前的操作，用来进行客户端的数据推送的
		/// </summary>
		/// <param name="sender">日志对象</param>
		/// <param name="e">日志信息</param>
		private void LogNet_BeforeSaveToFile( object sender, HslEventArgs e )
		{
			this.edgeResources.MqttServer?.PublishTopicPayload( "__log", Encoding.UTF8.GetBytes( e.HslMessage.ToString( ) ) );
		}

		private void EdgeLog_EdgeLogger( string deviceID, string key, HslMessageDegree degree, string msg )
		{
			switch (degree)
			{
				case HslMessageDegree.DEBUG: this.logNet?.WriteDebug( key, msg ); break;
				case HslMessageDegree.INFO:  this.logNet?.WriteInfo(  key, msg ); break;
				case HslMessageDegree.WARN:  this.logNet?.WriteWarn(  key, msg ); break;
				case HslMessageDegree.ERROR: this.logNet?.WriteError( key, msg ); break;
				case HslMessageDegree.FATAL: this.logNet?.WriteFatal( key, msg ); break;
			}

			if (!string.IsNullOrEmpty( deviceID ))
			{
				HslMessageItem hslMessageItem = new HslMessageItem( );
				hslMessageItem.KeyWord = key;
				hslMessageItem.Text = msg;
				hslMessageItem.ThreadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
				hslMessageItem.Degree = degree;

				this.edgeResources.MqttServer?.PublishTopicPayload( "__log:" + deviceID, Encoding.UTF8.GetBytes( hslMessageItem.ToString( ) ) );
			}
		}

		private void EdgeLog_DeviceTelegramLogger( string deviceID, string msg )
		{
			// 通信电文的日志记录
			if (!string.IsNullOrEmpty( deviceID ))
			{
				this.edgeResources.MqttServer?.PublishTopicPayload( "__log.Telegram:" + deviceID, Encoding.UTF8.GetBytes( msg ) );
			}
		}

		#endregion

		#region Service Support

		/// <summary>
		/// 在远程客户端连接当前的网关系统时，如果系统没有启动完毕，则等待系统启动完成
		/// </summary>
		/// <remarks>
		/// 防止请求服务器的数据时，还有些数据还没初始化完成，导致客户端解析失败的bug。
		/// </remarks>
		public void AwaitSystemRunning( )
		{
			int tick = 0;
			while( this.isRunning == 0)
			{
				HslTechnologyHelper.Sleep( 20 );
				tick++;

				if (tick > 500)
				{
					// 等待最多10秒钟的时间，一般情况都足够等待服务启动完成了。
					break;
				}
			}
		}

		#region Admin Interface

		[HslMqttApi( ApiTopic = "Admin/ServerSettingsRequest", Description = "查看服务器的系统配置信息" )]
		public OperateResult<JObject> ServerSettingsRequest( ISessionContext context )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountServerSettings( context.UserName ))
				return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.GetSettingsJsonString( ) );
			else
				return new OperateResult<JObject>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/ServerSettingsModify", Description = "修改服务器了参数信息" )]
		public OperateResult ServerSettingsModify( ISessionContext context, string data )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountServerSettings( context.UserName ))
				return new OperateResult<JObject>( EdgeStringResource.OperateNoPermision );

			try
			{
				this.edgeResources.EdgeSettings.LoadSettingsByJson( data );
				this.edgeResources.EdgeSettings.SaveSettings( );
				this.logNet?.WriteWarn( "ServerSettingsModify", "修改服务器了参数信息" ); //  参数为：data
				// 如果日志修改了日志等级，直接生效。
				this.logNet.SetMessageDegree( this.edgeResources.EdgeSettings.LogInfo.MessageDegree );
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				this.logNet?.WriteException( "ServerSettingsModify", ex );
				return new OperateResult( ex.Message );
			}
		}

		[HslMqttApi( ApiTopic = "Admin/ServerInfos", Description = "获取网关服务器的基本信息" )]
		public OperateResult<JObject> ServerInfos( ISessionContext context )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountServerSettings( context.UserName ))
				return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.GetServerInfos( ) );
			else
				return new OperateResult<JObject>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/XmlSettingsRequest", Description = "查看服务器的原始的设备配置信息，包含所有的设备细节，点位细节，文件格式为XML" )]
		public OperateResult<string> XmlSettingsRequest( ISessionContext context )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.GetXmlSettings( ).ToString( ) );
			else
				return new OperateResult<string>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/XmlSettingsModify", Description = "修改服务器了设备节点配置，文件格式为XML" )]
		public OperateResult XmlSettingsModify( ISessionContext context, string data )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return new OperateResult<JObject>( EdgeStringResource.OperateNoPermision );
			try
			{
				this.edgeResources.EdgeSettings.SetXmlSettings( data );
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				this.logNet?.WriteException( "XmlSettingsModify", ex );
				return new OperateResult( ex.Message );
			}
		}

		[HslMqttApi( ApiTopic = "Admin/JsonSettingsRequest", Description = "查看服务器的原始的设备配置信息，包含所有的设备细节，点位细节，文件格式为JSON" )]
		public OperateResult<string> JsonSettingsRequest( ISessionContext context )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return OperateResult.CreateSuccessResult( HslTechnologyHelper.ConvertXmlToJsonString( this.edgeResources.EdgeSettings.GetXmlSettings( ), Newtonsoft.Json.Formatting.None ) );
			else
				return new OperateResult<string>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/JsonSettingsModify", Description = "修改服务器了设备节点配置，文件格式为JSON" )]
		public OperateResult JsonSettingsModify( ISessionContext context, string data )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return new OperateResult<JObject>( EdgeStringResource.OperateNoPermision );
			try
			{
				this.edgeResources.EdgeSettings.SetXmlSettings( HslTechnologyHelper.ConvertJsonToXmlString( data ) );
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				this.logNet?.WriteException( "XmlSettingsModify", ex );
				return new OperateResult( ex.Message );
			}
		}


		[HslMqttApi( ApiTopic = "Admin/XmlSettingsHistoryNamesRequest", Description = "查看网关服务器历史的配置文件列表" )]
		public OperateResult<string[]> XmlSettingsHistoryNamesRequest( ISessionContext context )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.GetXmlSettingsHistory( ) );
			else
				return new OperateResult<string[]>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/XmlSettingsHistoryRequest", Description = "根据指定的文件名，获取网关服务器历史的配置文件信息" )]
		public OperateResult<string> XmlSettingsHistoryRequest( ISessionContext context, string fileName )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return this.edgeResources.EdgeSettings.GetXmlSettingsHistory( fileName );
			else
				return new OperateResult<string>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/XmlSettingsHistoryDelete", Description = "根据指定的文件名，删除历史配置的文件信息" )]
		public OperateResult<string> XmlSettingsHistoryDelete( ISessionContext context, string fileName )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return this.edgeResources.EdgeSettings.DeleteXmlSettingsHistory( fileName );
			else
				return new OperateResult<string>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/XmlSettingsStandbyNamesRequest", Description = "查看网关服务器备用的配置文件列表" )]
		public OperateResult<string[]> XmlSettingsStandbyNamesRequest( ISessionContext context )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.GetXmlSettingsStandby( ) );
			else
				return new OperateResult<string[]>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/XmlSettingsStandbyRequest", Description = "根据指定的文件名，获取网关服务器备用的配置文件信息" )]
		public OperateResult<string> XmlSettingsStandbyRequest( ISessionContext context, string fileName )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return this.edgeResources.EdgeSettings.GetXmlSettingsStandby( fileName );
			else
				return new OperateResult<string>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/XmlSettingsStandbyDelete", Description = "根据指定的文件名，删除备用配置的文件信息" )]
		public OperateResult<string> XmlSettingsStandbyDelete( ISessionContext context, string fileName )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return this.edgeResources.EdgeSettings.DeleteXmlSettingsStandby( fileName );
			else
				return new OperateResult<string>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/XmlSettingsStandbyAdd", Description = "增加一个备用配置文件信息，需要指定文件名，数据，如果文件已经存在，则覆盖。" )]
		public OperateResult<string> XmlSettingsStandbyAdd( ISessionContext context, string fileName, string data )
		{
			if (this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return this.edgeResources.EdgeSettings.AddXmlSettingsStandby( fileName, data );
			else
				return new OperateResult<string>( EdgeStringResource.OperateNoPermision );
		}

		[HslMqttApi( ApiTopic = "Admin/ServerCloseAndRestart", Description = "关闭服务器并且重新启动" )]
		public OperateResult<JObject> ServerCloseAndRestart( ISessionContext context )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountServerCloseRestart( context.UserName ))
				return new OperateResult<JObject>( EdgeStringResource.OperateNoPermision );

			ThreadPool.QueueUserWorkItem( new WaitCallback( ( obj ) =>
			{
				Program.QuitMessage = $"来自远程客户端[{context.UserName}]使用了重启命令操作！";
				HslTechnologyHelper.Sleep( 100 );
				this.Close( );
				this.ReStarted = true;
			} ) );
			return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.GetSettingsJsonString( ) );
		}

		[HslMqttApi( ApiTopic = "Admin/ServerClose", Description = "关闭服务器的操作" )]
		public OperateResult<JObject> ServerClose( ISessionContext context )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountServerCloseRestart( context.UserName ))
				return new OperateResult<JObject>( EdgeStringResource.OperateNoPermision );

			ThreadPool.QueueUserWorkItem( new WaitCallback( ( obj ) =>
			{
				Program.QuitMessage = $"来自远程客户端[{context.UserName}]使用了关闭命令操作！";
				HslTechnologyHelper.Sleep( 100 );
				this.Close( );
				this.ReStarted = false;
			} ) );
			return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.GetSettingsJsonString( ) );
		}

		[HslMqttApi( ApiTopic = "Admin/UpdateRemoteProgram", Description = "启动远程更新服务的功能" )]
		public OperateResult UpdateRemoteProgram( ISessionContext context )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAdminAccount( context.UserName ))
				return new OperateResult<JObject>( EdgeStringResource.AdminAuthority );

			ThreadPool.QueueUserWorkItem( new WaitCallback( ( obj ) =>
			{
				Program.QuitMessage = $"来自远程客户端[{context.UserName}]使用了更新程序命令操作！";
				HslTechnologyHelper.Sleep( 100 );
				this.isNewUpdateServer = true;
				this.Close( );
				this.ReStarted = true;
			} ) );
			return OperateResult.CreateSuccessResult( );
		}

		[HslMqttApi( ApiTopic = "Admin/UndoUpdateRemoteProgram", Description = "撤销远程更新服务的功能" )]
		public OperateResult UndoUpdateRemoteProgram( ISessionContext context )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAdminAccount( context.UserName ))
				return new OperateResult<JObject>( EdgeStringResource.AdminAuthority );

			this.isNewUpdateServer = false;
			ClearUpdateServerPath( );
			return OperateResult.CreateSuccessResult( );
		}

		[HslMqttApi( ApiTopic = "Admin/GetDeviceAddressLabel", Description = "获取指定设备的标签地址映射关系" )]
		public OperateResult<DeviceSingleAddressLabel[]> GetDeviceAddressLabel( ISessionContext context, string data )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return new OperateResult<DeviceSingleAddressLabel[]>( EdgeStringResource.OperateNoPermision );

			return devicesCollection.GetDeviceAddressLabel( data );
		}

		[HslMqttApi( ApiTopic = "Admin/GetAddressLabelByTagName", Description = "获取指定设备的标签地址映射关系" )]
		public OperateResult<DeviceSingleAddressLabel> GetAddressLabelByTagName( ISessionContext context, string tagName )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return new OperateResult<DeviceSingleAddressLabel>( EdgeStringResource.OperateNoPermision );

			return devicesCollection.GetAddressLabelByTagName( tagName );
		}


		private bool standbyEdgeExist = false;
		private DateTime standbyEdgeActiveTime = HslTechnologyHelper.GetDateTimeNow( );
		[HslMqttApi( ApiTopic = "Admin/StandbyEdgeCheck", Description = "备用服务器定时请求的接口，主服务器用于确认备用服务器的状态" )]
		public OperateResult<string> StandbyEdgeCheck( ISessionContext context, string edgeName )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAdminAccount( context.UserName ))
				return new OperateResult<string>( EdgeStringResource.AdminAuthority );

			if (!standbyEdgeExist) standbyEdgeExist = true;

			standbyEdgeActiveTime = HslTechnologyHelper.GetDateTimeNow( );
			this.serverData.SetSingleValue( "__standbyStatus", "Online" );
			return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.ServerInfo.DeviceName );
		}

		[HslMqttApi( ApiTopic = "Admin/ReadSourceRequest", Description = "指定设备路径，如果不存在，则创建新的设备，来读取原始的字节数据" )]
		public async Task<OperateResult<string>> ReadSourceRequest( ISessionContext context, string deviceUrl, string deviveXml, string requestXml )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountDeviceSettings( context.UserName ))
				return new OperateResult<string>( EdgeStringResource.OperateNoPermision );

			return await devicesCollection.ReadSourceRequest( deviceUrl, deviveXml, requestXml );
		}

		#endregion

		#region Edge Interface

		[HslMqttApi( ApiTopic = "Edge/XmlDeviceNodes", Description = "查看服务器的基本的设备节点内容，包含设备基本信息和类型" )]
		public OperateResult<string> XmlDeviceNodes( )
		{
			return OperateResult.CreateSuccessResult( this.devicesCollection.XmlDeviceNodes.ToString( ) );
		}

		[HslMqttApi( ApiTopic = "Edge/CommunicationTest", Description = "通信测试的接口信息", HttpMethod = "GET" )]
		public OperateResult<string> CommunicationTest( )
		{
			return OperateResult.CreateSuccessResult( devicesCollection.DeviceName );
		}

		/// <inheritdoc cref="IEdgeServices.DeviceData(string)"/>
		[HslMqttApi( ApiTopic = "Edge/DeviceData", Description = "获得指定设备路径的数据，可以用来获取所有的数据", HttpMethod = "GET" )]
		public OperateResult<JToken> DeviceData( string data )
		{
			try
			{
				string path = data;
				bool isPath = path.IsDevicePath( );
				string[] nodePath = path.SplitDeviceNodes( );

				if (nodePath.Length == 0)
				{
					// response = this.serverData.DeviceJsonTemp;
					JObject json = devicesCollection.GetAllDevicesData( nodePath, isPath );
					json["__status"] = this.serverData.DeviceJsonClone;
					return OperateResult.CreateSuccessResult( (JToken)json );
				}
				else if (nodePath[0] == "__status")
				{
					if (nodePath.Length == 1) return OperateResult.CreateSuccessResult( (JToken)this.serverData.DeviceJsonClone );
					else return OperateResult.CreateSuccessResult( this.serverData.GetJTokenValueByName( nodePath[1] ) );
				}
				else
				{
					// 真实的设备数据信息
					return devicesCollection.GetDataByNodePath( nodePath, isPath );
				}
			}
			catch(Exception ex)
			{
				logNet?.WriteException( $"DeviceData[{data}]", ex );
				return new OperateResult<JToken>( ex.Message );
			}
		}

		/// <inheritdoc cref="IEdgeServices.DeviceDataArray(string)"/>
		[HslMqttApi( ApiTopic = "Edge/DeviceDataArray", Description = "获得指定设备路径的数据，可以用来获取所有的数据", HttpMethod = "GET" )]
		public OperateResult<JArray> DeviceDataArray( string[] data )
		{
			try
			{
				JArray array = new JArray( );
				for (int i = 0; i < data.Length; i++)
				{
					OperateResult<JToken> read = DeviceData( data[i] );
					if (!read.IsSuccess) return OperateResult.CreateFailedResult<JArray>( read );

					array.Add( read.Content );
				}
				return OperateResult.CreateSuccessResult( array );
			}
			catch (Exception ex)
			{
				logNet?.WriteException( $"DeviceDataArray[{data}]", ex );
				return new OperateResult<JArray>( ex.Message );
			}
		}


		/// <inheritdoc cref="IEdgeServices.GetSourceRequestCahce(string)"/>
		[HslMqttApi( ApiTopic = "Edge/GetSourceRequestCahce", Description = "获得指定设备路径的数据", HttpMethod = "GET" )]
		public OperateResult<byte[]> GetSourceRequestCahce( string data )
		{
			try
			{
				string path = data;
				bool isPath = path.IsDevicePath( );
				string[] nodePath = path.SplitDeviceNodes( );

				if (nodePath.Length == 0 || nodePath[0] == "__status" || isPath)
				{
					return new OperateResult<byte[]>( "需要指定设备的请求节点信息才能访问" );
				}
				else
				{
					// 真实的设备数据信息
					return devicesCollection.GetSourceRequestCahceByNodePath( nodePath );
				}
			}
			catch (Exception ex)
			{
				logNet?.WriteException( $"DeviceData[{data}]", ex );
				return new OperateResult<byte[]>( ex.Message );
			}
		}

		[HslMqttApi( ApiTopic = "Edge/GetExceptionDeviceList", Description = "根据状态信息，获取当前所有的设备的列表，0表示在线设备，1表示暂停设备，2表示异常设备", HttpMethod = "GET" )]
		public OperateResult<DeviceExceptionMessage[]> GetExceptionDeviceList( int status )
		{
			return this.devicesCollection.GetExceptionDeviceList( status );
		}

		/// <inheritdoc cref="IEdgeServices.GetDeviceCore(string)(string)"/>
		public OperateResult<DeviceCore> GetDeviceCore( string data )
		{
			return this.devicesCollection.GetDeviceCoreByPath( data );
		}

		[HslMqttApi( ApiTopic = "Edge/DeviceContinueRequest", Description = "通知设备继续请求数据或是调用方法，如果设备不处于暂时的话，则没有影响", HttpMethod = "GET" )]
		public OperateResult<string> DeviceContinueRequest( string data )
		{
			return devicesCollection.DeviceContinueRequest( data );
		}

		[HslMqttApi( ApiTopic = "Edge/DeviceStopRequest", Description = "通知设备暂停请求数据或是调用方法，如果设备处于暂时的话，则没有影响", HttpMethod = "GET" )]
		public OperateResult<string> DeviceStopRequest( string data )
		{
			return devicesCollection.DeviceStopRequest( data );
		}

		[HslMqttApi( ApiTopic = "Edge/DeviceRequestCount", Description = "获取当前设备的某个请求的请求次数，无论是单标量请求，原始字节请求，方法调用，定时写入请求", HttpMethod = "GET" )]
		public OperateResult<long> DeviceRequestCount( string data )
		{
			return devicesCollection.DeviceRequestCount( data );
		}

		[HslMqttApi( ApiTopic = "Edge/GetRamUseHistoryData", Description = "获取网关最新的内存大小的缓存数据，通常只有一两千个数据", HttpMethod = "GET" )]
		public OperateResult<float[]> GetRamUseHistoryData( )
		{
			return OperateResult.CreateSuccessResult( this.ramUseHistory.ToArray( ) );
		}

		[HslMqttApi( ApiTopic = "Edge/GetThreadUseHistoryData", Description = "获取网关最新的线程数量的短暂历史缓存数据，通常只有一两千个数据", HttpMethod = "GET" )]
		public OperateResult<float[]> GetThreadUseHistoryData( )
		{
			return OperateResult.CreateSuccessResult( this.threadUseHistory.ToArray( ) );
		}

		[HslMqttApi( ApiTopic = "Edge/BrowseDeviceDataNodes", Description = "浏览指定设备的数据节点信息，用来在浏览数据时提供可视化的支持", HttpMethod = "GET" )]
		public OperateResult<ScalarDataNode[]> BrowseDeviceDataNodes( string data )
		{
			bool isPath = data.IsDevicePath( );
			if (isPath) return new OperateResult<ScalarDataNode[]>( "当前的设备信息不存在！无法指定一个路径来获取节点信息。" );
			string[] nodePath = data.SplitDeviceNodes( );

			if (nodePath.Length == 0) return new OperateResult<ScalarDataNode[]>( "当前的设备信息不存在！不能指定网关本身。" );
			else if (nodePath[0] == "__status") return new OperateResult<ScalarDataNode[]>( "当前的设备信息不存在！不能指定状态信息。" );
			else return devicesCollection.BrowseDeviceDataNodes( nodePath );
		}

		[HslMqttApi( ApiTopic = "Edge/WriteData", Description = "写入设备指定的数据，只能针对配置的节点写入数据", HttpMethod = "POST" )]
		public OperateResult WriteData( ISessionContext context, string data, string value )
		{
			if (context != null)
				if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountWritePermission( context.UserName ))
					return new OperateResult<JObject>( EdgeStringResource.NoWritePermision );

			OperateResult write = devicesCollection.WriteDataByNodePath( data, value );
			if (!write.IsSuccess) logNet?.WriteWarn( ToString( ), $"WriteData failed, [{data}] reason: {write.Message}" );
			return write.IsSuccess ? OperateResult.CreateSuccessResult( ) : write;
		}

		[HslMqttApi( ApiTopic = "Edge/WriteDataArray", Description = "批量写入设备指定的数据，只能针对配置的节点写入数据，返回的bool[]标记每个标签是否写入成功\r\nparallel: 如果需要开启并行写入操作以加快写入性能，则指定该参数为 True", HttpMethod = "POST" )]
		public OperateResult<bool[]> WriteDataArray( ISessionContext context, string[] data, string[] value, bool parallel = false )
		{
			if (context != null)
				if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountWritePermission( context.UserName ))
					return new OperateResult<bool[]>( EdgeStringResource.NoWritePermision );

			if (data == null || value == null) return new OperateResult<bool[]>( "写入的数据不能为空，或是写入的路径不能为空" );
			if (data.Length != value.Length) return new OperateResult<bool[]>( "两个参数的数组长度不一致，无法写入。" );
			bool[] result = new bool[data.Length];
			string errMessage = string.Empty;

			if (parallel)
			{
				Parallel.For( 0, data.Length, i =>
				{
					OperateResult write = WriteData( context, data[i], value[i] );
					result[i] = write.IsSuccess;
					if (!write.IsSuccess && string.IsNullOrEmpty( errMessage )) errMessage = $"写入地址[{data[i]}]，值[{value[i]}] 异常：" + write.Message;
				} );
			}
			else
			{
				for (int i = 0; i < data.Length; i++)
				{
					OperateResult write = WriteData( context, data[i], value[i] );
					result[i] = write.IsSuccess;
					if (!write.IsSuccess && string.IsNullOrEmpty( errMessage )) errMessage = $"写入地址[{data[i]}]，值[{value[i]}] 异常：" + write.Message;
				}
			}

			OperateResult<bool[]> op = OperateResult.CreateSuccessResult( result );
			if (!string.IsNullOrEmpty( errMessage )) op.Message = errMessage;
			return op;
		}

		[HslMqttApi( ApiTopic = "Edge/DeviceName", Description = "获取当前的设备名称", HttpMethod = "GET" )]
		public OperateResult<string> DeviceName( )
		{
			return OperateResult.CreateSuccessResult( this.edgeResources.EdgeSettings.ServerInfo.DeviceName );
		}

		[HslMqttApi( ApiTopic = "Edge/DeviceSerialPorts", Description = "获取当前的有效的串口信息列表", HttpMethod = "GET" )]
		public OperateResult<string[]> DeviceSerialPorts( bool needMapping = true )
		{
			try
			{
				if (needMapping)
				{
					string[] ports = this.edgeResources.EdgeSettings.PortMapping.GetPorts( );
					Array.Sort( ports );
					return OperateResult.CreateSuccessResult( ports );
				}
				return OperateResult.CreateSuccessResult( System.IO.Ports.SerialPort.GetPortNames( ) );
			}
			catch (Exception ex)
			{
				logNet?.WriteFatal( ToString( ), "GetSerialPorts failed: " + ex.Message );
				return OperateResult.CreateSuccessResult( new string[0] );
			}
		}

		[HslMqttApi( ApiTopic = "Edge/DeviceCommunicationTest", Description = "设备的通信测试，返回测试结果", HttpMethod = "GET" )]
		public OperateResult DeviceCommunicationTest( string xmlDevice )
		{
			try
			{
				XElement element = XElement.Parse( xmlDevice );
				if (element.Name == NodeType.DeviceNode.ToString( ) ||
					element.Name == NodeType.RobotNode.ToString( ) ||
					element.Name == NodeType.CncNode.ToString( ))
				{
					OperateResult<DeviceCoreBase> deviceResult = EdgeReflectionHelper.GetDeviceCoreFrom( element );
					if (!deviceResult.IsSuccess) return deviceResult.ConvertFailed<string>( );

					DeviceCoreBase device = deviceResult.Content;
					if (device == null) return new OperateResult( "当前的设备不支持测试！" );

					// 初始化
					device.IniDevice( element, this.edgeResources );
					OperateResult<string> test = device.CheckDeviceStatus( );
					if (test.IsSuccess)
						this.logNet?.WriteDebug( device.ToString( ) + " 测试通信成功！" );
					else
						this.logNet?.WriteWarn( device.ToString( ) + " 测试通信失败: " + test.Message );
					return test;
				}
				else if (element.Name == NodeType.DatabaseNode.ToString( ))
				{
					DatabaseCore database = DatabaseResource.CreateDatabaseCore( element );
					if (database == null) return new OperateResult( "当前的设备不支持测试！" );

					OperateResult test = database.TestDatabase( ).Result;
					return test;
				}
				else
				{
					return new OperateResult( "当前的设备不支持测试！" );
				}
			}
			catch(Exception ex)
			{
				this.logNet?.WriteWarn( "测试设备通信失败：" + xmlDevice );
				return new OperateResult( "测试设备通信失败：" + ex.Message );
			}
		}

		[HslMqttApi( ApiTopic = "Edge/GetMethodByDeviceType", Description = "获取指定设备类型的方法接口信息", HttpMethod = "GET" )]
		public OperateResult<MqttRpcApiInfo[]> GetMethodByDeviceType( DeviceType deviceType )
		{
			return EdgeReflectionHelper.GetMethodByDeviceType( deviceType.ToString( ) );
		}

		[HslMqttApi( ApiTopic = "Edge/GetMethodByDevicePlugins", Description = "获取指定插件设备的方法接口信息", HttpMethod = "GET" )]
		public OperateResult<MqttRpcApiInfo[]> GetMethodByDevicePlugins( string pluginsType )
		{
			return OperateResult.CreateSuccessResult( PluginsHelper.GetDeviceRpcInfoFromPlugins( pluginsType ) );
		}

		[HslMqttApi( ApiTopic = "Edge/GetMethodInfoByDeviceID", Description = "获取指定设备ID的方法接口信息", HttpMethod = "GET" )]
		public OperateResult<MethodRpcInfo[]> GetMethodInfoByDeviceID( string data )
		{
			return devicesCollection.GetMethodInfoByDeviceID( data );
		}

		[HslMqttApi( ApiTopic = "Edge/CallDeviceMethod", Description = "根据设备ID，方法名称，参数信息来调用方法", HttpMethod = "POST" )]
		public async Task<object> CallDeviceMethod( ISessionContext context, string data, string method, string parameterJson )
		{
			if (!this.edgeResources.EdgeSettings.ServerInfo.CheckAccountCallMethodPermission( context.UserName ))
				return new OperateResult<DeviceSingleAddressLabel[]>( EdgeStringResource.OperateNoPermision );

			return await devicesCollection.CallDeviceMethod( this.edgeResources.EdgeSettings.ServerInfo.CheckAdminAccount( context.UserName ), data, method, parameterJson );
		}

		[HslMqttApi( ApiTopic = "Edge/GetDeviceExampleAddress", Description = "获取指定设备的示例地址数据", HttpMethod = "GET" )]
		public OperateResult<DeviceAddressExample[]> GetDeviceExampleAddress( DeviceType deviceType, string pluginsType )
		{
			return EdgeReflectionHelper.GetDeviceExampleAddress( deviceType, pluginsType );
		}

		[HslMqttApi( ApiTopic = "Edge/GetEdgeRuntimeHistory", Description = "获取服务器最近的运行记录，Quit: 0表示异常退出，没有任何记录，1表示正常关闭退出，2表示当前正在运行中", HttpMethod = "GET" )]
		public OperateResult<EdgeTimeConsume[]> GetEdgeRuntimeHistory( ISessionContext context )
		{
			return OperateResult.CreateSuccessResult( this.RunTimeAnalysis.GetEdgeTimes( ) );
		}

		[HslMqttApi( ApiTopic = "Edge/GetEdgeUnhandledException", Description = "获取网关的本地的未捕获异常的消息信息", HttpMethod = "GET" )]
		public OperateResult<string> GetEdgeUnhandledException( )
		{
			try
			{
				string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "UnhandledException.txt" );
				if (File.Exists( path ))
				{
					return OperateResult.CreateSuccessResult( File.ReadAllText( path, Encoding.UTF8 ) );
				}
				return OperateResult.CreateSuccessResult( string.Empty );
			}
			catch (Exception)
			{
				return OperateResult.CreateSuccessResult( string.Empty );
			}
		}

		[HslMqttApi( ApiTopic = "Edge/GetEdgeRuntimeExceptions", Description = "获取网关开始运行后，记录的连续10个重要的异常错误消息", HttpMethod = "GET" )]
		public OperateResult<string[]> GetEdgeRuntimeExceptions( ISessionContext context )
		{
			return OperateResult.CreateSuccessResult( this.edgeResources.FatalMessage.GetMessages( ) );
		}

		[HslMqttApi( ApiTopic = "Edge/GetEdgeInitializeTxt", Description = "获取网关的本地的初始化日志信息", HttpMethod = "GET" )]
		public OperateResult<string> GetEdgeInitializeTxt( )
		{
			try
			{
				string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "initialize.txt");
				if (File.Exists( path ))
					return OperateResult.CreateSuccessResult( File.ReadAllText( path, Encoding.UTF8 ) );
				else
					return new OperateResult<string>( "File not exit " );
			}
			catch( Exception ex )
			{
				return new OperateResult<string>( "Read file failed: " + ex.Message );
			}
		}

		#endregion

		#region Plugins Interface

		[HslMqttApi( ApiTopic = "Plugins/GetRegisterPlugins", Description = "获取当前已经注册的插件信息列表", HttpMethod = "GET" )]
		public OperateResult<PluginsDefinition[]> GetRegisterPlugins( )
		{
			if (PluginsHelper.Plugins != null)
			{
				return OperateResult.CreateSuccessResult( PluginsHelper.Plugins.Values.ToArray( ) );
			}
			else
			{
				return OperateResult.CreateSuccessResult( new PluginsDefinition[0] );
			}
		}

		[HslMqttApi( ApiTopic = "Plugins/GetPluginsPropertyConfig", Description = "获取插件设备的类型获取到属性定义的列表", HttpMethod = "GET" )]
		public OperateResult<NodePropertyConfig[]> GetPluginsPropertyConfig( string pluginsType )
		{
			return OperateResult.CreateSuccessResult( PluginsHelper.GetPluginsPropertyConfigs( pluginsType ) );
		}

		[HslMqttApi( ApiTopic = "Plugins/GetAllPluginsPropertyConfigs", Description = "获取所有插件设备的类型获取到属性定义的列表", HttpMethod = "GET" )]
		public OperateResult<Dictionary<string, NodePropertyConfig[]>> GetAllPluginsPropertyConfigs( )
		{
			return OperateResult.CreateSuccessResult( PluginsHelper.GetAllPluginsPropertyConfigs( ) );
		}

		[HslMqttApi( ApiTopic = "Plugins/EveryAddressOccupyByte", Description = "将指定的插件上传到网关", HttpMethod = "POST" )]
		public OperateResult<int> GetPluginsEveryAddressOccupyByte( string pluginsType, string address )
		{
			return OperateResult.CreateSuccessResult( PluginsHelper.GetPluginsEveryAddressOccupyByte( pluginsType, address ) );
		}


		[HslMqttApi( ApiTopic = "Plugins/UnloadRegisterPlugins", Description = "卸载指定的插件信息", HttpMethod = "POST" )]
		public OperateResult UnloadRegisterPlugins( string pluginsDllName )
		{
			return PluginsHelper.UnloadPlugins( pluginsDllName );
		}

		[HslMqttApi( ApiTopic = "Plugins/LoadRegisterPlugins", Description = "安装指定的插件信息", HttpMethod = "POST" )]
		public OperateResult LoadRegisterPlugins( string pluginsDllName )
		{
			return PluginsHelper.LoadPlugins( pluginsDllName );
		}

		[HslMqttApi( ApiTopic = "Plugins/CheckRegisterPluginsExist", Description = "检查指定的插件是否已经存在，结果1表示存在，0表示不存在", HttpMethod = "POST" )]
		public OperateResult<int> CheckRegisterPluginsExist( string pluginsDllName )
		{
			return PluginsHelper.CheckRegisterPluginsExist( pluginsDllName );
		}

		[HslMqttApi( ApiTopic = "Plugins/UploadPluginsFile", Description = "将指定的插件上传到网关", HttpMethod = "POST" )]
		public OperateResult UploadPluginsFile( string pluginsDllName, string fileName, byte[] content )
		{
			string path = pluginsDllName;
			if (path.EndsWith( ".dll" ) || path.EndsWith( ".DLL" )) path = path.RemoveLast( 4 );
			string filePath = Path.Combine( PluginsHelper.PluginsDirectory( ), path, fileName );
			if (!Directory.Exists( Path.Combine( PluginsHelper.PluginsDirectory( ), path ) ))
				Directory.CreateDirectory( Path.Combine( PluginsHelper.PluginsDirectory( ), path ) );
			try
			{
				File.WriteAllBytes( filePath, content );
				return OperateResult.CreateSuccessResult( );
			}
			catch( Exception ex)
			{
				return new OperateResult( ex.Message );
			}
		}

		#endregion

		#endregion

		#region Public Method

		/// <summary>
		/// 服务器运行的记录信息
		/// </summary>
		public EdgeRunTimeAnalysis RunTimeAnalysis { get; set; }

		/// <inheritdoc cref="IEdgeServices.EdgeLogNet"/>
		public EdgeLog EdgeLogNet { get => this.edgeLog; }

		/// <summary>
		/// 获取设备的集合信息
		/// </summary>
		public DevicesCollection DevicesCollection => this.devicesCollection;

		/// <summary>
		/// 获取当前网关的资源信息
		/// </summary>
		public EdgeDeviceResources EdgeResources => this.edgeResources;

		/// <summary>
		/// 服务器是否处于运行中
		/// </summary>
		public bool IsRunning
		{
			get => this.isRunning < 2;
		}

		/// <summary>
		/// 关闭之后是否进行重启操作
		/// </summary>
		public bool ReStarted { get; set; }

		/// <summary>
		/// 是否是新的版本
		/// </summary>
		public bool IsNewVersion => this.isNewUpdateServer;

		/// <summary>
		/// 服务器的端口信息
		/// </summary>
		public int Port => this.port;

		#endregion

		#region Server Upload Data

		/// <summary>
		/// 更新服务的状态信息
		/// </summary>
		public void UpdateServerStatus( )
		{
			//#if NET461
			//			PerformanceCounter curpcp = new PerformanceCounter( "Process", "Working Set - Private", process.ProcessName );
			//			PerformanceCounter curtime = new PerformanceCounter( "Process", "% Processor Time", process.ProcessName );
			//#endif
			process.Refresh( );
			this.serverData.SetSingleValue( "__activeTime",        HslTechnologyHelper.GetDateTimeNow( ).ToEdgeString( ) );
			this.serverData.SetSingleValue( "__deviceOnlineCount", this.devicesCollection.DeviceOnlineCount );
			this.serverData.SetSingleValue( "__deviceStopCount",   this.devicesCollection.DeviceStopCount );
			this.serverData.SetSingleValue( "__timeDeviation",     HslTechnologyHelper.DateTimeDelta );
			this.serverData.SetSingleValue( "__threadCount",       this.process.Threads.Count );
			this.serverData.SetSingleValue( "__ServerOnline",      this.edgeResources.MqttServer == null ? 0 : this.edgeResources.MqttServer.OnlineCount );
			this.serverData.SetSingleValue( "__timeout",           HslTimeOut.TimeOutCheckCount );
			try
			{
				this.serverData.SetSingleValue( "__ramUse", process.PrivateMemorySize64 );
			}
			catch( Exception ex )
			{
				this.logNet?.WriteError( "EdgeServices", "UpdateServerStatus->PrivateMemorySize64 failed: " + ex.Message );
			}
			this.serverData.SetJArray(      "__deviceList",        this.devicesCollection.GetDevicesStatus( ) );
			//#if NET461
			//				this.serverData.SetJObjectValue( "__ramUse",                                    SoftBasic.GetSizeDescription( (int)curpcp.NextValue( ) ) );
			//				this.serverData.SetJObjectValue( "__cpu",                                       ((int)curtime.NextValue( ) / Environment.ProcessorCount).ToString( ) );
			//#else
			this.serverData.UpdateJsonTmp( );
			this.ramUseHistory.Add( (float)(process.PrivateMemorySize64 / 1024d / 1024d) );
			this.threadUseHistory.Add( (float)this.process.Threads.Count );

			// 更新备用网关的状态信息，如果备用网关存在，且没有信号交互30秒，则将备用网关的在线状态设置为false
			if (standbyEdgeExist)
			{
				TimeSpan ts = HslTechnologyHelper.GetDateTimeNow( ) - standbyEdgeActiveTime;
				if ( ts.TotalSeconds > 30)
				{
					this.serverData.SetSingleValue( "__standbyStatus", "Offline" );
				}
			}

			// 如果有客户端订阅当前网关的状态，则发布该主题消息
			if(this.edgeResources.MqttServer.OnlineCount > 0)
				this.edgeResources.MqttServer?.PublishTopicPayload( "__status", Encoding.UTF8.GetBytes( serverData.DeviceJsonTemp ), this.edgeResources.EdgeSettings.UploadInfo.MqttRetain );
		}

		/// <summary>
		/// 检查服务器的心跳信息
		/// </summary>
		public void GuardTimerCheck( )
		{
			if (!this.devicesCollection.CheckDevicesWork( )) // 如果发现有线程假死，就尝试重启网关
			{
				// 立即记录系统相关的一些参数信息
				Program.CurrentDomain_UnhandledException( null, new UnhandledExceptionEventArgs( new Exception( this.serverData.ToString( ) ), false ) );

				Program.QuitMessage = $"边缘网关服务器检测到了线程假死（不活动）使用了重启命令操作！";
				this.Close( );
				ReStarted = true;
			}
		}

		private void ThreadUploadServer( )
		{
			HslTechnologyHelper.Sleep( 1000 );  // delay
			bool settingsUpload = false;                                        // 配置信息是否上传服务器
			this.logNet?.WriteInfo( "上传服务器的线程启动！" );
			DateTime redisActiveTime = DateTime.Now;
			DateTime captureStart = DateTime.Now;
			DateTime mqttUploadTime1 = DateTime.Now;                            // 用于MQTT配置了变化上传时，也强制定时上传的功能
			DateTime mqttUploadTime2 = DateTime.Now;                            // 用于MQTT配置了变化上传时，也强制定时上传的功能
			HslTimerTick.SleepToSenconds( );
			HslTimerTick hslTimerTick = new HslTimerTick( this.edgeResources.EdgeSettings.UploadInfo.UploadTimeInterval );
			while (this.IsRunning)
			{
				HslTechnologyHelper.Sleep( 20 );
				if (!hslTimerTick.IsTickHappen( )) continue; // 还没到请求时间

				hslTimerTick.MilliSeconds = this.edgeResources.EdgeSettings.UploadInfo.UploadTimeInterval;
				if (!this.IsRunning) return;

				uploadCycleCount++;
				if (uploadCycleCount > 10000) uploadCycleCount = 0;

				captureStart = DateTime.Now;
				// 上传Redis服务，始终使用哈希键来存储相关的数据内容
				if (this.edgeResources.EdgeSettings.UploadInfo.UseRedisServer)
				{
					if (this.redisClient == null)
					{
						this.redisClient = new RedisClient(
							this.edgeResources.EdgeSettings.UploadInfo.RedisIpAddress,
							this.edgeResources.EdgeSettings.UploadInfo.RedisPort,
							this.edgeResources.EdgeSettings.UploadInfo.RedisPassword );
						this.redisClient.SetPersistentConnection( );
						this.redisClient.SelectDB( this.edgeResources.EdgeSettings.UploadInfo.RedisDBNumber );
					}

					// 第一次上传的时候，把xml的配置文档信息也上传
					if (!settingsUpload)
					{
						settingsUpload = this.redisClient.WriteHashKey( devicesCollection.DeviceName, "__settings", this.edgeResources.EdgeSettings.GetXmlSettings( ).ToString( ) ).IsSuccess;
					}

					// 如果配置了redis就进行时间同步操作
					if (uploadCycleCount == 10)
					{
						OperateResult<DateTime> serverTime = redisClient.ReadServerTime( );
						if (serverTime.IsSuccess) HslTechnologyHelper.DateTimeDelta = (serverTime.Content - DateTime.Now).TotalSeconds;
					}

					OperateResult<int> write = this.redisClient.WriteHashKey( devicesCollection.DeviceName, "__status", serverData.DeviceJsonTemp );
					if (write.IsSuccess)
					{
						redisActiveTime = DateTime.Now;
						this.logNet?.WriteInfo( this.redisClient.ToString( ) + " 上传Redis成功！耗时: " + (DateTime.Now - captureStart).TotalMilliseconds.ToString( "F0" ) + " ms" );
					}
					else
					{
						this.logNet?.WriteError( this.ToString( ) + " 上传Redis失败！持续: " + SoftBasic.GetTimeSpanDescription( DateTime.Now - redisActiveTime ) + " 本次耗时：" + SoftBasic.GetTimeSpanDescription( DateTime.Now - captureStart ) );
					}
					for (int i = 0; i < this.devicesCollection.DeviceCount; i++)
					{
						this.redisClient.WriteHashKey( devicesCollection.DeviceName, this.devicesCollection[i].GetDeviceNameWithPath( ), this.devicesCollection[i].JsonData );
					}

					captureStart = DateTime.Now;
				}

				if (!this.IsRunning) return;                           // 如果不是运行中，直接返回
				if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer)
				{
					if (this.mqttConnectionOptions != null)
					{
						try
						{
							if (hslMqttClient == null)          // 如果为空，就创建连接，注意，hslMqttClient只在上传数据的线程里进行连接的操作
							{
								MqttClient mqtt = new MqttClient( mqttConnectionOptions );
								mqtt.OnNetworkError += ( object sender, EventArgs e ) => { if (sender is MqttClient client) { client.ConnectClose( ); } return; };
								OperateResult connect = mqtt.ConnectServer( );
								if (connect.IsSuccess) hslMqttClient = mqtt;
								else throw new Exception( connect.Message );

								if (this.edgeResources.EdgeSettings.UploadInfo.MqttType == UploadInfoConfig.MqttTypeJetLinks)
								{
									mqtt.OnMqttMessageReceived += Mqtt_OnMqttMessageReceived;
									// 订阅所有设备相关的主题
									this.devicesCollection.MqttSubscrib( mqtt, this.edgeResources.EdgeSettings.UploadInfo.MqttType );
								}
								else if (this.edgeResources.EdgeSettings.UploadInfo.MqttType == UploadInfoConfig.MqttTypeNormal)
								{
									mqtt.OnMqttMessageReceived += Mqtt_OnMqttMessageReceived;
									mqtt.SubscribeMessage( new string[]{
										this.edgeResources.EdgeSettings.ServerInfo.DeviceName + "/WriteData",
										this.edgeResources.EdgeSettings.ServerInfo.DeviceName + "/WriteDataArray" } );
								}

								// 在第一次连接上服务器的时候，当配置了标签点位变化上传的时候，也进行发布一次数据信息
								if (this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 2)
								{
									this.devicesCollection.RaiseValueChangeEvent( );
								}
							}
							if (hslMqttClient != null)
							{
								bool dataChangePublish = false;
								if (this.edgeResources.EdgeSettings.UploadInfo.UploadWhenTimedTriggers > 0)
								{
									if ((DateTime.Now - mqttUploadTime1).TotalMilliseconds > this.edgeResources.EdgeSettings.UploadInfo.UploadWhenTimedTriggers)
									{
										dataChangePublish = true;
										mqttUploadTime1 = DateTime.Now;
									}
								}
								if (this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 1) // 每个设备一个主题信息
								{
									OperateResult publish = hslMqttClient.PublishMessage( new MqttApplicationMessage( )
									{
										Topic                 = "__status",
										Payload               = Encoding.UTF8.GetBytes( serverData.DeviceJsonTemp ),
										QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
										Retain                = this.edgeResources.EdgeSettings.UploadInfo.MqttRetain,
									} );
									if (!publish.IsSuccess) throw new Exception( publish.Message );

									this.devicesCollection.UploadByDevice( this.hslMqttClient, dataChangePublish );
								}
								else if (this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 0) // 每个网关一个主题信息
								{
									this.devicesCollection.UploadByEdge( this.hslMqttClient, serverData.DeviceJsonClone, dataChangePublish, this.edgeResources.EdgeSettings.UploadInfo.MqttType );
								}
								else if (this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 2 ) // 数据标签单主题
								{
									if (this.edgeResources.EdgeSettings.UploadInfo.UploadWhenDataChange) // 变化上传的点位
									{
										if (dataChangePublish) // 如果多久不上传，必然上传一次
										{
											this.devicesCollection.RaiseValueChangeEvent( );
										}
									}
									else
									{
										this.devicesCollection.RaiseValueChangeEvent( ); // 没有开启变化，仍然是定时推送
									}
								}
							}
						}
						catch (Exception ex)
						{
							hslMqttClient?.ConnectClose( );
							hslMqttClient = null;
							this.logNet?.WriteError( this.ToString( ) + " MQTT上传失败：" + ex.Message );
							HslTechnologyHelper.Sleep( 1000 );
						}
					}
					else if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer)
					{
						try
						{
							this.mqttConnectionOptions = this.edgeResources.EdgeSettings.CreateHslMqttClientOption( );
						}
						catch (Exception ex)
						{
							this.mqttConnectionOptions = null;
							this.logNet?.WriteError( ToString( ), $"用于上传远程[{this.edgeResources.EdgeSettings.UploadInfo.MqttIpAddress}] MQTT客户端创建失败，无法上传MQTT数据！" + ex.Message );
						}
					}
				}
				if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer2)
				{
					if (this.mqttConnectionOptions2 != null)
					{
						try
						{
							if (hslMqttClient2 == null)          // 如果为空，就创建连接，注意，hslMqttClient2只在上传数据的线程里进行连接的操作
							{
								MqttClient mqtt = new MqttClient( mqttConnectionOptions2 );
								mqtt.OnNetworkError += ( object sender, EventArgs e ) => { if (sender is MqttClient client) { client.ConnectClose( ); } return; };
								OperateResult connect = mqtt.ConnectServer( );
								if (connect.IsSuccess) hslMqttClient2 = mqtt;
								else throw new Exception( connect.Message );

								if (this.edgeResources.EdgeSettings.UploadInfo.MqttType2 == UploadInfoConfig.MqttTypeJetLinks)
								{
									mqtt.OnMqttMessageReceived += Mqtt_OnMqttMessageReceived2;
									// 订阅所有设备相关的主题
									this.devicesCollection.MqttSubscrib( mqtt, this.edgeResources.EdgeSettings.UploadInfo.MqttType2 );
								}
								else if (this.edgeResources.EdgeSettings.UploadInfo.MqttType2 == UploadInfoConfig.MqttTypeNormal)
								{
									mqtt.OnMqttMessageReceived += Mqtt_OnMqttMessageReceived2;
									mqtt.SubscribeMessage( new string[]{
										this.edgeResources.EdgeSettings.ServerInfo.DeviceName + "/WriteData",
										this.edgeResources.EdgeSettings.ServerInfo.DeviceName + "/WriteDataArray" } );
								}

								// 在第一次连接上服务器的时候，当配置了标签点位变化上传的时候，也进行发布一次数据信息
								if (this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 2)
								{
									this.devicesCollection.RaiseValueChangeEvent( );
								}
							}
							if (hslMqttClient2 != null)
							{
								bool dataChangePublish = false;
								if (this.edgeResources.EdgeSettings.UploadInfo.UploadWhenTimedTriggers > 0)
								{
									if ((DateTime.Now - mqttUploadTime2).TotalMilliseconds > this.edgeResources.EdgeSettings.UploadInfo.UploadWhenTimedTriggers)
									{
										dataChangePublish = true;
										mqttUploadTime2 = DateTime.Now;
									}
								}
								if (this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 1) // 每个设备一个主题信息
								{
									OperateResult publish = hslMqttClient2.PublishMessage( new MqttApplicationMessage( )
									{
										Topic = "__status",
										Payload = Encoding.UTF8.GetBytes( serverData.DeviceJsonTemp ),
										QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
										Retain = this.edgeResources.EdgeSettings.UploadInfo.MqttRetain2,
									} );
									if (!publish.IsSuccess) throw new Exception( publish.Message );

									this.devicesCollection.UploadByDevice( this.hslMqttClient2, dataChangePublish );
								}
								else if (this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 0) // 每个网关一个主题信息
								{
									this.devicesCollection.UploadByEdge( this.hslMqttClient2, serverData.DeviceJsonClone, dataChangePublish, this.edgeResources.EdgeSettings.UploadInfo.MqttType2 );
								}
								else if (this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 2) // 数据标签单主题
								{
									if (this.edgeResources.EdgeSettings.UploadInfo.UploadWhenDataChange) // 变化上传的点位
									{
										if (dataChangePublish) // 如果多久不上传，必然上传一次
										{
											this.devicesCollection.RaiseValueChangeEvent( );
										}
									}
									else
									{
										this.devicesCollection.RaiseValueChangeEvent( ); // 没有开启变化，仍然是定时推送
									}
								}
							}
						}
						catch (Exception ex)
						{
							hslMqttClient2?.ConnectClose( );
							hslMqttClient2 = null;
							this.logNet?.WriteError( this.ToString( ) + " MQTT2上传失败：" + ex.Message );
							HslTechnologyHelper.Sleep( 1000 );
						}
					}
					else if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer2)
					{
						try
						{
							this.mqttConnectionOptions2 = this.edgeResources.EdgeSettings.CreateHslMqttClientOption2( );
						}
						catch (Exception ex)
						{
							this.mqttConnectionOptions2 = null;
							this.logNet?.WriteError( ToString( ), $"用于上传远程[{this.edgeResources.EdgeSettings.UploadInfo.MqttIpAddress2}] MQTT2客户端创建失败，无法上传MQTT数据！" + ex.Message );
						}
					}
				}
			}
		}

		private async void Mqtt_OnMqttMessageReceived( MqttClient client, MqttApplicationMessage message )
		{
			// 收到服务器发布的消息
			string topic = message.Topic;
			byte[] payload = message.Payload;

			if (this.edgeResources.EdgeSettings.UploadInfo.MqttType == UploadInfoConfig.MqttTypeJetLinks)
			{
				await MqttJetLinks.Mqtt_OnMqttMessageReceived( this, client, topic, payload );
			}
			else if (this.edgeResources.EdgeSettings.UploadInfo.MqttType == UploadInfoConfig.MqttTypeNormal)
			{
				if (topic.EndsWith( "/WriteData" ))
				{
					// 写入单个的数据
					try
					{
						JObject json = JObject.Parse( Encoding.UTF8.GetString( payload ) );
						string data = json["data"].Value<string>( );
						WriteData( null, data, json["value"].Value<string>( ) );
					}
					catch( Exception ex )
					{
						logNet?.WriteError( ToString( ), $"Mqtt WriteData failed, [{Encoding.UTF8.GetString( payload )}] reason: {ex.Message}" );
					}
				}
				else if ( topic.EndsWith( "/WriteDataArray" ))
				{
					// 批量写入多个数据
					try
					{
						JObject json = JObject.Parse( Encoding.UTF8.GetString( payload ) );
						string[] data = json["data"].Values<string>( ).ToArray( );
						string[] value = json["value"].Values<string>( ).ToArray( );
						OperateResult write = WriteDataArray( null, data, value );
					}
					catch (Exception ex)
					{
						logNet?.WriteError( ToString( ), $"Mqtt WriteDataArray failed, [{Encoding.UTF8.GetString( payload )}] reason: {ex.Message}" );
					}
				}
			}
		}

		private async void Mqtt_OnMqttMessageReceived2( MqttClient client, MqttApplicationMessage message )
		{
			// 收到服务器发布的消息
			string topic = message.Topic;
			byte[] payload = message.Payload;
			if (this.edgeResources.EdgeSettings.UploadInfo.MqttType2 == UploadInfoConfig.MqttTypeJetLinks)
			{
				await MqttJetLinks.Mqtt_OnMqttMessageReceived( this, client, topic, payload );
			}
			else if (this.edgeResources.EdgeSettings.UploadInfo.MqttType2 == UploadInfoConfig.MqttTypeNormal)
			{
				if (topic.EndsWith( "/WriteData" ))
				{
					// 写入单个的数据
					try
					{
						JObject json = JObject.Parse( Encoding.UTF8.GetString( payload ) );
						string data = json["data"].Value<string>( );
						WriteData( null, data, json["value"].Value<string>( ) );
					}
					catch (Exception ex)
					{
						logNet?.WriteError( ToString( ), $"Mqtt2 WriteData failed, [{Encoding.UTF8.GetString( payload )}] reason: {ex.Message}" );
					}
				}
				else if (topic.EndsWith( "/WriteDataArray" ))
				{
					// 批量写入多个数据
					try
					{
						JObject json = JObject.Parse( Encoding.UTF8.GetString( payload ) );
						string[] data = json["data"].Values<string>( ).ToArray( );
						string[] value = json["value"].Values<string>( ).ToArray( );
						OperateResult write = WriteDataArray( null, data, value );
					}
					catch (Exception ex)
					{
						logNet?.WriteError( ToString( ), $"Mqtt2 WriteDataArray failed, [{Encoding.UTF8.GetString( payload )}] reason: {ex.Message}" );
					}
				}
			}
		}

		private void DevicesCollection_OnDeviceValueChanged( DeviceCoreBase device, DeviceSingleAddressLabel addressLabel, JToken value )
		{
			// 按标签时，有个问题，一个结构体的数据，是按照整个结构体变化进行数据推送？还是按照里面一个一个子数据的变化进行推送
			// 此处先过滤结构体
			if (addressLabel == null) return;
			if (addressLabel.DataType == DataType.Struct || addressLabel.DataType == DataType.Class) return;

			if(this.edgeResources.EdgeSettings.UploadInfo.UploadRemoteMode == 2 && this.edgeResources.EdgeSettings.UploadInfo.UploadWhenDataChange) // 数据变化了，推送值信息
			{
				// 这里不是很容易，因为需要解决的是，网络不通畅的时候，依然不影响网关的采集速度，网关的采集速度也不影响上传速度
				if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer)
				{
					try
					{
						PublishMqttOnDeviceValueChanged( hslMqttClient, this.edgeResources.EdgeSettings.UploadInfo.MqttType, this.edgeResources.EdgeSettings.UploadInfo.MqttRetain, device, addressLabel, value );
					}
					catch (Exception ex)
					{
						hslMqttClient?.ConnectClose( );
						hslMqttClient = null;
						this.logNet?.WriteError( this.ToString( ) + " MQTT上传失败：" + ex.Message );
					}
				}

				if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer2)
				{
					try
					{
						PublishMqttOnDeviceValueChanged( hslMqttClient2, this.edgeResources.EdgeSettings.UploadInfo.MqttType2, this.edgeResources.EdgeSettings.UploadInfo.MqttRetain2, device, addressLabel, value );
					}
					catch (Exception ex)
					{
						hslMqttClient2?.ConnectClose( );
						hslMqttClient2 = null;
						this.logNet?.WriteError( this.ToString( ) + " MQTT2上传失败：" + ex.Message );
					}
				}
			}
		}

		private void PublishMqttOnDeviceValueChanged( MqttClient mqtt, int mqttType, bool retain, DeviceCoreBase device, DeviceSingleAddressLabel addressLabel, JToken value )
		{
			if (mqtt != null)
			{
				if (mqttType == UploadInfoConfig.MqttTypeNormal)
				{
					OperateResult publish = mqtt.PublishMessage( new MqttApplicationMessage( )
					{
						Topic = device.GetMqttTopic( ) + HslTechnologyExtension.DeviceDefaultSplit + addressLabel.Name,
						Payload = Encoding.UTF8.GetBytes( value.ToString( ) ),
						QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
						Retain = retain
					} );
					if (!publish.IsSuccess) throw new Exception( publish.Message );
				}
				else if (mqttType == UploadInfoConfig.MqttTypeJetLinks)
				{
					JObject properties = new JObject( );
					properties[addressLabel.Name] = value;
					JObject json = new JObject( );
					json["deviceId"] = device.Name;
					json["properties"] = properties;
					OperateResult publish = mqtt.PublishMessage( new MqttApplicationMessage( )
					{
						Topic = device.GetMqttTopic( ) + "/properties/report",
						Payload = Encoding.UTF8.GetBytes( json.ToString( ) ),
						QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
						Retain = retain
					} );
					if (!publish.IsSuccess) throw new Exception( publish.Message );
				}
			}
		}

		private void DevicesCollection_OnDeviceStatusChanged( DeviceCoreBase device, bool status )
		{
			if (this.edgeResources.EdgeSettings.UploadInfo.UseRedisServer)
			{
				try
				{
					PublishMqttOnDeviceStatusChanged( hslMqttClient, this.edgeResources.EdgeSettings.UploadInfo.MqttType, this.edgeResources.EdgeSettings.UploadInfo.MqttRetain, device, status );
				}
				catch (Exception ex)
				{
					hslMqttClient?.ConnectClose( );
					hslMqttClient = null;
					this.logNet?.WriteError( this.ToString( ) + " MQTT上传失败：" + ex.Message );
				}
			}

			if (this.edgeResources.EdgeSettings.UploadInfo.UseMqttServer2)
			{
				try
				{
					PublishMqttOnDeviceStatusChanged( hslMqttClient2, this.edgeResources.EdgeSettings.UploadInfo.MqttType2, this.edgeResources.EdgeSettings.UploadInfo.MqttRetain2, device, status );
				}
				catch (Exception ex)
				{
					hslMqttClient2?.ConnectClose( );
					hslMqttClient2 = null;
					this.logNet?.WriteError( this.ToString( ) + " MQTT2上传失败：" + ex.Message );
				}
			}
		}

		private void PublishMqttOnDeviceStatusChanged( MqttClient mqtt, int mqttType, bool retain, DeviceCoreBase device, bool status )
		{
			if (mqtt != null)
			{
				if (mqttType == UploadInfoConfig.MqttTypeNormal)
				{

				}
				else if (mqttType == UploadInfoConfig.MqttTypeJetLinks)
				{
					JObject json = new JObject( );
					json["deviceId"] = device.Name;
					json["timestamp"] = edgeResources.EdgeSettings.UploadInfo.GetTimeStamp( );
					json["messageId"] = HslCommunication.Core.HslHelper.HslRandom.Next( int.MaxValue ).ToString( );

					OperateResult publish = mqtt.PublishMessage( new MqttApplicationMessage( )
					{
						Topic = device.GetMqttTopic( ) + (status ? "/online" : "/offline"),
						Payload = Encoding.UTF8.GetBytes( json.ToString( ) ),
						QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
						Retain = retain
					} );
					if (!publish.IsSuccess) throw new Exception( publish.Message );
				}
			}
		}

		private async void ThreadCheckVersion( )
		{
			int failedCount = 0;
			bool isCloseByCheckNewVersion = false;
			HslTechnologyHelper.Sleep( 1000 );  // delay

			if (string.IsNullOrEmpty( this.edgeResources.EdgeSettings.ServerInfo.RemoteServerIp ))
			{
				this.logNet?.WriteInfo( "检测服务器更新的不启动！" );
			}
			else
			{
				this.logNet?.WriteInfo( "检测服务器更新的线程启动！" );
				NetSimplifyClient client = new NetSimplifyClient( this.edgeResources.EdgeSettings.ServerInfo.RemoteServerIp, 10153 );
				client.ConnectTimeOut = 5000;
				while (this.IsRunning)
				{
					OperateResult<string> read = await client.ReadFromServerAsync( 1, this.edgeResources.EdgeSettings.Version.ToString( ) );
					if (!read.IsSuccess)
					{
						failedCount++;
						this.logNet?.WriteError( "获取服务器的版本号失败！" );
						HslTechnologyHelper.Sleep( 30_000 + failedCount > 2 ? failedCount * 60_000 : 0 );
						continue;
					}

					SystemVersion version = new SystemVersion( read.Content );
					if (version > this.edgeResources.EdgeSettings.Version)
					{
						this.logNet?.WriteInfo( "检测到服务器新的版本的软件！" + version.ToString( ) );
						// 需要更新了
						IntegrationFileClient fileClient = new IntegrationFileClient( this.edgeResources.EdgeSettings.ServerInfo.RemoteServerIp, 10152 );
						fileClient.Token = Guid.Parse( "32be1c64-5748-4bd1-b3d7-3e3af368f001" );

						// 获取所有的更新文件
#if NET461
						string factory = "Windows";
#else
						string factory = "Linux";
#endif
						OperateResult<GroupFileItem[]> result = await fileClient.DownloadPathFileNamesAsync( factory, "", "" );
						if (!read.IsSuccess)
						{
							this.logNet?.WriteError( "获取服务器的更新的文件列表失败！" );
							HslTechnologyHelper.Sleep( 5_000 );
							continue;
						}

						ClearUpdateServerPath( );
						GroupFileItem[] files = result.Content;
						for (int i = 0; i < files.Length; i++)
						{
							DateTime fileStartTime = DateTime.Now;
							OperateResult download = await fileClient.DownloadFileAsync( files[i].FileName, factory, "", "", null, Path.Combine( fileServer.FilesDirectoryPath, files[i].FileName ) );
							if (!download.IsSuccess)
							{
								this.logNet?.WriteError( $"当前的文件下载失败：{files[i].FileName}  跳过本次更新，准备重新检测" );
								HslTechnologyHelper.Sleep( 5_000 );
								continue;
							}
							else
							{
								this.logNet?.WriteInfo( $"当前的文件下载成功 {files[i].FileName} 大小：{SoftBasic.GetSizeDescription( files[i].FileSize )}  用时：{SoftBasic.GetTimeSpanDescription( DateTime.Now - fileStartTime )}" );
							}
						}

						isCloseByCheckNewVersion = true;
						break;
					}
					else
					{
						int waitSleep = 30_000 + this.random.Next( 100_000 ); //+ this.random.Next( 60_000 );
						this.logNet?.WriteInfo( "没有检测到服务器的！" );
						HslTechnologyHelper.Sleep( waitSleep );
					}
				}

				if (isCloseByCheckNewVersion)
				{
					// 所有的文件下载完成，准备更新重启操作
					this.isNewUpdateServer = true;
					this.Close( );
					this.ReStarted = true;
				}
			}
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"EdgeServices[{Port}]";

		#endregion

		#region Private Member

		/// <summary>
		/// 当前网关的资源信息
		/// </summary>
		private EdgeDeviceResources edgeResources;                                         // 
		private AdvancedFileServer fileServer;                                             // 用于远程更新的服务器实现
		private int port = 521;                                                            // 服务器的端口信息
		private ILogNet logNet;                                                            // 日志信息
		private EdgeLog edgeLog;                                                           // 中间日志信息
		/// <summary>
		/// 0：刚启动未运行，1：运行中，2：系统结束运行
		/// </summary>
		private int isRunning = 0;                                                         // 服务器是否处在运行之中，0：刚启动未运行，1：运行中，2：系统结束运行
		private bool isNewUpdateServer = false;                                            // 在关闭重启后，是否需要更新服务器的程序
		private DevicesCollection devicesCollection;                                       // 设备的对象集合
		private NetworkAlienClient dtuServer;                                              // DTU的服务器数据信息
		private Process process;                                                           // 当前的进程信息

		private MqttClient hslMqttClient;                                                  // 基于Hsl的Mqtt的客户端
		private MqttClient hslMqttClient2;                                                 // 基于Hsl的Mqtt的客户端2
		private MqttConnectionOptions mqttConnectionOptions;                               // 基于Hsl的mqtt的客户端的配置信息
		private MqttConnectionOptions mqttConnectionOptions2;                              // 基于Hsl的mqtt的客户端的配置信息
		private RedisClient redisClient;                                                   // Redis客户端的对象，用来上传服务器的信息
		private DeviceData serverData;                                                     // 服务器的数据信息
		private Thread uploadServerThread;                                                 // 上传服务器的线程
		private int uploadCycleCount = 0;                                                  // 上传服务器的循环计数
		private Random random;                                                             // 随机数
		private SharpList<float> ramUseHistory;                                            // 内存使用的时候的短暂的缓存数据
		private SharpList<float> threadUseHistory;                                         // 线程使用的时候的短暂的缓存数据

        #endregion

        public bool TZXXCheckOK = false;
        public int RunCount = -1;
    }
}
