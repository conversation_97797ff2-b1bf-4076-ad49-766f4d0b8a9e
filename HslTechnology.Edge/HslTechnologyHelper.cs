using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Node.Alarm;
using HslCommunication.BasicFramework;
using HslCommunication;
using HslCommunication.LogNet;
using System.Threading;

namespace HslTechnology.Edge
{
	/// <summary>
	/// 边缘系统相关的静态辅助办法<br />
	/// Static assistance related to the edge system
	/// </summary>
	public class HslTechnologyHelper
	{
		/// <summary>
		/// 将XML的标签信息转换为JSON格式的字符串
		/// </summary>
		/// <param name="element">XML元素信息</param>
		/// <returns>json格式字符串</returns>
		public static string ConvertXmlToJsonString( XElement element, Newtonsoft.Json.Formatting formatting )
		{
			System.Xml.XmlDocument xmlDocument = new System.Xml.XmlDocument( );
			xmlDocument.Load( element.CreateReader( ) );
			return Newtonsoft.Json.JsonConvert.SerializeXmlNode( xmlDocument, formatting );
		}

		/// <summary>
		/// 将JSON字符串转换为XML标签信息的元素
		/// </summary>
		/// <param name="json">JSON格式字符串</param>
		/// <returns>XMl元素信息</returns>
		public static string ConvertJsonToXmlString( string json )
		{
			System.Xml.XmlDocument xmlDocument = Newtonsoft.Json.JsonConvert.DeserializeXmlNode( json );
			return xmlDocument.InnerXml;
		}

		/// <summary>
		/// 将指定描述的枚举字符串转变为枚举对象本身
		/// </summary>
		/// <typeparam name="T">枚举的类型</typeparam>
		/// <param name="desc">描述信息</param>
		/// <returns>枚举对象</returns>
		public static T GetEnumByString<T>( string desc ) where T : Enum => (T)Enum.Parse( typeof( T ), desc );

		/// <summary>
		/// 加密当前的数据信息，对最终用户进行隐藏
		/// </summary>
		/// <param name="content">字符串的数据信息</param>
		/// <returns>加密之后的结果信息</returns>
		internal static string Encrypted( string content )
		{
			return SoftSecurity.MD5Encrypt( content, "bzaohG0L" );
		}

		/// <summary>
		/// 解密当前的数据信息，解析出真实的数据信息，如果解析失败，就返回空字符串数据信息
		/// </summary>
		/// <param name="content">等待解密的数据信息</param>
		/// <returns>解密后真实的值</returns>
		internal static string Decrypted( string content )
		{
			try
			{
				return SoftSecurity.MD5Decrypt( content, "bzaohG0L" );
			}
			catch
			{
				return string.Empty;
			}
		}

		/// <summary>
		/// 休眠指定的时间，如果发生异常，就记录消息到系统日志里
		/// </summary>
		/// <param name="millisecond">毫秒数</param>
		public static void Sleep( int millisecond )
		{
			try
			{
				Thread.Sleep( millisecond );
			}
			catch
			{

			}
		}

		/// <summary>
		/// 根据时间差获取该时间差的描述文本
		/// </summary>
		/// <param name="ts">时间差信息</param>
		/// <returns>描述文本</returns>
		public static string GetTimeSpanText( TimeSpan ts )
		{
			if (ts.TotalSeconds < 60) return ts.Seconds.ToString( ) + " S";
			else if (ts.TotalMinutes < 60) return $"{ts.Minutes} M {ts.Seconds:D2} S";
			else if (ts.TotalHours < 24) return $"{ts.Hours} H {ts.Minutes:D2} M {ts.Seconds:D2} S";
			else return $"{ts.Days} D {ts.Hours:D2} H {ts.Minutes:D2} M";
		}

		/// <summary>
		/// 获取当前报警等级的所有枚举对象，外加一个"全部"的选项。
		/// </summary>
		/// <returns>字符串信息</returns>
		public static string[] GetAlarmDegreeWithAll( )
		{
			AlarmDegree[] degrees = SoftBasic.GetEnumValues<AlarmDegree>( );
			string[] tmp = new string[degrees.Length + 1];
			tmp[0] = "全部";
			for (int i = 1; i < tmp.Length; i++)
			{
				tmp[i] = degrees[i - 1].ToString( );
			}
			return tmp;
		}

		/// <summary>
		/// 当前的网关系统和远程标准服务器系统的时间差
		/// </summary>
		public static double DateTimeDelta = 0d;

		/// <summary>
		/// 获取当前的系统的时间
		/// </summary>
		/// <returns>当前系统的时间信息</returns>
		public static DateTime GetDateTimeNow( )
		{
			return DateTime.Now.AddSeconds( DateTimeDelta );
		}

		/// <summary>
		/// 根据当前的路径信息，重新组合成一个完整的路径信息
		/// </summary>
		/// <param name="nodePath">数据数组</param>
		/// <param name="isPath">是否路径</param>
		/// <returns>完整路径信息</returns>
		public static string CombineEdgePath( string[] nodePath, bool isPath )
		{
			if (nodePath == null || nodePath.Length == 0) return string.Empty;
			StringBuilder sb = new StringBuilder( );
			for (int i = 0; i < nodePath.Length; i++)
			{
				sb.Append( nodePath[i] );
				if (i < nodePath.Length - 1)
					sb.Append( HslTechnologyExtension.DeviceDefaultSplit );
			}
			if(isPath) sb.Append( HslTechnologyExtension.DeviceDefaultSplit );
			return sb.ToString( );
		}

		public static sbyte[] GetSByteArrayFrom( byte[] value )
		{
			if (value == null) return null;
			sbyte[] array = new sbyte[value.Length];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = (sbyte)value[i];
			}
			return array;
		}

		public static byte[] GetByteArrayFrom( sbyte[] value )
		{
			if (value == null) return null;
			byte[] array = new byte[value.Length];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = (byte)value[i];
			}
			return array;
		}

		public static OperateResult<sbyte[]> ConvertFromByte(OperateResult<byte[]> read )
		{
			if (!read.IsSuccess) return OperateResult.CreateFailedResult<sbyte[]>( read );
			return OperateResult.CreateSuccessResult( GetSByteArrayFrom( read.Content ) );
		}

		public static List<string> GroupNodeCategorys = new List<string>( ) { "节点信息", "设备信息", "服务器信息", "串口信息",
			"网络层信息", "请求信息", "PLC信息", "Modbus信息", "MRPC信息", "DTU信息", "报警信息", "数据变换" };
	}
}
