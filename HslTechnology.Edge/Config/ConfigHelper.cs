using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 配置类对象的辅助静态方法
	/// </summary>
	public static class ConfigHelper
	{
		/// <summary>
		/// 从JToken中读取相关的配置参数信息，如果不存在，就返回默认的值<br />
		/// Read the relevant configuration parameter information from JToken, if it does not exist, return the default value
		/// </summary>
		/// <typeparam name="T">值的类型</typeparam>
		/// <param name="token">JToken对象</param>
		/// <param name="name">参数的名称</param>
		/// <param name="defaultValue">默认的值</param>
		/// <returns>最终返回的值，如果不存在，就返回默认值</returns>
		public static T GetConfig<T>( JToken token, string name, T defaultValue )
		{
			JToken jToken = token[name];
			if (jToken == null) return defaultValue;
			return jToken.Value<T>( );
		}

		public static List<T> GetConfigArray<T>( JToken token, string name )
		{
			try
			{
				JArray jArray = (JArray)token[name];
				if (jArray == null) return new List<T>( );
				return jArray.ToObject<List<T>>( );
			}
			catch
			{
				return new List<T>( );
			}
		}
	}
}
