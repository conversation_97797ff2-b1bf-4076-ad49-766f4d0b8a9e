using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Device.Base;
using Newtonsoft.Json.Linq;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 主备模式下，作为备份的相关参数信息
	/// </summary>
	public class StandbyInfoConfig
	{
		/// <summary>
		/// 是否以备份服务器的模式进行运行
		/// </summary>
		public bool StandbyEnable { get; set; }

		/// <summary>
		/// 跟踪的主服务器的IP地址
		/// </summary>
		public string MainServerIp { get; set; } = "127.0.0.1";

		/// <summary>
		/// 跟踪的主服务器的PORT端口信息
		/// </summary>
		public int MainServerPort { get; set; } = 521;

		/// <summary>
		/// 跟踪的主服务器的账户名信息
		/// </summary>
		public string MainServerAccount { get; set; } = "admin";

		/// <summary>
		/// 跟踪的主服务器的密码信息
		/// </summary>
		public string MainServerPassword { get; set; }

		/// <summary>
		/// 是否同步主服务器的系统参数信息
		/// </summary>
		public bool SyncEdgeParameters { get; set; } = true;

		/// <summary>
		/// 是否同步主服务器的配置的设备参数信息
		/// </summary>
		public bool SyncDeviceParameters { get; set; } = true;

		/// <summary>
		/// 获取或设置和边缘网关主服务器失联多久再启动备用服务器，单位秒，默认60秒
		/// </summary>
		public int CheckTimeoutInSeconds { get; set; } = 60;

		/// <summary>
		/// 获取服务器的连接对象
		/// </summary>
		/// <returns>连接对象</returns>
		public MqttSyncClient GetMainServerMqttSyncClient( )
		{
			MqttSyncClient syncClient = new MqttSyncClient( new MqttConnectionOptions( )
			{
				IpAddress      = MainServerIp,
				Port           = MainServerPort,
				Credentials    = new MqttCredential( MainServerAccount, MainServerPassword ),
				UseRSAProvider = true,
			} );
			syncClient.ConnectTimeOut = 2000;
			syncClient.SetPersistentConnection( );
			return syncClient;
		}

		/// <summary>
		/// 将网关作为备用服务器执行，请求服务器
		/// </summary>
		/// <param name="settingsServer">网关配置信息</param>
		/// <param name="logNet">网关的日志对象</param>
		/// <returns>备用服务器的任务对象</returns>
		public async Task RunAsStandby( SettingsServer settingsServer, ILogNet logNet )
		{
			logNet.WriteInfo( $"当前已经配置为备用边缘网关模式。" );
			MqttSyncClient standbyServer = GetMainServerMqttSyncClient( );
			standbyServer.ConnectServer( );
			int checkTimeout = this.CheckTimeoutInSeconds;
			// 同步主服务器的配置信息
			while (true)
			{
				if (!this.SyncDeviceParameters && !this.SyncEdgeParameters) break;
				HslTechnologyHelper.Sleep( 1000 );

				bool isAllSuccess = true;
				if (this.SyncEdgeParameters)
				{
					OperateResult<JObject> readEdgeParameters = await standbyServer.ReadRpcAsync<JObject>( "Admin/ServerSettingsRequest", "{}" );
					if (!readEdgeParameters.IsSuccess)
					{
						isAllSuccess = false;
						logNet.WriteError( $"请求主边缘网关的参数信息失败！" + readEdgeParameters.Message );
					}
					else
					{
						// 如果请求成功，覆盖本地的参数
						settingsServer.LoadSettingsByJson( readEdgeParameters.Content.ToString( ) );
						logNet.WriteInfo( $"请求主边缘网关的参数信息成功！" );
					}
				}

				if (this.SyncDeviceParameters)
				{
					OperateResult<string> readDeviceParameters = await standbyServer.ReadRpcAsync<string>( "Admin/XmlSettingsRequest", "{}" );
					if (!readDeviceParameters.IsSuccess)
					{
						isAllSuccess = false;
						logNet.WriteError( $"请求主边缘网关的设备配置信息失败！" + readDeviceParameters.Message );
					}
					else
					{
						// 如果请求成功，覆盖本地的参数
						settingsServer.SetXmlSettings( readDeviceParameters.Content );
						logNet.WriteInfo( $"请求主边缘网关的设备配置信息成功！" );
					}
				}

				if (isAllSuccess)
				{
					break;
				}
			}

			// 请求边缘网关主服务器，监视运行状态
			int failedCount = 0;                      // 连续失败次数
			HslTimerTick hslTimerTick = new HslTimerTick( );
			while (true)
			{
				HslTechnologyHelper.Sleep( 20 );
				if (!hslTimerTick.IsTickHappen( )) continue; // 还没到请求时间

				OperateResult<string> readDeviceParameters = await standbyServer.ReadRpcAsync<string>( "Admin/StandbyEdgeCheck", new { edgeName = settingsServer.ServerInfo.DeviceName } );
				if (!readDeviceParameters.IsSuccess)
				{
					failedCount++;
					logNet.WriteError( $"检测主边缘网关的运行信息失败，持续[{failedCount}]秒！" + readDeviceParameters.Message );
					if (failedCount > checkTimeout)
					{
						// 在启动之前再保存参数信息
						settingsServer.SaveSettings( );
						logNet.WriteError( $"检测主边缘网关的运行信息失败超时，现在启用备用服务器并切换为主网关。" + readDeviceParameters.Message );
						break;
					}
				}
				else
				{
					failedCount = 0;
					logNet.WriteInfo( $"检测主边缘网关的运行信息成功！" );
				}
			}

		}

	}
}
