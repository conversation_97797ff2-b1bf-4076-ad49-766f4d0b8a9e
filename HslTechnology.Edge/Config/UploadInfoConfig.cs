using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Node;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 上传服务器这部分的配置信息
	/// </summary>
	public class UploadInfoConfig
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public UploadInfoConfig( )
		{
			this.RedisPort          = 6379;
			this.RedisDBNumber      = 0;
			this.UploadTimeInterval = 1000;
			this.MqttPort           = 1883;
			this.MqttClientID       = null;
		}

		/// <summary>
		/// 是否启动Redis的服务器存储信息
		/// </summary>
		public bool UseRedisServer { get; set; }

		/// <summary>
		/// 整个服务器对应的Redis的IP地址信息
		/// </summary>
		public string RedisIpAddress { get; set; }

		/// <summary>
		/// 整个服务器对应的Redis的端口号信息
		/// </summary>
		public int RedisPort { get; set; }

		/// <summary>
		/// Redis服务器的密码，如果没有的话，就设置为String.Empty
		/// </summary>
		public string RedisPassword { get; set; }

		/// <summary>
		/// Redis相关的DB块信息
		/// </summary>
		public int RedisDBNumber { get; set; }

		/// <summary>
		/// 是否启动远程Mqtt服务器
		/// </summary>
		public bool UseMqttServer { get; set; }

		/// <summary>
		/// 上传MQTT的方式，默认0是普通的MQTT， 1表示 JetLinks
		/// </summary>
		public int MqttType { get; set; }

		/// <summary>
		/// 远程Mqtt服务器的Ip地址
		/// </summary>
		public string MqttIpAddress { get; set; }

		/// <summary>
		/// Mqtt的客户端ID信息，如果为空，则使用网关自身的ID信息
		/// </summary>
		public string MqttClientID { get; set; }

		/// <summary>
		/// 远程Mqtt服务器的端口地址
		/// </summary>
		public int MqttPort { get; set; }

		/// <summary>
		/// 远程Mqtt的用户名，默认为空
		/// </summary>
		public string MqttUserName { get; set; }

		/// <summary>
		/// 远程Mqtt的密码，默认为空
		/// </summary>
		public string MqttPassword { get; set; }

		/// <summary>
		/// 发布远程MQTT消息的时候，是否使用retain消息驻留操作
		/// </summary>
		public bool MqttRetain { get; set; } = true;

		/// <summary>
		/// 是否启用安全认证
		/// </summary>
		public bool UseSSL { get; set; } = false;

		/// <summary>
		/// 证书名称
		/// </summary>
		public string MqttCAFileName { get; set; }

		/// <summary>
		/// 证书的内容
		/// </summary>
		public string MqttCAFileContent { get; set; }

		#region Mqtt2

		/// <summary>
		/// 是否启动远程Mqtt服务器
		/// </summary>
		public bool UseMqttServer2 { get; set; }

		/// <summary>
		/// 上传MQTT的方式，默认0是普通的MQTT， 1表示 JetLinks
		/// </summary>
		public int MqttType2 { get; set; }

		/// <summary>
		/// 远程Mqtt服务器的Ip地址
		/// </summary>
		public string MqttIpAddress2 { get; set; }

		/// <summary>
		/// Mqtt的客户端ID信息，如果为空，则使用网关自身的ID信息
		/// </summary>
		public string MqttClientID2 { get; set; }

		/// <summary>
		/// 远程Mqtt服务器的端口地址
		/// </summary>
		public int MqttPort2 { get; set; }

		/// <summary>
		/// 远程Mqtt的用户名，默认为空
		/// </summary>
		public string MqttUserName2 { get; set; }

		/// <summary>
		/// 远程Mqtt的密码，默认为空
		/// </summary>
		public string MqttPassword2 { get; set; }

		/// <summary>
		/// 发布远程MQTT消息的时候，是否使用retain消息驻留操作
		/// </summary>
		public bool MqttRetain2 { get; set; } = true;

		/// <summary>
		/// 是否启用安全认证
		/// </summary>
		public bool UseSSL2 { get; set; } = false;

		/// <summary>
		/// 证书名称
		/// </summary>
		public string MqttCAFileName2 { get; set; }

		/// <summary>
		/// 证书的内容
		/// </summary>
		public string MqttCAFileContent2 { get; set; }

		#endregion

		/// <summary>
		/// 数据上传的方式，0: 单网关上传，1: 单设备上传，2: 数据标签单主题
		/// </summary>
		public int UploadRemoteMode { get; set; }

		/// <summary>
		/// 获取或设置是否当数据变化的时候才进行数据发布的操作
		/// </summary>
		public bool UploadWhenDataChange { get; set; }

		/// <summary>
		/// 当设置了数据变化上传的时候，强制隔多久必须上传一次数据
		/// </summary>
		public int UploadWhenTimedTriggers { get; set; } = -1;

		/// <summary>
		/// 是否上传报警信息
		/// </summary>
		public bool UploadAlarm { get; set; } = true;

		/// <summary>
		/// 获取或设置上传服务器的时间
		/// </summary>
		public int UploadTimeInterval { get; set; }

		/// <summary>
		/// 是否开启数据的订阅操作
		/// </summary>
		public bool EnableSubscription { get; set; }

		public long GetTimeStamp( )
		{
			return (long)(DateTime.Now.AddHours( -8 ) - new DateTime( 1970, 1, 1 )).TotalMilliseconds;
		}

		public const int UploadEveryEdge = 0;
		public const int UploadEveryDevice = 1;
		public const int UploadEveryDeviceTag = 2;

		public const int MqttTypeNormal = 0;
		public const int MqttTypeJetLinks = 1;
	}
}
