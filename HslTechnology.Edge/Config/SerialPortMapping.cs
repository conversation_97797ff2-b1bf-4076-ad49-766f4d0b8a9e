using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 串口配置的映射关系
	/// </summary>
	public class SerialPortMapping
	{
		/// <summary>
		/// 原始的串口端口
		/// </summary>
		public string SerialPort { get; set; }

		/// <summary>
		/// 映射的串口信息，如果映射之后的串口名称为空，则表示隐藏该串口信息
		/// </summary>
		public string SerialMapping { get; set; }
	}
}
