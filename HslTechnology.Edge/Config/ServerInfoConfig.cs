using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.MQTT;
using HslTechnology.Edge.Reflection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 服务器的基本信息的配置
	/// </summary>
	public class ServerInfoConfig
	{
		#region Constructor

		/// <inheritdoc cref="Node.GroupNode.GroupNode( )"/>
		public ServerInfoConfig( )
		{
			// 设备的节点信息
			byte[] buffer = new byte[6];
			Random random = new Random( );
			random.NextBytes( buffer );
			DeviceName = "Edge:" + buffer.ToHexString( );
			ServerPort = 521;
			UserName = "admin";
			Password = "123456";
			MqttWildcard = true;
		}

		#endregion

		/// <summary>
		/// 获取或设置设备的名称信息
		/// </summary>
		public string DeviceName { get; set; }

		/// <summary>
		/// 当前设备系统的唯一ID信息
		/// </summary>
		public string UniqueId { get; set; }

		/// <summary>
		/// 配置服务器的端口号信息
		/// </summary>
		public int ServerPort { get; set; }

		/// <summary>
		/// 用户名
		/// </summary>
		public string UserName { get; set; }

		/// <summary>
		/// 密码
		/// </summary>
		/// <remarks>
		/// RY1HiZvxIuZku1zBekWv8Fp6juJe3bxvPbPEzkoB5184czt9iNK+7Yg1J/QwzHZf4UBi0o6VcAYYGWY12BC2oR2OWCVgQ2zyt5hGzlShtKih8x5FmzhLBfwOg9LFOPh7nVDFFo5x+iagjHKAFZE43rdXJq9YqB4NgQAVwoLzMpM=
		/// </remarks>
		[JsonConverter( typeof( JsonPasswordConverter ) )]
		public string Password { get; set; }

		public List<UserAccount> Accounts { get; set; }

		/// <summary>
		/// 是否启用MQTT的通配符功能
		/// </summary>
		public bool MqttWildcard { get; set; }

		/// <summary>
		/// 获取或设置读取读取失败的时候，是否重置空值
		/// </summary>
		public bool ResetNull { get; set; }

		/// <summary>
		/// 当前网关的安装位置信息，需要手动设置信息
		/// </summary>
		public string Location { get; set; }

		/// <summary>
		/// 当前网关的经纬坐标信息，如果网关可以获取到硬件的坐标信息的话，就输入该值
		/// </summary>
		public string Coordinate { get; } = "Not Supported";

		/// <summary>
		/// 用于检测更新的远程服务器的IP地址信息
		/// </summary>
		public string RemoteServerIp { get; set; }

		public MqttCredential[] CreateHttpAccount( )
		{
			List<MqttCredential> mqttCredentials = new List<MqttCredential>( );
			mqttCredentials.Add( new MqttCredential( UserName, Password ) );
			if (this.Accounts != null)
			{
				for (int i = 0; i < this.Accounts.Count; i++)
				{
					UserAccount account = this.Accounts[i];
					if (account == null) continue;
					
					if (account.LoginEnable)
					{
						mqttCredentials.Add( new MqttCredential( account.UserName, account.Password ) );
					}
				}
			}
			return mqttCredentials.ToArray( );
		}

		/// <summary>
		/// 检测当前的账户是否合法，可以进行登录操作<br />
		/// Check whether the current account is legal, you can log in
		/// </summary>
		/// <param name="name">用户名信息</param>
		/// <param name="password">密码</param>
		/// <returns>账户验证是否通过</returns>
		public bool CheckAccount( string name, string password )
		{
			if (name == this.UserName && password == this.Password) return true;
			if (this.Accounts == null) return false;
			if (this.Accounts.Count == 0) return false;
			for (int i = 0; i < this.Accounts.Count; i++)
			{
				UserAccount account = this.Accounts[i];
				if (account == null) continue;
				if (account.UserName == name && account.Password == password)
				{
					if (account.LoginEnable) return true;
					else return false;
				}
			}
			return false;
		}

		/// <summary>
		/// 检查指定的账户是不是管理员账户，只有管理员的账户支持配置设备信息，修改设备的配置信息
		/// </summary>
		/// <param name="name">等待检查的用户名</param>
		/// <returns>是否是管理员的账户</returns>
		public bool CheckAdminAccount( string name )
		{
			return name == this.UserName;
		}

		private bool CheckAccountPermisson( string name, Func<UserAccount, bool> condition )
		{
			if (this.Accounts == null) return false;
			if (this.Accounts.Count == 0) return false;
			for (int i = 0; i < this.Accounts.Count; i++)
			{
				UserAccount account = this.Accounts[i];
				if (account == null) continue;
				if (account.UserName == name)
				{
					if (account.LoginEnable && condition( account )) return true;
					else return false;
				}
			}
			return false;
		}

		public bool CheckAccountServerSettings( string name )
		{
			if (name == this.UserName) return true;

			return CheckAccountPermisson( name, m => m.ModifyEdgeSetting );
		}

		public bool CheckAccountDeviceSettings( string name )
		{
			if (name == this.UserName) return true;

			return CheckAccountPermisson( name, m => m.ModifyDeviceSetting );
		}

		public bool CheckAccountServerCloseRestart( string name )
		{
			if (name == this.UserName) return true;

			return CheckAccountPermisson( name, m => m.CloseRestartEdge );
		}

		public bool CheckAccountLogin( string name )
		{
			if (name == this.UserName) return true;

			return CheckAccountPermisson( name, m => true );
		}

		/// <summary>
		/// 检查当前的账户是否有写入的操作权限
		/// </summary>
		/// <param name="name">账户名</param>
		/// <returns>写入的操作权限</returns>
		public bool CheckAccountWritePermission( string name )
		{
			if (name == this.UserName) return true;

			return CheckAccountPermisson( name, m => m.WriteDeviceData );
		}
		public bool CheckAccountCallMethodPermission( string name )
		{
			if (name == this.UserName) return true;

			return CheckAccountPermisson( name, m => m.CallDeviceMethod );
		}
		// public bool AutoRestart { get; set; }
	}

	public class UserAccount
	{
		public string UserName { get; set; } = string.Empty;

		/// <summary>
		/// 密码，存储的时间加密处理
		/// </summary>
		/// <remarks>
		/// RY1HiZvxIuZku1zBekWv8Fp6juJe3bxvPbPEzkoB5184czt9iNK+7Yg1J/QwzHZf4UBi0o6VcAYYGWY12BC2oR2OWCVgQ2zyt5hGzlShtKih8x5FmzhLBfwOg9LFOPh7nVDFFo5x+iagjHKAFZE43rdXJq9YqB4NgQAVwoLzMpM=
		/// </remarks>
		[JsonConverter( typeof( JsonPasswordConverter ) )]
		public string Password { get; set; } = "123456";

		public bool LoginEnable { get; set; } = true;

		public bool ModifyEdgeSetting { get; set; }

		public bool CloseRestartEdge { get; set; }

		public bool ModifyDeviceSetting { get; set; }

		public bool WriteDeviceData { get; set; }

		public bool CallDeviceMethod { get; set; }
	}

}
