using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 服务器的串口端
	/// </summary>
	public class PortMappingConfig
	{
		public PortMappingConfig( )
		{
			this.PortMappings = new List<SerialPortMapping>( );
			//try
			//{
			//	string[] ports = System.IO.Ports.SerialPort.GetPortNames( );
			//	for (int i = 0; i < ports.Length; i++)
			//	{
			//		SerialPortMapping serialPort = new SerialPortMapping( )
			//		{
			//			SerialPort = ports[i],
			//		};
			//	}
			//}
			//catch
			//{

			//}
		}

		/// <summary>
		/// 当前的映射关系表
		/// </summary>
		public List<SerialPortMapping> PortMappings { get; set; }

		/// <summary>
		/// 获取当前的串口信息，映射之后的串口信息
		/// </summary>
		/// <returns>实际的端口信息</returns>
		public string[] GetPorts( )
		{
			List<string> array = new List<string>( );

			string[] ports = System.IO.Ports.SerialPort.GetPortNames( );
			for (int i = 0; i < ports.Length; i++)
			{
				SerialPortMapping portMapping = PortMappings?.FirstOrDefault( m => m.SerialPort == ports[i] );
				if (portMapping == null)
				{
					// 端口映射不存在
					array.Add( ports[i] );
				}
				else if (!string.IsNullOrEmpty( portMapping.SerialMapping ))
				{
					// 如果端口映射不为空，则替换映射，否则隐藏该原始端口信息
					array.Add( portMapping.SerialMapping );
				}
			}
			return array.ToArray( );
		}

		/// <summary>
		/// 传入映射后的名字，返回真实的端口信息，如果端口不存在配置，则返回原始端口信息
		/// </summary>
		/// <param name="port">映射后的端口信息</param>
		/// <returns>真实的串口信息</returns>
		public string GetSourcePort( string port )
		{
			SerialPortMapping portMapping = PortMappings?.FirstOrDefault( m => m.SerialMapping == port );
			if (portMapping == null) return port;

			return portMapping.SerialPort;
		}
	}
}
