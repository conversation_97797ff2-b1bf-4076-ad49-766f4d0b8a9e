using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// DTU相关的配置参数，DTU服务器针对的是TCP接口实现
	/// </summary>
	public class DtuConfig
	{
		/// <summary>
		/// 获取或设置是否开启DTU服务器功能，默认不开启
		/// </summary>
		public bool UseDtuServer { get; set; }

		/// <summary>
		/// DTU服务器的相关的端口
		/// </summary>
		public int Port { get; set; } = 10000;

		/// <summary>
		/// DTU服务器的注册包密码
		/// </summary>
		public string Password { get; set; } = "123456";

		/// <summary>
		/// 获取或设置是否需要返回包
		/// </summary>
		public bool ResponseAck { get; set; } = true;
	}
}
