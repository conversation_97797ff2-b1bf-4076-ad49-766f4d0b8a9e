using HslCommunication.BasicFramework;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge.Plugins;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 网关服务器的配置信息
	/// </summary>
	public class HslServerSettings
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public HslServerSettings( )
		{
			Alias     = string.Empty;
			IpAddress = "127.0.0.1";
			Port      = 521;
			Token     = Guid.Empty;
		}

		#endregion

		/// <summary>
		/// Ip地址信息
		/// </summary>
		public string IpAddress { get; set; }

		/// <summary>
		/// 端口号信息
		/// </summary>
		public int Port { get; set; }

		/// <summary>
		/// 服务器的别名
		/// </summary>
		public string Alias { get; set; }

		/// <summary>
		/// 网关的唯一ID信息
		/// </summary>
		public string EdgeID { get; set; }

		/// <summary>
		/// 服务器的令牌
		/// </summary>
		public Guid Token { get; set; }

		/// <summary>
		/// 用户名
		/// </summary>
		public string UserName { get; set; }

		/// <summary>
		/// 密码
		/// </summary>
		public string Password { get; set; }

		/// <summary>
		/// 使用加密通信
		/// </summary>
		public bool UseEncryptedCommunication { get; set; } = true;

		/// <summary>
		/// 当前的网关安装的插件信息
		/// </summary>
		[JsonIgnore]
		public PluginsDefinition[] Plugins { get; set; }

		/// <summary>
		/// 当前的网关的所有插件设备的情况
		/// </summary>
		[JsonIgnore]
		public Dictionary<string, NodePropertyConfig[]> PluginsDefinition { get; set; }

		private MqttSyncClient GetNewMqttSyncClient( )
		{
			MqttCredential credential = new MqttCredential( );
			if (!string.IsNullOrEmpty( Password ))
			{
				credential.Password = HslCommunication.BasicFramework.SoftSecurity.MD5Decrypt( Password, oasdjoasjdohfiasdasjd );
			}
			credential.UserName = UserName;

			MqttSyncClient mqtt = new MqttSyncClient( new MqttConnectionOptions( )
			{
				IpAddress = IpAddress,
				Port = Port,
				Credentials = credential,
				UseRSAProvider = UseEncryptedCommunication,
			} );
			mqtt.ConnectTimeOut = 5000;
			mqtt.SetPersistentConnection( );
			mqtt.ReceiveTimeOut = 30_000;
			return mqtt;
		}

		/// <summary>
		/// 根据配置信息获取到服务器的连接对象信息
		/// </summary>
		/// <returns>客户端的连接信息</returns>
		public MqttSyncClient GetMqttSyncClient( bool newInstance = false )
		{
			if (newInstance) return GetNewMqttSyncClient( );
			if (client == null) client = GetNewMqttSyncClient( );
			return client;
		}

		/// <summary>
		/// 将 MqttSyncClient 关闭并设置为空
		/// </summary>
		public void MqttSyncClientConnectClose( )
		{
			client?.ConnectClose( );
			client = null;
		}

		/// <summary>
		/// 获取到MqttClient对象
		/// </summary>
		/// <returns>连接对象</returns>
		public MqttClient GetMqttClient( )
		{
			MqttClient client = new MqttClient( new MqttConnectionOptions( )
			{
				ClientId = Guid.NewGuid( ).ToString( "N" ),
				IpAddress = IpAddress,
				Port = Port,
				Credentials = new MqttCredential( UserName,
				string.IsNullOrEmpty( Password ) ? string.Empty : SoftSecurity.MD5Decrypt( Password, oasdjoasjdohfiasdasjd ) ),
				UseRSAProvider = UseEncryptedCommunication
			} );
			return client;
		}

		/// <summary>
		/// 根据配置信息获取到服务器的连接对象信息
		/// </summary>
		/// <returns>客户端的连接信息</returns>
		public IntegrationFileClient GetServicesFileClient( )
		{
			IntegrationFileClient client = new IntegrationFileClient( );

			client.ServerIpEndPoint = new System.Net.IPEndPoint( System.Net.IPAddress.Parse( IpAddress ), Port + 2 );
			client.Token            = Token;
			client.ConnectTimeOut   = 1000;
			return client;
		}

		/// <summary>
		/// 获取用于在网关客户端界面显示的文本信息，携带IP地址
		/// </summary>
		/// <returns>文本信息</returns>
		public string GetDisplayText( )
		{
			if (!string.IsNullOrEmpty( Alias ))
				return Alias + $" [{IpAddress}]";
			else
				return EdgeID + $" [{IpAddress}]";
		}

		private MqttSyncClient client;

		/// <inheritdoc/>
		public override string ToString( ) => $"{Alias}";


		public static string oasdjoasjdohfiasdasjd = "qwert123";
	}
}
