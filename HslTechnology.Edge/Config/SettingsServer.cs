using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Enthernet;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Node;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 一个支持参数配置化的服务器，支持加载和存储本地的配置文件，支持远程的配置信息的更新
	/// </summary>
	public class SettingsServer
	{
		#region Constructor

		/// <summary>
		/// 实例化一个配置服务器信息，允许传入
		/// </summary>
		/// <param name="args">启动的参数信息</param>
		/// <param name="logNet">日志组件</param>
		public SettingsServer( string[] args, ILogNet logNet )
		{
			this.logNet = logNet;
			string xmlPathArgs = string.Empty;
			if (args?.Length > 0)
			{
				// 寻找 -xml 参数，如果有的话，重新指定设备配置文件的路径，如果有通信的激活码的话，就实现激活操作
				for (int i = 0; i < args.Length / 2; i++)
				{
					if (args[i * 2].ToUpper( ) == "-XML") xmlPathArgs = args[i * 2 + 1];
					if (args[i * 2].ToUpper( ) == "-HSLCOMMUNICATION")
						if (HslCommunication.Authorization.SetAuthorizationCode( args[i * 2 + 1] ))
							this.logNet?.WriteInfo( "HslCommunication actived" );
                    if (args[i * 2].ToUpper() == "-PWD")
                    {
                        if (args[i * 2 + 1].ToUpper() == "FJTZXXZX-87853795")
                        {
                            CreateRegistration();
                        }
                    }
                }
			}

			this.fileHybirdLock               = new SimpleHybirdLock( );
			this.xmlHybirdLock                = new SimpleHybirdLock( );

			this.fileName                     = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, fileName );
			if (!string.IsNullOrEmpty( xmlPathArgs ))
			{
				// 如果在控制台设置了额外的XML路径信息，就设置为专门的路径
				this.xmlDeviceFileName = xmlPathArgs;
			}
			else
			{
				this.xmlDeviceFileName = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, xmlDeviceFileName );
			}

			this.version                      = new SystemVersion( "1.8.0" );
			this.serverInfoConfig             = new ServerInfoConfig( );
			this.logInfoConfig                = new LogInfoConfig( );
			this.uploadInfoConfig             = new UploadInfoConfig( );
			this.dtuConfig                    = new DtuConfig( );
			this.standbyInfoConfig            = new StandbyInfoConfig( );
			this.portMappingConfig            = new PortMappingConfig( );
			this.serverInfoConfig.UniqueId    = GetUniqueId( );
			this.logNet?.WriteInfo( "当前设备配置文件路径：" + this.xmlDeviceFileName );
		}

        private void CreateRegistration()
        {
            string file = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TZXX.XML");
            if (File.Exists(file))
            {
                File.Delete(file);
            }
            MD5 md5 = MD5.Create();
            byte[] t = md5.ComputeHash(Encoding.Unicode.GetBytes(CPUID()));
            md5.Clear();
            StringBuilder sb = new StringBuilder();
            for (int a = 0; a < t.Length; a++)
            {
                sb.Append(a.ToString("X2"));
            }
            var str = sb.ToString();
            StreamWriter sw = new StreamWriter(file);//这里写上你要保存的路径
            sw.Write(str);//按行写
            sw.Close();//关闭
            Console.WriteLine("注册完成");
        }

        #endregion

        #region Settings Save Load

		/// <summary>
		/// 获取服务器的一些信息，例如最多的设备激活数
		/// </summary>
		/// <returns>JSON数据信息</returns>
		public JObject GetServerInfos( )
		{
			JObject json = new JObject( );
			json.Add( nameof( MaxDeviceCount ), this.maxActiveDeviceCount );
			return json;
		}

		/// <summary>
		/// 将服务器的配置信息转换成JSON字符串
		/// </summary>
		/// <returns>Json字符串数据</returns>
		public JObject GetSettingsJsonString( )
		{
			JObject json = new JObject( );
			// 系统信息部分
			json.Add( nameof( ServerInfoConfig ),          JObject.FromObject( serverInfoConfig ) );
			json.Add( nameof( LogInfoConfig ),             JObject.FromObject( logInfoConfig ) );
			json.Add( nameof( UploadInfoConfig ),          JObject.FromObject( uploadInfoConfig ) );
			json.Add( nameof( DtuConfig ),                 JObject.FromObject( dtuConfig ) );
			json.Add( nameof( StandbyInfoConfig ),         JObject.FromObject( standbyInfoConfig ) );
			json.Add( nameof( PortMappingConfig ),         JObject.FromObject( portMappingConfig ) );
			//json.Add( "UniqueId",              new JValue( this.uniqueId ) );
			return json;
		}

		/// <summary>
		/// 从字符串加载部分的参数信息，这部分的参数信息主要是用于可配置的情况的
		/// </summary>
		/// <param name="json">json对象</param>
		public void LoadSettingsByJson( string json )
		{
			JObject jObject = JObject.Parse( json );
			if (jObject.ContainsKey( nameof( ServerInfoConfig ) ))  this.serverInfoConfig      = jObject[nameof( ServerInfoConfig )]. ToObject<ServerInfoConfig>( );
			if (jObject.ContainsKey( nameof( UploadInfoConfig ) ))  this.uploadInfoConfig      = jObject[nameof( UploadInfoConfig )]. ToObject<UploadInfoConfig>( );
			if (jObject.ContainsKey( nameof( LogInfoConfig ) ))     this.logInfoConfig         = jObject[nameof( LogInfoConfig )].    ToObject<LogInfoConfig>( );
			if (jObject.ContainsKey( nameof( DtuConfig ) ))         this.dtuConfig             = jObject[nameof( DtuConfig )].        ToObject<DtuConfig>( );
			if (jObject.ContainsKey( nameof( StandbyInfoConfig ) )) this.standbyInfoConfig     = jObject[nameof( StandbyInfoConfig )].ToObject<StandbyInfoConfig>( );
			if (jObject.ContainsKey( nameof( PortMappingConfig ) )) this.portMappingConfig     = jObject[nameof( PortMappingConfig )].ToObject<PortMappingConfig>( );
		}

		/// <summary>
		/// 将配置信息存储到文件
		/// </summary>
		public void SaveSettings( )
		{
			fileHybirdLock.Enter( );
			try
			{
				File.WriteAllBytes( fileName, Encoding.UTF8.GetBytes( GetSettingsJsonString( ).ToString( ) ) );
			}
			catch
			{
				throw;
			}
			finally
			{
				fileHybirdLock.Leave( );
			}
		}

		/// <summary>
		/// 从文件加载配置信息
		/// </summary>
		public void LoadSettings( )
		{
			if(File.Exists( fileName ))
			{
				fileHybirdLock.Enter( );
				try
				{
					string json = Encoding.UTF8.GetString( File.ReadAllBytes( fileName ) );
					LoadSettingsByJson( json );
				}
				catch
				{
					throw;
				}
				finally
				{
					fileHybirdLock.Leave( );
				}
			}
			else
			{
				SaveSettings( );
			}
		}

		#endregion

		#region Xml File

		private void CreateDefaultFile( )
		{
			XElement xElement = GroupNode.CreateDefaultXmlSettings( );
			// 存储文件
			xmlHybirdLock.Enter( );
			xElement.Save( xmlDeviceFileName );
			xmlHybirdLock.Leave( );
		}

		/// <summary>
		/// 获取设备的配置文件信息
		/// </summary>
		/// <returns>xml元素信息</returns>
		public XElement GetXmlSettings( )
		{
			if (!File.Exists( xmlDeviceFileName )) CreateDefaultFile( );

			xmlHybirdLock.Enter( );
			XElement xElement = XElement.Load( xmlDeviceFileName );
			xmlHybirdLock.Leave( );
			return xElement;
		}

		#region XmlSettings History

		/// <summary>
		/// 获取变更历史的文件名列表信息
		/// </summary>
		/// <returns>历史的配置文件名称信息</returns>
		public string[] GetXmlSettingsHistory( )
		{
			string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, XmlHistory );
			if (!Directory.Exists( path )) return new string[0];

			try
			{
				return Directory.GetFiles( path, "devices_*.xml" ).Select( m => Regex.Match( m, @"devices_[0-9]+\.xml" ).Value ).ToArray( );
			}
			catch (Exception ex)
			{
				logNet?.WriteException( ToString( ), "GetXmlSettingsHistory", ex );
				return new string[0];
			}
		}

		/// <summary>
		/// 获取历史变更的配置文件信息
		/// </summary>
		/// <param name="fileName">文件名称</param>
		/// <returns>XML配置信息</returns>
		public OperateResult<string> GetXmlSettingsHistory( string fileName )
		{
			if (string.IsNullOrEmpty( fileName )) return new OperateResult<string>( "文件名不能为空！" );
			if (fileName.Contains( "/" ) || fileName.Contains( "\\" )) return new OperateResult<string>( "文件名含有特殊符号" );

			string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, XmlHistory, fileName );
			if (!File.Exists( path )) return new OperateResult<string>( "文件不存在！" );

			try
			{
				return OperateResult.CreateSuccessResult( File.ReadAllText( path, Encoding.UTF8 ) );
			}
			catch (Exception ex)
			{
				return new OperateResult<string>( "文件读取失败：" + ex.Message );
			}
		}

		/// <summary>
		/// 从历史的配置文件中删除指定的文件名称
		/// </summary>
		/// <param name="fileName">文件名称</param>
		/// <returns>是否删除成功</returns>
		public OperateResult<string> DeleteXmlSettingsHistory( string fileName )
		{
			if (string.IsNullOrEmpty( fileName )) return new OperateResult<string>( "文件名不能为空！" );
			if (fileName.Contains( "/" ) || fileName.Contains( "\\" )) return new OperateResult<string>( "文件名含有特殊符号" );

			string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, XmlHistory, fileName );
			if (!File.Exists( path )) return new OperateResult<string>( "文件不存在，删除失败！" );

			try
			{
				File.Delete( path );
				return OperateResult.CreateSuccessResult( "" );
			}
			catch(Exception ex)
			{
				return new OperateResult<string>( "文件删除失败: " + ex.Message );
			}
		}

		#endregion

		#region XmlSettings Standby

		/// <summary>
		/// 新增一个备用的设备配置文件信息，如果文件已经存在，则进行覆盖操作
		/// </summary>
		/// <param name="fileName">文件名称</param>
		/// <param name="xml">文件数据内容</param>
		/// <returns>是否新增成功</returns>
		public OperateResult<string> AddXmlSettingsStandby( string fileName, string xml )
		{
			if (string.IsNullOrEmpty( fileName )) return new OperateResult<string>( "文件名不能为空！" );
			if (fileName.Contains( "/" ) || fileName.Contains( "\\" )) return new OperateResult<string>( "文件名含有特殊符号" );

			string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, XmlStandby );
			try
			{
				if (!Directory.Exists( path )) Directory.CreateDirectory( path );
			}
			catch( Exception ex)
			{
				return new OperateResult<string>( "文件路径创建失败：" + ex.Message );
			}

			try
			{
				if (!fileName.ToUpper().EndsWith( ".XML" )) fileName += ".xml";

				string fileFullName = Path.Combine( path, fileName );
				XElement.Parse( xml ).Save( fileFullName );
				return OperateResult.CreateSuccessResult( "" );
			}
			catch (Exception ex)
			{
				return new OperateResult<string>( "Save failed: " + ex.Message );
			}
		}

		/// <summary>
		/// 获取备用的配置文件名列表信息
		/// </summary>
		/// <returns>备用的配置文件名称信息</returns>
		public string[] GetXmlSettingsStandby( )
		{
			string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, XmlStandby );
			if (!Directory.Exists( path )) return new string[0];

			try
			{
				return Directory.GetFiles( path, "*.xml" ).Select( m => new FileInfo( m ).Name ).ToArray( );
			}
			catch (Exception ex)
			{
				logNet?.WriteException( ToString( ), "GetXmlSettingsTemplate", ex );
				return new string[0];
			}
		}

		/// <summary>
		/// 获取备用的的配置文件内容，需要指定文件名称
		/// </summary>
		/// <param name="fileName">文件名称</param>
		/// <returns>XML配置信息</returns>
		public OperateResult<string> GetXmlSettingsStandby( string fileName )
		{
			if (string.IsNullOrEmpty( fileName )) return new OperateResult<string>( "文件名不能为空！" );
			if (fileName.Contains( "/" ) || fileName.Contains( "\\" )) return new OperateResult<string>( "文件名含有特殊符号" );

			string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, XmlStandby, fileName );
			if (!File.Exists( path )) return new OperateResult<string>( "文件不存在！" );

			try
			{
				return OperateResult.CreateSuccessResult( File.ReadAllText( path, Encoding.UTF8 ) );
			}
			catch (Exception ex)
			{
				return new OperateResult<string>( "文件读取失败：" + ex.Message );
			}
		}

		/// <summary>
		/// 删除一个备用的的配置文件信息
		/// </summary>
		/// <param name="fileName">文件名称</param>
		/// <returns>是否删除成功</returns>
		public OperateResult<string> DeleteXmlSettingsStandby( string fileName )
		{
			if (string.IsNullOrEmpty( fileName )) return new OperateResult<string>( "文件名不能为空！" );
			if (fileName.Contains( "/" ) || fileName.Contains( "\\" )) return new OperateResult<string>( "文件名含有特殊符号" );

			string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, XmlStandby, fileName );
			if (!File.Exists( path )) return new OperateResult<string>( "文件不存在，删除失败！" );

			try
			{
				File.Delete( path );
				return OperateResult.CreateSuccessResult( "" );
			}
			catch (Exception ex)
			{
				return new OperateResult<string>( "文件删除失败: " + ex.Message );
			}
		}

		#endregion

		/// <summary>
		/// 设置新的配置文件信息
		/// </summary>
		/// <param name="settings">配置文件</param>
		public void SetXmlSettings( string settings )
		{
			xmlHybirdLock.Enter( );
			try
			{
				// 如果没有，则创建历史文件夹
				string historyPath = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, XmlHistory );
				if (!Directory.Exists( historyPath )) Directory.CreateDirectory( historyPath );

				// 如果文件超过9个，则删除更老的文件信息
				string[] files = Directory.GetFiles( historyPath, "devices_*.xml" );
				if (files?.Length > 9)
				{
					FileInfo[] fileInfos = files.Select( m => new FileInfo( m ) ).OrderBy( m => m.LastWriteTime ).ToArray( );
					for (int i = 0; i < fileInfos.Length - 9; i++)
					{
						File.Delete( fileInfos[i].FullName );
					}
				}

				// 先将原来的 XML 文件存储到历史里面去
				FileInfo original = new FileInfo( xmlDeviceFileName );
				File.Copy( xmlDeviceFileName, Path.Combine( historyPath, $"devices_{original.LastWriteTime:yyyyMMddHHmmss}.xml" ) );
			}
			catch (Exception ex)
			{
				logNet?.WriteException( ToString( ), "SetXmlSettings", ex );
			}

			try
			{
				XElement.Parse( settings ).Save( xmlDeviceFileName );
				// 当处于linux系统时，还需要执行文件的 sync 同步指令，才能准确的写入到实际的本文里面

			}
			catch
			{
				throw;
			}
			finally
			{
				xmlHybirdLock.Leave( );
			}
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 指示所有的设备的配置情况的文件名
		/// </summary>
		public string XmlDeveiceFileName => xmlDeviceFileName;

		/// <summary>
		/// 服务器基本功能的配置信息
		/// </summary>
		public ServerInfoConfig ServerInfo => this.serverInfoConfig;

		/// <summary>
		/// 日志相关功能的配置信息
		/// </summary>
		public LogInfoConfig LogInfo => this.logInfoConfig;

		/// <summary>
		/// 远程上传相关功能的配置信息
		/// </summary>
		public UploadInfoConfig UploadInfo => this.uploadInfoConfig;

		/// <summary>
		/// 获取DTU相关的服务器参数信息
		/// </summary>
		public DtuConfig DtuInfo => this.dtuConfig;

		/// <summary>
		/// 获取备用服务器的相关配置信息
		/// </summary>
		public StandbyInfoConfig StandbyInfo => this.standbyInfoConfig;

		/// <summary>
		/// 获取当前的端口映射配置信息
		/// </summary>
		public PortMappingConfig PortMapping => this.portMappingConfig;

		/// <summary>
		/// 当前系统的版本信息
		/// </summary>
		public SystemVersion Version => this.version;

		/// <summary>
		/// 备用的XML配置文件夹
		/// </summary>
		public string XmlStandby => "XmlStandby";

		/// <summary>
		/// XML配置的历史记录文件夹，默认存储10个文件
		/// </summary>
		public string XmlHistory => "XmlHistory";

		/// <summary>
		/// 配置的最大设备数量
		/// </summary>
		public int MaxDeviceCount => this.maxActiveDeviceCount;

		/// <summary>
		/// 获取用于上传远程MQTT服务器的连接参数对象，获取该对象之后，可以方便的创建MQTT Client对象，进而连接远程服务器上传数据
		/// </summary>
		/// <returns>MQTT连接参数信息</returns>
		public MqttConnectionOptions CreateHslMqttClientOption( )
		{
			MqttConnectionOptions options = new MqttConnectionOptions( )
			{
				ConnectTimeout        = 5000,
				ClientId              = string.IsNullOrEmpty( this.uploadInfoConfig.MqttClientID ) ? this.serverInfoConfig.DeviceName : this.uploadInfoConfig.MqttClientID,
				IpAddress             = this.uploadInfoConfig.MqttIpAddress,
				Port                  = this.uploadInfoConfig.MqttPort,
				CleanSession          = true,
				KeepAlivePeriod       = TimeSpan.FromSeconds( 100 ),
				KeepAliveSendInterval = TimeSpan.FromSeconds( 30 ),
			};
			if (this.uploadInfoConfig.MqttType == UploadInfoConfig.MqttTypeNormal)
			{
				// 上传普通的MQTT
				options.Credentials = new MqttCredential( )
				{
					UserName = this.uploadInfoConfig.MqttUserName,
					Password = this.uploadInfoConfig.MqttPassword,
				};
			}
			else if (this.uploadInfoConfig.MqttType == UploadInfoConfig.MqttTypeJetLinks)
			{
				// 上传JetLinks
				DateTime startTime = TimeZoneInfo.ConvertTimeFromUtc( new System.DateTime( 1970, 1, 1 ), TimeZoneInfo.Local );
				long tick = (long)(DateTime.Now - startTime).TotalMilliseconds;
				string name = this.uploadInfoConfig.MqttUserName + "|" + tick;
				string pwd = SoftBasic.CalculateStreamMD5( name + "|" + this.uploadInfoConfig.MqttPassword ).ToLower( );
				options.Credentials = new MqttCredential( )
				{
					UserName = name,
					Password = pwd,
				};
			}

			if (this.uploadInfoConfig.UseSSL)
			{
				options.UseSSL = this.uploadInfoConfig.UseSSL;
				if (!string.IsNullOrEmpty( this.uploadInfoConfig.MqttCAFileName ) && !string.IsNullOrEmpty( this.uploadInfoConfig.MqttCAFileContent ))
				{
					try
					{
						string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "CA Files" );
						if (!System.IO.Directory.Exists( path )) Directory.CreateDirectory( path );

						path = Path.Combine( path, this.uploadInfoConfig.MqttCAFileName );
						File.WriteAllBytes( path, Convert.FromBase64String( this.uploadInfoConfig.MqttCAFileContent ) );

						options.CertificateFile = path;
					}
					catch (Exception ex)
					{
						logNet?.WriteException( "生成MQTT上传证书文件时异常，当前不启用登录登录的操作。", ex );
					}
				}
			}
			return options;
		}
		public MqttConnectionOptions CreateHslMqttClientOption2( )
		{
			MqttConnectionOptions options = new MqttConnectionOptions( )
			{
				ConnectTimeout = 5000,
				ClientId = string.IsNullOrEmpty( this.uploadInfoConfig.MqttClientID2 ) ? this.serverInfoConfig.DeviceName : this.uploadInfoConfig.MqttClientID2,
				IpAddress = this.uploadInfoConfig.MqttIpAddress2,
				Port = this.uploadInfoConfig.MqttPort2,
				CleanSession = true,
				KeepAlivePeriod = TimeSpan.FromSeconds( 100 ),
				KeepAliveSendInterval = TimeSpan.FromSeconds( 30 ),
			};
			if (this.uploadInfoConfig.MqttType2 == UploadInfoConfig.MqttTypeNormal)
			{
				// 上传普通的MQTT
				options.Credentials = new MqttCredential( )
				{
					UserName = this.uploadInfoConfig.MqttUserName2,
					Password = this.uploadInfoConfig.MqttPassword2,
				};
			}
			else if (this.uploadInfoConfig.MqttType2 == UploadInfoConfig.MqttTypeJetLinks)
			{
				// 上传JetLinks
				DateTime startTime = TimeZoneInfo.ConvertTimeFromUtc( new System.DateTime( 1970, 1, 1 ), TimeZoneInfo.Local );
				long tick = (long)(DateTime.Now - startTime).TotalMilliseconds;
				string name = this.uploadInfoConfig.MqttUserName2 + "|" + tick;
				string pwd = SoftBasic.CalculateStreamMD5( name + "|" + this.uploadInfoConfig.MqttPassword2 ).ToLower( );
				options.Credentials = new MqttCredential( )
				{
					UserName = name,
					Password = pwd,
				};
			}

			if (this.uploadInfoConfig.UseSSL2)
			{
				options.UseSSL = this.uploadInfoConfig.UseSSL2;
				if (!string.IsNullOrEmpty( this.uploadInfoConfig.MqttCAFileName2 ) && !string.IsNullOrEmpty( this.uploadInfoConfig.MqttCAFileContent2 ))
				{
					try
					{
						string path = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "CA Files" );
						if (!System.IO.Directory.Exists( path )) Directory.CreateDirectory( path );

						path = Path.Combine( path, this.uploadInfoConfig.MqttCAFileName2 );
						File.WriteAllBytes( path, Convert.FromBase64String( this.uploadInfoConfig.MqttCAFileContent2 ) );

						options.CertificateFile = path;
					}
					catch (Exception ex)
					{
						logNet?.WriteException( "生成MQTT上传证书文件时异常，当前不启用登录登录的操作。", ex );
					}
				}
			}
			return options;
		}

		/// <summary>
		/// 检查指定的用户名和密码是否正确，如果正确的话，返回 <c>True</c>, 如果是错误的，则返回 <c>False</c>
		/// </summary>
		/// <param name="name">用户名信息</param>
		/// <param name="pwd">密码</param>
		/// <returns>账户是否校验成功</returns>
		public bool CheckAccount( string name, string pwd )
		{
			return this.serverInfoConfig.CheckAccount( name, pwd );
		}

		/// <summary>
		/// 设置HttpServer的登录账户及密码信息
		/// </summary>
		/// <param name="httpServer">基于Http的服务器信息</param>
		public void SetHttpLoginAccount( HttpServer httpServer )
		{
			httpServer.SetLoginAccessControl( this.serverInfoConfig.CreateHttpAccount( ) );
		}

		/// <summary>
		/// 获取当前的设备的唯一信息，如果没有信息，就自动生成一串唯一的随机的数据
		/// </summary>
		/// <returns>唯一数据</returns>
		public static string GetUniqueId( )
		{
			if (RuntimeInformation.IsOSPlatform( OSPlatform.Linux ))
			{
				try
				{
					if(File.Exists( "/proc/cpuinfo" ))
					{
						string[] tmp = File.ReadAllLines( "/proc/cpuinfo", Encoding.UTF8 );
						for (int i = 0; i < tmp.Length; i++)
						{
							if (tmp[i].StartsWith( "Serial" ))
							{
								return "Pi" + tmp[i].Substring( tmp[i].IndexOf( ':' ) + 1 ).Trim( ' ' );
							}
						}
					}
				}
				catch
				{

				}
			}

			string filepath = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "uniqueId.txt" );
			if(File.Exists( filepath ))
			{
				return File.ReadAllText( filepath, Encoding.UTF8 );
			}
			else
			{
				string id = RuntimeInformation.IsOSPlatform( OSPlatform.Linux ) ? "Linux" : "Win" + SoftBasic.GetUniqueStringByGuidAndRandom( );
				try
				{
					File.WriteAllText( filepath, id, Encoding.UTF8 );
				}
				catch
				{

				}
				return id;
			}
		}

        public static string CPUID()
        {
			try
			{
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    string cpuInfo = string.Empty;
                    System.Management.ManagementClass mc = new System.Management.ManagementClass("win32_processor");
                    System.Management.ManagementObjectCollection moc = mc.GetInstances();
                    foreach (System.Management.ManagementObject mo in moc)
                    {
                        if (cpuInfo == "")
                        {
                            cpuInfo = mo.Properties["processorID"].Value.ToString();
                            break;
                        }
                    }
                    if (string.IsNullOrEmpty(cpuInfo))
                    {
                        cpuInfo = "TZXX";
                    }
                    return cpuInfo;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    return Bash("dmidecode -t processor | grep -E ID | sed 's/.*: //' | head -n 1");
                }
                else
                {
                    return "";
                }
            }
			catch
			{
				return "tzxxzx";
			}
            
        }

        public static string Bash(string cmd)
        {
            string result = String.Empty;
            try
            {
                var escapedArgs = cmd.Replace("\"", "\\\"");
                using (Process process = new Process())
                {
                    process.StartInfo = new ProcessStartInfo
                    {
                        FileName = "/bin/bash",
                        Arguments = $"-c \"{escapedArgs}\"",
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                    };
                    process.Start();
                    result = process.StandardOutput.ReadToEnd();
                    process.WaitForExit(1500);
                    process.Kill();
                };
            }
            catch (Exception ex)
            {
                //Logger.ErrorFormat(ex.Message, ex);
            }
            return result;
        }


		#endregion

		#region Private Member

		private ILogNet logNet;                                              // 当前的日志信息
		private SimpleHybirdLock fileHybirdLock;                             // 文件的存储锁
		private SimpleHybirdLock xmlHybirdLock;                              // 文件的存储锁
		private string fileName = "settings.txt";                            // 基础的配置文件信息的存储
		private string xmlDeviceFileName = "devices.xml";                    // 设备的存储文件名

		private ServerInfoConfig serverInfoConfig;                           // 服务器基本信息配置
		private LogInfoConfig logInfoConfig;                                 // 日志基本信息配置
		private UploadInfoConfig uploadInfoConfig;                           // 上传基本信息配置
		private DtuConfig dtuConfig;                                         // Dtu相关的配置信息
		private StandbyInfoConfig standbyInfoConfig;                         // 作为备用服务器运行的配置信息
		private PortMappingConfig portMappingConfig;                         // 端口映射关系的配置
		private SystemVersion version;                                       // 当前程序的版本号
		private int maxActiveDeviceCount = -1;                               // 当前允许激活的最多的设备数量，小于0为没有限制

		#endregion
	}
}
