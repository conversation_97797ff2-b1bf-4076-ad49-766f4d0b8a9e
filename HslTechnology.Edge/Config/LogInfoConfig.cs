using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Node;
using HslCommunication.LogNet;
using System.IO;

namespace HslTechnology.Edge.Config
{
	/// <summary>
	/// 日志相关的配置信息
	/// </summary>
	public class LogInfoConfig
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public LogInfoConfig( )
		{
			this.LogMode               = "None";
			this.LogFileSize           = 1024;                          // 默认1M
			this.LogFileSizeCount      = 10;                            // 10个文件
			this.LogDateTimeMode       = GenerateMode.ByEveryDay;       // 按照天存储
			this.LogDateTimeFileCount  = 10;                            // 10天日志
			this.MessageDegree         = HslMessageDegree.INFO;         // 存储等级
		}

		#endregion

		/// <summary>
		/// 文件存储模式  None SingleFile FileSize DateTime
		/// </summary>
		public string LogMode { get; set; }

		/// <summary>
		/// 文件大小，默认 1024 单位，KB
		/// </summary>
		public int LogFileSize { get; set; }

		/// <summary>
		/// 文件模式下存储的文件的数量，默认是10个
		/// </summary>
		public int LogFileSizeCount { get; set; }

		/// <summary>
		/// 按时间存储模式下的
		/// </summary>
		public GenerateMode LogDateTimeMode { get; set; }

		/// <summary>
		/// 按照时间存储的模式的文件个数
		/// </summary>
		public int LogDateTimeFileCount { get; set; }

		/// <summary>
		/// 存储的消息等级
		/// </summary>
		public HslMessageDegree MessageDegree { get; set; }

		/// <summary>
		/// 根据日志的配置信息，获取相关的日志对象
		/// </summary>
		/// <returns>日志对象</returns>
		public ILogNet GetLogNet( )
		{
			ILogNet log;
			if (LogMode == "None") log = new LogNetSingle( "" );
			else if (LogMode == "SingleFile") log = new LogNetSingle( Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "Logs", "log.txt" ) );
			else if (LogMode == "FileSize") log = new LogNetFileSize(
				 Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "Logs" ),
				 LogFileSize * 1024,
				 LogFileSizeCount );
			else if (LogMode == "DateTime") log = new LogNetDateTime(
				 Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "Logs" ),
				 LogDateTimeMode,
				 LogDateTimeFileCount );
			else log = new LogNetSingle( "" );

			if (log != null)
			{
				log.SetMessageDegree( MessageDegree );
				log.ConsoleOutput = true;
			}
			return log;
		}
	}
}
