using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication.LogNet;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.Node.Alarm;
using Newtonsoft.Json.Linq;
using HslCommunication.BasicFramework;
using HslCommunication.Reflection;
using HslCommunication;
using HslTechnology.Edge.DataBusiness.Oee;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Oee;
using System.Xml.Linq;
using HslTechnology.Edge.DataBusiness.Database;

namespace HslTechnology.Edge.DataBusiness
{
	/// <summary>
	/// 所有的数据业务的核心引擎
	/// </summary>
	public class BusinessEngine
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public BusinessEngine( ILogNet logNet )
		{
			this.logNet          = logNet;
			this.analysisDevices = new Dictionary<string, AdvanceAnalysisDevice>( );
			this.Templates       = new Dictionary<string, XElement>( );
		}

		/// <summary>
		/// 触发报警完成的事件
		/// </summary>
		public event Action<string, AlarmItem> OnAlarmFinishEvent;

		/// <summary>
		/// 触发报警开始的事件
		/// </summary>
		public event Action<string, AlarmItem> OnAlarmStartEvent;

		/// <summary>
		/// 处理当前的报警节点相关的信息内容，然后增加到报警分析引擎信息
		/// </summary>
		/// <param name="alarmRelate">支持报警的节点信息接口</param>
		/// <param name="alarmNode">报警的节点定义信息param>
		/// <param name="deviceUrl">设备的唯一路径名称</param>
		/// <param name="tagName">数据的真实标签名称</param>
		public void PrepareDealAlarm( IAlarmRelateNode alarmRelate, AlarmNode alarmNode, string deviceUrl, string tagName )
		{
			if (string.IsNullOrEmpty( alarmRelate.AlarmRelate )) return;
			if (string.IsNullOrEmpty( tagName )) tagName = alarmRelate.Name;

			if (alarmNode == null) return;
			// 有报警设置
			if (alarmRelate.IsBoolRegular( ))
			{
				if (alarmNode.AlarmType == AlarmType.Boolean)
					AddHexAlarmAnalysis( deviceUrl, tagName, alarmNode.AlarmDefinitions, false );
				else if (alarmNode.AlarmType == AlarmType.Hex)
				{
					NodeAlarmHex nodeAlarmHex = alarmNode as NodeAlarmHex;
					AddHexAlarmAnalysis( deviceUrl, tagName, alarmNode.AlarmDefinitions, nodeAlarmHex.RevserveByWord );
				}
			}
			else if (alarmRelate.DataTypeCode == RegularNodeTypeItem.Byte.Text && alarmRelate.Length > 0)
			{
				if (alarmNode.AlarmType == AlarmType.Hex)
				{
					NodeAlarmHex nodeAlarmHex = alarmNode as NodeAlarmHex;
					AddHexAlarmAnalysis( deviceUrl, tagName, alarmNode.AlarmDefinitions, nodeAlarmHex.RevserveByWord );
				}
			}
			else if (
				alarmRelate.Length < 0 &&
				alarmRelate.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
				alarmRelate.DataTypeCode == RegularNodeTypeItem.SByte.Text ||
				alarmRelate.DataTypeCode == RegularNodeTypeItem.Int16.Text ||
				alarmRelate.DataTypeCode == RegularNodeTypeItem.UInt16.Text ||
				alarmRelate.DataTypeCode == RegularNodeTypeItem.Int32.Text ||
				alarmRelate.DataTypeCode == RegularNodeTypeItem.UInt32.Text ||
				alarmRelate.DataTypeCode == RegularNodeTypeItem.Int64.Text ||
				alarmRelate.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
			{
				if (alarmNode.AlarmType == AlarmType.Integer)
					AddIntegerAlarm( deviceUrl, tagName, alarmNode.AlarmDefinitions );
				else if (alarmNode.AlarmType == AlarmType.DataRange)
					AddDataRangeAlarm( deviceUrl, tagName, alarmNode as NodeDataRangeAlarm );
			}
			else if (alarmRelate.Length < 0 &&
				alarmRelate.DataTypeCode == RegularNodeTypeItem.Float.Text ||
				alarmRelate.DataTypeCode == RegularNodeTypeItem.Double.Text)
			{
				if (alarmNode.AlarmType == AlarmType.DataRange)
					AddDataRangeAlarm( deviceUrl, tagName, alarmNode as NodeDataRangeAlarm );
			}
			else if (alarmRelate.DataTypeCode == RegularNodeTypeItem.String.Text)
			{
				if (alarmNode.AlarmType == AlarmType.String)
				{
					AddTextAlarm( deviceUrl, tagName );
				}
			}
		}

		/// <summary>
		/// 处理当前的报警节点相关的信息内容，然后增加到报警分析引擎信息
		/// </summary>
		/// <param name="oeeRelate">支持报警的节点信息接口</param>
		/// <param name="oeeNode">Oee节点定义信息</param>
		/// <param name="deviceUrl">设备的唯一路径名称</param>
		/// <param name="tagName">数据的真实标签名称</param>
		public void PrepareDealOee( IOeeRelateNode oeeRelate, OeeNode oeeNode, string deviceUrl, string tagName = null )
		{
			if (string.IsNullOrEmpty( oeeRelate.OeeRelate )) return;
			if (string.IsNullOrEmpty( tagName )) tagName = oeeRelate.Name;

			//OeeNode oeeNode = OeeResource.GetOeeNode( oeeRelate.OeeRelate );
			if (oeeNode == null) return;
			// 有OEE配置
			if (
				oeeRelate.Length < 0 &&
				oeeRelate.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.SByte.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.Int16.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.UInt16.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.Int32.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.UInt32.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.Int64.Text ||
				oeeRelate.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
			{
				AddOeeAnalysis( deviceUrl, tagName, oeeNode );
			}
		}

		#region Device Add

		/// <summary>
		/// 将一个设备添加到分析引擎
		/// </summary>
		/// <param name="deviceName">设备的唯一名称，带路径属性</param>
		public AdvanceAnalysisDevice AddDevice( string deviceName )
		{
			if (string.IsNullOrEmpty( deviceName ))
			{
				this.logNet?.WriteError( "当前的设备为空，无法添加设备分析引擎。" );
				return null;
			}
			if (analysisDevices.ContainsKey( deviceName ))
			{
				this.logNet?.WriteError( $"当前的设备[{deviceName}]已经存在，无法添加设备分析引擎。" );
				return null;
			}

			AdvanceAnalysisDevice device = new AdvanceAnalysisDevice( deviceName );
			device.OnAlarmStartEvent     += Device_OnAlarmStartEvent;
			device.OnAlarmFinishEvent    += Device_OnAlarmFinishEvent;
			analysisDevices.Add( deviceName, device );
			return device;
		}

		private void Device_OnAlarmFinishEvent( string deviceName, AlarmItem alarm )
		{
			OnAlarmFinishEvent?.Invoke( deviceName, alarm );
		}

		private void Device_OnAlarmStartEvent( string deviceName, AlarmItem alarm )
		{
			OnAlarmStartEvent?.Invoke( deviceName, alarm );
		}

		/// <summary>
		/// 新增一个HEX格式的报警内容，需要指定唯一的设备名称，节点名称，点位定义的信息<br />
		/// Add a HEX format alarm content, you need to specify a unique device name, node name, point definition information
		/// </summary>
		/// <remarks>
		/// HEX格式等同于bool数组的格式，通常包含两种状态，0，1，通常1表示报警，0表示正常。<br />
		/// The HEX format is equivalent to the format of the bool array, and usually contains two states, 0, 1, usually 1 means alarm, 0 means normal.
		/// </remarks>
		/// <param name="deviceName">设备的唯一名称，带路径</param>
		/// <param name="tagName">标签名称</param>
		/// <param name="alarmDefinitionNodes">报警节点的定义信息</param>
		private void AddHexAlarmAnalysis( string deviceName, string tagName, AlarmDefinitionNode[] alarmDefinitionNodes, bool reverseByWord )
		{
			if (string.IsNullOrEmpty( deviceName ))
			{
				this.logNet?.WriteError( "当前的设备为空，无法添加设备Hex报警分析引擎。" );
				return;
			}
			if (!analysisDevices.ContainsKey( deviceName ))
			{
				this.logNet?.WriteError( $"当前的设备[{deviceName}]不存在，无法添加设备Hex报警分析引擎。" );
				return;
			}
			analysisDevices[deviceName].AddHexAlarms( tagName, alarmDefinitionNodes, reverseByWord );
		}

		/// <summary>
		/// 新增一个整型的报警内容，需要指定唯一的设备名称，节点名称，点位定义信息
		/// </summary>
		/// <remarks>
		/// 整数的报警方式，一个ushort类型的值即可表示65535种报警信息，缺点是无法表示两个报警同时发生
		/// </remarks>
		/// <param name="deviceName">设备的唯一名称，带路径</param>
		/// <param name="tagName">数据标签名称</param>
		/// <param name="alarmDefinitionNodes">报警节点的定义信息</param>
		private void AddIntegerAlarm( string deviceName, string tagName, AlarmDefinitionNode[] alarmDefinitionNodes )
		{
			if (string.IsNullOrEmpty( deviceName ))
			{
				this.logNet?.WriteError( "当前的设备为空，无法添加设备Hex报警分析引擎。" );
				return;
			}
			if (!analysisDevices.ContainsKey( deviceName ))
			{
				this.logNet?.WriteError( $"当前的设备[{deviceName}]不存在，无法添加设备Integer报警分析引擎。" );
				return;
			}
			analysisDevices[deviceName].AddIntegerAlarm( tagName, alarmDefinitionNodes );
		}

		/// <summary>
		/// 新增一个数值范围的报警内容，需要指定唯一的设备名称，节点名称，数据范围的定义
		/// </summary>
		/// <param name="deviceName">唯一的设备名称</param>
		/// <param name="tagName">数据标签名称</param>
		/// <param name="nodeDataRange">数值范围定义的信息</param>
		private void AddDataRangeAlarm( string deviceName, string tagName, NodeDataRangeAlarm nodeDataRange )
		{
			if (string.IsNullOrEmpty( deviceName ))
			{
				this.logNet?.WriteError( "当前的设备为空，无法添加设备Hex报警分析引擎。" );
				return;
			}
			if (!analysisDevices.ContainsKey( deviceName ))
			{
				this.logNet?.WriteError( $"当前的设备[{deviceName}]不存在，无法添加设备DataRange报警分析引擎。" );
				return;
			}
			analysisDevices[deviceName].AddDataRangeAlarm( tagName, nodeDataRange );
		}

		/// <summary>
		/// 新增一个基于文本的报警信息，需要指定唯一的设备名称，节点名称，对于文本报警来说，文本为空则表示正常，文本不为空则表示报警。
		/// </summary>
		/// <param name="deviceName">唯一的设备名称</param>
		/// <param name="tagName">数据标签名称</param>
		private void AddTextAlarm( string deviceName, string tagName )
		{
			if (string.IsNullOrEmpty( deviceName ))
			{
				this.logNet?.WriteError( "当前的设备为空，无法添加设备Hex报警分析引擎。" );
				return;
			}
			if (!analysisDevices.ContainsKey( deviceName ))
			{
				this.logNet?.WriteError( $"当前的设备[{deviceName}]不存在，无法添加设备DataRange报警分析引擎。" );
				return;
			}
			analysisDevices[deviceName].AddTextAlarm( tagName );
		}

		#endregion

		#region DealAlarm

		/// <summary>
		/// 处理Hex的报警信息，传入hex字符串，或是byte[], 或是bool[]
		/// </summary>
		/// <param name="deviceName">唯一的设备路径信息</param>
		/// <param name="tagName">数据节点名称</param>
		/// <param name="hexString">传入的分析数据信息</param>
		public void DealHexData( string deviceName, string tagName, string hexString )
		{
			if (analysisDevices.ContainsKey( deviceName ))
			{
				analysisDevices[deviceName].DealHexData( tagName,hexString );
			}
		}

		/// <inheritdoc cref="DealHexData(string, string, string)"/>
		public void DealHexData( string deviceName, string tagName, byte[] byteArray )
		{
			if (analysisDevices.ContainsKey( deviceName ))
			{
				analysisDevices[deviceName].DealHexData( tagName, byteArray );
			}
		}

		/// <inheritdoc cref="DealHexData(string, string, string)"/>
		public void DealHexData( string deviceName, string tagName, bool boolValue )
		{
			DealHexData( deviceName, tagName, new bool[] { boolValue } );
		}

		/// <inheritdoc cref="DealHexData(string, string, string)"/>
		public void DealHexData( string deviceName, string tagName, bool[] boolArray )
		{
			if (analysisDevices.ContainsKey( deviceName ))
			{
				analysisDevices[deviceName].DealHexData( tagName, boolArray );
			}
		}

		public void DealIntegerData( string deviceName, string tagName, int alarmCode )
		{
			if (analysisDevices.ContainsKey( deviceName ))
			{
				analysisDevices[deviceName].DealIntegerData( tagName, alarmCode );
			}
		}

		public void DealTextData( string deviceName, string tagName, string alarmCode )
		{
			if (analysisDevices.ContainsKey( deviceName ))
			{
				analysisDevices[deviceName].DealTextData( tagName, alarmCode );
			}
		}

		public void DealDataRange( string deviceName, string tagName, double value )
		{
			if (analysisDevices.ContainsKey( deviceName ))
			{
				analysisDevices[deviceName].DealDataRange( tagName, value );
			}
		}

		#endregion

		#region Get Alarms

		/// <summary>
		/// 将指定唯一编码 UniqueID 的报警强制中断掉，报警会被视为结束，状态更改为 Abort
		/// </summary>
		/// <param name="uniqueId">报警对象的唯一ID</param>
		/// <returns>是否中断成功</returns>
		[HslMqttApi( ApiTopic = "Alarm/Abort", Description = "将指定唯一编码 UniqueID 的报警强制中断掉，报警会被视为结束，状态更改为 Abort" )]
		public OperateResult AbortAlarm( long uniqueId )
		{
			foreach (var item in analysisDevices.Values)
			{
				if (item.AbortAlarm( uniqueId ))
					return OperateResult.CreateSuccessResult( );
			}
			return new OperateResult( $"当前的ID[{uniqueId}] 不存在或是已经报警结束。" );
		}

		/// <summary>
		/// 根据指定的唯一的设备名称或是路径来获取报警列表信息<br />
		/// Obtain the alarm list information according to the specified unique device name
		/// </summary>
		/// <param name="data">设备的名称</param>
		/// <returns>报警的列表信息</returns>
		[HslMqttApi( ApiTopic = "Alarm/GetAlarms", Description = "获取当前的系统的对象列表，根据传入路径区分，空为所有报警，如果是路径，则路径下所有设备报警列表，如果是设备，设备的报警列表" )]
		public OperateResult<AlarmItem[]> GetAlarms( string data )
		{
			if (string.IsNullOrEmpty( data ))
			{
				List<AlarmItem> alarms = new List<AlarmItem>( );
				foreach (var item in analysisDevices.Values)
				{
					alarms.AddRange( item.GetAlarms( ) );
				}
				return OperateResult.CreateSuccessResult( alarms.ToArray( ) );
			}
			if (data.IsDevicePath( ))
			{
				List<AlarmItem> alarms = new List<AlarmItem>( );
				foreach (var item in analysisDevices.Values)
				{
					if (item.InSpecifiedPath( data.SplitDeviceNodes( ) ))
					{
						alarms.AddRange( item.GetAlarms( ) );
					}
				}
				return OperateResult.CreateSuccessResult( alarms.ToArray( ) );
			}
			else
			{
				if (analysisDevices.ContainsKey( data ))
				{
					return OperateResult.CreateSuccessResult( analysisDevices[data].GetAlarms( ).ToArray( ) );
				}
				return new OperateResult<AlarmItem[]>( $"Device [{data}] is not exist!" );
			}
		}

		/// <summary>
		/// 获取当前的所有的设备的报警列表，根据设备的名称进行分类的报警信息
		/// </summary>
		/// <returns>JSON结果对象</returns>
		[HslMqttApi( ApiTopic = "Alarm/GetAlarmsGroupByMachine", Description = " 获取当前的所有的设备的报警列表，根据设备的名称进行分类的报警信息" )]
		public JObject GetAlarmsGroupByMachine( )
		{
			JObject json = new JObject( );
			foreach (var item in analysisDevices.Values)
			{
				json.Add( item.DeviceName, item.GetAlarmJsonCount( ) );
			}
			return json;
		}

		/// <summary>
		/// 获取当前的系统的报警总个数的统计信息，包括每不同的等级的报警数量信息
		/// </summary>
		/// <param name="data">设备的索引，为空就是读取所有的设备</param>
		/// <returns>json对象</returns>
		[HslMqttApi( ApiTopic = "Alarm/GetAlarmJsonCount", Description = "获取当前的系统的报警总个数的统计信息，包括每不同的等级的报警数量信息" )]
		public JObject GetAlarmJsonCount( string data )
		{
			// 此处要识别路径
			if (string.IsNullOrEmpty( data ) || data.IsDevicePath( ))
			{
				JObject json = new JObject( );
				int alarmCount = 0;
				int alarm1 = 0;
				int alarm2 = 0;
				int alarm3 = 0;
				int alarm4 = 0;
				foreach (var item in analysisDevices.Values)
				{
					if (string.IsNullOrEmpty( data ) || item.InSpecifiedPath( data.SplitDeviceNodes( ) ))
					{
						JObject jobject = item.GetAlarmJsonCount( );
						alarmCount += jobject.Value<int>( "AlarmCount" );
						alarm1 += jobject.Value<int>( nameof( AlarmDegree.Hint ) );
						alarm2 += jobject.Value<int>( nameof( AlarmDegree.Warn ) );
						alarm3 += jobject.Value<int>( nameof( AlarmDegree.Error ) );
						alarm4 += jobject.Value<int>( nameof( AlarmDegree.Fatal ) );
						json.Add( item.DeviceName, jobject );
					}
				}
				json.Add( "__AlarmCount", new JValue( alarmCount ) );
				json.Add( "__Hint", new JValue( alarm1 ) );
				json.Add( "__Warn", new JValue( alarm2 ) );
				json.Add( "__Error", new JValue( alarm3 ) );
				json.Add( "__Fatal", new JValue( alarm4 ) );
				return json;
			}
			else
			{
				if (analysisDevices.ContainsKey( data ))
				{
					return analysisDevices[data].GetAlarmJsonCount( );
				}
				return null;
			}
		
		}

		/// <summary>
		/// 使用指定的数据库对象来执行指定的SQL语句，如果指定的数据库不存在，会返回失败信息，如果语句执行成功，会返回受影响的行数信息
		/// </summary>
		/// <param name="database">服务器配置的数据库名称信息</param>
		/// <param name="sqlComd">SQL命令语句</param>
		/// <returns>如果执行成功，返回受影响的行数信息</returns>
		[HslMqttApi( ApiTopic = "Database/ExecuteSqlByDatabase", Description = "使用指定的数据库对象来执行指定的SQL语句，如果指定的数据库不存在，会返回失败信息，如果语句执行成功，会返回受影响的行数信息" )]
		public async Task<OperateResult<int>> ExecuteSqlByDatabase( string database, string sqlComd )
		{
			DatabaseCore databaseCore = DatabaseResource.GetDatabaseCore( database );
			if (databaseCore == null) return new OperateResult<int>( $"数据库名词[{database}]不存在，无法执行相关的指令" );

			try
			{
				return await databaseCore.ExecuteCommand( sqlComd );
			}
			catch (Exception ex)
			{
				return new OperateResult<int>( $"SQL 执行失败：" + ex.Message );
			}
		}

		/// <summary>
		/// 获取或设置业务引擎关联的报警资源信息
		/// </summary>
		public AlarmResource AlarmResource { get; set; }

		/// <summary>
		/// 获取或设置业务引擎关联的OEE资源信息
		/// </summary>
		public OeeResource OeeResource { get; set; }

		/// <summary>
		/// 获取或设置数据库业务相关的资源对象信息s
		/// </summary>
		public DatabaseResource DatabaseResource { get; set; }

		/// <summary>
		/// 获取或设置业务引擎关联的设备模板信息
		/// </summary>
		public Dictionary<string, XElement> Templates { get; private set; }

		/// <summary>
		/// 添加一个新的模板资源到
		/// </summary>
		/// <param name="key">模板的关键字信息</param>
		/// <param name="element">模板设备的XML信息</param>
		public void AddTemplates( string key, XElement element )
		{
			if (!Templates.ContainsKey( key ))
			{
				Templates.Add( key, element );
			}
			else
			{
				logNet?.WriteError( "Template 资源加载冲突，资源标识符重复，无法添加重复的值：" + key );
			}
		}

		#endregion

		#region Get OEE

		private void AddOeeAnalysis( string deviceName, string tagName, OeeNode nodeOee )
		{
			if (string.IsNullOrEmpty( deviceName ))
			{
				this.logNet?.WriteError( "当前的设备为空，无法添加设备Hex报警分析引擎。" );
				return;
			}
			if (!analysisDevices.ContainsKey( deviceName ))
			{
				this.logNet?.WriteError( $"当前的设备[{deviceName}]不存在，无法添加设备OEE分析引擎。" );
				return;
			}
			analysisDevices[deviceName].AddIntegerOEE( tagName, nodeOee );
		}

		/// <summary>
		/// 根据指定的唯一的设备名称或是路径来获取OEE信息列表<br />
		/// Obtain the alarm list information according to the specified unique device name
		/// </summary>
		/// <param name="data">设备的名称</param>
		/// <returns>报警的列表信息</returns>
		[HslMqttApi( ApiTopic = "Oee/GetOees", Description = "根据指定的唯一的设备名称或是路径来获取OEE信息列表" )]
		public OperateResult<OEEItem[]> GetOee( string data )
		{
			if (string.IsNullOrEmpty( data ))
			{
				List<OEEItem> oEEItems = new List<OEEItem>( );
				foreach (var item in analysisDevices.Values)
				{
					oEEItems.AddRange( item.GetOEE( ) );
				}
				return OperateResult.CreateSuccessResult( oEEItems.ToArray( ) );
			}
			if (data.IsDevicePath( ))
			{
				List<OEEItem> oEEItems = new List<OEEItem>( );
				foreach (var item in analysisDevices.Values)
				{
					if (item.InSpecifiedPath( data.SplitDeviceNodes( ) ))
					{
						oEEItems.AddRange( item.GetOEE( ) );
					}
				}
				return OperateResult.CreateSuccessResult( oEEItems.ToArray( ) );
			}
			else
			{
				if (analysisDevices.ContainsKey( data ))
				{
					return OperateResult.CreateSuccessResult( analysisDevices[data].GetOEE( ).ToArray( ) );
				}
				return new OperateResult<OEEItem[]>( $"Device [{data}] is not exist!" );
			}
		}

		/// <summary>
		/// 处理系统的OEE状态信息，并生成OEE的数据报告
		/// </summary>
		/// <param name="deviceName">唯一的带路径的设备名称</param>
		/// <param name="tagName">节点数据信息</param>
		/// <param name="status">最新的状态信息</param>
		public void DealOeeStatus( string deviceName, string tagName, int status )
		{
			if (analysisDevices.ContainsKey( deviceName ))
			{
				analysisDevices[deviceName].DealOeeStatus( tagName, status );
			}
		}

		/// <summary>
		/// 重置OEE的状态
		/// </summary>
		public void ResetOee( )
		{
			foreach (var item in analysisDevices.Values)
			{
				item.ResetOeeStatus( );
			}
		}

		#endregion

		#region Offline Time

		/// <summary>
		/// 处理当前设备的状态信息，传入当前的设备的在线状态，然后进行自动分析
		/// </summary>
		/// <param name="deviceName">唯一的设备路径信息</param>
		/// <param name="status">在线状态，为true就是在线，为false就是离线</param>
		public void DealDeviceOnlineTimes( string deviceName, bool status )
		{
			if (analysisDevices.ContainsKey( deviceName ))
			{
				this.analysisDevices[deviceName].DealDeviceOnlineTimes( status );
			}
		}

		/// <summary>
		/// 获取指定设备的离线信息，默认为7天的离线信息，实际视服务器的配置参数决定
		/// </summary>
		/// <param name="data">设备的ID信息</param>
		/// <returns>离线信息</returns>
		[HslMqttApi( ApiTopic = "Time/GetDeviceOfflineInformation", Description = "获取指定设备的离线信息，默认为7天的离线信息，实际视服务器的配置参数决定" )]
		public OperateResult<JArray> GetDeviceOfflineInformation( string data )
		{
			if (analysisDevices.ContainsKey( data ))
			{
				return OperateResult.CreateSuccessResult( analysisDevices[data].GetDeviceOfflineInformation( ) );
			}
			return new OperateResult<JArray>( $"Device [{data}] is not exist!" );
		}

		#endregion

		#region Private Member

		private ILogNet logNet;
		private Dictionary<string, AdvanceAnalysisDevice> analysisDevices;

		#endregion

		#region Static Core

		/// <summary>
		/// 当前系统的业务引擎核心
		/// </summary>
		public static BusinessEngine Business { get; set; }

		#endregion

	}
}
