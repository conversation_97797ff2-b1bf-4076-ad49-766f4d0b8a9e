using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.LogNet;

namespace HslTechnology.Edge.DataBusiness.Log
{
	/// <summary>
	/// 网关的中间日志对象
	/// </summary>
	public class EdgeLog
	{
		public delegate void EdgeLoggerDelegate( string deviceID, string key, HslMessageDegree degree, string msg );

		public delegate void DeviceTelegramLoggerDelegate( string deviceID, string msg );

		/// <summary>
		/// 当前的网关的基础日志事件
		/// </summary>
		public event EdgeLoggerDelegate EdgeLogger;

		/// <summary>
		/// 记录当前的设备基本运行日志
		/// </summary>
		/// <param name="deviceID">设备的唯一ID</param>
		/// <param name="degree">消息等级</param>
		/// <param name="msg">消息内容</param>
		public void Logger( string deviceID, string key, HslMessageDegree degree, string msg )
		{
			this.EdgeLogger?.Invoke( deviceID, key, degree, msg );
		}

		/// <summary>
		/// 记录错误日志
		/// </summary>
		/// <param name="deviceID"></param>
		/// <param name="key"></param>
		/// <param name="msg"></param>
		public void WriteError( string deviceID, string key, string msg )
		{
			Logger(deviceID, key, HslMessageDegree.ERROR, msg );
		}

		/// <summary>
		/// 记录信息日志
		/// </summary>
		/// <param name="deviceID"></param>
		/// <param name="key"></param>
		/// <param name="msg"></param>
		public void WriteInfo( string deviceID, string key, string msg )
		{
			Logger( deviceID, key, HslMessageDegree.INFO, msg );
		}

		/// <summary>
		/// 记录警告的消息
		/// </summary>
		/// <param name="deviceID"></param>
		/// <param name="key"></param>
		/// <param name="msg"></param>
		public void WriteWarn( string deviceID, string key, string msg )
		{
			Logger( deviceID, key, HslMessageDegree.WARN, msg );
		}

		/// <summary>
		/// 记录调试的消息
		/// </summary>
		/// <param name="deviceID"></param>
		/// <param name="key"></param>
		/// <param name="msg"></param>
		public void WriteDebug( string deviceID, string key, string msg )
		{
			Logger( deviceID, key, HslMessageDegree.DEBUG, msg );
		}

		/// <summary>
		/// 记录异常的消息
		/// </summary>
		/// <param name="deviceID"></param>
		/// <param name="key"></param>
		/// <param name="msg"></param>
		public void WriteFatal( string deviceID, string key, string msg )
		{
			Logger( deviceID, key, HslMessageDegree.FATAL, msg );
		}

		/// <summary>
		/// 当前的设备通信电文的事件
		/// </summary>
		public event DeviceTelegramLoggerDelegate DeviceTelegramLogger;

		/// <summary>
		/// 记录设备的电文信息
		/// </summary>
		/// <param name="deviceID">设备唯一的ID</param>
		/// <param name="msg">电文内容</param>
		public void LoggerTelegram( string deviceID, string msg )
		{
			this.DeviceTelegramLogger?.Invoke( deviceID, msg );
		}

		/// <summary>
		/// 记录设备的电文信息
		/// </summary>
		/// <param name="deviceID">设备的唯一ID</param>
		/// <param name="key">电文的关键字</param>
		/// <param name="telegram">电文信息</param>
		/// <param name="logMsgFormatBinary">日志的记录是否按照二进制还是ASCII</param>
		public void LoggerTelegram( string deviceID, string key, byte[] telegram, bool logMsgFormatBinary )
		{
			HslMessageItem hslMessage = new HslMessageItem( );
			hslMessage.ThreadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
			hslMessage.KeyWord = key;
			hslMessage.Text = logMsgFormatBinary ? telegram.ToHexString( ' ' ) : SoftBasic.GetAsciiStringRender( telegram );
			hslMessage.Degree = HslMessageDegree.DEBUG;
			this.DeviceTelegramLogger?.Invoke( deviceID, hslMessage.ToString( ) );
		}

	}
}
