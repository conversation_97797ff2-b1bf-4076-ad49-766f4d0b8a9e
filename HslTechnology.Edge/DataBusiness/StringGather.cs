using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Concurrent;

namespace HslTechnology.Edge.DataBusiness
{
	/// <summary>
	/// 字符串聚合类，可以往里面丢多个字符串数据，然后获取其中一个字符串即可
	/// </summary>
	/// <typeparam name="T">每个字符串绑定的关键字</typeparam>
	public class StringGather<T> 
	{
		#region Constructor

		/// <inheritdoc cref="HslTechnology.Edge.Node.GroupNode.GroupNode"/>
		public StringGather( )
		{
			_data = new ConcurrentDictionary<T, string>( );
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 新增一个字符串对象信息
		/// </summary>
		/// <param name="key">关键字信息</param>
		/// <param name="value">增加的字符串数据信息</param>
		public void AddStringMsg( T key, string value )
		{
			if (string.IsNullOrEmpty( value ))
			{
				_data.TryRemove( key, out string remove );
			}
			else
			{
				_data.AddOrUpdate( key, value, ( k, oldValue ) => value );
			}
		}

		/// <summary>
		/// 获取第一个字符串的信息
		/// </summary>
		/// <returns>字符串数据</returns>
		public string GetDefaultStringMsg( )
		{
			return _data.FirstOrDefault( ).Value;
		}

		/// <summary>
		/// 清除当前所有的数据内容
		/// </summary>
		public void Clear( ) => _data.Clear( );

		#endregion

		#region Private Member

		private ConcurrentDictionary<T, string> _data;

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"StringGather<{typeof(T)}>";

		#endregion
	}
}
