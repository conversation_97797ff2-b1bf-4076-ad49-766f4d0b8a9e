using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;

namespace HslTechnology.Edge.DataBusiness.Database
{
	/// <summary>
	/// 数据库的基础类对象
	/// </summary>
	public class DatabaseCore
	{
		/// <summary>
		/// 打开数据库连接操作
		/// </summary>
		/// <returns>是否打开成功</returns>
		public virtual async Task<OperateResult> OpenDatabase( )
		{
			return await Task.FromResult( new OperateResult( ) );
		}

		/// <summary>
		/// 测试连接数据库的操作
		/// </summary>
		/// <returns>是否测试成功</returns>
		public virtual async Task<OperateResult> TestDatabase( )
		{
			return await Task.FromResult( new OperateResult( ) );
		}

		/// <summary>
		/// 针对SQL的命令报文，执行写入的操作信息
		/// </summary>
		/// <param name="sqlCommand">命令信息</param>
		/// <returns>返回成功的命令行数</returns>
		public virtual async Task<OperateResult<int>> ExecuteCommand( string sqlCommand )
		{
			return await Task.FromResult( new OperateResult<int>( ) );
		}



		protected IDbConnection connection;         // 命令信息
	}
}
