using HslCommunication.BasicFramework;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.DataBusiness.Database
{
	public class DatabaseResource
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">节点的配置信息</param>
		public DatabaseResource( XElement element )
		{
			this.databaseResource = new Dictionary<string, DatabaseCore>( );
			foreach (XElement item in element.Elements( nameof( NodeType.DatabaseNode ) ))
			{
				string name = item.Attribute( nameof( GroupNode.Name ) ).Value;
				DatabaseCore databaseCore = CreateDatabaseCore( item );
				if (databaseCore != null)
				{
					this.databaseResource.Add( name, databaseCore );
				}
				else
				{

				}
			}
		}

		/// <summary>
		/// 根据数据库的名称来获取数据库的执行对象
		/// </summary>
		/// <param name="alarmName">数据库的名称</param>
		/// <returns>报警的节点信息</returns>
		public DatabaseCore GetDatabaseCore( string database )
		{
			if (databaseResource == null) return null;
			if (databaseResource.ContainsKey( database )) return databaseResource[database];
			return null;
		}

		private Dictionary<string, DatabaseCore> databaseResource;                           // 所有报警节点的资源

		#region Static Helper

		/// <summary>
		/// 从XML的根里面获取到Database，并解析出报警资源
		/// </summary>
		/// <param name="element">配置节点信息</param>
		/// <returns>报警资源节点信息</returns>
		public static DatabaseResource PraseFromRootXml( XElement element )
		{
			foreach (var xmlNode in element.Elements( ))
			{
				if (xmlNode.Attribute( nameof( GroupNode.Name ) ).Value == GroupNode.RootDatabase)
				{
					return new DatabaseResource( xmlNode );
				}
			}
			return null;
		}

		/// <summary>
		/// 根据指定的元素，创建真实的数据库对象信息
		/// </summary>
		/// <param name="element">资源里的XML信息</param>
		/// <returns>真实的数据库对象</returns>
		public static DatabaseCore CreateDatabaseCore( XElement element )
		{
			DatabaseType databaseType = SoftBasic.GetEnumFromString<DatabaseType>( element.Attribute( nameof( DatabaseNodeNet.DbType ) ).Value );
			if (databaseType == DatabaseType.SQLServer)
			{
				NodeSqlServer nodeSqlServer = new NodeSqlServer( element );
				return new DatabaseSqlServer( nodeSqlServer );
			}
			return null;
		}

		#endregion
	}
}
