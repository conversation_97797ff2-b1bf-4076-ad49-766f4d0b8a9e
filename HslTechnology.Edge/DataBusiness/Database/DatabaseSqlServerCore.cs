using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Node.Database;
using HslCommunication.Core;
#if NETSTANDARD2_0 || NETSTANDARD2_1
using Microsoft.Data.SqlClient;
//using System.Data.SqlClient;
#else
using System.Data.SqlClient;
#endif
using HslCommunication;

namespace HslTechnology.Edge.DataBusiness.Database
{
	/// <summary>
	/// 基于SQL Server的数据路通信对象
	/// </summary>
	public class DatabaseSqlServer : DatabaseCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public DatabaseSqlServer( )
		{

		}

		/// <inheritdoc/>
		public DatabaseSqlServer( NodeSqlServer node )
		{
			// 为了支持多实例，屏蔽掉端口号信息，可以让端口号变负数
			string port = node.Port < 0 ? string.Empty : $",{node.Port}";
			this.nodeSqlServer = node;
			StringBuilder sb = new StringBuilder( $"Data Source={node.IpAddress}{port};Initial Catalog={node.Database};User ID={node.UserName};Password={node.Password};Encrypt=True;TrustServerCertificate=True;" );
			if (node.MultipleActiveResultSets)
			{
				sb.Append( "MultipleActiveResultSets=True;" );
			}
			this.sqlConnection = new SqlConnection( sb.ToString( ) );
			this.connection = this.sqlConnection;
		}

		#endregion

		#region Database Core Override

		/// <inheritdoc/>
		public async override Task<OperateResult> OpenDatabase( )
		{
			try
			{
				if (this.sqlConnection.State != System.Data.ConnectionState.Open)
					await sqlConnection.OpenAsync( );
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				return new OperateResult<int>( "SqlConnection open failed: " + ex.Message );
			}
		}

		/// <inheritdoc/>
		public override async Task<OperateResult> TestDatabase( )
		{
			OperateResult open = await OpenDatabase( );
			if (open.IsSuccess)
			{
				sqlConnection.Close( );
			}
			return open;
		}


		/// <inheritdoc/>
		public override async Task<OperateResult<int>> ExecuteCommand( string sqlCommand )
		{
			hybirdLock.Enter( );
			OperateResult open = await OpenDatabase( );
			if (!open.IsSuccess)
			{
				hybirdLock.Leave( );
				return OperateResult.CreateFailedResult<int>( open );
			}

			try
			{
				using (SqlCommand command = new SqlCommand( sqlCommand, this.sqlConnection ))
				{
					int count = await command.ExecuteNonQueryAsync( );
					hybirdLock.Leave( );
					return OperateResult.CreateSuccessResult( count );
				}
			} 
			catch (Exception ex)
			{
				hybirdLock.Leave( );
				return new OperateResult<int>( "ExecuteCommand failed: " + ex.Message );
			}
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"SqlServer[{nodeSqlServer.IpAddress}/{nodeSqlServer.Database}]";

		#endregion

		#region Private Member

		private SimpleHybirdLock hybirdLock = new SimpleHybirdLock( );
		private NodeSqlServer nodeSqlServer;
		private SqlConnection sqlConnection;

		#endregion

	}
}
