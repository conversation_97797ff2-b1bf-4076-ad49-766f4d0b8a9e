using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness
{
	/// <summary>
	/// 边缘网关的致命消息管理
	/// </summary>
	public class ListMessage
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="count">数据量多少的问题</param>
		public ListMessage( int count = 10 )
		{
			fatalMessages = new List<string>( );
			lock_obj = new object( );
			this.dataCount = count;
		}

		/// <summary>
		/// 新增加一个异常信息
		/// </summary>
		/// <param name="msg"></param>
		public void AddMessage( string msg )
		{
			lock (lock_obj)
			{
				while(fatalMessages.Count >= this.dataCount)
				{
					fatalMessages.RemoveAt( 0 );
				}

				fatalMessages.Add( msg );
			}
		}

		/// <summary>
		/// 获取所有的消息列表
		/// </summary>
		/// <returns>消息信息</returns>
		public string[] GetMessages( )
		{
			string[] array = null;
			lock (lock_obj)
			{
				array = fatalMessages.ToArray( );
			}
			return array;
		}



		private int dataCount = 10;                        // 数据个数多少
		private List<string> fatalMessages;
		private object lock_obj;
	}
}
