using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Alarm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.DataBusiness.Alarm
{
	/// <summary>
	/// 报警相关的资源类
	/// </summary>
	public class AlarmResource
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">节点的配置信息</param>
		public AlarmResource( XElement element )
		{
			this.alarmResource = new Dictionary<string, AlarmNode>( );
			this.alarmDatabaseNodes = new List<AlarmDatabaseNode>( );

			foreach (XElement item in element.Elements(  ))
			{
				if (item.Name == nameof( NodeType.AlarmNode ))
				{
					AlarmNode alarmNode = AlarmNode.CreateAlarmNodeFromXml( item );
					if (alarmNode != null)
					{
						if (alarmNode.AlarmType == AlarmType.Boolean ||
							alarmNode.AlarmType == AlarmType.Hex ||
							alarmNode.AlarmType == AlarmType.Integer)
						{
							List<AlarmDefinitionNode> alarmDefinitions = new List<AlarmDefinitionNode>( );
							foreach (var definition in item.Elements( nameof( AlarmDefinitionNode ) ))
							{
								alarmDefinitions.Add( new AlarmDefinitionNode( definition ) );
							}
							alarmNode.AlarmDefinitions = alarmDefinitions.ToArray( );
						}
					}
					this.alarmResource.Add( alarmNode.Name, alarmNode );
				}
				else if (item.Name == nameof( NodeType.AlarmDatabase ))
				{
					AlarmDatabaseNode node = new AlarmDatabaseNode( item );
					if (node != null)
						this.alarmDatabaseNodes.Add( node );
				}
			}
		}

		/// <summary>
		/// 根据报警的名称来获取报警节点内容
		/// </summary>
		/// <param name="alarmName">报警的名称</param>
		/// <returns>报警的节点信息</returns>
		public AlarmNode GetAlarmNode( string alarmName )
		{
			if (alarmResource == null) return null;
			if (alarmResource.ContainsKey( alarmName )) return alarmResource[alarmName];
			return null;
		}

		/// <summary>
		/// 获取当前系统里定义的报警存储资源信息
		/// </summary>
		/// <returns>报警资源信息</returns>
		public List<AlarmDatabaseNode> GetAlarmDatabases( ) => this.alarmDatabaseNodes;

		private Dictionary<string, AlarmNode> alarmResource;                           // 所有报警节点的资源
		private List<AlarmDatabaseNode> alarmDatabaseNodes;                            // 所有报警存储数据库的资源信息

		#region Static Helper

		/// <summary>
		/// 从XML的根里面获取到Alarm，并解析出报警资源
		/// </summary>
		/// <param name="element">配置节点信息</param>
		/// <returns>报警资源节点信息</returns>
		public static AlarmResource PraseFromRootXml( XElement element )
		{
			foreach (var xmlNode in element.Elements( ))
			{
				if (xmlNode.Attribute( nameof( GroupNode.Name ) ).Value == GroupNode.RootAlarm)
				{
					return new AlarmResource( xmlNode );
				}
			}
			return null;
		}

		#endregion
	}
}
