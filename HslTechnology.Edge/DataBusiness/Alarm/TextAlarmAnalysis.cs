using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Node.Alarm;

namespace HslTechnology.Edge.DataBusiness.Alarm
{
	/// <summary>
	/// 基于文本的报警信息
	/// </summary>
	public class TextAlarmAnalysis : AlarmBase
	{
		#region Contructor

		/// <summary>
		/// 实例化一个对象，需要指定分类，含义，使用的是统一的报警等级
		/// </summary>
		public TextAlarmAnalysis( string deviceName, string tagName ) : base( deviceName, tagName )
		{

		}

		#endregion

		#region Public Method

		/// <summary>
		/// 一串报警数组的内容，和16进制的方式其实是一致的
		/// </summary>
		/// <param name="message">新的报警数据</param>
		public void AnalysisAlarm( string message )
		{
			bool isChanged = false;
			int alarmCountTmp = 0;

			if(alarmPrevious != message)
			{
				if(!string.IsNullOrEmpty( message ))
				{
					// 发生了报警
					AlarmItem alarmItem     = AlarmItem.GetAlamItemWithUniqueId( );
					alarmItem.DeviceName    = this.DeviceName;
					alarmItem.TagName       = this.TagName;
					alarmItem.AlarmCode     = 1;
					alarmItem.Checked       = false;
					alarmItem.Degree        = AlarmDegree.Hint;
					alarmItem.AlarmContent  = message;
					alarmItem.StartTime     = HslTechnologyHelper.GetDateTimeNow( );
					alarmItem.Status        = AlarmStatus.Alarm;
					AddAlarm( alarmItem );
					OnAlarmStart?.Invoke( alarmItem );

					isChanged = true;
				}

				if(!string.IsNullOrEmpty( alarmPrevious ))
				{
					// 结束了报警
					AlarmItem item = RemoveAlarmBy( 1 );
					if (item != null && item.Status == AlarmStatus.Finish) OnAlarmFinish?.Invoke( item );
					isChanged = true;
				}
			}
			else if(!string.IsNullOrEmpty( message ))
			{
				// 持续报警
				AlarmItem item = ChangeAlarmStatusAlarm( 1, 0, out bool changed );
				if (changed && item != null) OnAlarmStart?.Invoke( item );
			}

			if (!string.IsNullOrEmpty( message ))
			{
				alarmCountTmp++;
			}

			alarmCurrent = alarmCountTmp;
			alarmPrevious = message;

			if (isChanged) OnAlarmChanged?.Invoke( AlarmCount );
		}

		#endregion

		#region Private Member

		private string alarmPrevious = string.Empty;                                     // 之前的值

		#endregion

	}
}
