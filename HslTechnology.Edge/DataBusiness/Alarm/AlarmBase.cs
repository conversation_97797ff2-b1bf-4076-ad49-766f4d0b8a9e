using HslCommunication.Core;
using HslTechnology.Edge.Node.Alarm;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness.Alarm
{
	/// <summary>
	/// 报警基础类
	/// </summary>
	public abstract class AlarmBase : IDisposable
	{
		#region Constructor

		/// <summary>
		/// 实例化一个报警的分析类基类对象
		/// </summary>
		/// <param name="deviceName">设备的名称</param>
		/// <param name="tagName">数据的标签信息</param>
		public AlarmBase( string deviceName, string tagName )
		{
			this.DeviceName = deviceName;
			this.TagName    = tagName;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 当前报警绑定的设备名称信息
		/// </summary>
		public string DeviceName { get; set; }

		/// <summary>
		/// 当前报警绑定的数据节点名称
		/// </summary>
		public string TagName { get; set; }

		/// <summary>
		/// 当前正在报警的数量
		/// </summary>
		public int AlarmCount => alarmCurrent;

		#endregion

		#region Event Handler

		/// <summary>
		/// 当报警结束的时候触发
		/// </summary>
		public Action<AlarmItem> OnAlarmFinish { get; set; }

		/// <summary>
		/// 当报警开始的时候触发
		/// </summary>
		public Action<AlarmItem> OnAlarmStart { get; set; }

		/// <summary>
		/// 当报警更改的时候触发
		/// </summary>
		public Action<int> OnAlarmChanged { get; set; }

		/// <summary>
		/// 获取可用于存储的数据信息
		/// </summary>
		/// <returns>报警信息</returns>
		public JArray GetCurrentJsonValue( )
		{
			JArray array = new JArray( );
			simpleHybird.Enter( );
			array = JArray.FromObject( alarmItems );
			simpleHybird.Leave( );
			return array;
		}

		/// <summary>
		/// 获得报警列表的备份数据信息，即使修改了报警信息，也不影响系统原来的报警内容
		/// </summary>
		/// <returns>报警的列表</returns>
		public List<AlarmItem> GetAlarms( )
		{
			List<AlarmItem> ret = new List<AlarmItem>( );
			simpleHybird.Enter( );
			for (int i = 0; i < alarmItems.Count; i++)
			{
				ret.Add( new AlarmItem( alarmItems[i] ) );
			}
			simpleHybird.Leave( );
			return ret;
		}

		#endregion

		#region IDisposable Support

		private bool disposedValue = false; // 要检测冗余调用

		/// <summary>
		/// 带冗余功能的释放对象
		/// </summary>
		/// <param name="disposing">是否释放</param>
		protected virtual void Dispose( bool disposing )
		{
			if (!disposedValue)
			{
				if (disposing)
				{
					simpleHybird?.Dispose( );
				}

				disposedValue = true;
			}
		}

		/// <inheritdoc cref="IDisposable.Dispose"/>
		public void Dispose( )
		{
			Dispose( true );
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 移除指定唯一编码的报警内容
		/// </summary>
		/// <param name="uniqueId">唯一的报警id信息</param>
		/// <returns>是否移除成功</returns>
		public bool AbortAlarm( long uniqueId )
		{
			bool move = false;
			AlarmItem alarmItem = null;
			simpleHybird.Enter( );
			for (int i = 0; i < alarmItems.Count; i++)
			{
				if (alarmItems[i].UniqueId == uniqueId)
				{
					alarmItems[i].FinishTime = DateTime.Now;
					alarmItems[i].Status = AlarmStatus.Abort;
					alarmItem = alarmItems[i];
					alarmItems.RemoveAt( i );
					move = true;
					break;
				}
			}
			simpleHybird.Leave( );
			if (alarmItem != null) OnAlarmFinish?.Invoke( alarmItem );
			return move;
		}

		#endregion

		#region Protect Method

		/// <summary>
		/// 新增一个报警信息
		/// </summary>
		/// <param name="alarm">报警的内容</param>
		protected void AddAlarm( AlarmItem alarm )
		{
			simpleHybird.Enter( );
			alarmItems.Add( alarm );
			simpleHybird.Leave( );
		}

		/// <summary>
		/// 根据报警的代号移除一个报警
		/// </summary>
		/// <param name="code">报警代号信息</param>
		/// <returns>移除的报警对象</returns>
		protected AlarmItem RemoveAlarmBy( int code )
		{
			AlarmItem item = null;
			simpleHybird.Enter( );

			for (int i = 0; i < alarmItems.Count; i++)
			{
				if (alarmItems[i].AlarmCode == code)
				{
					alarmItems[i].FinishTime = HslTechnologyHelper.GetDateTimeNow( );
					if (alarmItems[i].Status == AlarmStatus.Alarm)
						alarmItems[i].Status = AlarmStatus.Finish;
					item = alarmItems[i];
					alarmItems.RemoveAt( i );
					break;
				}
			}

			simpleHybird.Leave( );
			return item;
		}

		/// <summary>
		/// 当使用了延时报警时，需要更新当前的报警的状态信息
		/// </summary>
		/// <param name="code">报警的代号</param>
		/// <param name="delay">延迟信息</param>
		/// <param name="changed">是否报警状态从准备到报警变化</param>
		/// <returns>报警对象</returns>
		protected AlarmItem ChangeAlarmStatusAlarm( int code, int delay, out bool changed )
		{
			AlarmItem item = null;
			changed = false;
			simpleHybird.Enter( );

			for (int i = 0; i < alarmItems.Count; i++)
			{
				if (alarmItems[i].AlarmCode == code)
				{
					if((DateTime.Now - alarmItems[i].StartTime).TotalSeconds > delay)
					{
						if(alarmItems[i].Status == AlarmStatus.Prepared)
						{
							alarmItems[i].Status = AlarmStatus.Alarm;
							item = alarmItems[i];
							changed = true;
						}
					}
					break;
				}
			}

			simpleHybird.Leave( );
			return item;
		}

		/// <summary>
		/// 通过报警的代号信息来修改当前的报警内容
		/// </summary>
		/// <param name="code">报警代号信息</param>
		/// <param name="alarmContent">新的报警内容</param>
		/// <param name="alarmDegree">新的报警等级</param>
		protected void ChangeAlarmContentByCode( int code, string alarmContent, AlarmDegree alarmDegree )
		{
			simpleHybird.Enter( );

			for (int i = 0; i < alarmItems.Count; i++)
			{
				if (alarmItems[i].AlarmCode == code)
				{
					alarmItems[i].AlarmContent = alarmContent;
					alarmItems[i].Degree = alarmDegree;
					break;
				}
			}

			simpleHybird.Leave( );
		}

		/// <summary>
		/// 根据报警的节点定义信息，将当前的报警提升相关的等级
		/// </summary>
		/// <param name="code">报警的代号</param>
		/// <param name="definitionNode">报警节点定义</param>
		protected void RaiseAlarmDegree( int code, AlarmDefinitionNode definitionNode )
		{
			simpleHybird.Enter( );
			for (int i = 0; i < alarmItems.Count; i++)
			{
				if (alarmItems[i].AlarmCode == code)
				{
					alarmItems[i].RaiseAlarmDegree( definitionNode );
					break;
				}
			}
			simpleHybird.Leave( );
		}

		#endregion

		#region Private Member

		private List<AlarmItem> alarmItems = new List<AlarmItem>( );
		private SimpleHybirdLock simpleHybird = new SimpleHybirdLock( );
		/// <summary>
		/// 当前的报警数量
		/// </summary>
		protected int alarmCurrent = 0;                                               // 当前报警的总数量

		#endregion

		/// <summary>
		/// json的格式化内容
		/// </summary>
		public static Newtonsoft.Json.Formatting Formatting = Newtonsoft.Json.Formatting.None;
	}
}
