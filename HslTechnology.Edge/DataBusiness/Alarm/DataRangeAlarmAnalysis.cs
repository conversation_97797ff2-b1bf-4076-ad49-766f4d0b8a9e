using HslTechnology.Edge.Node.Alarm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness.Alarm
{
	/// <summary>
	/// 数据范围的报警
	/// </summary>
	public class DataRangeAlarmAnalysis : AlarmBase
	{
		#region Contructor

		/// <summary>
		/// 实例化一个对象，需要指定分类，含义，使用的是统一的报警等级
		/// </summary>
		public DataRangeAlarmAnalysis( string deviceName, string tagName, NodeDataRangeAlarm nodeDataRange ) : base( deviceName, tagName )
		{
			this.nodeDataRange = nodeDataRange;
			if (double.IsNaN( this.nodeDataRange.MaxMaxValue )) this.nodeDataRange.MaxMaxValue = double.MaxValue;
			if (double.IsNaN( this.nodeDataRange.MinMinValue )) this.nodeDataRange.MinMinValue = double.MinValue;
			if (double.IsNaN( this.nodeDataRange.MaxValue ))    this.nodeDataRange.MaxValue = this.nodeDataRange.MaxMaxValue;
			if (double.IsNaN( this.nodeDataRange.MinValue ))    this.nodeDataRange.MinValue = this.nodeDataRange.MinMinValue;
		}

		#endregion

		#region Public Method

		private bool IsDataAlarm( double value )
		{
			return value > this.nodeDataRange.MaxValue || value < this.nodeDataRange.MinValue;
		}

		private bool IsNeedChangeAlarmContent( double value )
		{
			if (value < this.nodeDataRange.MinValue && alarmPrevious > this.nodeDataRange.MaxValue) return true;
			if (value > this.nodeDataRange.MaxValue && alarmPrevious < this.nodeDataRange.MinValue) return true;
			if (value > this.nodeDataRange.MaxValue)
			{
				if (value > this.nodeDataRange.MaxMaxValue && alarmPrevious < this.nodeDataRange.MaxMaxValue) return true;
				if (value < this.nodeDataRange.MaxMaxValue && alarmPrevious > this.nodeDataRange.MaxMaxValue) return true;
			}
			else if (value < this.nodeDataRange.MinValue)
			{
				if (value > this.nodeDataRange.MinMinValue && alarmPrevious < this.nodeDataRange.MinMinValue) return true;
				if (value < this.nodeDataRange.MinMinValue && alarmPrevious > this.nodeDataRange.MinMinValue) return true;
			}

			return false;
		}

		private AlarmDegree GetAlarmDegree(double value )
		{
			if (value > this.nodeDataRange.MaxMaxValue)
				return this.nodeDataRange.SecondDegree;

			if (value > this.nodeDataRange.MaxValue)
				return this.nodeDataRange.Degree;

			if (value < this.nodeDataRange.MinMinValue)
				return this.nodeDataRange.SecondDegree;

			if (value < this.nodeDataRange.MinValue)
				return this.nodeDataRange.Degree;

			return this.nodeDataRange.Degree;
		}

		private string GetAlarmContent( double value )
		{
			if (value > this.nodeDataRange.MaxMaxValue)
				return this.nodeDataRange.MaxMaxAlarmContent;

			if (value > this.nodeDataRange.MaxValue)
				return this.nodeDataRange.MaxAlarmContent;

			if (value < this.nodeDataRange.MinMinValue)
				return this.nodeDataRange.MinMinAlarmContent;

			if (value < this.nodeDataRange.MinValue)
				return this.nodeDataRange.MinAlarmContent;

			return string.Empty;
		}

		/// <summary>
		/// 一串报警数组的内容，和16进制的方式其实是一致的
		/// </summary>
		/// <param name="value">数据值信息</param>
		public void AnalysisAlarm( double value )
		{
			bool isChanged = false;
			int alarmCountTmp = 0;

			if (IsDataAlarm( value ))
			{
				if (dealAlarmTimes == 0 || !IsDataAlarm( alarmPrevious ))
				{
					// 发生了报警
					AlarmItem alarmItem    = AlarmItem.GetAlamItemWithUniqueId( );
					alarmItem.DeviceName   = this.DeviceName;
					alarmItem.TagName      = this.TagName;
					alarmItem.AlarmCode    = 1;
					alarmItem.Checked      = false;
					alarmItem.Degree       = GetAlarmDegree( value );
					alarmItem.AlarmContent = GetAlarmContent( value );
					alarmItem.Status       = nodeDataRange.Delay <= 0 ? AlarmStatus.Alarm : AlarmStatus.Prepared;
					AddAlarm( alarmItem );
					if (nodeDataRange.Delay <= 0) OnAlarmStart?.Invoke( alarmItem );

					isChanged = true;
				}
				else
				{
					// 持续报警
					if (nodeDataRange.Delay > 0)
					{
						AlarmItem item = ChangeAlarmStatusAlarm( 1, nodeDataRange.Delay, out bool changed );
						item.FinishTime = HslTechnologyHelper.GetDateTimeNow( );
						if (changed && item != null) OnAlarmStart?.Invoke( item );
					}
					if (IsNeedChangeAlarmContent( value ))
					{
						ChangeAlarmContentByCode( 1, GetAlarmContent( value ), GetAlarmDegree( value ) );
					}
				}
			}
			else if (dealAlarmTimes != 0)
			{
				// 结束了报警
				AlarmItem item = RemoveAlarmBy( 1 );
				if (item != null && item.Status == AlarmStatus.Finish) OnAlarmFinish?.Invoke( item );
				isChanged = true;
			}

			if (dealAlarmTimes == 0) dealAlarmTimes = 1;   // 标记已经处理了一次
			if (IsDataAlarm( value )) alarmCountTmp++;
			alarmCurrent = alarmCountTmp;
			alarmPrevious = value;
			if (isChanged) OnAlarmChanged?.Invoke( AlarmCount );
		}

		#endregion

		#region Private Member

		private int dealAlarmTimes = 0;
		private NodeDataRangeAlarm nodeDataRange = null;                      // 报警的配置信息
		private double alarmPrevious = 0;                                     // 之前的值

		#endregion

	}
}
