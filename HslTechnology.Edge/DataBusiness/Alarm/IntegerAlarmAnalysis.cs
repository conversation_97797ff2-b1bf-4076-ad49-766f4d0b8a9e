using HslCommunication.BasicFramework;
using HslTechnology.Edge.Node.Alarm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness.Alarm
{
	/// <summary>
	/// 基于整数的报警信息
	/// </summary>
	public class IntegerAlarmAnalysis : AlarmBase
	{
		#region Contructor

		/// <summary>
		/// 实例化一个对象，需要指定分类，含义，使用的是统一的报警等级
		/// </summary>
		public IntegerAlarmAnalysis( string deviceName, string tagName, AlarmDefinitionNode[] alarmDefinitionNodes ) : base( deviceName, tagName )
		{
			alarmConfigs = new Dictionary<int, AlarmDefinitionNode>( );
			for (int i = 0; i < alarmDefinitionNodes.Length; i++)
			{
				alarmConfigs.Add( alarmDefinitionNodes[i].Code, alarmDefinitionNodes[i] );
			}
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 一串报警数组的内容，和16进制的方式其实是一致的
		/// </summary>
		/// <param name="error">报警的整数信息</param>
		public void AnalysisAlarm( int error )
		{
			bool isChanged = false;
			int alarmCountTmp = 0;

			if (error != alarmPrevious)
			{
				if (error != 0)
				{
					// 发生了报警
					if (alarmConfigs.ContainsKey( error ))
					{
						AlarmItem alarmItem    = AlarmItem.GetAlamItemWithUniqueId( );
						alarmItem.DeviceName   = this.DeviceName;
						alarmItem.TagName      = this.TagName;
						alarmItem.AlarmCode    = error;
						alarmItem.Checked      = false;
						alarmItem.Degree       = alarmConfigs[error].Degree;
						alarmItem.AlarmContent = alarmConfigs[error].Name;
						alarmItem.StartTime    = HslTechnologyHelper.GetDateTimeNow( );
						alarmItem.Status       = alarmConfigs[error].Delay <= 0 ? AlarmStatus.Alarm : AlarmStatus.Prepared;
						AddAlarm( alarmItem );
						if (alarmConfigs[error].Delay <= 0) OnAlarmStart?.Invoke( alarmItem );
					}
					isChanged = true;
				}

				if (alarmPrevious != 0)
				{
					// 结束了报警
					if (alarmConfigs.ContainsKey( alarmPrevious ))
					{
						AlarmItem item = RemoveAlarmBy( alarmPrevious );
						if (item != null && item.Status == AlarmStatus.Finish) OnAlarmFinish?.Invoke( item );
						isChanged = true;
					}
				}
			}
			else if (error != 0)
			{
				// 持续报警的信息
				if (alarmConfigs.ContainsKey( error ))
				{
					if (alarmConfigs[error].Delay > 0)
					{
						AlarmItem item = ChangeAlarmStatusAlarm( error, alarmConfigs[error].Delay, out bool changed );
						if (changed && item != null) OnAlarmStart?.Invoke( item );
					}

					if (alarmConfigs[error].DegreeErrorRaise > 0 ||
						alarmConfigs[error].DegreeHintRaise > 0 ||
						alarmConfigs[error].DegreeWarnRaise > 0)
					{
						RaiseAlarmDegree( error, alarmConfigs[error] );
					}
				}
			}

			if (error != 0)
			{
				alarmCountTmp++;
			}

			alarmCurrent = alarmCountTmp;
			alarmPrevious = error;

			if (isChanged)
			{
				OnAlarmChanged?.Invoke( AlarmCount );
			}
		}

		#endregion

		#region Private Member

		private Dictionary<int, AlarmDefinitionNode> alarmConfigs = null;     // 报警的配置信息
		private int alarmPrevious = 0;                                        // 之前报警的状态

		#endregion
	}
}
