using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslTechnology.Edge.Node.Alarm;

namespace HslTechnology.Edge.DataBusiness.Alarm
{
	/// <summary>
	/// bool数组报警和HEX报警公用的规则
	/// </summary>
	public class HexAlarmAnalysis : AlarmBase
	{
		#region Contructor

		/// <summary>
		/// 实例化一个对象，需要指定分类，含义，使用的是统一的报警等级
		/// </summary>
		/// <param name="deviceName">唯一的设备标签</param>
		/// <param name="tagName">关联的标签名称</param>
		/// <param name="alarmDefinitionNodes">报警细节的定义</param>
		/// <param name="reverseByWord">是否按照字为单位反转</param>
		public HexAlarmAnalysis( string deviceName, string tagName, AlarmDefinitionNode[] alarmDefinitionNodes, bool reverseByWord ) : base( deviceName, tagName )
		{
			this.hexReverseByWord = reverseByWord;
			this.alarmConfigs =  new Dictionary<int, AlarmDefinitionNode>( );
			for (int i = 0; i < alarmDefinitionNodes.Length; i++)
			{
				this.alarmConfigs.Add( alarmDefinitionNodes[i].Code, alarmDefinitionNodes[i] );
			}
		}

		#endregion

		#region Public Method

		private void ReverseBytes( byte[] buffer )
		{
			if (buffer == null) return;
			for (int i = 0; i < buffer.Length; i++)
			{
				if (i + 1 < buffer.Length)
				{
					// 可以置换
					byte tmp = buffer[i + 1];
					buffer[i + 1] = buffer[i];
					buffer[i] = tmp;
					i++;
				}
				else
				{
					break;
				}

			}
		}

		/// <summary>
		/// 分析一串传入的16进制的报警内容，例如 5A45BC918F
		/// </summary>
		/// <param name="hexString">hex表示的字符串信息</param>
		public void AnalysisAlarm( string hexString )
		{
			byte[] data      = SoftBasic.HexStringToBytes( hexString );
			if (hexReverseByWord) ReverseBytes( data );
			bool[] boolArray = SoftBasic.ByteToBoolArray( data );
			AnalysisAlarm( boolArray );
		}

		/// <summary>
		/// 分析一串传入的16进制的报警内容，例如 5A45BC918F
		/// </summary>
		/// <param name="byteArray">字节信息</param>
		public void AnalysisAlarm( byte[] byteArray )
		{
			if (hexReverseByWord) ReverseBytes( byteArray );
			bool[] boolArray = SoftBasic.ByteToBoolArray( byteArray );
			AnalysisAlarm( boolArray );
		}

		/// <summary>
		/// 一串报警数组的内容，和16进制的方式其实是一致的
		/// </summary>
		/// <param name="array">bool数组信息</param>
		public void AnalysisAlarm( bool[] array )
		{
			// 初始化或是
			if (alarmPrevious == null) alarmPrevious = new bool[array.Length];
			if (alarmPrevious.Length < array.Length) 
			{
				bool[] buffer = new bool[array.Length];
				alarmPrevious.CopyTo( buffer, 0 );
				alarmPrevious = buffer;
			}

			bool isChanged = false;
			int alarmCountTmp = 0;
			for (int i = 0; i < alarmPrevious.Length; i++)
			{
				if (i < array.Length)
				{
					if (!alarmConfigs.ContainsKey( i )) continue;

					if (array[i] && !alarmPrevious[i])
					{
						// 发生了报警
						AlarmItem alarmItem    = AlarmItem.GetAlamItemWithUniqueId( );
						alarmItem.DeviceName   = this.DeviceName;
						alarmItem.TagName      = this.TagName;
						alarmItem.AlarmCode    = i;
						alarmItem.Checked      = false;
						alarmItem.Degree       = alarmConfigs[i].Degree;
						alarmItem.AlarmContent = alarmConfigs[i].Name;
						alarmItem.StartTime    = DateTime.Now;
						alarmItem.Status       = alarmConfigs[i].Delay <= 0 ? AlarmStatus.Alarm : AlarmStatus.Prepared;
						AddAlarm( alarmItem );
						if (alarmConfigs[i].Delay <= 0) OnAlarmStart?.Invoke( alarmItem );
						isChanged = true;
					}
					else if (!array[i] && alarmPrevious[i])
					{
						// 结束了报警
						AlarmItem item = RemoveAlarmBy( i );
						if (item != null && item.Status == AlarmStatus.Finish) OnAlarmFinish?.Invoke( item );
						isChanged = true;
					}
					else if (array[i] && alarmPrevious[i])
					{
						// 持续报警的信息，如果报警被中断，则不发生任何事
						if (alarmConfigs[i].Delay > 0)
						{
							AlarmItem item = ChangeAlarmStatusAlarm( i, alarmConfigs[i].Delay, out bool changed );
							if (item != null && changed ) OnAlarmStart?.Invoke( item );
						}
						if(alarmConfigs[i].DegreeErrorRaise > 0 || 
							alarmConfigs[i].DegreeHintRaise > 0 ||
							alarmConfigs[i].DegreeWarnRaise > 0)
						{
							RaiseAlarmDegree( i, alarmConfigs[i] );
						}
					}

					if (array[i])
					{
						alarmCountTmp++;
					}

					alarmPrevious[i] = array[i];
				}
			}
			alarmCurrent = alarmCountTmp;

			if (isChanged)
			{
				OnAlarmChanged?.Invoke( AlarmCount );
			}
		}

		#endregion

		#region Private Member

		private Dictionary<int, AlarmDefinitionNode> alarmConfigs = null;     // 报警的配置信息
		private bool[] alarmPrevious = null;                                  // 之前报警的状态
		private bool hexReverseByWord = false;                                // 解析byte[]数组时，是否需要按照字为单位反转

		#endregion
	}
}
