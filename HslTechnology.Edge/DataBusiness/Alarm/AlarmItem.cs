using HslTechnology.Edge.Node.Alarm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using HslTechnology.Edge.Reflection;
using Newtonsoft.Json.Converters;

namespace HslTechnology.Edge.DataBusiness.Alarm
{
	/// <summary>
	/// 单个报警分析的对象，一个对象的实例表示一个独立的报警，报警依赖设备的唯一ID信息
	/// </summary>
	public class AlarmItem
	{
		#region Contructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public AlarmItem( )
		{
			this.StartTime = HslTechnologyHelper.GetDateTimeNow( );
			this.FinishTime = this.StartTime;
		}

		/// <summary>
		/// 从另一个报警的对象实例化，相当于拷贝的操作信息
		/// </summary>
		/// <param name="item">另一个报警对象</param>
		public AlarmItem( AlarmItem item )
		{
			this.uniqueId     = item.UniqueId;
			this.AlarmCode    = item.AlarmCode;
			this.AlarmContent = item.AlarmContent;
			this.StartTime    = item.StartTime;
			if (item.Status == AlarmStatus.Alarm)
				this.FinishTime = DateTime.Now;
			else
				this.FinishTime = item.FinishTime;
			this.Checked      = item.Checked;
			this.Degree       = item.Degree;
			this.Status       = item.Status;
			this.DeviceName   = item.DeviceName;
			this.TagName      = item.TagName;
		}

		/// <summary>
		/// 使用唯一的设备标识和报警描述信息来初始化报警
		/// </summary>
		/// <param name="deviceName">设备名称</param>
		/// <param name="alarmCode">报警的代号</param>
		/// <param name="alarmContent">报警的描述信息</param>
		public AlarmItem( string deviceName, int alarmCode, string alarmContent ) : this( )
		{
			this.DeviceName   = deviceName;
			this.AlarmCode    = alarmCode;
			this.AlarmContent = alarmContent;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 本次系统运行的唯一报警信息，用来标识操作的信息的
		/// </summary>
		public long UniqueId
		{
			get => uniqueId;
			set => this.uniqueId = value;
		}

		/// <summary>
		/// 报警的ID信息，在bool, integer, hex报警里作为区分的标记
		/// </summary>
		public int AlarmCode { get; set; }

		/// <summary>
		/// 报警的开始时间
		/// </summary>
		[JsonConverter( typeof( DateTimeFormatConverter ) )]
		public DateTime StartTime { get; set; }

		/// <summary>
		/// 报警的结束时间
		/// </summary>
		[JsonConverter( typeof( DateTimeFormatConverter ) )]
		public DateTime FinishTime { get; set; }

		/// <summary>
		/// 报警的内容
		/// </summary>
		public string AlarmContent { get; set; }

		/// <summary>
		/// 当前的报警是否被确认过
		/// </summary>
		public bool Checked { get; set; }

		/// <summary>
		/// 当前报警的等级
		/// </summary>
		[JsonConverter( typeof( StringEnumConverter ) )]
		public AlarmDegree Degree { get; set; }

		/// <summary>
		/// 报警的状态，表示当前的标记
		/// </summary>
		[JsonConverter( typeof( StringEnumConverter ) )]
		public AlarmStatus Status { get; set; }

		/// <summary>
		/// 当前报警所在的设备名称，带路径的唯一设备名称
		/// </summary>
		public string DeviceName { get; set; }

		/// <summary>
		/// 当前报警所绑定的数据标签名称
		/// </summary>
		public string TagName { get; set; }

		#endregion

		#region Public Method

		/// <summary>
		/// 根据节点的配置规则来提升当前的报警等级
		/// </summary>
		/// <param name="alarmDefinition">报警的定义</param>
		public void RaiseAlarmDegree( AlarmDefinitionNode alarmDefinition )
		{
			switch (Degree)
			{
				case AlarmDegree.Hint:
					if (alarmDefinition.DegreeHintRaise > 0)
						if ((DateTime.Now - StartTime).TotalSeconds > alarmDefinition.DegreeHintRaise)
							Degree = AlarmDegree.Warn;
					break;
				case AlarmDegree.Warn:
					if (alarmDefinition.DegreeWarnRaise > 0)
						if ((DateTime.Now - StartTime).TotalSeconds > alarmDefinition.DegreeWarnRaise)
							Degree = AlarmDegree.Error;
					break;
				case AlarmDegree.Error:
					if (alarmDefinition.DegreeErrorRaise > 0)
						if ((DateTime.Now - StartTime).TotalSeconds > alarmDefinition.DegreeErrorRaise)
							Degree = AlarmDegree.Fatal;
					break;
			}
		}

		#endregion

		#region Private Member

		private long uniqueId = 0;                              // 报警的唯一标识
		private static long AlarmIdCurrent = 0;

		#endregion

		#region Static Helper

		/// <summary>
		/// 获取一个新的<see cref="AlarmItem"/>对象，将会赋值一个唯一的报警ID信息
		/// </summary>
		/// <returns>报警对象信息</returns>
		public static AlarmItem GetAlamItemWithUniqueId( )
		{
			return new AlarmItem( )
			{
				uniqueId = Interlocked.Increment( ref AlarmIdCurrent )
			};
		}

		#endregion
	}
}
