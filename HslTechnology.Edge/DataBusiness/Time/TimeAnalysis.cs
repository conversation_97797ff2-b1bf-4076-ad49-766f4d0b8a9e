using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace HslTechnology.Edge.DataBusiness.Time
{
	/// <summary>
	/// 用于时间分析的类，主要用于分析设备的掉线次数，及每次掉线的时间，可以缓存一定的天数
	/// </summary>
	public class TimeAnalysis
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="deviceId">设备的id信息</param>
		public TimeAnalysis( string deviceId )
		{
			this.deviceId = deviceId;
			this.consumes = new List<TimeConsume>( );

		}

		/// <summary>
		/// 处理状态信息
		/// </summary>
		/// <param name="status">在线状态与否</param>
		public void DealStatus( bool status )
		{
			simpleHybird.Enter( );
			int statusNew = status ? 1 : 0;
			if (statusNew != this.statusPre ) // 状态发送了变更
			{
				if(statusNew == 0) // 产生了新的离线信息
				{
					TimeConsume consume = new TimeConsume( );
					consume.StartTime   = DateTime.Now;
					consume.FinishTime  = consume.StartTime;

					consumes.Add( consume );
					RemoveOutdateValue( );
				}
				else if (statusNew == 1 && this.statusPre == 0) // 结束了旧的离线信息
				{
					if (consumes.Count > 0)
					{
						consumes[consumes.Count - 1].FinishTime = DateTime.Now;
					}
					RemoveOutdateValue( );
				}
			}
			else if(statusNew == 0)
			{
				if (consumes.Count > 0)
				{
					consumes[consumes.Count - 1].FinishTime = DateTime.Now;
				}
			}
			this.statusPre = statusNew;
			simpleHybird.Leave( );
		}

		/// <summary>
		/// 获取所有的状态信息
		/// </summary>
		/// <returns>所有的掉线信息</returns>
		public JArray GetAllStatus( )
		{
			JArray jArray = null;
			simpleHybird.Enter( );
			RemoveOutdateValue( );
			jArray = JArray.FromObject( consumes );
			simpleHybird.Leave( );
			return jArray;
		}

		private void RemoveOutdateValue( )
		{
			while (consumes.Count > 0)
			{
				if (consumes[0].FinishTime < DateTime.Now.Date.AddDays( 0 - cacheDays ))
				{
					consumes.RemoveAt( 0 );
				}
				else
				{
					break;
				}
			}
		}

		#region Private Member

		private int statusPre = -1;                              // 状态信息
		private List<TimeConsume> consumes;                      // 所有时间消耗的列表
		private string deviceId = string.Empty;                  // 当前的设备的唯一id信息
		private int cacheDays = 7;                               // 缓存的天数信息
		//private int cacheMaxCount = 1000;                        // 最大缓存的数量
		private SimpleHybirdLock simpleHybird = new SimpleHybirdLock( );

		#endregion
	}
}
