using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using System.IO;
using HslCommunication.LogNet;

namespace HslTechnology.Edge.DataBusiness.Time
{
	/// <summary>
	/// 服务器分析运行时间的类
	/// </summary>
	public class EdgeRunTimeAnalysis
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public EdgeRunTimeAnalysis( string filePath )
		{
			this.lockTime = new object( );
			this.filePath = filePath;

			LoadFromFile( );
			AddRuntime( );
		}

		#endregion

		private void LoadFromFile( )
		{
			try
			{
				if (File.Exists( filePath ))
					this.timeConsumes = JArray.Parse( File.ReadAllText( this.filePath, Encoding.UTF8 ) ).ToObject<List<EdgeTimeConsume>>( );
				else
					this.timeConsumes = new List<EdgeTimeConsume>( );

				if (this.timeConsumes == null) this.timeConsumes = new List<EdgeTimeConsume>( );
				for (int i = 0; i < this.timeConsumes.Count; i++)
				{
					if (this.timeConsumes[i].Quit == 0x02)
					{
						this.timeConsumes[i].Quit = 0x00;
						if (string.IsNullOrEmpty( this.timeConsumes[i].Message ))
						{
							this.timeConsumes[i].Message = "Unknown, 可能来自直接终止程序或是断电重启！";
						}
					}
				}
			}
			catch (Exception)
			{
				this.timeConsumes = new List<EdgeTimeConsume>( );
			}
		}

		/// <summary>
		/// 增加最新的运行时间记录，如果最后一个信息为
		/// </summary>
		private void AddRuntime( )
		{
			lock( this.lockTime)
			{
				EdgeTimeConsume edgeTimeConsume = new EdgeTimeConsume( );
				edgeTimeConsume.StartTime       = HslTechnologyHelper.GetDateTimeNow( );
				edgeTimeConsume.FinishTime      = HslTechnologyHelper.GetDateTimeNow( );
				edgeTimeConsume.Quit            = 0x02;

				// 把最后一个时间信息置空
				if (this.timeConsumes.Count > 0)
				{
					if (this.timeConsumes[this.timeConsumes.Count - 1].Quit == 0x02)
						this.timeConsumes[this.timeConsumes.Count - 1].Quit = 0x00;
				}
				this.timeConsumes.Add( edgeTimeConsume );
				while(this.timeConsumes.Count > 10)
				{
					this.timeConsumes.RemoveAt( 0 );
				}
			}
		}

		/// <summary>
		/// 更新内部的缓存时间信息，应该是每秒调用一次
		/// </summary>
		public void UpdateCache( )
		{
			lock (this.lockTime)
			{
				// 把最后一个时间更新到最新
				if (this.timeConsumes.Count > 0)
				{
					this.timeConsumes[this.timeConsumes.Count - 1].FinishTime = HslTechnologyHelper.GetDateTimeNow( );
				}
			}
		}

		/// <summary>
		/// 每分钟调用一次更新下当前的缓存中，最后一个数据信息
		/// </summary>
		public void UpdateFile( )
		{
			UpdateCache( );
			try
			{
				File.WriteAllText( filePath, JArray.FromObject( this.timeConsumes ).ToString( ), Encoding.UTF8 );
			}
			catch
			{

			}
		}

		/// <summary>
		/// 当系统手动退出的时候调用本方法，然后当前的时间消耗存储到本地的文件里去
		/// </summary>
		/// <param name="message">手动关闭的消息内容</param>
		public void Quit( string message )
		{
			lock (this.lockTime)
			{
				// 把最后一个时间更新到最新
				if (this.timeConsumes.Count > 0)
				{
					this.timeConsumes[this.timeConsumes.Count - 1].FinishTime = HslTechnologyHelper.GetDateTimeNow( );
					this.timeConsumes[this.timeConsumes.Count - 1].Quit = 0x01;
					this.timeConsumes[this.timeConsumes.Count - 1].Message = message;
				}

				try
				{
					File.WriteAllText( filePath, JArray.FromObject( this.timeConsumes ).ToString( ), Encoding.UTF8 );
				}
				catch
				{

				}
			}
		}

		/// <summary>
		/// 获取当前的所有的时间的消耗信息，获取的是副本数组
		/// </summary>
		/// <returns>时间消耗</returns>
		public EdgeTimeConsume[] GetEdgeTimes( )
		{
			EdgeTimeConsume[] array;
			lock (this.lockTime)
			{
				array = this.timeConsumes.ToArray( );
			}
			return array;
		}


		#region Private Member

		private string filePath = string.Empty;                 // 配置文件的存储路径
		private List<EdgeTimeConsume> timeConsumes;             // 当前缓存的列表信息
		private object lockTime;                                // 操作队列的锁

		#endregion
	}
}
