using HslTechnology.Edge.Reflection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness.Time
{
	/// <summary>
	/// 时间长度类
	/// </summary>
	public class TimeConsume
	{
		/// <summary>
		/// 开始时间
		/// </summary>
		[JsonConverter( typeof( DateTimeFormatConverter ) )]
		public DateTime StartTime { get; set; }

		/// <summary>
		/// 结束时间
		/// </summary>
		[JsonConverter( typeof( DateTimeFormatConverter ) )]
		public DateTime FinishTime { get; set; }
	}
}
