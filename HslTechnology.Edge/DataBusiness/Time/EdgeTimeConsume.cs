using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness.Time
{
	public class EdgeTimeConsume : TimeConsume
	{
		/// <summary>
		/// 是否正常的退出，0表示异常退出，没有任何记录，1表示正常关闭退出，2表示当前正在运行中
		/// </summary>
		public int Quit { get; set; }

		/// <summary>
		/// 退出时的消息记录，标记退出的方式
		/// </summary>
		public string Message { get; set; }

		/// <summary>
		/// 克隆一个对象信息
		/// </summary>
		/// <returns>边缘网关的时间类</returns>
		public EdgeTimeConsume Clone( )
		{
			return new EdgeTimeConsume( )
			{
				StartTime  = this.StartTime,
				FinishTime = this.FinishTime,
				Quit       = this.Quit,
				Message    = this.Message
			};
		}

		/// <inheritdoc/>
		public override string ToString( )
		{
			return $"Start: {StartTime.ToEdgeString( )} " +
				(Quit == 2 ? "Running" : Quit == 1 ? $"Quit:{FinishTime.ToEdgeString( )}" : $"Abort:{FinishTime.ToEdgeString( )}");
		}
	}
}
