using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslCommunication.Core;
using Newtonsoft.Json.Linq;
using HslCommunication.BasicFramework;
using HslTechnology.Edge;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.DataBusiness.Oee;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.Edge.DataBusiness.Time;
using HslTechnology.Edge.Device.Base;

namespace HslTechnology.Edge.DataBusiness
{
	/// <summary>
	/// 一个设备的所有报警信息的分析
	/// </summary>
	public class AdvanceAnalysisDevice
	{
		/// <summary>
		/// 实例化一个默认的设备
		/// </summary>
		public AdvanceAnalysisDevice( string deviceName )
		{
			this.hexAlarms       = new Dictionary<string, HexAlarmAnalysis>( );
			this.integeAlarms    = new Dictionary<string, IntegerAlarmAnalysis>( );
			this.textAlarms      = new Dictionary<string, TextAlarmAnalysis>( );
			this.dataRangeAlarms = new Dictionary<string, DataRangeAlarmAnalysis>( );
			this.oeeAnalysis     = new Dictionary<string, IntegerOEEAnalysis>( );
			this.timeAnalysis    = new TimeAnalysis( deviceName );
			//this.hybirdLock      = new SimpleHybirdLock( );
			this.alarmJsonCount  = new JObject( );
			this.deviceName      = deviceName;
			string[] nodes = deviceName.SplitDeviceNodes( );
			if (nodes.Length > 0) DeviceShortName = nodes[nodes.Length - 1];
			DeviceNodes = nodes;
		}

		#region Event Handle

		/// <summary>
		/// 表示当前设备的名称，有两个名称，一个是带路径的完整名称，还有一个是设备的名称
		/// </summary>
		public string DeviceShortName { get; set; }

		/// <summary>
		/// 当前设备的名称信息，携带完整的路径的信息
		/// </summary>
		public string DeviceName => this.deviceName;

		/// <inheritdoc cref="DeviceCoreBase.DeviceNodes"/>
		public string[] DeviceNodes { get; set; }

		/// <summary>
		/// 触发报警完成的事件
		/// </summary>
		public event Action<string, AlarmItem> OnAlarmFinishEvent;

		/// <summary>
		/// 触发报警开始的事件
		/// </summary>
		public event Action<string, AlarmItem> OnAlarmStartEvent;

		/// <summary>
		/// 当报警结束的时候触发
		/// </summary>
		private void OnAlarmFinish( AlarmItem alarmItem )
		{
			OnAlarmFinishEvent?.Invoke( this.deviceName, alarmItem );
		}

		/// <summary>
		/// 当报警开始的时候触发
		/// </summary>
		private void OnAlarmStart( AlarmItem alarmItem )
		{
			OnAlarmStartEvent?.Invoke( this.deviceName, alarmItem );
		}

		#endregion

		#region Public Method

		/// <inheritdoc cref="DeviceCoreBase.IsCurrentDevice(string[])"/>
		public bool IsCurrentDevice( string[] nodes )
		{
			if (DeviceNodes != null && nodes != null)
			{
				if (nodes.Length < DeviceNodes.Length) return false;
				for (int i = 0; i < DeviceNodes.Length; i++)
				{
					if (DeviceNodes[i] != nodes[i])
					{
						return false;
					}
				}
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <inheritdoc cref="DeviceCoreBase.InSpecifiedPath(string[])"/>
		public bool InSpecifiedPath( string[] paths )
		{
			if (DeviceNodes != null && paths != null)
			{
				if (paths.Length != DeviceNodes.Length - 1) return false;
				for (int i = 0; i < DeviceNodes.Length - 1; i++)
				{
					if (DeviceNodes[i] != paths[i])
					{
						return false;
					}
				}
				return true;
			}
			else
			{
				return false;
			}
		}

		#endregion

		#region Hex Alarm

		/// <summary>
		/// 新增新的HEX报警的信息，统一的报警等级
		/// </summary>
		/// <param name="tagName">设备数据的名称</param>
		/// <param name="alarmDefinitionNodes">报警的基本详细信息</param>
		/// <param name="reverseByWord">解析的字节信息是否根据字单位进行反转</param>
		public void AddHexAlarms( string tagName, AlarmDefinitionNode[] alarmDefinitionNodes, bool reverseByWord )
		{
			if (hexAlarms.ContainsKey( tagName )) hexAlarms.Remove( tagName );
			HexAlarmAnalysis hexAlarm = new HexAlarmAnalysis( deviceName, tagName, alarmDefinitionNodes, reverseByWord );
			hexAlarm.OnAlarmFinish = OnAlarmFinish;
			hexAlarm.OnAlarmStart = OnAlarmStart;
			hexAlarms.Add( tagName, hexAlarm );
		}

		/// <summary>
		/// 处理Hex的报警信息，传入hex字符串，或是byte[], 或是bool[]
		/// </summary>
		/// <param name="tagName">数据节点名称</param>
		/// <param name="hexString">传入的分析数据信息</param>
		public void DealHexData( string tagName, string hexString )
		{
			if (hexAlarms.ContainsKey( tagName ))
			{
				hexAlarms[tagName].AnalysisAlarm( hexString );
			}
		}

		/// <inheritdoc cref="DealHexData(string, string)"/>
		public void DealHexData( string tagName, byte[] byteArray )
		{
			if (hexAlarms.ContainsKey( tagName ))
			{
				hexAlarms[tagName].AnalysisAlarm( byteArray );
			}
		}

		/// <inheritdoc cref="DealHexData(string, string)"/>
		public void DealHexData( string tagName, bool[] boolArray )
		{
			if (hexAlarms.ContainsKey( tagName ))
			{
				hexAlarms[tagName].AnalysisAlarm( boolArray );
			}
		}

		#endregion

		#region Integer Alarm

		/// <summary>
		/// 新增一个基于整数的报警内容
		/// </summary>
		/// <param name="tagName">节点名称信息</param>
		/// <param name="alarmDefinitionNodes">报警信息的详细定义</param>
		public void AddIntegerAlarm( string tagName, AlarmDefinitionNode[] alarmDefinitionNodes )
		{
			if (integeAlarms.ContainsKey( tagName )) integeAlarms.Remove( tagName );
			IntegerAlarmAnalysis integerAlarm = new IntegerAlarmAnalysis( deviceName, tagName, alarmDefinitionNodes );
			integerAlarm.OnAlarmStart = OnAlarmStart;
			integerAlarm.OnAlarmFinish = OnAlarmFinish;
			integeAlarms.Add( tagName, integerAlarm );
		}

		/// <summary>
		/// 处理基于整数的报警内容信息
		/// </summary>
		/// <param name="tagName">节点名</param>
		/// <param name="alarmCode"></param>
		public void DealIntegerData( string tagName, int alarmCode )
		{
			if (integeAlarms.ContainsKey( tagName ))
			{
				integeAlarms[tagName].AnalysisAlarm( alarmCode );
			}
		}

		#endregion

		#region Text Alarm

		/// <summary>
		/// 新增一个文本的报警信息
		/// </summary>
		/// <param name="tagName">节点名</param>
		public void AddTextAlarm( string tagName )
		{
			if (textAlarms.ContainsKey( tagName )) textAlarms.Remove( tagName );
			TextAlarmAnalysis textAlarm = new TextAlarmAnalysis( this.deviceName, tagName );
			textAlarm.OnAlarmStart = OnAlarmStart;
			textAlarm.OnAlarmFinish = OnAlarmFinish;
			textAlarms.Add( tagName, textAlarm );
		}

		/// <summary>
		/// 处理文字报警的相关的
		/// </summary>
		/// <param name="tagName">节点信息</param>
		/// <param name="alarmCode">报警的内容</param>
		public void DealTextData( string tagName, string alarmCode )
		{
			if (textAlarms.ContainsKey( tagName ))
			{
				textAlarms[tagName].AnalysisAlarm( alarmCode );
			}
		}

		#endregion

		#region DataRange Alarm

		/// <summary>
		/// 新增一个数据范围的报警内容
		/// </summary>
		/// <param name="tagName">数据标签信息</param>
		/// <param name="nodeDataRange">报警的数据节点信息</param>
		public void AddDataRangeAlarm( string tagName, NodeDataRangeAlarm nodeDataRange )
		{
			if (dataRangeAlarms.ContainsKey( tagName )) dataRangeAlarms.Remove( tagName );

			DataRangeAlarmAnalysis dataRangeAlarm = new DataRangeAlarmAnalysis( this.deviceName, tagName, nodeDataRange );
			dataRangeAlarm.OnAlarmStart = OnAlarmStart;
			dataRangeAlarm.OnAlarmFinish = OnAlarmFinish;
			dataRangeAlarms.Add( tagName, dataRangeAlarm );
		}

		/// <summary>
		/// 处理数据范围报警的功能
		/// </summary>
		/// <param name="tagName">标签名</param>
		/// <param name="value">处理的数据值</param>
		public void DealDataRange(string tagName, double value )
		{
			if (dataRangeAlarms.ContainsKey( tagName ))
			{
				if(tagName == "内温")
				{
					;
				}
				dataRangeAlarms[tagName].AnalysisAlarm( value );
			}
		}

		#endregion

		/// <inheritdoc cref="AlarmBase.AbortAlarm(long)"/>
		public bool AbortAlarm( long uniqueId )
		{
			foreach (var item in hexAlarms.Values)
				if (item.AbortAlarm( uniqueId ))
					return true;
			foreach (var item in integeAlarms.Values)
				if (item.AbortAlarm( uniqueId ))
					return true;
			foreach (var item in textAlarms.Values)
				if (item.AbortAlarm( uniqueId ))
					return true;
			foreach (var item in dataRangeAlarms.Values)
				if (item.AbortAlarm( uniqueId ))
					return true;
			return false;
		}

		/// <summary>
		/// 获取报警数据统计的方法，包含总的报警数量以及各个报警等级的数量
		/// </summary>
		/// <returns>json对象 { "AlarmCount" : 30, "Hint" : 20 }</returns>
		public JObject GetAlarmJsonCount( )
		{
			if ((DateTime.Now - lastTimeGetAlarmJson).TotalSeconds < 1)
				return alarmJsonCount;
			JObject json = new JObject( );
			List<AlarmItem> alarms = GetAlarms( );
			int count = 0;
			int hintCount = 0;
			int warnCount = 0;
			int errorCount = 0;
			int fatalCount = 0;
			for (int i = 0; i < alarms.Count; i++)
			{
				if (alarms[i].Degree == AlarmDegree.Hint)
					hintCount++;
				else if (alarms[i].Degree == AlarmDegree.Warn)
					warnCount++;
				else if (alarms[i].Degree == AlarmDegree.Error)
					errorCount++;
				else if (alarms[i].Degree == AlarmDegree.Fatal)
					fatalCount++;
			}
			count = hintCount + warnCount + errorCount + fatalCount;
			json.Add( "AlarmCount", new JValue( count ) );
			json.Add( nameof( AlarmDegree.Hint), new JValue( hintCount ) );
			json.Add( nameof( AlarmDegree.Warn), new JValue( warnCount ) );
			json.Add( nameof( AlarmDegree.Error), new JValue( errorCount ) );
			json.Add( nameof( AlarmDegree.Fatal ), new JValue( fatalCount ) );

			alarmJsonCount = json;
			lastTimeGetAlarmJson = DateTime.Now;
			return json;
		}

		/// <summary>
		/// 获取当前设备所有的报警信息列表
		/// </summary>
		/// <returns>报警列表信息</returns>
		public List<AlarmItem> GetAlarms( )
		{
			List<AlarmItem> alarms = new List<AlarmItem>( );
			foreach (var item in hexAlarms.Values)
				alarms.AddRange( item.GetAlarms( ) );
			foreach (var item in integeAlarms.Values)
				alarms.AddRange( item.GetAlarms( ) );
			foreach (var item in textAlarms.Values)
				alarms.AddRange( item.GetAlarms( ) );
			foreach (var item in dataRangeAlarms.Values)
				alarms.AddRange( item.GetAlarms( ) );
			return alarms;
		}

		#region OEE About


		/// <summary>
		/// 触发报警完成的事件
		/// </summary>
		public event Action<string, IntegerOEEAnalysis, OEEItem> OnOEEAfterReset;

		/// <summary>
		/// 重置状态信息
		/// </summary>
		public void ResetOeeStatus( )
		{
			foreach (var item in oeeAnalysis)
			{
				item.Value.StatusResetCheck( );
			}
		}

		// 重置前触发事件
		private void AfterReset( IntegerOEEAnalysis oEEAnalysis, OEEItem oEEItem )
		{
			OnOEEAfterReset?.Invoke( this.deviceName, oEEAnalysis, oEEItem );
		}

		/// <summary>
		/// 获取当前的状态分析列表
		/// </summary>
		/// <returns>OEE信息列表</returns>
		public List<OEEItem> GetOEE( )
		{
			List<OEEItem> list = new List<OEEItem>( );
			foreach (var item in oeeAnalysis.Values)
				list.Add( item.GetOEEAnalysis( )) ;
			return list;
		}

		/// <summary>
		/// 新增一个OEE处理的信息
		/// </summary>
		/// <param name="tagName">分类ID信息</param>
		/// <param name="oeeNode">OeeNode的节点定义信息</param>
		public void AddIntegerOEE( string tagName, OeeNode oeeNode )
		{
			if (oeeAnalysis.ContainsKey( tagName )) oeeAnalysis.Remove( tagName );
			IntegerOEEAnalysis oEEAnalysis = new IntegerOEEAnalysis( deviceName, tagName, oeeNode );
			oEEAnalysis.AfterOeeReset = AfterReset;
			oeeAnalysis.Add( tagName, oEEAnalysis );
		}

		/// <summary>
		/// 根据分类的标识，来处理OEE的状态变更
		/// </summary>
		/// <param name="tagName">分类ID信息</param>
		/// <param name="status">最新的状态信息</param>
		public void DealOeeStatus( string tagName, int status )
		{
			if (oeeAnalysis.ContainsKey( tagName )) oeeAnalysis[tagName].AnalysisStatus( status );
		}

		/// <inheritdoc cref="DealOeeStatus(string, int)"/>
		public void DealOeeStatus( string tagName, bool status )
		{
			if (oeeAnalysis.ContainsKey( tagName )) oeeAnalysis[tagName].AnalysisStatus( status );
		}


		#endregion

		#region Time Analysis

		/// <summary>
		/// 处理当前设备的状态信息，传入当前的设备的在线状态，然后进行自动分析
		/// </summary>
		/// <param name="status">在线状态，为true就是在线，为false就是离线</param>
		public void DealDeviceOnlineTimes( bool status )
		{
			this.timeAnalysis.DealStatus( status );
		}

		/// <summary>
		/// 获取当前设备的所有的离线信息
		/// </summary>
		/// <returns>jarry类型的信息</returns>
		public JArray GetDeviceOfflineInformation( )
		{
			return this.timeAnalysis.GetAllStatus( );
		}

		#endregion

		private string deviceName = string.Empty;
		
		// 报警相关的信息
		private Dictionary<string, HexAlarmAnalysis>         hexAlarms = null;
		private Dictionary<string, IntegerAlarmAnalysis>     integeAlarms = null;
		private Dictionary<string, TextAlarmAnalysis>        textAlarms = null;
		private Dictionary<string, DataRangeAlarmAnalysis>   dataRangeAlarms = null;
		private Dictionary<string, IntegerOEEAnalysis>       oeeAnalysis = null;
		private TimeAnalysis                                 timeAnalysis = null;

		private JObject alarmJsonCount;
		private DateTime lastTimeGetAlarmJson = DateTime.Now.AddSeconds( -10 );
	}
}
