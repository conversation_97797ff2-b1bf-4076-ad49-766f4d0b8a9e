using HslTechnology.Edge.Node.Oee;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness.Oee
{
	/// <summary>
	/// 单个的OEE状态的消耗时间
	/// </summary>
	public class ItemStatusConsume
	{
		/// <summary>
		/// 状态码信息
		/// </summary>
		public int StatusCode { get; set; }

		/// <summary>
		/// 当前状态的文本描述信息
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// 总计消耗的时间
		/// </summary>
		public double Consume { get; set; }

		/// <summary>
		/// 当前的节点信息是否属于工作时间，如果是工作时间，则当前的状态都会记录到有效工作时间
		/// </summary>
		public bool IsWorkingTime { get; set; }

		/// <inheritdoc/>
		public override string ToString( ) => $"ItemStatusConsume[{StatusCode}]";

		/// <summary>
		/// 克隆当前的对象信息
		/// </summary>
		/// <returns>单个状态的数据类</returns>
		public ItemStatusConsume Clone( )
		{
			return new ItemStatusConsume( )
			{
				Consume       = this.Consume,
				StatusCode    = this.StatusCode,
				Name          = this.Name,
				IsWorkingTime = this.IsWorkingTime
			};
		}

		/// <summary>
		/// 拷贝状态时间消耗信息的列表内容
		/// </summary>
		/// <param name="consumes">时间状态消耗列表</param>
		/// <returns>深度拷贝后的结果</returns>
		public static ItemStatusConsume[] Copy( ItemStatusConsume[] consumes )
		{
			if (consumes == null) return null;
			ItemStatusConsume[] itemStatuses = new ItemStatusConsume[consumes.Length];
			for (int i = 0; i < itemStatuses.Length; i++)
			{
				itemStatuses[i] = consumes[i].Clone( );
			}
			return itemStatuses;
		}

		/// <summary>
		/// 根据节点的配置信息来生成状态时间消耗列表
		/// </summary>
		/// <param name="definitionNodes">节点定义信息</param>
		/// <returns>时间消耗的列表</returns>
		public static ItemStatusConsume[] CreateInstanceFrom( OeeDefinitionNode[] definitionNodes )
		{
			ItemStatusConsume[] itemStatuses = new ItemStatusConsume[definitionNodes.Length];
			for (int i = 0; i < itemStatuses.Length; i++)
			{
				itemStatuses[i]               = new ItemStatusConsume( );
				itemStatuses[i].StatusCode    = definitionNodes[i].Code;
				itemStatuses[i].Name          = definitionNodes[i].Name;
				itemStatuses[i].IsWorkingTime = definitionNodes[i].IsWorkingTime;
				itemStatuses[i].Consume    = 0;
			}
			return itemStatuses;
		}
	}
}
