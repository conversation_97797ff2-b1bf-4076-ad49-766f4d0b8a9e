using HslTechnology.Edge.Node.Oee;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness.Oee
{
	public class ShiftItemConsume
	{
		/// <summary>
		/// 当前状态的文本描述信息
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// 总计消耗的时间
		/// </summary>
		public double Consume { get; set; }

		/// <summary>
		/// 开始的时间点
		/// </summary>
		public int Start{ get; set; }

		/// <summary>
		/// 结束的时间点
		/// </summary>
		public int End{ get; set; }

		/// <summary>
		/// 判断当前给定的时间是否是区域内的时间
		/// </summary>
		/// <param name="second">一天中的秒信息</param>
		/// <returns>是否是区域的时间</returns>
		public bool IsSecondsInPeriod( int second )
		{
			if (End >= Start)
			{
				return Start <= second && second < End;
			}
			else
			{
				return second >= Start || second < End;
			}
		}

		public string GetWorkName( int paintWidth )
		{
			if (paintWidth >= 120)
				return Name + "(" + OeeTimePeriod.GetTimeText( Start ) + "-" + OeeTimePeriod.GetTimeText( End ) + ")";
			else
				return Name;
		}

		public int GetWorkingPercent(  )
		{
			if (End >= Start)
			{
				int div = End - Start;
				if (div == 0) div = 1;
				return Convert.ToInt32( Consume * 100 / div );
			}
			else
			{
				int div = End + 3600 * 24 - Start;
				if (div == 0) div = 1;
				return Convert.ToInt32( Consume * 100 / div );
			}
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"ShiftItemConsume[{Consume}]";

		/// <summary>
		/// 克隆当前的对象信息
		/// </summary>
		/// <returns>单个状态的数据类</returns>
		public ShiftItemConsume Clone( )
		{
			return new ShiftItemConsume( )
			{
				Consume  = this.Consume,
				Name     = this.Name,
				Start    = this.Start,
				End      = this.End,
			};
		}

		/// <summary>
		/// 拷贝状态时间消耗信息的列表内容
		/// </summary>
		/// <param name="consumes">时间状态消耗列表</param>
		/// <returns>深度拷贝后的结果</returns>
		public static ShiftItemConsume[] Copy( ShiftItemConsume[] consumes )
		{
			if (consumes == null) return null;
			ShiftItemConsume[] itemStatuses = new ShiftItemConsume[consumes.Length];
			for (int i = 0; i < itemStatuses.Length; i++)
			{
				itemStatuses[i] = consumes[i].Clone( );
			}
			return itemStatuses;
		}


		/// <summary>
		/// 根据节点的配置信息来生成状态时间消耗列表
		/// </summary>
		/// <param name="definitionNodes">节点定义信息</param>
		/// <returns>时间消耗的列表</returns>
		public static ShiftItemConsume[] CreateInstanceFrom( OeeTimePeriod[] definitionNodes )
		{
			ShiftItemConsume[] itemStatuses = new ShiftItemConsume[definitionNodes.Length];
			for (int i = 0; i < itemStatuses.Length; i++)
			{
				itemStatuses[i] = new ShiftItemConsume( );
				itemStatuses[i].Name = definitionNodes[i].ShiftName;
				itemStatuses[i].Start = definitionNodes[i].StartSecond;
				itemStatuses[i].End = definitionNodes[i].EndSecond;
				itemStatuses[i].Consume = 0;
			}
			return itemStatuses;
		}
	}
}
