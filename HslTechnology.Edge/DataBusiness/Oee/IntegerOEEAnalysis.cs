using HslCommunication.Core;
using HslTechnology.Edge.Node.Oee;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication.LogNet;

namespace HslTechnology.Edge.DataBusiness.Oee
{
	/// <summary>
	/// OEE分析的类
	/// </summary>
	public class IntegerOEEAnalysis : IDisposable
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="deviceName">设备的唯一ID</param>
		/// <param name="tagName">绑定的节点名称</param>
		/// <param name="oeeNode">已经定义的OEE列表信息</param>
		public IntegerOEEAnalysis( string deviceName, string tagName, OeeNode oeeNode )
		{
			this.oeeNode        = oeeNode;              // oee的节点信息
			this.tagName        = tagName;
			this.oee            = OEEItem.CreateIntanceByOeeNode( oeeNode.OeeDefinitions, oeeNode.OeeTimePeriodsRuntime );
			this.oee.TagName    = tagName;
			this.oee.DeviceName = deviceName;

			DateTime time = HslTechnologyHelper.GetDateTimeNow( ).Date.AddSeconds( oeeNode.ResetTime );
			while (time < DateTime.Now)
			{
				time = time.AddDays( 1 );
			}
			this.nextResetTime = time;
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 分析状态信息，这是一个整数的状态，每个值代表了不同的含义
		/// </summary>
		/// <param name="statusCode">状态代号</param>
		public void AnalysisStatus( int statusCode )
		{
			simpleHybird.Enter( );
			oee.CalculateOeeStatus( statusCode );
			simpleHybird.Leave( );
		}

		/// <inheritdoc cref="AnalysisStatus(int)"/>
		public void AnalysisStatus( bool statusCode )
		{
			simpleHybird.Enter( );
			oee.CalculateOeeStatus( statusCode ? 1 : 0 );
			simpleHybird.Leave( );
		}

		/// <summary>
		/// 检查是否需要进行OEE重置操作，如果达到指定时间，即触发重置操作
		/// </summary>
		public void StatusResetCheck( )
		{
			DateTime dateTimeNow = HslTechnologyHelper.GetDateTimeNow( );
			if (dateTimeNow >= this.nextResetTime)
			{
				// 刚好过重置时间，一般周期是一天，需要存储上一天的OEE情况，然后数据保存
				simpleHybird.Enter( );
				OEEItem item = oee.ResetOeeStatus( );
				this.nextResetTime = this.nextResetTime.AddDays( 1 );
				simpleHybird.Leave( );
				this.AfterOeeReset?.Invoke( this, item );
			}
		}

		/// <summary>
		/// 在重置前执行的委托内容
		/// </summary>
		public Action<IntegerOEEAnalysis, OEEItem> AfterOeeReset { get; set; }

		/// <summary>
		/// 获取当前的OEE结果分析对象信息
		/// </summary>
		/// <returns>OEE对象</returns>
		public OEEItem GetOEEAnalysis( )
		{
			OEEItem ret;
			simpleHybird.Enter( );
			ret = oee.Clone( );
			simpleHybird.Leave( );
			return ret;
		}

		#endregion

		#region Private Member

		private SimpleHybirdLock simpleHybird = new SimpleHybirdLock( );
		private string tagName = "";                                                  // 用户的信息
		private DateTime nextResetTime = HslTechnologyHelper.GetDateTimeNow( );       // 下次状态重置的时间
		private OEEItem oee;                                                          // 当前的oee数据分析对象
		private OeeNode oeeNode = null;                            

		#endregion

		#region IDisposable Support

		private bool disposedValue = false; // 要检测冗余调用

		/// <inheritdoc cref="IDisposable.Dispose"/>
		protected virtual void Dispose( bool disposing )
		{
			if (!disposedValue)
			{
				if (disposing)
				{
					simpleHybird.Dispose( );
				}

				disposedValue = true;
			}
		}

		// 添加此代码以正确实现可处置模式。
		/// <inheritdoc cref="IDisposable.Dispose"/>
		public void Dispose( )
		{
			Dispose( true );
		}
		#endregion
	}
}
