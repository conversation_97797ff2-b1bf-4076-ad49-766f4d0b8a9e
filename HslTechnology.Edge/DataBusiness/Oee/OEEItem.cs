using HslTechnology.Edge.Node.Oee;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.DataBusiness.Oee
{
	/// <summary>
	/// OEE分析的单个对象信息
	/// </summary>
	public class OEEItem
	{
		/// <summary>
		/// 当前报警所在的设备名称，带路径的唯一设备名称
		/// </summary>
		public string DeviceName { get; set; }

		/// <summary>
		/// 当前的OEE绑定的数据标签名
		/// </summary>
		public string TagName { get; set; }

		/// <summary>
		/// 当前的设备状态信息
		/// </summary>
		public int OeeStatus { get; set; }

		/// <summary>
		/// 获取或设置当前工作中的总时长
		/// </summary>
		public double WorkingSecondsCount { get; set; }

		/// <summary>
		/// 持续的秒的时间的总和
		/// </summary>
		public double SecondsCount { get; set; }

		/// <summary>
		/// 当前的稼动率的百分比
		/// </summary>
		public double OeePercent { get; set; }

		/// <summary>
		/// 重置前的稼动率，当前的状态占重置前所有的时间的总和的百分比
		/// </summary>
		public double LastOeePercent { get; set; }

		/// <summary>
		/// 当前的各个状态的时间消耗信息
		/// </summary>
		public ItemStatusConsume[] StatusConsumes { get; set; }

		/// <summary>
		/// 重置前的各个状态的时间消耗信息
		/// </summary>
		public ItemStatusConsume[] LastStatusConsumes { get; set; }

		/// <summary>
		/// 当前各个班次的时间情况
		/// </summary>
		public ShiftItemConsume[] ShiftConsumes { get; set; }

		/// <summary>
		/// 重置前的各个班次的时间情况
		/// </summary>
		public ShiftItemConsume[] LastShiftConsumes{ get; set; }

		/// <summary>
		/// 克隆当前的数据对象信息
		/// </summary>
		/// <returns>OEE对象</returns>
		public OEEItem Clone( )
		{
			OEEItem item             = new OEEItem( );
			item.DeviceName          = this.DeviceName;
			item.TagName             = this.TagName;
			item.OeeStatus           = this.OeeStatus;
			item.WorkingSecondsCount = this.WorkingSecondsCount;
			item.SecondsCount        = this.SecondsCount;
			item.OeePercent          = this.OeePercent;
			item.LastOeePercent      = this.LastOeePercent;
			item.StatusConsumes      = ItemStatusConsume.Copy( StatusConsumes );
			item.LastStatusConsumes  = ItemStatusConsume.Copy( LastStatusConsumes );
			if (ShiftConsumes != null) item.ShiftConsumes = ShiftItemConsume.Copy( ShiftConsumes );
			if (LastShiftConsumes != null) item.LastShiftConsumes = ShiftItemConsume.Copy( LastShiftConsumes );
			return item;
		}

		/// <summary>
		/// 根据最新的OEE状态，计算当前的OEE状态信息
		/// </summary>
		/// <param name="statusCode">最新的OEE状态信息</param>
		public void CalculateOeeStatus( int statusCode )
		{
			DateTime dateTimeNow = HslTechnologyHelper.GetDateTimeNow( );
			double workSeconds = 0d;
			for (int i = 0; i < this.StatusConsumes.Length; i++)
			{
				if (this.StatusConsumes[i].StatusCode == statusCode)
				{
					this.StatusConsumes[i].Consume += (dateTimeNow - lastDealTime).TotalSeconds;
					if (this.StatusConsumes[i].IsWorkingTime)
					{
						workSeconds = (dateTimeNow - lastDealTime).TotalSeconds;
						this.WorkingSecondsCount += workSeconds;
					}
					break;
				}
			}
			// 如果有班次信息的话，还要根据班次分别计算
			if (ShiftConsumes?.Length > 0 && workSeconds > 0)
			{
				for (int i = 0; i < ShiftConsumes.Length; i++)
				{
					ShiftItemConsume item = ShiftConsumes[i];
					if (item.IsSecondsInPeriod( OeeTimePeriod.GetSecondsFromDateTime( lastDealTime ) ))
					{
						item.Consume += workSeconds;
					}
				}
			}


			// 计算当前一整天的OEE稼动率
			this.SecondsCount      = (dateTimeNow - this.lastResetTime).TotalSeconds;
			this.OeePercent        = SecondsCount > 0 ? Math.Round( this.WorkingSecondsCount / SecondsCount, 4 ) : 0;
			this.lastDealTime      = dateTimeNow;
			this.OeeStatus         = statusCode;
		}

		/// <summary>
		/// 重置当前的OEE状态信息
		/// </summary>
		public OEEItem ResetOeeStatus( )
		{
			CalculateOeeStatus( OeeStatus );
			this.LastStatusConsumes = ItemStatusConsume.Copy( StatusConsumes );
			this.LastOeePercent     = OeePercent;

			for (int i = 0; i < this.StatusConsumes.Length; i++)
			{
				this.StatusConsumes[i].Consume = 0;
			}

			if (ShiftConsumes?.Length > 0)
			{
				this.LastShiftConsumes = ShiftItemConsume.Copy( ShiftConsumes );
				for (int i = 0; i < this.ShiftConsumes.Length; i++)
				{
					this.ShiftConsumes[i].Consume = 0;
				}
			}


			this.WorkingSecondsCount = 0;
			this.SecondsCount        = 0;
			this.OeePercent          = 0;
			this.lastResetTime       = HslTechnologyHelper.GetDateTimeNow( );
			return Clone( );
		}

		private DateTime lastDealTime  = HslTechnologyHelper.GetDateTimeNow( );       // 上次的处理时间
		private DateTime lastResetTime = HslTechnologyHelper.GetDateTimeNow( );       // 上次的状态重置的时间

		///// <summary>
		///// 克隆一个对象
		///// </summary>
		///// <returns>克隆后的对象信息</returns>
		//public OEEItem Clone( )
		//{
		//	return new OEEItem( )
		//	{
		//		StatusCode   = this.StatusCode,
		//		Description  = this.Description,
		//		SecondsCount = this.SecondsCount,
		//		StopedNum    = this.StopedNum,
		//	};
		//}

		/// <summary>
		/// 构建一个OEE数据对象，用来客户端的数据获取。
		/// </summary>
		/// <param name="definitionNodes">OEE的节点定义</param>
		/// <returns>OEE数据对象</returns>
		public static OEEItem CreateIntanceByOeeNode( OeeDefinitionNode[] definitionNodes, OeeTimePeriod[] timePeriods )
		{
			OEEItem item              = new OEEItem( );
			item.WorkingSecondsCount  = 0;
			item.OeeStatus            = -1;
			item.SecondsCount         = 0;
			item.OeePercent           = 0;
			item.LastOeePercent       = 0;
			item.StatusConsumes       = ItemStatusConsume.CreateInstanceFrom( definitionNodes );
			item.LastStatusConsumes   = ItemStatusConsume.CreateInstanceFrom( definitionNodes );
			if (timePeriods?.Length > 0)
			{
				// 如果设置了班组信息的话，这里就实例化班组的内容
				item.ShiftConsumes     = ShiftItemConsume.CreateInstanceFrom( timePeriods );
				item.LastShiftConsumes = ShiftItemConsume.CreateInstanceFrom( timePeriods );
			}
			return item;
		}
	}
}
