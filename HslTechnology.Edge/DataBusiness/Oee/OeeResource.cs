using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Oee;

namespace HslTechnology.Edge.DataBusiness.Oee
{
	/// <summary>
	/// OEE的资源类信息
	/// </summary>
	public class OeeResource
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">节点的配置信息</param>
		public OeeResource( XElement element )
		{
			this.alarmResource = new Dictionary<string, OeeNode>( );
			foreach (XElement item in element.Elements( nameof( NodeType.OeeNode ) ))
			{
				OeeNode oeeNode = new OeeNode( item );
				List<OeeDefinitionNode> oeeDefinitions = new List<OeeDefinitionNode>( );
				foreach (var definition in item.Elements( nameof( NodeType.DataOeeDefinitionNode ) ))
				{
					oeeDefinitions.Add( new OeeDefinitionNode( definition ) );
				}
				oeeNode.OeeDefinitions = oeeDefinitions.ToArray( );
				this.alarmResource.Add( oeeNode.Name, oeeNode );
			}
		}

		/// <summary>
		/// 根据报警的名称来获取报警节点内容
		/// </summary>
		/// <param name="alarmName">报警的名称</param>
		/// <returns>报警的节点信息</returns>
		public OeeNode GetOeeNode( string alarmName )
		{
			if (alarmResource.ContainsKey( alarmName )) return alarmResource[alarmName];
			return null;
		}

		private Dictionary<string, OeeNode> alarmResource;                           // 所有OEE节点的资源

		#region Static Helper

		/// <summary>
		/// 从XML的根里面获取到Oee节点信息，并解析出报警资源
		/// </summary>
		/// <param name="element">配置节点信息</param>
		/// <returns>Oee资源节点信息</returns>
		public static OeeResource PraseFromRootXml( XElement element )
		{
			foreach (var xmlNode in element.Elements( ))
			{
				if (xmlNode.Attribute( nameof( GroupNode.Name ) ).Value == GroupNode.RootOEE)
				{
					return new OeeResource( xmlNode );
				}
			}
			return null;
		}

		#endregion
	}
}
