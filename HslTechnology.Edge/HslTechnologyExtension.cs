using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge
{
	/// <summary>
	/// 静态的扩展辅助方法
	/// </summary>
	public static class HslTechnologyExtension
	{
		private static char[] deviceSplitChars = new char[] { '\\', '/' };

		/// <summary>
		/// 所有设备默认的分割字符串
		/// </summary>
		public readonly static string DeviceDefaultSplit = "/";

		public static string GetDevicePathFromDeviceID( string deviceID )
		{
			string[] splits = deviceID.Split( deviceSplitChars, StringSplitOptions.RemoveEmptyEntries );
			if (splits == null || splits.Length < 2) return DeviceDefaultSplit;
			if (splits.Length == 2) return splits[0];
			StringBuilder sb = new StringBuilder( );
			for (int i = 0; i < splits.Length - 1; i++)
			{
				sb.Append( splits[i] );
				if (i < splits.Length - 2)
					sb.Append( HslTechnologyExtension.DeviceDefaultSplit );
			}
			return sb.ToString( );
		}

		/// <summary>
		/// 将普通的字符串数组的节点打包为一个完整的用于服务器请求或是显示的路径
		/// </summary>
		/// <param name="nodes">字符串节点的信息</param>
		/// <returns>用于显示或是请求服务器的完整的路径</returns>
		public static string ContactStringArrayPath( string[] nodes, bool isPath )
		{
			StringBuilder sb = new StringBuilder( );
			if (nodes.Length > 0)
			{
				for (int i = 0; i < nodes.Length; i++)
				{
					sb.Append( nodes[i] );
					if (isPath || i < nodes.Length - 1) sb.Append( HslTechnologyExtension.DeviceDefaultSplit );
				}
			}
			else
				sb.Append( HslTechnologyExtension.DeviceDefaultSplit );
			return sb.ToString( );
		}

		/// <summary>
		/// 将当前的字符串数据，按照设备分割字符分割成路径数组
		/// </summary>
		/// <param name="input">输入的字符串内容</param>
		/// <returns>分割之后的路径信息</returns>
		public static string[] SplitDeviceNodes( this string input )
		{
			if (string.IsNullOrEmpty( input )) return new string[0];
			return input.Split( deviceSplitChars, StringSplitOptions.RemoveEmptyEntries );
		}

		/// <summary>
		/// 判断当前的字符串是否是设备节点的路径信息
		/// </summary>
		/// <param name="input">输入的字符串内容</param>
		/// <returns>True 表示是路径信息，反之，则不是</returns>
		public static bool IsDevicePath( this string input )
		{
			return input.EndsWith( "\\" ) || input.EndsWith( "/" ) || input.EndsWith( "." );
		}

		/// <summary>
		/// 将时间的数据转变为实际的字符串的格式，年月日，时分秒。
		/// </summary>
		/// <param name="dateTime">时间</param>
		/// <returns>字符串的格式</returns>
		public static string ToEdgeString( this DateTime dateTime )
		{
			return dateTime.ToString( "yyyy-MM-dd HH:mm:ss" );
		}
	}
}
