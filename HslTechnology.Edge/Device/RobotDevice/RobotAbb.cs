using System;
using System.Collections.Generic;
using System.Net.NetworkInformation;
using System.Text;
using System.Xml.Linq;
using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Reflection;
using HslCommunication.Robot.ABB;
using HslTechnology.Edge.Node.Device;
using System.Threading.Tasks;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.RobotDevice
{
	/// <summary>
	/// Abb机器人的设备类对象
	/// </summary>
	public class RobotAbb : RobotCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Abb机器人的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public RobotAbb( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeRobot      = new NodeRobotAbb( element );
			this.webApiClient   = new ABBWebApiClient( nodeRobot.IpAddress, nodeRobot.Port, nodeRobot.UserName, nodeRobot.Password );
			this.ByteTransform  = new HslCommunication.Core.RegularByteTransform( );
			this.RobotNetDevice = this.webApiClient;
			this.UniqueId       = HslCommunication.BasicFramework.SoftBasic.GetUniqueStringByGuidAndRandom( );
			this.RegisterRpcService( deviceResources.MqttServer, deviceResources.HttpServer, nodeRobot, webApiClient );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) { }

		/// <inheritdoc/>
		protected override void AfterClose( ) { }

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.webApiClient == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			using (Ping ping = new Ping( ))
			{
				IPStatus status = ping.Send( nodeRobot.IpAddress ).Status;

				return status == IPStatus.Success ?
					OperateResult.CreateSuccessResult( ToString( ) ) :
					new OperateResult<string>( "Ping Wrong:" + status.ToString( ) );
			}
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="ABBWebApiClient.GetCtrlState"/>
		[HslMqttApi( Description = "获取当前的控制状态\r\nGet the current control state" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetCtrlState( ) => await this.webApiClient.GetCtrlStateAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetErrorState"/>
		[HslMqttApi( Description = "获取当前的错误状态\r\nGets the current error state" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetErrorState( ) => await this.webApiClient.GetErrorStateAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetJointTarget"/>
		[HslMqttApi( Description = "获取当前机器人的物理关节点信息，返回json格式的关节信息\r\nGet the physical node information of the current robot and return the joint information in json format" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetJointTarget( ) => await this.webApiClient.GetJointTargetAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetSpeedRatio"/>
		[HslMqttApi( Description = "获取当前机器人的速度配比信息\r\nGet the speed matching information of the current robot" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetSpeedRatio( ) => await this.webApiClient.GetSpeedRatioAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetOperationMode"/>
		[HslMqttApi( Description = "获取当前机器人的工作模式\r\nGets the current working mode of the robot" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetOperationMode( ) => await this.webApiClient.GetOperationModeAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetIOIn"/>
		[HslMqttApi( Description = "获取当前机器人的本机的输入IO\r\nGets the input IO of the current robot's native" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetIOIn( ) => await this.webApiClient.GetIOInAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetIOOut"/>
		[HslMqttApi( Description = "获取当前机器人的本机的输出IO\r\nGets the output IO of the current robot's native" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetIOOut( ) => await this.webApiClient.GetIOOutAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetIO2In"/>
		[HslMqttApi( Description = "获取当前机器人的本机的输入IO2\r\nGets the input IO2 of the current robot's native" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetIO2In( ) => await this.webApiClient.GetIO2InAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetIO2Out"/>
		[HslMqttApi( Description = "获取当前机器人的本机的输出IO2\r\nGets the output IO2 of the current robot's native" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetIO2Out( ) => await this.webApiClient.GetIO2OutAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetLog(int)"/>
		[HslMqttApi( Description = "获取当前机器人的日志记录，默认记录为10条\r\nGets the log record for the current robot, which is 10 by default" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetLog( int logCount = 10 ) => await this.webApiClient.GetLogAsync( logCount );

		/// <inheritdoc cref="ABBWebApiClient.GetSystem"/>
		[HslMqttApi( Description = "取当前机器人的系统信息，版本号，唯一ID等信息\r\nGet the current robot's system information, version number, unique ID and other information" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetSystem( ) => await this.webApiClient.GetSystemAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetRobotTarget"/>
		[HslMqttApi( Description = "获取机器人的目标坐标信息\r\nGet the current robot's target information" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetRobotTarget( ) => await this.webApiClient.GetRobotTargetAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetServoEnable"/>
		[HslMqttApi( Description = "获取当前机器人的伺服使能状态\r\nGet the current robot servo enable state" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetServoEnable( ) => await this.webApiClient.GetServoEnableAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetRapidExecution"/>
		[HslMqttApi( Description = "获取当前机器人的当前程序运行状态\r\nGet the current program running status of the current robot" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetRapidExecution( ) => await this.webApiClient.GetRapidExecutionAsync( );

		/// <inheritdoc cref="ABBWebApiClient.GetRapidTasks"/>
		[HslMqttApi( Description = "获取当前机器人的任务列表\r\nGet the task list of the current robot" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> GetRapidTasks( ) => await this.webApiClient.GetRapidTasksAsync( );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[RobotAbbWebApi] [{GetDeviceNameWithPath( )}] [{nodeRobot.GetSocketInfo( )}]";

		#endregion

		#region Private

		private ABBWebApiClient webApiClient;                 // abb机器人的连接对象
		private NodeRobotAbb nodeRobot;                       // 节点信息

		#endregion
	}
}
