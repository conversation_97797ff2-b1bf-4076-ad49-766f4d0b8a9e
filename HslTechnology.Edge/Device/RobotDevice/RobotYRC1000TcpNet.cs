using HslCommunication;
using HslCommunication.Reflection;
using HslCommunication.Robot.YASKAWA;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device.Robot;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.RobotDevice
{
	/// <summary>
	/// 安川机器人的通信对象
	/// </summary>
	public class RobotYRC1000TcpNet : RobotCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个安川机器人的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public RobotYRC1000TcpNet( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeRobot = new NodeRobotYRC1000TcpNet( element );

			this.robot                = new YRC1000TcpNet( nodeRobot.IpAddress, nodeRobot.Port );
			this.robot.Type           = nodeRobot.Type;
			this.robot.ConnectTimeOut = nodeRobot.ConnectTimeOut;
			this.robot.ReceiveTimeOut = nodeRobot.ReceiveTimeOut;

			this.ByteTransform        = this.robot.ByteTransform;
			this.RobotNetDevice       = this.robot;
			this.UniqueId             = this.robot.ConnectionId;
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => robot?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => robot?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.robot == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = robot.ConnectServer( );
			if (connect.IsSuccess) robot.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="YRC1000TcpNet.ReadByCommand(string, string)"/>
		[HslMqttApi( Description = "根据指令来读取设备的信息，如果命令数据为空，则传入null即可，注意，所有的命令不带换行符\r\ncommand 命令的内容RALARM/RPOSJ/RJSEQ\r\ncommandData 命令数据内容" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadByCommand( string command, string commandData ) => await this.robot.ReadByCommandAsync( command, commandData );

		/// <inheritdoc cref="YRC1000TcpNet.ReadALARMAsync"/>
		[HslMqttApi( Description = "读取机器人的报警信息" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadALARM( ) => await this.robot.ReadALARMAsync( );

		/// <inheritdoc cref="YRC1000TcpNet.ReadPOSJ"/>
		[HslMqttApi( Description = "关节坐标系的坐标位置读取。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadPOSJ( ) => await this.robot.ReadPOSJAsync( );

		/// <inheritdoc cref="YRC1000TcpNet.ReadPOSC(int, bool)"/>
		[HslMqttApi( Description = "指定坐标系的当前值读取。并且可以指定外部轴的有无。\r\ncoordinate 0:基座坐标，1:机器人坐标，2-65分别表示用户坐标1-64\r\nhasExteralAxis 外部轴的有/无" )]
		[RpcExtensionInfo( DataType = DataType.Class )]
		public async Task<OperateResult<YRCRobotData>> ReadPOSC( int coordinate, bool hasExteralAxis ) => await this.robot.ReadPOSCAsync( coordinate, hasExteralAxis );

		/// <inheritdoc cref="YRC1000TcpNet.ReadStats"/>
		[HslMqttApi( Description = "模式状态，循环状态，动作状态，报警错误状态，伺服状态的读取。\r\n结果: [0]: 单步 [1]: 循环 [2]: 自动连续 [3]: 运行中 [4]: 运转中 [5]: 示教 [6]: 在线 [7]: 命令模式 [9]: 示教编程器HOLD中 [12]: 发生警报 [13]: 发生错误 [14]: 伺服ON" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadStats( ) => await this.robot.ReadStatsAsync( );

		/// <inheritdoc cref="YRC1000TcpNet.ReadJSeq"/>
		[HslMqttApi( Description = "读取当前的程序名，行编号，步编号。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadJSeq( ) => await this.robot.ReadJSeqAsync( );

		/// <inheritdoc cref="YRC1000TcpNet.ReadUFrame"/>
		[HslMqttApi( Description = "读取指定用户的坐标数据\r\nframe 用户坐标编号，1-64" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadUFrame( int frame ) => await this.robot.ReadUFrameAsync( frame );

		/// <inheritdoc cref="YRC1000TcpNet.ReadByteVariable"/>
		[HslMqttApi( Description = "读取机器人的字节型变量的数据，需要传入变量的编号。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadByteVariable( string variableAdderss ) => await this.robot.ReadByteVariableAsync( variableAdderss );

		/// <inheritdoc cref="YRC1000TcpNet.ReadIntegerVariable"/>
		[HslMqttApi( Description = "读取机器人的整型变量的数据，需要传入变量的编号。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadIntegerVariable( string variableAdderss ) => await this.robot.ReadIntegerVariableAsync( variableAdderss );

		/// <inheritdoc cref="YRC1000TcpNet.ReadDoubleIntegerVariable"/>
		[HslMqttApi( Description = "读取机器人的双精度整型变量的数据，需要传入变量的编号。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadDoubleIntegerVariable( string variableAdderss ) => await this.robot.ReadDoubleIntegerVariableAsync( variableAdderss );

		/// <inheritdoc cref="YRC1000TcpNet.ReadRealVariable"/>
		[HslMqttApi( Description = "读取机器人的实数变量的数据，需要传入变量的编号。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadRealVariable( string variableAdderss ) => await this.robot.ReadRealVariableAsync( variableAdderss );

		/// <inheritdoc cref="YRC1000TcpNet.ReadStringVariable"/>
		[HslMqttApi( Description = "读取机器人的字符串变量的数据，需要传入变量的编号。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadStringVariable( string variableAdderss ) => await this.robot.ReadStringVariableAsync( variableAdderss );


		/// <inheritdoc cref="YRC1000TcpNet.Hold"/>
		[HslMqttApi( Description = "进行HOLD 的 ON/OFF 操作，状态参数 False: OFF操作，True: ON操作。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Hold( bool status ) => await this.robot.HoldAsync( status );

		/// <inheritdoc cref="YRC1000TcpNet.Reset"/>
		[HslMqttApi( Description = "对机械手的报警进行复位。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Reset( ) => await this.robot.ResetAsync( );

		/// <inheritdoc cref="YRC1000TcpNet.Cancel"/>
		[HslMqttApi( Description = "进行错误取消。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Cancel( ) => await this.robot.CancelAsync( );

		/// <inheritdoc cref="YRC1000TcpNet.Mode"/>
		[HslMqttApi( Description = "选择模式。模式编号为1:示教模式，2:再现模式。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Mode( int number ) => await this.robot.ModeAsync( number );

		/// <inheritdoc cref="YRC1000TcpNet.Cycle"/>
		[HslMqttApi( Description = "选择循环。循环编号 1:步骤，2:循环，3:连续自动。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Cycle( int number ) => await this.robot.CycleAsync( number );

		/// <inheritdoc cref="YRC1000TcpNet.Svon"/>
		[HslMqttApi( Description = "进行伺服电源的ON/OFF操作，状态参数 False: OFF，True: ON。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Svon( bool status ) => await this.robot.SvonAsync( status );

		/// <inheritdoc cref="YRC1000TcpNet.HLock"/>
		[HslMqttApi( Description = "设定示教编程器和 I/O的操作信号的联锁。 状态参数 False: OFF，True: ON。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> HLock( bool status ) => await this.robot.HLockAsync( status );

		/// <inheritdoc cref="YRC1000TcpNet.MSDP"/>
		[HslMqttApi( Description = "接受消息数据时， 在YRC1000的示教编程器的远程画面下显示消息若。若不是远程画面时，强制切换到远程画面。显示MDSP命令的消息。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> MSDP( string message ) => await this.robot.MSDPAsync( message );

		/// <inheritdoc cref="YRC1000TcpNet.Start"/>
		[HslMqttApi( Description = "开始程序。操作时指定程序名时，此程序能附带对应主程序，则从该程序的开头开始执行。如果没有指定，则从前行开始执行" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Start( string programName = null ) => await this.robot.StartAsync( programName );

		/// <inheritdoc cref="YRC1000TcpNet.Delete"/>
		[HslMqttApi( Description = "删除指定的程序。指定「*」 时， 删除当前登录的所有程序。指定「 删除程序名称」 时，仅删除指定的程序。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Delete( string programName = null ) => await this.robot.DeleteAsync( programName );

		/// <inheritdoc cref="YRC1000TcpNet.SetMJ"/>
		[HslMqttApi( Description = "指定的程序设定为主程序。设定主程序的同时执行程序也被设定。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> SetMJ( string programName = null ) => await this.robot.SetMJAsync( programName );

		/// <inheritdoc cref="YRC1000TcpNet.JSeq"/>
		[HslMqttApi( Description = "设定执行程序的名称和行编号。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> JSeq( string programName, int line ) => await this.robot.JSeqAsync( programName, line );

		/// <inheritdoc cref="YRC1000TcpNet.MoveJ"/>
		[HslMqttApi( Description = "指定的坐标系位置进行关节动作。其中没有外部轴的系统， 7-12外部轴的值设定为「0」。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> MoveJ( YRCRobotData robotData ) => await this.robot.MoveJAsync( robotData );

		/// <inheritdoc cref="YRC1000TcpNet.IORead"/>
		[HslMqttApi( Description = "读取I/O 信号。 I/O 数据是每8个点输出，所以读出接点数是8的倍数。" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> IORead( int address, int length ) => await this.robot.IOReadAsync( address, length );

		/// <inheritdoc cref="YRC1000TcpNet.IOWrite"/>
		[HslMqttApi( Description = "写入I/O信号状态，写入接点数请指定8的倍数。IO 信号的网络写入仅可是（ #27010 ～ #29567）。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> IOWrite( int address, bool[] value ) => await this.robot.IOWriteAsync( address, value );



		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Robot YRC1000TcpNet] [{GetDeviceNameWithPath( )}] [{nodeRobot.GetSocketInfo( )}]";

		#endregion

		#region Private

		private YRC1000TcpNet robot;                                          // 机器人的交互对象
		private NodeRobotYRC1000TcpNet nodeRobot;                              // 节点信息

		#endregion
	}
}
