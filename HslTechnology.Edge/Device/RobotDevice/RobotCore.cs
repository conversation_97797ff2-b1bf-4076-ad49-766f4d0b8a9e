using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.LogNet;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.Threading.Tasks;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Device;
using Newtonsoft.Json.Linq;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node;

namespace HslTechnology.Edge.Device.RobotDevice
{
	/// <summary>
	/// 一个机器人的统一数据读取对象
	/// </summary>
	public class RobotCore : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个机器人的数据访问的设备对象
		/// </summary>
		public RobotCore( XElement element ) : base( element )
		{

		}

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
		}

		#endregion

		/// <summary>
		/// 数据转换规则
		/// </summary>
		public IByteTransform ByteTransform { get; set; }

		/// <summary>
		/// 当前的数据读写信息
		/// </summary>
		public IRobotNet RobotNetDevice { get; set; }

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualAsync( RequestBase request )
		{
			if(request.RequestType == RequestType.ScalarRead)
			{
				ScalarReadRequest scalarRequest = (ScalarReadRequest)request;

				//if (request.RegularType == Node.Regular.RegularNodeTypeItem.None.Text)
				//	return DealWithReadResult( await RobotNetDevice.ReadAsync( request.Address ), content => ParseFromRequest( content, request, ByteTransform ) );
				if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Byte.Text)
					return DealWithReadResult( await RobotNetDevice.ReadAsync( scalarRequest.Address ), content => SetJsonValue( scalarRequest.Name, HslCommunication.BasicFramework.SoftBasic.ByteToHexString( content ), false ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.String.Text)
					return DealWithReadResult( await RobotNetDevice.ReadStringAsync( scalarRequest.Address ), content => SetJsonValue( scalarRequest.Name, content, false ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.StringJson.Text)
					return DealWithReadResult( await RobotNetDevice.ReadStringAsync( scalarRequest.Address ), content =>
					{
						try
						{
							SetJsonObjectValue( scalarRequest.Name, JToken.FromObject( Newtonsoft.Json.JsonConvert.DeserializeObject( content ) ) );
						}
						catch
						{
							SetJsonValue( scalarRequest.Name, content, false );
						}
					} );
				else
					return new OperateResult( "Not implementation type: " + scalarRequest.DataTypeCode );
			}
			else
			{
				return new OperateResult( "Not implementation request: " + request.RequestType );
			}
		}
	}
}
