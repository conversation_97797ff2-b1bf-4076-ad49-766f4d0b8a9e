using HslCommunication;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Reflection;
using HslCommunication.Robot.YASKAWA;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device.Robot;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.RobotDevice
{
	public class RobotYRCHighEthernet : RobotCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个埃夫特机器人的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public RobotYRCHighEthernet( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeRobotYRCHighEthernet( element );
			this.robot = new YRCHighEthernet( node.IpAddress, node.Port );
			this.robot.ReceiveTimeout = node.ReceiveTimeOut;

			//this.ByteTransform = this.robot.ByteTransform;
			//this.RobotNetDevice = this.robot;
			this.UniqueId = this.robot.ConnectionId;
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{

		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.robot == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			System.Net.NetworkInformation.IPStatus status = robot.IpAddressPing( );

			return status == System.Net.NetworkInformation.IPStatus.Success ?
				OperateResult.CreateSuccessResult( ToString( ) ) :
				new OperateResult<string>( "Ping Wrong:" + status.ToString( ) );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="YRCHighEthernet.ReadCommand(ushort, ushort, byte, byte, byte[])"/>
		[HslMqttApi( Description = "使用自定义的命令来读取机器人指定的数据信息，每个命令返回的数据格式互不相同，需要根据手册来自定义解析的。" )]
		[RpcExtensionInfo( DataType = DataType.Byte, DataDimension = DataDimension.One )]
		public OperateResult<byte[]> ReadCommand( ushort command, ushort dataAddress, byte dataAttribute, byte dataHandle, byte[] dataPart ) =>
			this.robot.ReadCommand( command, dataAddress, dataAttribute, dataHandle, dataPart );

		/// <inheritdoc cref="YRCHighEthernet.ReadAlarms"/>
		[HslMqttApi( Description = "读取机器人的最新的报警列表信息，最多为四个报警" )]
		[RpcExtensionInfo( DataType = DataType.Class, DataDimension = DataDimension.One )]
		public OperateResult<YRCAlarmItem[]> ReadAlarms( ) =>
			this.robot.ReadAlarms( );

		/// <inheritdoc cref="YRCHighEthernet.ReadHistoryAlarms(ushort, short)"/>
		[HslMqttApi( Description = "读取机器人的指定的报警信息，需要指定报警类型，及报警数量，其中length为1-100之间。\r\n报警类型，1-100:重故障; 1001-1100: 轻故障; 2001-2100: 用户报警(系统); 3001-3100: 用户报警(用户); 4001-4100:在线报警" )]
		[RpcExtensionInfo( DataType = DataType.Class, DataDimension = DataDimension.One )]
		public OperateResult<YRCAlarmItem[]> ReadHistoryAlarms( ushort alarmType, short length ) =>
			this.robot.ReadHistoryAlarms( alarmType, length );

		/// <inheritdoc cref="YRCHighEthernet.ReadStats"/>
		[HslMqttApi( Description = "模式状态，循环状态，动作状态，报警错误状态，伺服状态的读取。\r\n结果: [0]: 单步 [1]: 循环 [2]: 自动连续 [3]: 运行中 [4]: 运转中 [5]: 示教 [6]: 在线 [7]: 命令模式 [9]: 示教编程器HOLD中 [12]: 发生警报 [13]: 发生错误 [14]: 伺服ON" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public OperateResult<bool[]> ReadStats( ) =>
			this.robot.ReadStats( );

		/// <inheritdoc cref="YRCHighEthernet.ReadJSeq(ushort)"/>
		[HslMqttApi( Description = "读取当前的机器人的程序名称，行编号，步骤编号，速度超出值。需要指定当前的任务号，默认为1，表示主任务\r\n任务标识，1:主任务; 2-16分别表示子任务1-子任务15" )]
		[RpcExtensionInfo( DataType = DataType.String, DataDimension = DataDimension.One )]
		public OperateResult<string[]> ReadJSeq( ushort task = 1 ) =>
			this.robot.ReadJSeq( task );

		/// <inheritdoc cref="YRCHighEthernet.ReadPose"/>
		[HslMqttApi( Description = "读取机器人的姿态信息，包括X,Y,Z,Rx,Ry,Rz,如果是七轴机器人，还包括Re" )]
		[RpcExtensionInfo( DataType = DataType.String, DataDimension = DataDimension.One )]
		public OperateResult<string[]> ReadPose( ) =>
			this.robot.ReadPose( );

		/// <inheritdoc cref="YRCHighEthernet.ReadTorqueData"/>
		[HslMqttApi( Description = "读取力矩数据功能" )]
		[RpcExtensionInfo( DataType = DataType.String, DataDimension = DataDimension.One )]
		public OperateResult<string[]> ReadTorqueData( ) =>
			this.robot.ReadTorqueData( );

		/// <inheritdoc cref="YRCHighEthernet.ReadIO(ushort)"/>
		[HslMqttApi( Description = "读取IO数据，需要指定IO的地址。" )]
		[RpcExtensionInfo( DataType = DataType.Byte )]
		public OperateResult<byte> ReadIO( ushort address ) =>
			this.robot.ReadIO( address );

		/// <inheritdoc cref="YRCHighEthernet.WriteIO(ushort, byte)"/>
		[HslMqttApi( Description = "写入IO的数据，只可写入网络输入信号，也即地址是 2701~2956" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteIO( ushort address, byte value ) =>
			this.robot.WriteIO( address, value );

		#region Register Variable

		/// <inheritdoc cref="YRCHighEthernet.ReadRegisterVariable(ushort)"/>
		[HslMqttApi( Description = "读取寄存器的数据，地址范围 0 ~ 999" )]
		[RpcExtensionInfo( DataType = DataType.UInt16 )]
		public OperateResult<ushort> ReadRegisterVariable( ushort address ) =>
			this.robot.ReadRegisterVariable( address );

		/// <inheritdoc cref="YRCHighEthernet.WriteRegisterVariable(ushort, ushort)"/>
		[HslMqttApi( Description = "将数据写入到寄存器，支持写入的地址范围为 0 ~ 599" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteRegisterVariable( ushort address, ushort value ) =>
			this.robot.WriteRegisterVariable( address, value );

		/// <inheritdoc cref="YRCHighEthernet.ReadRegisterVariable(ushort, int)"/>
		[HslMqttApi( Description = "批量读取多个寄存器的数据，地址范围 0 ~ 999，指定读取的数据长度，最大不超过237 个" )]
		[RpcExtensionInfo( DataType = DataType.UInt16, DataDimension = DataDimension.One )]
		public OperateResult<ushort[]> ReadRegisterVariableArray( ushort address, int length ) =>
			this.robot.ReadRegisterVariable( address, length );

		/// <inheritdoc cref="YRCHighEthernet.WriteRegisterVariable(ushort, ushort[])"/>
		[HslMqttApi( Description = "写入多个数据到寄存器，地址范围 0 ~ 999，指定读取的数据长度，最大不超过237 个" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteRegisterVariableArray( ushort address, ushort[] value ) =>
			this.robot.WriteRegisterVariable( address, value );

		#endregion

		#region Byte Variable

		/// <inheritdoc cref="YRCHighEthernet.ReadByteVariable(ushort)"/>
		[HslMqttApi( Description = "读取字节型变量的数据，标准地址范围为 0 ~ 99" )]
		[RpcExtensionInfo( DataType = DataType.Byte )]
		public OperateResult<byte> ReadByteVariable( ushort address ) =>
			this.robot.ReadByteVariable( address );

		/// <inheritdoc cref="YRCHighEthernet.WriteByteVariable(ushort, byte)"/>
		[HslMqttApi( Description = "将数据写入到字节型变量的地址里去，标准地址范围为 0 ~ 99" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteByteVariable( ushort address, byte value ) =>
			this.robot.WriteByteVariable( address, value );

		/// <inheritdoc cref="YRCHighEthernet.ReadByteVariable(ushort, int)"/>
		[HslMqttApi( Description = "读取多个的字节型变量的数据，读取的最大个数为 474 个。" )]
		[RpcExtensionInfo( DataType = DataType.Byte, DataDimension = DataDimension.One )]
		public OperateResult<byte[]> ReadByteVariableArray( ushort address, int length ) =>
			this.robot.ReadByteVariable( address, length );

		/// <inheritdoc cref="YRCHighEthernet.WriteByteVariable(ushort, byte[])"/>
		[HslMqttApi( Description = "将多个字节的变量的数据写入到指定的地址，最大个数为 474 个，仅可指定2的倍数" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteByteVariableArray( ushort address, byte[] vaule ) =>
			this.robot.WriteByteVariable( address, vaule );

		#endregion

		#region Interger Variable

		/// <inheritdoc cref="YRCHighEthernet.ReadIntegerVariable(ushort)"/>
		[HslMqttApi( Description = "读取单个的整型变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Int16 )]
		public OperateResult<short> ReadIntegerVariable( ushort address ) =>
			this.robot.ReadIntegerVariable( address );

		/// <inheritdoc cref="YRCHighEthernet.WriteIntegerVariable(ushort, short)"/>
		[HslMqttApi( Description = "将单个的数据写入到整型变量去，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteIntegerVariable( ushort address, short value ) =>
			this.robot.WriteIntegerVariable( address, value );

		/// <inheritdoc cref="YRCHighEthernet.ReadIntegerVariable(ushort, int)"/>
		[HslMqttApi( Description = "读取多个的整型变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Int16, DataDimension = DataDimension.One )]
		public OperateResult<short[]> ReadIntegerVariableArray( ushort address, int length ) =>
			this.robot.ReadIntegerVariable( address, length );

		/// <inheritdoc cref="YRCHighEthernet.WriteIntegerVariable(ushort, short[])"/>
		[HslMqttApi( Description = "写入多个的整型变量数据到机器人，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteIntegerVariableArray( ushort address, short[] value ) =>
			this.robot.WriteIntegerVariable( address, value );

		#endregion

		#region DoubleInteger Variable

		/// <inheritdoc cref="YRCHighEthernet.ReadDoubleIntegerVariable(ushort)"/>
		[HslMqttApi( Description = "读取单个的双精度整型变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		public OperateResult<int> ReadDoubleIntegerVariable( ushort address ) =>
			this.robot.ReadDoubleIntegerVariable( address );

		/// <inheritdoc cref="YRCHighEthernet.WriteDoubleIntegerVariable(ushort, int)"/>
		[HslMqttApi( Description = "将单个的数据写入到双精度整型变量去，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteDoubleIntegerVariable( ushort address, int value ) =>
			this.robot.WriteDoubleIntegerVariable( address, value );

		/// <inheritdoc cref="YRCHighEthernet.ReadDoubleIntegerVariable(ushort, int)"/>
		[HslMqttApi( Description = "读取多个的双精度整型变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Int32, DataDimension = DataDimension.One )]
		public OperateResult<int[]> ReadDoubleIntegerVariableArray( ushort address, int length ) =>
			this.robot.ReadDoubleIntegerVariable( address, length );

		/// <inheritdoc cref="YRCHighEthernet.WriteDoubleIntegerVariable(ushort, int[])"/>
		[HslMqttApi( Description = "写入多个的双精度整型变量数据到机器人，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteDoubleIntegerVariableArray( ushort address, int[] value ) =>
			this.robot.WriteDoubleIntegerVariable( address, value );

		#endregion

		#region Real Variable

		/// <inheritdoc cref="YRCHighEthernet.ReadRealVariable(ushort)"/>
		[HslMqttApi( Description = "读取单个的实数型变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Float )]
		public OperateResult<float> ReadRealVariable( ushort address ) =>
			this.robot.ReadRealVariable( address );

		/// <inheritdoc cref="YRCHighEthernet.WriteRealVariable(ushort, float)"/>
		[HslMqttApi( Description = "将单个的数据写入到实数型变量去，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteRealVariable( ushort address, float value ) =>
			this.robot.WriteRealVariable( address, value );

		/// <inheritdoc cref="YRCHighEthernet.ReadRealVariable(ushort, int)"/>
		[HslMqttApi( Description = "读取多个的实数型变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Float, DataDimension = DataDimension.One )]
		public OperateResult<float[]> ReadRealVariableArray( ushort address, int length ) =>
			this.robot.ReadRealVariable( address, length );

		/// <inheritdoc cref="YRCHighEthernet.WriteRealVariable(ushort, float[])"/>
		[HslMqttApi( Description = "写入多个的实数型的变量数据到机器人，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteRealVariableArray( ushort address, float[] value ) =>
			this.robot.WriteRealVariable( address, value );

		#endregion

		#region String Variable

		/// <inheritdoc cref="YRCHighEthernet.ReadStringVariable(ushort)"/>
		[HslMqttApi( Description = "读取单个的字符串变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public OperateResult<string> ReadStringVariable( ushort address ) =>
			this.robot.ReadStringVariable( address );

		/// <inheritdoc cref="YRCHighEthernet.WriteStringVariable(ushort, string)"/>
		[HslMqttApi( Description = "写入单个的字符串变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteStringVariable( ushort address, string value ) =>
			this.robot.WriteStringVariable( address, value );

		/// <inheritdoc cref="YRCHighEthernet.ReadStringVariable(ushort, int)"/>
		[HslMqttApi( Description = "读取多个的字符串变量数据，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.String, DataDimension = DataDimension.One )]
		public OperateResult<string[]> ReadStringVariableArray( ushort address, int length ) =>
			this.robot.ReadStringVariable( address, length );

		/// <inheritdoc cref="YRCHighEthernet.WriteStringVariable(ushort, string[])"/>
		[HslMqttApi( Description = "写入多个的字符串变量数据到机器人，地址范围：0 ～ 99（ 标准设定时）" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult WriteStringVariableArray( ushort address, string[] value ) =>
			this.robot.WriteStringVariable( address, value );

		#endregion

		#region System Control

		/// <inheritdoc cref="YRCHighEthernet.Hold(bool)"/>
		[HslMqttApi( Description = "进行HOLD 的 ON/OFF 操作，状态参数 False: OFF，True: ON" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult Hold( bool status ) =>
			this.robot.Hold( status );

		/// <inheritdoc cref="YRCHighEthernet.Reset"/>
		[HslMqttApi( Description = "对机械手的报警进行复位" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult Reset( ) => this.robot.Reset( );

		/// <inheritdoc cref="YRCHighEthernet.Cancel"/>
		[HslMqttApi( Description = "进行错误取消" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult Cancel( ) => this.robot.Cancel( );

		/// <inheritdoc cref="YRCHighEthernet.Svon(bool)"/>
		[HslMqttApi( Description = "进行伺服电源的ON/OFF操作，状态参数 False: OFF，True: ON" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult Svon( bool status ) => this.robot.Svon( status );

		/// <inheritdoc cref="YRCHighEthernet.HLock(bool)"/>
		[HslMqttApi( Description = "设定示教编程器和 I/O的操作信号的联锁。 状态参数 False: OFF，True: ON" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult HLock( bool status ) => this.robot.HLock( status );

		/// <inheritdoc cref="YRCHighEthernet.Cycle(int)"/>
		[HslMqttApi( Description = "选择循环。循环编号 1:步骤，2:1循环，3:连续自动" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult Cycle( int number ) => this.robot.Cycle( number );

		/// <inheritdoc cref="YRCHighEthernet.MSDP(string)"/>
		[HslMqttApi( Description = "接受消息数据时， 在YRC1000的示教编程器的远程画面下显示消息若。若不是远程画面时，强制切换到远程画面。显示MDSP命令的消息。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult MSDP( string message ) => this.robot.MSDP( message );

		/// <inheritdoc cref="YRCHighEthernet.Start"/>
		[HslMqttApi( Description = "开始程序。操作时指定程序名时，此程序能附带对应主程序，则从该程序的开头开始执行。如果没有指定，则从前行开始执行" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult RobotStart( ) => this.robot.Start( );

		/// <inheritdoc cref="YRCHighEthernet.ReadManagementTime(ushort)"/>
		[HslMqttApi( Description = "读取机器人的时间信息，根据地址来获取不同的时间: 1:控制器接通电源,10:伺服电源总时间,110:再线时间" )]
		[RpcExtensionInfo( DataType = DataType.DateTime )]
		public OperateResult<DateTime> ReadManagementTime( ushort address ) => this.robot.ReadManagementTime( address );

		/// <inheritdoc cref="YRCHighEthernet.ReadManagementTime(ushort)"/>
		[HslMqttApi( Description = "读取机器人的持续时间信息，根据地址来获取不同的时间: 1:控制器接通电源,10:伺服电源总时间,110:再线时间" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public OperateResult<string> ReadManagementTimeSpan( ushort address ) => this.robot.ReadManagementTimeSpan( address );

		/// <inheritdoc cref="YRCHighEthernet.ReadSystemInfo(ushort)"/>
		[HslMqttApi( Description = "读取系统的参数信息，其中系统种类参数：11~18:机种信息R1~R8;21~44:机种信息S1~S24;101~108: 用途信息(用途1~用途8);\r\n返回分别为 [0]:系统软件版本；[1]:机种名称/用途名称；[2]:参数版本" )]
		[RpcExtensionInfo( DataType = DataType.String, DataDimension = DataDimension.One )]
		public OperateResult<string[]> ReadSystemInfo( ushort system ) => this.robot.ReadSystemInfo( system );

		/// <inheritdoc cref="YRCHighEthernet.JSeq(string, int)"/>
		[HslMqttApi( Description = "设定执行程序的名称和行编号" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult JSeq( string programName, int line ) => this.robot.JSeq( programName, line );

		#endregion

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( )
		{
			return $"[Robot YRCHighEthernet] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
		}

		#endregion

		#region Private

		private YRCHighEthernet robot;                   // 机器人的交互对象
		private NodeRobotYRCHighEthernet node;                // 节点信息


		#endregion
	}
}
