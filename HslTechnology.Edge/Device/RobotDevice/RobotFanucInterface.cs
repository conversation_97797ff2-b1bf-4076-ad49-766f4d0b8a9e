using System;
using System.Collections.Generic;
using System.Text;
using HslCommunication.Robot.FANUC;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication.Reflection;
using System.Threading.Tasks;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.RobotDevice
{
	/// <summary>
	/// Fanuc机器人的网络数据类
	/// </summary>
	public class RobotFanucInterface : RobotCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个FANUC机器人的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public RobotFanucInterface( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeRobot = new NodeRobotFanucInterface( element );

			this.fanucInterface                     = new FanucInterfaceNet( nodeRobot.IpAddress, nodeRobot.Port );
			this.fanucInterface.FanucDataRetainTime = nodeRobot.RefreshTime;
			this.fanucInterface.ConnectTimeOut      = nodeRobot.ConnectTimeOut;
			this.fanucInterface.ReceiveTimeOut      = nodeRobot.ReceiveTimeOut;

			this.ByteTransform                      = this.fanucInterface.ByteTransform;
			this.RobotNetDevice                     = this.fanucInterface;
			this.UniqueId                           = this.fanucInterface.ConnectionId;
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => fanucInterface?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => fanucInterface?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.fanucInterface == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = fanucInterface.ConnectServer( );
			if (connect.IsSuccess) fanucInterface.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="FanucInterfaceNet.ReadFanucData"/>
		[HslMqttApi( Description = "读取机器人的详细信息，返回解析后的数据类型\r\nRead the details of the robot and return the resolved data type" )]
		[RpcExtensionInfo( DataType = DataType.Class )]
		public async Task<OperateResult<FanucData>> ReadFanucData( ) => await this.fanucInterface.ReadFanucDataAsync( );

		/// <inheritdoc cref="FanucInterfaceNet.ReadSDO(ushort, ushort)"/>
		[HslMqttApi( Description = "读取机器人的SDO信息\r\nRead the SDO information of the robot" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadSDO( ushort address, ushort length ) => await this.fanucInterface.ReadSDOAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WriteSDO(ushort, bool[])"/>
		[HslMqttApi( Description = "写入机器人的SDO信息\r\nWrite the SDO information of the robot" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteSDO( ushort address, bool[] value ) => await this.fanucInterface.WriteSDOAsync( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.ReadSDI(ushort, ushort)"/>
		[HslMqttApi( Description = "读取机器人的SDI信息\r\nRead the SDI information of the robot" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadSDI( ushort address, ushort length ) => await this.fanucInterface.ReadSDIAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WriteSDI(ushort, bool[])"/>
		[HslMqttApi( Description = "写入机器人的SDI信息\r\nWrite the SDI information of the robot" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteSDI( ushort address, bool[] value ) => await this.fanucInterface.WriteSDIAsync( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.ReadRDI(ushort, ushort)"/>
		[HslMqttApi( Description = "读机器人的RDI信息" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadRDI( ushort address, ushort length ) => await this.fanucInterface.ReadRDIAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WriteRDI(ushort, bool[])"/>
		[HslMqttApi( Description = "写机器人的RDI信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteRDI( ushort address, bool[] value ) => await this.fanucInterface.WriteRDIAsync( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.ReadUI(ushort, ushort)"/>
		[HslMqttApi( Description = "读机器人的UI信息" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadUI( ushort address, ushort length ) => await this.fanucInterface.ReadUIAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.ReadUO(ushort, ushort)"/>
		[HslMqttApi( Description = "读机器人的UO信息" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadUO( ushort address, ushort length ) => await this.fanucInterface.ReadUOAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WriteUO(ushort, bool[])"/>
		[HslMqttApi( Description = "写机器人的UO信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteUO( ushort address, bool[] value ) => await this.fanucInterface.WriteUOAsync( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.ReadSI(ushort, ushort)"/>
		[HslMqttApi( Description = "读取机器人的SI信息" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadSI( ushort address, ushort length ) => await this.fanucInterface.ReadSIAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.ReadSO(ushort, ushort)"/>
		[HslMqttApi( Description = "读取机器人的SO信息" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadSO( ushort address, ushort length ) => await this.fanucInterface.ReadSOAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WriteSO(ushort, bool[])"/>
		[HslMqttApi( Description = "写入机器人的SO信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteSO( ushort address, bool[] value ) => await this.fanucInterface.WriteSOAsync( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.ReadGI(ushort, ushort)"/>
		[HslMqttApi( Description = "读取机器人的GI信息" )]
		[RpcExtensionInfo( DataType = DataType.UInt16, DataDimension = DataDimension.One )]
		public async Task<OperateResult<ushort[]>> ReadGI( ushort address, ushort length ) => await this.fanucInterface.ReadGIAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WriteGI(ushort, ushort[])"/>
		[HslMqttApi( Description = "写入机器人的GI信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteGI( ushort address, ushort[] value ) => await this.fanucInterface.WriteGIAsync( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.ReadGO(ushort, ushort)"/>
		[HslMqttApi( Description = "读取机器人的GO信息" )]
		[RpcExtensionInfo( DataType = DataType.UInt16, DataDimension = DataDimension.One )]
		public async Task<OperateResult<ushort[]>> ReadGO( ushort address, ushort length ) => await this.fanucInterface.ReadGOAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WriteGO(ushort, ushort[])"/>
		[HslMqttApi( Description = "写入机器人的GO信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteGO( ushort address, ushort[] value ) => await this.fanucInterface.WriteGOAsync( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.ReadPMCR2(ushort, ushort)"/>
		[HslMqttApi( Description = "读取机器人的PMCR2信息" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadPMCR2( ushort address, ushort length ) => await this.fanucInterface.ReadPMCR2Async( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WritePMCR2(ushort, bool[])"/>
		[HslMqttApi( Description = "写入机器人的PMCR2信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WritePMCR2( ushort address, bool[] value ) => await this.fanucInterface.WritePMCR2Async( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.ReadRDO(ushort, ushort)"/>
		[HslMqttApi( Description = "读取机器人的RDO信息" )]
		[RpcExtensionInfo( DataType = DataType.Bool, DataDimension = DataDimension.One )]
		public async Task<OperateResult<bool[]>> ReadRDO( ushort address, ushort length ) => await this.fanucInterface.ReadRDOAsync( address, length );

		/// <inheritdoc cref="FanucInterfaceNet.WriteRDO(ushort, bool[])"/>
		[HslMqttApi( Description = "写入机器人的RDO信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteRDO( ushort address, bool[] value ) => await this.fanucInterface.WriteRDOAsync( address, value );

		/// <inheritdoc cref="FanucInterfaceNet.WriteRXyzwpr(ushort, float[], short[], short, short)"/>
		[HslMqttApi( Description = "写入机器人的Rxyzwpr信息，谨慎调用" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteRXyzwpr( ushort Address, float[] Xyzwpr, short[] Config, short UserFrame, short UserTool ) =>
			await this.fanucInterface.WriteRXyzwprAsync( Address, Xyzwpr, Config, UserFrame, UserTool );

		/// <inheritdoc cref="FanucInterfaceNet.WriteRJoint(ushort, float[], short, short)"/>
		[HslMqttApi( Description = "写入机器人的Joint信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteRJoint( ushort address, float[] joint, short UserFrame, short UserTool ) =>
			await this.fanucInterface.WriteRJointAsync( address, joint, UserFrame, UserTool );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Robot FanucInterface] [{GetDeviceNameWithPath( )}] [{nodeRobot.GetSocketInfo( )}]";

		#endregion

		#region Private

		private FanucInterfaceNet fanucInterface;                              // 机器人的交互对象
		private NodeRobotFanucInterface nodeRobot;                             // 节点信息

		#endregion
	}
}
