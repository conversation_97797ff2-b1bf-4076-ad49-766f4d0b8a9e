using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Reflection;
using HslCommunication.Robot.EFORT;
using HslTechnology.Edge.Node.Device;
using System.Threading.Tasks;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.RobotDevice
{
	/// <summary>
	/// 埃夫特机器人的网络数据类
	/// </summary>
	public class RobotEfortNet : RobotCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个埃夫特机器人的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public RobotEfortNet( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeRobotEfort = new NodeRobotEfort( element );
			if (nodeRobotEfort.IsVersionNew)
			{
				this.eR7BC10                      = new ER7BC10( nodeRobotEfort.IpAddress, nodeRobotEfort.Port );
				this.eR7BC10.ConnectTimeOut       = nodeRobotEfort.ConnectTimeOut;
				this.eR7BC10.ReceiveTimeOut       = nodeRobotEfort.ReceiveTimeOut;

				this.ByteTransform                = this.eR7BC10.ByteTransform;
				this.RobotNetDevice               = this.eR7BC10;
				this.UniqueId                     = this.eR7BC10.ConnectionId;
			}
			else
			{
				this.eR7BC10Previous                = new ER7BC10Previous( nodeRobotEfort.IpAddress, nodeRobotEfort.Port );
				this.eR7BC10Previous.ConnectTimeOut = nodeRobotEfort.ConnectTimeOut;
				this.eR7BC10Previous.ReceiveTimeOut = nodeRobotEfort.ReceiveTimeOut;

				this.ByteTransform                  = this.eR7BC10Previous.ByteTransform;
				this.RobotNetDevice                 = this.eR7BC10Previous;
				this.UniqueId                       = this.eR7BC10Previous.ConnectionId;
			}
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			if (this.nodeRobotEfort.IsVersionNew)
			{
				eR7BC10?.SetPersistentConnection( );
			}
			else
			{
				eR7BC10Previous?.SetPersistentConnection( );
			}
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			if (this.nodeRobotEfort.IsVersionNew)
			{
				eR7BC10?.ConnectClose( );
			}
			else
			{
				eR7BC10Previous?.ConnectClose( );
			}
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.nodeRobotEfort.IsVersionNew)
			{
				if (this.eR7BC10 == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

				OperateResult connect = eR7BC10.ConnectServer( );
				if (connect.IsSuccess) eR7BC10.ConnectClose( );

				return connect.Convert( ToString( ) );
			}
			else
			{
				if (this.eR7BC10Previous == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

				OperateResult connect = eR7BC10Previous.ConnectServer( );
				if (connect.IsSuccess) eR7BC10Previous.ConnectClose( );

				return connect.Convert( ToString( ) );
			}
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="ER7BC10.ReadEfortData"/>
		[HslMqttApi( Description = "读取机器人的详细信息，返回解析后的数据类型\r\nRead the details of the robot and return the resolved data type" )]
		[RpcExtensionInfo( DataType = DataType.Class )]
		public async Task<OperateResult<EfortData>> ReadEfortData( )
		{
			if (this.nodeRobotEfort.IsVersionNew)
				return await eR7BC10.ReadEfortDataAsync( );
			else
				return await eR7BC10Previous.ReadEfortDataAsync( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( )
		{
			if (this.nodeRobotEfort.IsVersionNew)
				return $"[Robot ER7BC10] [{GetDeviceNameWithPath( )}] [{nodeRobotEfort.GetSocketInfo( )}]";
			else
				return $"[Robot ER7BC10-Pre] [{GetDeviceNameWithPath( )}] [{nodeRobotEfort.GetSocketInfo( )}]";
		}

		#endregion

		#region Private

		private ER7BC10 eR7BC10;                              // 机器人的交互对象
		private ER7BC10Previous eR7BC10Previous;              // 二进制格式的交互对象
		private NodeRobotEfort nodeRobotEfort;                // 节点信息

		#endregion
	}
}
