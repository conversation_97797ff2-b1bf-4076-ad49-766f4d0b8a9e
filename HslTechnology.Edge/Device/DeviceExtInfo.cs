using HslTechnology.Edge.Device.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device
{
	/// <summary>
	/// 设备的扩展信息
	/// </summary>
	public class DeviceExtInfo
	{
		/// <summary>
		/// 使用指定的设备对象来实例化设备扩展对象信息
		/// </summary>
		/// <param name="device">设备信息</param>
		public DeviceExtInfo( DeviceCoreBase device )
		{
			this.Device = device;
		}

		/// <summary>
		/// 获取或设置指定的设备的重启次数
		/// </summary>
		public int RestartThreadTick { get; set; }

		/// <summary>
		/// 设备的检查状态的计数
		/// </summary>
		public int DeviceCheckStatus { get; set; }

		/// <summary>
		/// 获取当前设备扩展对象绑定的实际设备对象
		/// </summary>
		public DeviceCoreBase Device { get; private set; }
	}

	/// <summary>
	/// 整个网关系统的设备列表信息
	/// </summary>
	public class DeviceExtInfoList : List<DeviceExtInfo>
	{
		public DeviceCoreBase GetByDevicePath( string path )
		{
			for (int i = 0; i < Count; i++)
			{
				if (this[i].Device == null) continue;
				if (this[i].Device.GetDeviceNameWithPath( ) == path)
				{
					return this[i].Device;
				}
			}
			return null;
		}
	}
}
