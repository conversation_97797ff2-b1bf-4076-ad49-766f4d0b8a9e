using HslCommunication.Instrument.DLT;
using HslCommunication;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device.CJT;
using HslCommunication.Instrument.CJT;

namespace HslTechnology.Edge.Device.Instrument.CJT
{
	/// <summary>
	/// CJT188协议的网口透传版本
	/// </summary>
	public class DeviceCJT188OverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个CJT串口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceCJT188OverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeCJT188OverTcp( element );
			this.cjt = new CJT188OverTcp( this.node.Station );
			try
			{
				this.cjt.InstrumentType = Convert.ToByte( this.node.TypeCode, 16 );
			}
			catch
			{
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), "IniDevice", "InstrumentType get failed! need hex string, but is " + this.node.TypeCode );
			}
			this.cjt.EnableCodeFE = this.node.EnableFECode;
			this.cjt.StationMatch = this.node.StationMatch;
			this.SetDeviceInfo( deviceResources, node, this.cjt );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			cjt?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( ) => cjt?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = cjt.ConnectServer( );
			if (connect.IsSuccess) cjt.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[CJT188OverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private CJT188OverTcp cjt;               // 核心交互对象
		private NodeCJT188OverTcp node;          // 节点信息

		#endregion
	}
}
