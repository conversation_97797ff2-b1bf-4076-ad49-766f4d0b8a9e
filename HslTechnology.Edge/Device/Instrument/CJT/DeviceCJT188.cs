using HslCommunication.Instrument.DLT;
using HslCommunication.Reflection;
using HslCommunication;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Instrument.CJT;
using HslTechnology.Edge.Node.Device.CJT;

namespace HslTechnology.Edge.Device.Instrument.CJT
{
	/// <summary>
	/// CJT188的设备信息
	/// </summary>
	public class DeviceCJT188 : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个DLT串口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceCJT188( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeCJT188( element );
			this.cjt = new CJT188( this.node.Station );
			try
			{
				this.cjt.InstrumentType = Convert.ToByte( this.node.TypeCode, 16 );
			}
			catch
			{
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), "IniDevice", "InstrumentType get failed! need hex string, but is " + this.node.TypeCode );
			}
			this.cjt.EnableCodeFE = this.node.EnableFECode;
			this.SetDeviceInfo( deviceResources, node, this.cjt );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			cjt?.Open( );
		}

		/// <inheritdoc/>
		protected override bool UseAsyncReadWrite( ) => false;

		/// <inheritdoc/>
		protected override void AfterClose( ) => cjt?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = cjt.Open( );
			if (connect.IsSuccess) cjt.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region Method Interface

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[CJT188] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private CJT188 cjt;               // 核心交互对象
		private NodeCJT188 node;          // 节点信息

		#endregion
	}
}
