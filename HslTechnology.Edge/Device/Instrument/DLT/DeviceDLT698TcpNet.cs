using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.Instrument.DLT;
using HslCommunication.MQTT;
using HslCommunication.Reflection;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.Instrument.DLT
{
	public class DeviceDLT698TcpNet : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个DLT698协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceDLT698TcpNet( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeDlt698TcpNet( element );
			this.dlt = new DLT698TcpNet( this.node.Station );
			this.dlt.EnableCodeFE = this.node.EnableFECode;
			this.dlt.UseSecurityResquest = this.node.UseSecurityResquest;
			this.SetDeviceInfo( deviceResources, node, this.dlt );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			dlt?.SetPersistentConnection( );

			if (this.node != null && this.node.ReadAddressAfterOpen)
			{
				dlt?.ReadAddress( );
			}
		}

		/// <inheritdoc/>
		protected override void AfterClose( ) => dlt?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = dlt.ConnectServer( );
			if (connect.IsSuccess) dlt.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			OperateResult<string> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadStringAsync( scalarRequest.Address, (ushort)scalarRequest.StringLength ) :
					ReadWriteDevice.ReadString( scalarRequest.Address, (ushort)scalarRequest.Length );
			if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateFailedResult<string>( read ) );

			string value = read.Content;
			if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToInt64( value ) ) );
			else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToDouble( value ) ) );
			else
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( value ) );
		}

		/// <inheritdoc/>
		protected override OperateResult WriteValueByScalarRequest( IReadWriteNet readWriteDevice, string address, IScalarTransform transform, string value )
		{
			if (transform.DataTypeCode == RegularNodeTypeItem.String.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.IntOfString.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text
				)
			{
				if (transform is ScalarReadRequest request)   // 标量的情况
					return readWriteDevice.Write( address, value );
				else
					return base.WriteValueByScalarRequest( readWriteDevice, address, transform, value );
			}
			else
				return base.WriteValueByScalarRequest( readWriteDevice, address, transform, value );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="DLT698.ReadAddress"/>
		[HslMqttApi( Description = "读取设备的通信地址，仅支持点对点通讯的情况，返回地址域数据，例如：149100007290" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public OperateResult<string> ReadAddress( )
		{
			return dlt == null ? new OperateResult<string>( "Device is null" ) : dlt.ReadAddress( );
		}

		/// <inheritdoc cref="DLT698.ActiveDeveice"/>
		[HslMqttApi( Description = "激活设备的命令，只发送数据到设备，不等待设备数据返回" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult ActiveDeveice( )
		{
			return dlt == null ? new OperateResult( "Device is null" ) : dlt.ActiveDeveice( );
		}

		/// <inheritdoc cref="DLT698.ReadStringArray(string)"/>
		[HslMqttApi( Description = "读取指定地址的所有的字符串数据信息，一般来说，一个地址只有一个数据，当属性为数组或是结构体的时候，存在多个数据，具体几个数据，根据实际情况决定" )]
		[RpcExtensionInfo( DataType = DataType.String, DataDimension = DataDimension.One )]
		public OperateResult<string[]> ReadStringArray( string address )
		{
			return dlt == null ? new OperateResult<string[]>( "Device is null" ) : dlt.ReadStringArray( address );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[DLT698TcpNet] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private DLT698TcpNet dlt;               // 核心交互对象
		private NodeDlt698TcpNet node;          // 节点信息

		#endregion
	}
}
