using HslCommunication.Instrument.DLT;
using HslCommunication;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device.DLT;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Resources;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using HslCommunication.Core;
using HslTechnology.Edge.Node;

namespace HslTechnology.Edge.Device.Instrument.DLT
{
	/// <summary>
	/// DLT645-1997串口转网口的协议
	/// </summary>
	public class DeviceDLT645With1997OverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个DLT串口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceDLT645With1997OverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeDlt645With1997OverTcp( element );
			this.dlt = new DLT645With1997OverTcp( this.node.IpAddress, this.node.Port, this.node.Station );
			this.dlt.EnableCodeFE = this.node.EnableFECode;
			this.SetDeviceInfo( deviceResources, node, this.dlt );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			dlt?.SetPersistentConnection( );

			if (this.node != null && this.node.ActiveWhenOpen)
			{
				dlt?.ActiveDeveice( );
			}
		}

		/// <inheritdoc/>
		protected override void AfterClose( ) => dlt?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = dlt.ConnectServer( );
			if (connect.IsSuccess) dlt.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			OperateResult<string> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadStringAsync( scalarRequest.Address, (ushort)scalarRequest.StringLength ) :
					ReadWriteDevice.ReadString( scalarRequest.Address, (ushort)scalarRequest.Length );
			if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateFailedResult<string>( read ) );

			string value = read.Content;
			if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToInt64( value ) ) );
			else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToDouble( value ) ) );
			else
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( value ) );
		}

		/// <inheritdoc/>
		protected override OperateResult WriteValueByScalarRequest( IReadWriteNet readWriteDevice, string address, IScalarTransform transform, string value )
		{
			if (transform.DataTypeCode == RegularNodeTypeItem.String.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.IntOfString.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text
				)
			{
				if (transform is ScalarReadRequest request)   // 标量的情况
					return readWriteDevice.Write( address, value );
				else
					return base.WriteValueByScalarRequest( readWriteDevice, address, transform, value );
			}
			else
				return base.WriteValueByScalarRequest( readWriteDevice, address, transform, value );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[DLT645With1997OverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private DLT645With1997OverTcp dlt;               // 核心交互对象
		private NodeDlt645With1997OverTcp node;          // 节点信息

		#endregion
	}
}
