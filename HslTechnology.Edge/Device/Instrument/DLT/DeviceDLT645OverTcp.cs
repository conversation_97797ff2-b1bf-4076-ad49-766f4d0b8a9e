using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.Instrument.DLT;
using HslCommunication.MQTT;
using HslCommunication.Reflection;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.Instrument.DLT
{
	/// <summary>
	/// DLT645的串口转网口的设备信息
	/// </summary>
	public class DeviceDLT645OverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个DLT串口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceDLT645OverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node             = new NodeDlt645OverTcp( element );
			this.dlt              = new DLT645OverTcp( this.node.IpAddress, this.node.Port, this.node.Station, this.node.Password, this.node.OpCode );
			this.dlt.EnableCodeFE = this.node.EnableFECode;
			this.SetDeviceInfo( deviceResources, node, this.dlt );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			dlt?.SetPersistentConnection( );

			if (this.node != null && this.node.ActiveWhenOpen)
			{
				dlt?.ActiveDeveice( );
			}

			if (this.node != null && this.node.ReadAddressAfterOpen)
			{
				dlt?.ReadAddress( );
			}
		}

		/// <inheritdoc/>
		protected override void AfterClose( ) => dlt?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = dlt.ConnectServer( );
			if (connect.IsSuccess) dlt.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			OperateResult<string> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadStringAsync( scalarRequest.Address, (ushort)scalarRequest.StringLength ) :
					ReadWriteDevice.ReadString( scalarRequest.Address, (ushort)scalarRequest.Length );
			if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateFailedResult<string>( read ) );

			string value = read.Content;
			if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToInt64( value ) ) );
			else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToDouble( value ) ) );
			else
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( value ) );
		}

		/// <inheritdoc/>
		protected override OperateResult WriteValueByScalarRequest( IReadWriteNet readWriteDevice, string address, IScalarTransform transform, string value )
		{
			if (transform.DataTypeCode == RegularNodeTypeItem.String.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.IntOfString.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text
				)
			{
				if (transform is ScalarReadRequest request)   // 标量的情况
					return readWriteDevice.Write( address, value );
				else
					return base.WriteValueByScalarRequest( readWriteDevice, address, transform, value );
			}
			else
				return base.WriteValueByScalarRequest( readWriteDevice, address, transform, value );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="DLT645.ReadAddress"/>
		[HslMqttApi( Description = "读取设备的通信地址，仅支持点对点通讯的情况，返回地址域数据，例如：149100007290" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public OperateResult<string> ReadAddress( )
		{
			return dlt == null ? new OperateResult<string>( "Device is null" ) : dlt.ReadAddress( );
		}

		/// <inheritdoc cref="DLT645.ActiveDeveice"/>
		[HslMqttApi( Description = "激活设备的命令，只发送数据到设备，不等待设备数据返回" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult ActiveDeveice( )
		{
			return dlt == null ? new OperateResult( "Device is null" ) : dlt.ActiveDeveice( );
		}

		/// <inheritdoc cref="DLT645.BroadcastTime(DateTime)"/>
		[HslMqttApi( Description = "广播指定的时间，强制从站与主站时间同步，传入System.DateTime时间对象，没有数据返回。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult BroadcastTime( DateTime dateTime )
		{
			return dlt == null ? new OperateResult( "Device is null" ) : dlt.BroadcastTime( dateTime );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[DLT645OverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private DLT645OverTcp dlt;               // 核心交互对象
		private NodeDlt645OverTcp node;          // 节点信息

		#endregion
	}
}
