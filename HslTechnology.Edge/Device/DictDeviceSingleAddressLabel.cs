using HslCommunication;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Device.Base;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static HslTechnology.Edge.Device.Base.DeviceCoreBase;

namespace HslTechnology.Edge.Device
{
    /// <summary>
    /// 基于词典的设备地址标签集合
    /// </summary>
    public class DictDeviceSingleAddressLabel
	{
		#region Constructor

		public DictDeviceSingleAddressLabel( DeviceCoreBase device, EdgeDeviceResources edge )
		{
			this.dicts = new Dictionary<string, DeviceSingleAddressLabel>( );
			this.device = device;
		}

		#endregion


		#region Public Method

		public void AddDeviceSingleAddress( DeviceSingleAddressLabel addressLabel, bool subscription )
		{
			if (this.dicts.ContainsKey( addressLabel.Name ))
				this.device.LogNet?.WriteError( this.device.GetDeviceNameWithPath( ), ToString( ), $"设备点位[{addressLabel.Name}]配置重复，请仔细检查，当前点位跳过。" );
			else
			{
				// 这里是否开启订阅才真的的实现值变化通知技术
				if (subscription) addressLabel.EnableSubscription = subscription;
				addressLabel.OnValueChanged += AddressLabel_OnValueChanged;
				this.dicts.Add( addressLabel.Name, addressLabel );
			}
		}

		private void AddressLabel_OnValueChanged( DeviceSingleAddressLabel addressLabel, JToken value )
		{
			if (addressLabel.EnableSubscription)
			{
				string topic = string.Empty;

				if (this.edge.MqttServer != null)
				{
					if (string.IsNullOrEmpty( topic )) topic = this.device.GetDeviceNameWithPath( ) + HslTechnologyExtension.DeviceDefaultSplit + addressLabel.Name;
					this.edge.MqttServer?.PublishTopicPayload( topic, Encoding.UTF8.GetBytes( value.ToString( ) ), retain: true );
				}

				if (this.edge.WebSocketServer != null)
				{
					if (string.IsNullOrEmpty( topic )) topic = this.device.GetDeviceNameWithPath( ) + HslTechnologyExtension.DeviceDefaultSplit + addressLabel.Name;
					JObject json = new JObject( );
					json.Add( "Topic", topic );
					json.Add( "Value", value );
					this.edge.WebSocketServer?.PublishClientPayload( topic, json.ToString( ) );
				}

				OnDeviceValueChanged?.Invoke( device, addressLabel, value );
			}

			this.device.MarkDataChanged( );                                             // 标记当前的设备的数据发生了变化
		}

		/// <summary>
		/// 获取设备信号的地址数据信息
		/// </summary>
		/// <returns>设备的地址数据信息</returns>
		public DeviceSingleAddressLabel[] GetDeviceSingleAddresses( )
		{
			List<DeviceSingleAddressLabel> singleAddressLabels = new List<DeviceSingleAddressLabel>( this.dicts.Count );
			foreach (var item in this.dicts.Values)
			{
				singleAddressLabels.Add( item );
			}
			return singleAddressLabels.ToArray( );
		}

		/// <summary>
		/// 根据给定的<see cref="MqttClient"/>客户端，将单个点位的数据上传到服务器上去，如果
		/// </summary>
		/// <param name="mqttClient"></param>
		/// <param name="isRunning"></param>
		/// <param name="retain"></param>
		/// <exception cref="Exception"></exception>
		public void UploadByDeviceTag( MqttClient mqttClient, bool isRunning, bool retain )
		{
			foreach (var item in this.dicts)
			{
				if (!isRunning) return;
				DeviceSingleAddressLabel addressLabel = item.Value;
				if (addressLabel == null) continue;
				if (!addressLabel.EnableSubscription) continue;

				OperateResult publish = mqttClient.PublishMessage( new MqttApplicationMessage( )
				{
					Topic = device.GetDeviceNameWithPath( ) + HslTechnologyExtension.DeviceDefaultSplit + addressLabel.Name,
					Payload = Encoding.UTF8.GetBytes( addressLabel.Value == null ? string.Empty : addressLabel.Value.ToString( ) ),
					QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce,
					Retain = retain,
				} );
				if (!publish.IsSuccess) throw new Exception( publish.Message );
			}
		}

		#endregion

		#region Event

		/// <summary>
		/// 数据值变化的事件
		/// </summary>
		public event DelegateOnDeviceValueChanged OnDeviceValueChanged;

		#endregion


















		private Dictionary<string, DeviceSingleAddressLabel> dicts;                 // 数据词典信息
		private DeviceCoreBase device;                                              // 真实的设备信息
		private EdgeDeviceResources edge;                                           // 网关资源信息

	}
}
