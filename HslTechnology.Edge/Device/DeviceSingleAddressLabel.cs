using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Threading;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Node.Render;
using System.Dynamic;

namespace HslTechnology.Edge.Device
{
	/// <summary>
	/// 在设备数据里，关于所有的单个的数据标识信息，如果是结构体，会被拆分成实际的一个个子标签
	/// </summary>
	public class DeviceSingleAddressLabel
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public DeviceSingleAddressLabel( )
		{
			Enable = true;
			EnableSubscription = false;
		}

		/// <summary>
		/// 根据指定的是标量请求来实例化一个对象
		/// </summary>
		/// <param name="scalarReadRequest">标量请求信息</param>
		public DeviceSingleAddressLabel( ScalarReadRequest scalarReadRequest ) : this( )
		{
			Name                      = scalarReadRequest.Name;
			DisplayName               = scalarReadRequest.DisplayName;
			PhysicalAddress           = scalarReadRequest.Address;
			Enable                    = scalarReadRequest.Enable;
			ForbidRemoteWrite         = scalarReadRequest.ForbidRemoteWrite;
			EnableSubscription        = scalarReadRequest.Subscription;
			ScalarTransform           = scalarReadRequest;
			DataType                  = ScalarDataNode.ParseFrom( scalarReadRequest, AccessLevel.Read ).DataType;

			if (scalarReadRequest.RequestType == RequestType.ScalarCache)
			{
				this.isScalarCacheRequest = true;
				this.scalarCacheRequest = scalarReadRequest as ScalarCacheRequest;
			}
		}

		/// <summary>
		/// 根据指定的原始字节请求和配置的标量分析来实例化一个对象
		/// </summary>
		/// <param name="sourceReadRequest">原始字节请求的信息</param>
		/// <param name="regularScalarNode">标量分析的节点</param>
		public DeviceSingleAddressLabel( SourceReadRequest sourceReadRequest, RegularScalarNode regularScalarNode ) : this( )
		{
			Name               = regularScalarNode.Name;
			DisplayName        = regularScalarNode.DisplayName;
			Enable             = sourceReadRequest.Enable;
			ForbidRemoteWrite  = regularScalarNode.ForbidRemoteWrite;
			EnableSubscription = regularScalarNode.Subscription;
			ScalarTransform    = regularScalarNode;
			DataType           = ScalarDataNode.ParseFrom( regularScalarNode, AccessLevel.Read ).DataType;
		}

		/// <summary>
		/// 根据指定的原始字节请求和配置的标量分析来实例化一个对象
		/// </summary>
		/// <param name="sourceReadRequest">原始字节请求的信息</param>
		/// <param name="regularStructNode">结构体的基本信息</param>
		/// <param name="index">如果结构体是列表，那么Index就是索引，否则为-1</param>
		public DeviceSingleAddressLabel( SourceReadRequest sourceReadRequest, RegularStructNode regularStructNode, int index ) : this( )
		{
			if (index < 0)
			{
				Name        = regularStructNode.Name;
				DisplayName = regularStructNode.DisplayName;
			}
			else
			{
				Name        = regularStructNode.Name + $"[{index}]";
				DisplayName = regularStructNode.GetDisplayName( NodeDisplayMode.ShowDisplayName ) + $"[{index}]";
			}
			Enable            = sourceReadRequest.Enable;
			ForbidRemoteWrite = true;
			DataType          = DataType.Struct;
		}

		/// <summary>
		/// 根据指定的原始字节请求和配置的标量分析来实例化一个对象
		/// </summary>
		/// <param name="sourceReadRequest">原始字节请求的信息</param>
		/// <param name="regularStructNode">结构体的基本信息</param>
		/// <param name="index">如果结构体是列表，那么Index就是索引，否则为-1</param>
		/// <param name="regularScalarNode">标量分析的节点</param>
		public DeviceSingleAddressLabel( SourceReadRequest sourceReadRequest, RegularStructNode regularStructNode, int index, RegularScalarNode regularScalarNode ) : this( )
		{
			if (regularStructNode.StructParseType == Node.Core.ParseType.Unflod)
			{
				Name            = regularScalarNode.Name;
				DisplayName     = regularScalarNode.DisplayName;
			}
			else
			{
				if (index < 0)
				{
					Name = regularStructNode.Name + "." + regularScalarNode.Name;
					DisplayName = regularStructNode.GetDisplayName( NodeDisplayMode.ShowDisplayName ) + "." + regularScalarNode.GetDisplayName( NodeDisplayMode.ShowDisplayName );
				}
				else
				{
					Name = regularStructNode.Name + $"[{index}]." + regularScalarNode.Name;
					DisplayName = regularStructNode.GetDisplayName( NodeDisplayMode.ShowDisplayName ) + $"[{index}]." + regularScalarNode.GetDisplayName( NodeDisplayMode.ShowDisplayName );
				}
			}
			Enable             = sourceReadRequest.Enable;
			ForbidRemoteWrite  = regularScalarNode.ForbidRemoteWrite;
			EnableSubscription = regularScalarNode.Subscription;
			ScalarTransform    = regularScalarNode;
			DataType           = ScalarDataNode.ParseFrom( regularScalarNode, AccessLevel.Read ).DataType;
		}

		#endregion

		/// <summary>
		/// 数据的名称信息
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// 当前的显示的名称信息
		/// </summary>
		public string DisplayName { get; set; }

		/// <summary>
		/// 实际的PLC的物理地址信息
		/// </summary>
		public string PhysicalAddress { get; set; }

		/// <summary>
		/// 获取或设置当前的数据标签是否支持写入操作
		/// </summary>
		public bool Enable { get; set; }

		/// <summary>
		/// 获取或设置是否被设置为禁止远程写入
		/// </summary>
		public bool ForbidRemoteWrite { get; set; }

		/// <summary>
		/// 当前的标签值的数据信息
		/// </summary>
		public JToken Value { get; set; }

		/// <summary>
		/// 获取或设置当前的变化规则
		/// </summary>
		[JsonIgnore]
		public IScalarTransform ScalarTransform { get; set; }

		/// <summary>
		/// 是否启用订阅的操作
		/// </summary>
		public bool EnableSubscription { get; set; }

		/// <summary>
		/// 当前数据的类型信息
		/// </summary>
		public DataType DataType { get; set; }

		#region Event

		/// <summary>
		/// 当前的数据值变化的委托信息
		/// </summary>
		/// <param name="addressLabel">触发的地址标签对象</param>
		/// <param name="value">变化之后的数据值</param>
		public delegate void DelegateOnValueChanged( DeviceSingleAddressLabel addressLabel, JToken value );

		/// <summary>
		/// 数据值变化的事件
		/// </summary>
		public event DelegateOnValueChanged OnValueChanged;

		#endregion

		private void SetTransformValue( IScalarTransform transform, dynamic value )
		{
			double transValue = RegularHelper.TransValueByScalarTransform( transform, value );
			if (EnableSubscription)
			{
				if (Interlocked.CompareExchange( ref initialization, 1, 0 ) == 0)
				{
					OnValueChanged?.Invoke( this, new JValue( transValue ) );
				}
				else if (this.Value != null)
				{
					double valueOld = this.Value.Value<double>( );
					if (valueOld != transValue)
						OnValueChanged?.Invoke( this, new JValue( transValue ) );
				}
			}
			this.Value = new JValue( transValue );
		}

		/// <summary>
		/// 获取当前的地址是否绑定的缓存的内存标量数据请求，默认返回 False
		/// </summary>
		/// <returns>如果绑定了，则返回 True</returns>
		public bool BindingScalarCacheRequest( )
		{
			return this.isScalarCacheRequest;
		}

		/// <summary>
		/// 获取当前的地址绑定的标量缓存的请求，如果不是该类型的请求，则返回 NULL
		/// </summary>
		/// <returns>绑定的标量请求</returns>
		public ScalarCacheRequest GetScalarCacheRequest( ) => this.scalarCacheRequest;

		/// <summary>
		/// 设置一个JSON对象的值
		/// </summary>
		public void SetNewValue( JToken token )
		{
			this.Value = token;
		}

		private bool HasArrayChanged<T>( T[] value )
		{
			JArray old = this.Value as JArray;
			if (old == null) return true;

			bool changed = false;
			if (old.Count != value.Length)
			{
				changed = true;
			}
			else
			{
				for (int i = 0; i < old.Count; i++)
				{
					if (!old[i].Value<T>( ).Equals( value[i]))
					{
						changed = true;
						break;
					}
				}
			}
			return changed;
		}

		/// <inheritdoc cref="SetNewSingleValue{T}(T, EdgeDeviceResources)"/>
		public void SetNewArrayValue<T>( T[] value, EdgeDeviceResources edgeResuources )
		{
			if (EnableSubscription || edgeResuources.EdgeSettings.UploadInfo.EnableSubscription)
			{
				if (Interlocked.CompareExchange( ref initialization, 1, 0 ) == 0)
				{
					OnValueChanged?.Invoke( this, new JArray( value ) );
				}
				else if (this.Value != null && this.Value.Type != JTokenType.Null)
				{
					if (HasArrayChanged( value )) OnValueChanged?.Invoke( this, new JArray( value ) );
				}
				else
				{
					OnValueChanged?.Invoke( this, new JArray( value ) );
				}
			}
			this.Value = new JArray( value );
		}

		/// <summary>
		/// 设置一个新的值到当前的地址标签里，支持泛型值设置，如果值发现了变化，并且开启了订阅模式的话，将触发 <see cref="OnValueChanged"/> 事件
		/// </summary>
		/// <typeparam name="T">新值的类型信息</typeparam>
		/// <param name="value">新的值信息</param>
		/// <param name="edgeResuources">当前网关的资源信息</param>
		public void SetNewSingleValue<T>( T value, EdgeDeviceResources edgeResuources )
		{
			JValue jValue = new JValue( value );
			if (EnableSubscription || edgeResuources.EdgeSettings.UploadInfo.EnableSubscription)
			{
				if (Interlocked.CompareExchange( ref initialization, 1, 0 ) == 0)
				{
					OnValueChanged?.Invoke( this, jValue );
				}
				else if (this.Value != null && this.Value.Type != JTokenType.Null)
				{
					T valueOld = this.Value.Value<T>( );
					if (!valueOld.Equals( value )) OnValueChanged?.Invoke( this, jValue );
				}
				else
				{
					OnValueChanged?.Invoke( this, jValue );
				}
			}
			this.Value = jValue;
		}

		/// <inheritdoc cref="SetNewSingleValue{T}(T, EdgeDeviceResources)"/>
		public void SetNewStringValue( string value, EdgeDeviceResources edgeResuources )
		{
			if (EnableSubscription || edgeResuources.EdgeSettings.UploadInfo.EnableSubscription)
			{
				if (Interlocked.CompareExchange( ref initialization, 1, 0 ) == 0)
				{
					OnValueChanged?.Invoke( this, new JValue( value ) );
				}
				else if (this.Value != null && this.Value.Type != JTokenType.Null)
				{
					string valueOld = this.Value.Value<string>( );
					if (valueOld != value)
						OnValueChanged?.Invoke( this, new JValue( value ) );
				}
			}
			this.Value = new JValue( value );
		}

		/// <summary>
		/// 根据给定的Jtoken对象，获取到实际的数据类型的对象
		/// </summary>
		/// <param name="token">给定的token值</param>
		/// <returns>实际的类型信息</returns>
		private dynamic GetDynamicValue( JToken token )
		{
			if (token == null) return null;
			if (token.Type == JTokenType.Null)    return null;
			if (token.Type == JTokenType.String)  return token.Value<string>( );
			if (token.Type == JTokenType.Integer) return token.Value<long>( );
			if (token.Type == JTokenType.Float)   return token.Value<double>( );
			if (token.Type == JTokenType.Boolean) return token.Value<bool>( );
			if (token.Type == JTokenType.Date)    return token.Value<DateTime>( );
			if (token.Type == JTokenType.Guid)    return token.Value<Guid>( );
			if (token.Type == JTokenType.Array && token.HasValues)
			{
				// 如果是数组的情况
				JEnumerable<JToken> tokens = token.Children( );
				JToken item = tokens.ElementAt( 0 );

				if (item.Type == JTokenType.Null)    return tokens.Select( m => (object)null ).ToArray( );
				if (item.Type == JTokenType.String)  return tokens.Select( m => m.Value<string>( ) ).ToArray( );
				if (item.Type == JTokenType.Integer) return tokens.Select( m => m.Value<long>( ) ).ToArray( );
				if (item.Type == JTokenType.Float)   return tokens.Select( m => m.Value<double>( ) ).ToArray( );
				if (item.Type == JTokenType.Boolean) return tokens.Select( m => m.Value<bool>( ) ).ToArray( );
				if (item.Type == JTokenType.Date)    return tokens.Select( m => m.Value<DateTime>( ) ).ToArray( );
				if (item.Type == JTokenType.Guid)    return tokens.Select( m => m.Value<Guid>( ) ).ToArray( );
				if (item.Type == JTokenType.Object)  return tokens.Select( m => GetDynamicValue( m ) ).ToArray( );

				return null; // token.Children( ).Select( m => GetDynamicValue( m ) ).ToArray( );
			}
			if (token.Type == JTokenType.Object && token.HasValues)
			{
				// 如果是自定义的对象
				IEnumerable<JProperty> childs = ((JObject)token).Properties( );
				IDictionary<string, object> dict = new ExpandoObject( );
				foreach (var item in childs)
				{
					dict.Add( item.Name, GetDynamicValue( item.Value ) );
				}
				return dict as ExpandoObject;
			}
			// 不支持的类型
			return null;
		}

		/// <summary>
		/// 强制引发一个值变化的事件，从而触发值变化的操作，例如上传数据到服务器上去
		/// </summary>
		public void RaiseValueChangeEvent( )
		{
			if (this.Value != null && this.Value.Type != JTokenType.Null) OnValueChanged?.Invoke( this, this.Value );
		}

		/// <summary>
		/// 获取当前值的动态值信息，主要针对标签的单数据类型，用于定时写入的脚本功能获取数据对象信息
		/// </summary>
		/// <returns>动态值对象</returns>
		public dynamic GetDynamicValue( )
		{
			return GetDynamicValue( this.Value );
		}

		#region Private Member

		private int initialization = 0;
		private bool isScalarCacheRequest = false;
		private ScalarCacheRequest scalarCacheRequest;

		#endregion
	}
}
