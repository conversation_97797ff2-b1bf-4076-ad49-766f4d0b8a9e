using HslCommunication.Core;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;

namespace HslTechnology.Edge.Device
{
	/// <summary>
	/// 设备的富数据对象，支持动态的设置和获取数据信息，支持获取所有的数据对象，或是其中某一个的数据对象
	/// </summary>
	public class DeviceData
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的设备富数据对象
		/// </summary>
		public DeviceData( )
		{
			jObjectData              = new JObject( );
			jsonTmp                  = jObjectData.ToString( );
			jsonLock                 = new SimpleHybirdLock( );
		}

		#endregion

		#region JSON Object

		private JObject GetSaveJObject( )
		{
			if (isStoreToTemp)
			{
				if (jObjectTempStore == null) jObjectTempStore = new JObject( );
				return jObjectTempStore;
			}
			return jObjectData;
		}

		/// <summary>
		/// 通过节点值名称，获取本设备信息的值，如果节点名称不存在，则返回为null
		/// </summary>
		/// <param name="name">节点值名称</param>
		/// <returns>返回富数据中的JToken数值</returns>
		public JToken GetJTokenValueByName( string name )
		{
			JToken result = null;
			jsonLock.Enter( );
			try
			{
				if (jObjectData.ContainsKey( name ))
				{
					result = jObjectData[name];
				}
				jsonLock.Leave( );
				return result;
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 设置JSON的数据对象，需要指定是否为数组对象
		/// </summary>
		/// <param name="name">数据名称</param>
		/// <param name="value">数据值</param>
		/// <param name="isArray">是否是数组</param>
		public void SetJsonValue( string name, dynamic value, bool isArray )
		{
			jsonLock.Enter( );

			JObject json = GetSaveJObject( );

			try
			{
				if (isArray)
					json[name] = new JArray( value );
				else
					json[name] = new JValue( value );

				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 设置泛型的数组对象，然后赋值到缓存数据中去
		/// </summary>
		/// <typeparam name="T">类型信息</typeparam>
		/// <param name="name">数据名称</param>
		/// <param name="value">值信息</param>
		public void SetArrayValue<T>( string name, T[] value )
		{
			jsonLock.Enter( );

			JObject json = GetSaveJObject( );

			try
			{
				json[name] = new JArray( value );
				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 设置泛型的单个的数值，
		/// </summary>
		/// <typeparam name="T">泛型的单个的数值</typeparam>
		/// <param name="name">数据名信息</param>
		/// <param name="value">值信息</param>
		public void SetSingleValue<T>( string name, T value )
		{
			jsonLock.Enter( );

			JObject json = GetSaveJObject( );

			try
			{
				json[name] = new JValue( value );
				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 设置json的对象内容
		/// </summary>
		/// <param name="name">数据名称</param>
		/// <param name="value">数据值信息</param>
		public void SetJToken( string name, JToken value )
		{
			jsonLock.Enter( );

			JObject json = GetSaveJObject( );

			try
			{
				json[name] = value;
				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 设置jarray的对象内容
		/// </summary>
		/// <param name="name">数据名称</param>
		/// <param name="value">数据值信息</param>
		public void SetJArray( string name, JArray value )
		{
			jsonLock.Enter( );

			JObject json = GetSaveJObject( );

			try
			{
				json[name] = value;

				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 移除数据中相关的属性数据
		/// </summary>
		/// <param name="name">数据名称</param>
		public void RemoveValue( string name )
		{
			jsonLock.Enter( );

			try
			{
				if (jObjectData.ContainsKey( name )) jObjectData.Remove( name );

				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 设置字符串数据到设备缓存数据上去，如果字符串为空，则自动移除该数据信息，减少通信数据内容
		/// </summary>
		/// <param name="name">数据的属性名称</param>
		/// <param name="value">数据值</param>
		public void SetStringValue( string name, string value )
		{
			jsonLock.Enter( );
			try
			{
				if (string.IsNullOrEmpty( value ))
				{
					jObjectData.Remove( name );
				}
				else
				{
					jObjectData[name] = new JValue( value );
				}

				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 更新所有数据的缓存信息
		/// </summary>
		public void UpdateJsonTmp( )
		{
			jsonLock.Enter( );
			try
			{
				jsonTmp = jObjectData.ToString( Newtonsoft.Json.Formatting.None );
				// JsonObjTmp = JObject.Parse( jsonTmp );
				JsonObjTmp = (JObject)jObjectData.DeepClone( );
				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		/// <summary>
		/// 启用将数据暂时存储到缓存中，等待调用方法 <see cref="FinishStoreTemp"/> 来统一更新数据到最终的数据上
		/// </summary>
		public void StartStoreTemp( )
		{
			this.isStoreToTemp = true;
		}

		/// <summary>
		/// 结束当前的缓存操作，将所有的数据存储到结果对象上去
		/// </summary>
		/// <param name="hasData">是否有数据写入的操作</param>
		public void FinishStoreTemp( bool hasData = true )
		{
			if (!hasData)
			{
				this.isStoreToTemp = false;
				return;
			}

			jsonLock.Enter( );
			try
			{
				if (this.isStoreToTemp)
				{
					if (jObjectTempStore != null)
					{
						foreach (JProperty jProperty in jObjectTempStore.Properties( ))
						{
							jObjectData[jProperty.Name] = jProperty.Value;
						}

						jObjectTempStore.RemoveAll( );
					}
					this.isStoreToTemp = false;
				}
				jsonLock.Leave( );
			}
			catch
			{
				jsonLock.Leave( );
				throw;
			}
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 获取整个设备的数据信息
		/// </summary>
		public string DeviceJsonTemp => jsonTmp;
		
		/// <summary>
		/// 整个设备对象的
		/// </summary>
		public JObject DeviceJsonClone => JsonObjTmp;

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"DeviceData";

		#endregion

		#region Private Member

		private readonly JObject jObjectData;                                // JSON数据中心
		private string jsonTmp = string.Empty;                               // JSON数据缓存
		private JObject JsonObjTmp = null;                                   // JSON的对象缓存中心
		private SimpleHybirdLock jsonLock;                                   // JSON对象的安全锁

		private bool isStoreToTemp = false;                                    // 指示数据是否暂时存储到缓存中
		private JObject jObjectTempStore;                                    // 暂时存储的数据地方

		#endregion
	}
}
