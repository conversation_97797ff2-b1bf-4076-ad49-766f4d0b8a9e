using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.MQTT;
using HslCommunication;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node;
using HslCommunication.Reflection;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.Hsl
{
	/// <summary>
	/// MQTT的RPC接口信息
	/// </summary>
	public class DeviceMqttRpcClient : DeviceCore
	{
		/// <summary>
		/// 实例化一个MQTT-RPC的客户端对象信息
		/// </summary>
		/// <param name="element">设备的配置信息</param>
		public DeviceMqttRpcClient( XElement element ) : base( element ) { }

		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeMqttRpcClient( element );
			this.mqttSyncClient = new MqttSyncClient(new MqttConnectionOptions( )
			{
				IpAddress      = this.node.IpAddress,
				Port           = this.node.Port,
				ConnectTimeout = this.node.ConnectTimeOut,
				Credentials    = new MqttCredential(this.node.UserName, this.node.Password),
				UseRSAProvider = this.node.UseRSAEncrypt,
			} );
		}

		#region Override Method

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			this.mqttSyncClient?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			this.mqttSyncClient?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.mqttSyncClient == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = mqttSyncClient.ConnectServer( );
			if (connect.IsSuccess) mqttSyncClient.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region Read Support

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualBoolAsync( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<bool> read = await this.mqttSyncClient.ReadRpcAsync<bool>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<bool[]> read = await this.mqttSyncClient.ReadRpcAsync<bool[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualByteAsync( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<byte> read = await this.mqttSyncClient.ReadRpcAsync<byte>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<byte[]> read = await this.mqttSyncClient.ReadRpcAsync<byte[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualSByteAsync( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<sbyte> read = await this.mqttSyncClient.ReadRpcAsync<sbyte>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<sbyte[]> read = await this.mqttSyncClient.ReadRpcAsync<sbyte[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualInt16Async( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<short> read = await this.mqttSyncClient.ReadRpcAsync<short>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<short[]> read = await this.mqttSyncClient.ReadRpcAsync<short[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualUInt16Async( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<ushort> read = await this.mqttSyncClient.ReadRpcAsync<ushort>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<ushort[]> read = await this.mqttSyncClient.ReadRpcAsync<ushort[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualInt32Async( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<int> read = await this.mqttSyncClient.ReadRpcAsync<int>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<int[]> read = await this.mqttSyncClient.ReadRpcAsync<int[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualUInt32Async( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<uint> read = await this.mqttSyncClient.ReadRpcAsync<uint>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<uint[]> read = await this.mqttSyncClient.ReadRpcAsync<uint[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualInt64Async( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<long> read = await this.mqttSyncClient.ReadRpcAsync<long>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<long[]> read = await this.mqttSyncClient.ReadRpcAsync<long[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualUInt64Async( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<ulong> read = await this.mqttSyncClient.ReadRpcAsync<ulong>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<ulong[]> read = await this.mqttSyncClient.ReadRpcAsync<ulong[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualFloatAsync( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<float> read = await this.mqttSyncClient.ReadRpcAsync<float>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<float[]> read = await this.mqttSyncClient.ReadRpcAsync<float[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualDoubleAsync( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<double> read = await this.mqttSyncClient.ReadRpcAsync<double>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<double[]> read = await this.mqttSyncClient.ReadRpcAsync<double[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				OperateResult<string> read = await this.mqttSyncClient.ReadRpcAsync<string>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<string[]> read = await this.mqttSyncClient.ReadRpcAsync<string[]>( readCommand, new { data = scalarRequest.Address } );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualAsync( RequestBase request )
		{
			if (request.RequestType == RequestType.ScalarRead)
			{
				return await base.ReadActualAsync( request );
			}
			else if (request.RequestType == RequestType.SourceRead)
			{
				SourceReadRequest sourceReadRequest = (SourceReadRequest)request;
				OperateResult<string, byte[]> read = await this.mqttSyncClient.ReadAsync( readCommand, Encoding.UTF8.GetBytes( (new { data = sourceReadRequest.Address }).ToJsonString( ) ) );

				return DealWithSourceReadResult( read.Convert( read.Content2 ), content => ParseFromRequest( content, sourceReadRequest, ByteTransform ) );
			}
			else
			{
				return new OperateResult( "Not Supported Request" );
			}
		}

		/// <inheritdoc/>
		public override OperateResult WriteValueByName( DeviceSingleAddressLabel addressLabel, string value )
		{
			return this.mqttSyncClient.ReadRpc<string>( writeCommand, new { data = addressLabel.PhysicalAddress, value = value } );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="MqttSyncClient.ReadString(string, string, Action{long, long}, Action{string, string}, Action{long, long})"/>
		[HslMqttApi( Description = "从服务器请求数据信息，包括读写操作。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadString( string topic, string payload )
		{
			OperateResult<string, string> read = await this.mqttSyncClient.ReadStringAsync( topic, payload );
			if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>( read );

			return OperateResult.CreateSuccessResult( read.Content2 );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"DeviceMqttRpcClient[{this.node.GetSocketInfo( )}]";

		#endregion

		#region Private Member

		private const string readCommand = "Edge/DeviceData";
		private const string writeCommand = "Edge/WriteData";
		private NodeMqttRpcClient node;
		private MqttSyncClient mqttSyncClient;

		#endregion
	}
}
