using HslCommunication.Core.Pipe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device
{
	/// <summary>
	/// 网关的管道接口
	/// </summary>
	public interface IEdgePipe
	{
		/// <summary>
		/// 获取当前的管道信息
		/// </summary>
		/// <returns>管道类</returns>
		PipeBase GetPipe( );

		/// <summary>
		/// 关闭当前的管道
		/// </summary>
		void ClosePipe( );
	}
}
