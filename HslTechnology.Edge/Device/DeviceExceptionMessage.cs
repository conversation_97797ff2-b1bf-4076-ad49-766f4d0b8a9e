using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device
{
	public class DeviceExceptionMessage
	{
		/// <summary>
		/// 获取或设置设备的名称
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// 设备开始运行的时间
		/// </summary>
		public DateTime StartTime { get; set; }

		/// <summary>
		/// 设备的活动时间
		/// </summary>
		public DateTime ActiveTime { get; set; }

		/// <summary>
		/// 设备异常的消息
		/// </summary>
		public string Message { get; set; }
	}
}
