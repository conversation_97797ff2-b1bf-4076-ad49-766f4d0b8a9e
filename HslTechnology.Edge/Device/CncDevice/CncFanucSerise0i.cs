using HslCommunication;
using HslCommunication.CNC.Fanuc;
using HslCommunication.Core.Address;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Reflection;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.CncDevice
{
	/// <summary>
	/// 数控机床的设备对象
	/// </summary>
	public class CncFanucSerise0i : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public CncFanucSerise0i( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeCncFanucSerise           = new NodeCncFanucSerise( element );
			this.fanucSeries0I                = new FanucSeries0i( nodeCncFanucSerise.IpAddress, nodeCncFanucSerise.Port );
			this.fanucSeries0I.ConnectTimeOut = nodeCncFanucSerise.ConnectTimeOut;
			this.fanucSeries0I.ReceiveTimeOut = nodeCncFanucSerise.ReceiveTimeOut;
			this.fanucSeries0I.TextEncoding   = RegularScalarNode.EncodingGB.Value;
			this.UniqueId                     = this.fanucSeries0I.ConnectionId;
			this.RegisterRpcService( deviceResources.MqttServer, deviceResources.HttpServer, nodeCncFanucSerise, fanucSeries0I );
		}

		#endregion

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest( SourceReadRequest sourceReadRequest, int byteOffset, int index, IScalarTransform scalarTransform )
		{
			OperateResult<FanucPMCAddress> analysis = FanucPMCAddress.ParseFrom( sourceReadRequest.Address, (ushort)sourceReadRequest.GetLength( ) );
			if (!analysis.IsSuccess) return string.Empty;

			try
			{
				if (scalarTransform.IsBoolRegular( )) return string.Empty;

				string type = sourceReadRequest.Address.Substring( 0, 1 );
				int add = Convert.ToInt32( sourceReadRequest.Address.Substring( 1 ) );

				return type + (add + byteOffset + index).ToString( );
			}
			catch
			{
				return string.Empty;
			}
		}

		/// <inheritdoc/>
		public override OperateResult WriteValueByName( DeviceSingleAddressLabel addressLabel, string value )
		{
			OperateResult<byte[]> read = RegularHelper.GetSourceDataFromString( this.fanucSeries0I.ByteTransform, addressLabel.ScalarTransform, value );
			if (!read.IsSuccess) return read;

			return this.fanucSeries0I.WritePMCData( addressLabel.PhysicalAddress, read.Content );
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualAsync( RequestBase request )
		{
			if (request.RequestType == Node.RequestType.ScalarRead)
			{
				ScalarReadRequest scalarRequest = (ScalarReadRequest)request;
				if (scalarRequest.Address == "主轴转速")
				{
					OperateResult<double, double> read = await this.fanucSeries0I.ReadSpindleSpeedAndFeedRateAsync( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content1, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "进给倍率")
				{
					OperateResult<double, double> read = this.fanucSeries0I.ReadSpindleSpeedAndFeedRate( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content2, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "当前程序名")
				{
					OperateResult<string, int> read = this.fanucSeries0I.ReadSystemProgramCurrent( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content1, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "当前程序号")
				{
					OperateResult<string, int> read = this.fanucSeries0I.ReadSystemProgramCurrent( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content2, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "伺服负载")
				{
					OperateResult<double[]> read = this.fanucSeries0I.ReadFanucAxisLoad( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, true );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "机床坐标")
				{
					OperateResult<SysAllCoors> read = this.fanucSeries0I.ReadSysAllCoors( );
					if (read.IsSuccess)
					{
						SetJsonObjectValue( scalarRequest.Name, JObject.FromObject( read.Content ) );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "报警信息")
				{
					OperateResult<SysAlarm[]> read = this.fanucSeries0I.ReadSystemAlarm( );
					if (read.IsSuccess)
					{
						SetJsonObjectValue( scalarRequest.Name, JArray.FromObject( read.Content ) );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "运行时间")
				{
					OperateResult<long> read = this.fanucSeries0I.ReadTimeData( 1 );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "循环时间")
				{
					OperateResult<long> read = this.fanucSeries0I.ReadTimeData( 3 );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "报警状态")
				{
					OperateResult<int> read = this.fanucSeries0I.ReadAlarmStatus( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "系统信息")
				{
					OperateResult<SysStatusInfo> read = this.fanucSeries0I.ReadSysStatusInfo( );
					if (read.IsSuccess)
					{
						SetJsonObjectValue( scalarRequest.Name, JObject.FromObject( read.Content ) );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "程序列表")
				{
					OperateResult<int[]> read = this.fanucSeries0I.ReadProgramList( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, true );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "刀具补偿列表")
				{
					OperateResult<CutterInfo[]> read = this.fanucSeries0I.ReadCutterInfos( );
					if (read.IsSuccess)
					{
						SetJsonObjectValue( scalarRequest.Name, JArray.FromObject( read.Content ) );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "工件尺寸")
				{
					OperateResult<double[]> read = this.fanucSeries0I.ReadDeviceWorkPiecesSize( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, true );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "程序前台路径")
				{
					OperateResult<string> read = this.fanucSeries0I.ReadCurrentForegroundDir( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "机床实时时间")
				{
					OperateResult<DateTime> read = this.fanucSeries0I.ReadCurrentDateTime( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content.ToString( "yyyy-MM-dd HH:mm:ss" ), false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "已加工零件数")
				{
					OperateResult<int> read = this.fanucSeries0I.ReadCurrentProduceCount( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address == "总加工零件数")
				{
					OperateResult<int> read = this.fanucSeries0I.ReadExpectProduceCount( );
					if (read.IsSuccess)
					{
						SetJsonValue( scalarRequest.Name, read.Content, false );
					}
					if (!read.IsSuccess) return read;
				}
				else if (scalarRequest.Address.StartsWith( "宏变量=" ))
				{
					if (int.TryParse( scalarRequest.Address.Substring( 4 ), out int add ))
					{
						try
						{
							OperateResult<double> read = this.fanucSeries0I.ReadSystemMacroValue( add );
							if (read.IsSuccess)
							{
								SetJsonValue( scalarRequest.Name, read.Content, false );
							}
							if (!read.IsSuccess) return read;
						}
						catch (Exception ex)
						{
							return new OperateResult( $"采集变量[{scalarRequest.Address}]失败，原因：" + ex.Message );
						}
					}
					else
					{
						return new OperateResult( "MacroValue Address is format error:" + scalarRequest.Address.Substring( 4 ) );
					}
				}
				else
					return new OperateResult( "Not implementation type: " + scalarRequest.DataTypeCode );

				return OperateResult.CreateSuccessResult( );
			}
			else if (request.RequestType == Node.RequestType.SourceRead)
			{
				SourceReadRequest sourceReadRequest = (SourceReadRequest)request;

				if (ushort.TryParse( sourceReadRequest.Length, out ushort length ))
				{
					OperateResult<byte[]> read = await this.fanucSeries0I.ReadPMCDataAsync( sourceReadRequest.Address, length );
					return DealWithSourceReadResult( read, content => ParseFromRequest( content, sourceReadRequest, this.fanucSeries0I.ByteTransform ) );
				}
				else
					return new OperateResult<byte[]>( "SourceRead Request Length formate wrong: " + sourceReadRequest.Length );
			}
			else
			{
				return new OperateResult( "不支持的请求类型!" );
			}
		}

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => this.fanucSeries0I?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => this.fanucSeries0I?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.fanucSeries0I == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = fanucSeries0I.ConnectServer( );
			if (connect.IsSuccess) fanucSeries0I.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="FanucSeries0i.ReadSysInfo"/>
		[RpcExtensionInfo( DataType = DataType.Class )]
		[HslMqttApi( Description = "获取fanuc机床设备的基本信息，型号，轴数量等等" )]
		public OperateResult<FanucSysInfo> ReadSysInfo( )
		{
			OperateResult<FanucSysInfo> read = this.fanucSeries0I.ReadSysInfo( );
			if (read.IsSuccess == false && read.Message == "Must connect device first!")
			{
				OperateResult connect = this.fanucSeries0I.ConnectServer( );
				if (connect.IsSuccess) return this.fanucSeries0I.ReadSysInfo( );

				return OperateResult.CreateFailedResult<FanucSysInfo>( connect );
			}
			return read;
		}

		/// <inheritdoc cref="FanucSeries0i.ReadOperatorMessage"/>
		[RpcExtensionInfo(DataType = DataType.Class, DataDimension = DataDimension.One)]
		[HslMqttApi(Description = "读取机床的操作信息")]
		public OperateResult<FanucOperatorMessage[]> ReadOperatorMessage() => this.fanucSeries0I.ReadOperatorMessage();

		/// <inheritdoc cref="FanucSeries0i.ReadSpindleSpeedAndFeedRateAsync"/>
		[RpcExtensionInfo( DataType = DataType.Double )]
		[HslMqttApi( Description = "读取主轴转速" )]
		public async Task<OperateResult<double>> ReadSpindleSpeed( )
		{
			var read = await this.fanucSeries0I.ReadSpindleSpeedAndFeedRateAsync( );
			if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>( read );

			return OperateResult.CreateSuccessResult( read.Content1 );
		}

		/// <inheritdoc cref="FanucSeries0i.ReadSpindleSpeedAndFeedRateAsync"/>
		[HslMqttApi( Description = "读取进给倍率" )]
		[RpcExtensionInfo( DataType = DataType.Double )]
		public async Task<OperateResult<double>> ReadFeedRate( )
		{
			var read = await this.fanucSeries0I.ReadSpindleSpeedAndFeedRateAsync( );
			if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>( read );

			return OperateResult.CreateSuccessResult( read.Content2 );
		}

		/// <inheritdoc cref="FanucSeries0i.ReadSpindleSpeedAndFeedRateAsync"/>
		[HslMqttApi( Description = "读取进给倍率2，作为ReadFeedRate方法读取不到数据的时候的备选方案" )]
		[RpcExtensionInfo( DataType = DataType.Double )]
		public OperateResult<double> ReadFeedRate2( )
		{
			OperateResult<byte[]> read = this.fanucSeries0I.ReadPMCData( "G12", 1 );
			if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>( read );

			return OperateResult.CreateSuccessResult( 255d - read.Content[0] );
		}

		/// <inheritdoc cref="FanucSeries0i.ReadSystemProgramCurrent"/>
		[HslMqttApi( Description = "读取当前正在运行的程序名" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadSystemProgramCurrent( )
		{
			var read = await this.fanucSeries0I.ReadSystemProgramCurrentAsync( );
			if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>( read );

			return OperateResult.CreateSuccessResult( read.Content1 );
		}

		/// <inheritdoc cref="FanucSeries0i.ReadLanguage"/>
		[RpcExtensionInfo( DataType = DataType.UInt16 )]
		[HslMqttApi( Description = "读取机床的语言设定信息，此处举几个常用值 0: 英语 1: 日语 2: 德语 3: 法语 4: 中文繁体 6: 韩语 15: 中文简体 16: 俄语 17: 土耳其语" )]
		public async Task<OperateResult<ushort>> ReadLanguage( ) => await this.fanucSeries0I.ReadLanguageAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadFanucAxisLoad"/>
		[RpcExtensionInfo( DataType = DataType.Double, DataDimension = DataDimension.One )]
		[HslMqttApi( Description = "读取伺服负载\r\nRead servo load" )]
		public async Task<OperateResult<double[]>> ReadFanucAxisLoad( ) => await this.fanucSeries0I.ReadFanucAxisLoadAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadSysAllCoors"/>
		[RpcExtensionInfo( DataType = DataType.Class )]
		[HslMqttApi( Description = "读取机床的坐标信息, 包括机械坐标, 绝对坐标，相对坐标" )]
		public async Task<OperateResult<SysAllCoors>> ReadSysAllCoors( ) => await this.fanucSeries0I.ReadSysAllCoorsAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadSystemAlarm"/>
		[RpcExtensionInfo( DataType = DataType.Class, DataDimension = DataDimension.One )]
		[HslMqttApi( Description = "读取当前的报警信息列表\r\nRead alarm information" )]
		public async Task<OperateResult<SysAlarm[]>> ReadSystemAlarm( ) => await this.fanucSeries0I.ReadSystemAlarmAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadSystemMacroValue(int)"/>
		[RpcExtensionInfo( DataType = DataType.Double )]
		[HslMqttApi( Description = "读取宏变量信息\r\nRead macro variable, can be used to read tool number" )]
		public async Task<OperateResult<double>> ReadSystemMacroValue( int number ) => await this.fanucSeries0I.ReadSystemMacroValueAsync( number );

		/// <inheritdoc cref="FanucSeries0i.ReadSystemMacroValue(int, int)"/>
		[RpcExtensionInfo( DataType = DataType.Double, DataDimension = DataDimension.One )]
		[HslMqttApi( ApiTopic = "ReadSystemMacroValueArray", Description = "读取宏变量数组信息\r\nRead macro variable, can be used to read tool number" )]
		public async Task<OperateResult<double[]>> ReadSystemMacroValue( int number, int length ) => await this.fanucSeries0I.ReadSystemMacroValueAsync( number, length );

		/// <inheritdoc cref="FanucSeries0i.WriteSystemMacroValue(int, double[])"/>
		[RpcExtensionInfo( DataType = DataType.Method )]
		[HslMqttApi( Description = "写宏变量，需要指定地址及写入的数据" )]
		public async Task<OperateResult> WriteSystemMacroValue( int number, double[] values ) => await this.fanucSeries0I.WriteSystemMacroValueAsync( number, values );

		/// <inheritdoc cref="FanucSeries0i.ReadTimeData(int)"/>
		[RpcExtensionInfo( DataType = DataType.Int64 )]
		[HslMqttApi( Description = "读取fanuc机床的时间，0是开机时间，1是运行时间，2是切割时间，3是循环时间，4是空闲时间，返回秒为单位的信息" )]
		public async Task<OperateResult<long>> ReadTimeData( int timeType ) => await this.fanucSeries0I.ReadTimeDataAsync( timeType );

		/// <inheritdoc cref="FanucSeries0i.ReadAlarmStatus"/>
		[HslMqttApi( Description = "读取报警状态信息\r\nRead alarm status information" )]
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		public async Task<OperateResult<int>> ReadAlarmStatus( ) => await this.fanucSeries0I.ReadAlarmStatusAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadSysStatusInfo"/>
		[RpcExtensionInfo( DataType = DataType.Class )]
		[HslMqttApi( Description = "读取系统的基本信息状态，工作模式，运行状态，是否急停等等操作" )]
		public async Task<OperateResult<SysStatusInfo>> ReadSysStatusInfo( ) => await this.fanucSeries0I.ReadSysStatusInfoAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadProgramList"/>
		[RpcExtensionInfo( DataType = DataType.Int32, DataDimension = DataDimension.One )]
		[HslMqttApi( Description = "读取设备的程序列表\r\nRead the program list of the device" )]
		public async Task<OperateResult<int[]>> ReadProgramList( ) => await this.fanucSeries0I.ReadProgramListAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadCutterInfos(int)"/>
		[RpcExtensionInfo( DataType = DataType.Class, DataDimension = DataDimension.One )]
		[HslMqttApi( Description = "读取当前的刀具补偿信息\r\nRead current tool compensation information" )]
		public async Task<OperateResult<CutterInfo[]>> ReadCutterInfos( int cutterNumber = 24 ) => await this.fanucSeries0I.ReadCutterInfosAsync( cutterNumber );

		/// <inheritdoc cref="FanucSeries0i.ReadCutterNumber"/>
		[HslMqttApi( Description = "读取当前的正在使用的刀具号\r\nRead the tool number currently in use" )]
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		public async Task<OperateResult<int>> ReadCutterNumber( ) => await this.fanucSeries0I.ReadCutterNumberAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadPMCDataAsync(string, ushort)"/>
		[RpcExtensionInfo( DataType = DataType.Hex )]
		[HslMqttApi( Description = "读取PMC数据，需要传入起始地址和长度信息，地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5，返回byte[]数据信息" )]
		public async Task<OperateResult<byte[]>> ReadPMCData( string address, ushort length ) => await this.fanucSeries0I.ReadPMCDataAsync( address, length );

		/// <inheritdoc cref="FanucSeries0i.WritePMCData(string, byte[])"/>
		[RpcExtensionInfo( DataType = DataType.Method )]
		[HslMqttApi( Description = "写入PMC数据，需要传入起始地址和原始字节内润，地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5，返回是否写入成功" )]
		public async Task<OperateResult> WritePMCData( string address, byte[] value ) => await this.fanucSeries0I.WritePMCDataAsync( address, value );

		/// <inheritdoc cref="FanucSeries0i.ReadDeviceWorkPiecesSize"/>
		[RpcExtensionInfo( DataType = DataType.Double, DataDimension = DataDimension.One )]
		[HslMqttApi( Description = "读取工件尺寸\r\nRead workpiece size" )]
		public async Task<OperateResult<double[]>> ReadDeviceWorkPiecesSize( ) => await this.fanucSeries0I.ReadDeviceWorkPiecesSizeAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadCurrentProgram"/>
		[HslMqttApi( Description = "读取当前的程序内容，只能读取程序的片段，返回程序内容。" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadCurrentProgram( ) => await this.fanucSeries0I.ReadCurrentProgramAsync( );

		/// <inheritdoc cref="FanucSeries0i.SetCurrentProgram(ushort)"/>
		[HslMqttApi( Description = "设置指定的程序号为当前的主程序，如果程序号不存在，返回错误信息" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> SetCurrentProgram( ushort programNum ) => await this.fanucSeries0I.SetCurrentProgramAsync( programNum );

		/// <inheritdoc cref="FanucSeries0i.StartProcessingAsync"/>
		[HslMqttApi( Description = "启动加工程序\r\nStart the processing program" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> StartProcessing( ) => await this.fanucSeries0I.StartProcessingAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadCurrentForegroundDir"/>
		[HslMqttApi( Description = "读取当前程序的前台路径\r\nRead the foreground path of the current program" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadCurrentForegroundDir( ) => await this.fanucSeries0I.ReadCurrentForegroundDirAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadCurrentDateTime"/>
		[HslMqttApi( Description = "读取机床的当前时间信息\r\nRead the current time information of the machine tool" )]
		[RpcExtensionInfo( DataType = DataType.DateTime )]
		public async Task<OperateResult<DateTime>> ReadCurrentDateTime( ) => await this.fanucSeries0I.ReadCurrentDateTimeAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadCurrentProduceCountAsync"/>
		[HslMqttApi( Description = "读取当前的已加工的零件数量\r\nRead the current number of processed parts" )]
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		public async Task<OperateResult<int>> ReadCurrentProduceCount( ) => await this.fanucSeries0I.ReadCurrentProduceCountAsync( );

		/// <inheritdoc cref="FanucSeries0i.ReadExpectProduceCount"/>
		[HslMqttApi( Description = "读取期望的加工的零件数量\r\nRead the expected number of processed parts" )]
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		public async Task<OperateResult<int>> ReadExpectProduceCount( ) => await this.fanucSeries0I.ReadExpectProduceCountAsync( );

		/// <inheritdoc cref="FanucSeries0i.WriteProgramContent"/>
		[HslMqttApi( Description = "下载数控加工程序到数控机床，返回下载是否成功\r\nDownload the NC machining program to the CNC machine tool, and return whether the download is successful\r\n" +
			"程序路径信息，默认为空，就是 //CNC_MEM/USER/PATH1/ 如果需要指定PATH2，需要输入 //CNC_MEM/USER/PATH2/" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteProgramContent( string program, int everyWriteSize = 512, string path = "" ) => await this.fanucSeries0I.WriteProgramContentAsync( program, everyWriteSize, path );

		/// <inheritdoc cref="FanucSeries0i.ReadProgram"/>
		[HslMqttApi( Description = "读取指定程序号的程序内容，可以指定路径信息，路径默认为空就是主路径，//CNC_MEM/USER/PATH1/ ，也可以指定其他路\r\nRead the program content of the specified program number" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadProgram( int program, string path = "" ) => await this.fanucSeries0I.ReadProgramAsync( program, path );

		/// <inheritdoc cref="FanucSeries0i.ReadAllDirectoryAndFile(string)"/>
		[RpcExtensionInfo( DataType = DataType.Class, DataDimension = DataDimension.One )]
		[HslMqttApi( Description = "读取指定路径下的所有的子路径和文件的信息，路径信息，例如 \"//CNC_MEM/USER/\"" )]
		public async Task<OperateResult<FileDirInfo[]>> ReadAllDirectoryAndFile( string path ) => await this.fanucSeries0I.ReadAllDirectoryAndFileAsync( path );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Cnc FanucSeries0i] [{GetDeviceNameWithPath( )}] [{nodeCncFanucSerise.GetSocketInfo( )}]";

		#endregion

		private FanucSeries0i fanucSeries0I;                                // Fanuc机床的核心通信类
		private NodeCncFanucSerise nodeCncFanucSerise;                      // cnc设备的节点对象

	}
}
