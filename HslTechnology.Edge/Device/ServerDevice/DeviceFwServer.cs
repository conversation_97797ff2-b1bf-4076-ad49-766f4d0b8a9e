using HslCommunication.Profinet.Siemens;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Server;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.ServerDevice
{
	/// <summary>
	/// 西门子FW的虚拟plc
	/// </summary>
	public class DeviceFwServer : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceFwServer( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeServer    = new NodeSiemensFWServer( element );
			this.tcpServer     = new SiemensFetchWriteServer( );
			this.SetDeviceInfo( deviceResources, this.nodeServer, this.tcpServer );
		}

		#endregion

		#region IServerDevice

		/// <inheritdoc cref="IServerDevice.Start"/>
		protected override void BeforStart( )
		{
			try
			{
				this.tcpServer.ServerStart( this.nodeServer.Port );
				this.UpdateDeviceOnlineStatus( true );
			}
			catch (Exception ex)
			{
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "Start Server failed: " + ex.Message );
				SetDeviceForceMessgae( "Server", "Start Server failed: " + ex.Message );
			}
			base.BeforStart( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			this.tcpServer.ServerClose( );
			base.AfterClose( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[FwServer] [{GetDeviceNameWithPath( )}:{nodeServer.Port}]";

		#endregion

		#region Private Member

		private NodeSiemensFWServer nodeServer;             // 节点信息
		private SiemensFetchWriteServer tcpServer;          // Tcp的服务器

		#endregion
	}
}
