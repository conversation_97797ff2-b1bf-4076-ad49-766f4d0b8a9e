using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Server;
using HslCommunication.Profinet.Melsec;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.ServerDevice
{
	/// <summary>
	/// 三菱MC协议的虚拟服务器
	/// </summary>
	public class DeviceMcServer : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMcServer( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeServer = new NodeMelsecMCServer( element );
			if (this.nodeServer.Protocol == Node.ProtocolType.TCP)
			{
				this.tcpServer = new MelsecMcServer( this.nodeServer.IsBinary );
				this.SetDeviceInfo( deviceResources, this.nodeServer, this.tcpServer );
			}
			else
			{
				this.udpServer = new MelsecMcUdpServer( this.nodeServer.IsBinary );
				this.SetDeviceInfo( deviceResources, this.nodeServer, this.udpServer );
			}
		}

		#endregion

		#region IServerDevice

		/// <inheritdoc cref="IServerDevice.Start"/>
		protected override void BeforStart( )
		{
			try
			{
				if (this.nodeServer.Protocol == Node.ProtocolType.TCP)
					this.tcpServer.ServerStart( this.nodeServer.Port );
				else
					this.udpServer.ServerStart( this.nodeServer.Port );
				this.UpdateDeviceOnlineStatus( true );
			}
			catch (Exception ex) 
			{ 
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "Start Server failed: " + ex.Message );
				SetDeviceForceMessgae( "Server", "Start Server failed: " + ex.Message );
			}
			base.BeforStart( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			if (this.nodeServer.Protocol == Node.ProtocolType.TCP)
				this.tcpServer.ServerClose( );
			else
				this.udpServer.ServerClose( );
			base.AfterClose( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[McServer:{nodeServer.Protocol}] [{GetDeviceNameWithPath( )}:{nodeServer.Port}]";

		#endregion

		#region Private Member

		private NodeMelsecMCServer nodeServer;             // 节点信息
		private MelsecMcServer tcpServer;                  // Tcp的服务器
		private MelsecMcUdpServer udpServer;               // Udp的服务器

		#endregion
	}
}
