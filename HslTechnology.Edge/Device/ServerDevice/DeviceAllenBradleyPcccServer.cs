using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Server;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Profinet.AllenBradley;

namespace HslTechnology.Edge.Device.ServerDevice
{
	/// <summary>
	/// AB的PCCC协议的服务器
	/// </summary>
	public class DeviceAllenBradleyPcccServer : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceAllenBradleyPcccServer( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeServer   = new NodeAllenBradleyPcccServer( element );
			this.tcpServer    = new AllenBradleyPcccServer( );
			this.SetDeviceInfo( deviceResources, this.nodeServer, this.tcpServer );
		}

		#endregion

		#region IServerDevice

		/// <inheritdoc cref="IServerDevice.Start"/>
		protected override void BeforStart( )
		{
			try
			{
				this.tcpServer.ServerStart( this.nodeServer.Port );
				this.UpdateDeviceOnlineStatus( true );
			}
			catch (Exception ex)
			{
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "Start Server failed: " + ex.Message );
				SetDeviceForceMessgae( "Server", "Start Server failed: " + ex.Message );
			}
			base.BeforStart( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			this.tcpServer.ServerClose( );
			base.AfterClose( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AllenBradleyPcccServer] [{GetDeviceNameWithPath( )}:{nodeServer.Port}]";

		#endregion

		#region Private Member

		private NodeAllenBradleyPcccServer nodeServer;             // 节点信息
		private AllenBradleyPcccServer tcpServer;                  // Tcp的服务器

		#endregion
	}
}
