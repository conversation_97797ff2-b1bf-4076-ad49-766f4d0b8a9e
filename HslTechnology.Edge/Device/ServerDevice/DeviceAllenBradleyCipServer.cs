using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Server;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Profinet.AllenBradley;

namespace HslTechnology.Edge.Device.ServerDevice
{
	public class DeviceAllenBradleyCipServer : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceAllenBradleyCipServer( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeServer                   = new NodeAllenBradleyCipServer( element );
			this.tcpServer                    = new AllenBradleyServer( );
			this.tcpServer.CreateTagWithWrite = this.nodeServer.CreateTagWithWrite;

			this.SetDeviceInfo( deviceResources, this.nodeServer, this.tcpServer );
		}

		#endregion

		#region IServerDevice

		/// <inheritdoc cref="IServerDevice.Start"/>
		protected override void BeforStart( )
		{
			try
			{
				this.tcpServer.ServerStart( this.nodeServer.Port );
				this.UpdateDeviceOnlineStatus( true );
			}
			catch (Exception ex)
			{
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "Start Server failed: " + ex.Message );
				SetDeviceForceMessgae( "Server", "Start Server failed: " + ex.Message );
			}
			base.BeforStart( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			this.tcpServer.ServerClose( );
			base.AfterClose( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AllenBradleyCipServer] [{GetDeviceNameWithPath( )}:{nodeServer.Port}]";

		#endregion

		#region Private Member

		private NodeAllenBradleyCipServer nodeServer;             // 节点信息
		private AllenBradleyServer tcpServer;          // Tcp的服务器

		#endregion
	}
}
