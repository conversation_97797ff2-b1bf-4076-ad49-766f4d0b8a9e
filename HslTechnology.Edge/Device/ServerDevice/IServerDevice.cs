using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Device.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.ServerDevice
{
	/// <summary>
	/// 服务器相关的设备接口信息<br />
	/// Server-related device interface information
	/// </summary>
	public interface IServerDevice
	{
		/// <summary>
		/// 启动服务器的服务<br />
		/// Start the server
		/// </summary>
		void Start( );

		/// <summary>
		/// 关闭服务器的服务<br />
		/// Close the server
		/// </summary>
		void Close( );

		/// <inheritdoc cref="DeviceCoreBase.RegisterRpcService(MqttServer, HttpServer)"/>
		void RegisterRpcService( MqttServer mqttServer, HttpServer httpServer );

	}
}
