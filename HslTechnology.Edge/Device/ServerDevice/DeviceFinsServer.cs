using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Server;
using HslCommunication.Profinet.Omron;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.ServerDevice
{
	/// <summary>
	/// FinsTCP的虚拟服务器
	/// </summary>
	public class DeviceFinsServer : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceFinsServer( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeServer = new NodeOmronFinsServer( element );
			if (this.nodeServer.Protocol == Node.ProtocolType.TCP)
			{
				this.tcpServer = new OmronFinsServer( );
				this.SetDeviceInfo( deviceResources, this.nodeServer, this.tcpServer );
			}
			else
			{
				this.udpServer = new OmronFinsUdpServer( );
				this.SetDeviceInfo( deviceResources, this.nodeServer, this.udpServer );
			}
		}

		#endregion

		#region IServerDevice

		/// <inheritdoc cref="IServerDevice.Start"/>
		protected override void BeforStart( )
		{
			try
			{
				if (this.nodeServer.Protocol == Node.ProtocolType.TCP)
					this.tcpServer.ServerStart( this.nodeServer.Port );
				else
					this.udpServer.ServerStart( this.nodeServer.Port );
				this.UpdateDeviceOnlineStatus( true );
			}
			catch(Exception ex)
			{
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "Start Server failed: " + ex.Message );
				SetDeviceForceMessgae( "Server", "Start Server failed: " + ex.Message );
			}
			base.BeforStart( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			if (this.nodeServer.Protocol == Node.ProtocolType.TCP)
				this.tcpServer.ServerClose( );
			else
				this.udpServer.ServerClose( );
			base.AfterClose( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[FinsServer:{nodeServer.Protocol}] [{GetDeviceNameWithPath( )}:{nodeServer.Port}]";

		#endregion

		#region Private Member

		private NodeOmronFinsServer nodeServer;             // 节点信息
		private OmronFinsServer tcpServer;                  // Tcp的服务器
		private OmronFinsUdpServer udpServer;               // Udp的服务器

		#endregion
	}
}
