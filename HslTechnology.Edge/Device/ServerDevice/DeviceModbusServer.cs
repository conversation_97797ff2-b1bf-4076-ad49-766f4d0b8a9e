using HslTechnology.Edge.Node.Server;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.ModBus;
using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Net;
using System.IO.Ports;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Node.Regular;
using HslCommunication.Core.Address;
using HslCommunication;

namespace HslTechnology.Edge.Device.ServerDevice
{
	/// <summary>
	/// Modbus服务器的节点信息
	/// </summary>
	public class DeviceModbusServer : DeviceServerBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceModbusServer( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeServer = new NodeModbusServer( element );
			this.modbusServer = new ModbusTcpServer( );
			this.modbusServer.ActiveTimeSpan = TimeSpan.FromSeconds( this.nodeServer.ActiveTime );
			this.modbusServer.ByteTransform.DataFormat = this.nodeServer.DataFormat;
			this.modbusServer.Station = this.nodeServer.Station;
			this.modbusServer.StationDataIsolation = this.nodeServer.StationDataIsolation;
			this.SetDeviceInfo( deviceResources, this.nodeServer, this.modbusServer );
		}

		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			base.AnalysisByBussiness( business );
		}

		#endregion

		#region IServerDevice

		/// <inheritdoc cref="IServerDevice.Start"/>
		protected override void BeforStart( )
		{
			try
			{
				this.modbusServer.ServerStart( this.nodeServer.Port );
				if (!string.IsNullOrEmpty( this.nodeServer.PortName ))
					this.modbusServer.StartSerialSlave( sp =>
					{
						sp.PortName = this.nodeServer.PortName;
						sp.BaudRate = this.nodeServer.BaudRate;
						sp.DataBits = this.nodeServer.DataBits;
						sp.Parity   = this.nodeServer.Parity;
						sp.StopBits = this.nodeServer.StopBits;
					} );
				this.modbusServer.OnDataReceived += ModbusServer_OnDataReceived;
				this.UpdateDeviceOnlineStatus( true );
			}
			catch (Exception ex) 
			{
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "Start Server failed: " + ex.Message );
				SetDeviceForceMessgae( EdgeStringResource.StringGather_DeviceIni, "Start Server failed: " + ex.Message );
			}
			base.BeforStart( );
		}

		private void WriteDataToMapping( RegularAddressWriteMappingNode regular, int addressOffset, byte[] value )
		{
			if (regular.DeviceCore == null) {
				OperateResult<DeviceCore> read = this.edgeResources.EdgeServices.GetDeviceCore( regular.TargetDevice );
				if (!read.IsSuccess) return;

				regular.DeviceCore = read.Content;
			}

			if (regular.DeviceCore != null)
			{
				// 尝试正式写入的操作
				if (addressOffset == 0)
				{
					// OperateResult write = regular.DeviceCore.ReadWriteDevice?.Write( regular.TargetAddress, value ); 现在改为切换到设备的主线程执行写入操作
					OperateResult write = regular.DeviceCore.ExecuteThreadInsertMission( new Base.ThreadInsertMission( )
					{
						DeviceOpreate = new Func<Base.DeviceCoreBase, object>( ( device ) =>
						{
							if (!device.DeviceStatus) return new OperateResult( "当前的设备不在线，请等待设备在线的时候再写入数据！" );
							return ((DeviceCore)device).ReadWriteDevice?.Write( regular.TargetAddress, value );
						} )
					} ) as OperateResult;
					if (write != null && write.IsSuccess == false)
					{
						LogNet?.WriteError( GetDeviceNameWithPath( ), "WriteDataToMapping", $"Write address[{regular.TargetAddress}] failed: " + write.Message );
					}
				}
				else
				{
					ModbusAddress modbusAddress = new ModbusAddress( regular.TargetAddress );
					modbusAddress.Address = (ushort)(modbusAddress.Address + addressOffset);
					// OperateResult write = regular.DeviceCore.ReadWriteDevice?.Write( modbusAddress.ToString( ), value ); 现在改为切换到设备的主线程执行写入操作
					OperateResult write = regular.DeviceCore.ExecuteThreadInsertMission( new Base.ThreadInsertMission( )
					{
						DeviceOpreate = new Func<Base.DeviceCoreBase, object>( ( device ) =>
						{
							if (!device.DeviceStatus) return new OperateResult( "当前的设备不在线，请等待设备在线的时候再写入数据！" );
							return ((DeviceCore)device).ReadWriteDevice?.Write( modbusAddress.ToString( ), value ); ;
						} )
					} ) as OperateResult;
					if (write != null && write.IsSuccess == false)
					{
						LogNet?.WriteError( GetDeviceNameWithPath( ), "WriteDataToMapping", $"Write address[{modbusAddress}] failed: " + write.Message );
					}
				}
			}
		}

		private void WriteDataToMapping( RegularAddressWriteMappingNode regular, int addressOffset, short value )
		{
			if (regular.DeviceCore == null)
			{
				OperateResult<DeviceCore> read = this.edgeResources.EdgeServices.GetDeviceCore( regular.TargetDevice );
				if (!read.IsSuccess) return;

				regular.DeviceCore = read.Content;
			}

			if (regular.DeviceCore != null)
			{
				// 尝试正式写入的操作
				if (addressOffset == 0)
				{
					// OperateResult write = regular.DeviceCore.ReadWriteDevice?.Write( regular.TargetAddress, value );
					OperateResult write = regular.DeviceCore.ExecuteThreadInsertMission( new Base.ThreadInsertMission( )
					{
						DeviceOpreate = new Func<Base.DeviceCoreBase, object>( ( device ) =>
						{
							if (!device.DeviceStatus) return new OperateResult( "当前的设备不在线，请等待设备在线的时候再写入数据！" );
							return ((DeviceCore)device).ReadWriteDevice?.Write( regular.TargetAddress, value );
						} )
					} ) as OperateResult;
					if (write != null && write.IsSuccess == false)
					{
						LogNet?.WriteError( GetDeviceNameWithPath( ), "WriteDataToMapping", $"Write address[{regular.TargetAddress}] failed: " + write.Message );
					}
				}
				else
				{
					ModbusAddress modbusAddress = new ModbusAddress( regular.TargetAddress );
					modbusAddress.Address = (ushort)(modbusAddress.Address + addressOffset);
					// OperateResult write = regular.DeviceCore.ReadWriteDevice?.Write( modbusAddress.ToString( ), value );
					OperateResult write = regular.DeviceCore.ExecuteThreadInsertMission( new Base.ThreadInsertMission( )
					{
						DeviceOpreate = new Func<Base.DeviceCoreBase, object>( ( device ) =>
						{
							if (!device.DeviceStatus) return new OperateResult( "当前的设备不在线，请等待设备在线的时候再写入数据！" );
							return ((DeviceCore)device).ReadWriteDevice?.Write( modbusAddress.ToString( ), value ); ;
						} )
					} ) as OperateResult;
					if (write != null && write.IsSuccess == false)
					{
						LogNet?.WriteError( GetDeviceNameWithPath( ), "WriteDataToMapping", $"Write address[{modbusAddress}] failed: " + write.Message );
					}
				}
			}
		}

		private void WriteDataToMapping( RegularAddressWriteMappingNode regular, int addressOffset, bool[] value )
		{
			if (regular.DeviceCore == null)
			{
				OperateResult<DeviceCore> read = this.edgeResources.EdgeServices.GetDeviceCore( regular.TargetDevice );
				if (!read.IsSuccess) return;

				regular.DeviceCore = read.Content;
			}

			if (regular.DeviceCore != null)
			{
				// 尝试正式写入的操作
				if (addressOffset == 0)
				{
					OperateResult write = regular.DeviceCore.ReadWriteDevice?.Write( regular.TargetAddress, value );
					if (write != null && write.IsSuccess == false)
					{
						LogNet?.WriteError( GetDeviceNameWithPath( ), "WriteDataToMapping", $"Write address[{regular.TargetAddress}] failed: " + write.Message );
					}
				}
				else
				{
					ModbusAddress modbusAddress = new ModbusAddress( regular.TargetAddress );
					modbusAddress.Address = (ushort)(modbusAddress.Address + addressOffset);
					OperateResult write = regular.DeviceCore.ReadWriteDevice?.Write( modbusAddress.ToString( ), value );
					if (write != null && write.IsSuccess == false)
					{
						LogNet?.WriteError( GetDeviceNameWithPath( ), "WriteDataToMapping", $"Write address[{modbusAddress}] failed: " + write.Message );
					}
				}
			}
		}

		private void WriteModbusCoreCommand( byte[] modbusCore )
		{
			try
			{
				if (modbusCore[1] == ModbusInfo.WriteRegister)
				{
					if (modbusCore[6] != modbusCore.Length - 7) return;                                      // 长度不符合要求

					ushort address = modbusServer.ByteTransform.TransUInt16( modbusCore, 2 );
					for (int i = 0; i < this.writeMappingNodes.Count; i++)
					{
						RegularAddressWriteMappingNode regular = this.writeMappingNodes[i];
						ModbusAddress modbusAddress = new ModbusAddress( regular.StartAddress );

						if (modbusAddress.Function < 0 || modbusAddress.Function == ModbusInfo.ReadRegister)
						{
							if (address < modbusAddress.Address)
							{
								if (address + modbusCore[6] / 2 > modbusAddress.Address)
								{
									byte[] value = modbusCore.SelectMiddle( (modbusAddress.Address - address) * 2 + 7, Math.Min( regular.Length * 2, (address + modbusCore[6] / 2 - modbusAddress.Address) * 2 ) );
									//for (int j = 0; j < value.Length; j += 4)
									//{
									//	if (j + 3 < value.Length)
									//	{
									//		float tmp = ByteTransform.TransSingle( value, j );
									//		WriteDataToMapping( regular, j / 2, Convert.ToInt16( tmp ) );
									//	}
									//}
									WriteDataToMapping( regular, 0, value );
								}
							}
							else if (address < modbusAddress.Address + regular.Length)
							{
								byte[] value = modbusCore.SelectMiddle( 7, Math.Min( modbusCore[6], (regular.Length - (address - modbusAddress.Address)) * 2 ) );
								//for (int j = 0; j < value.Length; j += 4)
								//{
								//	if (j + 3 < value.Length)
								//	{
								//		float tmp = ByteTransform.TransSingle( value, j );
								//		WriteDataToMapping( regular, address - modbusAddress.Address + j / 2, Convert.ToInt16( tmp ) );
								//	}
								//}
								WriteDataToMapping( regular, address - modbusAddress.Address, value );
							}
						}
					}
				}
				else if (modbusCore[1] == ModbusInfo.WriteOneRegister)
				{
					if (modbusCore.Length != 6) return;      // 如果长度不符合要求
					ushort address = modbusServer.ByteTransform.TransUInt16( modbusCore, 2 );
					for (int i = 0; i < this.writeMappingNodes.Count; i++)
					{
						RegularAddressWriteMappingNode regular = this.writeMappingNodes[i];
						ModbusAddress modbusAddress = new ModbusAddress( regular.StartAddress );

						if (modbusAddress.Function < 0 || modbusAddress.Function == ModbusInfo.ReadRegister)
						{
							if (address >= modbusAddress.Address && address < modbusAddress.Address + regular.Length)
							{
								byte[] value = modbusCore.SelectLast( 2 );
								WriteDataToMapping( regular, address - modbusAddress.Address, value );
							}
						}
					}
				}
				else if (modbusCore[1] == ModbusInfo.WriteCoil)
				{
					// 01 0F 00 64 00 03 01 05
					if (modbusCore[6] != modbusCore.Length - 7) return;                                      // 长度不符合要求

					ushort address = modbusServer.ByteTransform.TransUInt16( modbusCore, 2 );
					ushort length  = modbusServer.ByteTransform.TransUInt16( modbusCore, 4 );
					for (int i = 0; i < this.writeMappingNodes.Count; i++)
					{
						RegularAddressWriteMappingNode regular = this.writeMappingNodes[i];
						ModbusAddress modbusAddress = new ModbusAddress( regular.StartAddress );

						if (modbusAddress.Function == ModbusInfo.ReadCoil)
						{
							if (address < modbusAddress.Address)
							{
								if (address + length > modbusAddress.Address)
								{
									bool[] value = modbusCore.SelectMiddle(7, modbusCore[6] ).ToBoolArray( ).SelectMiddle( 
										modbusAddress.Address - address, Math.Min( regular.Length, address + length - modbusAddress.Address ) );
									WriteDataToMapping( regular, 0, value );
								}
							}
							else if (address < modbusAddress.Address + regular.Length)
							{
								bool[] value = modbusCore.SelectMiddle( 7, modbusCore[6] ).ToBoolArray( ).SelectMiddle( 
									0, Math.Min( length, regular.Length - (address - modbusAddress.Address) ) );
								WriteDataToMapping( regular, address - modbusAddress.Address, value );
							}
						}
					}
				}
				else if (modbusCore[1] == ModbusInfo.WriteOneCoil)
				{
					// 01 05 00 64 FF 00
					if (modbusCore.Length != 6) return;      // 如果长度不符合要求
					ushort address = modbusServer.ByteTransform.TransUInt16( modbusCore, 2 );
					for (int i = 0; i < this.writeMappingNodes.Count; i++)
					{
						RegularAddressWriteMappingNode regular = this.writeMappingNodes[i];
						ModbusAddress modbusAddress = new ModbusAddress( regular.StartAddress );

						if (modbusAddress.Function == ModbusInfo.ReadCoil)
						{
							if (address >= modbusAddress.Address && address < modbusAddress.Address + regular.Length)
							{
								bool[] value = new bool[] { modbusCore[4] == 0xFF };
								WriteDataToMapping( regular, address - modbusAddress.Address, value );
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				LogNet?.WriteError( GetDeviceNameWithPath( ), "Write Mapping", ex.Message );
			}

		}

		private void ModbusServer_OnDataReceived( object sender, object source, byte[] data )
		{
			if (source is AppSession session)
			{
				#region 如果地址有映射到其他的设备，则支持反写过去

				// ModbusTcp报文
				// 收到客户端的写入操作，然后反写映射的真实设备部分内容太复杂，需要查询本地的request请求，以及关联的request请求及其设备对象，
				byte[] modbusCore = ModbusInfo.ExplodeTcpCommandToCore( data );
				if (this.writeMappingNodes == null) return;                             // 没有地址映射写入操作

				WriteModbusCoreCommand( modbusCore );
				#endregion
			}
			else if (source is SerialPort serialPort)
			{
				// ModbusRtu报文
				byte[] modbusCore = ModbusInfo.ExplodeRtuCommandToCore( data );
				if (this.writeMappingNodes == null) return;                             // 没有地址映射写入操作

				WriteModbusCoreCommand( modbusCore );
			}
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			if (!string.IsNullOrEmpty( this.nodeServer.PortName ))
				this.modbusServer.CloseSerialSlave( );
			this.modbusServer.ServerClose( );
			base.AfterClose( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusServer] [{GetDeviceNameWithPath( )}:{nodeServer.Port}]";

		#endregion

		#region Private Member

		private NodeModbusServer nodeServer;
		private ModbusTcpServer modbusServer;

		#endregion
	}
}
