using HslCommunication.ModBus;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.ServerDevice
{
	/// <summary>
	/// 所有服务器设备类的基类信息
	/// </summary>
	public class DeviceServerBase : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceServerBase( XElement element ) : base( element ) 
		{
			
		}

		#endregion

		#region Load Request

		/// <inheritdoc/>
		protected override void OtherNameXmlLoad( XElement other )
		{
			if (other.Name == nameof( NodeType.RegularWriteMapping ))
			{
				if (this.writeMappingNodes == null) this.writeMappingNodes = new List<RegularAddressWriteMappingNode>( );
				// 解析地址映射信息
				RegularAddressWriteMappingNode regular = new RegularAddressWriteMappingNode( other );
				this.writeMappingNodes.Add( regular );
			}
		}

		#endregion


		#region Private Member

		protected List<RegularAddressWriteMappingNode> writeMappingNodes;

		#endregion
	}
}
