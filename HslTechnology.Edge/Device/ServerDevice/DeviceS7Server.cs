using HslTechnology.Edge.Device.PLCDevice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Profinet.Siemens;
using HslTechnology.Edge.Node.Server;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.ServerDevice
{
	/// <summary>
	/// 虚拟的S7服务器，模拟的S7-1200的通信
	/// </summary>
	public class DeviceS7Server : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceS7Server( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeServer     = new NodeSiemensS7Server( element );
			this.tcpServer      = new SiemensS7Server( );
			this.SetDeviceInfo( deviceResources, this.nodeServer, this.tcpServer );
		}

		#endregion

		#region IServerDevice

		/// <inheritdoc cref="IServerDevice.Start"/>
		protected override void BeforStart( )
		{
			try
			{
				this.tcpServer.ServerStart( this.nodeServer.Port );
				this.UpdateDeviceOnlineStatus( true );
			}
			catch (Exception ex) 
			{
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "Start Server failed: " + ex.Message );
				SetDeviceForceMessgae( "Server", "Start Server failed: " + ex.Message );
			}
			base.BeforStart( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			this.tcpServer.ServerClose( );
			base.AfterClose( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[S7Server] [{GetDeviceNameWithPath( )}:{nodeServer.Port}]";

		#endregion

		#region Private Member

		private NodeSiemensS7Server nodeServer;             // 节点信息
		private SiemensS7Server tcpServer;                  // Tcp的服务器

		#endregion
	}
}
