using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device
{
	/// <summary>
	/// 网关的串口管道信息
	/// </summary>
	public class EdgePipeSerial : IEdgePipe
	{
		/// <summary>
		/// 实例化一个串口的管道对象信息
		/// </summary>
		/// <param name="node">串口的节点定义</param>
		/// <param name="pipeSerial">串口管道信息</param>
		public EdgePipeSerial( NodeSerialPipe node, PipeSerial pipeSerial )
		{
			Node       = node;
			PipeSerial = pipeSerial;
		}

		/// <summary>
		/// 串口的管道节点信息
		/// </summary>
		public NodeSerialPipe Node { get; set; }

		/// <summary>
		/// 真实的串口管道信息
		/// </summary>
		public PipeSerial PipeSerial { get; set; }

		/// <inheritdoc/>
		public void ClosePipe( )
		{
			PipeSerial?.Close( extraOnClose: null );
		}

		/// <inheritdoc/>
		public PipeBase GetPipe( ) => PipeSerial;
	}
}
