using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Profinet.Panasonic;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice
{
	public class DevicePanasonicHelper
	{
		public class DevicePanasonic
		{

			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 松下PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 松下PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持
				string address = sourceReadRequest.Address;

				// 如果不是松下的地址格式，就直接返回
				OperateResult<McAddressData> addressResult = McAddressData.ParsePanasonicFrom( address, (ushort)sourceReadRequest.GetLength( ), false );
				if (!addressResult.IsSuccess) return string.Empty;

				// 松下MC目前只支持D,LD, SD的批量解析写入分析操作
				if (addressResult.Content.McDataType == MelsecMcDataType.Panasonic_LD ||
					addressResult.Content.McDataType == MelsecMcDataType.Panasonic_DT ||
					addressResult.Content.McDataType == MelsecMcDataType.Panasonic_SD)
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					if (addressResult.Content.McDataType == MelsecMcDataType.Panasonic_LD)
					{
						return address.Substring( 0, 2 ) + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );
					}
					else if (addressResult.Content.McDataType == MelsecMcDataType.Panasonic_SD)
					{
						return "SD" + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );
					}
					else
					{
						return address.Substring( 0, 1 ) + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );
					}
				}
				else
				{
					return string.Empty;
				}
			}
		}


		public class PanasonicMewtocol
		{

			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 三菱PLC对于字地址的点位，无法进行写入操作
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 先提取站号信息
				string station = string.Empty;
				string address = sourceReadRequest.Address;
				OperateResult<int> analysis = HslHelper.ExtractParameter( ref address, "s" );
				if (analysis.IsSuccess) station = $"s={analysis.Content};";

				// 三菱PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是三菱的地址格式，就直接返回
				OperateResult<string, int> addressResult = PanasonicHelper.AnalysisAddress( address );
				if (!addressResult.IsSuccess) return string.Empty;

				// 三菱目前只支持D,R的批量解析写入分析操作
				if (addressResult.Content1 == "D" ||
					addressResult.Content1 == "LD" ||
					addressResult.Content1 == "F")
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					return station + addressResult.Content1 +  (addressResult.Content2 + (byteOffset + index) / 2).ToString( );
				}
				else
				{
					return string.Empty;
				}
			}
		}
	}
}
