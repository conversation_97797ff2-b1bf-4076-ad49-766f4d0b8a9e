using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Panasonic;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 松下PLC，使用Mewtocol协议访问的情况
	/// </summary>
	public class DevicePanasonicMewtocol : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个松下设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DevicePanasonicMewtocol( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                           = new NodePanasonicMewtocol( element );
			this.panasonic                      = new PanasonicMewtocol( );
			this.panasonic.Station              = node.Station;
			this.SetDeviceInfo( deviceResources, node, this.panasonic );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => panasonic?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => panasonic?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = panasonic.Open( );
			if (connect.IsSuccess) panasonic.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DevicePanasonicHelper.PanasonicMewtocol.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DevicePanasonicHelper.PanasonicMewtocol.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[PanasonicMewtocol] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private PanasonicMewtocol panasonic;               // 核心交互对象
		private NodePanasonicMewtocol node;                // 节点对象

		#endregion
	}
}
