using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslCommunication.Profinet.Panasonic;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 松下的设备对象
	/// </summary>
	public class DevicePanasonicMc3E : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个松下的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DevicePanasonicMc3E( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node                        = new NodePanasonicMc( element );
			this.panasonicMcNet              = new PanasonicMcNet( node.IpAddress, node.Port );
			this.panasonicMcNet.ConnectionId = node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.panasonicMcNet );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => panasonicMcNet?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => panasonicMcNet?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = panasonicMcNet.ConnectServer( );
			if (connect.IsSuccess) panasonicMcNet.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			panasonicMcNet?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DevicePanasonicHelper.DevicePanasonic.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DevicePanasonicHelper.DevicePanasonic.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[PanasonicMcNet] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private PanasonicMcNet panasonicMcNet;                      // 二进制格式的交互对象
		private NodePanasonicMc node;                               // 节点定义信息

		#endregion
	}
}
