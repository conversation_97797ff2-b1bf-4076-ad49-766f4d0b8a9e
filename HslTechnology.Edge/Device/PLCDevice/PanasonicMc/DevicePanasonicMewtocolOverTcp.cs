using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Panasonic;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 松下PLC设备
	/// </summary>
	public class DevicePanasonicMewtocolOverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个松下的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DevicePanasonicMewtocolOverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                   = new NodePanasonicMewtocolOverTcp( element );
			this.panasonic              = new PanasonicMewtocolOverTcp( node.IpAddress, node.Port );
			this.panasonic.Station      = node.Station;
			this.panasonic.ConnectionId = node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.panasonic );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => panasonic?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => panasonic?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = panasonic.ConnectServer( );
			if (connect.IsSuccess) panasonic.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			panasonic?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DevicePanasonicHelper.PanasonicMewtocol.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DevicePanasonicHelper.PanasonicMewtocol.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[PanasonicMewtocolOverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private PanasonicMewtocolOverTcp panasonic;                      // 二进制格式的交互对象
		private NodePanasonicMewtocolOverTcp node;                       // 节点信息

		#endregion
	}
}
