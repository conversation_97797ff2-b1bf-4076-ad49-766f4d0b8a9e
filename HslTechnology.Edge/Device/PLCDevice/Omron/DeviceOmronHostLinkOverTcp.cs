using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Omron;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 欧姆龙的HostLink的PLC，使用网口透传的机制来实现的
	/// </summary>
	public class DeviceOmronHostLinkOverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个欧姆龙的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceOmronHostLinkOverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                                        = new NodeOmronHostLinkOverTcp( element );
			this.omron                                       = new OmronHostLinkOverTcp( node.IpAddress, node.Port );
			this.omron.UnitNumber                            = node.UnitNumber;
			this.omron.DA2                                   = node.DA2;
			this.omron.SA2                                   = node.SA2;
			this.omron.ByteTransform.DataFormat              = node.DataFormat;
			this.omron.ConnectionId                          = node.DTU;
			this.omron.ByteTransform.IsStringReverseByteWord = node.IsStringReverse;
			this.SetDeviceInfo( deviceResources, node, this.omron );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			omron?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			omron?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = omron.ConnectServer( );
			if (connect.IsSuccess) omron.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			omron?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			return Omron.Helper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronHostLinkOverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private OmronHostLinkOverTcp omron;               // 核心交互对象
		private NodeOmronHostLinkOverTcp node;            // 节点信息

		#endregion
	}
}
