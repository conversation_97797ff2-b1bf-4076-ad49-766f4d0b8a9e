using HslCommunication;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Profinet.Omron;
using HslCommunication.Core.Net;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 欧姆龙的基于连接的CIP协议
	/// </summary>
	public class DeviceOmronConnectedCipNet : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Omron PLC的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceOmronConnectedCipNet( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node   = new NodeOmronConnectedCipNet( element );
			this.omron  = new OmronConnectedCipNet( node.IpAddress, node.Port );
			this.omron.ConnectionId = node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.omron );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			if (scalarRequest.Length < 0)
			{
				if (scalarRequest.StringLength == 0 || scalarRequest.StringLength == 1)
				{
					OperateResult<string> read = UseAsyncReadWrite( ) ? await omron.ReadStringAsync( scalarRequest.Address ) : omron.ReadString( scalarRequest.Address );
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
				}
				else
					return await base.ReadActualStringAsync( scalarRequest );
			}
			else
			{
				return new OperateResult( "OmronConnectedCipNet 不支持字符串数组的标量请求！" );
			}
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			omron?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			omron?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			OperateResult connect = omron.ConnectServer( );
			if (connect.IsSuccess) omron.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			omron?.ConnectServer( alienSession );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronConnectedCIP] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private OmronConnectedCipNet omron;                // 核心交互对象
		private NodeOmronConnectedCipNet node;             // 节点对象信息

		#endregion
	}
}
