using HslCommunication;
using HslCommunication.Core.Address;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice.Omron
{
	/// <summary>
	/// 辅助类对象
	/// </summary>
	public class Helper
	{
		private static string GetAddressFromBitIndex( int addressStart )
		{
			if (addressStart % 16 == 0) return (addressStart / 16).ToString( );
			return (addressStart / 16).ToString( ) + "." + (addressStart % 16).ToString( );
		}

		private static string TranslateAddress( string address, OmronFinsAddress finsAddress )
		{
			switch (address[0])
			{
				case 'D':
				case 'd':
					{
						return "D" + GetAddressFromBitIndex( finsAddress.AddressStart );
					}
				case 'C':
				case 'c':
					{
						return "C" + GetAddressFromBitIndex( finsAddress.AddressStart );
					}
				case 'W':
				case 'w':
					{
						return "W" + GetAddressFromBitIndex( finsAddress.AddressStart );
					}
				case 'H':
				case 'h':
					{
						return "H" + GetAddressFromBitIndex( finsAddress.AddressStart );
					}
				case 'A':
				case 'a':
					{
						return "A" + GetAddressFromBitIndex( finsAddress.AddressStart );
					}
				case 'E':
				case 'e':
					{
						// E区，比较复杂，需要专门的计算
						string add = address.Substring( 0, address.IndexOf( '.' ) );
						return add + "." + GetAddressFromBitIndex( finsAddress.AddressStart );
					}
				default: return string.Empty;
			}
		}

		/// <inheritdoc cref="Base.DeviceCoreBase.CalculatePhysicalAddressFromSourceReadRequest"/>
		public static string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			// 欧姆龙PLC原始请求的解析对byte变量无效
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text) return string.Empty;
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text) return string.Empty;
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text) return string.Empty;

			var analysis = OmronFinsAddress.ParseFrom( sourceReadRequest.Address, 0 );
			if (!analysis.IsSuccess) return string.Empty;

			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text)
			{
				// bool的情况，按照字进行颠倒
				analysis.Content.AddressStart += byteOffset * 8 + index;
				if (analysis.Content.AddressStart % 16 < 8)
					analysis.Content.AddressStart += 8;
				else
					analysis.Content.AddressStart -= 8;

				return TranslateAddress( sourceReadRequest.Address, analysis.Content );
			}
			else
			{
				// 奇数的索引不支持写入操作
				if (index % 2 == 1) return string.Empty;
				if (byteOffset % 2 == 1) return string.Empty;

				analysis.Content.AddressStart += (byteOffset + index) * 8;
				return TranslateAddress( sourceReadRequest.Address, analysis.Content );
			}
		}

	}
}
