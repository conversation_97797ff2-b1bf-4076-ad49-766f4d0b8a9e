using HslCommunication;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Omron;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 一个欧姆龙PLC的设备对象，使用HostLink的CMode模式连接的
	/// </summary>
	public class DeviceOmronHostLinkCMode: DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个欧姆龙的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceOmronHostLinkCMode( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                                        = new NodeOmronHostLinkCMode( element );
			this.omron                                       = new OmronHostLinkCMode( );
			this.omron.UnitNumber                            = node.UnitNumber;
			this.omron.ByteTransform.DataFormat              = node.DataFormat;
			this.omron.ByteTransform.IsStringReverseByteWord = node.IsStringReverse;
			this.SetDeviceInfo( deviceResources, node, this.omron );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => omron?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => omron?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = omron.Open( );
			if (connect.IsSuccess) omron.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			return Omron.Helper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronHostLinkCMode] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private OmronHostLinkCMode omron;               // 核心交互对象
		private NodeOmronHostLinkCMode node;            // 节点信息

		#endregion
	}
}
