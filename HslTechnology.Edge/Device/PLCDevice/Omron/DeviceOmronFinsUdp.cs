using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Omron;
using HslCommunication.Reflection;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 欧姆龙的设备UDP对象
	/// </summary>
	public class DeviceOmronFinsUdp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个欧姆龙的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceOmronFinsUdp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeOmron                                          = new NodeOmronFinsUdp( element );
			this.omronFinsNet                                       = new OmronFinsUdp( nodeOmron.IpAddress, nodeOmron.Port );
			this.omronFinsNet.DA2                                   = nodeOmron.DA2;
			this.omronFinsNet.SA1                                   = nodeOmron.SA1;
			this.omronFinsNet.GCT                                   = nodeOmron.GCT;
			this.omronFinsNet.SID                                   = nodeOmron.SID;
			this.omronFinsNet.ByteTransform.DataFormat              = nodeOmron.DataFormat;
			this.omronFinsNet.ByteTransform.IsStringReverseByteWord = nodeOmron.IsStringReverse;
			this.SetDeviceInfo( deviceResources, nodeOmron, this.omronFinsNet );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{

		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{

		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			System.Net.NetworkInformation.IPStatus status = omronFinsNet.IpAddressPing( );

			return status == System.Net.NetworkInformation.IPStatus.Success ?
				OperateResult.CreateSuccessResult( ToString( ) ) :
				new OperateResult<string>( "Ping Wrong:" + status.ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			return Omron.Helper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="OmronFinsNet.Run"/>
		[HslMqttApi( Description = "将CPU单元的操作模式更改为RUN，从而使PLC能够执行其程序。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult Run( )
		{
			return this.omronFinsNet.Run( );
		}

		/// <inheritdoc cref="OmronFinsNet.Stop"/>
		[HslMqttApi( Description = "将CPU单元的操作模式更改为PROGRAM，停止程序执行。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult Stop( )
		{
			return this.omronFinsNet.Stop( );
		}

		/// <inheritdoc cref="OmronFinsNet.ReadCpuUnitData"/>
		[HslMqttApi( Description = "读取CPU的一些数据信息，主要包含型号，版本，一些数据块的大小。" )]
		[RpcExtensionInfo( DataType = DataType.Class )]
		public OperateResult<OmronCpuUnitData> ReadCpuUnitData( )
		{
			return this.omronFinsNet.ReadCpuUnitData( );
		}

		/// <inheritdoc cref="OmronFinsNet.ReadCpuUnitStatus( )"/>
		[HslMqttApi( Description = "读取CPU单元的一些操作状态数据，主要包含运行状态，工作模式，错误信息等。" )]
		[RpcExtensionInfo( DataType = DataType.Class )]
		public OperateResult<OmronCpuUnitStatus> ReadCpuUnitStatus( )
		{
			return this.omronFinsNet.ReadCpuUnitStatus( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronFinsUdp] [{GetDeviceNameWithPath( )}] [{nodeOmron.GetSocketInfo( )}]";

		#endregion

		#region Private

		private OmronFinsUdp omronFinsNet;               // 核心交互对象
		private NodeOmronFinsUdp nodeOmron;              // 节点信息

		#endregion

	}
}
