using HslCommunication;
using HslCommunication.Core.Address;
using HslCommunication.Core.Net;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Omron;
using HslCommunication.Reflection;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 这是一个基于fins协议的欧姆龙的设备对象
	/// </summary>
	public class DeviceOmronFins : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个欧姆龙的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceOmronFins( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeOmron                                          = new NodeOmronFinsTcp( element );
			this.omronFinsNet                                       = new OmronFinsNet( nodeOmron.IpAddress, nodeOmron.Port );
			this.omronFinsNet.DA2                                   = nodeOmron.DA2;
			this.omronFinsNet.ByteTransform.DataFormat              = nodeOmron.DataFormat;
			this.omronFinsNet.ConnectionId                          = nodeOmron.DTU;
			this.omronFinsNet.ByteTransform.IsStringReverseByteWord = nodeOmron.IsStringReverse;
			this.SetDeviceInfo( deviceResources, nodeOmron, this.omronFinsNet );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			omronFinsNet?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			omronFinsNet?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = omronFinsNet.ConnectServer( );
			if (connect.IsSuccess) omronFinsNet.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			omronFinsNet?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest( 
			SourceReadRequest sourceReadRequest, 
			int byteOffset, 
			int index, 
			IScalarTransform scalarTransform )
		{
			return Omron.Helper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="OmronFinsNet.Run"/>
		[HslMqttApi( Description = "将CPU单元的操作模式更改为RUN，从而使PLC能够执行其程序。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Run( )
		{
			return await this.omronFinsNet.RunAsync( );
		}

		/// <inheritdoc cref="OmronFinsNet.Stop"/>
		[HslMqttApi( Description = "将CPU单元的操作模式更改为PROGRAM，停止程序执行。" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> Stop( )
		{
			return await this.omronFinsNet.StopAsync( );
		}

		/// <inheritdoc cref="OmronFinsNet.ReadCpuUnitData"/>
		[HslMqttApi( Description = "读取CPU的一些数据信息，主要包含型号，版本，一些数据块的大小。" )]
		[RpcExtensionInfo( DataType = DataType.Class )]
		public async Task<OperateResult<OmronCpuUnitData>> ReadCpuUnitData( )
		{
			return await this.omronFinsNet.ReadCpuUnitDataAsync( );
		}

		/// <inheritdoc cref="OmronFinsNet.ReadCpuUnitStatus( )"/>
		[HslMqttApi( Description = "读取CPU单元的一些操作状态数据，主要包含运行状态，工作模式，错误信息等。" )]
		[RpcExtensionInfo( DataType = DataType.Class )]
		public async Task<OperateResult<OmronCpuUnitStatus>> ReadCpuUnitStatus( )
		{
			return await this.omronFinsNet.ReadCpuUnitStatusAsync( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronFinsTcp] [{GetDeviceNameWithPath( )}] [{nodeOmron.GetSocketInfo( )}]";

		#endregion

		#region Private

		private OmronFinsNet omronFinsNet;               // 核心交互对象
		private NodeOmronFinsTcp nodeOmron;              // 节点信息

		#endregion
	}
}
