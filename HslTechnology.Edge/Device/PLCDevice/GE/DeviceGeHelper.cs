using HslCommunication;
using HslCommunication.Core.Address;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice.GE
{
	internal class DeviceGeHelper
	{
		public class SRTP
		{
			private static string GetAddress( byte dataCode )
			{
				switch (dataCode)
				{
					case 0x0A: return "AI";
					case 0x0C: return "AQ";
					case 0x08: return "R";
					case 0x18: return "SA";
					case 0x1A: return "SB";
					case 0x1C: return "SC";
					default: return null;
				}
			}

			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}


			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				string address = sourceReadRequest.Address;

				// PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是正确的地址格式，就直接返回
				OperateResult<GeSRTPAddress> addressResult = GeSRTPAddress.ParseFrom( address, (ushort)sourceReadRequest.GetLength( ), false );
				if (!addressResult.IsSuccess) return string.Empty;

				// 目前只支持AI,AQ,R,SA,SB,SC的批量解析写入分析操作
				string add = GetAddress( addressResult.Content.DataCode );
				if (string.IsNullOrEmpty( add )) return string.Empty;

				return add + (addressResult.Content.AddressStart + byteOffset + index).ToString( );
			}
		}
	}
}
