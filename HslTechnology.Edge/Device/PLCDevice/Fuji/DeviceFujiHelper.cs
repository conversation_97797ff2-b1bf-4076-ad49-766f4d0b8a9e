using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice
{
	internal class DeviceFujiHelper
	{
		public class SPB
		{

			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 富士SPB的PLC对于字地址的点位，无法进行写入操作
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 先提取站号信息
				string station = string.Empty;
				string address = sourceReadRequest.Address;
				OperateResult<int> analysis = HslHelper.ExtractParameter( ref address, "s" );
				if (analysis.IsSuccess) station = $"s={analysis.Content};";

				// PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是富士的地址格式，就直接返回
				OperateResult<FujiSPBAddress> addressResult = FujiSPBAddress.ParseFrom( address, (ushort)sourceReadRequest.GetLength( ) );
				if (!addressResult.IsSuccess) return string.Empty;

				// 富士目前只支持D,R,W的批量解析写入分析操作
				if (addressResult.Content.TypeCode == "0C" ||
					addressResult.Content.TypeCode == "0D" ||
					addressResult.Content.TypeCode == "0E")
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					return station + address.Substring( 0, 1 ) + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );
				}
				else
				{
					return string.Empty;
				}
			}
		}

		public class SPH
		{
			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 富士SPB的PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 先提取站号信息
				string station = string.Empty;
				string address = sourceReadRequest.Address;
				OperateResult<int> analysis = HslHelper.ExtractParameter( ref address, "s" );
				if (analysis.IsSuccess) station = $"s={analysis.Content};";

				// PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是富士的地址格式，就直接返回
				OperateResult<FujiSPHAddress> addressResult = FujiSPHAddress.ParseFrom( address );
				if (!addressResult.IsSuccess) return string.Empty;

				// 富士目前只支持D,R,W的批量解析写入分析操作
				if (addressResult.Content.TypeCode == 0x02 ||
					addressResult.Content.TypeCode == 0x04 ||
					addressResult.Content.TypeCode == 0x08)
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					string add = addressResult.Content.TypeCode == 0x02 ? "M1." : addressResult.Content.TypeCode == 0x04 ? "M3." : "M10.";
					return station + add + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );
				}
				else
				{
					return string.Empty;
				}
			}
		}
	}
}
