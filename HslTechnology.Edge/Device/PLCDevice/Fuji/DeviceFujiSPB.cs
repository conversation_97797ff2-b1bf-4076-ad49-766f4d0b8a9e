using HslCommunication;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Profinet.Fuji;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 富士PLC的设备
	/// </summary>
	public class DeviceFujiSPB : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个富士编程口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceFujiSPB( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node             = new NodeFujiSPB( element );
			this.fuji             = new FujiSPB( );
			this.fuji.Station     = node.Station;
			this.SetDeviceInfo( deviceResources, node, this.fuji );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => fuji?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => fuji?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = fuji.Open( );
			if (connect.IsSuccess) fuji.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceFujiHelper.SPB.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceFujiHelper.SPB.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[FujiSPB] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private FujiSPB fuji;               // 核心交互对象
		private NodeFujiSPB node;           // 节点信息

		#endregion
	}
}
