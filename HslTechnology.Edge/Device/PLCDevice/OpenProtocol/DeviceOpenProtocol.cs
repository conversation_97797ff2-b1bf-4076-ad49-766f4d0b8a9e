using HslCommunication.CNC.Fanuc;
using HslCommunication.Core.Address;
using HslCommunication.Reflection;
using HslCommunication;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device.OpenProtocol;
using HslCommunication.Profinet.OpenProtocol;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Resources;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Node;

namespace HslTechnology.Edge.Device.PLCDevice.OpenProtocol
{
	public class DeviceOpenProtocol : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceOpenProtocol( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeOpenProtocol( element );
			this.openProtocol = new OpenProtocolNet( node.IpAddress, node.Port );
			this.openProtocol.ConnectTimeOut = node.ConnectTimeOut;
			this.openProtocol.ReceiveTimeOut = node.ReceiveTimeOut;
			this.UniqueId = this.openProtocol.ConnectionId;
			this.RegisterRpcService( deviceResources.MqttServer, deviceResources.HttpServer, node, openProtocol );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => this.openProtocol?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => this.openProtocol?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.openProtocol == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = openProtocol.ConnectServer( );
			if (connect.IsSuccess) openProtocol.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="OpenProtocolNet.ReadCustomer(int, int, int, int, List{string})"/>
		[RpcExtensionInfo( DataType = DataType.String )]
		[HslMqttApi( Description = "使用自定义的命令读取数据，需要指定每个参数信息，然后返回字符串数据内容，根据实际的功能码，解析出实际的数据信息" )]
		public async Task<OperateResult<string>> ReadCustomerAsync( int mid, int revison, int stationId, int spindleId, string[] parameters ) => 
			await this.openProtocol.ReadCustomerAsync( mid, revison, stationId, spindleId, parameters.ToList( ) );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[OpenProtocolNet] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		private OpenProtocolNet openProtocol;               // OpenProtocol核心通信对象
		private NodeOpenProtocol node;                      // 设备的节点对象
	}
}
