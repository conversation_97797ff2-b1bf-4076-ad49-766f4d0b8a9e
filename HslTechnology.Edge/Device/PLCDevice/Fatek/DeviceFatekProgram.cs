using HslCommunication;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Profinet.FATEK;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 永宏设备的编程口协议
	/// </summary>
	public class DeviceFatekProgram : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个永宏编程口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceFatekProgram( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node           = new NodeFatekProgram( element );
			this.fatek          = new FatekProgram( );
			this.fatek.Station  = node.Station;
			this.SetDeviceInfo( deviceResources, node, this.fatek );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => fatek?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => fatek?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = fatek.Open( );
			if (connect.IsSuccess) fatek.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceFatekHelper.Program.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceFatekHelper.Program.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[FatekProgram] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private FatekProgram fatek;               // 核心交互对象
		private NodeFatekProgram node;            // 节点信息

		#endregion
	}
}
