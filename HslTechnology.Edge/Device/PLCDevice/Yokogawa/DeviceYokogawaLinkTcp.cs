using HslCommunication;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Profinet.Yokogawa;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Resources;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 横河PLC的设备信息
	/// </summary>
	public class DeviceYokogawaLinkTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个通用电气的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceYokogawaLinkTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                  = new NodeYokogawaLinkTcp( element );
			this.yokogawa              = new YokogawaLinkTcp( node.IpAddress, node.Port );
			this.yokogawa.CpuNumber    = node.CpuNumber;
			this.yokogawa.ConnectionId = node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.yokogawa );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			yokogawa?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			yokogawa?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = yokogawa.ConnectServer( );
			if (connect.IsSuccess) yokogawa.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			yokogawa?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => Yokogawa.DeviceYokogawaHelper.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Yokogawa.DeviceYokogawaHelper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[GeSRTPNet] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private YokogawaLinkTcp yokogawa;               // 核心交互对象
		private NodeYokogawaLinkTcp node;               // 节点信息

		#endregion
	}
}
