using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice.Yokogawa
{
	internal class DeviceYokogawaHelper
	{

		public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			// 信捷的PLC对于字地址的点位，无法进行写入操作
			if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
				if (scalarReadRequest.Address.Contains( "." ))
					return AccessLevel.Read;
			return AccessLevel.ReadWrite;
		}

		public static string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			// 先提取站号信息
			string station = string.Empty;
			string address = sourceReadRequest.Address;
			if (address.StartsWith( "Special:" ) || address.StartsWith( "special:" )) return string.Empty;

			OperateResult<int> analysis = HslHelper.ExtractParameter( ref address, "cpu" );
			if (analysis.IsSuccess) station = $"scpu={analysis.Content};";

			// PLC原始请求的解析对bool变量，byte变量无效
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
				return string.Empty;

			// 对于特殊的地址暂时不支持，如果不是富士的地址格式，就直接返回
			OperateResult<YokogawaLinkAddress> addressResult = YokogawaLinkAddress.ParseFrom( address, (ushort)sourceReadRequest.GetLength( ) );
			if (!addressResult.IsSuccess) return string.Empty;

			// 奇数的索引不支持写入操作
			if (index % 2 == 1) return string.Empty;
			if (byteOffset % 2 == 1) return string.Empty;

			return station + System.Text.RegularExpressions.Regex.Match(address, "[^0-9]+").Value + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );
		}
	}
}
