using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.AllenBradley;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// AB-PLC的设备，采用了 MicroCIP协议实现的
	/// </summary>
	public class DeviceAllenBradleyMicroCip : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个AB PLC的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceAllenBradleyMicroCip( XElement element ) : base( element ) { }

		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeAllenBradley             = new NodeAllenBradleyMicroCip( element );
			this.allenBradleyNet              = new AllenBradleyMicroCip( nodeAllenBradley.IpAddress, nodeAllenBradley.Port );
			this.allenBradleyNet.ConnectionId = nodeAllenBradley.DTU;
			this.allenBradleyNet.Slot         = nodeAllenBradley.Slot;
			this.SetDeviceInfo( deviceResources, nodeAllenBradley, this.allenBradleyNet );                     // 赋值连接对象，以及网络参数信息
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				if (scalarRequest.StringLength == 0 || scalarRequest.StringLength == 1)
				{
					OperateResult<string> read = UseAsyncReadWrite( ) ? await allenBradleyNet.ReadStringAsync( scalarRequest.Address ) : allenBradleyNet.ReadString( scalarRequest.Address );
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
				}
				else
					return await base.ReadActualStringAsync( scalarRequest );
			}
			else
			{
				return new OperateResult( "AllenBradleyMicroCip 不支持字符串数组的标量请求！" );
			}
		}

		/// <inheritdoc/>
		protected override OperateResult WriteValueByScalarRequest( IReadWriteNet readWriteDevice, string address, IScalarTransform transform, string value )
		{
			if (transform.DataTypeCode == RegularNodeTypeItem.String.Text)
			{
				var readType = allenBradleyNet.ReadTag( address, 1 );
				if (!readType.IsSuccess) return readType;

				if (readType.Content1 == 0xDA)
				{
					byte[] buffer = string.IsNullOrEmpty( value ) ? new byte[0] : Encoding.UTF8.GetBytes( value );
					return allenBradleyNet.WriteTag( address, readType.Content1, HslCommunication.BasicFramework.SoftBasic.SpliceArray( new byte[] { (byte)buffer.Length }, buffer ) );
				}
			}
			return base.WriteValueByScalarRequest( readWriteDevice, address, transform, value );
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			allenBradleyNet?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			allenBradleyNet?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = allenBradleyNet.ConnectServer( );
			if (connect.IsSuccess) allenBradleyNet.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			allenBradleyNet?.ConnectServer( alienSession );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AllenBradleyMicroCip] [{GetDeviceNameWithPath()}] [{nodeAllenBradley.GetSocketInfo( )}]";

		#endregion

		#region Private

		private AllenBradleyMicroCip allenBradleyNet;               // 核心交互对象
		private NodeAllenBradleyMicroCip nodeAllenBradley;          // plc的节点对象

		#endregion
	}
}
