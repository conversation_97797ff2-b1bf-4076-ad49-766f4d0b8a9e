using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.AllenBradley;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// AB-plc的PCCC协议实现的数据读写操作
	/// </summary>
	public class DeviceAllenBradleyPCCC : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个AB PLC的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceAllenBradleyPCCC( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeAllenBradley               = new NodeAllenBradleyPCCC( element );
			this.allenBradleyNet                = new AllenBradleyPcccNet( nodeAllenBradley.IpAddress, nodeAllenBradley.Port );
			this.allenBradleyNet.ConnectionId   = nodeAllenBradley.DTU;
			this.SetDeviceInfo( deviceResources, nodeAllenBradley, this.allenBradleyNet );
		}

		#endregion

		#region ReadString

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			if (scalarRequest.Length < 0)
			{
				if (!string.IsNullOrEmpty( scalarRequest.Address ) && scalarRequest.Address.StartsWith( "ST" ))
				{
					OperateResult<string> read = UseAsyncReadWrite( ) ?
						await allenBradleyNet.ReadStringAsync( scalarRequest.Address, (ushort)scalarRequest.StringLength, RegularScalarNode.GetEncoding( scalarRequest.StringEncoding ) ) :
						allenBradleyNet.ReadString( scalarRequest.Address, (ushort)scalarRequest.StringLength, RegularScalarNode.GetEncoding( scalarRequest.StringEncoding ) );
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
				}
				else
					return await base.ReadActualStringAsync( scalarRequest );
			}
			else
			{
				return new OperateResult( "AllenBradleyPCCC 不支持字符串数组的标量请求！" );
			}
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			allenBradleyNet?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			allenBradleyNet?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = allenBradleyNet.ConnectServer( );
			if (connect.IsSuccess) allenBradleyNet.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			allenBradleyNet?.ConnectServer( alienSession );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AllenBradleyPCCC] [{GetDeviceNameWithPath( )}] [{nodeAllenBradley.GetSocketInfo( )}]";

		#endregion

		#region Private

		private AllenBradleyPcccNet allenBradleyNet;               // 核心交互对象
		private NodeAllenBradleyPCCC nodeAllenBradley;             // 节点对象信息

		#endregion
	}
}
