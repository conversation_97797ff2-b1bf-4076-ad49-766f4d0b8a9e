using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.AllenBradley;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 使用SLC协议来通信的AB-PLC
	/// </summary>
	public class DeviceAllenBradleySLC : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个AB PLC的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceAllenBradleySLC( XElement element ) : base( element ) { }


		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeAllenBradley                      = new NodeAllenBradleySLC( element );
			this.allenBradleyNet                       = new AllenBradleySLCNet( nodeAllenBradley.IpAddress, nodeAllenBradley.Port );
			this.allenBradleyNet.ConnectionId          = nodeAllenBradley.DTU;
			this.SetDeviceInfo( deviceResources, nodeAllenBradley, this.allenBradleyNet );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			allenBradleyNet?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			allenBradleyNet?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = allenBradleyNet.ConnectServer( );
			if (connect.IsSuccess) allenBradleyNet.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			allenBradleyNet?.ConnectServer( alienSession );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AllenBradleySLC] [{GetDeviceNameWithPath( )}] [{nodeAllenBradley.GetSocketInfo( )}]";

		#endregion

		#region Private

		private AllenBradleySLCNet allenBradleyNet;               // 核心交互对象
		private NodeAllenBradleySLC nodeAllenBradley;             // 节点信息

		#endregion
	}
}
