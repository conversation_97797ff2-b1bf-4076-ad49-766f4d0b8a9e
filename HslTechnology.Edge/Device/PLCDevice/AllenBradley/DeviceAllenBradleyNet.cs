using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslCommunication.Profinet.AllenBradley;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication.Core.Net;
using System.Threading.Tasks;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Resources;
using HslCommunication.Profinet.Siemens;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 一个基于AB PLC专用协议的ab plc设备
	/// </summary>
	public class DeviceAllenBradleyNet : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个AB PLC的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceAllenBradleyNet( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeAllenBradley                      = new NodeAllenBradleyCIP( element );
			this.allenBradleyNet                       = new AllenBradleyNet( nodeAllenBradley.IpAddress, nodeAllenBradley.Port );
			this.allenBradleyNet.Slot                  = nodeAllenBradley.Slot;
			this.allenBradleyNet.ConnectionId          = nodeAllenBradley.DTU;
			if (!string.IsNullOrEmpty( this.nodeAllenBradley.MessageRouter ))
				this.allenBradleyNet.MessageRouter = new MessageRouter( this.nodeAllenBradley.MessageRouter );
			this.SetDeviceInfo( deviceResources, nodeAllenBradley,      this.allenBradleyNet );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected async override Task<OperateResult<byte[]>> ReadBatch( string[] address, ushort[] length )
		{
			return await allenBradleyNet.ReadAsync( address, length );
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			if (scalarRequest.Length < 0)
			{
				if (scalarRequest.StringLength == 0 || scalarRequest.StringLength == 1)
				{
					OperateResult<string> read = UseAsyncReadWrite( ) ? await allenBradleyNet.ReadStringAsync( scalarRequest.Address ) : allenBradleyNet.ReadString( scalarRequest.Address );
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
				}
				else
					return await base.ReadActualStringAsync( scalarRequest );
			}
			else
			{

				return new OperateResult( "AllenBradleyCIP 不支持字符串数组的标量请求！" );
			}
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			allenBradleyNet?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			allenBradleyNet?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			OperateResult connect = allenBradleyNet.ConnectServer( );
			if (connect.IsSuccess) allenBradleyNet.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			allenBradleyNet?.ConnectServer( alienSession );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AllenBradleyCIP] [{GetDeviceNameWithPath( )}] [{nodeAllenBradley.GetSocketInfo( )}]";

		#endregion

		#region Private

		private AllenBradleyNet allenBradleyNet;               // 核心交互对象
		private NodeAllenBradleyCIP nodeAllenBradley;          // 节点对象信息

		#endregion
	}
}
