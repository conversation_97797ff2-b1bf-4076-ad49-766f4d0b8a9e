using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslCommunication.Profinet.AllenBradley;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication.Core.Net;
using System.Threading.Tasks;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 一个基于AB PLC专用协议的ab plc设备
	/// </summary>
	public class DeviceAllenBradleyConnectedCIP : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个AB PLC的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceAllenBradleyConnectedCIP( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node                      = new NodeAllenBradleyConnectedCIP( element );
			this.allenBradleyNet                       = new AllenBradleyConnectedCipNet( node.IpAddress, node.Port );
			this.allenBradleyNet.ConnectionId          = node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.allenBradleyNet );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			if (scalarRequest.Length < 0)
			{
				if (scalarRequest.StringLength == 0 || scalarRequest.StringLength == 1)
				{
					OperateResult<string> read = UseAsyncReadWrite( ) ? await allenBradleyNet.ReadStringAsync( scalarRequest.Address ) : allenBradleyNet.ReadString( scalarRequest.Address );

					// 赋值缓冲区
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
				}
				else
					return await base.ReadActualStringAsync( scalarRequest );
			}
			else
			{
				return new OperateResult( "AllenBradleyConnectedCIP 不支持字符串数组的标量请求！" );
			}
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			allenBradleyNet?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			allenBradleyNet?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			OperateResult connect = allenBradleyNet.ConnectServer( );
			if (connect.IsSuccess) allenBradleyNet.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			allenBradleyNet?.ConnectServer( alienSession );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AllenBradleyConnectedCIP] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private AllenBradleyConnectedCipNet allenBradleyNet;               // 核心交互对象
		private NodeAllenBradleyConnectedCIP node;             // 节点对象信息

		#endregion
	}
}
