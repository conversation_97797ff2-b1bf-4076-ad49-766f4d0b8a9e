using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Profinet.Inovance;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice.Inovance
{
	internal class DeviceInovanceHelper
	{
		public class Modbus
		{
			private static OperateResult<string, int> AnalysisAddress( InovanceSeries series, string address )
			{
				try
				{
					if (series == InovanceSeries.AM)
					{
						if (address.StartsWith( "MW" ) || address.StartsWith( "mw" ))        return OperateResult.CreateSuccessResult( "MW",  Convert.ToInt32( address.Substring( 2 ) ) );
						else if (address.StartsWith( "SDW" ) || address.StartsWith( "sdw" )) return OperateResult.CreateSuccessResult( "SDW", Convert.ToInt32( address.Substring( 3 ) ) );
						else if (address.StartsWith( "SD" ) || address.StartsWith( "sd" ))   return OperateResult.CreateSuccessResult( "SD",  Convert.ToInt32( address.Substring( 2 ) ) );
						else return new OperateResult<string, int>( HslCommunication.StringResources.Language.NotSupportedDataType );
					}
					else if (series == InovanceSeries.H3U)
					{
						if (address.StartsWith( "D" ) || address.StartsWith( "d" ))        return OperateResult.CreateSuccessResult( "D",  Convert.ToInt32( address.Substring( 1 ) ) );
						else if (address.StartsWith( "SD" ) || address.StartsWith( "sd" )) return OperateResult.CreateSuccessResult( "SD", Convert.ToInt32( address.Substring( 2 ) ) );
						else if (address.StartsWith( "R" ) || address.StartsWith( "r" ))   return OperateResult.CreateSuccessResult( "R",  Convert.ToInt32( address.Substring( 1 ) ) );
						else return new OperateResult<string, int>( HslCommunication.StringResources.Language.NotSupportedDataType );
					}
					else if (series == InovanceSeries.H5U)
					{
						if (address.StartsWith( "D" ) || address.StartsWith( "d" ))        return OperateResult.CreateSuccessResult( "D",  Convert.ToInt32( address.Substring( 1 ) ) );
						else if (address.StartsWith( "R" ) || address.StartsWith( "r" ))   return OperateResult.CreateSuccessResult( "R",  Convert.ToInt32( address.Substring( 1 ) ) );
						else return new OperateResult<string, int>( HslCommunication.StringResources.Language.NotSupportedDataType );
					}
					else
						return new OperateResult<string, int>( HslCommunication.StringResources.Language.NotSupportedDataType );
				}
				catch(Exception ex)
				{
					return new OperateResult<string, int>( ex.Message );
				}
			}

			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				InovanceSeries series,
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 先提取站号信息
				string station = string.Empty;
				string address = sourceReadRequest.Address;
				OperateResult<int> analysis = HslHelper.ExtractParameter( ref address, "s" );
				if (analysis.IsSuccess) station = $"s={analysis.Content};";

				// PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是正确的地址格式，就直接返回
				OperateResult<string, int> addressResult = AnalysisAddress( series, address );
				if (!addressResult.IsSuccess) return string.Empty;

				// 奇数的索引不支持写入操作
				if (index % 2 == 1) return string.Empty;
				if (byteOffset % 2 == 1) return string.Empty;

				return station + addressResult.Content1 + (addressResult.Content2 + (byteOffset + index) / 2).ToString( );
			}
		}
	}
}
