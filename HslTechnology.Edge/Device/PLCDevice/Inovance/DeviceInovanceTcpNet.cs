using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Inovance;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 汇川ModbusTcp的类型的通信类
	/// </summary>
	public class DeviceInovanceTcpNet : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个台达设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceInovanceTcpNet( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node                          = new NodeInovanceTcpNet( element );
			this.inovance                      = new InovanceTcpNet( node.IpAddress, node.Port, node.Station );
			this.inovance.ConnectionId         = this.node.DTU;
			this.inovance.Series               = this.node.Series;
			this.inovance.DataFormat           = this.node.DataFormat;
			this.inovance.AddressStartWithZero = this.node.IsAddressStartWithZero;
			this.inovance.IsStringReverse      = this.node.IsStringReverse;
			this.SetDeviceInfo( deviceResources, node, this.inovance );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => inovance?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => inovance?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = inovance.ConnectServer( );
			if (connect.IsSuccess) inovance.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			inovance.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => Inovance.DeviceInovanceHelper.Modbus.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Inovance.DeviceInovanceHelper.Modbus.CalculatePhysicalAddressFromSourceReadRequest( node.Series, sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[InovanceTcpNet<{node.Series}>] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private InovanceTcpNet inovance;                  // 核心交互对象
		private NodeInovanceTcpNet node;                  // 节点信息

		#endregion
	}
}
