using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslCommunication.Core.Address;
using HslCommunication.ModBus;
using HslTechnology.Edge.Device.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication.Core;
using HslCommunication;
using System.Net;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// Modbus相关的通用的辅助方法
	/// </summary>
	public class DeviceModbusHelper
	{
		/// <inheritdoc cref="DeviceCoreBase.GetAccessLevelFromRequest(ScalarReadRequest)"/>
		public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			if (scalarReadRequest.IsBoolRegular( ))
			{
				// 如果读取了寄存器的位，则只能是只读的
				if (scalarReadRequest.Address.Contains( "." )) return AccessLevel.Read;
				// 读了输入线圈的也只能是只读的
				try
				{
					// 如果地址里有 format=ABCD;这种属性暂时不考虑
					ModbusAddress modbusAddress = new ModbusAddress( scalarReadRequest.Address, ModbusInfo.ReadCoil );
					if (modbusAddress.Function == ModbusInfo.ReadDiscrete) return AccessLevel.Read;
					return AccessLevel.ReadWrite;
				}
				catch
				{
					return AccessLevel.Read;
				}
			}
			else
			{
				try
				{
					ModbusAddress modbusAddress = new ModbusAddress( scalarReadRequest.Address, ModbusInfo.ReadRegister );
					if (modbusAddress.Function == ModbusInfo.ReadInputRegister) return AccessLevel.Read;
					return AccessLevel.ReadWrite;
				}
				catch
				{
					return AccessLevel.Read;
				}
			}
		}

		/// <inheritdoc cref="DeviceCoreBase.CalculatePhysicalAddressFromSourceReadRequest(SourceReadRequest, int, int, IScalarTransform)"/>
		public static string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			if (System.Text.RegularExpressions.Regex.IsMatch( sourceReadRequest.Address, "x=1;", System.Text.RegularExpressions.RegexOptions.IgnoreCase ))
			{
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text) 
				{
					ModbusAddress modbusAddress = new ModbusAddress( sourceReadRequest.Address, ModbusInfo.ReadCoil );
					return (modbusAddress.Address + byteOffset * 8 + index).ToString( );
				}
				else
				{
					return string.Empty;
				}
			}
			// Modbus的字地址的bool，byte，都没法单独写入
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text) return string.Empty;
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text) return string.Empty;
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text) return string.Empty;
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text) return string.Empty;

			try
			{
				string address = sourceReadRequest.Address;

				ModbusAddress modbusAddress = new ModbusAddress( address, ModbusInfo.ReadRegister );
				// 如果是读取的输入寄存器的也没办法写入
				if (modbusAddress.Function == ModbusInfo.ReadInputRegister) return string.Empty;

				// 地址偏移量为奇数的也不能写入
				if (byteOffset % 2 == 1) return string.Empty;
				if (index % 2 == 1) return string.Empty;
				if (modbusAddress.Function != ModbusInfo.ReadRegister) return string.Empty;
				if (modbusAddress.Station < 0) return (modbusAddress.Address + (byteOffset + index) / 2).ToString( );
				else return $"s={modbusAddress.Station};" + (modbusAddress.Address + (byteOffset + index) / 2).ToString( );
			}
			catch
			{
				return string.Empty;
			}
		}

		/// <summary>
		/// 修改当前Modbus的字节变换顺序，参数可以是 ABCD, BADC, CDAB, DCBA, 如果为空，则表示恢复默认
		/// </summary>
		/// <param name="modbus">Modbus的通信对象</param>
		/// <param name="dataFormat">参数信息</param>
		/// <param name="defaultFormat">默认的格式</param>
		/// <returns>是否修改成功</returns>
		public static OperateResult SetDataFormat( IModbus modbus, DataFormat defaultFormat, string dataFormat = "CDAB" )
		{
			if (string.IsNullOrEmpty( dataFormat )) modbus.DataFormat = defaultFormat;
			else
			{
				dataFormat = dataFormat.ToUpper( );
				switch (dataFormat)
				{
					case "ABCD": modbus.DataFormat = DataFormat.ABCD; break;
					case "BADC": modbus.DataFormat = DataFormat.BADC; break;
					case "CDAB": modbus.DataFormat = DataFormat.CDAB; break;
					case "DCBA": modbus.DataFormat = DataFormat.DCBA; break;
					default: return new OperateResult( $"参数dataFormat[{dataFormat}]输入错误，参数可以是 ABCD, BADC, CDAB, DCBA, 如果为空，则表示恢复默认" );
				}
			}
			return OperateResult.CreateSuccessResult( );
		}

		/// <summary>
		/// 修改当前的Modbus的字符串是否发生顺序的交换
		/// </summary>
		/// <param name="modbus">Modbus通信对象</param>
		/// <param name="isStringReverse">字符串是否发生两两顺序交换</param>
		/// <returns>是否修改成功</returns>
		public static OperateResult SetIsStringReverse( IModbus modbus, bool isStringReverse ) 
		{
			modbus.IsStringReverse = isStringReverse;
			return OperateResult.CreateSuccessResult( );
		}

		/// <summary>
		/// 修改当前的modbus对象起始地址是否从0开始
		/// </summary>
		/// <param name="modbus">Modbus通信对象</param>
		/// <param name="addressStartWithZero">地址是否从0开始</param>
		/// <returns>是否修改成功</returns>
		public static OperateResult SetAddressStartWithZero( IModbus modbus, bool addressStartWithZero )
		{
			modbus.AddressStartWithZero = addressStartWithZero;
			return OperateResult.CreateSuccessResult( );
		}

	}
}
