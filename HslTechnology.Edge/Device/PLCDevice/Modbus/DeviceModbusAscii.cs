using System;
using System.IO.Ports;
using System.Xml.Linq;
using HslCommunication.ModBus;
using HslTechnology.Edge.Node.Device;
using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using HslTechnology.Edge.Reflection;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// Modbus Ascii协议的设备对象
	/// </summary>
	public class DeviceModbusAscii : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Modbus-Ascii的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceModbusAscii( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                             = new NodeModbusAscii( element );
			this.modbus                      = new ModbusAscii( node.Station );
			this.modbus.AddressStartWithZero = node.IsAddressStartWithZero;
			this.modbus.DataFormat           = node.DataFormat;
			this.modbus.IsStringReverse      = node.IsStringReverse;
			this.SetDeviceInfo( deviceResources, node, this.modbus );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => modbus?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => modbus?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = modbus.Open( );
			if (connect.IsSuccess) modbus.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			return DeviceModbusHelper.GetAccessLevelFromRequest( scalarReadRequest );
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			return DeviceModbusHelper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="DeviceModbusHelper.SetDataFormat(IModbus, DataFormat, string)"/>
		[HslMqttApi( Description = "修改当前Modbus的字节变换顺序，参数可以是 ABCD, BADC, CDAB, DCBA, 如果为空，则表示恢复默认" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult ChangeDataFormat( string dataFormat = "CDAB" ) => DeviceModbusHelper.SetDataFormat( this.modbus, this.node.DataFormat, dataFormat );

		/// <inheritdoc cref="DeviceModbusHelper.SetIsStringReverse(IModbus, bool)"/>
		[HslMqttApi( Description = "修改当前的Modbus的字符串是否发生顺序的交换" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult ChangeIsStringReverse( bool isStringReverse ) => DeviceModbusHelper.SetIsStringReverse( this.modbus, isStringReverse );

		/// <inheritdoc cref="DeviceModbusHelper.SetAddressStartWithZero(IModbus, bool)(IModbus, bool)"/>
		[HslMqttApi( Description = "修改当前的modbus对象起始地址是否从0开始" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult ChangeAddressStartWithZero( bool addressStartWithZero ) => DeviceModbusHelper.SetAddressStartWithZero( this.modbus, addressStartWithZero );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusAscii] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private ModbusAscii modbus;               // 核心交互对象
		private NodeModbusAscii node;                  // 节点信息

		#endregion
	}
}
