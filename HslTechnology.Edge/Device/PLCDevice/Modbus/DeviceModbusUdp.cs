using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.ModBus;
using HslCommunication.MQTT;
using HslCommunication.Reflection;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// Modbus TCP协议实现的通信对象，但是走的网络确实UDP的网络
	/// </summary>
	public class DeviceModbusUdp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Modbus-Tcp的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceModbusUdp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeModbus                     = new NodeModbusUdp( element );
			this.modbus                         = new ModbusUdpNet( nodeModbus.IpAddress, nodeModbus.Port, nodeModbus.Station );
			this.modbus.AddressStartWithZero    = nodeModbus.IsAddressStartWithZero;
			this.modbus.DataFormat              = nodeModbus.DataFormat;
			this.modbus.IsStringReverse         = nodeModbus.IsStringReverse;
			this.SetDeviceInfo( deviceResources, nodeModbus, this.modbus );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{

		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{

		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			System.Net.NetworkInformation.IPStatus status = modbus.IpAddressPing( );

			return status == System.Net.NetworkInformation.IPStatus.Success ?
				OperateResult.CreateSuccessResult( ToString( ) ) :
				new OperateResult<string>( "Ping Wrong:" + status.ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			return DeviceModbusHelper.GetAccessLevelFromRequest( scalarReadRequest );
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			return DeviceModbusHelper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="DeviceModbusHelper.SetDataFormat(IModbus, HslCommunication.Core.DataFormat, string)"/>
		[HslMqttApi( Description = "修改当前Modbus的字节变换顺序，参数可以是 ABCD, BADC, CDAB, DCBA, 如果为空，则表示恢复默认" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult ChangeDataFormat( string dataFormat = "CDAB" ) => DeviceModbusHelper.SetDataFormat( this.modbus, this.nodeModbus.DataFormat, dataFormat );

		/// <inheritdoc cref="DeviceModbusHelper.SetIsStringReverse(IModbus, bool)"/>
		[HslMqttApi( Description = "修改当前的Modbus的字符串是否发生顺序的交换" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult ChangeIsStringReverse( bool isStringReverse ) => DeviceModbusHelper.SetIsStringReverse( this.modbus, isStringReverse );

		/// <inheritdoc cref="DeviceModbusHelper.SetAddressStartWithZero(IModbus, bool)(IModbus, bool)"/>
		[HslMqttApi( Description = "修改当前的modbus对象起始地址是否从0开始" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public OperateResult ChangeAddressStartWithZero( bool addressStartWithZero ) => DeviceModbusHelper.SetAddressStartWithZero( this.modbus, addressStartWithZero );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusUdp] [{GetDeviceNameWithPath( )}] [{nodeModbus.GetSocketInfo( )}]";

		#endregion
		
		#region Private

		private ModbusUdpNet modbus;               // 核心交互对象
		private NodeModbusUdp nodeModbus;          // 节点信息

		#endregion
	}
}
