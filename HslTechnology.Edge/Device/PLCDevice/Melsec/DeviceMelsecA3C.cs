using HslCommunication;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication.Profinet.Melsec;
using HslTechnology.Edge.Node.Device;
using System.Xml.Linq;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 三菱PLC设备，使用A3C协议实现的访问
	/// </summary>
	public class DeviceMelsecA3C : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Melsec的A3C协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecA3C( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node      = new NodeMelsecA3C( element );
			this.melsec          = new MelsecA3CNet( );
			this.melsec.Station  = node.Station;
			this.melsec.SumCheck = node.SumCheck;
			this.melsec.Format   = node.Format;
			this.SetDeviceInfo( deviceResources, node, this.melsec );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => melsec?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => melsec?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = melsec.Open( );
			if (connect.IsSuccess) melsec.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceMelsecHelper.Mc3C.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceMelsecHelper.Mc3C.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecA3C] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private MelsecA3CNet melsec;                // 核心交互对象
		private NodeMelsecA3C node;           // 节点信息

		#endregion
	}
}
