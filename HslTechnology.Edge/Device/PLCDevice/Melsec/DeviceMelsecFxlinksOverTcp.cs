using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 三菱PLC设备，使用计算机链接协议实现，采用网口透传访问。
	/// </summary>
	public class DeviceMelsecFxlinksOverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Melsec的Fxlinks协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecFxlinksOverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node             = new NodeMelsecFxlinksOverTcp( element );
			this.melsec                 = new MelsecFxLinksOverTcp( );
			this.melsec.Station         = node.Station;
			this.melsec.SumCheck        = node.SumCheck;
			this.melsec.WaittingTime    = node.WaittingTime;
			this.melsec.ConnectionId    = node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.melsec );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => melsec?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => melsec?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = melsec.ConnectServer( );
			if (connect.IsSuccess) melsec.ConnectClose( );

			return connect.Convert( ToString( ) ); ;
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			melsec?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceMelsecHelper.FxLinks.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceMelsecHelper.FxLinks.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecFxLinksOverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private MelsecFxLinksOverTcp melsec;               // 核心交互对象
		private NodeMelsecFxlinksOverTcp node;             // 节点对象

		#endregion
	}
}
