using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Profinet.Melsec.Helper;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 三菱的PLC设备，所使用的协议为Qna兼容1E帧协议
	/// </summary>
	public class DeviceMelsecMc1E : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个三菱的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecMc1E( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node = new NodeMelsec1E( element );
			if (node.IsBinary)
			{
				this.melsecMcNet                    = new MelsecA1ENet( node.IpAddress, node.Port );
				this.melsecMcNet.PLCNumber          = node.PLCNumber;
				this.melsecMcNet.ConnectionId       = node.DTU;
				SetDeviceInfo( deviceResources, node, this.melsecMcNet );
			}
			else
			{
				this.melsecMcAsciiNet                    = new MelsecA1EAsciiNet( node.IpAddress, node.Port );
				this.melsecMcAsciiNet.PLCNumber          = node.PLCNumber;
				this.melsecMcAsciiNet.ConnectionId       = node.DTU;
				SetDeviceInfo( deviceResources, node, this.melsecMcAsciiNet );
			}
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			if (node.IsBinary)
				melsecMcNet?.SetPersistentConnection( );
			else
				melsecMcAsciiNet?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			if (node.IsBinary)
				melsecMcNet?.ConnectClose( );
			else
				melsecMcAsciiNet?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			if (node.IsBinary)
			{
				OperateResult connect = melsecMcNet.ConnectServer( );
				if (connect.IsSuccess) melsecMcNet.ConnectClose( );

				return connect.Convert( ToString( ) );
			}
			else
			{
				OperateResult connect = melsecMcAsciiNet.ConnectServer( );
				if (connect.IsSuccess) melsecMcAsciiNet.ConnectClose( );

				return connect.Convert( ToString( ) ); ;
			}
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			if (node.IsBinary)
				melsecMcNet?.ConnectServer( alienSession );
			else
				melsecMcAsciiNet?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			// 三菱PLC对于字地址的点位，无法进行写入操作
			if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
				if (scalarReadRequest.Address.Contains( "." ))
					return AccessLevel.Read;
			return AccessLevel.ReadWrite;
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			// 三菱PLC原始请求的解析对byte变量无效
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
				return string.Empty;

			// 对于特殊的地址暂时不支持，如果不是三菱的地址格式，就直接返回
			string address = sourceReadRequest.Address;
			OperateResult<MelsecA1EDataType, int> analysis = HslCommunication.Profinet.Melsec.MelsecHelper.McA1EAnalysisAddress( address );
			if (!analysis.IsSuccess) return string.Empty;

			// 三菱目前只支持D,W,R的写入分析操作
			if (address.StartsWith( "D", StringComparison.OrdinalIgnoreCase ) ||
				address.StartsWith( "W", StringComparison.OrdinalIgnoreCase ) ||
				address.StartsWith( "R", StringComparison.OrdinalIgnoreCase ))
			{

				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ) return string.Empty;

				// 奇数的索引不支持写入操作
				if (index % 2 == 1) return string.Empty;
				if (byteOffset % 2 == 1) return string.Empty;

				return address.Substring( 0, 1 ) + (address.StartsWith( "W" ) ?
					(analysis.Content2 + (byteOffset + index) / 2).ToString( "X" ) :
					(analysis.Content2 + (byteOffset + index) / 2).ToString( ));
			}
			else if (
				address.StartsWith( "M", StringComparison.OrdinalIgnoreCase ) ||
				address.StartsWith( "S", StringComparison.OrdinalIgnoreCase ) ||
				address.StartsWith( "F", StringComparison.OrdinalIgnoreCase ) ||
				address.StartsWith( "B", StringComparison.OrdinalIgnoreCase ))
			{
				// 此处支持如果配置了按照字单位读取M区，且配置了位及其他类型读取的情况
				int bitIndex = 0;
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text)
				{
					// 如果配置了针对字寄存器的bool解析位，那么就提供bool索引信息
					bitIndex = byteOffset * 8 + index;
				}
				else
				{
					bitIndex = byteOffset * 8 + index * 8;
				}

				if (address.StartsWith( "B", StringComparison.OrdinalIgnoreCase ))
					return address.Substring( 0, 1 ) + (Convert.ToInt32( address.Substring( 1 ), 16 ) + bitIndex).ToString( "X" );
				else
					return address.Substring( 0, 1 ) + (Convert.ToInt32( address.Substring( 1 ) ) + bitIndex).ToString( );
			}
			else
			{
				return string.Empty;
			}
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( )
		{
			if (node.IsBinary)
				return $"[MelsecMc1E] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
			else
				return $"[MelsecMc1E-ASCII] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
		}

		#endregion

		#region Private

		private MelsecA1ENet melsecMcNet;                      // 二进制格式的交互对象
		private MelsecA1EAsciiNet melsecMcAsciiNet;            // ASCII格式的交互对象
		private NodeMelsec1E node;                       // 当前的节点参数对象

		#endregion
	}
}
