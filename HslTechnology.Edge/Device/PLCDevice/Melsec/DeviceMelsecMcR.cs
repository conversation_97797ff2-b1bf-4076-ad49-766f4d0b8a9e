using HslCommunication;
using HslCommunication.Core.Address;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Profinet.Melsec.Helper;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 三菱MC协议R系列的PLC的实体类设备
	/// </summary>
	public class DeviceMelsecMcR : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个三菱的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecMcR( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                     = new NodeMelsecMcR( element );
			this.melsecMcNet              = new MelsecMcRNet( node.IpAddress, node.Port );
			this.melsecMcNet.ConnectionId = this.node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.melsecMcNet );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			melsecMcNet?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			melsecMcNet?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = melsecMcNet.ConnectServer( );
			if (connect.IsSuccess) melsecMcNet.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			melsecMcNet?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceMelsecHelper.Mc3E.GetAccessLevelFromRequest( scalarReadRequest, true );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceMelsecHelper.Mc3E.CalculatePhysicalAddressFromSourceReadRequest( melsecMcNet, sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( )
		{
			return $"[MelsecMcR] [{GetDeviceNameWithPath( )}] [{melsecMcNet.GetPipeSocket( )}]";
		}

		#endregion

		#region Private

		private MelsecMcRNet melsecMcNet;                      // 二进制格式的交互对象
		private NodeMelsecMcR node;                            // 当前的节点信息

		#endregion
	}
}
