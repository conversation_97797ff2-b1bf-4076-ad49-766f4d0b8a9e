using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Profinet.Melsec.Helper;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice
{
	internal class DeviceMelsecHelper
	{
		public class Mc3E
		{
			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest, bool enableWriteBitToWord )
			{
				// 三菱PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text && enableWriteBitToWord == false)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				IReadWriteMc mc,
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 三菱PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持
				string address = sourceReadRequest.Address;
				if (address.StartsWith( "s=" ) || address.StartsWith( "S=" ) || Regex.IsMatch( address, "ext=[0-9]+;", RegexOptions.IgnoreCase ) ||
					Regex.IsMatch( address, "mem=", RegexOptions.IgnoreCase ) || Regex.IsMatch( address, "module=[0-9]+;", RegexOptions.IgnoreCase ))
					return string.Empty;


				// 如果不是三菱的地址格式，就直接返回
				OperateResult<McAddressData> addressResult = mc.McAnalysisAddress( address, (ushort)sourceReadRequest.GetLength( ), false );
				if (!addressResult.IsSuccess) return string.Empty;

				// 三菱目前只支持D,W,R,ZE的批量解析写入分析操作
				if (addressResult.Content.McDataType == MelsecMcDataType.D ||
					addressResult.Content.McDataType == MelsecMcDataType.W ||
					addressResult.Content.McDataType == MelsecMcDataType.R ||
					addressResult.Content.McDataType == MelsecMcDataType.ZR)
				{
					if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text && mc.EnableWriteBitToWordRegister == false) return string.Empty;
					if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text && mc.EnableWriteBitToWordRegister)
					{
						// 如果配置了针对字寄存器的bool解析位，那么就提供bool索引信息
						int bitIndex = index % 16;
						index = index / 16;
						if( byteOffset % 2 == 1 ) return string.Empty;

						if (address.StartsWith( "ZR" )) return address.Substring( 0, 2 ) + (addressResult.Content.AddressStart + (byteOffset / 2 + index)).ToString( "X" ) + $".{bitIndex}";

						return address.Substring( 0, 1 ) + (addressResult.Content.McDataType == MelsecMcDataType.W ?
							(addressResult.Content.AddressStart + (byteOffset / 2 + index)).ToString( "X" ) :
							(addressResult.Content.AddressStart + (byteOffset / 2 + index)).ToString( )) + $".{bitIndex}";
					}
					else
					{
						// 奇数的索引不支持写入操作
						if (index % 2 == 1) return string.Empty;
						if (byteOffset % 2 == 1) return string.Empty;

						if (address.StartsWith( "ZR" )) return address.Substring( 0, 2 ) + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" );

						return address.Substring( 0, 1 ) + (addressResult.Content.McDataType == MelsecMcDataType.W ?
							(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" ) :
							(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( ));
					}
				}
				else if (
					addressResult.Content.McDataType == MelsecMcDataType.M ||
					addressResult.Content.McDataType == MelsecMcDataType.L ||
					addressResult.Content.McDataType == MelsecMcDataType.F ||
					addressResult.Content.McDataType == MelsecMcDataType.V ||
					addressResult.Content.McDataType == MelsecMcDataType.S ||
					addressResult.Content.McDataType == MelsecMcDataType.B)
				{
					// 此处支持如果配置了按照字单位读取M区，且配置了位及其他类型读取的情况
					int bitIndex = 0;
					if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					{
						// 如果配置了针对字寄存器的bool解析位，那么就提供bool索引信息
						bitIndex = byteOffset * 8 + index;
					}
					else
					{
						bitIndex = byteOffset * 8 + index * 8;
					}
					if (addressResult.Content.McDataType.FromBase == 16)
						return address.Substring( 0, 1 ) + (addressResult.Content.AddressStart + bitIndex).ToString( "X" );
					else
						return address.Substring( 0, 1 ) + (addressResult.Content.AddressStart + bitIndex).ToString( );
				}
				else
				{
					return string.Empty;
				}
			}
		}


		public class Mc3C
		{
			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest, bool enableWriteBitToWord = false )
			{
				// 三菱PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text && enableWriteBitToWord == false)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 三菱PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是三菱的地址格式，就直接返回
				string address = sourceReadRequest.Address;
				OperateResult<McAddressData> addressResult = McAddressData.ParseMelsecFrom( address, (ushort)sourceReadRequest.GetLength( ), false );
				if (!addressResult.IsSuccess) return string.Empty;

				// 三菱目前只支持D,W,R,ZE的批量解析写入分析操作
				if (addressResult.Content.McDataType == MelsecMcDataType.D ||
					addressResult.Content.McDataType == MelsecMcDataType.W ||
					addressResult.Content.McDataType == MelsecMcDataType.R ||
					addressResult.Content.McDataType == MelsecMcDataType.ZR)
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					if (addressResult.Content.McDataType == MelsecMcDataType.ZR) return address.Substring( 0, 2 ) + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" );

					return address.Substring( 0, 1 ) + (addressResult.Content.McDataType == MelsecMcDataType.W ?
						(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" ) :
						(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( ));
				}
				else
				{
					return string.Empty;
				}
			}
		}

		public class FxLinks
		{
			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 三菱PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 先提取站号信息
				string station = string.Empty;
				string address = sourceReadRequest.Address;
				OperateResult<int> analysis = HslHelper.ExtractParameter( ref address, "s" );
				if (analysis.IsSuccess) station = $"s={analysis.Content};";

				// 三菱PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是三菱的地址格式，就直接返回
				OperateResult<MelsecFxLinksAddress> addressResult = MelsecFxLinksAddress.ParseFrom( address, (ushort)sourceReadRequest.GetLength( ) );
				if (!addressResult.IsSuccess) return string.Empty;

				// 三菱目前只支持D,R的批量解析写入分析操作
				if (addressResult.Content.TypeCode == "D" ||
					addressResult.Content.TypeCode == "R" )
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					return station + addressResult.Content.TypeCode +( addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );
				}
				else
				{
					return string.Empty;
				}
			}
		}

		public class FxSerial
		{
			/// <summary>
			/// 解析数据地址成不同的三菱地址类型
			/// </summary>
			/// <param name="address">数据地址</param>
			/// <returns>地址结果对象</returns>
			public static OperateResult<MelsecMcDataType, ushort> FxAnalysisAddress( string address )
			{
				var result = new OperateResult<MelsecMcDataType, ushort>( );
				try
				{
					switch (address[0])
					{
						case 'M':
						case 'm':
							{
								result.Content1 = MelsecMcDataType.M;
								result.Content2 = Convert.ToUInt16( address.Substring( 1 ), MelsecMcDataType.M.FromBase );
								break;
							}
						case 'X':
						case 'x':
							{
								result.Content1 = MelsecMcDataType.X;
								result.Content2 = Convert.ToUInt16( address.Substring( 1 ), 8 );
								break;
							}
						case 'Y':
						case 'y':
							{
								result.Content1 = MelsecMcDataType.Y;
								result.Content2 = Convert.ToUInt16( address.Substring( 1 ), 8 );
								break;
							}
						case 'D':
						case 'd':
							{
								result.Content1 = MelsecMcDataType.D;
								result.Content2 = Convert.ToUInt16( address.Substring( 1 ), MelsecMcDataType.D.FromBase );
								break;
							}
						case 'S':
						case 's':
							{
								result.Content1 = MelsecMcDataType.S;
								result.Content2 = Convert.ToUInt16( address.Substring( 1 ), MelsecMcDataType.S.FromBase );
								break;
							}
						case 'T':
						case 't':
							{
								if (address[1] == 'N' || address[1] == 'n')
								{
									result.Content1 = MelsecMcDataType.TN;
									result.Content2 = Convert.ToUInt16( address.Substring( 2 ), MelsecMcDataType.TN.FromBase );
									break;
								}
								else if (address[1] == 'S' || address[1] == 's')
								{
									result.Content1 = MelsecMcDataType.TS;
									result.Content2 = Convert.ToUInt16( address.Substring( 2 ), MelsecMcDataType.TS.FromBase );
									break;
								}
								else if (address[1] == 'C' || address[1] == 'c')
								{
									result.Content1 = MelsecMcDataType.TC;
									result.Content2 = Convert.ToUInt16( address.Substring( 2 ), MelsecMcDataType.TC.FromBase );
									break;
								}
								else
								{
									throw new Exception( StringResources.Language.NotSupportedDataType );
								}
							}
						case 'C':
						case 'c':
							{
								if (address[1] == 'N' || address[1] == 'n')
								{
									result.Content1 = MelsecMcDataType.CN;
									result.Content2 = Convert.ToUInt16( address.Substring( 2 ), MelsecMcDataType.CN.FromBase );
									break;
								}
								else if (address[1] == 'S' || address[1] == 's')
								{
									result.Content1 = MelsecMcDataType.CS;
									result.Content2 = Convert.ToUInt16( address.Substring( 2 ), MelsecMcDataType.CS.FromBase );
									break;
								}
								else if (address[1] == 'C' || address[1] == 'c')
								{
									result.Content1 = MelsecMcDataType.CC;
									result.Content2 = Convert.ToUInt16( address.Substring( 2 ), MelsecMcDataType.CC.FromBase );
									break;
								}
								else
								{
									throw new Exception( StringResources.Language.NotSupportedDataType );
								}
							}
						default: throw new Exception( StringResources.Language.NotSupportedDataType );
					}
				}
				catch (Exception ex)
				{
					result.Message = ex.Message;
					return result;
				}

				result.IsSuccess = true;
				return result;
			}

			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 三菱PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 先提取站号信息
				string address = sourceReadRequest.Address;

				// 三菱PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是三菱的地址格式，就直接返回
				OperateResult<MelsecMcDataType, ushort> addressResult = FxAnalysisAddress( address );
				if (!addressResult.IsSuccess) return string.Empty;

				// 三菱目前只支持D,R的批量解析写入分析操作
				if (addressResult.Content1 == MelsecMcDataType.D )
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					return (addressResult.Content2 + (byteOffset + index) / 2).ToString( );
				}
				else
				{
					return string.Empty;
				}
			}
		}
	}
}
