using HslCommunication;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 三菱的串口协议的实现
	/// </summary>
	public class DeviceMelsecFxSerial : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Melsec的编程口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecFxSerial( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeMelsecFxSerial( element );
			this.melsec = new MelsecFxSerial( );
			this.melsec.IsNewVersion = node.IsVersionNew;
			this.SetDeviceInfo( deviceResources, node, this.melsec );
		}

		private void ComIni( )
		{
			if (this.node == null) return;
			if (this.melsec == null) return;
			if (this.node.IsActiveOnStart)
			{
				int count = 0;
				OperateResult active = null;
				while (count < 3)
				{
					count++;
					active = this.melsec.ActivePlc( );
					if (active.IsSuccess)
					{
						SetDeviceForceMessgae( "COM", string.Empty );
						return;
					}
					else
					{
						this.RequestFailedCount++;
					}
					HslTechnologyHelper.Sleep( 100 );
				}

				if (active != null) SetDeviceForceMessgae( "COM", "COM Active 3 times failed: " + active.Message );
			}
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			melsec?.Open( );
			HslTechnologyHelper.Sleep( 100 );
			ComIni( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( ) => melsec?.Close( );

		/// <inheritdoc/>
		protected override void ExecuteAfterPipeWrong( )
		{
			base.ExecuteAfterPipeWrong( );
			ComIni( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = melsec.Open( );
			if (connect.IsSuccess) melsec.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceMelsecHelper.FxSerial.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceMelsecHelper.FxSerial.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecFxSerial] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private MelsecFxSerial melsec;               // 核心交互对象
		private NodeMelsecFxSerial node;             // 节点信息

		#endregion
	}
}
