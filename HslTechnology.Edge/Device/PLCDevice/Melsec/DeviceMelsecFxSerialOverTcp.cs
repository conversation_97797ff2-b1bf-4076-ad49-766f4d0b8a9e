using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 三菱PLC的编程口协议的网口透传版本，还支持GOT连接的方式。
	/// </summary>
	public class DeviceMelsecFxSerialOverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Melsec的编程口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecFxSerialOverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node          = new NodeMelsecFxSerialOverTcp( element );
			this.melsec              = new MelsecFxSerialOverTcp( );
			this.melsec.IsNewVersion = node.IsVersionNew;
			this.melsec.ConnectionId = node.DTU;
			this.melsec.UseGOT       = node.UseGotConnect;
			this.SetDeviceInfo( deviceResources, node, this.melsec );
		}

		private void NetIni( )
		{
			if (this.node == null) return;
			if (this.melsec == null) return;
			if (this.node.IsActiveOnStart)
			{
				int count = 0;
				OperateResult active = null;
				while (count < 3)
				{
					count++;
					active = this.melsec.ActivePlc( );
					if (active.IsSuccess)
					{
						SetDeviceForceMessgae( "NET", string.Empty );
						return;
					}
					HslTechnologyHelper.Sleep( 100 );
				}

				if (active != null) SetDeviceForceMessgae( "NET", "PLC Active 3 times failed: " + active.Message );
			}
		}
		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			melsec?.SetPersistentConnection( );
			NetIni( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( ) => melsec?.ConnectClose( );

		/// <inheritdoc/>
		protected override void ExecuteAfterPipeWrong( )
		{
			base.ExecuteAfterPipeWrong( );
			NetIni( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = melsec.ConnectServer( );
			if (connect.IsSuccess) melsec.ConnectClose( );

			return connect.Convert( ToString( ) ); ;
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			melsec?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceMelsecHelper.FxSerial.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceMelsecHelper.FxSerial.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecFxSerialOverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private MelsecFxSerialOverTcp melsec;               // 核心交互对象
		private NodeMelsecFxSerialOverTcp node;             // 节点信息

		#endregion
	}
}
