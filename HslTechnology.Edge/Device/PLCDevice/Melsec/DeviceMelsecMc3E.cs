using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslCommunication.Core.Address;
using System.Text.RegularExpressions;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Regular;
using HslCommunication.Profinet.Melsec.Helper;
using HslCommunication.Reflection;
using HslTechnology.Edge.Reflection;
using System.Threading.Tasks;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslCommunication.Core;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 三菱PLC的Qna兼容3E帧的请求方案
	/// </summary>
	public class DeviceMelsecMc3E : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个三菱的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecMc3E( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			this.node = new NodeMelsecMc( element );
			if (node.IsBinary)
			{
				this.melsecMcNet                              = new MelsecMcNet( node.IpAddress, node.Port );
				this.melsecMcNet.NetworkNumber                = node.NetworkNumber;
				this.melsecMcNet.NetworkStationNumber         = node.NetworkStationNumber;
				this.melsecMcNet.ConnectionId                 = node.DTU;
				this.melsecMcNet.EnableWriteBitToWordRegister = node.EnableWriteBitToWord;
				this.SetDeviceInfo( deviceResources, node, this.melsecMcNet );
			}
			else
			{
				this.melsecMcAscii                              = new MelsecMcAsciiNet( node.IpAddress, node.Port );
				this.melsecMcAscii.NetworkNumber                = node.NetworkNumber;
				this.melsecMcAscii.NetworkStationNumber         = node.NetworkStationNumber;
				this.melsecMcAscii.ConnectionId                 = node.DTU;
				this.melsecMcAscii.EnableWriteBitToWordRegister = node.EnableWriteBitToWord;
				this.SetDeviceInfo( deviceResources, node, this.melsecMcAscii );
			}
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			if (node.IsBinary)
				melsecMcNet?.SetPersistentConnection( );
			else
				melsecMcAscii?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			if (node.IsBinary)
				melsecMcNet?.ConnectClose( );
			else
				melsecMcAscii?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			if (node.IsBinary)
			{
				OperateResult connect = melsecMcNet.ConnectServer( );
				if (connect.IsSuccess) melsecMcNet.ConnectClose( );

				return connect.Convert( ToString( ) );
			}
			else
			{
				OperateResult connect = melsecMcAscii.ConnectServer( );
				if (connect.IsSuccess) melsecMcAscii.ConnectClose( );

				return connect.Convert( ToString( ) );
			}
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			if (node.IsBinary)
				melsecMcNet?.ConnectServer( alienSession );
			else
				melsecMcAscii?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		private IReadWriteMc GetMelsec( )
		{
			if (node.IsBinary)
				return melsecMcNet;
			else
				return melsecMcAscii;
		}

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceMelsecHelper.Mc3E.GetAccessLevelFromRequest( scalarReadRequest, this.node.EnableWriteBitToWord );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceMelsecHelper.Mc3E.CalculatePhysicalAddressFromSourceReadRequest( GetMelsec( ), sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Method Interface

		/// <inheritdoc cref="MelsecMcNet.RemoteRun"/>
		[HslMqttApi( Description = "远程Run操作" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> RemoteRun( )
		{
			if (node.IsBinary)
				return await melsecMcNet.RemoteRunAsync( );
			else
				return await melsecMcAscii.RemoteRunAsync( );
		}

		/// <inheritdoc cref="MelsecMcNet.RemoteStop"/>
		[HslMqttApi( Description = "远程Stop操作" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> RemoteStop( )
		{
			if (node.IsBinary)
				return await melsecMcNet.RemoteStopAsync( );
			else
				return await melsecMcAscii.RemoteStopAsync( );
		}

		/// <inheritdoc cref="MelsecMcNet.RemoteReset"/>
		[HslMqttApi( Description = "LED 熄灭 出错代码初始化" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> RemoteReset( )
		{
			if (node.IsBinary)
				return await melsecMcNet.RemoteResetAsync( );
			else
				return await melsecMcAscii.RemoteResetAsync( );
		}

		/// <inheritdoc cref="MelsecMcNet.ErrorStateReset"/>
		[HslMqttApi( Description = "LED 熄灭 出错代码初始化" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> ErrorStateReset( )
		{
			if (node.IsBinary)
				return await melsecMcNet.ErrorStateResetAsync( );
			else
				return await melsecMcAscii.ErrorStateResetAsync( );
		}

		/// <inheritdoc cref="MelsecMcNet.ReadPlcType"/>
		[HslMqttApi( Description = "读取PLC的型号信息，例如 Q02HCPU" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadPlcType( )
		{
			if (node.IsBinary)
				return await melsecMcNet.ReadPlcTypeAsync( );
			else
				return await melsecMcAscii.ReadPlcTypeAsync( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( )
		{
			if (node.IsBinary)
				return $"[MelsecMc3E] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
			else
				return $"[MelsecMc3E-ASCII] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
		}

		#endregion

		#region Private

		private MelsecMcAsciiNet melsecMcAscii;               // ASCII格式的交互对象
		private MelsecMcNet melsecMcNet;                      // 二进制格式的交互对象
		private NodeMelsecMc node;                            // 当前的节点信息

		#endregion
	}
}
