using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Profinet.Melsec.Helper;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// MELSEC设备的MC协议的
	/// </summary>
	public class DeviceMelsecMcUdp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个三菱的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecMcUdp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node = new NodeMelsecMcUdp( element );
			if (node.IsBinary)
			{
				this.melsecMcNet                              = new MelsecMcUdp( node.IpAddress, node.Port );
				this.melsecMcNet.NetworkNumber                = node.NetworkNumber;
				this.melsecMcNet.NetworkStationNumber         = node.NetworkStationNumber;
				this.melsecMcNet.EnableWriteBitToWordRegister = node.EnableWriteBitToWord;
				this.SetDeviceInfo( deviceResources, node, this.melsecMcNet );
			}
			else
			{
				this.melsecMcAscii                              = new MelsecMcAsciiUdp( node.IpAddress, node.Port );
				this.melsecMcAscii.NetworkNumber                = node.NetworkNumber;
				this.melsecMcAscii.NetworkStationNumber         = node.NetworkStationNumber;
				this.melsecMcAscii.EnableWriteBitToWordRegister = node.EnableWriteBitToWord;
				this.SetDeviceInfo( deviceResources, node, this.melsecMcAscii );
			}
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			if (node.IsBinary)
			{
				System.Net.NetworkInformation.IPStatus status = melsecMcNet.IpAddressPing( );

				return status == System.Net.NetworkInformation.IPStatus.Success ?
					OperateResult.CreateSuccessResult( ToString( ) ) :
					new OperateResult<string>( "Ping Wrong:" + status.ToString( ) );
			}
			else
			{
				System.Net.NetworkInformation.IPStatus status = melsecMcAscii.IpAddressPing( );

				return status == System.Net.NetworkInformation.IPStatus.Success ?
					OperateResult.CreateSuccessResult( ToString( ) ) :
					new OperateResult<string>( "Ping Wrong:" + status.ToString( ) );
			}
		}
		
		#endregion

		#region ReadWrite Access

		private IReadWriteMc GetMelsec( )
		{
			if (node.IsBinary)
				return melsecMcNet;
			else
				return melsecMcAscii;
		}

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceMelsecHelper.Mc3E.GetAccessLevelFromRequest( scalarReadRequest, this.node.EnableWriteBitToWord );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceMelsecHelper.Mc3E.CalculatePhysicalAddressFromSourceReadRequest( GetMelsec( ), sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( )
		{
			if (node.IsBinary)
				return $"[MelsecMcUdp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
			else
				return $"[MelsecMcUdp-ASCII] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
		}

		#endregion

		#region Private

		private MelsecMcAsciiUdp melsecMcAscii;               // ASCII格式的交互对象
		private MelsecMcUdp melsecMcNet;                      // 二进制格式的交互对象
		private NodeMelsecMcUdp node;                   // 节点的配置对象信息

		#endregion
	}
}
