using HslCommunication;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Melsec;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 三菱PLC的计算机链接协议
	/// </summary>
	public class DeviceMelsecFxlinks : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个Melsec的Fxlinks协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceMelsecFxlinks( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node             = new NodeMelsecFxlinks( element );
			this.melsec                 = new MelsecFxLinks( );
			this.melsec.Station         = node.Station;
			this.melsec.SumCheck        = node.SumCheck;
			this.melsec.WaittingTime    = node.WaittingTime;
			this.SetDeviceInfo( deviceResources, node, this.melsec );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => melsec?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => melsec?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = melsec.Open( );
			if (connect.IsSuccess) melsec.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => DeviceMelsecHelper.FxLinks.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => DeviceMelsecHelper.FxLinks.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecFxlinks] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private MelsecFxLinks melsec;               // 核心交互对象
		private NodeMelsecFxlinks node;             // 节点信息

		#endregion
	}
}
