using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication;
using HslTechnology.Edge.Node.Device;
using HslCommunication.Profinet.Beckhoff;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Resources;
using HslCommunication.Reflection;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 倍福的PLC设备信息
	/// </summary>
	public class DeviceBeckhoffAds : DeviceCore
	{
		/// <summary>
		/// 实例化一个默认的倍福的ADS协议的PLC设备
		/// </summary>
		/// <param name="element">元素对象</param>
		public DeviceBeckhoffAds( XElement element ) : base( element ) { }

		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node                   = new NodeBeckhoffAds( element );
			this.beckhoff               = new BeckhoffAdsNet( node.IpAddress, node.Port );
			this.beckhoff.UseTagCache   = node.TagCache;
			this.beckhoff.ConnectionId  = this.node.DTU;
			SetDeviceInfo( deviceResources, node, this.beckhoff );
			if (string.IsNullOrEmpty( node.SenderNetId ) && string.IsNullOrEmpty( node.TargetNetId ))
			{
				this.beckhoff.UseAutoAmsNetID = true;
				this.beckhoff.AmsPort = node.AmsPort;
			}
			else
			{
				this.beckhoff.SetSenderAMSNetId( node.SenderNetId );
				this.beckhoff.SetTargetAMSNetId( node.TargetNetId );
			}
		}

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => beckhoff?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => beckhoff?.ConnectClose( );

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			return base.GetAccessLevelFromRequest( scalarReadRequest );
		}

		private string GetPhysicalAddress( 
			string address, 
			int byteOffset,
			int index, 
			IScalarTransform scalarTransform )
		{
			// Bool数值或是数组暂时不允许写入操作
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text) return string.Empty;

			// 排除掉绝对地址开头以及符号地址开头的
			if (address.StartsWith( "i=", StringComparison.OrdinalIgnoreCase ) ||
				address.StartsWith( "s=", StringComparison.OrdinalIgnoreCase ) ||
				address.StartsWith( "ig=", StringComparison.OrdinalIgnoreCase )) return string.Empty;

			try
			{
				string type = address.Substring( 0, 1 );
				uint add = Convert.ToUInt32( address.Substring( 1 ) );

				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text)
				{
					return type + $"{add + byteOffset + index / 8}.{index % 8}";
				}
				else
				{
					return type + $"{add + byteOffset + index}";
				}
			}
			catch
			{
				return string.Empty;
			}
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			return SourceReadRequest.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform, GetPhysicalAddress );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = beckhoff.ConnectServer( );
			if (connect.IsSuccess) beckhoff.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			beckhoff?.ConnectServer( alienSession );
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult<byte[]>> ReadBatch( string[] address, ushort[] length )
		{
			return await beckhoff.ReadAsync( address, length );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="BeckhoffAdsNet.TransValueHandle(string)"/>
		[HslMqttApi( Description = "将字符串的地址转换为内存的地址，其他地址则不操作" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> TransValueHandleAsync( string address ) => await beckhoff.TransValueHandleAsync( address );

		/// <inheritdoc cref="BeckhoffAdsNet.ReadAdsDeviceInfo"/>
		[HslMqttApi( Description = "读取Ads设备的设备信息。主要是版本号，设备名称" )]
		[RpcExtensionInfo( DataType = DataType.Class )]
		public async Task<OperateResult<AdsDeviceInfo>> ReadAdsDeviceInfoAsync( ) => await beckhoff.ReadAdsDeviceInfoAsync( );

		/// <inheritdoc cref="BeckhoffAdsNet.ReadAdsState"/>
		[HslMqttApi( Description = "读取Ads设备的状态信息，其中 [Content1] 是Ads State，[Content2] 是Device State" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> ReadAdsStateAsync( ) => await beckhoff.ReadAdsStateAsync( );

		/// <inheritdoc cref="BeckhoffAdsNet.ReleaseSystemHandle(uint)"/>
		[HslMqttApi( Description = "释放当前的系统句柄，该句柄是通过 [ReadValueHandle(string)] 获取的" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> ReleaseSystemHandle( uint handle ) => await beckhoff.ReleaseSystemHandleAsync( handle );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[BeckhoffAdsNet] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private BeckhoffAdsNet beckhoff;               // 核心交互对象
		private NodeBeckhoffAds node;                  // 节点信息

		#endregion
	}
}
