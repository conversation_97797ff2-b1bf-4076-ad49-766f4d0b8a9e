using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslCommunication.Profinet.Keyence;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 基恩士的Nano协议的PLC设备，使用网口透传实现的方式
	/// </summary>
	public class DeviceKeyenceNanoOverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个基恩士设备的对象，需要传入基本配置信息
		/// </summary>
		/// <param name="element">配置元素信息</param>
		public DeviceKeyenceNanoOverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node                 = new NodeKeyenceNanoOverTcp( element );
			this.keyence              = new KeyenceNanoSerialOverTcp( node.IpAddress, node.Port );
			this.keyence.ConnectionId = this.node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.keyence );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			keyence?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			keyence?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = keyence.ConnectServer( );
			if (connect.IsSuccess) keyence.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			keyence?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => Keyence.DeviceKeyenceHelper.Nano.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Keyence.DeviceKeyenceHelper.Nano.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( )
		{
			return $"[KeyenceNanoSerialOverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
		}

		#endregion

		#region Private Member

		private KeyenceNanoSerialOverTcp keyence;               // 核心设备信息
		private NodeKeyenceNanoOverTcp node;                    // 节点信息

		#endregion

	}
}
