using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Keyence;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 基恩士PLC的Qna兼容3E帧的请求方案
	/// </summary>
	public class DeviceKeyenceMc3E : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个基恩士的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceKeyenceMc3E( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeKeyenceMc( element );

			if (node.IsBinary)
			{
				this.keyenceMcNet                            = new KeyenceMcNet( node.IpAddress, node.Port );
				this.keyenceMcNet.NetworkNumber              = node.NetworkNumber;
				this.keyenceMcNet.NetworkStationNumber       = node.NetworkStationNumber;
				this.keyenceMcNet.ConnectionId               = node.DTU;
				this.SetDeviceInfo( deviceResources, node, this.keyenceMcNet );
			}
			else
			{
				this.keyenceMcAscii                          = new KeyenceMcAsciiNet( node.IpAddress, node.Port );
				this.keyenceMcAscii.NetworkNumber            = node.NetworkNumber;
				this.keyenceMcAscii.NetworkStationNumber     = node.NetworkStationNumber;
				this.keyenceMcAscii.ConnectionId             = node.DTU;
				this.SetDeviceInfo( deviceResources, node, this.keyenceMcAscii );
			}
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			if (node.IsBinary)
				keyenceMcNet?.SetPersistentConnection( );
			else
				keyenceMcAscii?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			if (node.IsBinary)
				keyenceMcNet?.ConnectClose( );
			else
				keyenceMcAscii?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			if (node.IsBinary)
			{
				OperateResult connect = keyenceMcNet.ConnectServer( );
				if (connect.IsSuccess) keyenceMcNet.ConnectClose( );

				return connect.Convert( ToString( ) ); ;
			}
			else
			{
				OperateResult connect = keyenceMcAscii.ConnectServer( );
				if (connect.IsSuccess) keyenceMcAscii.ConnectClose( );

				return connect.Convert( ToString( ) );
			}
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			if (node.IsBinary)
				keyenceMcNet?.ConnectServer( alienSession );
			else
				keyenceMcAscii?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => Keyence.DeviceKeyenceHelper.MC3E.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Keyence.DeviceKeyenceHelper.MC3E.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( )
		{
			if (node.IsBinary)
				return $"[KeyenceMc] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
			else
				return $"[KeyenceMc-ASCII] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";
		}

		#endregion

		#region Private
		
		private KeyenceMcAsciiNet keyenceMcAscii;               // ASCII格式的交互对象
		private KeyenceMcNet keyenceMcNet;                      // 二进制格式的交互对象
		private NodeKeyenceMc node;                             // 节点信息

		#endregion
	}
}
