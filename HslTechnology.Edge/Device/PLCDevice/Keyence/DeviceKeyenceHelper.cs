using HslCommunication;
using HslCommunication.Core.Address;
using HslCommunication.Profinet.Melsec;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice.Keyence
{
	internal class DeviceKeyenceHelper
	{
		public class MC3E
		{

			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 基恩士PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持
				string address = sourceReadRequest.Address;

				// 如果不是正确的地址格式，就直接返回
				OperateResult<McAddressData> addressResult = McAddressData.ParseKeyenceFrom( address, (ushort)sourceReadRequest.GetLength( ), false );
				if (!addressResult.IsSuccess) return string.Empty;

				// 基恩士目前只支持D,SD,R,W的批量解析写入分析操作
				if (addressResult.Content.McDataType == MelsecMcDataType.Keyence_D ||
					addressResult.Content.McDataType == MelsecMcDataType.Keyence_SD ||
					addressResult.Content.McDataType == MelsecMcDataType.Keyence_R ||
					addressResult.Content.McDataType == MelsecMcDataType.Keyence_W)
				{
					// 奇数的索引不支持写入操作
					if (index % 2 == 1) return string.Empty;
					if (byteOffset % 2 == 1) return string.Empty;

					if (addressResult.Content.McDataType == MelsecMcDataType.Keyence_SD) return address.Substring( 0, 2 ) + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );

					return address.Substring( 0, 1 ) + (addressResult.Content.McDataType == MelsecMcDataType.W ?
						(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" ) :
						(addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( ));
				}
				else
				{
					return string.Empty;
				}
			}

		}
		public class Nano
		{
			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 基恩士PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持
				string address = sourceReadRequest.Address;
				if (address.StartsWith( "unit=" ))
					return string.Empty;

				// 如果不是正确的地址格式，就直接返回
				OperateResult<KeyenceNanoAddress> addressResult = KeyenceNanoAddress.ParseFrom( address, (ushort)sourceReadRequest.GetLength( ) );
				if (!addressResult.IsSuccess) return string.Empty;

				if (addressResult.Content.DataCode == "CR" ||
					addressResult.Content.DataCode == "MR" ||
					addressResult.Content.DataCode == "LR" ||
					string.IsNullOrEmpty( addressResult.Content.DataCode )) return string.Empty;

				// 奇数的索引不支持写入操作
				if (index % 2 == 1) return string.Empty;
				if (byteOffset % 2 == 1) return string.Empty;

				// 基恩士目前只支持D,W,R,ZE的批量解析写入分析操作
				if (addressResult.Content.DataCode == "VB" ||
					addressResult.Content.DataCode == "W" ||
					addressResult.Content.DataCode == "B")
				{
					return addressResult.Content.DataCode + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( "X" );
				}
				else
				{
					return addressResult.Content.DataCode + (addressResult.Content.AddressStart + (byteOffset + index) / 2).ToString( );
				}
			}
		}
	}
}
