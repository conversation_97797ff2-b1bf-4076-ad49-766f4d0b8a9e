using HslCommunication;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Vigor;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 丰炜PLC的串口转网口设备
	/// </summary>
	public class DeviceVigorSerialOverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个丰炜编程口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceVigorSerialOverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node          = new NodeVigorSerialOverTcp( element );
			this.vigor         = new  VigorSerialOverTcp( node.IpAddress, node.Port );
			this.vigor.Station = node.Station;
			this.SetDeviceInfo( deviceResources, node, this.vigor );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => vigor?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => vigor?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = vigor.ConnectServer( );
			if (connect.IsSuccess) vigor.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => Vigor.DeviceVigorHelper.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Vigor.DeviceVigorHelper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[VigorSerialOverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private VigorSerialOverTcp vigor;               // 核心交互对象
		private NodeVigorSerialOverTcp node;            // 节点信息

		#endregion
	}
}
