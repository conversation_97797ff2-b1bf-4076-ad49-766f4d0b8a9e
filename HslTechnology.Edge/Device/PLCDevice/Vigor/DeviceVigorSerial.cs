using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslCommunication.Profinet.Vigor;
using HslCommunication;
using HslTechnology.Edge.Resources;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 丰炜的编程口的设备对象
	/// </summary>
	public class DeviceVigorSerial : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个丰炜编程口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceVigorSerial( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node          = new NodeVigorSerial( element );
			this.vigor         = new VigorSerial( );
			this.vigor.Station = node.Station;
			this.SetDeviceInfo( deviceResources, node, this.vigor );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => vigor?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => vigor?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = vigor.Open( );
			if (connect.IsSuccess) vigor.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => Vigor.DeviceVigorHelper.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Vigor.DeviceVigorHelper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[VigorSerial] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private VigorSerial vigor;               // 核心交互对象
		private NodeVigorSerial node;            // 节点信息

		#endregion
	}
}
