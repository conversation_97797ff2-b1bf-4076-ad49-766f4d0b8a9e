using HslTechnology.Edge.Node.Device;
using HslCommunication.Profinet.XINJE;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslCommunication;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 信捷串口设备对象，底层使用的是Modbus-Rtu协议
	/// </summary>
	public class DeviceXinJESerial : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个基于Modbus-RTU信捷编程口协议的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceXinJESerial( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node                       = new NodeXinJESerial( element );
			this.xinJE                      = new XinJESerial( node.Station );
			this.xinJE.AddressStartWithZero = node.IsAddressStartWithZero;
			this.xinJE.DataFormat           = node.DataFormat;
			this.xinJE.IsStringReverse      = node.IsStringReverse;
			this.xinJE.Series               = node.PlcType;
			this.SetDeviceInfo( deviceResources, node, this.xinJE );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => xinJE?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => xinJE?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = xinJE.Open( );
			if (connect.IsSuccess) xinJE.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => XINJE.DeviceXinJEHelper.Modbus.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => XINJE.DeviceXinJEHelper.Modbus.CalculatePhysicalAddressFromSourceReadRequest( node.PlcType, sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[XinJESerial<{node.PlcType}>] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private XinJESerial xinJE;               // 核心交互对象
		private NodeXinJESerial node;            // 节点信息

		#endregion
	}
}
