using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.XINJE;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	public class DeviceXinJETcpNet : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个基于Modbus-Tcp协议的信捷设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceXinJETcpNet( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node                       = new NodeXinJETcpNet( element );
			this.xinJE                      = new XinJETcpNet( node.IpAddress, node.Port, node.Station );
			this.xinJE.AddressStartWithZero = node.IsAddressStartWithZero;
			this.xinJE.DataFormat           = node.DataFormat;
			this.xinJE.IsStringReverse      = node.IsStringReverse;
			this.xinJE.ConnectionId         = node.DTU;
			this.xinJE.IsCheckMessageId     = node.CheckMessageID;
			this.xinJE.Series               = node.PlcType;
			this.SetDeviceInfo( deviceResources, node, this.xinJE );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => xinJE?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => xinJE?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = xinJE.ConnectServer( );
			if (connect.IsSuccess) xinJE.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			xinJE?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => XINJE.DeviceXinJEHelper.Modbus.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => XINJE.DeviceXinJEHelper.Modbus.CalculatePhysicalAddressFromSourceReadRequest( node.PlcType, sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[XinJETcpNet<{node.PlcType}>] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private XinJETcpNet xinJE;                // 核心交互对象
		private NodeXinJETcpNet node;             // 节点信息

		#endregion
	}
}
