using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Profinet.XINJE;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice.XINJE
{
	internal class DeviceXinJEHelper
	{
		private static OperateResult<string, int> AnalysisAddress( XinJESeries series, string address )
		{
			try
			{
				if (series == XinJESeries.XC)
				{
					if      (address.StartsWith( "D" ) || address.StartsWith( "d" )) return OperateResult.CreateSuccessResult( "D", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "F" ) || address.StartsWith( "f" )) return OperateResult.CreateSuccessResult( "F", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "E" ) || address.StartsWith( "e" )) return OperateResult.CreateSuccessResult( "E", Convert.ToInt32( address.Substring( 1 ) ) );
					else return new OperateResult<string, int>( HslCommunication.StringResources.Language.NotSupportedDataType );
				}
				else
				{
					if      (address.StartsWith( "ID" ) || address.StartsWith( "id" ))     return OperateResult.CreateSuccessResult( "ID",   Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "QD" ) || address.StartsWith( "qd" ))     return OperateResult.CreateSuccessResult( "QD",   Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "HSCD" ) || address.StartsWith( "hscd" )) return OperateResult.CreateSuccessResult( "HSCD", Convert.ToInt32( address.Substring( 4 ) ) );
					else if (address.StartsWith( "ETD" ) || address.StartsWith( "etd" ))   return OperateResult.CreateSuccessResult( "ETD",  Convert.ToInt32( address.Substring( 3 ) ) );
					else if (address.StartsWith( "HSD" ) || address.StartsWith( "hsd" ))   return OperateResult.CreateSuccessResult( "HSD",  Convert.ToInt32( address.Substring( 3 ) ) );
					else if (address.StartsWith( "HTD" ) || address.StartsWith( "htd" ))   return OperateResult.CreateSuccessResult( "HTD",  Convert.ToInt32( address.Substring( 3 ) ) );
					else if (address.StartsWith( "HCD" ) || address.StartsWith( "hcd" ))   return OperateResult.CreateSuccessResult( "HCD",  Convert.ToInt32( address.Substring( 3 ) ) );
					else if (address.StartsWith( "SFD" ) || address.StartsWith( "sfd" ))   return OperateResult.CreateSuccessResult( "SFD",  Convert.ToInt32( address.Substring( 3 ) ) );
					else if (address.StartsWith( "SD" )  || address.StartsWith( "sd" ))    return OperateResult.CreateSuccessResult( "SD",   Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "TD" )  || address.StartsWith( "td" ))    return OperateResult.CreateSuccessResult( "TD",   Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "CD" )  || address.StartsWith( "cd" ))    return OperateResult.CreateSuccessResult( "CD",   Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "HD" )  || address.StartsWith( "hd" ))    return OperateResult.CreateSuccessResult( "HD",   Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "FD" )  || address.StartsWith( "fd" ))    return OperateResult.CreateSuccessResult( "FD",   Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "FS" )  || address.StartsWith( "fs" ))    return OperateResult.CreateSuccessResult( "FS",   Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "D" )   || address.StartsWith( "d" ))     return OperateResult.CreateSuccessResult( "D",    Convert.ToInt32( address.Substring( 1 ) ) );
					else return new OperateResult<string, int>( HslCommunication.StringResources.Language.NotSupportedDataType );
				}
			}
			catch (Exception ex)
			{
				return new OperateResult<string, int>( ex.Message );
			}
		}

		public class Modbus
		{


			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 信捷的PLC对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				XinJESeries series,
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 先提取站号信息
				string station = string.Empty;
				string address = sourceReadRequest.Address;
				OperateResult<int> analysis = HslHelper.ExtractParameter( ref address, "s" );
				if (analysis.IsSuccess) station = $"s={analysis.Content};";

				// PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是富士的地址格式，就直接返回
				OperateResult<string, int> addressResult = AnalysisAddress( series, address );
				if (!addressResult.IsSuccess) return string.Empty;

				// 奇数的索引不支持写入操作
				if (index % 2 == 1) return string.Empty;
				if (byteOffset % 2 == 1) return string.Empty;

				return station + addressResult.Content1 + (addressResult.Content2 + (byteOffset + index) / 2).ToString( );
			}
		}

	}
}
