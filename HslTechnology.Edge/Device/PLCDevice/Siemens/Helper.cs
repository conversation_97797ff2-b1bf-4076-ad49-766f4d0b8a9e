using HslCommunication;
using HslCommunication.Core.Address;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice.Siemens
{
	/// <summary>
	/// 辅助类工具
	/// </summary>
	public class Helper
	{
		private static string CalculateBitIndex( int addressStart )
		{
			if (addressStart % 8 == 0)
				return (addressStart / 8).ToString( );
			else
				return (addressStart / 8).ToString( ) + "." + (addressStart % 8).ToString( );
		}

		private static string ToStringAddress( S7AddressData s7Address )
		{
			if (s7Address.DataCode == 0x81)
				return "I" + CalculateBitIndex( s7Address.AddressStart );
			else if (s7Address.DataCode == 0x82)
				return "Q" + CalculateBitIndex( s7Address.AddressStart );
			else if (s7Address.DataCode == 0x83)
				return "M" + CalculateBitIndex( s7Address.AddressStart );
			else if (s7Address.DataCode == 0x84)
				return "DB" + s7Address.DbBlock + "." + CalculateBitIndex( s7Address.AddressStart );
			else if (s7Address.DataCode == 0x06)
				return "AI" + CalculateBitIndex( s7Address.AddressStart );
			else if (s7Address.DataCode == 0x07)
				return "AQ" + CalculateBitIndex( s7Address.AddressStart );
			else
				return string.Empty;  // 暂时对T,C区不支持
		}

		internal static string CalculatePhysicalAddressFromSourceReadRequest(
			string address,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			// 西门子对于bool[]只能读取，不能写入
			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text && scalarTransform.Length > 0)
				return string.Empty;

			// 如果不是西门子地址格式，就直接返回
			OperateResult<S7AddressData> siemensAddress = S7AddressData.ParseFrom( address );
			if (!siemensAddress.IsSuccess) return string.Empty;

			if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text)
				siemensAddress.Content.AddressStart += byteOffset * 8 + index;
			else
				siemensAddress.Content.AddressStart += byteOffset * 8 + index * 8;

			return ToStringAddress( siemensAddress.Content );
		}

		/// <inheritdoc/>
		internal static string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform )
		{
			return SourceReadRequest.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform, CalculatePhysicalAddressFromSourceReadRequest );
		}
	}
}
