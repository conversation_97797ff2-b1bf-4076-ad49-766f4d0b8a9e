using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Xml.Linq;
using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Siemens;
using HslCommunication.Core.Address;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using System.Threading.Tasks;
using HslCommunication.Core;
using HslCommunication.Reflection;
using HslTechnology.Edge.Reflection;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Resources;
using HslCommunication.BasicFramework;
using Newtonsoft.Json.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 西门子的S7协议的设备
	/// </summary>
	public class DeviceSiemensS7 : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个西门子的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceSiemensS7( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.nodeSiemens = new NodeSiemensS7( element );
			if      (nodeSiemens.PlcType == SiemensPLCS.S300)      this.siemensS7Net = new SiemensS7Net( SiemensPLCS.S300 );
			else if (nodeSiemens.PlcType == SiemensPLCS.S400)      this.siemensS7Net = new SiemensS7Net( SiemensPLCS.S400 );
			else if (nodeSiemens.PlcType == SiemensPLCS.S1200)     this.siemensS7Net = new SiemensS7Net( SiemensPLCS.S1200 );
			else if (nodeSiemens.PlcType == SiemensPLCS.S1500)     this.siemensS7Net = new SiemensS7Net( SiemensPLCS.S1500 );
			else if (nodeSiemens.PlcType == SiemensPLCS.S200Smart) this.siemensS7Net = new SiemensS7Net( SiemensPLCS.S200Smart );
			else if (nodeSiemens.PlcType == SiemensPLCS.S200)      this.siemensS7Net = new SiemensS7Net( SiemensPLCS.S200 );
			else this.siemensS7Net = new SiemensS7Net( SiemensPLCS.S1200 );

			if(nodeSiemens.PlcType == SiemensPLCS.S200Smart)
			{

			}
			else if (nodeSiemens.PlcType == SiemensPLCS.S200)
			{
				this.siemensS7Net.DestTSAP  = nodeSiemens.DestTSAP;
				this.siemensS7Net.LocalTSAP = nodeSiemens.LocalTSAP;
			}
			else
			{
				this.siemensS7Net.Slot           = nodeSiemens.Slot;
				this.siemensS7Net.Rack           = nodeSiemens.Rack;
				this.siemensS7Net.ConnectionType = nodeSiemens.ConnectionType;
				this.siemensS7Net.LocalTSAP      = nodeSiemens.LocalTSAP;
			}
			this.siemensS7Net.ConnectionId       = nodeSiemens.DTU;
			this.SetDeviceInfo( deviceResources, nodeSiemens, this.siemensS7Net );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			siemensS7Net?.ConnectServer( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			siemensS7Net?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = siemensS7Net.ConnectServer( );
			if (connect.IsSuccess) siemensS7Net.ConnectClose( );

			return connect.Convert( ToString() );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			siemensS7Net?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected async override Task<OperateResult<byte[]>> ReadBatch( string[] address, ushort[] length )
		{
			return await siemensS7Net.ReadAsync( address, length );
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			// TODO 字符串数组的读取还没有解析
			if (scalarRequest.Length < 0)
			{
				if (scalarRequest.StringLength == 0)
				{
					OperateResult<string> read = UseAsyncReadWrite( ) ?
						await siemensS7Net.ReadStringAsync( scalarRequest.Address, RegularScalarNode.GetEncoding( scalarRequest.StringEncoding ) ) :
						siemensS7Net.ReadString( scalarRequest.Address, RegularScalarNode.GetEncoding( scalarRequest.StringEncoding ) );
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
				}
				else
					return await base.ReadActualStringAsync( scalarRequest );
			}
			else
			{
				return new OperateResult( "SiemensS7 不支持字符串数组的标量请求，请使用原始字节请求配置！" );
			}
		}

		/// <inheritdoc/>
		protected override OperateResult WriteValueByScalarRequest( IReadWriteNet readWriteDevice, string address, IScalarTransform transform, string value )
		{
			if (transform.DataTypeCode == RegularNodeTypeItem.String.Text)
			{
				if (transform is ScalarReadRequest request)   // 标量的情况
				{
					// 如果是西门子自适应的长度的情况
					if ( request.StringLength == 0)
					{

						return readWriteDevice.Write( address, value, RegularScalarNode.GetEncoding( request.StringEncoding ) );
					}
				}
			}
			return base.WriteValueByScalarRequest( readWriteDevice, address, transform, value );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			// 西门子对于bool[]只能读取，不能写入
			if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text &&
				scalarReadRequest.Length > 0)
				return AccessLevel.Read;
			return base.GetAccessLevelFromRequest( scalarReadRequest );
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Siemens.Helper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Method Interface

		/// <inheritdoc cref="SiemensS7Net.ReadOrderNumber"/>
		[HslMqttApi( Description = "从PLC读取订货号信息" )]
		[RpcExtensionInfo( DataType = DataType.String )]
		public async Task<OperateResult<string>> ReadOrderNumber( )
		{
			return await siemensS7Net.ReadOrderNumberAsync( );
		}

		/// <inheritdoc cref="HslCommunication.Core.IReadWriteNet.ReadInt16(string)"/>
		[HslMqttApi( Description = "从PLC指定地址读取short数据" )]
		[RpcExtensionInfo( DataType = DataType.Int16 )]
		public async Task<OperateResult<short>> ReadInt16( string address )
		{
			return await siemensS7Net.ReadInt16Async( address );
		}

		/// <inheritdoc cref="HslCommunication.Core.IReadWriteNet.ReadInt16(string)"/>
		[HslMqttApi( Description = "往PLC指定地址写入short数据" )]
		[RpcExtensionInfo( DataType = DataType.Method )]
		public async Task<OperateResult> WriteInt16( string address, short value )
		{
			return await siemensS7Net.WriteAsync( address, value );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Siemens{nodeSiemens.PlcType}] [{GetDeviceNameWithPath( )}] [{nodeSiemens.GetSocketInfo( )}]";

		#endregion

		#region Private

		private SiemensS7Net siemensS7Net;               // S7协议交互对象
		private NodeSiemensS7 nodeSiemens;               // 节点对象

		#endregion

		#region Static Helper

		#endregion
	}
}
