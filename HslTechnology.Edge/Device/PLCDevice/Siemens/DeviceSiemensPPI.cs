using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication.Profinet.Siemens;
using HslCommunication;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node.Regular;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 西门子PPI协议实现的设备信息
	/// </summary>
	public class DeviceSiemensPPI : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个西门子的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceSiemensPPI( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                  = new NodeSiemensPPI( element );
			this.siemens               = new SiemensPPI( );
			this.siemens.Station       = node.Station;
			this.SetDeviceInfo( deviceResources, node, this.siemens );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => siemens?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => siemens?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = siemens.Open( );
			if (connect.IsSuccess) siemens.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			// 西门子对于bool[]只能读取，不能写入
			if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text &&
				scalarReadRequest.Length > 0)
				return AccessLevel.Read;
			return base.GetAccessLevelFromRequest( scalarReadRequest );
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Siemens.Helper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensPPI] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion

		#region Private

		private SiemensPPI siemens;               // 核心交互对象
		private NodeSiemensPPI node;              // 节点信息

		#endregion
	}
}
