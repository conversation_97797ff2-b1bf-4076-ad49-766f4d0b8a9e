using HslCommunication.Profinet.Siemens;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslCommunication;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Request;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 基于fetch/write协议的西门子设备信息
	/// </summary>
	public class DeviceSiemensFW : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个西门子的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceSiemensFW( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                           = new NodeSiemensFW( element );
			this.siemensFetchWrite              = new SiemensFetchWriteNet( node.IpAddress, node.Port );
			this.siemensFetchWrite.ConnectionId = node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.siemensFetchWrite );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			siemensFetchWrite?.ConnectServer( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			siemensFetchWrite?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = siemensFetchWrite.ConnectServer( );
			if (connect.IsSuccess) siemensFetchWrite.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			siemensFetchWrite?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Siemens.Helper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensFW] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private SiemensFetchWriteNet siemensFetchWrite;               // FW协议交互对象
		private NodeSiemensFW node;                                   // 节点信息

		#endregion
	}
}
