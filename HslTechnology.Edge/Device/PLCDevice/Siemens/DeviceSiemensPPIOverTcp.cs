using HslCommunication;
using HslCommunication.Core.Address;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Siemens;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 西门子的PPI协议的PLC，使用网口透传来实现连接的
	/// </summary>
	public class DeviceSiemensPPIOverTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个西门子的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceSiemensPPIOverTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node                  = new NodeSiemensPPIOverTcp( element );
			this.siemens               = new SiemensPPIOverTcp( node.IpAddress, node.Port );
			this.siemens.Station       = node.Station;
			this.siemens.ConnectionId  = node.DTU;
			this.SetDeviceInfo( deviceResources, node, this.siemens );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			siemens?.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			siemens?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = siemens.ConnectServer( );
			if (connect.IsSuccess) siemens.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			siemens?.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			// 西门子对于bool[]只能读取，不能写入
			if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text &&
				scalarReadRequest.Length > 0)
				return AccessLevel.Read;
			return base.GetAccessLevelFromRequest( scalarReadRequest );
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Siemens.Helper.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensPPIOverTcp] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		#region Private

		private SiemensPPIOverTcp siemens;               // 核心交互对象
		private NodeSiemensPPIOverTcp node;       // 节点信息

		#endregion
	}
}
