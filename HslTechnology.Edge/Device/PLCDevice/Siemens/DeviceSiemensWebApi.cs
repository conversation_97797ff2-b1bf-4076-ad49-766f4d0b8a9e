using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Siemens;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// WebApi接口的西门子的PLC设备
	/// </summary>
	public class DeviceSiemensWebApi : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个西门子的设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceSiemensWebApi( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.nodeSiemens    = new NodeSiemensWebApi( element );
			this.siemensWebApi  = new SiemensWebApi( nodeSiemens.IpAddress, nodeSiemens.Port );
			this.SetDeviceInfo( deviceResources, nodeSiemens, this.siemensWebApi );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			siemensWebApi?.ConnectServer( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			siemensWebApi?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = siemensWebApi.ConnectServer( );
			if (connect.IsSuccess) siemensWebApi.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		protected override async Task EveryMinuteExecuteMethod( int minute )
		{
			// 每分钟重连一次服务器，一般两分钟不进行读取下，就会异常断开
			if (siemensWebApi != null) await siemensWebApi.ConnectServerAsync( );
			await base.EveryMinuteExecuteMethod( minute );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensWebApi] [{GetDeviceNameWithPath( )}] [{nodeSiemens.GetSocketInfo( )}]";

		#endregion

		#region Private

		private SiemensWebApi siemensWebApi;               // FW协议交互对象
		private NodeSiemensWebApi nodeSiemens;                        // 节点信息

		#endregion
	}
}
