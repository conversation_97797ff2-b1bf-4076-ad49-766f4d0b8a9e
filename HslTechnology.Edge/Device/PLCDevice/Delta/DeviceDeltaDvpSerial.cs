using HslCommunication;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Delta;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 台达的Dvp系列的PLC设备，使用modbus-rtu来访问
	/// </summary>
	public class DeviceDeltaDvpSerial : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个台达设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceDeltaDvpSerial( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node           = new NodeDeltaDvpSerial( element );
			this.delta          = new DeltaSerial( );
			this.delta.Series   = node.Series;
			this.delta.Station  = node.Station;
			this.SetDeviceInfo( deviceResources, node, this.delta );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => delta?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => delta?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = delta.Open( );
			if (connect.IsSuccess) delta.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => Delta.DeviceDeltaHelper.Dvp.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Delta.DeviceDeltaHelper.Dvp.CalculatePhysicalAddressFromSourceReadRequest( node.Series, sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[DeltaDvpSerial<{node.Series}>] [{GetDeviceNameWithPath( )}] [{node.GetSerialInfo( )}]";

		#endregion
		
		#region Private

		private DeltaSerial delta;                  // 核心交互对象
		private NodeDeltaDvpSerial node;            // 节点信息

		#endregion
	}
}
