using HslCommunication;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Enthernet;
using HslCommunication.MQTT;
using HslCommunication.Profinet.Delta;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// 台达的PLC设备，DVP系列，使用的是ModbusTcp协议读取
	/// </summary>
	public class DeviceDeltaDvpTcp : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个台达设备对象，从配置信息创建
		/// </summary>
		/// <param name="element">配置信息</param>
		public DeviceDeltaDvpTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node               = new NodeDeltaDvpTcp( element );
			this.delta              = new DeltaTcpNet( node.IpAddress, node.Port, node.Station );
			this.delta.ConnectionId = this.node.DTU;
			this.delta.Series       = node.Series;
			this.SetDeviceInfo( deviceResources, node, this.delta );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( ) => delta?.SetPersistentConnection( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => delta?.ConnectClose( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = delta.ConnectServer( );
			if (connect.IsSuccess) delta.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		/// <inheritdoc/>
		public override void SetDtuSession( AlienSession alienSession )
		{
			delta.ConnectServer( alienSession );
		}

		#endregion

		#region ReadWrite Access

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest ) => Delta.DeviceDeltaHelper.Dvp.GetAccessLevelFromRequest( scalarReadRequest );

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform ) => Delta.DeviceDeltaHelper.Dvp.CalculatePhysicalAddressFromSourceReadRequest( node.Series, sourceReadRequest, byteOffset, index, scalarTransform );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[DeltaDvpTcp<{node.Series}>] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion
		
		#region Private

		private DeltaTcpNet delta;                  // 核心交互对象
		private NodeDeltaDvpTcp node;               // 节点信息

		#endregion
	}
}
