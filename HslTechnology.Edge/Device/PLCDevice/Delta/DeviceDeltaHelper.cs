using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Profinet.Delta;
using HslCommunication.Profinet.Delta.Helper;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.PLCDevice.Delta
{
	internal class DeviceDeltaHelper
	{
		public class Dvp
		{
			private static OperateResult<string, int> PraseAsAddress( string address )
			{
				try
				{
					if (address.StartsWith( "SR" ) || address.StartsWith( "sr" ))
						return OperateResult.CreateSuccessResult( "SR", Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "HC" ) || address.StartsWith( "hc" ))
						return OperateResult.CreateSuccessResult( "HC", Convert.ToInt32( address.Substring( 2 ) ) );
					else if (address.StartsWith( "D" ) || address.StartsWith( "d" ))
						return OperateResult.CreateSuccessResult( "D", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "X" ) || address.StartsWith( "x" ))
						return OperateResult.CreateSuccessResult( "X", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "Y" ) || address.StartsWith( "y" ))
						return OperateResult.CreateSuccessResult( "Y", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "C" ) || address.StartsWith( "c" ))
						return OperateResult.CreateSuccessResult( "C", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "T" ) || address.StartsWith( "t" ))
						return OperateResult.CreateSuccessResult( "T", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "E" ) || address.StartsWith( "e" ))
						return OperateResult.CreateSuccessResult( "E", Convert.ToInt32( address.Substring( 1 ) ) );
					else
						return new OperateResult<string, int>( HslCommunication.StringResources.Language.NotSupportedDataType );
				}
				catch (Exception ex)
				{
					return new OperateResult<string, int>( ex.Message );
				}
			}

			private static OperateResult<string, int> PraseDvpAddress( string address )
			{
				try
				{
					if (address.StartsWith( "D" ) || address.StartsWith( "d" ))
						return OperateResult.CreateSuccessResult( "D", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "C" ) || address.StartsWith( "c" ))
						return OperateResult.CreateSuccessResult( "C", Convert.ToInt32( address.Substring( 1 ) ) );
					else if (address.StartsWith( "T" ) || address.StartsWith( "t" ))
						return OperateResult.CreateSuccessResult( "T", Convert.ToInt32( address.Substring( 1 ) ) );
					else
						return new OperateResult<string, int>( HslCommunication.StringResources.Language.NotSupportedDataType );
				}
				catch (Exception ex)
				{
					return new OperateResult<string, int>( ex.Message );
				}
			}

			public static AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
			{
				// 对于字地址的点位，无法进行写入操作
				if (scalarReadRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					if (scalarReadRequest.Address.Contains( "." ))
						return AccessLevel.Read;
				return AccessLevel.ReadWrite;
			}

			public static string CalculatePhysicalAddressFromSourceReadRequest(
				DeltaSeries series,
				SourceReadRequest sourceReadRequest,
				int byteOffset,
				int index,
				IScalarTransform scalarTransform )
			{
				// 先提取站号信息
				string station = string.Empty;
				string address = sourceReadRequest.Address;
				OperateResult<int> analysis = HslHelper.ExtractParameter( ref address, "s" );
				if (analysis.IsSuccess) station = $"s={analysis.Content};";

				// PLC原始请求的解析对bool变量，byte变量无效
				if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
					scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					return string.Empty;

				// 对于特殊的地址暂时不支持，如果不是富士的地址格式，就直接返回
				OperateResult<string, int> addressResult = series == DeltaSeries.AS ? PraseAsAddress( address ) : PraseDvpAddress( address );
				if (!addressResult.IsSuccess) return string.Empty;

				// 奇数的索引不支持写入操作
				if (index % 2 == 1) return string.Empty;
				if (byteOffset % 2 == 1) return string.Empty;

				return station + addressResult.Content1 + (addressResult.Content2 + (byteOffset + index) / 2).ToString( );
			}
		}
	}
}
