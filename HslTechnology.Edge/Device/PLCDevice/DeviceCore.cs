using HslCommunication;
using HslCommunication.Core;
using HslCommunication.LogNet;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.Threading.Tasks;
using System.Reflection;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Node;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Device.ServerDevice;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Server;
using HslTechnology.Edge.Node.Alarm;
using Newtonsoft.Json.Linq;
using HslTechnology.Edge.Resources;
using System.Linq;
using HslCommunication.Core.Pipe;
using System.Text.RegularExpressions;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Reflection;

namespace HslTechnology.Edge.Device.PLCDevice
{
	/// <summary>
	/// PLC设备的数据访问对象
	/// </summary>
	public class DeviceCore : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个PLC的数据访问的设备对象
		/// </summary>
		/// <param name="element">设备的Xml配置信息</param>
		public DeviceCore( XElement element ) : base( element )
		{
			this.deviceXml = element;
			this.DeviceNode = Edge.Reflection.EdgeReflectionHelper.CreateInstanceFrom( element );
		}

		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			if (this.pluginsDeviceObject != null)
			{
				Type deviceType = this.pluginsDeviceObject.GetType( );

				MethodInfo method = deviceType.GetMethod( "IniDevice", new Type[] { typeof( XElement ) } );
				if (method != null)
				{
					method.Invoke( this.pluginsDeviceObject, new object[] { element } );
				}
			}

		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 当前的设备的节点信息
		/// </summary>
		public DeviceNode DeviceNode { get; set; }

		/// <summary>
		/// 数据转换规则
		/// </summary>
		public IByteTransform ByteTransform { get; set; }

		/// <summary>
		/// 当前的数据读写信息
		/// </summary>
		public IReadWriteNet ReadWriteDevice 
		{
			get => this.readWriteNet;
			set
			{
				this.readWriteNet = value;
				if (this.pluginsDeviceObject != null)
				{
					MethodInfo method = this.pluginsDeviceObject.GetType( ).GetMethod( "SetDevice" );
					method?.Invoke( this.pluginsDeviceObject, new object[] { this.ReadWriteDevice } );
				}
			}
		}

		#endregion

		#region Write Support

		/// <inheritdoc/>
		public override OperateResult WriteValueByName( DeviceSingleAddressLabel addressLabel, string value )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			OperateResult write = WriteValueByScalarRequest( ReadWriteDevice, addressLabel.PhysicalAddress, addressLabel.ScalarTransform, value );
			if (write.IsSuccess)
			{
				this.UpdateDeviceOnlineStatus( true, clearMessage: true );
			}
			return write;
		}

		private MethodInfo writeByteMethod = null;
		private bool writeMethodIni = false;
		protected virtual OperateResult WriteByte( IReadWriteNet readWriteDevice, string address, byte value )
		{
			Type type = readWriteDevice.GetType( );

			if (!writeMethodIni)
			{
				writeByteMethod = type.GetMethod( "Write", new Type[] { typeof( string ), typeof( byte ) } );
				writeMethodIni = true;
			}
			if (writeByteMethod == null) return new OperateResult( "当前的设备不支持单个的字节写入操作，写入失败" );
			return (OperateResult)writeByteMethod.Invoke( readWriteDevice, new object[] { address, value } );
		}

		private OperateResult GetWriteValueHelper<T>( IScalarTransform transform, string address, T[] value, Func<string, T[], OperateResult> write )
		{
			if (value.Length <= transform.Length)
				return write( address, value );
			else
				return new OperateResult( $"写入的 [{transform.DataTypeCode}] 数组长度太长，超过原本的定义" );
		}

		/// <summary>
		/// 根据标量数据的请求内容，把传入的字符串数据，按照<see cref="IScalarTransform"/>指定的转换规则，转成对应的数据，然后写入到设备中去。
		/// </summary>
		/// <param name="readWriteDevice">设备的通信对象</param>
		/// <param name="address">地址信息</param>
		/// <param name="transform">数据转换规则</param>
		/// <param name="value">等待写入值的字符串</param>
		/// <returns>是否写入成功</returns>
		protected virtual OperateResult WriteValueByScalarRequest( IReadWriteNet readWriteDevice, string address, IScalarTransform transform, string value )
		{
			if (transform.DataTypeCode == RegularNodeTypeItem.Bool.Text)
			{
				if (transform.Length < 0) 
					return readWriteDevice.Write( address, RegularHelper.GetBoolValue( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetBoolArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)
			{
				if (transform.Length < 0)
					return WriteByte( readWriteDevice, address, RegularHelper.GetBoolValue( transform, value ) ? (byte)0x01 : (byte)0x00 );
				else
					return readWriteDevice.Write( address, RegularHelper.GetBoolArrayValue( transform, value ).Select( m => m ? (byte)0x01 : (byte)0x00 ).ToArray( ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Byte.Text)
			{
				if (transform.Length < 0)
					return WriteByte( readWriteDevice, address, RegularHelper.GetByteValue( transform, value ) );
				else
				{
					if (Regex.IsMatch( value, @"^[0-9a-fA-F ]+$" ))
					{
						return readWriteDevice.Write( address, value.ToHexBytes( ) );
					}
					else
					{
						return readWriteDevice.Write( address, value.ToStringArray<byte>( ) );
					}
				}
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
			{
				if (transform.Length < 0) return WriteByte( readWriteDevice, address, (byte)RegularHelper.GetSByteValue( transform, value ) );

				byte[] array = HslTechnologyHelper.GetByteArrayFrom( RegularHelper.GetSByteArrayValue( transform, value ) );
				return GetWriteValueHelper( transform, address, array, readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int16.Text)
			{
				if (transform.Length < 0) 
					return readWriteDevice.Write( address, RegularHelper.GetInt16Value( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetInt16ArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt16.Text)
			{
				if (transform.Length < 0) 
					return readWriteDevice.Write( address, RegularHelper.GetUInt16Value( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetUInt16ArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int32.Text)
			{
				if (transform.Length < 0)
					return readWriteDevice.Write( address, RegularHelper.GetInt32Value( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetInt32ArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt32.Text)
			{
				if (transform.Length < 0)
					return readWriteDevice.Write( address, RegularHelper.GetUInt32Value( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetUInt32ArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int64.Text)
			{
				if (transform.Length < 0)
					return readWriteDevice.Write( address, RegularHelper.GetInt64Value( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetInt64ArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
			{
				if (transform.Length < 0)
					return readWriteDevice.Write( address, RegularHelper.GetUInt64Value( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetUInt64ArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Float.Text)
			{
				if (transform.Length < 0)
					return readWriteDevice.Write( address, RegularHelper.GetFloatValue( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetFloatArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Double.Text)
			{
				if (transform.Length < 0)
					return readWriteDevice.Write( address, RegularHelper.GetDoubleValue( transform, value ) );
				else
					return GetWriteValueHelper( transform, address, RegularHelper.GetDoubleArrayValue( transform, value ), readWriteDevice.Write );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.String.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.IntOfString.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text
				)
			{
				try
				{
					if (transform is ScalarReadRequest request)   // 标量的情况
					{
						int maxLength = request.StringLength * this.DeviceNode.GetEveryAddressOccupyByte( request.Address );
						if (transform.Length < 0)
						{
							if (maxLength < 0)
								return readWriteDevice.Write( address, value, RegularScalarNode.GetEncoding( request.StringEncoding ) );
							else
								return readWriteDevice.Write( address, value, maxLength, RegularScalarNode.GetEncoding( request.StringEncoding ) );
						}
						else
						{
							byte[] buffer = new byte[transform.Length * maxLength];
							string[] strings = JArray.Parse( value ).Values<string>( ).ToArray( );
							for (int i = 0; i < strings.Length; i++)
							{
								if (i < transform.Length)
								{
									byte[] temp = RegularScalarNode.GetEncoding( request.StringEncoding ).GetBytes( strings[i] );
									SoftBasic.ArrayExpandToLength( temp, maxLength ).CopyTo( buffer, i * maxLength );
								}
								else
								{
									break;
								}
							}
							return readWriteDevice.Write( address, buffer );
						}
					}
					else if (transform is RegularScalarNode regularScalarNode )  // 结构体解析的情况
					{
						if (regularScalarNode.Length < 0)
						{
							byte[] source = regularScalarNode.GetSourceValueFromString( value, this.ByteTransform );
							if (source != null && regularScalarNode.StringSourceLengthToEven && source.Length % 2 == 1)
							{
								source = SoftBasic.SpliceArray( source, new byte[] { 0x00 } );
							}
							return readWriteDevice.Write( address, source );
						}
						else
						{
							byte[] source = regularScalarNode.GetSourceValueFromStringArray( value, this.ByteTransform );
							return readWriteDevice.Write( address, source );
						}
					}
					return readWriteDevice.Write( address, value );
				}
				catch (Exception ex)
				{
					return new OperateResult( $"当前写入操作失败，需要类型为 {RegularNodeTypeItem.String.Text} 的数据，数据转化失败！" + ex.Message );
				}
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				if (transform.Length < 0)
				{
					// 单个的数据写入操作
					OperateResult<byte[]> buffer = RegularScalarNode.GetBytesFromBCD( value, transform, this.DeviceNode );
					if (!buffer.IsSuccess) return buffer;

					return readWriteDevice.Write( address, buffer.Content );
				}
				else
				{
					// 如果是写入数组的情况
					OperateResult<byte[]> buffer = RegularScalarNode.GetBytesFromBCDArray( value, transform, this.DeviceNode );
					if (!buffer.IsSuccess) return buffer;

					return readWriteDevice.Write( address, buffer.Content );
				}
			}
			else
				return new OperateResult( $"当前的数据类型[{transform.DataTypeCode}] 暂时不支持写入操作" );
		}

		#endregion

		#region Read Support

		/// <summary>
		/// 是否使用异步的方式来进行读写访问，对于TCP通信，应该返回<c>True</c>，对于串口及UDP设备来说，应该重写，返回<c>False</c><br />
		/// Whether to use asynchronous method for read and write access, for TCP communication, it should return <c>True</c>, 
		/// for serial port and UDP devices, it should be rewritten and return <c>False</c>
		/// </summary>
		/// <returns>是否使用异步的读写接口</returns>
		protected virtual bool UseAsyncReadWrite( ) => useAsyncReadWrite;

		/// <summary>
		/// 从设备读取真实的数据信息，根据当前的标量请求内容
		/// </summary>
		/// <param name="scalarRequest">标量请求的数据内容</param>
		/// <returns>是否请求成功，并赋值到缓存的数据内容</returns>
		protected virtual async Task<OperateResult> ReadActualBoolAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<bool> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadBoolAsync( scalarRequest.Address ).ConfigureAwait( false ) : 
					ReadWriteDevice.ReadBool( scalarRequest.Address );

				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<bool[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadBoolAsync( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) : 
					ReadWriteDevice.ReadBool( scalarRequest.Address, (ushort)scalarRequest.Length );

				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualByteAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<byte[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadAsync( scalarRequest.Address, 1 ).ConfigureAwait( false ) : 
					ReadWriteDevice.Read( scalarRequest.Address, 1 );

				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, ByteTransformHelper.GetResultFromArray( read ) );
			}
			else
			{
				// 字节数组读取无视转换规则
				OperateResult<byte[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadAsync( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) : 
					ReadWriteDevice.Read( scalarRequest.Address, (ushort)scalarRequest.Length );

				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualSByteAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<byte[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadAsync( scalarRequest.Address, 1 ).ConfigureAwait( false ) : 
					ReadWriteDevice.Read( scalarRequest.Address, 1 );

				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read.IsSuccess ? OperateResult.CreateSuccessResult( (sbyte)read.Content[0] ) : OperateResult.CreateFailedResult<sbyte>( read ) );
			}
			else
			{
				OperateResult<sbyte[]> read = HslTechnologyHelper.ConvertFromByte( UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadAsync( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) : 
					ReadWriteDevice.Read( scalarRequest.Address, (ushort)scalarRequest.Length ) );

				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualInt16Async( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<short> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadInt16Async( scalarRequest.Address ).ConfigureAwait( false ) : 
					ReadWriteDevice.ReadInt16( scalarRequest.Address );

				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<short[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadInt16Async( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) : 
					ReadWriteDevice.ReadInt16( scalarRequest.Address, (ushort)scalarRequest.Length );

				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualUInt16Async( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<ushort> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadUInt16Async( scalarRequest.Address ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadUInt16( scalarRequest.Address );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<ushort[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadUInt16Async( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadUInt16( scalarRequest.Address, (ushort)scalarRequest.Length );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualInt32Async( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<int> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadInt32Async( scalarRequest.Address ).ConfigureAwait( false ) :
					 ReadWriteDevice.ReadInt32( scalarRequest.Address );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<int[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadInt32Async( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadInt32( scalarRequest.Address, (ushort)scalarRequest.Length );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualUInt32Async( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<uint> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadUInt32Async( scalarRequest.Address ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadUInt32( scalarRequest.Address );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<uint[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadUInt32Async( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadUInt32( scalarRequest.Address, (ushort)scalarRequest.Length );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualInt64Async( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<long> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadInt64Async( scalarRequest.Address ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadInt64( scalarRequest.Address );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<long[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadInt64Async( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadInt64( scalarRequest.Address, (ushort)scalarRequest.Length );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualUInt64Async( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<ulong> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadUInt64Async( scalarRequest.Address ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadUInt64( scalarRequest.Address );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<ulong[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadUInt64Async( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadUInt64( scalarRequest.Address, (ushort)scalarRequest.Length );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualFloatAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<float> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadFloatAsync( scalarRequest.Address ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadFloat( scalarRequest.Address );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<float[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadFloatAsync( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadFloat( scalarRequest.Address, (ushort)scalarRequest.Length );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualDoubleAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				OperateResult<double> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadDoubleAsync( scalarRequest.Address ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadDouble( scalarRequest.Address );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				OperateResult<double[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadDoubleAsync( scalarRequest.Address, (ushort)scalarRequest.Length ).ConfigureAwait( false ) :
					ReadWriteDevice.ReadDouble( scalarRequest.Address, (ushort)scalarRequest.Length );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		private async Task<OperateResult> ScalarReadRequestStringAsync( ScalarReadRequest scalarRequest )
		{
			// 先读取字节数组，然后解析出实际的字符串数据信息
			Encoding encoding = RegularScalarNode.GetEncoding( scalarRequest.StringEncoding );
			if (scalarRequest.Length < 0)
			{
				OperateResult<byte[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadAsync( scalarRequest.Address, (ushort)scalarRequest.StringLength ).ConfigureAwait( false ) :
					ReadWriteDevice.Read( scalarRequest.Address, (ushort)scalarRequest.StringLength );
				if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateFailedResult<string>( read ) );

				string value = this.ByteTransform == null ? encoding.GetString( read.Content ) :
					this.ByteTransform.TransString( read.Content, 0, read.Content.Length, encoding );

				if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToInt64( value ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToDouble( value ) ) );
				else
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( value ) );
			}
			else
			{
				OperateResult<byte[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadAsync( scalarRequest.Address, (ushort)(scalarRequest.StringLength * scalarRequest.Length) ).ConfigureAwait( false ) :
					ReadWriteDevice.Read( scalarRequest.Address, (ushort)(scalarRequest.StringLength * scalarRequest.Length) );
				if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateFailedResult<string[]>( read ) );

				string[] values = new string[scalarRequest.Length];
				int everyLength = read.Content.Length / scalarRequest.Length;
				for (int i = 0; i < scalarRequest.Length; i++)
				{
					values[i] = this.ByteTransform == null ? encoding.GetString( read.Content, i * everyLength, everyLength ) : this.ByteTransform.TransString( read.Content, i * everyLength, everyLength, encoding );
				}

				if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values.Select( m => Convert.ToInt64( m ) ).ToArray( ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values.Select( m => Convert.ToDouble( m ) ).ToArray( ) ) );
				else
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values ) );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			return await ScalarReadRequestStringAsync( scalarRequest ).ConfigureAwait( false );
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualIntOfStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			return await ScalarReadRequestStringAsync( scalarRequest ).ConfigureAwait( false );
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualDoubleOfStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			return await ScalarReadRequestStringAsync( scalarRequest ).ConfigureAwait( false );
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualBCDAsync( ScalarReadRequest scalarRequest )
		{
			if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				// 读取byte
				OperateResult<byte[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadAsync( scalarRequest.Address, (ushort)scalarRequest.StringLength ).ConfigureAwait( false ) :
					ReadWriteDevice.Read( scalarRequest.Address, (ushort)scalarRequest.StringLength );
				if (!read.IsSuccess) return DealWithReadResult( OperateResult.CreateFailedResult<string>( read ), null );

				// 赋值缓冲区
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( RegularScalarNode.GetBCDValue( read.Content, scalarRequest.BCDFormat ) ) );
			}
			else
			{
				// 读取byte
				OperateResult<byte[]> read = UseAsyncReadWrite( ) ? await ReadWriteDevice.ReadAsync( scalarRequest.Address, (ushort)(scalarRequest.StringLength * scalarRequest.Length )).ConfigureAwait( false ) :
					ReadWriteDevice.Read( scalarRequest.Address, (ushort)(scalarRequest.StringLength * scalarRequest.Length) );
				if (!read.IsSuccess) return DealWithReadResult( OperateResult.CreateFailedResult<string>( read ), null );

				int everyLength = scalarRequest.Length == 0 ? read.Content.Length : (read.Content.Length / scalarRequest.Length);
				string[] values = new string[scalarRequest.Length];
				for (int i = 0; i < scalarRequest.Length; i++)
				{
					byte[] buffer = read.Content.SelectMiddle( i * everyLength, everyLength );
					values[i] = RegularScalarNode.GetBCDValue( buffer, scalarRequest.BCDFormat );
				}
				// 赋值缓冲区
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values ) );
			}
		}

		/// <summary>
		/// 读取原始字节的请求
		/// </summary>
		/// <param name="sourceReadRequest">原始的字节的请求</param>
		/// <returns>请求结果信息</returns>
		public virtual async Task<OperateResult<byte[]>> ReadActualSourceRequest( SourceReadRequest sourceReadRequest )
		{
			if (sourceReadRequest.Address.Contains( ";" ) && sourceReadRequest.Length.Contains( ";" ))
			{
				try
				{
					string[] address = sourceReadRequest.Address.Split( new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries );
					ushort[] length  = sourceReadRequest.Length.Split(  new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries ).Select( m => ushort.Parse( m ) ).ToArray( );

					OperateResult<byte[]> read = await ReadBatch( address, length );
					return DealWithSourceReadResult( read, content => ParseFromRequest( content, sourceReadRequest, ByteTransform ) );
				}
				catch (Exception ex)
				{
					this.edgeResources.FatalMessage.AddMessage( SoftBasic.GetExceptionMessage( ex ) );
					return new OperateResult<byte[]>( $"SourceRead Length[{ sourceReadRequest.Length }] formate wrong: " + ex.Message );
				}
			}
			else
			{
				if (ushort.TryParse( sourceReadRequest.Length, out ushort length ))
				{
					try
					{
						OperateResult<byte[]> read = await this.ReadWriteDevice.ReadAsync( sourceReadRequest.Address, length ).ConfigureAwait( false );
						return DealWithSourceReadResult( read, content => ParseFromRequest( content, sourceReadRequest, ByteTransform ) );
					}
					catch( Exception ex)
					{
						this.edgeResources.FatalMessage.AddMessage( SoftBasic.GetExceptionMessage( ex ) );
						return new OperateResult<byte[]>( "SourceRead Request Length wrong: " + ex.Message );
					}
				}
				else
					return new OperateResult<byte[]>( "SourceRead Request Length formate wrong: " + sourceReadRequest.Length );
			}
		}
			
		/// <summary>
		/// 批量读取地址的原始字节信息，如果有些设备支持，则可以重写支持
		/// </summary>
		/// <param name="address">地址数组信息</param>
		/// <param name="length">长度数组信息</param>
		/// <returns>返回的原始字节数据信息</returns>
		protected virtual Task<OperateResult<byte[]>> ReadBatch( string[] address, ushort[] length )
		{
			return Task.FromResult( new OperateResult<byte[]>( "Not Supported Request" ) );
		}

		/// <inheritdoc/>
		protected override async Task<OperateResult> ReadActualAsync( RequestBase request )
		{
			if (request.RequestType == RequestType.ScalarRead)
			{
				try
				{
					ScalarReadRequest scalarRequest = (ScalarReadRequest)request;
					if      (scalarRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)              return await ReadActualBoolAsync(           scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)        return await ReadActualBoolAsync(           scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Byte.Text)              return await ReadActualByteAsync(           scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.SByte.Text)             return await ReadActualSByteAsync(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int16.Text)             return await ReadActualInt16Async(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt16.Text)            return await ReadActualUInt16Async(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int32.Text)             return await ReadActualInt32Async(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt32.Text)            return await ReadActualUInt32Async(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int64.Text)             return await ReadActualInt64Async(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt64.Text)            return await ReadActualUInt64Async(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Float.Text)             return await ReadActualFloatAsync(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Double.Text)            return await ReadActualDoubleAsync(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.String.Text)            return await ReadActualStringAsync(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)       return await ReadActualIntOfStringAsync(    scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)    return await ReadActualDoubleOfStringAsync( scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.BCD.Text)               return await ReadActualBCDAsync(            scalarRequest ).ConfigureAwait( false );
					else return new OperateResult( "Not implementation type: " + scalarRequest.DataTypeCode );
				}
				catch(Exception ex)
				{
					// 算是发生了重大异常，记录系统的异常消息里
					this.edgeResources.FatalMessage.AddMessage( SoftBasic.GetExceptionMessage( ex ) );
					return new OperateResult( "ReadActualAsync failed: " + ex.Message );
				}
			}
			else if (request.RequestType == RequestType.SourceRead)
			{
				if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );

				SourceReadRequest sourceReadRequest = (SourceReadRequest)request;
				OperateResult<byte[]> read = null;
				if (sourceReadRequest.Address.StartsWith( "var=" ) && !sourceReadRequest.Address.Contains( ";" ))
				{
					// 如果节点来自网关的其他节点
					read = this.edgeResources.EdgeServices.GetSourceRequestCahce( sourceReadRequest.Address.Substring( 4 ) );
					DealWithSourceReadResult( read, content => ParseFromRequest( content, sourceReadRequest, ByteTransform ) );
				}
				else
				{
					read = await ReadActualSourceRequest( sourceReadRequest );
				}

				lock (this.localSourceRequestCache)
				{
					if (this.localSourceRequestCache.ContainsKey(request.Name))
						this.localSourceRequestCache[request.Name] = read;
					else
						this.localSourceRequestCache.Add(request.Name, read);
				}
				return read;
			}
			else
			{
				return new OperateResult( "Not Supported Request" );
			}
		}

		/// <inheritdoc/>
		protected async override Task<OperateResult> WriteActualAsync( ScalarWriteRequest request, string value )
		{
			if (request.RequestType == RequestType.WriteInterval)
			{
				try
				{
					if (this.ReadWriteDevice == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
					bool isArray = value.StartsWith( "[" ) && value.EndsWith( "]" );
					if (request.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					{
						if (!isArray)
							return UseAsyncReadWrite( ) ? await ReadWriteDevice.WriteAsync( request.Address, bool.Parse( value ) ).ConfigureAwait( false ) : ReadWriteDevice.Write( request.Address, bool.Parse( value ) );
						else
							return UseAsyncReadWrite( ) ? await ReadWriteDevice.WriteAsync( request.Address, RegularHelper.GetBoolArrayValue( transform: null, value ) ).ConfigureAwait( false ) : ReadWriteDevice.Write( request.Address, RegularHelper.GetBoolArrayValue( transform: null, value ) );

					}
					else if (request.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)
					{
						if (!isArray)
							return WriteByte( ReadWriteDevice, request.Address, RegularHelper.GetBoolValue( transform: null, value ) ? (byte)0x01 : (byte)0x00 );
						else
							return ReadWriteDevice.Write( request.Address, RegularHelper.GetBoolArrayValue( transform: null, value ).Select( m => m ? (byte)0x01 : (byte)0x00 ).ToArray( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Byte.Text)
					{
						if (!request.IsArray)
							return WriteByte( ReadWriteDevice, request.Address, byte.Parse( value ) );
						else
							return ReadWriteDevice.Write( request.Address, value.ToHexBytes( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					{
						if (!isArray) return WriteByte( ReadWriteDevice, request.Address, (byte)sbyte.Parse( value ) );

						ReadWriteDevice.Write( request.Address, HslTechnologyHelper.GetByteArrayFrom( value.ToStringArray<sbyte>( ) ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Int16.Text)
					{
						if (!isArray) return ReadWriteDevice.Write( request.Address, short.Parse( value ) );
						else return ReadWriteDevice.Write( request.Address, value.ToStringArray<short>( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.UInt16.Text)
					{
						if (!isArray) return ReadWriteDevice.Write( request.Address, ushort.Parse( value ) );
						else return ReadWriteDevice.Write( request.Address, value.ToStringArray<ushort>( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Int32.Text)
					{
						if (!isArray) return ReadWriteDevice.Write( request.Address, int.Parse( value ) );
						else return ReadWriteDevice.Write( request.Address, value.ToStringArray<int>( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.UInt32.Text)
					{
						if (!isArray) return ReadWriteDevice.Write( request.Address, uint.Parse( value ) );
						else return ReadWriteDevice.Write( request.Address, value.ToStringArray<uint>( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Int64.Text)
					{
						if (!isArray) return ReadWriteDevice.Write( request.Address, long.Parse( value ) );
						else return ReadWriteDevice.Write( request.Address, value.ToStringArray<long>( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
					{
						if (!isArray) return ReadWriteDevice.Write( request.Address, ulong.Parse( value ) );
						else return ReadWriteDevice.Write( request.Address, value.ToStringArray<ulong>( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Float.Text)
					{
						if (!isArray) return ReadWriteDevice.Write( request.Address, float.Parse( value ) );
						else return ReadWriteDevice.Write( request.Address, value.ToStringArray<float>( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Double.Text)
					{
						if (!isArray) return ReadWriteDevice.Write( request.Address, double.Parse( value ) );
						else return ReadWriteDevice.Write( request.Address, value.ToStringArray<double>( ) );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.BCD.Text)
					{
						byte[] buffer = RegularScalarNode.GetBytesFromBCD( value, BCDFormat.C8421 );
						return ReadWriteDevice.Write( request.Address, buffer );
					}
					else if (RegularNodeTypeItem.IsDataTypeString( request.DataTypeCode ))
					{
						return ReadWriteDevice.Write( request.Address, value );
					}
					else return new OperateResult( "Not implementation type: " + request.DataTypeCode );
				}
				catch(Exception ex)
				{
					return new OperateResult( ex.Message );
				}
			}
			return await base.WriteActualAsync( request, value );
		}

		#endregion

		#region Protect SetDeviceInfo

		/// <summary>
		/// 将设备的基本信息赋值给<see cref="DeviceCore"/>对象
		/// </summary>
		/// <param name="edgeResources">设备关联的额外的资源信息</param>
		/// <param name="node">设备的节点配置信息</param>
		/// <param name="device">实际的设备对象</param>
		protected void SetDeviceInfo( EdgeDeviceResources edgeResources, DeviceNodeSerial node, HslCommunication.Serial.SerialDeviceBase device )
		{
			EdgePipeSerial pipeSerial = edgeResources?.GetPipeSerial( this.deviceXml );
			// 串口信息
			if (pipeSerial != null && pipeSerial.Node != null) node.SetNodeSerialPipe( pipeSerial.Node );

			device.ReceiveTimeout = node.ReceiveTimeOut;
			if (!string.IsNullOrEmpty( node.PortName ))
			{
				if (edgeResources != null)
					node.PortName = edgeResources.EdgeSettings.PortMapping.GetSourcePort( node.PortName );
				else
					node.PortName = node.PortName;
				device.SerialPortInni( sp =>
				{
					sp.PortName  = node.PortName;
					sp.BaudRate  = node.BaudRate;
					sp.DataBits  = node.DataBits;
					sp.StopBits  = node.StopBits;
					sp.Parity    = node.Parity;
					sp.RtsEnable = node.RtsEnable;
				} );
			}

			// 设备基本信息
			this.ByteTransform          = device.ByteTransform;
			this.UniqueId               = device.ConnectionId;
			this.ReadWriteDevice        = device;
			this.useAsyncReadWrite      = false;
			this.ReadWriteDevice.LogNet = new LogNetSingle( string.Empty );
			this.ReadWriteDevice.LogNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;
			if (node.SleepTime > 0)
			{
				device.SleepTime = node.SleepTime;
			}

			// 如果配置了管道信息，就设置管道信息
			if (edgeResources != null)
			{
				if (pipeSerial != null && pipeSerial.PipeSerial != null) device.SetPipeSerial( pipeSerial.PipeSerial );
				this.RegisterRpcService( edgeResources.MqttServer, edgeResources.HttpServer, node, device );
			}
			this.pipeBase = device.GetPipeSerial( );
		}

		/// <inheritdoc cref="SetDeviceInfo(EdgeDeviceResources, DeviceNodeSerial, HslCommunication.Serial.SerialDeviceBase)"/>
		protected void SetDeviceInfo( EdgeDeviceResources edgeResources, DeviceNodeNet node, HslCommunication.Core.Net.NetworkDeviceBase device )
		{
			this.networkDeviceBase = device;
			this.deviceNodeNet = node;

			EdgePipeSocket pipeSocket = edgeResources?.GetPipeSocket( this.deviceXml );
			// 网口管道信息
			if (pipeSocket != null && pipeSocket.Node != null) node.SetNodeSocketPipe( pipeSocket.Node );

			// 网络信息
			device.IpAddress      = node.IpAddress;
			device.Port           = node.Port;
			device.ReceiveTimeOut = node.ReceiveTimeOut;
			device.ConnectTimeOut = node.ConnectTimeOut;
			if (!string.IsNullOrEmpty(node.HexBeforeSend))
				device.SendBeforeHex = node.HexBeforeSend;

			// 设备基本信息
			this.ByteTransform    = device.ByteTransform;
			this.UniqueId         = device.ConnectionId;
			this.ReadWriteDevice  = device;
			this.ReadWriteDevice.LogNet = new LogNetSingle( string.Empty );
			this.ReadWriteDevice.LogNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;

			// 如果配置了管道信息，就设置管道信息
			if (edgeResources != null)
			{
				if (pipeSocket != null && pipeSocket.PipeSocket != null) device.SetPipeSocket( pipeSocket.PipeSocket );
				this.RegisterRpcService( edgeResources.MqttServer, edgeResources.HttpServer, node, device );
			}
			this.pipeBase = device.GetPipeSocket( );
		}

		/// <inheritdoc cref="SetDeviceInfo(EdgeDeviceResources, DeviceNodeSerial, HslCommunication.Serial.SerialDeviceBase)"/>
		protected void SetDeviceInfo( EdgeDeviceResources edgeResources, DeviceNodeNet node, HslCommunication.Core.Net.NetworkWebApiDevice device )
		{
			// 网络信息
			device.IpAddress     = node.IpAddress;
			device.Port          = node.Port;
			// 设备基本信息
			this.ByteTransform   = device.ByteTransform;
			this.UniqueId        = device.ConnectionId;
			this.ReadWriteDevice = device;
			this.ReadWriteDevice.LogNet = new LogNetSingle( string.Empty );
			this.ReadWriteDevice.LogNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;
			if (edgeResources != null)
				this.RegisterRpcService( edgeResources.MqttServer, edgeResources.HttpServer, node, device );
		}

		/// <inheritdoc cref="SetDeviceInfo(EdgeDeviceResources, DeviceNodeSerial, HslCommunication.Serial.SerialDeviceBase)"/>
		protected void SetDeviceInfo( EdgeDeviceResources edgeResources, DeviceNodeNet node, HslCommunication.Core.Net.NetworkUdpDeviceBase device )
		{
			// 网络信息
			device.IpAddress       = node.IpAddress;
			device.Port            = node.Port;
			device.ReceiveTimeout  = node.ReceiveTimeOut;

			// 设备基本信息
			this.ByteTransform     = device.ByteTransform;
			this.UniqueId          = device.ConnectionId;
			this.ReadWriteDevice   = device;
			this.useAsyncReadWrite = false;
			this.ReadWriteDevice.LogNet = new LogNetSingle( string.Empty );
			this.ReadWriteDevice.LogNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;
			if (edgeResources != null)
				this.RegisterRpcService( edgeResources.MqttServer, edgeResources.HttpServer, node, device );

			this.pipeBase = device.GetPipeSocket( );
		}

		/// <inheritdoc cref="SetDeviceInfo(EdgeDeviceResources, DeviceNodeSerial, HslCommunication.Serial.SerialDeviceBase)"/>
		protected void SetDeviceInfo( EdgeDeviceResources edgeResources, ServerNode node, HslCommunication.Core.Net.NetworkDataServerBase device )
		{
			// 网络信息
			device.Port           = node.Port;
			device.ActiveTimeSpan = TimeSpan.FromSeconds( node.ActiveTime );

			// 设备基本信息
			this.ByteTransform    = device.ByteTransform;
			this.UniqueId         = device.ConnectionId;
			this.ReadWriteDevice  = device;
			this.useAsyncReadWrite = false;
			this.ReadWriteDevice.LogNet = new LogNetSingle( string.Empty );
			this.ReadWriteDevice.LogNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;
			if (edgeResources != null)
				this.RegisterRpcService( edgeResources.MqttServer, edgeResources.HttpServer, node, device );
		}

		private void LogNet_BeforeSaveToFile( object sender, HslEventArgs e )
		{
			// 通信电文的日志消息
			this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), e.HslMessage.ToString( ) );
		}

		/// <inheritdoc/>
		public override PipeBase GetPipeLock( )
		{
			return this.pipeBase;
		}

		/// <inheritdoc/>
		protected override bool RedundantAfterPipeWrong( )
		{
			if (networkDeviceBase != null && deviceNodeNet != null)
			{
				if (string.IsNullOrEmpty( deviceNodeNet.RedundantIpAddress )) return false;  // 没有切换

				if (deviceNodeNetIpIndex == 0)
				{
					this.deviceNodeNet.SetRedundantIp( useRedundantIp: true );
					this.deviceNodeNetIpIndex = 1;
					this.networkDeviceBase.IpAddress = deviceNodeNet.RedundantIpAddress;
					this.LogNet?.WriteInfo( GetDeviceNameWithPath( ), this.ToString( ), $"Redundant IpAddress [{deviceNodeNet.IpAddress}] -> [{deviceNodeNet.RedundantIpAddress}]" );
				}
				else
				{
					this.deviceNodeNet.SetRedundantIp( useRedundantIp: false );
					this.deviceNodeNetIpIndex = 0;
					this.networkDeviceBase.IpAddress = deviceNodeNet.IpAddress;
					this.LogNet?.WriteInfo( GetDeviceNameWithPath( ), this.ToString( ), $"Origin IpAddress [{deviceNodeNet.RedundantIpAddress}] -> [{deviceNodeNet.IpAddress}]" );
				}
				return true;
			}
			return false;
		}

		#endregion

		#region Plugins Support

		private object pluginsDeviceObject = null;

		/// <inheritdoc cref="DeviceCoreBase.SetJsonValue(string, dynamic, bool)"/>
		private void SetJsonValueHelper( string name, object value, bool isArray )
		{
			SetJsonValue( name, value, isArray );
		}

		/// <summary>
		/// 设置当前的扩展的插件设备对象，用来扩展方法接口，定时执行方法的
		/// </summary>
		/// <param name="device"></param>
		public void SetPluginsDeviceObject( object device )
		{
			this.pluginsDeviceObject = device;
			this.dictRpcApiInfos = EdgeReflectionHelper.GetMethodByDeviceObject( device, out this.methodRpcInfosBuffer );

			// 设置JSON数据的
			Type type = device.GetType( );
			PropertyInfo propertySet = type.GetProperty( "SetJsonValue" );
			if (propertySet != null && propertySet.CanWrite && propertySet.PropertyType == typeof( Action<string, object, bool> ))
			{
				propertySet.SetValue( device, new Action<string, object, bool>( this.SetJsonValueHelper ) );
			}

			// 绑定关联的定时执行的方法
			this.everySecondsExecuteMethod = EdgeReflectionHelper.ExecuteTimerMethod( type, device, "EverySecondsExecuteMethod" );
			this.everyMinuteExecuteMethod = EdgeReflectionHelper.ExecuteTimerMethod( type, device, "EveryMinuteExecuteMethod" );
			this.everyHourExecuteMethod = EdgeReflectionHelper.ExecuteTimerMethod( type, device, "EveryHourExecuteMethod" );
			this.everyDayExecuteMethod = EdgeReflectionHelper.ExecuteTimerMethod( type, device, "EveryDayExecuteMethod" );
		}

		/// <inheritdoc/>
		protected override XElement GetBuildInRequestRescources( )
		{
			if (this.pluginsDeviceObject == null) return base.GetBuildInRequestRescources( );

			MethodInfo method = this.pluginsDeviceObject.GetType( ).GetMethod( "BuildInRequestRescources" );
			if (method != null && method.ReturnType == typeof( XElement ))
			{
				return (XElement)method.Invoke( this.pluginsDeviceObject, null );
			}
			return base.GetBuildInRequestRescources( );
		}


		// 定时
		private Func<int, Task> everySecondsExecuteMethod = null;
		private Func<int, Task> everyMinuteExecuteMethod = null;
		private Func<int, Task> everyHourExecuteMethod = null;
		private Func<int, Task> everyDayExecuteMethod = null;

		/// <inheritdoc/>
		protected override async Task EverySecondsExecuteMethod( int second )
		{
			if (this.everySecondsExecuteMethod != null)
			{
				await this.everySecondsExecuteMethod( second );
			}
			await base.EverySecondsExecuteMethod( second );
		}

		/// <inheritdoc/>
		protected override async Task EveryMinuteExecuteMethod( int minute )
		{
			if (this.everyMinuteExecuteMethod != null)
			{
				await this.everyMinuteExecuteMethod( minute );
			}
			await base.EveryMinuteExecuteMethod( minute );
		}

		/// <inheritdoc/>
		protected override async Task EveryHourExecuteMethod( int hour )
		{
			if (this.everyHourExecuteMethod != null)
			{
				await this.everyHourExecuteMethod( hour );
			}
			await base.EveryHourExecuteMethod( hour );
		}

		/// <inheritdoc/>
		protected override async Task EveryDayExecuteMethod( int day )
		{
			if (this.everyDayExecuteMethod != null)
			{
				await this.everyDayExecuteMethod( day );
			}
			await base.EveryDayExecuteMethod( day );
		}


		#endregion

		#region Private Member

		private int deviceNodeNetIpIndex = 0;
		private DeviceNodeNet deviceNodeNet;
		private NetworkDeviceBase networkDeviceBase;
		private IReadWriteNet readWriteNet;

		private bool useAsyncReadWrite = true;
		private int addressBytesLength = 1;
		private XElement deviceXml;
		private PipeBase pipeBase = null;

		#endregion
	}
}
