using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device
{
	/// <summary>
	/// 设备相关的辅助类
	/// </summary>
	public class DeviceHelper
	{
		/// <summary>
		/// 根据设备的xml配置元素，找到当前的设备的原始路径，该路径不包括网关的名称，不包括设备的根目录信息<br />
		/// According to the xml configuration element of the device, find the original path of the current device, 
		/// the path does not include the name of the gateway, and does not include the root directory information of the device
		/// </summary>
		/// <param name="element">设备的xml配置信息</param>
		/// <param name="forcePipePath">强制把管道标记为路径信息，在创建管道资源时，可以有效防止资源重名问题</param>
		/// <returns>路径的字符串数组</returns>
		public static string[] GetXmlPath( XElement element, bool forcePipePath = false )
		{
			List<string> paths = new List<string>( );
			while (true)
			{
				if (element != null)
				{
					if(element.Name == GroupNode.RootSettings || element.Attribute( nameof( GroupNode.Name ) ) == null)
					{
						// 移除最后一个获得的配置Devices信息
						paths.RemoveAt( paths.Count - 1 );
						break;
					}
					if (element.Name == nameof( NodeType.GroupSerialPipe ))
					{
						// 串口管道
						if (new NodeSerialPipe( element ).UseAsGroupNode || forcePipePath)
						{
							paths.Add( element.Attribute( nameof( GroupNode.Name ) ).Value );
						}
					}
					else if (element.Name == nameof( NodeType.GroupSocketPipe ))
					{
						// 网口管道
						if (new NodeSocketPipe( element ).UseAsGroupNode || forcePipePath)
						{
							paths.Add( element.Attribute( nameof( GroupNode.Name ) ).Value );
						}
					}
					else if (element.Name == nameof( NodeType.GroupSingleThread ))
					{
						// 单线程管道
						if (new NodeSingleThread( element ).UseAsGroupNode || forcePipePath)
						{
							paths.Add( element.Attribute( nameof( GroupNode.Name ) ).Value );
						}
					}
					else
					{
						paths.Add( element.Attribute( nameof( GroupNode.Name ) ).Value );
					}
					element = element.Parent;
				}
				else
				{
					break;
				}
			}
			paths.Reverse( );
			return paths.ToArray( );
		}

		/// <summary>
		/// 获取唯一的路径名称
		/// </summary>
		/// <param name="element">设备元素信息</param>
		/// <param name="forcePipePath">强制把管道标记为路径信息，在创建管道资源时，可以有效防止资源重名问题</param>
		/// <returns>设备唯一的路径信息</returns>
		public static string GetUniquePath( XElement element, bool forcePipePath = false )
		{
			if (element == null) return null;
			string[] path = GetXmlPath( element, forcePipePath );
			StringBuilder sb = new StringBuilder( );
			for (int i = 0; i < path.Length; i++)
			{
				sb.Append( path[i] );
				if (i < path.Length - 1) sb.Append( HslTechnologyExtension.DeviceDefaultSplit );
			}
			return sb.ToString( );
		}

		/// <summary>
		/// 获取当前xml资源里的模板设备信息
		/// </summary>
		/// <param name="element">XML的基本信息</param>
		/// <returns>资源数据内容</returns>
		public static Dictionary<string, XElement> GetDeviceTemplate( XElement element )
		{
			Dictionary<string, XElement> template = new Dictionary<string, XElement>( );
			foreach (var xmlNode in element.Elements( ))
			{
				if (xmlNode.Attribute( nameof( GroupNode.Name ) ).Value == GroupNode.RootTemplate)
				{
					foreach (var templateXml in xmlNode.Elements())
					{
						if (templateXml.Attribute( nameof( GroupNode.Name ) ) != null)
						{
							template.Add( templateXml.Attribute( nameof( GroupNode.Name ) ).Value, templateXml );
						}
					}
					break;
				}
			}
			return template;
		}
	}
}
