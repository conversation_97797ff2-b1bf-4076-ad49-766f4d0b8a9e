using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device
{
	/// <summary>
	/// 网关的socket管道信息
	/// </summary>
	public class EdgePipeSocket : IEdgePipe
	{
		/// <summary>
		/// 实例化一个网口管道对象信息
		/// </summary>
		/// <param name="node">节点定义信息</param>
		/// <param name="pipeSocket">网口的通信对象信息</param>
		public EdgePipeSocket( NodeSocketPipe node, PipeSocket pipeSocket )
		{
			this.Node = node;
			this.PipeSocket = pipeSocket;
		}

		/// <summary>
		/// 管道的节点信息
		/// </summary>
		public NodeSocketPipe Node { get; set; }

		/// <summary>
		/// Socket管道对象
		/// </summary>
		public PipeSocket PipeSocket { get; set; }

		/// <inheritdoc/>
		public void ClosePipe( )
		{

		}

		/// <inheritdoc/>
		public PipeBase GetPipe( ) => PipeSocket;
	}
}
