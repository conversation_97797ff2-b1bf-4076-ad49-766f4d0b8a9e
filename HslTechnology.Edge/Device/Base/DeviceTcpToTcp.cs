using HslCommunication;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Enthernet;
using HslCommunication.LogNet;
using Newtonsoft.Json.Linq;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// TCP转TCP的设备实现
	/// </summary>
	public class DeviceTcpToTcp : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceTcpToTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node                               = new NodeTcpToTcp( element );
			this.tcpForward                         = new TcpForward( this.node.ServerPort, node.IpAddress, node.Port );
			this.tcpForward.LogMsgFormatBinary      = this.node.LogMsgFormatBinary;
			this.tcpForward.OnRemoteMessageReceived += TcpForward_OnRemoteMessageReceived;
			this.tcpForward.OnClientMessageReceive  += TcpForward_OnClientMessageReceive;
			this.tcpForward.SocketKeepAliveTime = 30_000;

			this.tcpForward.LogNet              = new LogNetSingle( string.Empty );
			this.tcpForward.LogNet.BeforeSaveToFile += LogNet_BeforeSaveToFile;
		}

		protected override void UpdateDeviceActiveTimeWhemNoneRequest( )
		{
			this.SetJsonObjectValue( "__activeTime", this.activeTime.ToEdgeString( ) );
		}

		private void TcpForward_OnClientMessageReceive( ForwardSession session, byte[] data )
		{
			if (data != null) this.tcpToDeviceCount += data.Length;
			this.RequestSuccessCount++;
			this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                    // 更新设备的活动时间
		}

		private void TcpForward_OnRemoteMessageReceived( ForwardSession session, byte[] data )
		{
			if (data != null) this.deviceToTcpCount += data.Length; 
			this.RequestSuccessCount++;
			this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                    // 更新设备的活动时间
		}

		private void LogNet_BeforeSaveToFile( object sender, HslEventArgs e )
		{
			// 通信电文的日志消息
			this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), e.HslMessage.ToString( ) );
		}

		#endregion

		#region Override Method


		private int secondsTick = 59;

		/// <inheritdoc/>
		protected async override Task EverySecondsExecuteMethod( int second )
		{
			this.SetJsonValue( _onlines,     this.tcpForward.OnlineSessionsCount, false );
			this.SetJsonValue( _deviceToTcp, this.deviceToTcpCount,               false );
			this.SetJsonValue( _tcpToDevice, this.tcpToDeviceCount,               false );
			this.SetJsonValue( "__success",  this.RequestSuccessCount,            false );                 // 成功请求次数

			secondsTick++;
			if (secondsTick > 60) secondsTick = 1;

			// 网络正常，30s ping一次，网络不正常，10s ping一次
			if (this.node.PingEnable && (this.pingFailedCount == 0 && secondsTick % 30 == 0) || (this.pingFailedCount != 0 && secondsTick % 10 == 0))
			{
				if (this.node != null && !string.IsNullOrEmpty( this.node.IpAddress ))
				{
					// 每隔30秒。ping一下设备方
					try
					{
						PingReply reply = await ping.SendPingAsync( this.node.IpAddress );
						if (reply.Status != IPStatus.Success)
						{
							this.pingFailedCount++;
							this.UpdateDeviceOnlineStatus( false );
						}
						else
						{
							this.pingSuccessCount++;
							this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
							this.pingFailedCount = 0;

							this.UpdateDeviceOnlineStatus( true );
						}
					}
					catch
					{
						this.pingFailedCount++;
						this.UpdateDeviceOnlineStatus( false );
					}
				}
			}

		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			ping = new Ping( );

			try
			{
				this.tcpForward.ServerStart( );
				this.SetDeviceForceMessgae( "NET", string.Empty );
			}
			catch (Exception ex)
			{
				// 网口启动失败
				string msg = $"网口 [{this.node.ServerPort}] 服务端口启动失败：{ex.Message}";
				this.SetDeviceForceMessgae( "NET", msg );
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
			}

			this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
			this.SetJsonValue( _deviceToTcp, this.deviceToTcpCount, false );
			this.SetJsonValue( _tcpToDevice, this.tcpToDeviceCount, false );
		}


		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _onlines,
				Description = "当前的转换会话数量",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _deviceToTcp,
				Description = "设备到网口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _tcpToDevice,
				Description = "网口到设备的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = "PingSuccess",
				Description = "Ping客户端的成功次数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );

			base.AnalysisByBussiness( business );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[TcpToTcp] [{GetDeviceNameWithPath( )}] [{node.IpAddress},{node.Port}->{node.ServerPort}]";

		#endregion

		#region Private Member

		private DateTime activeTime = HslTechnologyHelper.GetDateTimeNow( );
		private TcpForward tcpForward;                           // TCP转TCP的通信对象
		private Ping ping;                                       // 针对客户端的ping操作
		private long pingFailedCount = 0;                        // ping错误的次数
		private long pingSuccessCount = 0;                       // ping成功的次数
		private long deviceToTcpCount = 0;                       // 串口到网口的报文字节数
		private long tcpToDeviceCount = 0;                       // 网口到串口的报文字节数

		#endregion

		private NodeTcpToTcp node;
		private const string _deviceToTcp = "Device->Tcp";
		private const string _tcpToDevice = "Tcp->Device";
		private const string _onlines = "OnlineCount";
	}
}
