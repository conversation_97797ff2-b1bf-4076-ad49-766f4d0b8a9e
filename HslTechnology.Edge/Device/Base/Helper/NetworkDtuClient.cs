using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;

namespace HslTechnology.Edge.Device.Base.Helper
{
	/// <summary>
	/// DTU的客户端信息
	/// </summary>
	public class NetworkDtuClient : NetworkBase
	{

		private byte[] CreateHslAlienMessage( string dtuId, string password )
		{
			if (dtuId.Length > 11) dtuId = dtuId.Substring( 11 );
			byte[] sendBytes = new byte[28];
			sendBytes[0] = 0x48;
			sendBytes[1] = 0x73;
			sendBytes[2] = 0x6E;
			sendBytes[3] = 0x00;
			sendBytes[4] = 0x17;

			if (dtuId.Length > 11) dtuId = dtuId.Substring( 0, 11 );
			Encoding.ASCII.GetBytes( dtuId ).CopyTo( sendBytes, 5 );

			if (!string.IsNullOrEmpty( password ))
			{
				if (password.Length > 6) password = password.Substring( 6 );
				Encoding.ASCII.GetBytes( password ).CopyTo( sendBytes, 16 );
			}

			return sendBytes;
		}

		/// <summary>
		/// 连接设备的节点信息
		/// </summary>
		/// <param name="node">节点信息</param>
		/// <returns>是否连接成</returns>
		public async Task<OperateResult<Socket>> ConnectDeviveNetNode( DeviceNodeNet node )
		{
			return await CreateSocketAndConnectAsync( node.IpAddress, node.Port, node.ConnectTimeOut );
		}

		/// <summary>
		/// 创建连接DTU的对象信息
		/// </summary>
		/// <param name="node">DTU的节点信息</param>
		/// <returns>连接成功的DTU对象</returns>
		public async Task<OperateResult<Socket>> ConnectHslAlientClient( INodeDtu node )
		{
			byte[] sendBytes = string.IsNullOrEmpty( node.DtuRegistrationPackage ) ? CreateHslAlienMessage( node.DtuId, node.DtuPassword ) : node.DtuRegistrationPackage.ToHexBytes( );

			// 创建连接
			OperateResult<Socket> connect = await CreateSocketAndConnectAsync( node.DtuIpAddress, node.DtuPort, 10000 );
			if (!connect.IsSuccess) return connect;

			// 发送数据
			OperateResult send = await SendAsync( connect.Content, sendBytes );
			if (!send.IsSuccess) return OperateResult.CreateFailedResult<Socket>( send );

			// 如果不需要反馈数据，则直接返回成功
			if (!node.DtuResponseAck) return OperateResult.CreateSuccessResult( connect.Content );

			// 接收数据
			if (string.IsNullOrEmpty( node.DtuRegistrationPackage ))
			{
				OperateResult<byte[]> receive = await ReceiveByMessageAsync( connect.Content, 10000, new AlienMessage( ) );
				if (!receive.IsSuccess) return OperateResult.CreateFailedResult<Socket>( receive );

				switch (receive.Content[5])
				{
					case 0x00:
						{
							break;
						}
					case 0x01:
						{
							connect.Content?.Close( );
							return new OperateResult<Socket>( StringResources.Language.DeviceCurrentIsLoginRepeat );
						}
					case 0x02:
						{
							connect.Content?.Close( );
							return new OperateResult<Socket>( StringResources.Language.DeviceCurrentIsLoginForbidden );
						}
					case 0x03:
						{
							connect.Content?.Close( );
							return new OperateResult<Socket>( StringResources.Language.PasswordCheckFailed );
						}
					default:
						{
							connect.Content?.Close( );
							return new OperateResult<Socket>( StringResources.Language.UnknownError );
						}
				}
			}
			else
			{
				OperateResult<byte[]> receive = await ReceiveByMessageAsync( connect.Content, 10000, null );
				if (!receive.IsSuccess) return OperateResult.CreateFailedResult<Socket>( receive );
			}

			return OperateResult.CreateSuccessResult( connect.Content );
		}



	}
}
