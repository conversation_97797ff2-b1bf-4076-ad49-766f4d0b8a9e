using HslCommunication.Core.Net;
using HslCommunication.MQTT;
using HslCommunication;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Device.Base.Helper;
using System.IO.Ports;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Config;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// TCP端口转远程的Mqtt服务器数据
	/// </summary>
	public class DeviceTcpToRemoteMqtt : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceTcpToRemoteMqtt( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			this.networkDtu = new NetworkDtuClient( );
			this.node = new NodeTcpToRemoteMqtt( element );
		}

		#endregion

		#region Override Method

		protected override void UpdateDeviceActiveTimeWhemNoneRequest( )
		{
			this.SetJsonObjectValue( "__activeTime", this.activeTime.ToEdgeString( ) );
		}

		private int secondsTick = 59;

		/// <inheritdoc/>
		protected async override Task EverySecondsExecuteMethod( int second )
		{
			this.SetJsonValue( _tcpToMqtt, this.tcpToMqttCount, false );
			this.SetJsonValue( _mqttToTcp, this.mqttToTcpCount, false );
			this.SetJsonValue( "__success", this.RequestSuccessCount, false );                 // 成功请求次数
			secondsTick++;
			if (secondsTick > 60) secondsTick = 1;


			if (this.mqttClient != null)
			{
				if (this.mqttClient.IsConnected && this.mqttConnectedFirst)
				{
					this.SetDeviceForceMessgae( _mqtt, string.Empty );
					this.SetJsonValue( _mqtt, $"MQTT[{this.node.MqttIpAddress}:{this.node.MqttPort}] Connected success", false );
				}
				else
				{
					string msg = $"MQTT[{this.node.MqttIpAddress}:{this.node.MqttPort}] Connected failed";
					this.SetJsonValue( _mqtt, msg, false );
					this.SetDeviceForceMessgae( _mqtt, msg );
					if (!this.mqttConnectedFirst)
					{
						// 如此第一次没有连接上去，这里就不停的重复尝试连接
						this.mqttConnectedFirst = this.mqttClient.ConnectServer( ).IsSuccess;
					}
				}
			}

			// 如果发现本地未连接，则尝试连接
			if (this.localSocket == null)
			{
				OperateResult connect = await ConnectLocalServer( );
				if (connect.IsSuccess)
				{
					this.SetDeviceForceMessgae( _local, string.Empty );
				}
				else
				{
					this.SetDeviceForceMessgae( _local, connect.Message );
				}
			}

			// 网络正常，30s ping一次，网络不正常，10s ping一次
			if (this.node.PingEnable && (this.pingFailedCount == 0 && secondsTick % 30 == 0) || (this.pingFailedCount != 0 && secondsTick % 10 == 0))
			{
				if (this.localSocket != null)
				{
					// 每隔30秒。ping一下设备的IP地址
					try
					{
						PingReply reply = await ping.SendPingAsync( this.node.IpAddress );
						if (reply.Status != IPStatus.Success)
						{
							this.pingFailedCount++;
						}
						else
						{
							this.pingSuccessCount++;
							this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
							this.pingFailedCount = 0;
						}
					}
					catch
					{
						this.pingFailedCount++;
					}

					if (this.pingFailedCount > 3)
					{
						NetworkLocalOffline( );
					}
				}
			}

			// 网口没有错误的情况下
			if (string.IsNullOrEmpty( this.deviceMessage.GetDefaultStringMsg( ) ))
				UpdateDeviceOnlineStatus( true );
			else
				UpdateDeviceOnlineStatus( false );

			// 更新设备的活动时间
			this.SetJsonObjectValue( "__activeTime", new JValue( DateTime.Now.ToEdgeString( ) ) );
			await base.EverySecondsExecuteMethod( second );
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			ping = new Ping( );

			try
			{
				MqttConnectionOptions mqttConnectionOptions = new MqttConnectionOptions( )
				{
					IpAddress = this.node.MqttIpAddress,
					Port = this.node.MqttPort,
					ConnectTimeout = this.node.MqttConnectTimeOut,
				};
				if (!string.IsNullOrEmpty( this.node.UserName ))
				{
					mqttConnectionOptions.Credentials = new MqttCredential( this.node.UserName, this.node.Password );
				}
				mqttConnectionOptions.UseRSAProvider = this.node.UseRSA;
				mqttConnectionOptions.ClientId = string.IsNullOrEmpty( this.node.ClientID ) ? GetDeviceNameWithPath( ) : this.node.ClientID;

				this.mqttClient = new MqttClient( mqttConnectionOptions );
				this.mqttClient.OnClientConnected += MqttClient_OnClientConnected;
				this.mqttClient.OnMqttMessageReceived += MqttClient_OnMqttMessageReceived;

				OperateResult connect = this.mqttClient.ConnectServer( );
				this.mqttConnectedFirst = connect.IsSuccess;
			}
			catch (Exception ex)
			{
				// MQTT启动失败怎么办，继续执行
				string msg = $"Mqtt[{this.node.MqttIpAddress}:{this.node.MqttPort}] failed: {ex.Message}";
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				this.SetJsonValue( _mqtt, msg, false );
				this.SetDeviceForceMessgae( _mqtt, msg );
			}


			this.SetJsonValue( "PingSuccess",  this.pingSuccessCount, false );
			this.SetJsonValue( _tcpToMqtt,     this.tcpToMqttCount,   false );
			this.SetJsonValue( _mqttToTcp,     this.mqttToTcpCount,   false );
			this.SetJsonValue( _topic_publish, this.node.WriteTopic,  false );
			this.SetJsonValue( _topic_sub,     this.node.ReadTopic,   false );
		}

		private void MqttClient_OnMqttMessageReceived( MqttClient client, MqttApplicationMessage message )
		{
			// 从MQTT发布的原始报文，然后发回给本地的TCP连接
			string topic = message.Topic;
			byte[] payload = message.Payload;
			this.mqttToTcpCount += payload.Length;
			this.RequestSuccessCount++;

			this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _mqttToTcp, payload, this.node.LogMsgFormatBinary );
			this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                    // 更新设备的活动时间

			try
			{
				// 发送到本地的端口去
				if (payload != null && this.localSocket != null)
				{
					localSocket.Send( payload, 0, payload.Length, SocketFlags.None );
				}
			}
			catch
			{
				// 记录日志
				NetworkLocalOffline( );
			}
		}

		private void MqttClient_OnClientConnected( MqttClient client )
		{
			client.SubscribeMessage( this.node.ReadTopic );
		}

		private async Task<OperateResult> ConnectLocalServer( )
		{
			// 进行连接的操作
			OperateResult<Socket> create = await networkDtu.ConnectDeviveNetNode( this.node );
			if (!create.IsSuccess)
			{
				this.SetJsonValue( _local, $"Local[{this.node.IpAddress}:{this.node.Port}] Connect failed", false );
				string msg = $"Connect [{this.node.GetSocketInfo( )}] failed: " + create.Message;
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), GetDeviceNameWithPath( ), msg );
				return new OperateResult( msg );
			}
			else
			{
				try
				{
					create.Content.BeginReceive( serverBuffer, 0, serverBuffer.Length, SocketFlags.None, new AsyncCallback( ServerSocketReveiceCallBack ), create.Content );
					this.localSocket = create.Content;
					this.SetJsonValue( _local, $"Local[{this.node.IpAddress}:{this.node.Port}] Success", false );
					this.LogNet?.WriteInfo( GetDeviceNameWithPath( ), GetDeviceNameWithPath( ), $"Connect [{this.node.GetSocketInfo( )}] success! " );
					return OperateResult.CreateSuccessResult( );
				}
				catch (Exception ex)
				{
					create.Content?.Close( );
					string msg = $"Local[{this.node.IpAddress}:{this.node.Port}] Connect failed: " + ex.Message;
					this.SetJsonValue( _local, msg, false );
					this.LogNet?.WriteError( GetDeviceNameWithPath( ), GetDeviceNameWithPath( ), $"Connect [{this.node.GetSocketInfo( )}] failed: " + msg );
					return new OperateResult( msg );
				}
			}
		}

		private void ServerSocketReveiceCallBack( IAsyncResult ar )
		{
			if (ar.AsyncState is Socket socket)
			{
				byte[] data = null;
				try
				{
					int length = socket.EndReceive( ar );

					if (length == 0)
					{
						NetworkLocalOffline( );
						return;
					};

					data = serverBuffer.SelectBegin( length );
				}
				catch
				{
					NetworkLocalOffline( );
					return;
				}

				// 数据发送到MQTT
				this.RequestSuccessCount++;
				this.tcpToMqttCount += data.Length;
				this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _tcpToMqtt, data, this.node.LogMsgFormatBinary );
				this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                // 更新设备的活动时间
				this.mqttClient?.PublishMessage( new MqttApplicationMessage( )
				{
					Topic = this.node.WriteTopic,
					Payload = data,
				} );          // 将数据发布到MQTT


				try
				{
					socket.BeginReceive( serverBuffer, 0, serverBuffer.Length, SocketFlags.None, new AsyncCallback( ServerSocketReveiceCallBack ), socket );
				}
				catch
				{
					NetworkLocalOffline( );
					return;
				}

			}
		}

		private void NetworkLocalOffline( )
		{
			this.localSocket?.Close( );
			this.localSocket = null;
			this.SetJsonValue( _local, $"[{this.node.IpAddress}:{this.node.Port}] Closed", false );
		}

		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			// 添加几个只读节点数据信息
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _mqtt,
				Description = "MQTT状态信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _local,
				Description = "连接的本地设备的状态信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _tcpToMqtt,
				Description = "网口到MQTT的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _mqttToTcp,
				Description = "MQTT到网口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = "PingSuccess",
				Description = "Ping客户端的成功次数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _topic_publish,
				Description = "MQTT发布的主题，网关从串口接收数据后，发布该主题的数据到MQTT",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _topic_sub,
				Description = "MQTT订阅的主题，网关订阅该主题，收到MQTT数据，然后转发给串口",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );

			base.AnalysisByBussiness( business );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[TcpToMqtt] [{GetDeviceNameWithPath( )}] [{node.IpAddress}:{node.Port}]";

		#endregion

		#region Private Member

		private DateTime activeTime = HslTechnologyHelper.GetDateTimeNow( );
		private Socket localSocket = null;                       // 连接服务器的socket

		private Ping ping;                                       // 针对客户端的ping操作
		private long pingFailedCount = 0;                        // ping错误的次数
		private long pingSuccessCount = 0;                       // ping成功的次数
		private long tcpToMqttCount = 0;                         // 串口到网口的报文字节数
		private long mqttToTcpCount = 0;                         // 网口到串口的报文字节数
		private NetworkDtuClient networkDtu;                     // DTU的辅助客户端
		private MqttClient mqttClient;
		private bool mqttConnectedFirst = false;                  // MQTT是否发生了一次的连接成功

		private byte[] serverBuffer = new byte[2048];            // 服务器的数据缓冲

		#endregion

		private NodeTcpToRemoteMqtt node;
		private const string _local = "Local";
		private const string _mqtt = "MQTT";
		private const string _tcpToMqtt = "Tcp->Mqtt";
		private const string _mqttToTcp = "Mqtt->Tcp";
		private const string _topic_publish = "Publish-Topic";
		private const string _topic_sub = "Sub-Topic";

	}
}
