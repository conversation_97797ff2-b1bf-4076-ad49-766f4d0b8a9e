using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 设备线程循环部分的执行返回枚举信息
	/// </summary>
	public enum ThreadCycleRunReturn
	{
		/// <summary>
		/// 无任何状态的情况，继续循环
		/// </summary>
		None,

		/// <summary>
		/// 当前循环体进行了请求操作
		/// </summary>
		Request,

		/// <summary>
		/// 直接进行继续循环操作
		/// </summary>
		Continue,

		/// <summary>
		/// 跳出循环的操作
		/// </summary>
		Break,
	}
}
