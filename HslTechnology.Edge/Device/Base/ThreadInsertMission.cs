using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 线程插入的执行任务，可能是读取任务，也可能是写入任务
	/// </summary>
	public class ThreadInsertMission
	{
		/// <summary>
		/// 结果对象
		/// </summary>
		public object Result { get; set; }

		/// <summary>
		/// 当前的任务操作
		/// </summary>
		public Func<DeviceCoreBase, object> DeviceOpreate { get; set; }

		/// <summary>
		/// 当前任务是否执行完毕
		/// </summary>
		public bool Finished { get; set; }

		/// <summary>
		/// 超时时间，以秒为单位，默认120秒
		/// </summary>
		public int TimeoutSeconds { get; set; } = 120;
	}
}
