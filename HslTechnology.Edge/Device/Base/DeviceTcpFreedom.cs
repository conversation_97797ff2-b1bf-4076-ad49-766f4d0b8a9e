using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 基于自由的TCP通信的协议信息
	/// </summary>
	public class DeviceTcpFreedom : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceTcpFreedom( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeTcpFreedom( element );
			this.deviceTcp = new DeviceTcp( this.node.UseServerActivePush, this.node );
			if (this.node.ByteTransformType == Node.Core.ByteTransformType.LittleEndianOrder)
			{
				this.deviceTcp.ByteTransform = new RegularByteTransform( );
			}
			else if (this.node.ByteTransformType == Node.Core.ByteTransformType.BigEndianOrder)
			{
				this.deviceTcp.ByteTransform = new ReverseBytesTransform( );
			}
			else
			{
				this.deviceTcp.ByteTransform = new ReverseWordTransform( DataFormat.CDAB );
			}

			this.SetDeviceInfo( deviceResources, this.node, this.deviceTcp );
			this.deviceTcp.OnMessageReceive += DeviceTcp_OnMessageReceive;
			//this.node.PortName = deviceResources.PortMappingConfig.GetSourcePort( this.node.PortName );
		}

		private void DeviceTcp_OnMessageReceive( object sender, OperateResult<byte[]> e )
		{
			// 收到了数据
			if (e.IsSuccess)
			{

			}
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			if (this.node.UseServerActivePush == false && this.node.UsePersistentConnection)
				this.deviceTcp.SetPersistentConnection( );
		}

		/// <inheritdoc/>
		protected override Task EverySecondsExecuteMethod( int second )
		{
			// 在异步的模式下，需要定时检测网络的连接状态
			if (this.node.UseServerActivePush)
			{
				if (this.deviceTcp.GetPipeSocket( ).IsSocketError)
				{
					// 网络异常，需要重连一下

				}
			}
			return base.EverySecondsExecuteMethod( second );
		}

		#endregion

		#region Object Override

		public override string ToString( )
		{
			return $"TcpFreedom[{this.node.IpAddress}:{this.node.Port}]";
		}

		#endregion

		private DeviceTcp deviceTcp;
		private NodeTcpFreedom node;
	}


	class DeviceTcp : NetworkDeviceBase
	{
		public DeviceTcp( bool useServerActivePush, NodeTcpFreedom node )
		{
			this.UseServerActivePush = useServerActivePush;
			this.node = node;
		}

		/// <inheritdoc/>
		public override OperateResult<byte[]> Read( string address, ushort length )
		{
			// 如果 address 是 01 03 00 C8 00 02 45 F5<split/>01 03 00 64 00 01 C5 D5       就自动切割成两个报文分别交互
			string[] messages = address.Split( new string[] { splits }, StringSplitOptions.RemoveEmptyEntries );
			for (int i = 0; i < messages.Length; i++)
			{
				string msg = messages[i];
				// 输入报文信息，请求报文内容
				int startIndex = HslHelper.ExtractParameter( ref msg, "stx", 0x00 );

				byte[] send = this.node.UseAsciiFormate ? SoftBasic.GetFromAsciiStringRender( msg ) : msg.ToHexBytes( );
				OperateResult<byte[]> read = ReadFromCoreServer( send );
				if (!read.IsSuccess) return read;


				if (i >= messages.Length - 1)
				{
					if (startIndex >= read.Content.Length) return new OperateResult<byte[]>( StringResources.Language.ReceiveDataLengthTooShort );
					if (startIndex != 0x00)
						return OperateResult.CreateSuccessResult( read.Content.RemoveBegin( startIndex ) );
					else
						return read;
				}
			}
			return new OperateResult<byte[]>( "address failed: " + address );
		}

		/// <inheritdoc/>
		public override async Task<OperateResult<byte[]>> ReadAsync( string address, ushort length )
		{
			// 如果 address 是 01 03 00 C8 00 02 45 F5<split/>01 03 00 64 00 01 C5 D5       就自动切割成两个报文分别交互
			string[] messages = address.Split( new string[] { splits }, StringSplitOptions.RemoveEmptyEntries );
			for (int i = 0; i < messages.Length; i++)
			{
				string msg = messages[i];
				// 输入报文信息，请求报文内容
				int startIndex = HslHelper.ExtractParameter( ref msg, "stx", 0x00 );

				byte[] send = this.node.UseAsciiFormate ? SoftBasic.GetFromAsciiStringRender( msg ) : msg.ToHexBytes( );
				OperateResult<byte[]> read = await ReadFromCoreServerAsync( send );
				if (!read.IsSuccess) return read;


				if (i >= messages.Length - 1)
				{
					if (startIndex >= read.Content.Length) return new OperateResult<byte[]>( StringResources.Language.ReceiveDataLengthTooShort );
					if (startIndex != 0x00)
						return OperateResult.CreateSuccessResult( read.Content.RemoveBegin( startIndex ) );
					else
						return read;
				}
			}
			return new OperateResult<byte[]>( "address failed: " + address );
		}

		/// <inheritdoc/>
		public async override Task<OperateResult> WriteAsync( string address, byte[] value )
		{
			// 输入报文信息，请求报文内容
			HslHelper.ExtractParameter( ref address, "stx", 0x00 );

			byte[] send = this.node.UseAsciiFormate ? SoftBasic.GetFromAsciiStringRender( address ) : address.ToHexBytes( );
			OperateResult<byte[]> read = await ReadFromCoreServerAsync( send );
			if (!read.IsSuccess) return read;

			return read;
		}

		/// <inheritdoc/>
		protected override INetMessage GetNewNetMessage( )
		{
			if (this.node == null) return null;
			if (this.node.MessageType == Node.Core.MessageStructType.None) return null;
			if (this.node.MessageType == Node.Core.MessageStructType.FixedLength) return new TcpFreedomMessage( this.node, this.ByteTransform );
			if (this.node.MessageType == Node.Core.MessageStructType.VariableLength) return new TcpFreedomMessage( this.node, this.ByteTransform );
			if (this.node.MessageType == Node.Core.MessageStructType.SpecifiesEndChar)
			{
				byte[] buffer = this.node.EndHexString.ToHexBytes( );
				if (buffer.Length == 0) return null;

				if (buffer.Length == 1) return new SpecifiedCharacterMessage( buffer[0] );
				return new SpecifiedCharacterMessage( buffer[0], buffer[1] );
			}
			return base.GetNewNetMessage( );
		}

		/// <inheritdoc/>
		protected override bool DecideWhetherQAMessage( Socket socket, OperateResult<byte[]> receive )
		{
			if (this.UseServerActivePush)
			{
				OnMessageReceive?.Invoke( this, receive );
				return false;
			}
			return base.DecideWhetherQAMessage( socket, receive );
		}

		/// <summary>
		/// 当接收到远程服务器主动推送的消息的事件
		/// </summary>
		public event EventHandler<OperateResult<byte[]>> OnMessageReceive;

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"TcpFreedom[{this.node.IpAddress}:{this.node.Port}]";

		#endregion


		private NodeTcpFreedom node;
		private string splits = "<split>";
	}

	public class TcpFreedomMessage : NetMessageBase, INetMessage
	{
		public TcpFreedomMessage( NodeTcpFreedom node, IByteTransform byteTransform )
		{
			this.node = node;
			this.byteTransform = byteTransform;
		}

		private NodeTcpFreedom node;

		public int ProtocolHeadBytesLength
		{
			get 
			{
				return this.node.FixLength;
			}
		}

		public int GetContentLengthByHeadBytes( )
		{
			if (this.node.MessageType == Node.Core.MessageStructType.FixedLength) return 0;
			if (this.node.MessageType == Node.Core.MessageStructType.VariableLength)
			{
				return Convert.ToInt32( this.node.MessageRegularNode.GetValue( HeadBytes, 0, this.byteTransform, true ) );
			}
			return 0;
		}


		private IByteTransform byteTransform;
	}
}
