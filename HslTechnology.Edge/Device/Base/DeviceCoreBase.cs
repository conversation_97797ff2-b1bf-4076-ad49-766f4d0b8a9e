using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Enthernet;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Device;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.DataBusiness.Alarm;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Node.Alarm;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Reflection;
using HslCommunication.Reflection;
using System.Reflection;
using HslTechnology.Edge.Node.Method;
using HslTechnology.Edge.Node.Oee;
using HslTechnology.Edge.Resources;
using HslCommunication.Core.Pipe;
using System.Text.RegularExpressions;
using HslTechnology.Edge.DataBusiness.Database;
using System.Linq;
using HslTechnology.Edge.DataBusiness.Log;
using HslTechnology.Edge.Node.Core;
using System.Diagnostics;
using System.Dynamic;
using DynamicExpresso;
using System.Net.NetworkInformation;
using System.Collections.Concurrent;
using CsvHelper;
using System.Globalization;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 设备交互的核心类对象，主要定义了设备的请求逻辑
	/// </summary>
	public class DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 使用默认的无参构造方法
		/// </summary>
		/// <param name="element">设备的XML参数信息</param>
		public DeviceCoreBase( XElement element )
		{
			this.DeviceNodes             = new string[0];
			XAttribute dtuAttr = element.Attribute( "DTU" );
			if (dtuAttr != null && !string.IsNullOrEmpty( dtuAttr.Value )) this.dtu = dtuAttr.Value; // 表明这是一个DTU的设备
			this.useAsciiFormate = GroupNode.GetXmlValue( element, nameof( INodeAsciiFormate.UseAsciiFormate ), useAsciiFormate, bool.Parse );
			this.requestIntervalSleep = GroupNode.GetXmlValue( element, nameof( DeviceNode.RequestIntervalSleep ), 0, int.Parse );
			if (this.requestIntervalSleep > 30_000) this.requestIntervalSleep = 30_000;


			this.deviceMessage                       = new StringGather<string>( );                  // 实例化设备的消息对象
			this.deviceThreadState                   = new DeviceThreadState( );
			this.deviceThreadState.FuncExecuteByTime = TimeCheckExcute;
			this.autoResetQuit                       = new AutoResetEvent( false );
			this.deviceData                          = new DeviceData( );
			this.Name                                = element.Attribute( nameof( GroupNode.Name ) ).Value;
			this.StatusType                          = GroupNode.GetXmlEnum( element, nameof( DeviceNode.StatusType ), this.StatusType );
			this.dictDeviceSingleAddress             = new Dictionary<string, DeviceSingleAddressLabel>( );
			this.localSourceRequestCache             = new Dictionary<string, OperateResult<byte[]>>( );
			this.dictRpcApiInfos                     = EdgeReflectionHelper.GetMethodByDeviceObject( this, out this.methodRpcInfosBuffer );
			this.DeviceNodes                         = DeviceHelper.GetXmlPath( element );
			this.threadInsertMissions                = new ConcurrentQueue<ThreadInsertMission>( );

			// 拿到当前设备准备上传MQTT的主题的前置信息，相当于可以自由设置设备上传的主题，而与当前的路径无关
			XAttribute topicAttr = element.Attribute( nameof( DeviceNode.PreTopic ) );
			if (topicAttr != null) this.mqttTopicPre = topicAttr.Value;

			// 设置当前设备的备注信息
			this.SetNameDescriptionJsonValue( new GroupNode( element ) );
			
			// 调整为外部调用实现
			//try
			//{
			//	IniDevice( element );
			//}
			//catch (Exception ex)
			//{
			//	SetDeviceForceMessgae( EdgeStringResource.StringGather_DeviceIni, EdgeStringResource.DeviceIniFailed + ex.Message );
			//}
		}

		/// <summary>
		/// 使用一个错误的设备消息来实例化一个错误的设备对象
		/// </summary>
		/// <param name="element">设备的元素</param>
		/// <param name="deviceMessage">错误信息</param>
		public DeviceCoreBase( XElement element, string deviceMessage ) : this( element)
		{
			SetDeviceForceMessgae( EdgeStringResource.StringGather_DeviceIni, deviceMessage );
		}

		/// <summary>
		/// 加载设备的
		/// </summary>
		/// <param name="element"></param>
		public void LoadDeviceXmlContent( XElement element )
		{
			// 查询当前设备有没有绑定什么模板信息
			if (element.Attribute( nameof( DeviceNode.Template ) ) != null)
			{
				string template = element.Attribute( nameof( DeviceNode.Template ) ).Value;
				if (!string.IsNullOrEmpty( template ) && BusinessEngine.Business.Templates.ContainsKey( template ))
				{
					this.LoadRequest( BusinessEngine.Business.Templates[template], null );
				}
			}

			// 检查是否有内置的资源信息
			XElement buildIn = GetBuildInRequestRescources( );
			if (buildIn != null)
				this.LoadRequest( buildIn, null );

			this.LoadRequest( element, null );
		}

		/// <summary>
		/// 根据配置的设备参数信息（也就是XML文件），来初始化当前的设备信息，例如实际设备的实例化
		/// </summary>
		/// <remarks>
		/// 使用本方法初始设备的数据信息时，将会对错误异常进行捕获操作。不至于整个网关系统奔溃
		/// </remarks>
		/// <param name="element">设备的XML参数信息，如果为NULL，表示没有</param>
		/// <param name="deviceResources">设备关联的其他资源信息，主要是从外部获得的资源信息</param>
		public virtual void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			this.edgeResources = deviceResources;
		}

		/// <summary>
		/// 获取内置的资源信息，可以是各种请求，也可以是报警，结构体的内置资源信息
		/// </summary>
		/// <returns>包含各种资源的元素</returns>
		protected virtual XElement GetBuildInRequestRescources( )
		{
			return null;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 获取当前的工作线程信息
		/// </summary>
		public Thread WorkThread => this.thread;

		/// <summary>
		/// 设备的节点分布的信息点，举例 工厂一,车间二,设备A
		/// </summary>
		public string[] DeviceNodes { get; set; }

		/// <summary>
		/// 唯一的识别码，方便异形客户端寻找对应的处理逻辑
		/// </summary>
		public string UniqueId { get; set; }

		/// <summary>
		/// 获取或设置当前设备的名称，该名称基于XML配置文档实现的
		/// </summary>
		public string Name { get; private set; }

		/// <summary>
		/// 获取当前的设备状态信息，该状态基于XML配置文档来实现的
		/// </summary>
		public DeviceStatusType StatusType { get; set; } = DeviceStatusType.OnWork;

		/// <summary>
		/// 请求成功的次数统计
		/// </summary>
		public long RequestSuccessCount { get; protected set; }

		/// <summary>
		/// 请求失败的次数统计
		/// </summary>
		public long RequestFailedCount { get; protected set; }

		/// <summary>
		/// 指示设备是否正常的状态，如果是true，就是代表设备正常在线，false是读取失败，多半是因为设备网络掉线，或是关机了
		/// </summary>
		public bool DeviceStatus => this.deviceStatus != 0;

		/// <summary>
		/// 当前线程的活动时间，用来判定线程是否处于假死状态
		/// </summary>
		public DateTime ThreadActiveTime 
		{
			get
			{
				// 如果由外部的线程控制的话，则返回外部线程的控制信息
				if (this.CurrentThreadControlsEnable == false)
					return this.deviceThreadExecute.ThreadActiveTime;
				return this.deviceThreadState.ThreadActiveTime;
			}
		}

		/// <summary>
		/// 获取本设备所有的数据的缓存，是一个JSON格式的数据缓存信息
		/// </summary>
		public string JsonData { get => deviceData.DeviceJsonTemp; }

		/// <summary>
		/// 获取本设备所有的属性数据
		/// </summary>
		public JObject JsonObject { get => deviceData.DeviceJsonClone; }

		/// <summary>
		/// 获取本设备的所用由于数据公开的节点信息，比如配置了温度，压力，速度等等
		/// </summary>
		public ScalarDataNode[] DataNodes { get => this.scalarDataNodes; }

		/// <summary>
		/// 获取或设置当前的日志对象
		/// </summary>
		public virtual EdgeLog LogNet { get => logNet; set => logNet = value; }

		/// <summary>
		/// 获取当前的设备的DTU信息
		/// </summary>
		public string DTU => this.dtu;

		/// <summary>
		/// 获取当前的请求是否处于暂停中
		/// </summary>
		public bool RequestEnable => this.allRequestEnbale;

		/// <summary>
		/// 获取或设置当前的线程调度控制是否激活，如果为<c>False</c>，则由外部进行线程的调度控制
		/// </summary>
		public bool CurrentThreadControlsEnable => this.deviceThreadExecute == null;

		/// <summary>
		/// 设置当前的线程控制权
		/// </summary>
		/// <param name="deviceThreadExecute">线程控制信息</param>
		public void SetOtherThreadControl( DeviceThreadExecuteControl deviceThreadExecute )
		{
			this.deviceThreadExecute = deviceThreadExecute;
		}

		/// <summary>
		/// 处理MQTT服务器订阅到的Topic数据信息，其中topic就为设备的唯一ID信息
		/// </summary>
		/// <param name="path">数据的路径信息</param>
		/// <param name="payload">数据负载信息</param>
		/// <param name="payload2">基于字符串的负载数据信息</param>
		public virtual OperateResult DealWithPublishTopic( string[] path, byte[] payload, string payload2 )
		{
			// 正常进行反写数据的操作
			if (payload == null && payload2 == null) return new OperateResult( $"Publish Topic[{path[this.DeviceNodes.Length]}] failed: 当前反写的数据为空" );

			if (path.Length == this.DeviceNodes.Length)
			{
				string msg = $"Publish Topic[{path[this.DeviceNodes.Length]}] failed: 不能发布设备本身的主题数据";
				logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				return new OperateResult( msg: msg );
			}
			OperateResult write = null;
			if (payload == null)
			{
				write = WriteValueByName( path[this.DeviceNodes.Length], payload2, changeThread: true );
			}
			else
			{
				write = WriteValueByName( path[this.DeviceNodes.Length], Encoding.UTF8.GetString( payload ), changeThread: true );
			}
			if (!write.IsSuccess)
			{
				string msg = $"Dealwith Publish Topic[{path[this.DeviceNodes.Length]}] failed: " + write.Message;
				logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				return new OperateResult( msg: msg );
			}
			return write;
		}

		/// <summary>
		/// 设置一个值，标记当前的是否在读取失败的情况下，重置数据标签为空
		/// </summary>
		/// <param name="value">状态标记</param>
		public void SetResetNullIfFailed( bool value )
		{
			this.resetNullIfFaield = value;
		}

		#region Analysis Bussiness

		/// <summary>
		/// 将当前的设备信息添加到业务逻辑分析，需要传入业务处理的核心引擎以及上传的配置参数信息
		/// </summary>
		/// <param name="business">业务分析引擎</param>
		/// <param name="UploadInfo">上传的配置信息</param>
		public virtual void AnalysisByBussiness( BusinessEngine business )
		{
			// 从网关资源里拿取上传的参数配置信息
			UploadInfoConfig uploadInfo = this.edgeResources.EdgeSettings.UploadInfo;
			// 用于客户端显示网关设备有什么数据名，数据类型，是否读写的功能
			List<ScalarDataNode> dataNodes = new List<ScalarDataNode>( );
			this.advanceAnalysis = business.AddDevice( GetDeviceNameWithPath( ) );
			foreach (RequestBase request in Requests)
			{
				if (request.DeviceActual == null) request.DeviceActual = this;                // 如果没有关联其他的设备，则关联当前的设备
				if (request.RequestType == RequestType.ScalarRead || request.RequestType == RequestType.ScalarCache)
				{
					if (request is ScalarReadRequest scalarReadRequest)
					{
						// 业务分析->报警，OEE
						business.PrepareDealAlarm( scalarReadRequest, GetAlarmNodeFrom( scalarReadRequest ), GetDeviceNameWithPath( ), tagName: null );
						business.PrepareDealOee(   scalarReadRequest, GetOeeNodeFrom(   scalarReadRequest ), GetDeviceNameWithPath( ) );
						// 添加进客户端展示的数据节点信息
						dataNodes.Add( ScalarDataNode.ParseFrom( scalarReadRequest, GetAccessLevelFromRequest( scalarReadRequest ) ) );
						// 添加进数据标签信息里
						AddDeviceSingleAddress( new DeviceSingleAddressLabel( scalarReadRequest ), uploadInfo.EnableSubscription );

					}

					// 如果是缓存的数据节点信息，则先进行一次初始化的数据写入操作
					if (request.RequestType == RequestType.ScalarCache && request is ScalarCacheRequest scalarCacheRequest)
					{
						OperateResult _ = ReadScalarCache( scalarCacheRequest );
					}
				}
				else if (request.RequestType == RequestType.SourceRead)
				{
					if (request is SourceReadRequest sourceReadRequest)
					{
						foreach (RegularScalarNode regularScalarNode in sourceReadRequest.RegularScalarNodes)
						{
							// 业务分析->报警，OEE
							business.PrepareDealAlarm( regularScalarNode, GetAlarmNodeFrom( regularScalarNode ), GetDeviceNameWithPath( ), tagName: null );
							business.PrepareDealOee(   regularScalarNode, GetOeeNodeFrom(   regularScalarNode ), GetDeviceNameWithPath( ) );
							// 添加进客户端展示的数据节点信息
							string address = CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, 0, regularScalarNode.Index, regularScalarNode );
							dataNodes.Add( ScalarDataNode.ParseFrom( regularScalarNode, string.IsNullOrEmpty( address ) ? AccessLevel.Read : AccessLevel.ReadWrite ) );
							// 添加进数据标签信息里
							DeviceSingleAddressLabel addressLabel = new DeviceSingleAddressLabel( sourceReadRequest, regularScalarNode );
							addressLabel.PhysicalAddress = address;
							AddDeviceSingleAddress( addressLabel, uploadInfo.EnableSubscription );
						}
						foreach(RegularStructNode regularStructNode in sourceReadRequest.RegularStructNodes)
						{
							if (regularStructNode.StructParseType == ParseType.Struct)
							{
								// 添加进客户端展示的数据节点信息
								ScalarDataNode structNode = ScalarDataNode.ParseFrom( regularStructNode );
								RegularStructItemNode structItemNode = GetRegularStructItemNodeFromResource( structNode.StructName );

								if (structItemNode != null)
								{
									List<ScalarDataNode> scalarDataNodes = new List<ScalarDataNode>( );
									foreach (RegularScalarNode regularScalarNode in structItemNode.RegularScalarNodes)
									{
										// 添加进客户端展示的数据节点信息
										ScalarDataNode dataNode = ScalarDataNode.ParseFrom( regularScalarNode, AccessLevel.Read );
										string physicalAddress = CalculatePhysicalAddressFromSourceReadRequest(
											sourceReadRequest, regularStructNode.StructIndex, regularScalarNode.Index, regularScalarNode );
										dataNode.AccessLevel = string.IsNullOrEmpty( physicalAddress ) ? AccessLevel.Read : AccessLevel.ReadWrite;
										scalarDataNodes.Add( dataNode );
									}
									structNode.StructNodes = scalarDataNodes.ToArray( );

									if (regularStructNode.ArrayLength < 0)
									{
										// 添加进数据标签信息里，包括，结构体本身和结构体的子数据信息
										DeviceSingleAddressLabel structAddressLabel = new DeviceSingleAddressLabel( sourceReadRequest, regularStructNode, -1 );
										AddDeviceSingleAddress( structAddressLabel, false );
										foreach (RegularScalarNode scalarNode in structItemNode.RegularScalarNodes)
										{
											string tagName = regularStructNode.Name + "." + scalarNode.Name;
											// 业务分析->报警，OEE
											business.PrepareDealAlarm( scalarNode, GetAlarmNodeFrom( scalarNode ), GetDeviceNameWithPath( ), tagName );
											business.PrepareDealOee( scalarNode, GetOeeNodeFrom( scalarNode ), GetDeviceNameWithPath( ), tagName );

											DeviceSingleAddressLabel addressLabel = new DeviceSingleAddressLabel( sourceReadRequest, regularStructNode, -1, scalarNode );
											addressLabel.PhysicalAddress = CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest,
												regularStructNode.StructIndex, scalarNode.Index, scalarNode );
											AddDeviceSingleAddress( addressLabel, uploadInfo.EnableSubscription );
										}
									}
									else
									{
										// 结构体数组的情况，都是需要添加进数据标签信息的
										for (int i = 0; i < regularStructNode.ArrayLength; i++)
										{
											// 添加进数据标签信息里，包括，结构体本身和结构体的子数据信息
											DeviceSingleAddressLabel structAddressLabel = new DeviceSingleAddressLabel( sourceReadRequest, regularStructNode, i );
											AddDeviceSingleAddress( structAddressLabel, false );
											foreach (RegularScalarNode scalarNode in structItemNode.RegularScalarNodes)
											{
												string tagName = regularStructNode.Name + $"[{i}]." + scalarNode.Name;
												// 业务分析->报警，OEE
												business.PrepareDealAlarm( scalarNode, GetAlarmNodeFrom( scalarNode ), GetDeviceNameWithPath( ), tagName );
												business.PrepareDealOee( scalarNode, GetOeeNodeFrom( scalarNode ), GetDeviceNameWithPath( ), tagName );

												DeviceSingleAddressLabel addressLabel = new DeviceSingleAddressLabel( sourceReadRequest, regularStructNode, i, scalarNode );
												addressLabel.PhysicalAddress = CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest,
													regularStructNode.StructIndex + structItemNode.StructLength * i, scalarNode.Index, scalarNode );
												AddDeviceSingleAddress( addressLabel, uploadInfo.EnableSubscription );
											}
										}
									}
								}
								dataNodes.Add( structNode );
							}
							else if (regularStructNode.StructParseType == ParseType.FullName)
							{
								RegularStructItemNode structItemNode = GetRegularStructItemNodeFromResource( regularStructNode.StructName );

								if (structItemNode != null)
								{
									if (regularStructNode.ArrayLength < 0)
									{
										// 添加进数据标签信息里，包括，结构体本身和结构体的子数据信息
										foreach (RegularScalarNode scalarNode in structItemNode.RegularScalarNodes)
										{
											string tagName = regularStructNode.Name + "." + scalarNode.Name;
											// 业务分析->报警，OEE
											business.PrepareDealAlarm( scalarNode, GetAlarmNodeFrom( scalarNode ), GetDeviceNameWithPath( ), tagName );
											business.PrepareDealOee( scalarNode, GetOeeNodeFrom( scalarNode ), GetDeviceNameWithPath( ), tagName );

											DeviceSingleAddressLabel addressLabel = new DeviceSingleAddressLabel( sourceReadRequest, regularStructNode, -1, scalarNode );
											addressLabel.PhysicalAddress = CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, regularStructNode.StructIndex, scalarNode.Index, scalarNode );
											AddDeviceSingleAddress( addressLabel, uploadInfo.EnableSubscription );

											// 添加进客户端展示的数据节点信息
											ScalarDataNode dataNode = ScalarDataNode.ParseFrom( scalarNode, AccessLevel.Read );
											dataNode.Name = tagName;
											dataNode.AccessLevel = string.IsNullOrEmpty( addressLabel.PhysicalAddress ) ? AccessLevel.Read : AccessLevel.ReadWrite;
											dataNodes.Add( dataNode );
										}
									}
									else
									{
										// 结构体数组的情况，都是需要添加进数据标签信息的
										for (int i = 0; i < regularStructNode.ArrayLength; i++)
										{
											// 添加进数据标签信息里，包括，结构体本身和结构体的子数据信息
											foreach (RegularScalarNode scalarNode in structItemNode.RegularScalarNodes)
											{
												string tagName = regularStructNode.Name + $"[{i}]." + scalarNode.Name;
												// 业务分析->报警，OEE
												business.PrepareDealAlarm( scalarNode, GetAlarmNodeFrom( scalarNode ), GetDeviceNameWithPath( ), tagName );
												business.PrepareDealOee( scalarNode, GetOeeNodeFrom( scalarNode ), GetDeviceNameWithPath( ), tagName );

												DeviceSingleAddressLabel addressLabel = new DeviceSingleAddressLabel( sourceReadRequest, regularStructNode, i, scalarNode );
												addressLabel.PhysicalAddress = CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, regularStructNode.StructIndex + structItemNode.StructLength * i, scalarNode.Index, scalarNode );
												AddDeviceSingleAddress( addressLabel, uploadInfo.EnableSubscription );

												// 添加进客户端展示的数据节点信息
												ScalarDataNode dataNode = ScalarDataNode.ParseFrom( scalarNode, AccessLevel.Read );
												dataNode.Name = tagName;
												dataNode.AccessLevel = string.IsNullOrEmpty( addressLabel.PhysicalAddress ) ? AccessLevel.Read : AccessLevel.ReadWrite;
												dataNodes.Add( dataNode );
											}
										}
									}
								}
							}
							else
							{
								// 展开的节点，是单独的数据点
								RegularStructItemNode structItemNode = GetRegularStructItemNodeFromResource( regularStructNode.StructName );
								if (structItemNode != null)
								{
									foreach (RegularScalarNode regularScalarNode in structItemNode.RegularScalarNodes)
									{
										// 业务分析->报警，OEE
										business.PrepareDealAlarm( regularScalarNode, GetAlarmNodeFrom( regularScalarNode ), GetDeviceNameWithPath( ), tagName: null );
										business.PrepareDealOee( regularScalarNode, GetOeeNodeFrom( regularScalarNode ), GetDeviceNameWithPath( ) );
										// 添加进客户端展示的数据节点信息
										ScalarDataNode dataNode = ScalarDataNode.ParseFrom( regularScalarNode, AccessLevel.Read );
										string physicalAddress = CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, regularStructNode.StructIndex, regularScalarNode.Index, regularScalarNode );
										dataNode.AccessLevel = string.IsNullOrEmpty( physicalAddress ) ? AccessLevel.Read : AccessLevel.ReadWrite;
										dataNodes.Add( dataNode );

										// 添加进数据标签信息里
										DeviceSingleAddressLabel addressLabel = new DeviceSingleAddressLabel( sourceReadRequest, regularScalarNode );
										addressLabel.PhysicalAddress = physicalAddress;
										AddDeviceSingleAddress( addressLabel, uploadInfo.EnableSubscription );
									}
								}
							}
						}
					}
				}
				else if (request.RequestType == RequestType.MethodCall)
				{
					if ( request is CallMethodRequest callMethodRequest)
					{
						// 需要寻找方法的签名信息
						if (!callMethodRequest.StoreResultToDeviceData) continue;

						if (this.dictRpcApiInfos.ContainsKey( callMethodRequest.Address ))
						{
							ScalarDataNode scalarData = ScalarDataNode.ParseFrom( callMethodRequest, this.dictRpcApiInfos[callMethodRequest.Address].ExtensionInfoAttribute );
							if (scalarData.DataType != DataType.Method)
								dataNodes.Add( scalarData );
						}
						else
							logNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"加载方法[{callMethodRequest.Address}]时，但是找不到相关的接口，无法注册数据标签。" );
					}
				}
			}
			scalarDataNodes = dataNodes.ToArray( );
		}

		/// <summary>
		/// 当所有设备初始化结束之后，执行的一个功能方法
		/// </summary>
		public virtual void ExecuteAfterBussiness( DeviceExtInfoList deviceExtInfos ) { }

		/// <summary>
		/// 新增加一个请求信息，可以从外部额外指定。
		/// </summary>
		/// <param name="request">请求</param>
		public void AddRequest( RequestBase request )
		{
			this.Requests.Add( request );
		}

		private void AddDeviceSingleAddress( DeviceSingleAddressLabel addressLabel, bool subscription )
		{
			if (this.dictDeviceSingleAddress.ContainsKey( addressLabel.Name ))
				logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"设备点位[{addressLabel.Name}]配置重复，请仔细检查，当前点位跳过。" );
			else
			{
				// 这里是否开启订阅才真的的实现值变化通知技术
				// if (subscription) addressLabel.EnableSubscription = subscription;
				addressLabel.OnValueChanged += AddressLabel_OnValueChanged;
				this.dictDeviceSingleAddress.Add( addressLabel.Name, addressLabel );
			}
		}

		private void AddressLabel_OnValueChanged( DeviceSingleAddressLabel addressLabel, JToken value )
		{
			if (addressLabel.EnableSubscription || this.edgeResources.EdgeSettings.UploadInfo.EnableSubscription)
			{
				string topic = string.Empty;

				if (this.edgeResources?.MqttServer != null)
				{
					if (string.IsNullOrEmpty( topic )) topic = GetDeviceNameWithPath( ) + HslTechnologyExtension.DeviceDefaultSplit + addressLabel.Name;
					this.edgeResources?.MqttServer?.PublishTopicPayload( topic, Encoding.UTF8.GetBytes( value.ToString( ) ), retain: true );
				}

				if (this.edgeResources.WebSocketServer != null)
				{
					if (string.IsNullOrEmpty( topic )) topic = GetDeviceNameWithPath( ) + HslTechnologyExtension.DeviceDefaultSplit + addressLabel.Name;
					JObject json = new JObject( );
					json.Add( "Topic", topic );
					json.Add( "Value", value );
					this.edgeResources.WebSocketServer?.PublishClientPayload( topic, json.ToString( ), retain: true );
				}

				OnDeviceValueChanged?.Invoke( this, addressLabel, value );
			}
			MarkDataChanged( );                                             // 标记当前的设备的数据发生了变化
		}

		/// <inheritdoc cref="DeviceSingleAddressLabel.RaiseValueChangeEvent"/>
		public void RaiseValueChangeEvent( )
		{
			foreach (var item in this.dictDeviceSingleAddress)
			{
				if (!this.edgeResources.EdgeServices.IsRunning) return;     // 如果退出了，直接返回

				DeviceSingleAddressLabel addressLabel = item.Value;
				if (addressLabel == null) continue;

				// 强制引发值变化的事件，从而直接上传服务器数据
				addressLabel.RaiseValueChangeEvent( );
			}
		}

		#endregion

		#endregion

		#region Event

		/// <summary>
		/// 当前的数据值变化的委托信息
		/// </summary>
		/// <param name="device">当前的设备对象信息</param>
		/// <param name="addressLabel">触发的地址标签对象</param>
		/// <param name="value">变化之后的数据值</param>
		public delegate void DelegateOnDeviceValueChanged( DeviceCoreBase device, DeviceSingleAddressLabel addressLabel, JToken value );

		/// <summary>
		/// 数据值变化的事件
		/// </summary>
		public event DelegateOnDeviceValueChanged OnDeviceValueChanged;

		/// <summary>
		/// 设备当前的在线状态发生了变化
		/// </summary>
		/// <param name="device">当前的设备对象信息</param>
		/// <param name="status">当前的在线状态信息</param>
		public delegate void DelegateOnDeviceStatusChanged( DeviceCoreBase device, bool status );

		/// <summary>
		/// 设备状态变化的事件
		/// </summary>
		public event DelegateOnDeviceStatusChanged OnDeviceStatusChanged;

		#endregion

		#region Read Write Access

		/// <summary>
		/// 根据请求的信息，获取相关的访问权限信息
		/// </summary>
		/// <param name="scalarReadRequest">标量请求的信息</param>
		/// <returns>访问的权限信息</returns>
		protected virtual AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			return AccessLevel.ReadWrite;
		}

		/// <summary>
		/// 根据原始的数据请求，起始索引位置(规则解析为0，结构体为结构体的起始索引，结构体数组也是一样)，数据类型及维度信息，计算出实际偏移的地址信息
		/// </summary>
		/// <param name="sourceReadRequest">原始的报文请求信息</param>
		/// <param name="byteOffset">起始的偏移地址，如果是结构体，就是结构体的起始偏移字节，按照字节为单位</param>
		/// <param name="index">起始索引位置，如果是bool就是位索引，否则就是字节索引</param>
		/// <param name="scalarTransform">数据的基本变换情况</param>
		/// <returns>实际的地址信息，如果不支持，则直接返回NULL</returns>
		protected virtual string CalculatePhysicalAddressFromSourceReadRequest( 
			SourceReadRequest sourceReadRequest, int byteOffset, int index, IScalarTransform scalarTransform )
		{
			return null;
		}

		/// <inheritdoc cref="CalculatePhysicalAddressFromSourceReadRequest(SourceReadRequest, int, int, IScalarTransform)"/>
		public string CalculateDevicePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest, int byteOffset, int index, IScalarTransform scalarTransform )
		{
			return CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		/// <summary>
		/// 获取设备信号的地址数据信息
		/// </summary>
		/// <returns>设备的地址数据信息</returns>
		public DeviceSingleAddressLabel[] GetDeviceSingleAddresses( )
		{
			List<DeviceSingleAddressLabel> singleAddressLabels = new List<DeviceSingleAddressLabel>( dictDeviceSingleAddress.Count );
			foreach (var item in dictDeviceSingleAddress.Values)
			{
				singleAddressLabels.Add( item );
			}
			return singleAddressLabels.ToArray( );
		}

		#endregion

		#region Protect Method

		/// <summary>
		/// 在没有请求发生时候的更新设备活动时间的方法，这在一些自定义协议时间的时候，重写可以自由控制更新时间，例如 TCP转MQTT时，当有数据交互的时候，立即更新时间
		/// </summary>
		protected virtual void UpdateDeviceActiveTimeWhemNoneRequest( )
		{
			this.deviceData.SetSingleValue( "__activeTime", this.deviceThreadState.LastRequestSuccessTime.ToEdgeString( ) );
		}

		/// <summary>
		/// 获取当前设备的异常信息
		/// </summary>
		/// <returns>活动时间信息</returns>
		public DeviceExceptionMessage GetExceptionInfo( )
		{
			DeviceExceptionMessage deviceExceptionMessage = new DeviceExceptionMessage( );
			deviceExceptionMessage.Name = this.GetDeviceNameWithPath( );
			deviceExceptionMessage.StartTime  = DateTime.Parse( this.deviceData.GetJTokenValueByName( "__startTime" ).Value<string>( ) );
			deviceExceptionMessage.ActiveTime = DateTime.Parse( this.deviceData.GetJTokenValueByName( "__activeTime" ).Value<string>( ) );
			deviceExceptionMessage.Message = this.deviceData.GetJTokenValueByName( "__failedMsg" ).Value<string>( );
			return deviceExceptionMessage;
		}

		/// <summary>
		/// 根据设备节点的基本配置信息，将指定的对象注册为 MRPC 及 WebApi 信息
		/// </summary>
		/// <param name="mqttServer">Mqtt服务器</param>
		/// <param name="httpServer">Http服务器</param>
		/// <param name="deviceNode">设备节点</param>
		/// <param name="api">等待注册的API信息</param>
		protected void RegisterRpcService( MqttServer mqttServer, HttpServer httpServer, DeviceNode deviceNode, object api )
		{
			if (api == null) return;
			if (deviceNode != null && deviceNode.MRpcEnable)
				mqttServer?.RegisterMqttRpcApi( GetDeviceNameWithPath( ), api );
			if (deviceNode != null && deviceNode.WebApiEnable)
				httpServer?.RegisterHttpRpcApi( GetDeviceNameWithPath( ), api );
		}

		/// <summary>
		/// 使用固定的节点加载数据信息
		/// </summary>
		/// <param name="element">数据请求的所有列表信息</param>
		/// <param name="requestGroup">当前请求关联的请求分类信息</param>
		private void LoadRequest( XElement element, RequestGroupNode requestGroup )
		{
			if(Requests == null) Requests = new List<RequestBase>( );
			foreach (XElement request in element.Elements( ))
			{
				if (request.Name == nameof( NodeType.RequestNode ))
				{
					RequestType requestType = SoftBasic.GetEnumFromString<RequestType>( request.Attribute( nameof( RequestType ) ).Value );
					if (requestType == RequestType.ScalarRead)
					{
						ScalarReadRequest scalarReadRequest = new ScalarReadRequest( request, this );
						scalarReadRequest.LoadRequestGroup( requestGroup );
						Requests.Add( scalarReadRequest );
					}
					else if (requestType == RequestType.ScalarCache)
					{
						ScalarCacheRequest scalarCacheRequest = new ScalarCacheRequest( request, this );
						scalarCacheRequest.LoadRequestGroup( requestGroup );
						Requests.Add( scalarCacheRequest );
					}
					else if (requestType == RequestType.SourceRead)
					{
						// 解析原始请求里的标量数据解析和结构体解析
						SourceReadRequest sourceReadRequest = new SourceReadRequest( request, this );
						sourceReadRequest.LoadRequestGroup( requestGroup );
						foreach (XElement regularXml in request.Elements( ))
						{
							if (regularXml.Name == nameof( NodeType.RegularScalarNode ))
								sourceReadRequest.RegularScalarNodes.Add( new RegularScalarNode( regularXml ) );
							else if (regularXml.Name == nameof( NodeType.RegularStructNode ))
								sourceReadRequest.RegularStructNodes.Add( new RegularStructNode( regularXml ) );

						}
						Requests.Add( sourceReadRequest );
					}
					else if (requestType == RequestType.MethodCall)
					{
						// 方法的调用信息
						CallMethodRequest callMethodRequest = new CallMethodRequest( request, this );
						callMethodRequest.LoadRequestGroup( requestGroup );
						Requests.Add( callMethodRequest );
					}
					else if (requestType == RequestType.DatabaseOperate)
					{
						// 数据库调用
						DatabaseRequest databaseRequest = new DatabaseRequest( request, this );
						databaseRequest.LoadRequestGroup( requestGroup );
						Requests.Add( databaseRequest );
					}
					else if (requestType == RequestType.WriteInterval)
					{
						// 写入的请求调用
						ScalarWriteRequest scalarWriteRequest = new ScalarWriteRequest( request, this );
						scalarWriteRequest.LoadRequestGroup( requestGroup );
						Requests.Add( scalarWriteRequest );
					}
				}
				else if (request.Name == nameof( NodeType.MethodConfig ))
				{
					// 自定义的方法公开信息
					if (this.methodConfigNode == null)
						this.methodConfigNode = new MethodConfigNode( request );
				}
				else if (request.Name == nameof( NodeType.RegularStructItemNode ))
				{
					// 定义的本地的结构体资源信息
					if (this.localRegularStruct == null) this.localRegularStruct = new Dictionary<string, RegularStructItemNode>( );
					RegularStructItemNode regularStructItem = RegularStructItemNode.PraseFromFullXml( request );
					if (!this.localRegularStruct.ContainsKey( regularStructItem.Name ))
						this.localRegularStruct.Add( regularStructItem.Name, regularStructItem );
				}
				else if (request.Name == nameof( NodeType.AlarmNode ))
				{
					// 定义的本地的报警资源信息
				}
				else if (request.Name == nameof( NodeType.OeeNode ))
				{
					// 定义的本地的OEE分析的资源信息
				}
				else if (request.Name == nameof( NodeType.RequestGroupNode ))
				{
					RequestGroupNode group = new RequestGroupNode( request );
					LoadRequest( request, group );
				}
				else
				{
					OtherNameXmlLoad( request );
				}
			}
		}

		/// <summary>
		/// 其他的一些XML信息的解析，例如所有的服务器资源还支持地址映射解析的操作
		/// </summary>
		/// <param name="other">XML配置信息</param>
		protected virtual void OtherNameXmlLoad( XElement other )
		{

		}

		/// <summary>
		/// 寻找报警节点定义的方法，现在本地寻找相关的报警定义，如果没有找到，再去全局报警资源里进行寻找
		/// </summary>
		/// <param name="relateNode">报警相关的节点信息</param>
		/// <returns>报警节点</returns>
		private AlarmNode GetAlarmNodeFrom( IAlarmRelateNode relateNode )
		{
			if (string.IsNullOrEmpty( relateNode.AlarmRelate )) return null;
			if (this.localAlarmNodes != null && this.localAlarmNodes.ContainsKey( relateNode.AlarmRelate ))
				return this.localAlarmNodes[relateNode.AlarmRelate];
			else
				return BusinessEngine.Business.AlarmResource.GetAlarmNode( relateNode.AlarmRelate );
		}

		private OeeNode GetOeeNodeFrom( IOeeRelateNode relateNode )
		{
			if (string.IsNullOrEmpty( relateNode.OeeRelate )) return null;
			if (this.localOeeNodes != null && this.localOeeNodes.ContainsKey( relateNode.OeeRelate ))
				return this.localOeeNodes[relateNode.OeeRelate];
			else
				return BusinessEngine.Business.OeeResource.GetOeeNode( relateNode.OeeRelate );
		}

		/// <inheritdoc cref="DealIntegerDataAlarmNode(IAlarmRelateNode, int)"/>
		protected void DealBoolDataAlarmNode( IAlarmRelateNode relateNode, bool value, string tagName )
		{
			DealBoolArrayDataAlarmNode( relateNode, new bool[] { value }, tagName );
		}

		/// <inheritdoc cref="DealIntegerDataAlarmNode(IAlarmRelateNode, int)"/>
		protected void DealBoolArrayDataAlarmNode( IAlarmRelateNode relateNode, bool[] value, string tagName )
		{
			AlarmNode alarmNode = GetAlarmNodeFrom( relateNode );
			if (string.IsNullOrEmpty( tagName )) tagName = relateNode.Name;
			if (alarmNode == null)
			{
				LogNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"当前节点[{tagName}] 绑定的报警节点 [{relateNode.AlarmRelate}] 不存在!" );
				return;
			}
			if (alarmNode.AlarmType == AlarmType.Boolean || alarmNode.AlarmType == AlarmType.Hex)
				this.advanceAnalysis.DealHexData( tagName, value );
			else
				LogNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"当前节点[{tagName}] 的数据类型 {relateNode.DataTypeCode} 对于当前报警节点 [{relateNode.AlarmRelate}] 不支持。请重新修改配置信息。" );
		}

		/// <inheritdoc cref="DealIntegerDataAlarmNode(IAlarmRelateNode, int)"/>
		protected void DealHexDataAlarmNode( IAlarmRelateNode relateNode, byte[] value, string tagName )
		{
			AlarmNode alarmNode = GetAlarmNodeFrom( relateNode );
			if (string.IsNullOrEmpty( tagName )) tagName = relateNode.Name;
			if (alarmNode == null)
			{
				LogNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"当前节点[{tagName}] 绑定的报警节点 [{relateNode.AlarmRelate}] 不存在!" );
				return;
			}
			if (alarmNode.AlarmType == AlarmType.Boolean || alarmNode.AlarmType == AlarmType.Hex)
				this.advanceAnalysis.DealHexData( tagName, value );
			else
				LogNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"当前节点[{tagName}] 的数据类型 {relateNode.DataTypeCode} 对于当前报警节点 [{relateNode.AlarmRelate}] 不支持。请重新修改配置信息。" );
		}

		/// <summary>
		/// 处理整数的报警节点相关的数据内容
		/// </summary>
		/// <param name="relateNode">报警相关的节点信息</param>
		/// <param name="value">传递的数据值信息</param>
		protected void DealIntegerDataAlarmNode( IAlarmRelateNode relateNode, int value, string tagName )
		{
			AlarmNode alarmNode = GetAlarmNodeFrom( relateNode );
			if (string.IsNullOrEmpty( tagName )) tagName = relateNode.Name;
			if (alarmNode == null)
			{
				LogNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"当前节点[{tagName}] 绑定的报警节点 [{relateNode.AlarmRelate}] 不存在!" );
				return;
			}
			if (alarmNode.AlarmType == AlarmType.Integer)
				this.advanceAnalysis.DealIntegerData( tagName, value );
			else if (alarmNode.AlarmType == AlarmType.DataRange)
				this.advanceAnalysis.DealDataRange( tagName, value );
			else
				LogNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"当前节点[{tagName}] 的数据类型 {relateNode.DataTypeCode} 对于当前报警节点 [{relateNode.AlarmRelate}] 不支持。请重新修改配置信息。" );
		}

		/// <inheritdoc cref="DealIntegerDataAlarmNode(IAlarmRelateNode, int)"/>
		protected void DealDataRangeAlarmNode( IAlarmRelateNode relateNode, double value, string tagName )
		{
			AlarmNode alarmNode = GetAlarmNodeFrom( relateNode );
			if (string.IsNullOrEmpty( tagName )) tagName = relateNode.Name;
			if (alarmNode == null)
			{
				LogNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"当前节点[{tagName}] 绑定的报警节点 [{relateNode.AlarmRelate}] 不存在!" );
				return;
			}
			if (alarmNode.AlarmType == AlarmType.DataRange)
				this.advanceAnalysis.DealDataRange( tagName, value );
			else
				LogNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"当前节点[{tagName}] 的数据类型 {relateNode.DataTypeCode} 对于当前报警节点 [{relateNode.AlarmRelate}] 不支持。请重新修改配置信息。" );
		}


		/// <summary>
		/// 处理读取结果的统一方法，需要传入读取的结果对象，以及如何处理的 <see cref="Action{T}"/> 的委托对象
		/// </summary>
		/// <typeparam name="T">当前的类型对象</typeparam>
		/// <param name="read">读取的结果对象</param>
		/// <param name="action">如何处理的委托信息</param>
		/// <returns>最后处理的结果内容</returns>
		protected OperateResult<T> DealWithReadResult<T>( OperateResult<T> read, Action<T> action )
		{
			if (read.IsSuccess)
			{
				action?.Invoke( read.Content );
				return read;
			}
			else
			{
				return read;
			}
		}

		/// <summary>
		/// 处理原始字节读取结果的统一方法，传入读取的结果对象，已经如何处理的委托对象
		/// </summary>
		/// <param name="read">读取结果内容</param>
		/// <param name="action">指定如何处理的委托对象</param>
		/// <returns>最后的处理结果的内容</returns>
		protected OperateResult<byte[]> DealWithSourceReadResult( OperateResult<byte[]> read, Action<OperateResult<byte[]>> action )
		{
			if (read.IsSuccess || this.resetNullIfFaield)
			{
				action?.Invoke( read );
				return read;
			}
			else
			{
				return read;
			}
		}

		/// <summary>
		/// 设置设备的富数据信息，是一种富JSON的数据格式信息
		/// </summary>
		/// <param name="name">数据的名称</param>
		/// <param name="value">数据值</param>
		/// <param name="isArray">是否是数组</param>
		protected void SetJsonValue( string name, dynamic value, bool isArray ) => deviceData.SetJsonValue( name, value, isArray );

		/// <summary>
		/// 设置设备的JSON数据信息，类型为JObject，或是JArray
		/// </summary>
		/// <param name="name">数据的名称</param>
		/// <param name="value">数据值</param>
		protected void SetJsonObjectValue( string name, JToken value ) => deviceData.SetJToken( name, value );

		/// <summary>
		/// 设置名称和描述的设备数据信息
		/// </summary>
		/// <param name="node">设备描述信息</param>
		private void SetNameDescriptionJsonValue( GroupNode node )
		{
			deviceData.SetSingleValue( "__startTime",   DateTime.Now.ToEdgeString( ) );
			deviceData.SetSingleValue( "__name",        node.Name );
			deviceData.SetSingleValue( "__description", node.Description );
			if (!string.IsNullOrEmpty( node.DisplayName ))
				deviceData.SetSingleValue( "__displayName", node.DisplayName );
		}

		private JToken GetNullJToken( ) => JValue.CreateNull( );

		private void BussinessDataNullHelper( string tagName, IBussinessRelateNode relateNode, bool saveDeviceData = true, JObject structJson = null )
		{
			if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewValue( GetNullJToken( ) );
			if (saveDeviceData) SetJsonObjectValue( tagName, GetNullJToken( ) );
			if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = GetNullJToken( );
		}

		private OperateResult BussinessSingleDataHelper<T>( string tagName, IBussinessRelateNode relateNode, OperateResult<T> read, Func<T, decimal> trans, bool saveDeviceData = true, JObject structJson = null )
		{
			if (read.IsSuccess)
			{
				int transformEnable = relateNode.TransformType;
				string type = ((IScalarTransform)relateNode).DataTypeCode;
				if (type == RegularNodeTypeItem.String.Text ||
					type == RegularNodeTypeItem.StringJson.Text)
				{
					transformEnable = RegularHelper.TransformType_None;     // 屏蔽字符串类型的数据强行转换功能
				}

				if (transformEnable == RegularHelper.TransformType_None || transformEnable == RegularHelper.TransformType_Not)
				{
					T value = read.Content;
					if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewSingleValue( value, this.edgeResources );
					if (saveDeviceData) deviceData.SetSingleValue( tagName, value );
					if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JValue( value );
				}
				else if (transformEnable == RegularHelper.TransformType_Value && trans != null)
				{
					if (relateNode.TransformDecimal == 0)
					{
						long valueDynamic = (long)RegularHelper.TransValueByTransform( relateNode, read.Content, trans );
						if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewSingleValue( valueDynamic, this.edgeResources );
						if (saveDeviceData) deviceData.SetSingleValue( tagName, valueDynamic );
						if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JValue( valueDynamic );
					}
					else
					{
						double valueDynamic = (double)RegularHelper.TransValueByTransform( relateNode, read.Content, trans );
						if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewSingleValue( valueDynamic, this.edgeResources );
						if (saveDeviceData) deviceData.SetSingleValue( tagName, valueDynamic );
						if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JValue( valueDynamic );
					}
				}
				else if (transformEnable == RegularHelper.TransformType_Express)
				{
					object value = RegularHelper.TransValueByExpressTransform<T>( relateNode, read.Content );
					if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewSingleValue( value, this.edgeResources );
					if (saveDeviceData) deviceData.SetSingleValue( tagName, value );
					if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JValue( value );
				}
			}
			else if (resetNullIfFaield)
			{
				// 处理读取失败，恢复空值
				BussinessDataNullHelper( tagName, relateNode, saveDeviceData, structJson );
			}
			return read;
		}

		private OperateResult BussinessArrayDataHelper<T>( string tagName, IBussinessRelateNode relateNode, OperateResult<T[]> read, Func<T, decimal> trans, bool saveDeviceData = true, JObject structJson = null )
		{
			if (read.IsSuccess)
			{
				if (relateNode.TransformType == RegularHelper.TransformType_None || relateNode.TransformType == RegularHelper.TransformType_Not)
				{
					T[] value = read.Content;
					if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewArrayValue( value, this.edgeResources );
					if (saveDeviceData) deviceData.SetArrayValue( tagName, value );
					if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JArray( value );
				}
				else if (relateNode.TransformType == RegularHelper.TransformType_Value && trans != null)
				{
					if (relateNode.TransformDecimal == 0)
					{
						long[] valueDynamic = RegularHelper.TransLongArrayByTransform( relateNode, read.Content, trans );
						if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewArrayValue( valueDynamic, this.edgeResources );
						if (saveDeviceData) deviceData.SetArrayValue( tagName, valueDynamic );
						if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JArray( valueDynamic );
					}
					else
					{
						double[] valueDynamic = RegularHelper.TransArrayByTransform( relateNode, read.Content, trans );
						if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewArrayValue( valueDynamic, this.edgeResources );
						if (saveDeviceData) deviceData.SetArrayValue( tagName, valueDynamic );
						if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JArray( valueDynamic );
					}
				}
				else if (relateNode.TransformType == RegularHelper.TransformType_Express)
				{
					if (relateNode.TransformDecimal == 0)
					{
						long[] value = RegularHelper.TransArrayByExpressTransform<T>( relateNode, read.Content ) as long[];
						if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewArrayValue( value, this.edgeResources );
						if (saveDeviceData) deviceData.SetArrayValue( tagName, value );
						if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JArray( value );
					}
					else
					{
						double[] value = RegularHelper.TransArrayByExpressTransform<T>( relateNode, read.Content ) as double[];
						if (dictDeviceSingleAddress.ContainsKey( tagName )) dictDeviceSingleAddress[tagName].SetNewArrayValue( value, this.edgeResources );
						if (saveDeviceData) deviceData.SetArrayValue( tagName, value );
						if (structJson != null) structJson[((IScalarTransform)relateNode).Name] = new JArray( value );
					}
				}
			}
			else if (resetNullIfFaield)
			{
				// 处理读取失败，恢复空值
				BussinessDataNullHelper( tagName, relateNode, saveDeviceData, structJson );
			}
			return read;
		}


		/// <summary>
		/// 处理各种类型的数据信息
		/// </summary>
		/// <param name="tagName">数据名称</param>
		/// <param name="relateNode">商业的节点信息</param>
		/// <param name="read">读取结果内容</param>
		/// <param name="saveDeviceData">是否存储到设备数据对象信息</param>
		/// <param name="structJson">是否存储到结构体的json对象</param>
		/// <returns>是否处理成功</returns>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<bool> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 判断是否进行了bool反转
			if (read.IsSuccess && relateNode.TransformType == RegularHelper.TransformType_Not) read.Content = !read.Content;

			if (relateNode != null && !string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealBoolDataAlarmNode( relateNode, read.Content, tagName );
			// OEE业务处理
			if (relateNode != null && !string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, read.Content ? 1 : 0 );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, trans: m => m ? 1 : 0, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<bool[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 判断是否进行了bool反转
			if (read.IsSuccess && relateNode.TransformType == RegularHelper.TransformType_Not && read.Content != null)
			{
				for (int i = 0; i < read.Content.Length; i++)
					read.Content[i] = !read.Content[i];
			}

			// 报警处理
			if (relateNode != null && !string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealBoolArrayDataAlarmNode( relateNode, read.Content, tagName );

			return BussinessArrayDataHelper( tagName, relateNode, read, trans: m => m ? 1 : 0, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<byte> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (relateNode != null && !string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealIntegerDataAlarmNode( relateNode, read.Content, tagName );
			// oee处理
			if (relateNode != null && !string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, read.Content );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<byte[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealHexDataAlarmNode( relateNode, read.Content, tagName );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read.Then( m => OperateResult.CreateSuccessResult( m.ToHexString( ) ) ), null, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<sbyte> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (relateNode != null && !string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealIntegerDataAlarmNode( relateNode, read.Content, tagName );
			// oee处理
			if (relateNode != null && !string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, read.Content );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<sbyte[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 赋值缓存区
			return BussinessArrayDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<short> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealIntegerDataAlarmNode( relateNode, read.Content, tagName );
			// oee处理
			if (!string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, read.Content );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<short[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			return BussinessArrayDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<ushort> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealIntegerDataAlarmNode( relateNode, read.Content, tagName );
			// oee处理
			if (!string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, read.Content );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<ushort[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			return BussinessArrayDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<int> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealIntegerDataAlarmNode( relateNode, read.Content, tagName );
			// oee处理
			if (!string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, read.Content );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<int[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			return BussinessArrayDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<uint> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealIntegerDataAlarmNode( relateNode, (int)read.Content, tagName );
			// oee处理
			if (!string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, (int)read.Content );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<uint[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			return BussinessArrayDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<long> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealIntegerDataAlarmNode( relateNode, (int)read.Content, tagName );
			// oee处理
			if (!string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, (int)read.Content );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<long[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			return BussinessArrayDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<ulong> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealIntegerDataAlarmNode( relateNode, (int)read.Content, tagName );
			// oee处理
			if (!string.IsNullOrEmpty( relateNode.OeeRelate ) && read.IsSuccess)
				this.advanceAnalysis.DealOeeStatus( tagName, (int)read.Content );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<ulong[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			return BussinessArrayDataHelper( tagName, relateNode, read, m => m, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<float> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealDataRangeAlarmNode( relateNode, read.Content, tagName );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => ((decimal)m), saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<float[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			return BussinessArrayDataHelper( tagName, relateNode, read, m => ((decimal)m), saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<double> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理
			if (!string.IsNullOrEmpty( relateNode.AlarmRelate ) && read.IsSuccess)
				DealDataRangeAlarmNode( relateNode, read.Content, tagName );

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, m => ((decimal)m), saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<double[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			return BussinessArrayDataHelper( tagName, relateNode, read, m => ((decimal)m), saveDeviceData, structJson );
		}

		private string GetStringEndWithZero( string value )
		{
			int index = value.IndexOf( '\0' );
			if (index == 0) return string.Empty;
			if (index > 0) return value.Substring( 0, index );
			return value;
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<string> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理，字符串的报警暂时不支持
			if (relateNode.StringEndwithZero && read.IsSuccess)
			{
				read.Content = GetStringEndWithZero( read.Content );
			}

			// 赋值缓存区
			return BussinessSingleDataHelper( tagName, relateNode, read, decimal.Parse, saveDeviceData, structJson );
		}

		/// <inheritdoc cref="BussinessDataHelper(string, IBussinessRelateNode, OperateResult{bool}, bool, JObject)"/>
		public OperateResult BussinessDataHelper( string tagName, IBussinessRelateNode relateNode, OperateResult<string[]> read, bool saveDeviceData = true, JObject structJson = null )
		{
			// 报警处理，字符串的报警暂时不支持
			if (relateNode.StringEndwithZero && read.IsSuccess)
			{
				if (read.Content != null)
				{
					for (int i = 0; i < read.Content.Length; i++)
					{
						read.Content[i] = GetStringEndWithZero( read.Content[i] );
					}
				}
			}

			// 赋值缓存区
			return BussinessArrayDataHelper( tagName, relateNode, read, decimal.Parse, saveDeviceData, structJson );
		}


		#region Regular Read

		private OperateResult ReadActualBool( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<bool>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<bool[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset * 8;
				if (regular.Length < 0)
				{
					bool value = SoftBasic.ByteToBoolArray( data )[actualIndex];
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					bool[] value = SoftBasic.ByteToBoolArray( data ).SelectMiddle( actualIndex, regular.Length );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualBoolOfByte( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<bool>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<bool[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					bool value = regular.GetBoolOfByteValue( data, actualIndex, null, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					bool[] value = regular.GetBoolOfByteArrayValue( data, actualIndex, null, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualByte( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<byte>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<byte[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					byte value = regular.GetByteValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					byte[] value = regular.GetByteArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualSByte( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<sbyte>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<sbyte[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					sbyte value = regular.GetSByteValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					sbyte[] value = regular.GetSByteArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualInt16( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<short>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<short[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					short value = regular.GetInt16Value( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					short[] value = regular.GetInt16ArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualUInt16( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<ushort>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<ushort[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					ushort value = regular.GetUInt16Value( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					ushort[] value = regular.GetUInt16ArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualInt32( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<int>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<int[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					int value = regular.GetInt32Value( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					int[] value = regular.GetInt32ArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualUInt32( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<uint>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<uint[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					uint value = regular.GetUInt32Value( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					uint[] value = regular.GetUInt32ArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualInt64( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<long>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<long[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					long value = regular.GetInt64Value( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					long[] value = regular.GetInt64ArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualUInt64( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<ulong>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<ulong[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					ulong value = regular.GetUInt64Value( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					ulong[] value = regular.GetUInt64ArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualFloat( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<float>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<float[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					float value = regular.GetFloatValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					float[] value = regular.GetFloatArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualDouble( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				if (regular.Length < 0)
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<double>( ), saveDeviceData, structJson );
				else
					return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<double[]>( ), saveDeviceData, structJson );
			}
			else
			{
				int actualIndex = regular.Index + offset;
				if (regular.Length < 0)
				{
					double value = regular.GetDoubleValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					double[] value = regular.GetDoubleArrayValue( data, actualIndex, byteTransform, this.useAsciiFormate );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualString( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<string>( ), saveDeviceData, structJson );
			}
			else
			{
				if (regular.Length < 0)
				{
					string value = regular.GetStringValue( data, offset, byteTransform );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					string[] values = regular.GetStringArrayValue( data, offset, byteTransform, m => m );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( values ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualIntOfString( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<long>( ), saveDeviceData, structJson );
			}
			else
			{
				if (regular.Length < 0)
				{
					string value = regular.GetStringValue( data, offset, byteTransform );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( Convert.ToInt64( value ) ), saveDeviceData, structJson );
				}
				else
				{
					long[] values = regular.GetStringArrayValue( data, offset, byteTransform, long.Parse );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( values ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualDoubleOfString( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<double>( ), saveDeviceData, structJson );
			}
			else
			{
				if (regular.Length < 0)
				{
					string value = regular.GetStringValue( data, offset, byteTransform );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( Convert.ToDouble( value ) ), saveDeviceData, structJson );
				}
				else
				{
					double[] values = regular.GetStringArrayValue( data, offset, byteTransform, double.Parse );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( values ), saveDeviceData, structJson );
				}
			}
		}

		private OperateResult ReadActualBCD( SourceReadRequest request, byte[] data, int offset, RegularScalarNode regular, string tagName, IByteTransform byteTransform, bool saveDeviceData = true, JObject structJson = null )
		{
			if (data == null)
			{
				return request.DeviceActual.BussinessDataHelper( tagName, regular, new OperateResult<string>( ), saveDeviceData, structJson );
			}
			else
			{
				if (regular.Length < 0)
				{
					string value = regular.GetBCDValue( data, offset, byteTransform );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( value ), saveDeviceData, structJson );
				}
				else
				{
					string[] values = regular.GetBCDArray( data, offset, byteTransform );
					return request.DeviceActual.BussinessDataHelper( tagName, regular, OperateResult.CreateSuccessResult( values ), saveDeviceData, structJson );
				}
			}
		}


		private OperateResult ReadScalarCache( ScalarCacheRequest scalarRequest )
		{
			if(scalarRequest.Length < 0)
			{
				if      (scalarRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)            return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( bool) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)      return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( bool) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Byte.Text)            return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( byte ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.SByte.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( sbyte ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int16.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( short ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt16.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( ushort ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int32.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( int ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt32.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( uint ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int64.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( long ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt64.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( ulong ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Float.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( float ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Double.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( double ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.String.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( string.Empty ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)     return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( long ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)  return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( default( double ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.BCD.Text)             return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( string.Empty ) );
				else return new OperateResult( "Not implementation type: " + scalarRequest.DataTypeCode );
			}
			else
			{
				if      (scalarRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)            return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new bool[   scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)      return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new bool[   scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Byte.Text)            return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new byte[   scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.SByte.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new sbyte[  scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int16.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new short[  scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt16.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new ushort[ scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int32.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new int[    scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt32.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new uint[   scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int64.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new long[   scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt64.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new ulong[  scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Float.Text)           return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new float[  scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Double.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new double[ scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.String.Text)          return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( string.Empty ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)     return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new long[scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)  return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( new double[scalarRequest.Length] ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.BCD.Text)             return BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( string.Empty ) );
				else return new OperateResult( "Not implementation type: " + scalarRequest.DataTypeCode );
			}
		}

		#endregion

		/// <summary>
		/// 从本地的资源及网关全局的资源里查找结构体的定义信息，需要指定结构体的名称<br />
		/// To find the definition information of the structure from the local resources and the global resources of the gateway, 
		/// you need to specify the name of the structure
		/// </summary>
		/// <param name="structName">结构体的名称，不能为空</param>
		/// <returns>查找到的结构体定义对象，如果不存在，则返回 <c>NULL</c></returns>
		private RegularStructItemNode GetRegularStructItemNodeFromResource( string structName )
		{
			// 根据词典信息去查找
			if (this.localRegularStruct != null && this.localRegularStruct.ContainsKey( structName ))
				return this.localRegularStruct[structName];
			else if (RegularStructItemNode.DictionaryRegularStruct != null && RegularStructItemNode.DictionaryRegularStruct.ContainsKey( structName ))
				return RegularStructItemNode.DictionaryRegularStruct[structName];
			else
				return null;
		}

		/// <summary>
		/// 从设备原始字节请求去解析成真实的数据对象
		/// </summary>
		/// <param name="data">原始的数据</param>
		/// <param name="request">请求</param>
		/// <param name="byteTransform">解析转换规则</param>
		protected void ParseFromRequest( OperateResult<byte[]> read, SourceReadRequest request, IByteTransform byteTransform )
		{
			byte[] data = read.Content;
			foreach (var regular in request.RegularScalarNodes)
			{
				try
				{
					if      (regular.DataTypeCode == RegularNodeTypeItem.Bool.Text)           ReadActualBool(           request, data, 0, regular, regular.Name );
					else if (regular.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)     ReadActualBoolOfByte(     request, data, 0, regular, regular.Name );
					else if (regular.DataTypeCode == RegularNodeTypeItem.Byte.Text)           ReadActualByte(           request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.SByte.Text)          ReadActualSByte(          request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.Int16.Text)          ReadActualInt16(          request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.UInt16.Text)         ReadActualUInt16(         request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.Int32.Text)          ReadActualInt32(          request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.UInt32.Text)         ReadActualUInt32(         request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.Int64.Text)          ReadActualInt64(          request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.UInt64.Text)         ReadActualUInt64(         request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.Float.Text)          ReadActualFloat(          request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.Double.Text)         ReadActualDouble(         request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.String.Text)         ReadActualString(         request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)    ReadActualIntOfString(    request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text) ReadActualDoubleOfString( request, data, 0, regular, regular.Name, byteTransform );
					else if (regular.DataTypeCode == RegularNodeTypeItem.BCD.Text)            ReadActualBCD(            request, data, 0, regular, regular.Name, byteTransform );
					else throw new Exception( "Not Supported Data Type" );

					//dynamic value = regular.GetValue( data, 0, byteTransform );

					//if (regular.DataTypeCode != RegularNodeTypeItem.String.Text &&
					//	regular.Length >= 0)
					//{
					//	// 数组
					//	deviceData.SetJsonValue( regular.Name, value, true );
					//}
					//else
					//{
					//	// 单个的值
					//	deviceData.SetJsonValue( regular.Name, value, false );
					//}
					//// 处理报警的事
					//DealDynamicValueAlarmNode( regular, value );
				}
				catch (Exception ex)
				{
					// 索引信息配置不正确的时候，会触发错误消息
					LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"[{request}] [{regular}] {ex.Message}" );
				}
			}

			if (request.DeviceActual.localRegularStruct == null && RegularStructItemNode.DictionaryRegularStruct == null)    // 没有任何的结构体定义则直接返回
				return;
			// 先去本地资源寻找结构体的定义
			foreach (var regular in request.RegularStructNodes)
			{
				// 根据词典信息去查找
				RegularStructItemNode structDefine = request.DeviceActual.GetRegularStructItemNodeFromResource( regular.StructName );
				if (structDefine == null) continue;

				if (regular.StructParseType != ParseType.Struct) // && regular.ArrayLength < 0
				{
					// 结构体展开操作
					ParseFromUnfoldStruct( request, data, regular, structDefine, byteTransform );
				}
				else
				{
					JToken token = ParseFromStruct( request, data, regular.Name, -1, regular.StructIndex, regular.ArrayLength, structDefine, byteTransform );
					request.DeviceActual.deviceData.SetJToken( regular.Name, token );
				}
			}
		}

		private void ReadActualFromSourceByte( SourceReadRequest request, RegularStructNode regular, RegularStructItemNode structItemNode, byte[] data, int index, IByteTransform byteTransform, int structIndex = -1 )
		{
			foreach (var scalarNode in structItemNode.RegularScalarNodes)
			{
				try
				{
					string tagName = regular.GetTagName( scalarNode, structIndex );
					if      (scalarNode.DataTypeCode == RegularNodeTypeItem.Bool.Text)           ReadActualBool(           request, data, index, scalarNode, tagName );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)     ReadActualBoolOfByte(     request, data, index, scalarNode, tagName );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.Byte.Text)           ReadActualByte(           request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.SByte.Text)          ReadActualSByte(          request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.Int16.Text)          ReadActualInt16(          request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.UInt16.Text)         ReadActualUInt16(         request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.Int32.Text)          ReadActualInt32(          request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.UInt32.Text)         ReadActualUInt32(         request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.Int64.Text)          ReadActualInt64(          request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.UInt64.Text)         ReadActualUInt64(         request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.Float.Text)          ReadActualFloat(          request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.Double.Text)         ReadActualDouble(         request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.String.Text)         ReadActualString(         request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)    ReadActualIntOfString(    request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text) ReadActualDoubleOfString( request, data, index, scalarNode, tagName, byteTransform );
					else if (scalarNode.DataTypeCode == RegularNodeTypeItem.BCD.Text)            ReadActualBCD(            request, data, index, scalarNode, tagName, byteTransform );
					else throw new Exception( "Not Supported Data Type" );
					//dynamic value = scalarNode.GetValue( data, index, byteTransform );
					//if (scalarNode.DataTypeCode != RegularNodeTypeItem.String.Text && scalarNode.Length >= 0)
					//	deviceData.SetJsonValue( scalarNode.Name, value, true ); // 数组
					//else
					//	deviceData.SetJsonValue( scalarNode.Name, value, false ); // 单个的值
				}
				catch (Exception ex)
				{
					// 索引信息配置不正确的时候，会触发错误消息
					LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"Struct:[{structItemNode}] Scalar:[{scalarNode}] Message:{ex.Message}" );
				}
			}
		}

		/// <summary>
		/// 当结构体设置为展开之后，相关的操作是有一点区别的，将所有的子项数据解析为单个标签写入到设备数据里。
		/// </summary>
		/// <param name="data">原始的字节内容</param>
		/// <param name="regular">所在的字节索引</param>
		/// <param name="structItemNode">结构体的定义</param>
		/// <param name="byteTransform">字节转换规则</param>
		protected void ParseFromUnfoldStruct( SourceReadRequest request, byte[] data, RegularStructNode regular, RegularStructItemNode structItemNode, IByteTransform byteTransform )
		{
			if(regular.ArrayLength < 0)
			{
				int index = regular.StructIndex;
				ReadActualFromSourceByte( request, regular, structItemNode, data, index, byteTransform );
			}
			else
			{
				for (int i = 0; i < regular.ArrayLength; i++)
				{
					int index = regular.StructIndex + i * structItemNode.StructLength;
					ReadActualFromSourceByte( request, regular, structItemNode, data, index, byteTransform, i );
				}
			}
		}

		/// <summary>
		/// 从原始数据中根据给定的结构体规则<see cref="RegularStructItemNode"/>解析出真实的结构体对象，需要传入原始字节数组，偏移索引，字节转换规则
		/// </summary>
		/// <param name="request">原始字节的请求对象</param>
		/// <param name="data">原始数据内容</param>
		/// <param name="structName">结构体变量的名称，并非结构体的定义的名称</param>
		/// <param name="structIndex">结构体的数组的索引，如果不是数组，则传入-1</param>
		/// <param name="offsetIndex">起始的字节索引</param>
		/// <param name="arrayLength">解析的数组长度，如果小于0，则为标量，否则为数组</param>
		/// <param name="structDefine">结构体配置对象</param>
		/// <param name="byteTransform">数据的解析转换规则</param>
		/// <returns>解析后的JSON对象</returns>
		protected JToken ParseFromStruct( SourceReadRequest request, byte[] data, string structName, int structIndex, int offsetIndex, int arrayLength, RegularStructItemNode structDefine, IByteTransform byteTransform )
		{
			if (arrayLength < 0)
			{
				//if ()
				JObject json = new JObject( );
				foreach (var regular in structDefine.RegularScalarNodes)
				{
					string tagName = (structIndex < 0 ? structName : structName + $"[{structIndex}]") + "." + regular.Name;
					try
					{
						if      (regular.DataTypeCode == RegularNodeTypeItem.Bool.Text)           ReadActualBool(           request, data, offsetIndex, regular, tagName, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)     ReadActualBoolOfByte(     request, data, offsetIndex, regular, tagName, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.Byte.Text)           ReadActualByte(           request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.SByte.Text)          ReadActualSByte(          request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.Int16.Text)          ReadActualInt16(          request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.UInt16.Text)         ReadActualUInt16(         request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.Int32.Text)          ReadActualInt32(          request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.UInt32.Text)         ReadActualUInt32(         request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.Int64.Text)          ReadActualInt64(          request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.UInt64.Text)         ReadActualUInt64(         request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.Float.Text)          ReadActualFloat(          request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.Double.Text)         ReadActualDouble(         request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.String.Text)         ReadActualString(         request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)    ReadActualIntOfString(    request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text) ReadActualDoubleOfString( request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						else if (regular.DataTypeCode == RegularNodeTypeItem.BCD.Text)            ReadActualBCD(            request, data, offsetIndex, regular, tagName, byteTransform, false, json );
						//dynamic value = regular.GetValue( data, index, byteTransform );

						//if (regular.DataTypeCode != RegularNodeTypeItem.String.Text && regular.Length >= 0)
						//	json[regular.Name] = new JArray( value ); // 数组
						//else
						//	json[regular.Name] = new JValue( value ); // 单个的值

					}
					catch (Exception ex)
					{
						// 索引信息配置不正确的时候，会触发错误消息
						LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"[{structDefine}] [{regular}] {ex.Message}" );
					}
				}
				string addressStruct = structIndex < 0 ? structName : structName + $"[{structIndex}]";
				if (dictDeviceSingleAddress.ContainsKey( addressStruct ))
					dictDeviceSingleAddress[addressStruct].Value = json;
				return json;
			}
			else
			{
				JArray array = new JArray( );
				for (int i = 0; i < arrayLength; i++)
					array.Add( ParseFromStruct( request, data, structName, i, offsetIndex + i * structDefine.StructLength, -1, structDefine, byteTransform ) );
				return array;
			}
		}

		#endregion

		#region Server Method

		/// <summary>
		/// 启动读取数据，第一次调用是启动线程，之后调用为重启线程
		/// </summary>
		public void Start( )
		{
			if (this.StatusType == DeviceStatusType.OnStop)
			{
				this.deviceData.SetSingleValue( "__statusType", this.StatusType.ToString( ) );
				ThreadCycleBeforeAction( );
				// 设备处于停用状态
				return;
			}
			if (Interlocked.CompareExchange( ref isStarted, 1, 0 ) == 0)
			{
				if (!this.CurrentThreadControlsEnable) return;                                  // 由外部线程调度控制
				this.thread                       = new Thread( new ParameterizedThreadStart( ThreadReadBackground ) );
				this.thread.IsBackground          = true;
				this.thread.Priority              = ThreadPriority.AboveNormal;
				this.thread.Start( this.isStarted );
			}
			else // 重新启动
			{
				Interlocked.Increment( ref isStarted );     // 增加线程重启次数的标记
				if (!this.CurrentThreadControlsEnable) return;                                  // 由外部线程调度控制
				try
				{
					// 有些linux平台可能不支持，所以使用线程版本号来区分
					this.thread?.Abort( );
				}
				catch
				{

				}
				this.logNet?.WriteInfo( GetDeviceNameWithPath( ), this.ToString( ), " 重新启动采集线程。" );
				this.thread = new Thread( new ParameterizedThreadStart( ThreadReadBackground ) );
				this.thread.IsBackground = true;
				this.thread.Priority = ThreadPriority.AboveNormal;
				this.thread.Start( this.isStarted );
			}
		}

		/// <summary>
		/// 停止读取数据
		/// </summary>
		public void Close( )
		{
			if (isStarted > 0)
			{
				this.isQuit = 1;
				this.autoResetQuit.WaitOne( );
			}
		}

		/// <summary>
		/// 设置为异形客户端对象，支持DTU的模式
		/// </summary>
		/// <param name="alienSession">异形对象</param>
		public virtual void SetDtuSession( AlienSession alienSession )
		{

		}

		/// <summary>
		/// 检查设备的是否活动的情况下，如果发生长时间线程不活动，如果有锁行为的话就进行复位操作，并且返回 true
		/// </summary>
		public virtual PipeBase GetPipeLock( )
		{
			return null;
		}

		#endregion

		#region Path Check

		/// <summary>
		/// 判断当前的设备是否是传入的节点参数信息
		/// </summary>
		/// <param name="nodes">传入的节点参数信息</param>
		/// <returns>是否是当前的设备</returns>
		public bool IsCurrentDevice( string[] nodes )
		{
			if (DeviceNodes != null && nodes != null)
			{
				if (nodes.Length < DeviceNodes.Length) return false;
				for (int i = 0; i < DeviceNodes.Length; i++)
				{
					if (DeviceNodes[i] != nodes[i])
					{
						return false;
					}
				}
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 获取设备路径下的子信息，例如是 工厂一/车间A/设备A/温度  就是这个'温度'信息
		/// </summary>
		/// <param name="nodes">节点信息</param>
		/// <returns>子类信息，如果为空就返回空</returns>
		public string GetDeviceChildInfo( string[] nodes )
		{
			if (!IsCurrentDevice( nodes )) return string.Empty;
			if (nodes.Length <= DeviceNodes.Length) return string.Empty;
			return nodes[DeviceNodes.Length];
		}

		/// <summary>
		/// 判断当前的设备是否处于指定的路径下面，根据参数<paramref name="exactMatch"/>参数决定刚好子路径还是任意子路径
		/// </summary>
		/// <param name="paths">路径的组合信息</param>
		/// <param name="exactMatch">是否完全匹配，true的话就是刚好子路径下，false的话，就是任意子路径下都可以的</param>
		/// <returns>是否包含的结果</returns>
		public bool InSpecifiedPath( string[] paths, bool exactMatch = true )
		{
			if (DeviceNodes != null && paths != null)
			{
				if (exactMatch)
				{
					if (paths.Length != DeviceNodes.Length - 1) return false;
					for (int i = 0; i < DeviceNodes.Length - 1; i++)
					{
						if (DeviceNodes[i] != paths[i])
						{
							return false;
						}
					}
					return true;
				}
				else
				{
					if (paths.Length > DeviceNodes.Length - 1) return false;
					for (int i = 0; i < paths.Length; i++)
					{
						if (DeviceNodes[i] != paths[i])
						{
							return false;
						}
					}
					return true;
				}
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 获取本设备的名词信息，带有自由路径的格式
		/// </summary>
		/// <returns>带有自由路径的设备名称信息</returns>
		public string GetDeviceNameWithPath( )
		{
			if (DeviceNodes != null && DeviceNodes.Length > 1)
			{
				StringBuilder sb = new StringBuilder( );
				for (int i = 0; i < DeviceNodes.Length - 1; i++)
				{
					sb.Append( DeviceNodes[i] );
					sb.Append( HslTechnologyExtension.DeviceDefaultSplit );
				}
				sb.Append( Name );
				return sb.ToString( );
			}
			return Name;
		}

		/// <summary>
		/// 获取当前上传MQTT服务器的主题的前置数据信息
		/// </summary>
		/// <returns>主题信息</returns>
		public string GetMqttTopic( )
		{
			if (string.IsNullOrEmpty( this.mqttTopicPre )) return GetDeviceNameWithPath( );
			return this.mqttTopicPre + Name;
		}

		#endregion

		#region Value Get Write

		/// <summary>
		/// 获取本设备对象的值信息
		/// </summary>
		/// <param name="nodes">节点数据</param>
		/// <returns>值信息数据</returns>
		public JToken GetValueByName( string[] nodes )
		{
			if (nodes.Length == DeviceNodes.Length)
			{
				return deviceData.DeviceJsonClone;
			}
			else if (nodes.Length > DeviceNodes.Length)
			{
				// 可以先去词典中找找看有没有缓存数据信息，然后再来从deviceData里寻找相关的数据
				return GetValueByTagName( nodes[DeviceNodes.Length] );
			}
			else if (nodes.Length == 1)
			{
				// 可以先去词典中找找看有没有缓存数据信息，然后再来从deviceData里寻找相关的数据
				return GetValueByTagName( nodes[DeviceNodes.Length] );
			}
			else
			{
				return string.Empty;
			}
		}

		public JToken GetValueByTagName( string name )
		{
			// 可以先去词典中找找看有没有缓存数据信息，然后再来从deviceData里寻找相关的数据
			if (dictDeviceSingleAddress.ContainsKey( name ))
				return dictDeviceSingleAddress[name].Value;
			return deviceData.GetJTokenValueByName( name );
		}

		/// <summary>
		/// 根据数据的路径信息，获取指定原始字节请求的结果数据
		/// </summary>
		/// <param name="nodes">节点信息</param>
		/// <returns>是否读取成功的结果对象</returns>
		public OperateResult<byte[]> GetSourceRequestCahce( string[] nodes )
		{
			if (nodes.Length == DeviceNodes.Length)
			{
				// 不能指定设备本身，必须指定一个 SourceRequest 请求名字
				return new OperateResult<byte[]>( "无法指定设备本身，需要指定设备的一个原始字节请求" );
			}
			else if (nodes.Length > DeviceNodes.Length)
			{
				// 可以先去词典中找找看有没有缓存数据信息，然后再来从deviceData里寻找相关的数据
				lock (localSourceRequestCache)
				{
					if (localSourceRequestCache.ContainsKey( nodes[DeviceNodes.Length] ))
						return localSourceRequestCache[nodes[DeviceNodes.Length]];
				}
				return new OperateResult<byte[]>( "当前的原始字节请求不存在，请重新指定" );
			}
			else
			{
				return new OperateResult<byte[]>( "不正确的路径信息，请检查当前的路径" );
			}
		}

		/// <summary>
		/// 根据数据点信息，获取当前的地址标签的地址映射关系
		/// </summary>
		/// <param name="nodes">节点数据</param>
		/// <returns>地址映射关系</returns>
		public OperateResult<DeviceSingleAddressLabel> GetAddressLabelByTagName( string[] nodes )
		{
			if (nodes.Length == DeviceNodes.Length)
			{
				return new OperateResult<DeviceSingleAddressLabel>( "当前不能对设备节点进行操作！" );
			}
			else if (nodes.Length > DeviceNodes.Length)
			{
				// 可以先去词典中找找看有没有缓存数据信息，然后再来从deviceData里寻找相关的数据
				if (dictDeviceSingleAddress.ContainsKey( nodes[DeviceNodes.Length] ))
					return OperateResult.CreateSuccessResult( dictDeviceSingleAddress[nodes[DeviceNodes.Length]] );
				return new OperateResult<DeviceSingleAddressLabel>( "当前的设备节点无法获取到地址信息！" );
			}
			else
			{
				return new OperateResult<DeviceSingleAddressLabel>( "当前的节点找不到对应的数据内容！" );
			}
		}

		private DateTime tryWriteWhenOfflineTime = DateTime.MinValue;
		private object lock_tryWrite = new object( );


		private OperateResult WriteValueByName( string tagName, string value, bool changeThread )
		{
			if (dictDeviceSingleAddress.ContainsKey( tagName ))
			{
				DeviceSingleAddressLabel addressLabel = dictDeviceSingleAddress[tagName];
				if (!addressLabel.Enable) return new OperateResult( "当前的节点被设置为禁用模式，请联系管理员！" );
				if (addressLabel.ForbidRemoteWrite) return new OperateResult( "当前的节点不允许写入数据操作！请联系管理员！" );
				if (addressLabel.BindingScalarCacheRequest( ))
				{
					// 绑定了临时变量的信息
					IScalarTransform transform = addressLabel.ScalarTransform;
					try
					{
						if (transform.IsBoolRegular( ))
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetBoolValue( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetBoolArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.Byte.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetByteValue( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( value.ToHexBytes( ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetSByteValue( transform, value ) ) );
							byte[] array = HslTechnologyHelper.GetByteArrayFrom( RegularHelper.GetSByteArrayValue( transform, value ) );
							return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( array ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.Int16.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetInt16Value( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetInt16ArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.UInt16.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetUInt16Value( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetUInt16ArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.Int32.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetInt32Value( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetInt32ArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.UInt32.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetUInt32Value( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetUInt32ArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.Int64.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetInt64Value( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetInt64ArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetUInt64Value( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetUInt64ArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.Float.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetFloatValue( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetFloatArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.Double.Text)
						{
							if (transform.Length < 0)
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetDoubleValue( transform, value ) ) );
							else
								return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetDoubleArrayValue( transform, value ) ) );
						}
						else if (transform.DataTypeCode == RegularNodeTypeItem.String.Text || transform.DataTypeCode == RegularNodeTypeItem.BCD.Text )
							return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( value ) );
						else if (transform.DataTypeCode == RegularNodeTypeItem.IntOfString.Text )
							return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetInt64Value( transform, value ) ) );
						else if (transform.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
							return BussinessDataHelper( addressLabel.Name, addressLabel.GetScalarCacheRequest( ), OperateResult.CreateSuccessResult( RegularHelper.GetDoubleValue( transform, value ) ) );
						else
							return new OperateResult( $"不支持的数据格式：{transform.DataTypeCode}" );
					}
					catch (Exception ex)
					{
						return new OperateResult( $"当前的写入操作发生了异常：{ex.Message}" );
					}
				}
				else
				{
					if (string.IsNullOrEmpty( addressLabel.PhysicalAddress )) return new OperateResult( "当前的节点不允许写入数据操作！请联系管理员！" );

					if (!this.DeviceStatus)
					{
						bool tryWrite = false;
						lock (lock_tryWrite)
						{
							DateTime now = HslTechnologyHelper.GetDateTimeNow( );
							if ((now - tryWriteWhenOfflineTime).TotalSeconds > 30)
							{
								tryWrite = true;
								tryWriteWhenOfflineTime = now;
							}
						}
						// 即使设备不在线的话，这里也尝试每30秒尝试一次写入的操作
						if (tryWrite == false) return new OperateResult( "当前的设备不在线，请等待设备在线的时候再写入数据！" );
					}
					else
					{
						tryWriteWhenOfflineTime = DateTime.MinValue;
					}


					try
					{
						// OperateResult write = WriteValueByName( addressLabel, value );
						OperateResult write = changeThread ? (ExecuteThreadInsertMission( new ThreadInsertMission( )
						{
							DeviceOpreate = new Func<DeviceCoreBase, object>( device =>
							{
								return device.WriteValueByName( addressLabel, value );
							} )
						} ) as OperateResult) : WriteValueByName( addressLabel, value );
						if (write.IsSuccess)
						{
							this.deviceThreadState.LastRequestSuccessTime = HslTechnologyHelper.GetDateTimeNow( );
						}
						return write;
					}
					catch (Exception ex)
					{
						return new OperateResult( $"当前写入操作失败，输入的字符串需要支持转化成类型为 {addressLabel.ScalarTransform.DataTypeCode} 的数据，数据转化失败！" + ex.Message );
					}
				}
			}
			else
			{
				return new OperateResult( "当前的数据节点在设备上不存在" );
			}
		}

		/// <summary>
		/// 写入本设备的值数据信息，根据传入的数据节点名称来匹配写入操作，例如 分厂一/车间二/设备A/温度
		/// </summary>
		/// <param name="nodes">节点数据</param>
		/// <param name="value">等待写入的值</param>
		/// <returns>是否成功写入</returns>
		public OperateResult WriteValueByName( string[] nodes, string value )
		{
			if (nodes.Length == 1) return WriteValueByName( nodes[0], value, changeThread: true );
			return WriteValueByName( nodes[DeviceNodes.Length], value, changeThread: true );
		}

		/// <summary>
		/// 根据实际的设备地址标签来写入相关的变量数据信息
		/// </summary>
		/// <param name="addressLabel">地址标签对象</param>
		/// <param name="value">写入的字符串值，需要转为实际的值</param>
		/// <returns>是否写入成功，这个结果将返回给远程客户端对象</returns>
		public virtual OperateResult WriteValueByName( DeviceSingleAddressLabel addressLabel, string value )
		{
			return new OperateResult( "当前的写入操作不支持，请等待后续的完善。" );
		}

		/// <summary>
		/// 获取设备的方法接口列表信息
		/// </summary>
		/// <returns>设备方法接口</returns>
		public OperateResult<MethodRpcInfo[]> GetMethodInfoByDeviceID( )
		{
			return OperateResult.CreateSuccessResult( this.methodRpcInfosBuffer );
		}

		/// <summary>
		/// 执行调度当前的方法调用，通常来自于远程客户端的方法调用，允许客户端的设备进行重写，然后进行更加细致的账户控制权限。
		/// </summary>
		/// <param name="admin">是否管理员账户进行方法调用</param>
		/// <param name="method">方法的名称信息</param>
		/// <param name="parameterJson">当前的参数信息</param>
		/// <returns>方法执行后的调用结果信息</returns>
		public virtual async Task<object> CallDeviceMethod( bool admin, string method, string parameterJson )
		{
			if (!this.dictRpcApiInfos.ContainsKey( method )) return new OperateResult( $"当前的方法[{method}]接口不存在，请重新确认接口信息！" );

			MethodRpcInfo methodRpcInfo = this.dictRpcApiInfos[method];
			object retObject = null;
			object[] paras = HslReflectionHelper.GetParametersFromJson( null, methodRpcInfo.RpcApiInfo.Method.GetParameters( ), parameterJson );

			object obj = methodRpcInfo.RpcApiInfo.Method.Invoke( methodRpcInfo.RpcApiInfo.SourceObject, paras );
			if (obj is Task task)
			{
				await task;
				retObject = task.GetType( ).GetProperty( "Result" )?.GetValue( task, null );
			}
			else
			{
				retObject = obj;
			}

			if (retObject == null) return OperateResult.CreateSuccessResult( );
			if (retObject.GetType( ) == typeof( void ) || retObject.GetType( ) == typeof( Task )) return OperateResult.CreateSuccessResult( );
			// if (methodRpcInfo.ExtensionInfoAttribute.DataType == DataType.Method) return OperateResult.CreateSuccessResult(  );
			if (retObject is OperateResult operateResult)
			{
				if (!operateResult.IsSuccess) return operateResult;
				if (retObject.GetType( ) == typeof( OperateResult )) return OperateResult.CreateSuccessResult( );
				PropertyInfo property = retObject.GetType( ).GetProperty( "Content" );
				if (property != null)
				{
					return OperateResult.CreateSuccessResult( property.GetValue( retObject ) );
				}
				else
				{
					JObject json = new JObject( );
					for (int i = 1; i <= 10; i++)
					{
						PropertyInfo propertyJson = retObject.GetType( ).GetProperty( "Content" + i.ToString( ) );
						if (propertyJson != null)
						{
							json.Add( "Content" + i.ToString( ), JToken.FromObject( propertyJson.GetValue( retObject ) ) );
						}
						else
						{
							break;
						}
					}
					return OperateResult.CreateSuccessResult( json );
				}
			}
			else
			{
				return OperateResult.CreateSuccessResult( retObject );
			}
		}

		/// <summary>
		/// 设备继续启动请求
		/// </summary>
		/// <param name="requestName">指定的请求名称，如果不指定，则直接停止所有的请求</param>
		public void DeviceContinueRequest( string requestName = null )
		{
			if (string.IsNullOrEmpty( requestName ))
			{
				this.allRequestEnbale = true;
				this.deviceData.SetSingleValue( "__requestEnable", this.allRequestEnbale );
				this.deviceData.UpdateJsonTmp( );
			}
			else
			{
				for (int i = 0; i < this.Requests.Count; i++)
				{
					if (this.Requests[i].Name.Equals( requestName ))
					{
						this.Requests[i].Enable = true;
						break;
					}
				}
			}
		}

		/// <summary>
		/// 设备停止请求
		/// </summary>
		/// <param name="requestName">指定的请求名称，如果不指定，则直接停止所有的请求</param>
		public void DeviceStopRequest( string requestName = null )
		{
			if (string.IsNullOrEmpty( requestName ))
			{
				this.allRequestEnbale = false;
				this.deviceData.SetSingleValue( "__requestEnable", this.allRequestEnbale );
				this.deviceData.UpdateJsonTmp( );
			}
			else
			{
				for ( int i = 0; i < this.Requests.Count; i++ )
				{
					if (this.Requests[i].Name.Equals( requestName) )
					{
						this.Requests[i].Enable = false;
						break;
					}
				}
			}
		}

		/// <summary>
		/// 获取某个请求的实际请求次数，如果传入请求名称为空，则获取所有请求的次数之和
		/// </summary>
		/// <param name="requestName">请求名称</param>
		/// <returns>请求次数之和</returns>
		public OperateResult<long> DeviceRequestCount( string requestName )
		{
			if (string.IsNullOrEmpty( requestName ))
			{
				// 所有请求的数量和
				long count = 0;
				for (int i = 0; i < this.Requests.Count; i++)
				{
					count += this.Requests[i].RequestInfactCount;
				}
				return OperateResult.CreateSuccessResult( count );
			}

			for (int i = 0; i < this.Requests.Count; i++)
			{
				if (this.Requests[i].Name.Equals( requestName ))
				{
					return OperateResult.CreateSuccessResult( this.Requests[i].RequestInfactCount );
				}
			}
			return new OperateResult<long>( $"Request[{requestName}] is not exist" );
		}

		#endregion

		#region Virtual Method

		/// <summary>
		/// 设备在启动之前进行的操作信息，例如连接对象实例化，长连接设置<br />
		/// Operation information performed before the device is started, such as instantiation of the connection object, long connection settings
		/// </summary>
		protected virtual void BeforStart( ) { }

		/// <summary>
		/// 在关闭的时候需要进行的操作，将设备的连接对象进行关闭，或是其他额外的操作信息<br />
		/// The operation that needs to be performed when shutting down, closing the connected object of the device, or other additional operation information
		/// </summary>
		protected virtual void AfterClose( ) { }

		/// <summary>
		/// 强制设置设备的错误消息，无法通过实时消息刷新，除非被重新设置为空
		/// </summary>
		/// <param name="key">消息的关键字，为空则不处理</param>
		/// <param name="msg">强制的消息</param>
		public void SetDeviceForceMessgae( string key, string msg )
		{
			if (string.IsNullOrEmpty( key )) return;

			this.deviceMessage.AddStringMsg( key, msg );
			this.deviceData.SetStringValue( "__deviceMessage", this.deviceMessage.GetDefaultStringMsg( ) );
			this.deviceData.UpdateJsonTmp( );
		}

		/// <summary>
		/// 在调用方法的请求时，需要调用客户端的重写方法
		/// </summary>
		/// <param name="request">方法请求</param>
		/// <returns>调用结果</returns>
		private async Task<OperateResult> CallMethodRequestExecute( RequestBase request )
		{
			if (request is CallMethodRequest callMethodRequest)
			{
				if (this.dictRpcApiInfos.ContainsKey( request.Address ))
				{
					MethodRpcInfo methodRpcInfo = this.dictRpcApiInfos[request.Address];
					object retObject = null;
					object[] paras = null;
					try
					{
						paras = HslReflectionHelper.GetParametersFromJson( null, methodRpcInfo.RpcApiInfo.Method.GetParameters( ), callMethodRequest.ParameterJson );
					}
					catch(Exception ex)
					{
						return new OperateResult( ex.Message );
					}

					try
					{
						object obj = methodRpcInfo.RpcApiInfo.Method.Invoke( methodRpcInfo.RpcApiInfo.SourceObject, paras );
						if (obj is Task task)
						{
							await task;
							retObject = task.GetType( ).GetProperty( "Result" )?.GetValue( task, null );
						}
						else
						{
							retObject = obj;
						}
					}
					catch (Exception ex)
					{
						return new OperateResult( "Invoke Method failed: " + ex.Message );
					}

					if (methodRpcInfo.ExtensionInfoAttribute.DataType == DataType.Method) return OperateResult.CreateSuccessResult( );
					if (retObject == null) return OperateResult.CreateSuccessResult( );
					if (retObject is OperateResult operateResult)
					{
						if (!operateResult.IsSuccess)
						{
							if (resetNullIfFaield) SetJsonObjectValue( request.Name, GetNullJToken( ) );
							return operateResult;
						}
						if (retObject.GetType( ) == typeof( OperateResult )) return OperateResult.CreateSuccessResult( );
						PropertyInfo property = retObject.GetType( ).GetProperty( "Content" );
						if (property != null)
						{
							DealMethodInvokeResult( property.GetValue( retObject ), request, methodRpcInfo );
						}
						else
						{
							JObject json = new JObject( );
							for (int i = 1; i <= 10; i++)
							{
								PropertyInfo propertyJson = retObject.GetType( ).GetProperty( "Content" + i.ToString( ) );
								if (propertyJson != null)
								{
									json.Add( "Content" + i.ToString( ), JToken.FromObject( propertyJson.GetValue( retObject ) ) );
								}
								else
								{
									break;
								}
							}
							SetJsonObjectValue( request.Name, json );
						}
					}
					else
					{
						DealMethodInvokeResult( retObject, request, methodRpcInfo );
					}
				}
			}
			return OperateResult.CreateSuccessResult( );
		}

		private void DealMethodInvokeResult( object obj, RequestBase request, MethodRpcInfo methodRpcInfo )
		{
			RpcExtensionInfoAttribute rpcExtension = methodRpcInfo.ExtensionInfoAttribute;
			if (rpcExtension.DataDimension == DataDimension.Scalar)
			{
				//bool value = (bool)obj;
				//if (dictDeviceSingleAddress.ContainsKey( request.Name )) dictDeviceSingleAddress[request.Name].SetNewSingleValue( value, this.edgeResources );
				//deviceData.SetSingleValue( request.Name, value );
				switch (rpcExtension.DataType)
				{
					case DataType.Bool:
					case DataType.Byte:
					case DataType.SByte:
					case DataType.Int16:
					case DataType.UInt16:
					case DataType.Int32:
					case DataType.UInt32:
					case DataType.Int64:
					case DataType.UInt64:
					case DataType.Float:
					case DataType.Double:
					case DataType.DateTime:
					case DataType.Guid:
					case DataType.String:
					case DataType.BCD:
						{
							SetJsonValue( request.Name, obj, false );
							break;
						}

					case DataType.Hex:
						{
							if(obj is byte[] buffer)
							{
								SetJsonValue( request.Name, buffer.ToHexString( ), false );
							}
							else
							{
								SetJsonValue( request.Name, string.Empty, false );
							}
							break;
						}
					case DataType.Struct:
					case DataType.Class:
						{
							SetJsonObjectValue( request.Name, JObject.FromObject( obj ) );
							break;
						}
				}
			}
			else if (rpcExtension.DataDimension == DataDimension.One)
			{
				switch (rpcExtension.DataType)
				{
					case DataType.Bool:
					case DataType.Byte:
					case DataType.SByte:
					case DataType.Int16:
					case DataType.UInt16:
					case DataType.Int32:
					case DataType.UInt32:
					case DataType.Int64:
					case DataType.UInt64:
					case DataType.Float:
					case DataType.Double:
					case DataType.DateTime:
					case DataType.Guid:
					case DataType.String:
					case DataType.BCD:
						{
							SetJsonValue( request.Name, obj, true );
							break;
						}
					case DataType.Struct:
					case DataType.Class:
						{
							SetJsonObjectValue( request.Name, JArray.FromObject( obj ) );
							break;
						}
				}
			}
			else
			{
				SetJsonObjectValue( request.Name, JToken.FromObject( obj ) );
			}
		}

#pragma warning disable CS1998 // 异步方法缺少 "await" 运算符，将以同步方式运行
		/// <summary>
		/// 每秒钟执行的方法
		/// </summary>
		/// <param name="second">秒数信息，0-59</param>
		protected virtual async Task EverySecondsExecuteMethod( int second ) { }

		/// <summary>
		/// 每分钟执行的方法
		/// </summary>
		/// <param name="minute">分钟信息 0-59</param>
		protected virtual async Task EveryMinuteExecuteMethod( int minute ) { }

		/// <summary>
		/// 每个小时会指定的方法
		/// </summary>
		/// <param name="hour">小时信息 0-23</param>
		protected virtual async Task EveryHourExecuteMethod( int hour ) { }

		/// <summary>
		/// 每天会触发的方法
		/// </summary>
		/// <param name="day">日期信息 1 - 31</param>
		protected virtual async Task EveryDayExecuteMethod( int day ) { }

		/// <summary>
		/// 检查当前的设备的状态，进行连接一次设备，如果连接失败或是打开串口失败，则返回错误消息，用于客户端测试设备的网络状态<br />
		/// Check the current device status and connect to the device once. If the connection fails or the serial port fails to open, 
		/// an error message will be returned for the client to test the network status of the device
		/// </summary>
		/// <returns>是否成功的状态信息，如果失败，请检查携带的<see cref="OperateResult.Message"/>消息</returns>
		public virtual OperateResult<string> CheckDeviceStatus( ) => new OperateResult<string>( -1, "The current function is not implemented" );

		/// <summary>
		/// 当通道异常的时候，通常是返回的错误码小于0的时候，进行执行和调用的方法。比如有些串口需要进行一些额外的操作，发送一个特殊的激活报文之类的。<br />
		/// When the channel is abnormal, it is usually the method to execute and call when the returned error code is less than 0. For example, 
		/// some serial ports need to perform some additional operations, such as sending a special activation message.
		/// </summary>
		protected virtual void ExecuteAfterPipeWrong( ) { }

		/// <summary>
		/// 当设备连续通信异常的时候，如果设备配置了冗余的功能，那么这里就支持切换冗余的PLC的IP地址。<br />
		/// When the continuous communication of the device is abnormal, if the device is configured with the redundant function, the IP address of the redundant PLC can be switched.
		/// </summary>
		/// <returns>是否发生了真的切换</returns>
		protected virtual bool RedundantAfterPipeWrong( ) => false;

		/// <summary>
		/// 真正读取的方法，需要进行相关的重写操作
		/// </summary>
		protected virtual async Task<OperateResult> ReadActualAsync( RequestBase request )
		{
			return new OperateResult( -1, "The current function is not implemented" );
		}

		/// <summary>
		/// 真正写入数据的方法，需要进行相关的重写操作
		/// </summary>
		/// <param name="request">写入的请求信息</param>
		/// <param name="value">等待写入的值信息</param>
		/// <returns>是否写入成功</returns>
		protected virtual async Task<OperateResult> WriteActualAsync( ScalarWriteRequest request, string value ) => new OperateResult( -1, "The current function is not implemented" );

#pragma warning restore CS1998 // 异步方法缺少 "await" 运算符，将以同步方式运行

		#endregion

		#region Thread Read

		/// <summary>
		/// 指定一个插队任务，这个任务将在设备线程中执行，返回一个 Object 对象，可由自己决定这个类型
		/// </summary>
		/// <param name="mission">任务</param>
		/// <returns>结果对象</returns>
		public object ExecuteThreadInsertMission( ThreadInsertMission mission )
		{
			this.threadInsertMissions.Enqueue( mission );
			DateTime timestart = DateTime.Now;
			// 等待执行完成
			while( true)
			{
				HslCommunication.Core.HslHelper.ThreadSleep( 20 );
				if ( mission.Finished)
				{
					return mission.Result;
				}

				if ((DateTime.Now - timestart).TotalSeconds > mission.TimeoutSeconds)
				{
					return null;
				}
			}
		}

		/// <summary>
		/// 执行线程，调度每个写入的任务，有就进行写入操作
		/// </summary>
		public void ExecuteThreadInsertMission( )
		{
			if (this.threadInsertMissions.Count <= 0) return;
			while (true)
			{
				if ( this.threadInsertMissions.TryDequeue( out ThreadInsertMission mission ))
				{
					if (mission == null) break;
					try
					{
						mission.Result = mission.DeviceOpreate.Invoke( this );

						this.deviceThreadState.UpdateActiveTime( );              // 更新线程活动时间
						LogNet?.WriteInfo( GetDeviceNameWithPath( ), GetDeviceNameWithPath( ), $"ExecuteThreadInsertMission 执行了写入操作" );
					}
					catch (Exception ex)
					{
						LogNet?.WriteError( GetDeviceNameWithPath( ), GetDeviceNameWithPath( ), $"ExecuteThreadInsertMission 执行了写入失败: " + ex.Message );
					}
					mission.Finished = true;
				}
				else
				{
					break;
				}
			}
		}


		private async Task TimeCheckExcute( DeviceThreadState deviceThreadState, DateTime pre, DateTime now )
		{
			if (DeviceThreadState.IsSecondsHappen( pre, now ))  // 每秒执行的
			{
				await EverySecondsExecuteMethod( now.Second );
			}
			if (DeviceThreadState.IsMinuteHappen( pre, now))     // 每分钟执行
			{
				await EveryMinuteExecuteMethod( now.Minute );
			}
			if (DeviceThreadState.IsHourHappen( pre, now))       // 每小时执行
			{
				await EveryHourExecuteMethod( now.Hour );
			}
			if (DeviceThreadState.IsDayHappen( pre, now))        // 每天执行
			{
				await EveryDayExecuteMethod( now.Day );
			}
		}


		public async Task<ThreadCycleRunReturn> ThreadCycleRunAction( )
		{
			if (!Authorization.asdniasnfaksndiqwhawfskhfaiw( ))
			{
				this.logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"软件未进行授权，试用结束，使用时间为：{this.deviceThreadState.GetRunTimeDescription( )}，采集线程关闭！" );
				return ThreadCycleRunReturn.Break;
			}
			// 执行线程插队任务
			ExecuteThreadInsertMission( );
			this.deviceThreadExecute?.ExecuteEveryDeviceMission( );   // 看看线程控制器里有没有需要执行的任务

			// 执行差异时间
			await this.deviceThreadState.UpdateActiveTime( HslTechnologyHelper.GetDateTimeNow( ) ).ConfigureAwait( false );              // 更新线程活动时间
			if (this.isQuit > 0) return ThreadCycleRunReturn.Break;                                                                      // 如果退出，就立即退出了
			if (!this.allRequestEnbale) return ThreadCycleRunReturn.Continue;                                                            // 如果暂停请求，就跳过

			DateTime requestStart = this.deviceThreadState.ThreadActiveTime;
			ThreadCycleObject cycleObject = new ThreadCycleObject( );
			this.deviceThreadState.ResetCountTick( );                                                            // 重置计数统计
			for (int i = 0; i < Requests.Count; i++)
			{
				var request = Requests[i];
				if (request.DeviceActual == null) request.DeviceActual = this;

				if (request.RequestGroup == null || request.RequestGroup.DataSyncUpdate == false)
				{
					ThreadCycleRunReturn requestReturn = await ThreadDealWithRequest( request, requestStart, cycleObject );  // 执行请求
					if (requestReturn == ThreadCycleRunReturn.Break) break;
					if (requestReturn == ThreadCycleRunReturn.Continue) continue;
					if (this.requestIntervalSleep > 0) HslCommunication.Core.HslHelper.ThreadSleep( this.requestIntervalSleep );  // 成功执行请求之后，如果配置了休眠时间，则进行休眠操作
				}
				else
				{
					// 表示当前是一个数据同步的请求开始
					this.deviceData.StartStoreTemp( );    // 开启数据缓存
					ThreadCycleRunReturn requestReturn = await ThreadDealWithRequest( request, requestStart, cycleObject );
					if (requestReturn == ThreadCycleRunReturn.Break)
					{
						this.deviceData.FinishStoreTemp( hasData: false );
						break;
					}
					if (requestReturn == ThreadCycleRunReturn.Continue)
					{
						this.deviceData.FinishStoreTemp( hasData: false );
						// 这里跳过本次请求，并且要跳过同一个分组的请求
						int requestGroupCount = GetRequestGroupCount( i, request.RequestGroup );
						i += requestGroupCount - 1;  // 这里的减一是减去本次的请求计数
					}
					else
					{
						if (this.requestIntervalSleep > 0) HslCommunication.Core.HslHelper.ThreadSleep( this.requestIntervalSleep );  // 成功执行请求之后，如果配置了休眠时间，则进行休眠操作

						// 这里就需要真正的一起请求了
						int requestGroupCount = GetRequestGroupCount( i, request.RequestGroup );
						for (int j = 1; j < requestGroupCount; j++)
						{
							ThreadCycleRunReturn requestNext = await ThreadDealWithRequest( Requests[i + j], requestStart, cycleObject, ThreadCycleRunReturn.Request );
							if (requestNext == ThreadCycleRunReturn.Break) break;
							if (requestNext == ThreadCycleRunReturn.Continue) continue;

							if (this.requestIntervalSleep > 0) HslCommunication.Core.HslHelper.ThreadSleep( this.requestIntervalSleep );  // 成功执行请求之后，如果配置了休眠时间，则进行休眠操作
						}
						this.deviceData.FinishStoreTemp( );  // 请求完成之后更新数据缓存
						i += requestGroupCount - 1;  // 这里的减一是减去本次的请求计数
					}
				}
			}

			if (isQuit > 0) return ThreadCycleRunReturn.Break;                                          // 如果退出，就立即退出了
			if (deviceThreadState.RequestCount > 0)
			{
				if (deviceThreadState.CycleCaptureFailed == 0)
				{
					// 全部采集成功
					BusinessEngine.Business.DealDeviceOnlineTimes( GetDeviceNameWithPath( ), true );     // 处理在线
					this.logNet?.WriteInfo( GetDeviceNameWithPath( ), ToString( ), $"采集数据成功[{deviceThreadState}]！耗时: " + (HslTechnologyHelper.GetDateTimeNow( ) - requestStart).TotalMilliseconds.ToString( "F0" ) + " ms" );
				}
				else if (deviceThreadState.CycleCaptureSuccess > 0)
				{
					// 部分采集成功
					BusinessEngine.Business.DealDeviceOnlineTimes( GetDeviceNameWithPath( ), true );     // 处理在线
					this.logNet?.WriteWarn( GetDeviceNameWithPath( ), ToString( ), $"采集数据部分成功[{deviceThreadState}]！耗时: " + (HslTechnologyHelper.GetDateTimeNow( ) - requestStart).TotalMilliseconds.ToString( "F0" ) + " ms" );
				}
				else
				{
					// 全部采集失败
					BusinessEngine.Business.DealDeviceOnlineTimes( GetDeviceNameWithPath( ), false );    // 处理离线
					this.logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"采集数据失败[{deviceThreadState}] 持续: {this.deviceThreadState.GetOfflineTimeDescription( )}" );
				}


				if (isQuit > 0) return ThreadCycleRunReturn.Break;                                           // 如果退出，就立即退出了，否则更新Json字符串缓存
				this.deviceData.SetSingleValue( "__success",    this.RequestSuccessCount );                 // 成功请求次数
				this.deviceData.SetSingleValue( "__failed",     this.RequestFailedCount );                  // 失败请求次数
				this.deviceData.SetSingleValue( "__activeTime", this.deviceThreadState.LastRequestSuccessTime.ToEdgeString( ) );
				this.deviceData.SetSingleValue( "__failedMsg",  cycleObject.FailedMsg );
				this.deviceData.SetJToken(      "__alarmCount", BusinessEngine.Business.GetAlarmJsonCount( GetDeviceNameWithPath( ) ) );
				// if (!string.IsNullOrEmpty( this.deviceMessage.GetDefaultStringMsg( ) )) UpdateDeviceOnlineStatus( false );
				if (deviceThreadState.RequestCount > 0) this.deviceData.SetSingleValue( "__captureSpendTime", (int)(DateTime.Now - requestStart).TotalMilliseconds );
				this.deviceData.UpdateJsonTmp( );
			}
			else
			{
				if (this.deviceThreadState.IsUpdateCacheFromRequestInterval( requestStart ))
				{
					// 本次循环，没有任何的数据请求，仍然保持每隔1秒进行一次分析设备状态及报警数量信息
					if (!string.IsNullOrEmpty( this.deviceMessage.GetDefaultStringMsg( ) )) UpdateDeviceOnlineStatus( false );
					else
					{
						UpdateDeviceActiveTimeWhemNoneRequest( );
					}
					this.deviceData.SetSingleValue( "__success", this.RequestSuccessCount );                 // 成功请求次数
					this.deviceData.SetSingleValue( "__failed",  this.RequestFailedCount );                  // 失败请求次数
					this.deviceData.SetJToken( "__alarmCount", BusinessEngine.Business.GetAlarmJsonCount( GetDeviceNameWithPath( ) ) );
					this.deviceData.UpdateJsonTmp( );
				}
			}
			return deviceThreadState.RequestCount > 0 ? ThreadCycleRunReturn.Request : ThreadCycleRunReturn.None;
		}

		private Interpreter interpreter;
		private List<string> CreateParametersFromString( string value )
		{
			List<string> list = new List<string>( );
			foreach (var item in dictDeviceSingleAddress)
			{
				if (value.Contains( item.Key ))
				{
					// 结构体的话，应该赋值整个结构体的数据信息
					if (item.Value.DataType == DataType.Struct)
					{

					}

					if (!item.Key.Contains( "." ))
					{
						list.Add( item.Key );
					}
				}
			}
			return list;
		}
		private bool ExecuteRequestCondition( RequestBase request )
		{
			if (interpreter == null) interpreter = new Interpreter( );
			bool requestExexuteFirst = false;
			if (request.AdditionalParameterNames == null)
			{
				requestExexuteFirst = true;
				request.AdditionalParameterNames = CreateParametersFromString( request.AdditionalConditions );
			}
			List<Parameter> parameters = new List<Parameter>( );
			// 赋值参数的操作数据
			foreach (var item in request.AdditionalParameterNames)
			{
				if (dictDeviceSingleAddress.ContainsKey( item ))
				{
					parameters.Add( new Parameter( item, dictDeviceSingleAddress[item].GetDynamicValue( ) ) );
				}
			}

			// 如果设置了附加条件的话，这里还需要验证下附加条件
			bool result = false;
			bool enable = (bool)interpreter.Eval( request.AdditionalConditions, parameters.ToArray( ) );
			if (!requestExexuteFirst)                       // 第一次判断跳过，从请求的第二次开始判断
			{
				if (request.ConditionsEnableType == 1)
				{
					// 上升沿的附加条件
					if (!request.AdditionalConditionLast && enable)
					{
						result = true;
					}
				}
				else if (request.ConditionsEnableType == 2)
				{
					// 下升沿的附加条件
					if (request.AdditionalConditionLast && !enable)
					{
						result = true;
					}
				}
				else if (request.ConditionsEnableType == 3)
				{
					// 持续的信号，只要满足就触发
					result = enable;
				}
			}
			request.AdditionalConditionLast = enable;
			return result;
		}

		private async Task<ThreadCycleRunReturn> ThreadDealWithRequest( RequestBase request, DateTime requestStart, ThreadCycleObject cycleObject, ThreadCycleRunReturn requetState = ThreadCycleRunReturn.None )
		{
			if (isQuit > 0) return ThreadCycleRunReturn.Break;                                                                                 // 如果系统退出，则直接退出
			if (!request.Enable) return ThreadCycleRunReturn.Continue;                                                                         // 如果当前的请求被禁用，则直接忽略
			if (!request.DeviceActual.allRequestEnbale) return ThreadCycleRunReturn.Break;                                                     // 如果暂停请求，就跳过

			if (request.RequestCount >= 0 && request.RequestInfactCount >= request.RequestCount) return ThreadCycleRunReturn.Continue;         // 请求次数超过设定的最大值
			if (request.RequestType == RequestType.ScalarCache) return ThreadCycleRunReturn.Continue;                                          // 如果是标量缓存请求，则跳过该请求

			if (requetState == ThreadCycleRunReturn.Continue) return ThreadCycleRunReturn.Continue;                                            // 如果强制本次请求跳过，则直接跳过
			if (requetState == ThreadCycleRunReturn.None)
			{
				if (!request.CheckIfRequestAndUpdateTime( requestStart )) return ThreadCycleRunReturn.Continue;                                              // 当前的请求还没到请求时间
			}
			// 如果设置了附加条件的话，这里还需要验证下附加条件
			if (request.ConditionsEnableType > 0)
			{
				try
				{
					if (!ExecuteRequestCondition( request )) return ThreadCycleRunReturn.Continue;                     // 附加的条件不满足执行情况
				}
				catch( Exception ex )
				{
					logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"Eval RequestCondition failed, Request[{request.Name}]: " + ex.Message );
					return ThreadCycleRunReturn.Continue;
				}
			}

			await request.DeviceActual.deviceThreadState.UpdateActiveTime( HslTechnologyHelper.GetDateTimeNow( ) ).ConfigureAwait( false );          // 更新线程活动时间
			if (this.deviceThreadExecute != null)
				this.deviceThreadExecute.UpdateThreadActiveTime( );

			request.RequestInfactCount++;
			// 执行线程插队任务
			ExecuteThreadInsertMission( );
			this.deviceThreadExecute?.ExecuteEveryDeviceMission( );   // 看看线程控制器里有没有需要执行的任务

			// 本次需要请求了

			if (request.RequestType == RequestType.MethodCall)
			{
				// 方法调用的情况都是通用的
				OperateResult call = await CallMethodRequestExecute( request );
				if (call.IsSuccess)
				{
					this.deviceThreadState.CycleCaptureSuccess++;
					this.deviceThreadState.LastRequestSuccessTime = requestStart;
					this.RequestSuccessCount++;
					this.UpdateDeviceOnlineStatus( true );
					this.SetDeviceForceMessgae( request.Name, string.Empty );
				}
				else
				{
					deviceThreadState.CycleCaptureFailed++;
					if (call.ErrorCode < 0)
					{
						// 网络问题
						this.logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"调用[{request.Name}] {request.Address} 数据失败：{call.Message} 持续: {this.deviceThreadState.GetOfflineTimeDescription( )}" );
						this.UpdateDeviceOnlineStatus( false );
						this.SetDeviceForceMessgae( request.Name, $"Call Method[{request.Name}] failed: {call.Message}" );
						this.ExecuteAfterPipeWrong( );
					}
					else
					{
						this.deviceThreadState.LastRequestSuccessTime = requestStart;
						this.logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"调用[{request.Name}] {request.Address} 数据失败：{call.Message}" );
						this.UpdateDeviceOnlineStatus( true );
					}
					this.RequestFailedCount++;
					cycleObject.FailedMsg = call.Message;
				}
			}
			else if (request.RequestType == RequestType.DatabaseOperate)
			{
				// 数据库请求信息
				if (this.RequestFailedCount + this.RequestSuccessCount == 0) return ThreadCycleRunReturn.Continue;                            // 至少有一次请求发生，才开始存储数据库的操作
				DatabaseRequest databaseRequest = request as DatabaseRequest;
				if ( databaseRequest.ExecuteWhenOffline == false && this.DeviceStatus == false)
				{
					// 当前的请求配置了离线时不再执行数据库的请求操作
					return ThreadCycleRunReturn.Continue;
				}
				MatchEvaluator matchEvaluator = match =>
				{
					string name = match.Value.Substring( 1, match.Value.Length - 2 );
					if (dictDeviceSingleAddress.ContainsKey( name ))
					{
						DeviceSingleAddressLabel singleAddressLabel = dictDeviceSingleAddress[name];
						if (singleAddressLabel.DataType == DataType.Bool ||
							singleAddressLabel.DataType == DataType.String ||
							singleAddressLabel.DataType == DataType.Struct ||
							singleAddressLabel.DataType == DataType.BCD ||
							singleAddressLabel.DataType == DataType.DateTime ||
							singleAddressLabel.DataType == DataType.Enum ||
							singleAddressLabel.DataType == DataType.Guid ||
							singleAddressLabel.DataType == DataType.Hex)
							return $"'{singleAddressLabel.Value}'";
						else
							return $"{singleAddressLabel.Value}";
					}
					else
					{
						return "NULL";
					}
				};
				string sql = Regex.Replace( databaseRequest.SqlCommand, @"\{[^\}]+\}", matchEvaluator );
				DatabaseCore databaseCore = BusinessEngine.Business.DatabaseResource.GetDatabaseCore( databaseRequest.Address );
				if (databaseCore != null)
				{
					OperateResult exe = await databaseCore.ExecuteCommand( sql );
					if (!exe.IsSuccess)
					{
						// 数据库存储失败
						this.logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"存储数据库[{databaseRequest.Address}] 时发生失败：{exe.Message}" );
					}
					else
					{
						// 数据库存储成功
					}
				}
				else
				{
					this.logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"存储数据库[{databaseRequest.Address}] 时发生失败：未找到这个数据库对象" );
				}
			}
			else if (request.RequestType == RequestType.WriteInterval)
			{
				// 定时写入的请求信息
				ScalarWriteRequest writeRequest = request as ScalarWriteRequest;

				string value = writeRequest.FormatValue;
				if (value.StartsWith( "[" ) && value.EndsWith( "]" ))
				{
					// 数组的形式
					if (Regex.IsMatch( value, @"\[[0-9]+-[0-9]+\]" ))
					{
						MatchCollection mc = Regex.Matches( value, @"[0-9]+" );
						int start = int.Parse( mc[0].Value );
						int end = int.Parse( mc[1].Value );
						int current = 0;
						if (start <= end)
						{
							current = start + writeRequest.ArrayIndex;
							if (current >= end) writeRequest.ArrayIndex = 0;
							else writeRequest.ArrayIndex++;
						}
						else
						{
							current = start - writeRequest.ArrayIndex;
							if (current <= end) writeRequest.ArrayIndex = 0;
							else writeRequest.ArrayIndex++;
						}

						value = current.ToString( );
					}
					else
					{
						string[] array = writeRequest.FormatValue.ToStringArray<string>( );
						if (array.Length == 0) return ThreadCycleRunReturn.Continue;
						if (writeRequest.ArrayIndex < 0 || writeRequest.ArrayIndex >= array.Length) writeRequest.ArrayIndex = 0;

						value = array[writeRequest.ArrayIndex];
						writeRequest.ArrayIndex++;
						if (writeRequest.ArrayIndex >= array.Length) writeRequest.ArrayIndex = 0;
					}
				}
				else if (value.StartsWith( "var=", StringComparison.OrdinalIgnoreCase ))
				{
					// 获取网关的其他变量信息，使用网关提供的接口信息进行获取
					OperateResult<JToken> read = this.edgeResources.EdgeServices.DeviceData( value.Substring( 4 ) );
					if (read.IsSuccess)
					{
						value = read.Content?.ToString( );
					}
					else
					{
						// 读取失败
						this.logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"Write data[{request.Name}] -> read {request.Address} failed：{read.Message}" );
						return ThreadCycleRunReturn.Continue;
					}
				}
				else if (value.StartsWith( "eval=" ) || value.StartsWith( "Eval=" ))
				{
					// eval=(short)(50*sin(2*PI*x/100) + 80)                               目前支持的变量信息
					// 也可以使用当前设备对象的数据信息，例如 设备有内温(short), 外温(float)
					// eval=内温*10+外温
					// eval={设备1/温度}*10+外温
					try
					{
						if (writeRequest.Script == null)
						{
							writeRequest.Script = new DynamicExpresso.Interpreter( );
							writeRequest.Script.Reference( typeof( System.Random ) );

							// 获取参数名称，写入到列表中去，后续直接根据列表存储
							writeRequest.ParameterNames = CreateParametersFromString( value );
						}
						else
						{
							List<Parameter> parameters = new List<Parameter>( );
							// 赋值其他的操作数据
							foreach (var item in writeRequest.ParameterNames)
							{
								if (dictDeviceSingleAddress.ContainsKey( item ))
								{
									parameters.Add( new Parameter( item, dictDeviceSingleAddress[item].GetDynamicValue( ) ) );
								}
							}
							if (!writeRequest.ParameterNames.Contains( "x" ))
							{
								parameters.Add( new Parameter( "x", writeRequest.RequestInfactCount ) );
							}

							if (value.Contains( "{" ) && value.Contains( "}" ))
							{
								// 关联了其他设备的节点
								MatchEvaluator matchEvaluator = match =>
								{
									string name = match.Value.Substring( 1, match.Value.Length - 2 );
									if (name.StartsWith( "var=" )) value = name.Substring( 4 );
									OperateResult<JToken> read = this.edgeResources.EdgeServices.DeviceData( name );
									if (read.IsSuccess)
									{
										return read.Content?.ToString( );
									}
									return match.Value;
								};
								value = Regex.Replace( value, @"\{[^\}]+\}", matchEvaluator );
							}

							value = writeRequest.Script.Eval( value.Substring( 5 ), parameters.ToArray( ) ).ToString( );
						}
					}
					catch (Exception ex)
					{
						logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"Eval failed, Request[{writeRequest.Name}]: " + ex.Message );
						return ThreadCycleRunReturn.Continue;
					}
				}
				else if (value.StartsWith("csv=") || value.StartsWith("Csv="))
                {
                    // csv=(short)(变量地址|文件名)     目前支持的变量信息|csv文件（取第二个值）容积表计算
                    try
                    {
                        string tempstr = value.Substring(4);
                        string[] tmpstrs = tempstr.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                        if (tmpstrs.Length == 2)
                        {
                            // 获取网关的其他变量信息，使用网关提供的接口信息进行获取
                            OperateResult<JToken> read = this.edgeResources.EdgeServices.DeviceData(tmpstrs[0]);
                            if (read.IsSuccess)
                            {
                                value = read.Content?.ToString();
                                decimal height = 0;
                                decimal.TryParse(value, out height);
                                string filename = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, tmpstrs[1]);
                                if (System.IO.File.Exists(filename))
                                {
                                    using (System.IO.TextReader reader = new System.IO.StreamReader(filename))
                                    {
                                        var csvReader = new CsvReader(reader, CultureInfo.InvariantCulture);
                                        var records = csvReader.GetRecords<VolTable>().ToList();
                                        records.Sort((x, y) => x.OilHeight.CompareTo(y.OilHeight));
                                        var list = records.Where(i => i.OilHeight >= height).ToList(); //获取所有比检索值大的值
                                        if (list != null && list.Count > 0)
                                        {
                                            var record = list.OrderBy(x => x.OilHeight).FirstOrDefault();
                                            value = Convert.ToInt32(record.Volumn).ToString();
                                        }
                                        else
                                        {
                                            value = "0";
                                        }

                                    }
                                }
                                else
                                {
                                    // 读取失败
                                    this.logNet?.WriteError(GetDeviceNameWithPath(), ToString(), $"容积表[{filename}]不存在");
                                    return ThreadCycleRunReturn.Continue;
                                }
                            }
                            else
                            {
                                // 读取失败
                                this.logNet?.WriteError(GetDeviceNameWithPath(), ToString(), $"Write data[{request.Name}] -> read {request.Address} failed：{read.Message}");
                                return ThreadCycleRunReturn.Continue;
                            }
                        }
                        else
                        {
                            // 读取失败
                            this.logNet?.WriteError(GetDeviceNameWithPath(), ToString(), $"配置信息[{tempstr}]错误");
                            return ThreadCycleRunReturn.Continue;
                        }
                    }
                    catch (Exception ex)
                    {
                        logNet?.WriteError(GetDeviceNameWithPath(), ToString(), $"Eval failed, Request[{writeRequest.Name}]: " + ex.Message);
                        return ThreadCycleRunReturn.Continue;
                    }
                }
				if (value != null)
				{
					OperateResult writeResult;
					if (writeRequest.IsAbsoluteAddress)
					{
						// 如果使用的是绝对的地址
						writeResult = await WriteActualAsync( writeRequest, value ).ConfigureAwait( false );
					}
					else
					{
						writeResult = WriteValueByName( writeRequest.Address, value, changeThread: false );  // 不用跨线程调用
					}
					if (writeResult.IsSuccess)
					{
						this.deviceThreadState.CycleCaptureSuccess++;
						this.RequestSuccessCount++;
						this.deviceThreadState.LastRequestSuccessTime = requestStart;
						this.SetDeviceForceMessgae( request.Name, string.Empty );
					}
					else
					{
						deviceThreadState.CycleCaptureFailed++;
						this.logNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"Write data[{request.Name}] {request.Address} [{value}] failed：{writeResult.Message}" );
						this.RequestFailedCount++;
						cycleObject.FailedMsg = writeResult.Message;
					}
				}
			}
			else
			{
				// 标量请求和原始字节的请求
				OperateResult read = await ReadActualAsync( request ).ConfigureAwait( false );
				if (read.IsSuccess)
				{
					request.DeviceActual.deviceThreadState.CycleCaptureSuccess++;
					request.DeviceActual.deviceThreadState.LastRequestSuccessTime = requestStart;
					request.DeviceActual.RequestSuccessCount++;
					request.DeviceActual.UpdateDeviceOnlineStatus( true );
					request.DeviceActual.SetDeviceForceMessgae( request.Name, string.Empty );
					request.DeviceActual.deviceNetErrorTick = 0;
				}
				else
				{
					request.DeviceActual.deviceThreadState.CycleCaptureFailed++;
					request.DeviceActual.RequestFailedCount++;
					cycleObject.FailedMsg = read.Message;
					if (read.ErrorCode < 0)
					{
						// 网络问题
						request.DeviceActual.logNet?.WriteError( request.DeviceActual.GetDeviceNameWithPath( ), request.DeviceActual.ToString( ), $"采集[{request.Name}] {request.Address} 数据失败：{read.Message} 持续: {request.DeviceActual.deviceThreadState.GetOfflineTimeDescription( )}" );
						request.DeviceActual.UpdateDeviceOnlineStatus( false );
						request.DeviceActual.SetDeviceForceMessgae( request.Name, $"Request [{request.Name}] failed: {read.Message}" );
						request.DeviceActual.ExecuteAfterPipeWrong( );

						if (request.DeviceActual.deviceNetErrorTick == 10)
						{
							if (RedundantAfterPipeWrong( ))
							{
								// 发生了切换冗余设备
								request.DeviceActual.deviceNetErrorTick = 0;
							}
						}

						request.DeviceActual.deviceNetErrorTick++;
						if (read.ErrorCode != int.MinValue && request.DeviceActual.CurrentThreadControlsEnable == false)
						{
							if (request.DeviceActual.deviceThreadExecute.ChangeDeviceWhenError)
								return ThreadCycleRunReturn.Break;                       // 如果网络异常导致并且交出线程控制权的情况下，立即切换设备
						}
					}
					else
					{
						request.DeviceActual.deviceNetErrorTick = 0;
						// 其他问题，1.地址格式错误，2.PLC本身反馈了错误码，可能该地址受保护了，不允许进行相关的操作
						if (read.ErrorCode != 10000) request.DeviceActual.deviceThreadState.LastRequestSuccessTime = requestStart;
						request.DeviceActual.logNet?.WriteError( request.DeviceActual.GetDeviceNameWithPath( ), request.DeviceActual.ToString( ), $"采集[{request.Name}] {request.Address} 数据失败[{read.ErrorCode}]：{read.Message}" );
						if (read.ErrorCode != 10000) request.DeviceActual.UpdateDeviceOnlineStatus( true );
					}
					//break;
				}
			}

			return ThreadCycleRunReturn.Request;  // 发生了请求的操作
		}

		private int GetRequestGroupCount( int index, RequestGroupNode requestGroup )
		{
			int count = 0;
			for (int i = index; i < Requests.Count; i++)
			{
				if (Requests[i].RequestGroup == null) break;
				if (ReferenceEquals( requestGroup, Requests[i].RequestGroup ))
				{
					count++;
				}
				else
				{
					break;
				}
			}
			return count;
		}


		public void ThreadCycleFinishAction( )
		{
			this.AfterClose( );                            // 需要子类重写
			this.logNet?.WriteWarn( GetDeviceNameWithPath( ), this.ToString( ), $"采集数据关闭！退出线程，本次采集时间为 {deviceThreadState.GetRunTimeDescription( )}" );
			this.autoResetQuit.Set( );                     // 通知关闭的线程继续
			this.isQuitFinished = true;
		}

		public void ThreadCycleBeforeAction( )
		{
			this.deviceData.SetSingleValue( "__config",           ToString( ) );
			this.deviceData.SetSingleValue( "__success",          0 );
			this.deviceData.SetSingleValue( "__failed",           0 );
			this.deviceData.SetSingleValue( "__activeTime",       this.deviceThreadState.LastRequestSuccessTime.ToEdgeString( ) );
			this.deviceData.SetSingleValue( "__captureSpendTime", 0 );
			this.deviceData.SetSingleValue( "__deviceStatus",     false );
			this.deviceData.SetSingleValue( "__requestEnable",    this.allRequestEnbale );
			this.deviceData.SetSingleValue( "__onlineTime",       this.deviceThreadState.DeviceStartTime.ToEdgeString( ) );
			this.deviceData.SetSingleValue( "__failedMsg",        "" );
			this.deviceData.SetJToken(      "__alarmCount",       BusinessEngine.Business.GetAlarmJsonCount( GetDeviceNameWithPath( ) ) );
			this.deviceData.UpdateJsonTmp( );
			this.BeforStart( );                // 需要子类重写
			if ( !string.IsNullOrEmpty( DTU )) SetDtuSession( null );       // 如果设置了DTU的信息，则启用DTU模式
		}

		public bool ThreadCycleIsFinished( ) => this.isQuitFinished;

		private async void ThreadReadBackground( object threadIdObj )
		{
			int threadId = (int)threadIdObj;
			ThreadCycleBeforeAction( );          // 启动之前的方法
			HslTimerTick.SleepToSenconds( );     // 默认休息一下下

			while (this.isQuit == 0)
			{
				ThreadCycleRunReturn threadCycleRunReturn = await ThreadCycleRunAction( );
				if (threadCycleRunReturn == ThreadCycleRunReturn.Break) break;
				if (threadCycleRunReturn == ThreadCycleRunReturn.Continue)
				{
					HslTechnologyHelper.Sleep( 20 );
					continue;
				}

				// 检测当前的线程版本是否发生了变化，如果发生变化就立即退出
				if (threadId != this.isStarted)
				{
					return;
				}
				HslTechnologyHelper.Sleep( 20 );
			}
			ThreadCycleFinishAction( );
		}

		/// <summary>
		/// 标记当前的设备数据发生了变化
		/// </summary>
		public void MarkDataChanged( )
		{
			Interlocked.CompareExchange( ref hasAnyDataChanged, 1, 0 );
		}

		/// <summary>
		/// 检查当前的设备的数据是否发生了变化，如果发生变化，清0操作
		/// </summary>
		/// <returns></returns>
		public bool MarkDataChangeCheckAndReset( )
		{
			return Interlocked.CompareExchange( ref hasAnyDataChanged, 0, 1 ) == 1;
		}

		// 这里需要一点，当设备的状态改变的时候，缓存数据的设备状态信息立即变更

		/// <summary>
		/// 更新当前设备的在线状态信息，需要传入是否在线的状态
		/// </summary>
		/// <param name="status">在线与否的状态信息</param>
		public void UpdateDeviceOnlineStatus( bool status, bool clearMessage = false )
		{
			if ( this.deviceStatus == -1 || this.DeviceStatus != status)
			{
				if (status)
				{
					this.deviceData.SetSingleValue( "__onlineTime", HslTechnologyHelper.GetDateTimeNow( ).ToEdgeString( ) );
					if (clearMessage)
					{
						this.deviceMessage.Clear( );
						this.deviceData.SetSingleValue( "__failedMsg", string.Empty );
						this.deviceData.SetStringValue( "__deviceMessage", string.Empty );
					}
				}

				this.deviceData.SetSingleValue( "__deviceStatus", status );
				this.deviceStatus = status ? 1 : 0;
				this.deviceData.UpdateJsonTmp( );
				this.MarkDataChanged( );                                             // 标记当前的设备的数据发生了变化
				this.OnDeviceStatusChanged?.Invoke( this, status );                       // 触发状态变化的事件
			}
		}

		#endregion

		#region Private Member

		private int hasAnyDataChanged = 0;                                                         // 是否有任何的数据发生了变化，只要大于0就是发生了变化
		private Thread thread;                                                                     // 后台读取的线程
		private int isStarted = 0;                                                                 // 是否启动了后台数据读取
		private bool isQuitFinished = false;                                                       // 系统是否退出完成的操作
		private AutoResetEvent autoResetQuit;                                                      // 退出系统的时候的同步锁
		private int isQuit = 0;                                                                    // 是否准备从系统进行退出
		private EdgeLog logNet;                                                                    // 系统的日志
		private DeviceData deviceData;                                                             // 设备数据信息
		private DeviceThreadState deviceThreadState;                                               // 当前的设备线程的状态机
		private ScalarDataNode[] scalarDataNodes;                                                  // 可视化的所有的数据节点信息
		/// <summary>
		/// 设备本身涵盖的所有请求列表信息，包含标量请求，原始字节请求，方法请求等等
		/// </summary>
		protected List<RequestBase> Requests;                                                      // 设备本身涵盖的请求信息
		protected DeviceNode deviceNode;                                                           // 实际设备关联的配置节点信息
		private string dtu = string.Empty;                                                         // DTU的ID信息
		private bool useAsciiFormate = false;                                                      // 在读取原始字节时，是否使用ASCII格式的报文进行解析操作
		private int deviceStatus = -1;                                                             // 设备的状态，-1表示未知，0表示离线，1表示在线
		private int requestIntervalSleep = 0;                                                      // 连续请求之间的最低休眠时间

		/// <summary>
		/// 用于读写的数据信息字典
		/// </summary>
		protected Dictionary<string, DeviceSingleAddressLabel> dictDeviceSingleAddress;            // 用于读写的数据信息字典
		/// <summary>
		/// 基于本地的报警信息存储，通常是插件实现自定义的报警信息
		/// </summary>
		protected Dictionary<string, AlarmNode> localAlarmNodes;                                   // 基于本地的报警信息存储，通常是插件实现自定义的报警信息
		/// <summary>
		/// 基于本地的OEE信息存储，通常是插件实现自定义的OEE信息
		/// </summary>
		protected Dictionary<string, OeeNode> localOeeNodes;                                       // 基于本地的报警信息存储，通常是插件实现自定义的报警信息
		/// <summary>
		/// 基于本地的结构体信息存储，通常是插件实现自定义的结构体信息
		/// </summary>
		protected Dictionary<string, RegularStructItemNode> localRegularStruct;                    // 基于本地的结构体信息存储，通常是插件实现自定义的结构体信息
		/// <summary>
		/// 所有的原始字节请求数据缓存，以备不时之需
		/// </summary>
		protected Dictionary<string, OperateResult<byte[]>> localSourceRequestCache;               // 所有的原始字节请求数据缓存，以备不时之需
		/// <summary>
		/// 当前设备的高级分析对象
		/// </summary>
		protected AdvanceAnalysisDevice advanceAnalysis;                                           // 当前设备的高级分析对象
		/// <summary>
		/// 当前设备的消息对象信息
		/// </summary>
		protected StringGather<string> deviceMessage;                                              // 设备当前的消息列表信息
		/// <summary>
		/// 当前网关的资源信息
		/// </summary>
		protected EdgeDeviceResources edgeResources;                                             // 当前网关的资源信息

		protected Dictionary<string, MethodRpcInfo> dictRpcApiInfos;                               // 方法接口的词典信息
		protected MethodRpcInfo[] methodRpcInfosBuffer;                                              // 方法列表的缓存数据信息
		private MethodConfigNode methodConfigNode;                                                 // 方法公开配置信息
		private bool allRequestEnbale = true;                                                      // 指示当前是否在请求，如果是false，就暂停
		private bool resetNullIfFaield = false;                                                    // 当读取失败的时候，是否需要重置为空
		private string mqttTopicPre = string.Empty;                                                // Mqtt的前置主题信息
		private ConcurrentQueue<ThreadInsertMission> threadInsertMissions;                         // 线程相关的插队任务
		private DeviceThreadExecuteControl deviceThreadExecute;                                    // 如果设备由其他线程控制，
		private int deviceNetErrorTick = 0;                                                        // 设备因为网络异常连续的次数

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"Device[{GetDeviceNameWithPath( )}]";

		#endregion

	}

	/// <summary>
	/// 线程循环传递的对象
	/// </summary>
	public class ThreadCycleObject
	{
		/// <summary>
		/// 失败的消息
		/// </summary>
		public string FailedMsg { get; set; } = string.Empty;
	}

	public class EvalParameter
	{
		public double x { get; set; }
	}
}
