using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 设备的线程状态机，包含了设备的线程状态数据信息
	/// </summary>
	public class DeviceThreadState
	{

		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public DeviceThreadState( )
		{
			DateTime now = HslTechnologyHelper.GetDateTimeNow( );
			this.DeviceStartTime            = now;
			this.LastRequestSuccessTime     = now;
			this.activeTime                 = now;
			this.everySecondsUpdateCaheTime = now;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 本次请求的总的请求数量
		/// </summary>
		public int RequestCount
		{
			get => requestCount; 
		}

		/// <summary>
		/// 单次循环时采集成功的次数
		/// </summary>
		public int CycleCaptureSuccess
		{
			get => cycleCaptureSuccess;
			set
			{
				cycleCaptureSuccess = value;
				requestCount = cycleCaptureSuccess + cycleCaptureFailed;
			}
		}

		/// <summary>
		/// 单次循环时采集失败的次数
		/// </summary>
		public int CycleCaptureFailed
		{
			get => cycleCaptureFailed;
			set
			{
				cycleCaptureFailed = value;
				requestCount = cycleCaptureSuccess + cycleCaptureFailed;
			}
		}

		/// <summary>
		/// 当前线程的设备启动时间
		/// </summary>
		public DateTime DeviceStartTime { get; set; }

		/// <summary>
		/// 当前线程的活动时间
		/// </summary>
		public DateTime ThreadActiveTime => this.activeTime;

		/// <summary>
		/// 获取或设置最后一次请求成功的时间
		/// </summary>
		public DateTime LastRequestSuccessTime { get; set; }

		/// <summary>
		/// 随时间执行的功能委托方法
		/// </summary>
		public Func<DeviceThreadState, DateTime, DateTime, Task> FuncExecuteByTime { get; set; }

		#endregion

		#region Public Method

		/// <summary>
		/// 获取当前的线程运行了多少的文本描述信息
		/// </summary>
		/// <returns>运行时间</returns>
		public string GetRunTimeDescription( )
		{
			return HslCommunication.BasicFramework.SoftBasic.GetTimeSpanDescription( HslTechnologyHelper.GetDateTimeNow( ) - DeviceStartTime );
		}

		/// <summary>
		/// 获取当前设备请求失败（也就是离线状态）持续了多久的文本描述
		/// </summary>
		/// <returns>离线时间</returns>
		public string GetOfflineTimeDescription( )
		{
			return HslCommunication.BasicFramework.SoftBasic.GetTimeSpanDescription( HslTechnologyHelper.GetDateTimeNow( ) - LastRequestSuccessTime );
		}

		/// <summary>
		/// 重置当前的所有的采集数量信息
		/// </summary>
		public void ResetCountTick( )
		{
			this.requestCount = 0;
			this.cycleCaptureSuccess = 0;
			this.CycleCaptureFailed = 0;
		}

		/// <summary>
		/// 更新当前的线程的活动时间，并且执行一些时间校验的委托方法
		/// </summary>
		/// <param name="now">新的时间内润</param>
		/// <returns>Task任务对象</returns>
		public async Task UpdateActiveTime( DateTime now )
		{
			await FuncExecuteByTime?.Invoke( this, this.activeTime, now );
			this.activeTime = now;
		}

		/// <summary>
		/// 更新当前的线程活动时间到最新的当前时间
		/// </summary>
		public void UpdateActiveTime( )
		{
			this.activeTime = HslTechnologyHelper.GetDateTimeNow( );
		}

		/// <summary>
		/// 是否在请求间隙的过程中更新当前的报警或是其他的缓存信息
		/// </summary>
		/// <returns>是否执行更新的操作</returns>
		public bool IsUpdateCacheFromRequestInterval( DateTime now )
		{
			if ((now - LastRequestSuccessTime).TotalSeconds >= 1d && IsSecondsHappen( everySecondsUpdateCaheTime, now ))
			{
				this.everySecondsUpdateCaheTime = now;
				return true;
			}
			return false;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"{cycleCaptureSuccess}/{requestCount}";

		#endregion

		#region Private Members

		private int requestCount = 0;
		private int cycleCaptureSuccess = 0;
		private int cycleCaptureFailed = 0;
		private DateTime activeTime = DateTime.Now;
		private DateTime everySecondsUpdateCaheTime = DateTime.Now;

		#endregion

		#region Public Helper
		
		/// <summary>
		/// 检测两个时间的秒数是否发生了变化
		/// </summary>
		/// <param name="pre">之前的时间</param>
		/// <param name="now">最新的时间</param>
		/// <returns>是否处于不同秒信息</returns>
		public static bool IsSecondsHappen( DateTime pre, DateTime now )
		{
			if (pre.Second != now.Second) return true;
			if (pre.Minute != now.Minute) return true;
			if (pre.Hour   != now.Hour)   return true;
			if (pre.Day    != now.Day)    return true;
			if (pre.Month  != now.Month)  return true;
			if (pre.Year   != now.Year)   return true;
			return false;
		}

		/// <summary>
		/// 检测两个时间的分钟是否发生了变化
		/// </summary>
		/// <param name="pre">之前的时间</param>
		/// <param name="now">最新的时间</param>
		/// <returns>是否处于不同分钟信息</returns>
		public static bool IsMinuteHappen( DateTime pre, DateTime now )
		{
			if (pre.Minute != now.Minute) return true;
			if (pre.Hour   != now.Hour)   return true;
			if (pre.Day    != now.Day)    return true;
			if (pre.Month  != now.Month)  return true;
			if (pre.Year   != now.Year)   return true;
			return false;
		}

		/// <summary>
		/// 检测两个时间的小时是否发生了变化
		/// </summary>
		/// <param name="pre">之前的时间</param>
		/// <param name="now">最新的时间</param>
		/// <returns>是否处于不同的小时</returns>
		public static bool IsHourHappen( DateTime pre, DateTime now )
		{
			if (pre.Hour   != now.Hour)   return true;
			if (pre.Day    != now.Day)    return true;
			if (pre.Month  != now.Month)  return true;
			if (pre.Year   != now.Year)   return true;
			return false;
		}

		/// <summary>
		/// 检测两个时间的日期信息是否发生了变化
		/// </summary>
		/// <param name="pre">之前的时间</param>
		/// <param name="now">最新的时间</param>
		/// <returns>是否处于不同的日期</returns>
		public static bool IsDayHappen( DateTime pre, DateTime now )
		{
			if (pre.Day   != now.Day)   return true;
			if (pre.Month != now.Month) return true;
			if (pre.Year  != now.Year)  return true;
			return false;
		}

		#endregion
	}
}
