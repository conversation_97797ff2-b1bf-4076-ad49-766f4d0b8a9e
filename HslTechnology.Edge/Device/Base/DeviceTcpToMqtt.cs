using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Net.NetworkInformation;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Core.Net;
using System.Net.Sockets;
using HslCommunication;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Device.Base.Helper;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using Newtonsoft.Json.Linq;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 将TCP的设备转为MQTT
	/// </summary>
	public class DeviceTcpToMqtt : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceTcpToMqtt( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeTcpToMqtt( element );
			this.networkDtu = new NetworkDtuClient( );
			this.mqttServer = deviceResources.MqttServer;
		}

		#endregion

		#region Override Method

		protected override void UpdateDeviceActiveTimeWhemNoneRequest( )
		{
			this.SetJsonObjectValue( "__activeTime", this.activeTime.ToEdgeString( ) );
		}

		private int secondsTick = 59;

		/// <inheritdoc/>
		protected async override Task EverySecondsExecuteMethod( int second )
		{
			this.SetJsonValue( _tcpToMqtt,  this.tcpToMqttCount,      false );
			this.SetJsonValue( _mqttToTcp,  this.mqttToTcpCount,      false );
			this.SetJsonValue( "__success", this.RequestSuccessCount, false );                 // 成功请求次数
			secondsTick++;
			if (secondsTick > 60) secondsTick = 1;

			// 如果发现本地未连接，则尝试连接
			if (this.localSocket == null) await ConnectLocalServer( );

			// 网络正常，30s ping一次，网络不正常，10s ping一次
			if (this.node.PingEnable && (this.pingFailedCount == 0 && secondsTick % 30 == 0) || (this.pingFailedCount != 0 && secondsTick % 10 == 0))
			{
				if (this.localSocket != null)
				{
					// 每隔30秒。ping一下设备的IP地址
					try
					{
						PingReply reply = await ping.SendPingAsync( this.node.IpAddress );
						if (reply.Status != IPStatus.Success)
						{
							this.pingFailedCount++;
						}
						else
						{
							this.pingSuccessCount++;
							this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
							this.pingFailedCount = 0;
						}
					}
					catch
					{
						this.pingFailedCount++;
					}

					if (this.pingFailedCount > 3)
					{
						NetworkLocalOffline( );
					}
				}
			}

			// 网口没有错误的情况下
			if (string.IsNullOrEmpty( this.deviceMessage.GetDefaultStringMsg( ) ))
				UpdateDeviceOnlineStatus( true );
			else
				UpdateDeviceOnlineStatus( false );

			// 更新设备的活动时间
			this.SetJsonObjectValue( "__activeTime", new JValue( DateTime.Now.ToEdgeString( ) ) );
			await base.EverySecondsExecuteMethod( second );
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			ping = new Ping( );

			this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
			this.SetJsonValue( _tcpToMqtt,    this.tcpToMqttCount, false );
			this.SetJsonValue( _mqttToTcp,    this.mqttToTcpCount, false );
			this.SetJsonValue( _topic,        this.GetDeviceNameWithPath( ), false );
		}

		private async Task<OperateResult> ConnectLocalServer( )
		{
			// 进行连接的操作
			OperateResult<Socket> create = await networkDtu.ConnectDeviveNetNode( this.node );
			if (!create.IsSuccess)
			{
				this.SetJsonValue( _local, $"[{this.node.IpAddress}:{this.node.Port}] Closed", false );
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), GetDeviceNameWithPath( ), $"Connect [ {this.node.GetSocketInfo( )} ] failed: " + create.Message );
				return create;
			}
			else
			{
				try
				{
					create.Content.BeginReceive( serverBuffer, 0, serverBuffer.Length, SocketFlags.None, new AsyncCallback( ServerSocketReveiceCallBack ), create.Content );
					this.localSocket = create.Content;
					this.SetDeviceForceMessgae( _local, string.Empty );
					this.SetJsonValue( _local, $"[{this.node.IpAddress}:{this.node.Port}] Success", false );
					this.LogNet?.WriteInfo( GetDeviceNameWithPath( ), GetDeviceNameWithPath( ), $"Connect [{this.node.GetSocketInfo( )}] success! " );
					return OperateResult.CreateSuccessResult( );
				}
				catch (Exception ex)
				{
					create.Content?.Close( );
					string msg = $"[{this.node.IpAddress}:{this.node.Port}] Connect failed: " + ex.Message;
					this.SetDeviceForceMessgae( _local, msg );
					this.SetJsonValue( _local, msg, false );
					this.LogNet?.WriteError( GetDeviceNameWithPath( ), GetDeviceNameWithPath( ), $"Connect [{this.node.GetSocketInfo( )}] failed: " + msg );
					return new OperateResult( ex.Message );
				}
			}
		}

		private void ServerSocketReveiceCallBack( IAsyncResult ar )
		{
			if (ar.AsyncState is Socket socket)
			{
				byte[] data = null;
				try
				{
					int length = socket.EndReceive( ar );

					if (length == 0)
					{
						NetworkLocalOffline( );
						return;
					};

					data = serverBuffer.SelectBegin( length );
				}
				catch
				{
					NetworkLocalOffline( );
					return;
				}

				// 数据发送到MQTT
				this.RequestSuccessCount++;
				this.tcpToMqttCount += data.Length;
				this.mqttServer?.PublishTopicPayload( GetDeviceNameWithPath( ), data, false );
				this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _tcpToMqtt, data, this.node.LogMsgFormatBinary );
				this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                // 更新设备的活动时间

				try
				{
					socket.BeginReceive( serverBuffer, 0, serverBuffer.Length, SocketFlags.None, new AsyncCallback( ServerSocketReveiceCallBack ), socket );
				}
				catch
				{
					NetworkLocalOffline( );
					return;
				}
			}
		}

		/// <inheritdoc/>
		public override OperateResult DealWithPublishTopic( string[] path, byte[] payload, string payload2 )
		{
			// 从MQTT发布的原始报文，然后发回给本地的TCP连接
			this.mqttToTcpCount += payload.Length;
			this.RequestSuccessCount++;

			this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _mqttToTcp, payload, this.node.LogMsgFormatBinary );
			this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                    // 更新设备的活动时间

			try
			{
				// 发送到本地的端口去
				if (payload != null && this.localSocket != null)
				{
					localSocket.Send( payload, 0, payload.Length, SocketFlags.None );
					return OperateResult.CreateSuccessResult( );
				}
				else
				{
					return new OperateResult( $"[{this.node.IpAddress}:{this.node.Port}] failed, 数据为空或是没有连接" );
				}
			}
			catch
			{
				// 记录日志
				NetworkLocalOffline( );
				return new OperateResult( $"[{this.node.IpAddress}:{this.node.Port}] Closed, 通信异常，等待设备重新连接成功" );
			}
		}

		private void NetworkLocalOffline( )
		{
			this.localSocket?.Close( );
			this.localSocket = null;
			this.SetJsonValue( _local, $"[{this.node.IpAddress}:{this.node.Port}] Closed", false );
		}

		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			// 添加几个只读节点数据信息
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _local,
				Description = "连接的本地设备的状态信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _tcpToMqtt,
				Description = "网口到MQTT的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _mqttToTcp,
				Description = "MQTT到网口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = "PingSuccess",
				Description = "Ping客户端的成功次数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _topic,
				Description = "MQTT订阅的主题，就可以收到串口数据，发布该主题即写数据到串口",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );

			base.AnalysisByBussiness( business );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[TcpToMqtt] [{GetDeviceNameWithPath( )}] [{node.IpAddress}:{node.Port}]";

		#endregion

		#region Private Member

		private DateTime activeTime = HslTechnologyHelper.GetDateTimeNow( );
		private Socket localSocket = null;                       // 连接服务器的socket
		private NetworkDtuClient networkDtu;                     // DTU的辅助客户端
		private Ping ping;                                       // 针对客户端的ping操作
		private long pingFailedCount = 0;                        // ping错误的次数
		private long pingSuccessCount = 0;                       // ping成功的次数
		private long tcpToMqttCount = 0;                          // 串口到网口的报文字节数
		private long mqttToTcpCount = 0;                          // 网口到串口的报文字节数
		private MqttServer mqttServer;

		private byte[] serverBuffer = new byte[2048];            // 服务器的数据缓冲

		#endregion

		private NodeTcpToMqtt node;
		private const string _local = "Local";
		private const string _tcpToMqtt = "Tcp->Mqtt";
		private const string _mqttToTcp = "Mqtt->Tcp";
		private const string _topic = "Topic";

	}

	internal class TcpReceive : NetworkDoubleBase
	{
		public TcpReceive( string ip, int port )
		{
			this.IpAddress = ip;
			this.Port      = port;

			this.UseServerActivePush = true;
		}

		protected override bool DecideWhetherQAMessage( Socket socket, OperateResult<byte[]> receive )
		{
			RevicedData?.Invoke( this, receive );
			return false;
		}

		public event EventHandler<OperateResult<byte[]>> RevicedData;
	}
}
