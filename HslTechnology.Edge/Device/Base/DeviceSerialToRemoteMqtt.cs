using HslCommunication;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.MQTT;
using HslCommunication.Enthernet;
using Newtonsoft.Json.Linq;
using System.Threading;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 串口转MQTT的设备
	/// </summary>
	public class DeviceSerialToRemoteMqtt : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceSerialToRemoteMqtt( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeSerialToRemoteMqtt( element );
			this.node.PortName = deviceResources.EdgeSettings.PortMapping.GetSourcePort( this.node.PortName );
		}

		#endregion

		#region Override Method

		protected override void UpdateDeviceActiveTimeWhemNoneRequest( )
		{
			this.SetJsonObjectValue( "__activeTime", this.activeTime.ToEdgeString( ) );
		}

		/// <inheritdoc/>
		protected async override Task EverySecondsExecuteMethod( int second )
		{
			this.SetJsonValue( _mqttToSerial, this.mqttToSerialCount,   false );                       // 设置Mqtt到串口的字节通信数据量
			this.SetJsonValue( _serialToMqtt, this.serialToMqttCount,   false );                       // 设置串口到MQTT的字节通信数据量
			this.SetJsonValue( "__success",   this.RequestSuccessCount, false );                       // 成功请求次数

			if (this.mqttClient != null)
			{
				if (this.mqttClient.IsConnected && this.mqttConnectedFirst)
				{
					this.SetDeviceForceMessgae( _mqtt, string.Empty );
					this.SetJsonValue( _mqtt, $"MQTT[{this.node.MqttIpAddress}:{this.node.MqttPort}] Connected", false );
				}
				else
				{
					string msg = $"MQTT[{this.node.MqttIpAddress}:{this.node.MqttPort}] Connected failed";
					this.SetJsonValue( _mqtt, msg, false );
					this.SetDeviceForceMessgae( _mqtt, msg );
					if (!this.mqttConnectedFirst)
					{
						// 如此第一次没有连接上去，这里就不停的重复尝试连接
						this.mqttConnectedFirst = this.mqttClient.ConnectServer( ).IsSuccess;
					}
				}
			}

			try
			{
				if (!serialPort.IsOpen) serialPort.Open( );
				this.SetDeviceForceMessgae( _com, string.Empty );
				this.SetJsonValue( _com, $"[{this.node.PortName}] Open", false );
			}
			catch (Exception ex)
			{
				// 串口打开失败则记录消息
				string msg = $"[{this.node.PortName}] Open failed: {ex.Message}";
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				this.SetJsonValue( _com, msg, false );
				this.SetDeviceForceMessgae( _com, msg );
			}

			// 串口没有错误的情况下
			if (string.IsNullOrEmpty( this.deviceMessage.GetDefaultStringMsg( ) ))
				UpdateDeviceOnlineStatus( true );
			else
				UpdateDeviceOnlineStatus( false );


			await base.EverySecondsExecuteMethod( second );
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			try
			{
				MqttConnectionOptions mqttConnectionOptions = new MqttConnectionOptions( )
				{
					IpAddress      = this.node.MqttIpAddress,
					Port           = this.node.MqttPort,
					ConnectTimeout = this.node.MqttConnectTimeOut,
				};
				if (!string.IsNullOrEmpty( this.node.UserName ))
				{
					mqttConnectionOptions.Credentials = new MqttCredential( this.node.UserName, this.node.Password );
				}
				mqttConnectionOptions.UseRSAProvider = this.node.UseRSA;
				mqttConnectionOptions.ClientId = string.IsNullOrEmpty( this.node.ClientID ) ? GetDeviceNameWithPath( ) : this.node.ClientID;

				this.mqttClient = new MqttClient( mqttConnectionOptions );
				this.mqttClient.OnClientConnected += MqttClient_OnClientConnected;
				this.mqttClient.OnMqttMessageReceived += MqttClient_OnMqttMessageReceived;
				
				OperateResult connect = this.mqttClient.ConnectServer( );
				this.mqttConnectedFirst = connect.IsSuccess;
			}
			catch(Exception ex)
			{
				// MQTT启动失败怎么办，继续执行
				string msg = $"Mqtt[{this.node.MqttIpAddress}:{this.node.MqttPort}] failed: {ex.Message}";
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				this.SetJsonValue( _mqtt, msg, false );
				this.SetDeviceForceMessgae( _mqtt, msg );
			}

			serialPort = new SerialPort( );
			serialPort.PortName  = node.PortName;
			serialPort.BaudRate  = node.BaudRate;
			serialPort.DataBits  = node.DataBits;
			serialPort.StopBits  = node.StopBits;
			serialPort.Parity    = node.Parity;
			serialPort.RtsEnable = node.RtsEnable;

			try
			{
				serialPort.DataReceived += SP_ReadData_DataReceived;
				serialPort.Open( );
				this.SetJsonValue( _com, $"[{this.node.PortName}] Open", false );
				this.SetDeviceForceMessgae( _com, string.Empty );
			}
			catch (Exception ex)
			{
				// 串口启动失败怎么办，继续执行，因为每秒会检查串口是否成功打开
				string msg = $"[{this.node.PortName}] Open failed: {ex.Message}";
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				this.SetJsonValue( _com, msg, false );
				this.SetDeviceForceMessgae( _com, msg );
			}

			this.SetJsonValue( _serialToMqtt,  this.serialToMqttCount, false );
			this.SetJsonValue( _mqttToSerial,  this.mqttToSerialCount, false );
			this.SetJsonValue( _topic_publish, this.node.WriteTopic,   false );
			this.SetJsonValue( _topic_sub,     this.node.ReadTopic,    false );
		}

		private void MqttClient_OnMqttMessageReceived( MqttClient client, MqttApplicationMessage message )
		{
			// 从MQTT发布的原始报文，然后发回给本地的TCP连接
			string topic = message.Topic;
			byte[] payload = message.Payload;
			this.mqttToSerialCount += payload.Length;
			this.RequestSuccessCount++;

			this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _mqttToSerial, payload, this.node.LogMsgFormatBinary );       // 记录电文的日志
			this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                                             // 更新设备的活动时间

			try
			{
				// 发送到本地的端口去
				serialPort.Write( payload, 0, payload.Length );
			}
			catch (Exception ex)
			{
				// 记录日志
				LogNet?.WriteError( GetDeviceNameWithPath( ), string.Empty, $"从MQTT接收数据成功，但是[{node.PortName}] 发送数据失败：" + ex.Message );
			}
		}

		private void MqttClient_OnClientConnected( MqttClient client )
		{
			client.SubscribeMessage( this.node.ReadTopic );
		}

		private void SP_ReadData_DataReceived( object sender, SerialDataReceivedEventArgs e )
		{
			byte[] serialBuffer = new byte[1024];
			int recCount = 0;

			if (this.node.SleepTime > 0) HslTechnologyHelper.Sleep( this.node.SleepTime );
			// 接收数据
			try
			{
				recCount = serialPort.Read( serialBuffer, 0, serialBuffer.Length );
			}
			catch (Exception ex)
			{
				// 串口蹦了
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"从串口接收数据失败：{ex.Message}" );
				return;
			}
			if (recCount > 0)
			{
				// 数据发送到MQTT
				byte[] data = serialBuffer.SelectBegin( recCount );
				this.serialToMqttCount += recCount;
				this.RequestSuccessCount++;
				this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _serialToMqtt, data, this.node.LogMsgFormatBinary );           // 记录电文的日志
				this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                // 更新设备的活动时间
				this.mqttClient?.PublishMessage( new MqttApplicationMessage( )
				{
					Topic = this.node.WriteTopic,
					Payload= data,
				} );          // 将数据发布到MQTT
			}
		}


		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			// 添加几个只读节点数据信息
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _mqtt,
				Description = "MQTT状态信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _com,
				Description = "COM口的状态信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _serialToMqtt,
				Description = "串口到MQTT的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _mqttToSerial,
				Description = "MQTT到串口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _topic_publish,
				Description = "MQTT发布的主题，网关从串口接收数据后，发布该主题的数据到MQTT",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _topic_sub,
				Description = "MQTT订阅的主题，网关订阅该主题，收到MQTT数据，然后转发给串口",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );

			base.AnalysisByBussiness( business );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SerialToRemoteMqtt ] [{GetDeviceNameWithPath( )}] [{node.PortName}->Mqtt]";

		#endregion

		#region Private Member

		private DateTime activeTime = HslTechnologyHelper.GetDateTimeNow( );
		private SerialPort serialPort = null;                     // 串口交互的核心
		private long serialToMqttCount = 0;                       // 串口到网口的报文字节数
		private long mqttToSerialCount = 0;                       // 网口到串口的报文字节数
		private MqttClient mqttClient;                            // MQTT客户端信息
		private bool mqttConnectedFirst = false;                  // MQTT是否发生了一次的连接成功

		#endregion

		private NodeSerialToRemoteMqtt node;

		private const string _com = "COM";
		private const string _mqtt = "MQTT";
		private const string _serialToMqtt = "Serial->Mqtt";
		private const string _mqttToSerial = "Mqtt->Serial";
		private const string _topic_publish = "Publish-Topic";
		private const string _topic_sub = "Sub-Topic";
	}
}
