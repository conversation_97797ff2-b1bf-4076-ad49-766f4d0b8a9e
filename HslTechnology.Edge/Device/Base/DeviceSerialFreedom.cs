using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core;
using HslCommunication;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node.Device;
using HslCommunication.Serial;
using System.IO;
using HslCommunication.ModBus;
using HslTechnology.Edge.Resources;
using System.IO.Ports;
using HslCommunication.BasicFramework;

namespace HslTechnology.Edge.Device.Base
{
	public class DeviceSerialFreedom : DeviceCore
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceSerialFreedom( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeSerialFreedom( element );
			this.deviceSerial = new DeviceSerial( this.node.UseServerActivePush, this.node );
			if (this.node.ByteTransformType == Node.Core.ByteTransformType.LittleEndianOrder)
			{
				this.deviceSerial.ByteTransform = new RegularByteTransform( );
			}
			else if (this.node.ByteTransformType == Node.Core.ByteTransformType.BigEndianOrder)
			{
				this.deviceSerial.ByteTransform = new ReverseBytesTransform( );
			}
			else
			{
				this.deviceSerial.ByteTransform = new ReverseWordTransform( DataFormat.CDAB );
			}

			this.SetDeviceInfo( deviceResources, this.node, this.deviceSerial );
			this.deviceSerial.OnMessageReceive += DeviceTcp_OnMessageReceive;
			//this.node.PortName = deviceResources.PortMappingConfig.GetSourcePort( this.node.PortName );
		}

		private void DeviceTcp_OnMessageReceive( object sender, OperateResult<byte[]> e )
		{
			// 收到了数据
			if (e.IsSuccess)
			{

			}
		}



		#region Protect Override
		/// <inheritdoc/>
		protected override void BeforStart( ) => deviceSerial?.Open( );

		/// <inheritdoc/>
		protected override void AfterClose( ) => deviceSerial?.Close( );

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.ReadWriteDevice == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = deviceSerial.Open( );
			if (connect.IsSuccess) deviceSerial.Close( );

			return connect.Convert( ToString( ) );
		}

		#endregion


		/// <inheritdoc/>
		protected override Task EverySecondsExecuteMethod( int second )
		{
			// 在异步的模式下，需要定时检测网络的连接状态
			if (this.node.UseServerActivePush)
			{
				OperateResult open = deviceSerial.Open( );
				if (open.IsSuccess)
				{
					this.SetDeviceForceMessgae( _com, string.Empty );
					this.SetJsonValue( _com, $"[{this.node.PortName}] Open", false );
				}
				else
				{
					string msg = $"[{this.node.PortName}] Open failed: {open.Message}";
					LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
					this.SetJsonValue( _com, msg, false );
					this.SetDeviceForceMessgae( _com, msg );
				}

			}

			return base.EverySecondsExecuteMethod( second );
		}

		#endregion

		#region Object Override

		public override string ToString( )
		{
			return $"SerialFreedom[{this.node.GetSerialInfo()}]";
		}

		#endregion

		private DeviceSerial deviceSerial;
		private NodeSerialFreedom node;
		private const string _com = "COM";
	}


	class DeviceSerial : SerialDeviceBase
	{
		public DeviceSerial( bool useServerActivePush, NodeSerialFreedom node )
		{
			this.node = node;
			this.LogMsgFormatBinary = !node.UseAsciiFormate;
		}

		/// <inheritdoc/>
		public override OperateResult<byte[]> Read( string address, ushort length )
		{
			// 如果 address 是 01 03 00 C8 00 02 45 F5<split/>01 03 00 64 00 01 C5 D5       就自动切割成两个报文分别交互
			string[] messages = address.Split( new string[] { splits }, StringSplitOptions.RemoveEmptyEntries );
			for (int i = 0; i < messages.Length; i++)
			{
				string msg = messages[i];
				// 输入报文信息，请求报文内容
				int startIndex = HslHelper.ExtractParameter( ref msg, "stx", 0x00 );

				byte[] send = this.node.UseAsciiFormate ? SoftBasic.GetFromAsciiStringRender( msg ) : msg.ToHexBytes( );
				OperateResult<byte[]> read = ReadFromCoreServer( send );
				if (!read.IsSuccess) return read;


				if (i >= messages.Length - 1)
				{
					if (startIndex >= read.Content.Length) return new OperateResult<byte[]>( StringResources.Language.ReceiveDataLengthTooShort );
					if (startIndex != 0x00)
						return OperateResult.CreateSuccessResult( read.Content.RemoveBegin( startIndex ) );
					else
						return read;
				}
			}
			return new OperateResult<byte[]>( "address failed: " + address );
		}

		public override async Task<OperateResult<byte[]>> ReadAsync( string address, ushort length )
		{
			// 如果 address 是 01 03 00 C8 00 02 45 F5<split/>01 03 00 64 00 01 C5 D5       就自动切割成两个报文分别交互
			string[] messages = address.Split( new string[] { splits }, StringSplitOptions.RemoveEmptyEntries );
			for (int i = 0; i < messages.Length; i++)
			{
				string msg = messages[i];
				// 输入报文信息，请求报文内容
				int startIndex = HslHelper.ExtractParameter( ref msg, "stx", 0x00 );

				byte[] send = this.node.UseAsciiFormate ? SoftBasic.GetFromAsciiStringRender( msg ) : msg.ToHexBytes( );
				OperateResult<byte[]> read = await ReadFromCoreServerAsync( send );
				if (!read.IsSuccess) return read;


				if (i >= messages.Length - 1)
				{
					if (startIndex >= read.Content.Length) return new OperateResult<byte[]>( StringResources.Language.ReceiveDataLengthTooShort );
					if (startIndex != 0x00)
						return OperateResult.CreateSuccessResult( read.Content.RemoveBegin( startIndex ) );
					else
						return read;
				}
			}
			return new OperateResult<byte[]>( "address failed: " + address );
		}

		/// <inheritdoc/>
		protected override bool CheckReceiveDataComplete( MemoryStream ms )
		{
			// 检查报文是否完整
			if (this.node == null) return true;
			if (this.node.MessageType == Node.Core.MessageStructType.None) return true;

			byte[] buffer = ms.ToArray( );
			if (this.node.MessageType == Node.Core.MessageStructType.FixedLength) return buffer.Length >= this.node.FixLength;
			if (this.node.MessageType == Node.Core.MessageStructType.VariableLength)
			{
				if (buffer.Length < this.node.FixLength) return false;
				int nextLen = Convert.ToInt32( this.node.MessageRegularNode.GetValue( buffer, 0, this.ByteTransform, true ) );
				if (buffer.Length < this.node.FixLength + nextLen) return false;
				return true;
			}
			if (this.node.MessageType == Node.Core.MessageStructType.SpecifiesEndChar)
			{
				if (buffer.Length < 1) return false;

				byte[] end = this.node.EndHexString.ToHexBytes( );
				if (end.Length == 0) return true;

				if (end.Length == 1) return buffer[buffer.Length - 1] == end[0];

				if (buffer.Length < 2) return false;
				return buffer[buffer.Length - 2] == end[0] && buffer[buffer.Length - 1] == end[1];
			}
			return base.CheckReceiveDataComplete( ms );
		}

		#region Object Override

		public override string ToString( )
		{
			return $"SerialFreedom[{this.node.GetSerialInfo( )}]";
		}

		#endregion

		/// <summary>
		/// 当接收到远程服务器主动推送的消息的事件
		/// </summary>
		public event EventHandler<OperateResult<byte[]>> OnMessageReceive;


		private NodeSerialFreedom node;
		private string splits = "<split>";
	}

}
