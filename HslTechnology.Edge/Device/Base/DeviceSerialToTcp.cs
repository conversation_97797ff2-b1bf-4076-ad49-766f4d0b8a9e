using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication;
using System.Net.NetworkInformation;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Resources;
using HslCommunication.LogNet;
using System.Threading;
using System.IO;
using HslCommunication.BasicFramework;
using HslCommunication.ModBus;
using HslCommunication.Serial;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 串口转TCP的设备对象
	/// </summary>
	public class DeviceSerialToTcp : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceSerialToTcp( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			this.node = new NodeSerialToTcp( element );
			this.node.PortName = deviceResources.EdgeSettings.PortMapping.GetSourcePort( this.node.PortName );
		}

		#endregion

		#region Override Method

		protected override void UpdateDeviceActiveTimeWhemNoneRequest( )
		{
			this.SetJsonObjectValue( "__activeTime", this.activeTime.ToEdgeString( ) );
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			serialPort           = new SerialPort( );
			serialPort.PortName  = node.PortName;
			serialPort.BaudRate  = node.BaudRate;
			serialPort.DataBits  = node.DataBits;
			serialPort.StopBits  = node.StopBits;
			serialPort.Parity    = node.Parity;
			serialPort.RtsEnable = node.RtsEnable;

			ping = new Ping( );

			try
			{
				serialPort.DataReceived += SP_ReadData_DataReceived;
				serialPort.Open( );
				this.SetJsonValue(          _com, $"[{this.node.PortName}] Open", false );
				this.SetDeviceForceMessgae( _com, string.Empty );
			}
			catch (Exception ex)
			{
				// 串口启动失败怎么办，继续执行，因为每秒会检查串口是否成功打开
				string msg = $"[{this.node.PortName}] Open failed: {ex.Message}";
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				this.SetJsonValue(          _com, msg, false );
				this.SetDeviceForceMessgae( _com, msg );
			}

			// 连接服务器
			try
			{
				socketCore = new Socket( AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp );
				socketCore.Bind( new IPEndPoint( IPAddress.Any, node.ServerPort ) );
				socketCore.Listen( 10 );
				socketCore.BeginAccept( new AsyncCallback( AsyncAcceptCallback ), socketCore );
				this.SetDeviceForceMessgae( _net, string.Empty );
			}
			catch (Exception ex)
			{
				// 网口启动失败怎么办
				string msg = $"网口 [{this.node.ServerPort}] 服务端口启动失败：{ex.Message}";
				this.SetDeviceForceMessgae( _net, msg );
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
			}

			this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
			this.SetJsonValue( _serialToTcp, this.serialToTcpCount, false );
			this.SetJsonValue( _tcpToSerial, this.tcpToSerialCount, false );
		}

		private void SP_ReadData_DataReceived( object sender, SerialDataReceivedEventArgs e )
		{
			byte[] serialBuffer = new byte[1024];
			int recCount = 0;

			if (this.node.SleepTime > 0) HslTechnologyHelper.Sleep( this.node.SleepTime );
			// 接收数据
			try
			{
				recCount = serialPort.Read( serialBuffer, 0, serialBuffer.Length );
			}
			catch(Exception ex)
			{
				// 串口蹦了
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"从串口接收数据失败：{ex.Message}" );
				return;
			}
			if (recCount > 0)
			{
				// 数据发送到网口
				if(this.client != null)
				{
					try
					{
						byte[] data = serialBuffer.SelectBegin( recCount );
						this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _serialToTcp, data, this.node.LogMsgFormatBinary );

						if (this.node.WorkMode == Node.NetWorkMode.Transparent)
						{
							client.Send( data, 0, data.Length, SocketFlags.None );
							this.serialToTcpCount += recCount;
						}
						else if (this.node.WorkMode == Node.NetWorkMode.ModbusRtu2Tcp)
						{
							// 接收到的rtu的数据过期超过3秒，就视为过期数据
							if ((DateTime.Now - this.rtuSendTime).TotalMilliseconds > node.RtuResetTime) this.rtuSendBuffer = null;

							if (HslCommunication.ModBus.ModbusInfo.CheckRtuReceiveDataComplete(this.rtuSendBuffer, data ))
							{
								// 看看是否有干扰码
								byte[] rtu = ExtraRtuResponseContent( this.rtuSendBuffer, data );

								// 一次性接收到完整数据的方法
								rtu = HslCommunication.ModBus.ModbusInfo.PackCommandToTcp( HslCommunication.ModBus.ModbusInfo.ExplodeRtuCommandToCore( rtu ), modbusTcpId );
								client.Send( rtu, 0, rtu.Length, SocketFlags.None );
								this.serialToTcpCount += rtu.Length;
								this.rtuStream = new MemoryStream( );
							}
							else
							{
								// 这部分接收到的modbus可能不是完整的
								if ((DateTime.Now - this.rtuRecvTime).TotalMilliseconds > node.RtuResetTime) this.rtuStream = new MemoryStream( );

								this.rtuStream.Write( data );
								this.rtuRecvTime = DateTime.Now;

								byte[] rtu = this.rtuStream.ToArray( );
								if (HslCommunication.ModBus.ModbusInfo.CheckRtuReceiveDataComplete( this.rtuSendBuffer, rtu ))
								{
									// 看看是否有干扰码
									rtu = ExtraRtuResponseContent( this.rtuSendBuffer, rtu );

									// 一次性接收到完整数据的方法
									rtu = HslCommunication.ModBus.ModbusInfo.PackCommandToTcp( HslCommunication.ModBus.ModbusInfo.ExplodeRtuCommandToCore( rtu ), modbusTcpId );
									client.Send( rtu, 0, rtu.Length, SocketFlags.None );
									this.serialToTcpCount += rtu.Length;
									this.rtuStream = new MemoryStream( );
								}
							}
						}

						this.RequestSuccessCount++;
						this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                // 更新设备的活动时间
					}
					catch(Exception ex)
					{
						NetworkClientOffline( );
						LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"数据发送到网口失败！{ex.Message}" );
					}
				}
			}

		}

		private static byte[] ExtraRtuResponseContent( byte[] send, byte[] response )
		{
			if (response.Length < 5) return response;
			if (SoftCRC16.CheckCRC16( response )) return response;
			byte[] copy = response.RemoveBegin( 1 );
			if (SoftCRC16.CheckCRC16( copy ))
			{
				if (send == null || send.Length < 1) return response;
				if (copy[0] == send[0]) return copy;
			}
			return response;
		}

		private void AsyncAcceptCallback( IAsyncResult iar )
		{
			//还原传入的原始套接字
			if (iar.AsyncState is Socket server_socket)
			{
				try
				{
					// 在原始套接字上调用EndAccept方法，返回新的套接字
					this.client?.Close( );
					this.client = server_socket.EndAccept( iar );
					this.iPEndPoint = (IPEndPoint)client.RemoteEndPoint;

					client.BeginReceive( socketBuffer, 0, socketBuffer.Length, SocketFlags.None, new AsyncCallback( ReceiveCallBack ), this.client );
					// client上线
					this.SetJsonValue( _remote, this.iPEndPoint.ToString( ), false );
				}
				catch (ObjectDisposedException)
				{
					// 服务器关闭时候触发的异常，不进行记录
					NetworkClientOffline( );
					return;
				}
				catch
				{
					// 有可能刚连接上就断开了，那就不管
					NetworkClientOffline( );
				}

				try
				{
					server_socket.BeginAccept( new AsyncCallback( AsyncAcceptCallback ), server_socket );
				}
				catch (Exception ex)
				{
					string msg = $"网口 [{this.node.ServerPort}] 服务端口启动接收失败：{ex.Message}";
					this.SetDeviceForceMessgae( _net, msg );
					LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				}
			}
		}

		private void ReceiveCallBack( IAsyncResult ar )
		{
			if (ar.AsyncState is Socket client)
			{
				byte[] data = null;
				try
				{
					int length = client.EndReceive( ar );

					if (length == 0)
					{
						NetworkClientOffline( );
						return;
					};

					client.BeginReceive( socketBuffer, 0, socketBuffer.Length, SocketFlags.None, new AsyncCallback( ReceiveCallBack ), client );
					data = socketBuffer.SelectBegin( length );
				}
				catch
				{
					NetworkClientOffline( );
					return;
				}


				try
				{
					// 发送到串口去
					if (data != null)
					{
						if (this.node.WorkMode == Node.NetWorkMode.Transparent || data.Length < 6)
						{
							// 普通的透传
							serialPort.Write( data, 0, data.Length );
							this.tcpToSerialCount += data.Length;
						}
						else if (this.node.WorkMode == Node.NetWorkMode.ModbusRtu2Tcp)
						{
							// ModbusTcp转rtu
							LogNet?.WriteDebug( GetDeviceNameWithPath( ), ToString( ), "收到了数据：" + data.ToHexString( ' ' ) );
							modbusTcpId = (ushort)(data[0] * 256 + data[1]);
							data = HslCommunication.ModBus.ModbusInfo.PackCommandToRtu( HslCommunication.ModBus.ModbusInfo.ExplodeTcpCommandToCore( data ) );
							serialPort.Write( data, 0, data.Length );
							this.tcpToSerialCount += data.Length;
							this.rtuSendBuffer = data;
							this.rtuSendTime = DateTime.Now;
						}

						this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _tcpToSerial, data, this.node.LogMsgFormatBinary );
					}
					this.RequestSuccessCount++;
					this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                // 更新设备的活动时间
				}
				catch
				{
					// 记录日志
				}
			}
		}

		private void NetworkClientOffline( )
		{
			this.client?.Close( );
			this.client = null;
			this.iPEndPoint = null;
			this.SetJsonValue( _remote, $"", false );
		}
		
		/// <inheritdoc/>
		protected async override Task EverySecondsExecuteMethod( int second )
		{
			this.SetJsonValue( _serialToTcp, this.serialToTcpCount,    false );
			this.SetJsonValue( _tcpToSerial, this.tcpToSerialCount,    false );
			this.SetJsonValue( "__success",  this.RequestSuccessCount, false );                 // 成功请求次数

			try
			{
				if (!serialPort.IsOpen) serialPort.Open( );
				this.SetDeviceForceMessgae( _com, string.Empty );
				this.SetJsonValue( _com, $"[{this.node.PortName}] Open", false );
			}
			catch (Exception ex)
			{
				// 串口打开失败则记录消息
				string msg = $"[{this.node.PortName}] Open failed: {ex.Message}";
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				this.SetJsonValue(          _com, msg, false );
				this.SetDeviceForceMessgae( _com, msg );
			}

			// 网络正常，30s ping一次，网络不正常，10s ping一次
			if (this.node.PingEnable && (this.pingFailedCount == 0 && second % 30 == 0) || (this.pingFailedCount != 0 && second % 10 == 0))
			{
				if (this.client != null && this.iPEndPoint != null)
				{
					// 每隔30秒。ping一下客户端
					try
					{
						PingReply reply = await ping.SendPingAsync( iPEndPoint.Address );
						if (reply.Status != IPStatus.Success)
						{
							this.pingFailedCount++;
						}
						else
						{
							this.pingSuccessCount++;
							this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
							this.pingFailedCount = 0;
						}
					}
					catch
					{
						this.pingFailedCount++;
					}

					if (this.pingFailedCount > 3)
					{
						NetworkClientOffline( );
					}
				}
			}



			// 网口和DTU都没有错误的情况下
			if (string.IsNullOrEmpty( this.deviceMessage.GetDefaultStringMsg( ) ))
				UpdateDeviceOnlineStatus( true );
			else
				UpdateDeviceOnlineStatus( false );
			await base.EverySecondsExecuteMethod( second );
		}

		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			// 添加几个只读节点数据信息
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _com,
				Description = "COM口的状态信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _remote,
				Description = "远程连接的客户端信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _serialToTcp,
				Description = "串口到网口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _tcpToSerial,
				Description = "网口到串口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = "PingSuccess",
				Description = "Ping客户端的成功次数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );

			base.AnalysisByBussiness( business );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SerialToTcp] [{GetDeviceNameWithPath( )}] [{node.PortName}->{node.ServerPort}]";

		#endregion

		#region Private Member

		private DateTime activeTime = HslTechnologyHelper.GetDateTimeNow( );
		private SerialPort serialPort = null;                    // 串口交互的核心
		private Socket socketCore = null;                        // 服务器的socket
		private Socket client = null;                            // 连接的客户端的socket
		private IPEndPoint iPEndPoint;                           // 客户端的基本信息
		private Ping ping;                                       // 针对客户端的ping操作
		private long pingFailedCount = 0;                        // ping错误的次数
		private long pingSuccessCount = 0;                       // ping成功的次数
		private long serialToTcpCount = 0;                       // 串口到网口的报文字节数
		private long tcpToSerialCount = 0;                       // 网口到串口的报文字节数
		private ushort modbusTcpId = 0;

		private byte[] socketBuffer = new byte[2048];            // 缓冲的寄存器
		private byte[] rtuSendBuffer = null;                     // 发送到设备的rtu报文的缓存
		private DateTime rtuSendTime = DateTime.Now;             // 发送到设备的rtu报文的时间
		private MemoryStream rtuStream = new MemoryStream( );    // rtu报文的数据流
		private DateTime rtuRecvTime = DateTime.Now;

		#endregion

		private NodeSerialToTcp node;
		private const string _com = "COM";
		private const string _serialToTcp = "Serial->Tcp";
		private const string _tcpToSerial = "Tcp->Serial";
		private const string _remote = "Remote";
		private const string _net = "NET";
	}
}
