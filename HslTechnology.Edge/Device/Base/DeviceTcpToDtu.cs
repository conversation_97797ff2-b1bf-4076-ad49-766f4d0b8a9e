using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication;
using System.Net.NetworkInformation;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Device.Base.Helper;
using HslTechnology.Edge.Resources;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 串口转TCP的设备对象
	/// </summary>
	public class DeviceTcpToDtu : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceTcpToDtu( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.networkDtu    = new NetworkDtuClient( );
			this.node          = new NodeTcpToDTU( element );
			this.dtuActiveTime = HslTechnologyHelper.GetDateTimeNow( );
		}

		#endregion

		#region Override Method

		protected override void UpdateDeviceActiveTimeWhemNoneRequest( )
		{
			this.SetJsonObjectValue( "__activeTime", this.activeTime.ToEdgeString( ) );
		}

		private int secondsTick = 50;

		/// <inheritdoc/>
		protected async override Task EverySecondsExecuteMethod( int second )
		{
			this.SetJsonValue( _dtuToTcp,   this.dtuToTcpCount,       false );
			this.SetJsonValue( _tcpToDtu,   this.tcpToDtuCount,       false );
			this.SetJsonValue( "__success", this.RequestSuccessCount, false );                 // 成功请求次数

			secondsTick++;
			if (secondsTick > 60) secondsTick = 1;

			if (this.dtuSocket == null)
			{
				// DTU断开连接的情况下，本地也断开连接，并且不连接服务器
				NetworkLocalOffline( );
			}
			else
			{
				// DTU连接的情况下，如果发现本地未连接，则尝试连接
				if (this.localSocket == null) await ConnectLocalServer( );
			}

			// 网络正常，30s ping一次，网络不正常，10s ping一次
			if (this.node.PingEnable && (this.pingFailedCount == 0 && secondsTick % 30 == 0) || (this.pingFailedCount != 0 && secondsTick % 10 == 0))
			{
				if (this.dtuSocket != null && this.iPEndPoint != null)
				{
					// 每隔30秒。ping一下远程的DTU服务器
					try
					{
						PingReply reply = await ping.SendPingAsync( iPEndPoint.Address );
						if (reply.Status != IPStatus.Success)
						{
							this.pingFailedCount++;
						}
						else
						{
							this.pingSuccessCount++;
							this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
							this.pingFailedCount = 0;
						}
					}
					catch
					{
						this.pingFailedCount++;
					}

					if (this.pingFailedCount > 3)
					{
						NetworkDtuOffline( );
					}
				}
			}

			if (this.dtuSocket != null && (HslTechnologyHelper.GetDateTimeNow( ) - this.dtuActiveTime).TotalSeconds > this.node.DtuKeepAliveTimeout)
			{
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"当前DTU不活跃超过 [{this.node.DtuKeepAliveTimeout}] 秒，准备重连DTU服务器" );
				NetworkDtuOffline( );
			}

			if (secondsTick % 20 == 0 && this.dtuSocket == null)
			{
				// 每隔20秒钟检测连接服务器的情况
				OperateResult<Socket> create = await networkDtu.ConnectHslAlientClient( this.node );
				if (!create.IsSuccess)
				{
					// 网口启动失败的情况
					string msg = $"连接DTU [{this.node.DtuIpAddress}:{this.node.DtuPort}] 失败：{create.Message}";
					this.SetDeviceForceMessgae( _remote, msg );
					this.SetJsonValue( _remote, msg, false );
					this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				}
				else
				{
					// 网口启动成功
					this.LogNet?.WriteInfo( GetDeviceNameWithPath( ), ToString( ), $"连接DTU [{this.node.DtuIpAddress}:{this.node.DtuPort}] 成功!" );
					try
					{
						create.Content.BeginReceive( socketBuffer, 0, socketBuffer.Length, SocketFlags.None, new AsyncCallback( DtuReceiveCallBack ), create.Content );
						this.dtuSocket?.Close( );
						this.dtuSocket = create.Content;
						this.iPEndPoint = (IPEndPoint)dtuSocket.RemoteEndPoint;
						this.SetDeviceForceMessgae( _remote, string.Empty );
						this.SetJsonValue( _remote, this.iPEndPoint.ToString( ), false );
					}
					catch
					{
						string msg = $"连接DTU [{this.node.DtuIpAddress}:{this.node.DtuPort}] 成功，启动接收失败：{create.Message}";
						this.SetJsonValue( _remote, msg, false );
						this.SetDeviceForceMessgae( _remote, msg );
						NetworkDtuOffline( );
					}
				}
			}

			// 网口和DTU都没有错误的情况下
			if (string.IsNullOrEmpty( this.deviceMessage.GetDefaultStringMsg( ) ))
				UpdateDeviceOnlineStatus( true );
			else
				UpdateDeviceOnlineStatus( false );

			await base.EverySecondsExecuteMethod( second );
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			ping = new Ping( );

			this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
			this.SetJsonValue( _tcpToDtu,    this.tcpToDtuCount, false );
			this.SetJsonValue( _dtuToTcp,    this.dtuToTcpCount, false );
		}

		private async Task<OperateResult> ConnectLocalServer( )
		{
			// 进行连接的操作
			OperateResult<Socket> create = await networkDtu.ConnectDeviveNetNode( this.node );
			if (!create.IsSuccess)
			{
				this.SetJsonValue( _local, $"[{this.node.IpAddress}:{this.node.Port}] Closed", false );
				return create;
			}
			else
			{
				try
				{
					create.Content.BeginReceive( serverBuffer, 0, serverBuffer.Length, SocketFlags.None, new AsyncCallback( ServerSocketReveiceCallBack ), create.Content );
					this.localSocket = create.Content;
					this.SetDeviceForceMessgae( _local, string.Empty );
					this.SetJsonValue( _local, $"[{this.node.IpAddress}:{this.node.Port}] Success", false );
					return OperateResult.CreateSuccessResult( );
				}
				catch(Exception ex)
				{
					create.Content?.Close( );
					string msg = $"[{this.node.IpAddress}:{this.node.Port}] Connect failed: " + ex.Message;
					this.SetDeviceForceMessgae( _local, msg );
					this.SetJsonValue( _local, msg, false );
					return new OperateResult( ex.Message );
				}
			}
		}

		private void ServerSocketReveiceCallBack( IAsyncResult ar )
		{
			if (ar.AsyncState is Socket socket)
			{
				byte[] data = null;
				try
				{
					int length = socket.EndReceive( ar );

					if (length == 0)
					{
						NetworkLocalOffline( );
						return;
					};

					data = serverBuffer.SelectBegin( length );
					socket.BeginReceive( serverBuffer, 0, serverBuffer.Length, SocketFlags.None, new AsyncCallback( ServerSocketReveiceCallBack ), socket );
				}
				catch
				{
					NetworkLocalOffline( );
					return;
				}

				// 数据发送到DTU网口
				if (this.dtuSocket != null && data != null)
				{
					try
					{
						dtuSocket.Send( data, 0, data.Length, SocketFlags.None );
						this.tcpToDtuCount += data.Length;
						this.RequestSuccessCount++;

						this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _tcpToDtu, data, this.node.LogMsgFormatBinary );
						this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                // 更新设备的活动时间
					}
					catch (Exception ex)
					{
						NetworkDtuOffline( );
						LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"数据发送到DTU失败！{ex.Message}" );
					}
				}
			}
		}

		private void DtuReceiveCallBack( IAsyncResult ar )
		{
			if (ar.AsyncState is Socket socket)
			{
				byte[] data = null;
				try
				{
					int length = socket.EndReceive( ar );

					if (length == 0)
					{
						NetworkDtuOffline( );
						return;
					};
					data = socketBuffer.SelectBegin( length );
					socket.BeginReceive( socketBuffer, 0, socketBuffer.Length, SocketFlags.None, new AsyncCallback( DtuReceiveCallBack ), socket );
				}
				catch
				{
					NetworkDtuOffline( );
					return;
				}

				this.dtuActiveTime = HslTechnologyHelper.GetDateTimeNow( );
				this.dtuToTcpCount += data.Length;
				this.RequestSuccessCount++;

				this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _dtuToTcp, data, this.node.LogMsgFormatBinary );
				this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                // 更新设备的活动时间

				try
				{
					// 发送到本地的端口去
					if (data != null && this.localSocket != null)
					{
						localSocket.Send( data, 0, data.Length, SocketFlags.None );
					}
				}
				catch
				{
					// 记录日志
					NetworkLocalOffline( );
				}
			}
		}

		private void NetworkDtuOffline( )
		{
			this.dtuSocket?.Close( );
			this.dtuSocket = null;
			this.iPEndPoint = null;
			this.SetJsonValue( _remote, $"", false );


		}

		private void NetworkLocalOffline( )
		{
			this.localSocket?.Close( );
			this.localSocket = null;
			this.SetJsonValue( _local, $"[{this.node.IpAddress}:{this.node.Port}] Closed", false );
		}

		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			// 添加几个只读节点数据信息
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _local,
				Description = "连接的本地设备的状态信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _remote,
				Description = "远程连接的客户端信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _tcpToDtu,
				Description = "网口到DTU的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _dtuToTcp,
				Description = "DTU到网口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = "PingSuccess",
				Description = "Ping客户端的成功次数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );

			base.AnalysisByBussiness( business );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[TcpToDtu] [{GetDeviceNameWithPath( )}] [{node.IpAddress}:{node.Port}->{node.DtuIpAddress}]";

		#endregion

		#region Private Member

		private DateTime activeTime = HslTechnologyHelper.GetDateTimeNow( );
		private DateTime dtuActiveTime;                          // DTU的活动时间
		private NetworkDtuClient networkDtu;                     // DTU的辅助客户端
		private Socket localSocket = null;                       // 连接服务器的socket
		private Socket dtuSocket = null;                         // 连接的客户端的socket
		private IPEndPoint iPEndPoint;                           // 客户端的基本信息
		private Ping ping;                                       // 针对客户端的ping操作
		private long pingFailedCount = 0;                        // ping错误的次数
		private long pingSuccessCount = 0;                       // ping成功的次数
		private long tcpToDtuCount = 0;                          // 串口到网口的报文字节数
		private long dtuToTcpCount = 0;                          // 网口到串口的报文字节数

		private byte[] serverBuffer = new byte[2048];            // 服务器的数据缓冲
		private byte[] socketBuffer = new byte[2048];            // 缓冲的寄存器

		#endregion

		private NodeTcpToDTU node;
		private const string _local = "Local";
		private const string _remote = "Remote";
		private const string _tcpToDtu = "Tcp->Dtu";
		private const string _dtuToTcp = "Dtu->Tcp";
	}
}
