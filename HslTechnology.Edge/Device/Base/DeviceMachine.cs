using HslCommunication;
using HslCommunication.ModBus;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.Base
{
	public class DeviceMachine : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个PLC的数据访问的设备对象
		/// </summary>
		/// <param name="element">设备的Xml配置信息</param>
		public DeviceMachine( XElement element ) : base( element )
		{

		}

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			this.node = new NodeMachine( element );
		}


		#endregion

		#region Override

		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			if (this.business == null)
			{
				this.business = business;
			}
			else
			{
				base.AnalysisByBussiness( business );
			}
		}

		/// <inheritdoc/>
		public override void ExecuteAfterBussiness( DeviceExtInfoList deviceExtInfos )
		{
			if (!string.IsNullOrEmpty( node.OtherDevice ))
			{
				// 如果关联了其他的设备信息
				if (node.OtherDevice.StartsWith( "/" )) node.OtherDevice = node.OtherDevice.Substring( 1 );
				device = deviceExtInfos.GetByDevicePath( node.OtherDevice );
				if (device == null)
				{
					this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"关联其他的设备失败，路径[{node.OtherDevice}]未找到设备" );
					AnalysisByBussiness( this.business );
					return;
				}

				// 之前初始化跳过解析，此处解析一遍点位逻辑
				AnalysisByBussiness( this.business );

				for (int i = Requests.Count - 1; i >= 0; i--)
				{
					RequestBase request = Requests[i];
					if (request.RequestType == Node.RequestType.ScalarRead || request.RequestType == Node.RequestType.SourceRead)
					{
						Requests.RemoveAt( i );
						device.AddRequest( request );
					}
				}
			}
			else
			{
				AnalysisByBussiness( this.business );
			}
		}

		/// <inheritdoc/>
		public override OperateResult WriteValueByName( DeviceSingleAddressLabel addressLabel, string value )
		{
			if (this.device != null) return device.WriteValueByName( addressLabel, value );
			return base.WriteValueByName( addressLabel, value );
		}

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest( SourceReadRequest sourceReadRequest, int byteOffset, int index, IScalarTransform scalarTransform )
		{
			// 如果关联了其他的设备，那么就从其他的设备解析当前请求的实际地址信息
			if (this.device != null) return device.CalculateDevicePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
			return base.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[DeviceMachine] [{GetDeviceNameWithPath( )}]";

		#endregion
		#region Private Member

		private NodeMachine node;
		private DeviceCoreBase device;
		private BusinessEngine business;

		#endregion
	}
}
