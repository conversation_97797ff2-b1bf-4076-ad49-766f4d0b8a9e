using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication;
using System.Net.NetworkInformation;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Device.Base.Helper;
using HslTechnology.Edge.Resources;
using HslCommunication.LogNet;
using System.Threading;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 串口转TCP的设备对象
	/// </summary>
	public class DeviceSerialToDtu : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceSerialToDtu( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.networkDtu    = new NetworkDtuClient( );
			this.node          = new NodeSerialToDTU( element );
			this.node.PortName = deviceResources.EdgeSettings.PortMapping.GetSourcePort( this.node.PortName );
			this.dtuActiveTime = HslTechnologyHelper.GetDateTimeNow( );
		}

		#endregion

		#region Override Method

		protected override void UpdateDeviceActiveTimeWhemNoneRequest( )
		{
			this.SetJsonObjectValue( "__activeTime", this.activeTime.ToEdgeString( ) );
		}

		private int secondsTick = 0;

		/// <inheritdoc/>
		protected async override Task EverySecondsExecuteMethod( int second )
		{
			this.SetJsonValue( _dtuToSerial,  this.dtuToSerialCount,    false );
			this.SetJsonValue( _serialToDtu,  this.serialToDtuCount,    false );                       // 设置缓存
			this.SetJsonValue( "__success",   this.RequestSuccessCount, false );                       // 成功请求次数

			secondsTick++;
			if (secondsTick > 60) secondsTick = 1;

			try
			{
				if (!serialPort.IsOpen)
				{
					serialPort.Open( );
				}
				this.SetDeviceForceMessgae( _com, string.Empty );
				this.SetJsonValue(          _com, $"[{this.node.PortName}] Open", false );
			}
			catch (Exception ex)
			{
				// 串口打开失败则记录消息
				string msg = $"[{this.node.PortName}] Open Failed: {ex.Message}";
				this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				this.SetJsonValue(          _com,    msg, false );
				this.SetDeviceForceMessgae( _com,    msg );
			}

			// 网络正常，30s ping一次，网络不正常，10s ping一次
			if (this.node.PingEnable && (this.pingFailedCount == 0 && secondsTick % 30 == 0) || (this.pingFailedCount != 0 && secondsTick % 10 == 0))
			{
				if (this.dtuSocket != null && this.iPEndPoint != null)
				{
					// 每隔30秒。ping一下远程的DTU服务器
					try
					{
						PingReply reply = await ping.SendPingAsync( iPEndPoint.Address );
						if (reply.Status != IPStatus.Success)
						{
							this.pingFailedCount++;
						}
						else
						{
							this.pingSuccessCount++;
							this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
							this.pingFailedCount = 0;
						}
					}
					catch
					{
						this.pingFailedCount++;
					}

					if (this.pingFailedCount > 3)
					{
						NetworkDtuOffline( );
					}
				}
			}
			
			if (this.dtuSocket != null && (HslTechnologyHelper.GetDateTimeNow( ) - this.dtuActiveTime).TotalSeconds > this.node.DtuKeepAliveTimeout)
			{
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"当前DTU不活跃超过 [{this.node.DtuKeepAliveTimeout}] 秒，准备重连DTU服务器" );
				NetworkDtuOffline( );
			}

			if (secondsTick % 20 == 0 && this.dtuSocket == null)
			{
				// 每隔20秒钟检测连接服务器的情况
				OperateResult<Socket> create = await networkDtu.ConnectHslAlientClient( this.node );
				if (!create.IsSuccess)
				{
					// 网口启动失败的情况
					string msg = $"连接DTU [{this.node.DtuIpAddress}:{this.node.DtuPort}] 失败：{create.Message}";
					this.SetDeviceForceMessgae( _remote, msg );
					this.SetJsonValue(          _remote, msg, false );
					this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				}
				else
				{
					// 网口启动成功
					this.LogNet?.WriteInfo( GetDeviceNameWithPath( ), ToString( ), $"连接DTU [{this.node.DtuIpAddress}:{this.node.DtuPort}] 成功!" );
					try
					{
						create.Content.BeginReceive( socketBuffer, 0, socketBuffer.Length, SocketFlags.None, new AsyncCallback( ReceiveCallBack ), create.Content );
						this.dtuSocket?.Close( );
						this.dtuSocket = create.Content;
						this.iPEndPoint = (IPEndPoint)dtuSocket.RemoteEndPoint;
						this.SetDeviceForceMessgae( _remote, string.Empty );
						this.SetJsonValue(          _remote, this.iPEndPoint.ToString( ), false );
					}
					catch
					{
						string msg = $"连接DTU [{this.node.DtuIpAddress}:{this.node.DtuPort}] 成功，启动接收失败：{create.Message}";
						this.SetJsonValue(          _remote, msg, false );
						this.SetDeviceForceMessgae( _remote, msg );
						NetworkDtuOffline( );
					}
				}
			}

			// 串口和DTU都没有错误的情况下
			if (string.IsNullOrEmpty( this.deviceMessage.GetDefaultStringMsg( ) ))
				UpdateDeviceOnlineStatus( true );
			else
				UpdateDeviceOnlineStatus( false );

			await base.EverySecondsExecuteMethod( second );
		}

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			serialPort           = new SerialPort( );
			serialPort.PortName  = node.PortName;
			serialPort.BaudRate  = node.BaudRate;
			serialPort.DataBits  = node.DataBits;
			serialPort.StopBits  = node.StopBits;
			serialPort.Parity    = node.Parity;
			serialPort.RtsEnable = node.RtsEnable;

			ping = new Ping( );

			try
			{
				serialPort.DataReceived += SP_ReadData_DataReceived;
				serialPort.Open( );
				this.SetJsonValue(          _com, $"[{this.node.PortName}] Open", false );
				this.SetDeviceForceMessgae( _com, string.Empty );
			}
			catch (Exception ex)
			{
				// 串口启动失败怎么办，继续执行，因为每秒会检查串口是否成功打开
				string msg = $"[{this.node.PortName}] Open Failed: {ex.Message}";
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), msg );
				this.SetJsonValue(          _com, msg, false );
				this.SetDeviceForceMessgae( _com, msg );
			}


			this.SetJsonValue( "PingSuccess", this.pingSuccessCount, false );
			this.SetJsonValue( _serialToDtu, this.serialToDtuCount, false );
			this.SetJsonValue( _dtuToSerial, this.dtuToSerialCount, false );
		}

		private void SP_ReadData_DataReceived( object sender, SerialDataReceivedEventArgs e )
		{
			byte[] serialBuffer = new byte[1024];
			int recCount = 0;

			if (this.node.SleepTime > 0) HslTechnologyHelper.Sleep( this.node.SleepTime );
			// 接收数据
			try
			{
				recCount = serialPort.Read( serialBuffer, 0, serialBuffer.Length );
			}
			catch(Exception ex)
			{
				// 串口蹦了
				LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"从串口接收数据失败：{ex.Message}" );
				return;
			}
			if (recCount > 0)
			{
				this.serialToDtuCount += recCount;                                                          // 记录串口到DTU的数据总量
				this.RequestSuccessCount++;
				this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _serialToDtu, serialBuffer.SelectBegin( recCount ), this.node.LogMsgFormatBinary );
				this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                    // 更新设备的活动时间

				if (this.dtuSocket != null)
				{
					try
					{
						// 数据发送到DTU网口
						dtuSocket.Send( serialBuffer, 0, recCount, SocketFlags.None );
					}
					catch(Exception ex)
					{
						NetworkDtuOffline( );
						LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), $"数据发送到网口失败！{ex.Message}" );
					}
				}
			}
		}


		private void ReceiveCallBack( IAsyncResult ar )
		{
			if (ar.AsyncState is Socket socket)
			{
				byte[] data = null;
				try
				{
					int length = socket.EndReceive( ar );
					if (length != 0) data = socketBuffer.SelectBegin( length );
				}
				catch( Exception ex )
				{
					this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "EndReceive failed: " + ex.Message );
					NetworkDtuOffline( );
					return;
				}

				if (data == null)
				{
					NetworkDtuOffline( );
					return;
				}

				try
				{
					socket.BeginReceive( socketBuffer, 0, socketBuffer.Length, SocketFlags.None, new AsyncCallback( ReceiveCallBack ), socket );
				}
				catch( Exception ex )
				{
					this.LogNet?.WriteError( GetDeviceNameWithPath( ), ToString( ), "ReceiveCallBack failed: " +  ex.Message );
					NetworkDtuOffline( );
					return;
				}

				this.dtuActiveTime = HslTechnologyHelper.GetDateTimeNow( );

				this.dtuToSerialCount += data.Length;
				this.RequestSuccessCount++;
				this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), _dtuToSerial, data, this.node.LogMsgFormatBinary );
				this.activeTime = HslTechnologyHelper.GetDateTimeNow( );                                // 更新设备的活动时间

				try
				{
					if (data != null) serialPort.Write( data, 0, data.Length );   // 将DTU收到的数据 发送到串口去
				}
				catch(Exception ex)
				{
					// 记录日志
					LogNet?.WriteError( GetDeviceNameWithPath( ), string.Empty, $"从DTU接收数据成功，但是[{node.PortName}] 发送数据失败：" + ex.Message );
				}
			}
		}

		private void NetworkDtuOffline( )
		{
			this.dtuSocket?.Close( );
			this.dtuSocket = null;
			this.iPEndPoint = null;
			this.SetJsonValue( _remote, $"", false );
		}

		/// <inheritdoc/>
		public override void AnalysisByBussiness( BusinessEngine business )
		{
			// 添加几个只读节点数据信息
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _com,
				Description = "COM口的状态信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _remote,
				Description = "远程连接的客户端信息",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.String.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _serialToDtu,
				Description = "串口到网口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = _dtuToSerial,
				Description = "网口到串口的报文字节数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );
			Requests.Add( new ScalarCacheRequest( )
			{
				Name = "PingSuccess",
				Description = "Ping客户端的成功次数",
				Address = "",
				Length = -1,
				DataTypeCode = RegularNodeTypeItem.Int64.Text,
				ForbidRemoteWrite = true,                               // 禁止远程写入操作
				RequestInterval = 1000,                                 // 1秒请求一次
			} );

			base.AnalysisByBussiness( business );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SerialToDtu] [{GetDeviceNameWithPath( )}] [{node.PortName}->{node.DtuIpAddress}]";

		#endregion

		#region Private Member

		private DateTime activeTime = HslTechnologyHelper.GetDateTimeNow( );
		private DateTime dtuActiveTime;                          // DTU的活动时间
		private NetworkDtuClient networkDtu;                     // DTU的辅助客户端
		private SerialPort serialPort = null;                    // 串口交互的核心
		private Socket dtuSocket = null;                         // 连接的客户端的socket
		private IPEndPoint iPEndPoint;                           // 客户端的基本信息
		private Ping ping;                                       // 针对客户端的ping操作
		private long pingFailedCount = 0;                        // ping错误的次数
		private long pingSuccessCount = 0;                       // ping成功的次数
		private long serialToDtuCount = 0;                       // 串口到网口的报文字节数
		private long dtuToSerialCount = 0;                       // 网口到串口的报文字节数

		private byte[] socketBuffer = new byte[2048];            // 缓冲的寄存器

		#endregion

		private NodeSerialToDTU node;
		private const string _com = "COM";
		private const string _remote = "Remote";
		private const string _serialToDtu = "Serial->Dtu";
		private const string _dtuToSerial = "Dtu->Serial";


	}
}
