using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using HslCommunication.LogNet;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 用于多个设备的线程控制类，该类可以绑定多个设备对象，然后在一个线程里进行控制调度执行情况
	/// </summary>
	public class DeviceThreadExecuteControl
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public DeviceThreadExecuteControl( string uniqueID )
		{
			this.UniqueID = uniqueID;
			this.devices = new List<DeviceCoreBase>( );
			this.threadActiveTime = HslTechnologyHelper.GetDateTimeNow( );
		}

		#endregion

		#region Server Method

		/// <summary>
		/// 当前的线程控制器的唯一ID信息
		/// </summary>
		public string UniqueID { get; set; }

		/// <summary>
		/// 获取设置设备之前切换的时候，线程休眠的时间，默认为0，表示不进行休眠操作
		/// </summary>
		public int SleepTimeBetweenDevices { get; set; } = 0;

		/// <summary>
		/// 获取当前线程的采集时间
		/// </summary>
		public DateTime ThreadActiveTime => this.threadActiveTime;

		/// <summary>
		/// 当设备网络异常的时候，是否立即切换其他设备执行请求，默认为 true
		/// </summary>
		public bool ChangeDeviceWhenError { get; set; } = true;

		/// <summary>
		/// 当设备切换采集的时候，是否关闭当前的设备连接操作。
		/// </summary>
		public bool CloseWhenChangeDevice { get; set; } = false;

		/// <summary>
		/// 当前的设备线程控制器里的所有的设备的状态信息。默认都是有效的
		/// </summary>
		public Edge.Node.Core.DeviceStatusType DeviceStatus { get; set; } = Node.Core.DeviceStatusType.OnWork;

		/// <summary>
		/// 更新当前的线程活动时间
		/// </summary>
		public void UpdateThreadActiveTime( )
		{
			this.threadActiveTime = HslTechnologyHelper.GetDateTimeNow( );
		}

		/// <summary>
		/// 设置当前的管道信息
		/// </summary>
		/// <param name="edgePipe">管道</param>
		public void SetEdgePipe( IEdgePipe edgePipe )
		{
			this.edgePipe = edgePipe;
		}

		/// <summary>
		/// 新增一个控制设备信息
		/// </summary>
		/// <param name="deviceCoreBase">设备对象</param>
		public void AddDevices( DeviceCoreBase deviceCoreBase )
		{
			this.devices.Add( deviceCoreBase );
		}

		/// <summary>
		/// 启动读取数据，第一次调用是启动线程，之后调用为重启线程
		/// </summary>
		public void Start( )
		{
			if (Interlocked.CompareExchange( ref isStarted, 1, 0 ) == 0)
			{
				this.thread = new Thread( new ThreadStart( ThreadReadBackground ) );
				this.thread.IsBackground = true;
				this.thread.Priority = ThreadPriority.AboveNormal;
				this.thread.Start( );
			}
			else // 重新启动
			{
				Interlocked.Increment( ref isStarted );     // 增加线程重启次数的标记
				this.thread?.Abort( );
				this.thread = new Thread( new ThreadStart( ThreadReadBackground ) );
				this.thread.IsBackground = true;
				this.thread.Priority = ThreadPriority.AboveNormal;
				this.thread.Start( );
			}
		}

		/// <summary>
		/// 调度执行每个设备的线程写入任务
		/// </summary>
		public void ExecuteEveryDeviceMission( )
		{
			for (int i = 0; i < devices.Count; i++)
			{
				devices[i].ExecuteThreadInsertMission( );
			}
		}

		private async void ThreadReadBackground( )
		{
			devices.ForEach( devices => devices.ThreadCycleBeforeAction( ) );      // 每个设备启动之前的方法
			HslTechnologyHelper.Sleep( 1000 );                                     // 默认休息一下下

			while (this.isQuit == 0)
			{
				HslTechnologyHelper.Sleep( 20 );
				this.threadActiveTime = HslTechnologyHelper.GetDateTimeNow( );
				bool isAllDeviceFinished = true;

				for (int i = 0; i < devices.Count; i++)
				{
					if (devices[i].ThreadCycleIsFinished( )) continue;                                       // 如果线程已经退出采集则跳过
					isAllDeviceFinished = false;                                                             // 有线程没有退出采集

					this.threadActiveTime = HslTechnologyHelper.GetDateTimeNow( );
					ThreadCycleRunReturn threadCycleRunReturn = await devices[i].ThreadCycleRunAction( );
					if (threadCycleRunReturn == ThreadCycleRunReturn.Break)
					{
						devices[i].ThreadCycleFinishAction( );
					}
					else if (threadCycleRunReturn == ThreadCycleRunReturn.Request)
					{
						// 当前的设备发生了请求操作，设备切换的时候，根据属性 SleepTimeBetweenDevices 进行休眠指定时间
						if (SleepTimeBetweenDevices > 0) HslTechnologyHelper.Sleep( SleepTimeBetweenDevices );
						if (CloseWhenChangeDevice && edgePipe != null)
						{
							// devices[i].LogNet?.WriteError( devices[i].GetDeviceNameWithPath( ), "PipeSerial", "执行关闭共享的串口管道:" + edgePipe.GetPipe( ).ToString( ) );
							edgePipe.ClosePipe( );
						}
					}
				}

				if (isAllDeviceFinished) break;                   // 所有的设备都已经退出采集，控制线程也退出
			}
		}

		#endregion

		#region Private Member

		private List<DeviceCoreBase> devices;                     // 控制的所有的设备信息
		private Thread thread;                                    // 后台控制调度的线程
		private int isStarted = 0;                                // 是否启动了后台调度线程
		private int isQuit = 0;                                   // 是否准备从系统进行退出
		private DateTime threadActiveTime;                        // 线程的活动时间
		private IEdgePipe edgePipe;                               // 管道的接口信息
		//private ILogNet logNet;                                 // 系统的日志

		#endregion
	}
}
