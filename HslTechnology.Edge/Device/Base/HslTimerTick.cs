using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Device.Base
{
	/// <summary>
	/// 比较准时的时间控制类
	/// </summary>
	public class HslTimerTick
	{
		/// <inheritdoc cref="Node.GroupNode()"/>
		/// <param name="milliSeconds">按毫秒为单位的时间间隔</param>
		public HslTimerTick( int milliSeconds = 1000)
		{
			this.OperateTime = GetCorrectTime( HslTechnologyHelper.GetDateTimeNow( ).AddMilliseconds( - milliSeconds ) );
			this.MilliSeconds = milliSeconds;
			this.useCorrect = true;
		}

		/// <summary>
		/// 当前的时间计数是否触发
		/// </summary>
		/// <param name="currentTime">当前的时间信息</param>
		/// <returns>是否触发操作</returns>
		public bool IsTickHappen( DateTime currentTime )
		{
			DateTime executeExpect = this.OperateTime.AddMilliseconds( milliSeconds );
			if (currentTime < executeExpect) return false;                              // 还没到请求时间

			// 请求时间延时超过3个扫描周期，说明之前请求太花时间，当前需要重新计数
			// 超时在3个扫描周期之内的，会因为偶尔的超时会自动修复时间，累计超过60毫秒，则动态调整时间

			if ((currentTime - executeExpect).TotalMilliseconds > deviationTime)
			{
				if (useCorrect)
					this.OperateTime = GetCorrectTime( currentTime );
				else
					this.OperateTime = currentTime;
			}
			else
			{
				this.OperateTime = executeExpect;       // requestStart
			}
			return true;
		}

		/// <inheritdoc cref="IsTickHappen(DateTime)"/>
		public bool IsTickHappen( ) => IsTickHappen( HslTechnologyHelper.GetDateTimeNow( ) );

		/// <summary>
		/// 获取修正后的时间信息
		/// </summary>
		/// <param name="dateTime">等待修正的时间</param>
		/// <returns>修正后的时间</returns>
		private DateTime GetCorrectTime( DateTime dateTime )
		{
			if (milliSeconds % 1000 == 0)
			{
				// 按照秒为单位的修正
				if (dateTime.Millisecond < 200) return new DateTime( dateTime.Year, dateTime.Month, dateTime.Day, dateTime.Hour, dateTime.Minute, dateTime.Second );

				if (dateTime.Millisecond > 800)
				{
					dateTime.AddSeconds( 1 );
					return new DateTime( dateTime.Year, dateTime.Month, dateTime.Day, dateTime.Hour, dateTime.Minute, dateTime.Second );
				}

				return dateTime.AddMilliseconds( 100 );  // 其余情况每次向后推迟100ms，直到时间重新修正
			}
			else if (milliSeconds % 100 == 0)
			{
				// 按照100毫秒为单位进行修正时间
				if (dateTime.Millisecond % 100 < 50)
				{
					return dateTime.AddMilliseconds( - dateTime.Millisecond % 100 );
				}
				else
				{
					return dateTime.AddMilliseconds( 100 - dateTime.Millisecond % 100 );
				}
			}
			// 不进行纠偏
			return dateTime;
		}

		/// <summary>
		/// 获取下次的期望执行时间，这个期望时间是大于传入的当前时间的
		/// </summary>
		/// <param name="current">当前的时间</param>
		/// <returns>期望时间</returns>
		private DateTime GetNextExpectTime( DateTime current )
		{
			DateTime executeExpect = this.OperateTime.AddMilliseconds( milliSeconds );
			while (true)
			{
				if (executeExpect >= current) return executeExpect;

				long span = (long)(current - executeExpect).TotalMilliseconds / milliSeconds;

				if (span > 1) // 已经大于一个周期以上了
				{
					executeExpect.AddMilliseconds( span * milliSeconds );
				}
				else
				{
					executeExpect.AddMilliseconds( milliSeconds );
				}
			}
		}

		/// <summary>
		/// 最近一次的数据的操作时间
		/// </summary>
		public DateTime OperateTime { get; private set; }

		/// <summary>
		/// 获取或设置当前的时间间隔
		/// </summary>
		public int MilliSeconds
		{
			get => this.milliSeconds;
			set
			{
				if (value < 20) value = 20;
				this.milliSeconds = value;
			}
		}

		#region Private Member

		private int deviationTime = 60;                // 偏差时间
		private int milliSeconds = 1000;               // 请求间隔
		private bool useCorrect = false;               // 是否需要时间修正

		#endregion

		#region Static Helper

		/// <summary>
		/// 休息一秒之内的时间，直到秒的时间发生了变化位置
		/// </summary>
		public static void SleepToSenconds( )
		{
			DateTime start = HslTechnologyHelper.GetDateTimeNow( );
			while (true)
			{
				DateTime end = HslTechnologyHelper.GetDateTimeNow( );
				if (end.Second != start.Second) break;

				if(end.Millisecond > 980)
				{
					HslTechnologyHelper.Sleep( 0 );
				}
				else
				{
					HslTechnologyHelper.Sleep( 20 );
				}
			}
		}

		#endregion
	}
}
