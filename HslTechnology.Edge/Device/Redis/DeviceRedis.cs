using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.Enthernet.Redis;
using HslCommunication.MQTT;
using HslCommunication.Reflection;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Device.Redis
{
	/// <summary>
	/// Redis的设备信息
	/// </summary>
	public class DeviceRedis : DeviceCoreBase
	{

		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		/// <param name="element">设备的配置对象</param>
		public DeviceRedis( XElement element ) : base( element ) { }

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );
			
			this.node = new NodeRedisClient( element );
			this.redisClient = new RedisClient( this.node.IpAddress, this.node.Port, this.node.Password );
			// 网络信息
			this.redisClient.ReceiveTimeOut = node.ReceiveTimeOut;
			this.redisClient.ConnectTimeOut = node.ConnectTimeOut;

			this.RegisterRpcService( deviceResources.MqttServer, deviceResources.HttpServer, node, this.redisClient );
		}

		#endregion

		#region Protect Override

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			this.redisClient?.SetPersistentConnection( );
			this.redisClient?.SelectDB( this.node.DbBlock );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			this.redisClient?.ConnectClose( );
		}

		/// <inheritdoc/>
		public override OperateResult<string> CheckDeviceStatus( )
		{
			if (this.redisClient == null) return new OperateResult<string>( EdgeStringResource.DeviceNullException );

			OperateResult connect = this.redisClient.ConnectServer( );
			if (connect.IsSuccess) this.redisClient.ConnectClose( );

			return connect.Convert( ToString( ) );
		}

		#endregion

		#region Method Interface

		/// <inheritdoc cref="RedisClient.DeleteKey(string[])"/>
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		[HslMqttApi( Description = "删除给定的一个或多个 key 。不存在的 key 会被忽略。返回被删除KEY的数量" )]
		public async Task<OperateResult<int>> DeleteKeyAsync( string[] keys ) => await this.redisClient.DeleteKeyAsync( keys );

		/// <inheritdoc cref="RedisClient.ExistsKey(string)"/>
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		[HslMqttApi( Description = "检查给定 key 是否存在。若 key 存在，返回 1 ，否则返回 0" )]
		public async Task<OperateResult<int>> ExistsKeyAsync( string key ) => await this.redisClient.ExistsKeyAsync( key );

		/// <inheritdoc cref="RedisClient.ExpireKeyAsync(string, int)"/>
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		[HslMqttApi( Description = "为给定 key 设置生存时间，当 key 过期时(生存时间为 0 )，它会被自动删除。设置成功返回 1, 当 key 不存在或者不能为 key 设置生存时间时，返回 0" )]
		public async Task<OperateResult<int>> ExpireKeyAsync( string key, int seconds ) => await this.redisClient.ExpireKeyAsync( key, seconds );

		/// <inheritdoc cref="RedisClient.MoveKey(string, int)"/>
		[RpcExtensionInfo( DataType = DataType.Method )]
		[HslMqttApi( Description = "将当前数据库的 key 移动到给定的数据库 db 当中。" )]
		public async Task<OperateResult> MoveKeyAsync( string key, int db ) => await this.redisClient.MoveKeyAsync( key, db );

		/// <inheritdoc cref="RedisClient.ReadRandomKey"/>
		[RpcExtensionInfo( DataType = DataType.String )]
		[HslMqttApi( Description = "从当前数据库中随机返回(不删除)一个 key 。当数据库不为空时，返回一个 key 。当数据库为空时，返回 nil 。" )]
		public async Task<OperateResult<string>> ReadRandomKeyAsync( ) => await this.redisClient.ReadRandomKeyAsync( );

		/// <inheritdoc cref="RedisClient.RenameKey(string, string)"/>
		[RpcExtensionInfo( DataType = DataType.Method )]
		[HslMqttApi( Description = "将 key 改名为 newkey 。当 newkey 已经存在时， RENAME 命令将覆盖旧值。" )]
		public async Task<OperateResult> RenameKeyAsync( string key1, string key2 ) => await this.redisClient.RenameKeyAsync( key1, key2 );

		/// <inheritdoc cref="RedisClient.DecrementKey(string, long)"/>
		[RpcExtensionInfo( DataType = DataType.Int64 )]
		[HslMqttApi( Description = "将 key 所储存的值减去减量 decrement 。如果 key 不存在，那么 key 的值会先被初始化为 0 ，然后再执行 DECR 操作。返回减去 decrement 之后， key 的值。" )]
		public async Task<OperateResult<long>> DecrementKeyAsync( string key, long value ) => await this.redisClient.DecrementKeyAsync( key, value );

		/// <inheritdoc cref="RedisClient.ReadKey(string)"/>
		[RpcExtensionInfo( DataType = DataType.String )]
		[HslMqttApi( Description = "返回给定 key 的值。如果 key 不存在，那么这个 key 返回特殊值 null" )]
		public async Task<OperateResult<string>> ReadKeyAsync( string key ) => await this.redisClient.ReadKeyAsync( key );

		/// <inheritdoc cref="RedisClient.WriteKey(string, string)"/>
		[RpcExtensionInfo( DataType = DataType.Method )]
		[HslMqttApi( Description = "将字符串值 value 关联到 key 。如果 key 已经持有其他值， SET 就覆写旧值，无视类型。并且这个键原有的 TTL 将被清除。" )]
		public async Task<OperateResult> WriteKeyAsync( string key, string value ) => await this.redisClient.WriteKeyAsync( key, value );

		/// <inheritdoc cref="RedisClient.WriteExpireKeyAsync(string, string, long)"/>
		[RpcExtensionInfo( DataType = DataType.Method )]
		[HslMqttApi( Description = "将值 value 关联到 key ，并将 key 的生存时间设为 seconds (以秒为单位)。如果 key 已经存在， SETEX 命令将覆写旧值。" )]
		public async Task<OperateResult> WriteExpireKeyAsync( string key, string value, long seconds ) => await this.redisClient.WriteExpireKeyAsync( key, value, seconds );

		/// <inheritdoc cref="RedisClient.GetListLengthAsync(string)"/>
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		[HslMqttApi( Description = "返回列表 key 的长度。如果 key 不存在，则 key 被解释为一个空列表，返回 0 .如果 key 不是列表类型，返回一个错误。" )]
		public async Task<OperateResult<int>> GetListLengthAsync( string key ) => await this.redisClient.GetListLengthAsync( key );

		/// <inheritdoc cref="RedisClient.ReadListByIndexAsync(string, long)"/>
		[RpcExtensionInfo( DataType = DataType.String )]
		[HslMqttApi( Description = "返回列表 key 中，下标为 index 的元素。以 0 表示列表的第一个元素，以 -1 表示列表的最后一个元素，。如果 key 不是列表类型，返回一个错误" )]
		public async Task<OperateResult<string>> ReadListByIndexAsync( string key, long index ) => await this.redisClient.ReadListByIndexAsync( key, index );

		/// <inheritdoc cref="RedisClient.ListLeftPop(string)"/>
		[RpcExtensionInfo( DataType = DataType.String )]
		[HslMqttApi( Description = "移除并返回列表 key 的头元素。列表的头元素。当 key 不存在时，返回 nil 。" )]
		public async Task<OperateResult<string>> ListLeftPopAsync( string key ) => await this.redisClient.ListLeftPopAsync( key );

		/// <inheritdoc cref="RedisClient.ListLeftPush(string, string[])"/>
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		[HslMqttApi( Description = "将一个或多个值 value 插入到列表 key 的表头，如果 key 不存在，一个空列表会被创建并执行 LPUSH 操作。当 key 存在但不是列表类型时，返回一个错误。返回执行 LPUSH 命令后，列表的长度。" )]
		public async Task<OperateResult<int>> ListLeftPushAsync( string key, string[] values ) => await this.redisClient.ListLeftPushAsync( key, values );

		/// <inheritdoc cref="RedisClient.ListRange(string, long, long)"/>
		[RpcExtensionInfo( DataType = DataType.String, DataDimension = DataDimension.One )]
		[HslMqttApi( Description = "返回列表 key 中指定区间内的元素，区间以偏移量 start 和 stop 指定。都以 0 为底，以 -1 表示列表的最后一个元素，返回一个列表，包含指定区间内的元素。" )]
		public async Task<OperateResult<string[]>> ListRangeAsync( string key, long start, long stop ) => await this.redisClient.ListRangeAsync( key, start, stop );

		/// <inheritdoc cref="RedisClient.ListSet(string, long, string)"/>
		[RpcExtensionInfo( DataType = DataType.Method )]
		[HslMqttApi( Description = "设置数组的某一个索引的数据信息，当 index 参数超出范围，或对一个空列表( key 不存在)进行 LSET 时，返回一个错误。" )]
		public async Task<OperateResult> ListSetAsync( string key, long index, string value ) => await this.redisClient.ListSetAsync( key, index, value );

		/// <inheritdoc cref="RedisClient.ListRightPop(string)"/>
		[RpcExtensionInfo( DataType = DataType.String )]
		[HslMqttApi( Description = "移除并返回列表 key 的尾元素。当 key 不存在时，返回 nil 。" )]
		public async Task<OperateResult<string>> ListRightPopAsync( string key ) => await this.redisClient.ListRightPopAsync( key );

		/// <inheritdoc cref="RedisClient.ListRightPush(string, string[])"/>
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		[HslMqttApi( Description = "将一个或多个值 value 插入到列表 key 的表尾(最右边)。如果 key 不存在，一个空列表会被创建并执行 RPUSH 操作。当 key 存在但不是列表类型时，返回一个错误。返回表的长度。" )]
		public async Task<OperateResult<int>> ListRightPushAsync( string key, string[] values ) => await this.redisClient.ListRightPushAsync( key, values );

		/// <inheritdoc cref="RedisClient.DeleteHashKey(string, string[])"/>
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		[HslMqttApi( Description = "删除哈希表 key 中的一个或多个指定域，不存在的域将被忽略。返回被成功移除的域的数量，不包括被忽略的域。" )]
		public async Task<OperateResult<int>> DeleteHashKeyAsync( string key, string[] fields ) => await this.redisClient.DeleteHashKeyAsync( key, fields );

		/// <inheritdoc cref="RedisClient.ReadHashKey(string, string)"/>
		[RpcExtensionInfo( DataType = DataType.String )]
		[HslMqttApi( Description = "返回哈希表 key 中给定域 field 的值。当给定域不存在或是给定 key 不存在时，返回 nil " )]
		public async Task<OperateResult<string>> ReadHashKeyAsync( string key, string field ) => await this.redisClient.ReadHashKeyAsync( key, field );

		/// <inheritdoc cref="RedisClient.ReadHashKeyAll(string)"/>
		[RpcExtensionInfo( DataType = DataType.String, DataDimension = DataDimension.One )]
		[HslMqttApi( Description = "返回哈希表 key 中，所有的域和值。在返回值里，紧跟每个域名(field name)之后是域的值(value)，所以返回值的长度是哈希表大小的两倍。" )]
		public async Task<OperateResult<string[]>> ReadHashKeyAllAsync( string key ) => await this.redisClient.ReadHashKeyAllAsync( key );

		/// <inheritdoc cref="RedisClient.WriteHashKey(string, string, string)"/>
		[RpcExtensionInfo( DataType = DataType.Int32 )]
		[HslMqttApi( Description = "将哈希表 key 中的域 field 的值设为 value 。如果 field 是哈希表中的一个新建域，并且值设置成功，返回 1 。如果哈希表中域 field 已经存在且旧值已被新值覆盖，返回 0 。" )]
		public async Task<OperateResult<int>> WriteHashKeyAsync( string key, string field, string value ) => await this.redisClient.WriteHashKeyAsync( key, field, value );

		/// <inheritdoc cref="RedisClient.ReadServerTime"/>
		[RpcExtensionInfo( DataType = DataType.DateTime )]
		[HslMqttApi( Description = "获取服务器的时间戳信息，可用于本地时间的数据同步问题" )]
		public async Task<OperateResult<DateTime>> ReadServerTimeAsync( ) => await this.redisClient.ReadServerTimeAsync( );

		#endregion



		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Redis] [{GetDeviceNameWithPath( )}] [{node.GetSocketInfo( )}]";

		#endregion

		private RedisClient redisClient;                   // Redis数据库的核心通信类
		private NodeRedisClient node;                      // redis设备的节点对象


	}
}
