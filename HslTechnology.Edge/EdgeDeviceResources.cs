using HslCommunication;
using HslCommunication.Enthernet;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using HslCommunication.WebSocket;
using HslTechnology.Edge.Config;
using HslTechnology.Edge.DataBusiness;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Node;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge
{
	/// <summary>
	/// 网关系统当前本身的一些全局资源信息，可以每个设备访问到这些资源信息，并且使用这些资源内容做一些处理
	/// </summary>
	public class EdgeDeviceResources
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public EdgeDeviceResources( )
		{
			this.EdgeSerials     = new Dictionary<string, EdgePipeSerial>( );
			this.EdgeSocket      = new Dictionary<string, EdgePipeSocket>( );
			this.mqttServer      = new MqttServer( );
			this.httpServer      = new HttpServer( );
			this.webSocketServer = new WebSocketServer( );
			this.fatalMessage    = new ListMessage( );
			this.httpServer.IsCrossDomain = true;
		}

		/// <summary>
		/// 当前的共享的串口的管道资源
		/// </summary>
		public Dictionary<string, EdgePipeSerial> EdgeSerials { get; private set; }

		/// <summary>
		/// 当前的共享的网口的管道资源
		/// </summary>
		public Dictionary<string, EdgePipeSocket> EdgeSocket { get; private set; }

		/// <summary>
		/// 获取当前网关的参数配置信息
		/// </summary>
		public SettingsServer EdgeSettings { get => settingsServer; }

		/// <summary>
		/// 获取全局的Mqtt服务器对象
		/// </summary>
		public MqttServer MqttServer { get => this.mqttServer; }

		/// <summary>
		/// 获取全局的HttpServer
		/// </summary>
		public HttpServer HttpServer { get => this.httpServer; }

		/// <summary>
		/// 获取全局的WebSocketServer
		/// </summary>
		public WebSocketServer WebSocketServer { get => this.webSocketServer; }

		/// <summary>
		/// 网关最新的10个运行时的异常消息记录
		/// </summary>
		public ListMessage FatalMessage { get => this.fatalMessage; }

		/// <summary>
		/// 获取当前网关的服务器接口信息
		/// </summary>
		public IEdgeServices EdgeServices { get => this.edgeServices; }

		/// <summary>
		/// 当前网关资源的日志对象信息
		/// </summary>
		public ILogNet LogNet { get => this.logNet; }

		#region Set Properties

		/// <summary>
		/// 设置当前的网关的配置信息
		/// </summary>
		/// <param name="settingsServer">网关的配置信息</param>
		public void SetEdgeSettings( SettingsServer settingsServer )
		{
			this.settingsServer = settingsServer;
		}

		/// <summary>
		/// 设置当前网关的服务配置接口信息
		/// </summary>
		/// <param name="edgeServices">网关的接口服务信息</param>
		public void SetIEdgeServices( IEdgeServices edgeServices )
		{
			this.edgeServices = edgeServices;
		}

		/// <summary>
		/// 设置当前网关的日志对象
		/// </summary>
		/// <param name="logNet">日志对象</param>
		public void SetLogNet( ILogNet logNet )
		{
			this.logNet = logNet;
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 根据当前的管道唯一标记名称获取到当前的串口管道对象
		/// </summary>
		/// <param name="deviceXml">设备的配置信息</param>
		/// <returns></returns>
		public EdgePipeSerial GetPipeSerial( XElement deviceXml )
		{
			if (deviceXml.Parent != null)
			{
				if (deviceXml.Parent.Name == NodeType.GroupSerialPipe.ToString( ))
				{
					string key = DeviceHelper.GetUniquePath( deviceXml.Parent, true );
					if (string.IsNullOrEmpty( key )) return null;
					return EdgeSerials.ContainsKey( key ) ? EdgeSerials[key] : null;
				}
			}
			return null;
		}

		/// <summary>
		/// 根据当前的管道唯一标记名称获取到当前的网口管道对象
		/// </summary>
		/// <param name="deviceXml">设备的配置信息</param>
		/// <returns>网络管道对象</returns>
		public EdgePipeSocket GetPipeSocket( XElement deviceXml )
		{
			if (deviceXml.Parent != null)
			{
				if (deviceXml.Parent.Name == NodeType.GroupSocketPipe.ToString( ))
				{
					string key = DeviceHelper.GetUniquePath( deviceXml.Parent, true );
					if (string.IsNullOrEmpty( key )) return null;
					return EdgeSocket.ContainsKey( key ) ? EdgeSocket[key] : null;
				}
			}
			return null;
		}

		#endregion

		#region Private Member

		private MqttServer mqttServer;
		private HttpServer httpServer;
		private WebSocketServer webSocketServer;
		private SettingsServer settingsServer;
		private IEdgeServices edgeServices;
		private ILogNet logNet;
		private ListMessage fatalMessage;

		#endregion

	}
}
