using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Resources
{
#pragma warning disable CS1591 // 缺少对公共可见类型或成员的 XML 注释
	public class EdgeStringResource
	{
		public const string AdminAuthority         = "当前的操作需要管理员的权限";
		public const string OperateNoPermision     = "当前的账户没有操作的权限，请联系管理员。";
		public const string NoWritePermision       = "当前的账户没有写入数据的操作权限";
		public const string DeviceIniFailed        = "设备初始化失败: ";
		public const string DeviceNullException    = "设备未成功初始化，无法进行相关的读写操作";
		public const string MethodNullException    = "方法未成功初始化，为空值，无法进行相关的读写操作";



		public const string StringGather_DeviceIni  = "__DeviceIni";
	}

#pragma warning restore CS1591 // 缺少对公共可见类型或成员的 XML 注释
}
