using HslTechnology.Edge.Node.Regular;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	public class ScalarReadTypeConverter : StringConverter
	{
		/// <inheritdoc/>
		public override bool GetStandardValuesSupported( ITypeDescriptorContext context )
		{
			return true;  // 启动选择集，数据来自GetStandardValues
		}

		/// <inheritdoc/>
		public override StandardValuesCollection GetStandardValues( ITypeDescriptorContext context )
		{
			return new StandardValuesCollection(
				RegularNodeTypeItem.GetAllRegularNodeTypeItems( withBoolOfByte: false ).Select( m => m.Text ).ToArray( ) );
		}

		/// <inheritdoc/>
		public override bool GetStandardValuesExclusive( ITypeDescriptorContext context )
		{
			return false;  // 是否支持编辑操作
		}
	}
}
