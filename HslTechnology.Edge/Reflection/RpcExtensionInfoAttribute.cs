using HslTechnology.Edge.Node;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	/// <summary>
	/// Rpc接口的额外信息
	/// </summary>
	public class RpcExtensionInfoAttribute : Attribute
	{
		/// <summary>
		/// 实例化一个默认的对象，默认是标量
		/// </summary>
		public RpcExtensionInfoAttribute( )
		{
			DataDimension = DataDimension.Scalar;
		}

		private void DealType( Type returnType )
		{

			if (returnType == typeof( Nullable ))
			{
				DataType = DataType.Method;
			}
			else if (returnType == typeof( bool ))
			{
				DataType = DataType.Bool;
			}
			else if (returnType == typeof( bool[] ))
			{
				DataType = DataType.Bool;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( byte ))
			{
				DataType = DataType.Byte;
			}
			else if (returnType == typeof( byte[] ))
			{
				DataType = DataType.Byte;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( short ))
			{
				DataType = DataType.Int16;
			}
			else if (returnType == typeof( short[] ))
			{
				DataType = DataType.Int16;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( ushort ))
			{
				DataType = DataType.UInt16;
			}
			else if (returnType == typeof( ushort[] ))
			{
				DataType = DataType.UInt16;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( int ))
			{
				DataType = DataType.Int32;
			}
			else if (returnType == typeof( int[] ))
			{
				DataType = DataType.Int32;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( uint ))
			{
				DataType = DataType.UInt32;
			}
			else if (returnType == typeof( uint[] ))
			{
				DataType = DataType.UInt32;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( long ))
			{
				DataType = DataType.Int64;
			}
			else if (returnType == typeof( long[] ))
			{
				DataType = DataType.Int64;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( ulong ))
			{
				DataType = DataType.UInt64;
			}
			else if (returnType == typeof( ulong[] ))
			{
				DataType = DataType.UInt64;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( float ))
			{
				DataType = DataType.Float;
			}
			else if (returnType == typeof( float[] ))
			{
				DataType = DataType.Float;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( double ))
			{
				DataType = DataType.Double;
			}
			else if (returnType == typeof( double[] ))
			{
				DataType = DataType.Double;
				DataDimension = DataDimension.One;
			}
			else if (returnType == typeof( string ))
			{
				DataType = DataType.Float;
			}
			else if (returnType == typeof( string[] ))
			{
				DataType = DataType.Float;
				DataDimension = DataDimension.One;
			}
		}

		/// <summary>
		/// 根据返回的类型，自动创建扩展的 RPC 接口信息
		/// </summary>
		/// <param name="returnType">返回的类型</param>
		public RpcExtensionInfoAttribute( Type returnType ) : this( )
		{
			if (returnType.IsSubclassOf( typeof( Task ) ))
			{
				var propertyInfo = returnType.GetProperty( "Result" );
				if (propertyInfo == null)
					DataType = DataType.Method;
				else
				{
					DealType( propertyInfo.PropertyType );
				}
			}
			else
				DealType( returnType );
		}

		/// <summary>
		/// 获取或设置当前RPC返回的数据类型
		/// </summary>
		public DataType DataType { get; set; }

		/// <summary>
		/// 数据的维度信息，指示标量，还是一维数组，二维数组
		/// </summary>
		public DataDimension DataDimension { get; set; }

	}
}
