using HslCommunication.BasicFramework;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	/// <summary>
	/// 版本号的JSON转换器
	/// </summary>
	public class JsonSystemVersionConverter : JsonConverter
	{
		/// <inheritdoc/>
		public override void Write<PERSON>son( JsonWriter writer, object value, JsonSerializer serializer )
		{
			if (value is SystemVersion sv)
			{
				writer.WriteValue( sv.ToString( ) );
			}
		}

		/// <inheritdoc/>
		public override object ReadJson( JsonReader reader, Type objectType, object existingValue,
			JsonSerializer serializer )
		{
			var strValue = reader.Value.ToString( );
			return new SystemVersion( strValue );
		}

		/// <inheritdoc/>
		public override bool CanConvert( Type objectType )
		{
			return objectType == typeof( SystemVersion );
		}
	}
}
