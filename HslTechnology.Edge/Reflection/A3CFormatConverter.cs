using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	public class A3CFormatConverter : Int32Converter
	{
		/// <inheritdoc/>
		public override bool GetStandardValuesSupported( ITypeDescriptorContext context )
		{
			return true;  // 启动选择集，数据来自GetStandardValues
		}

		/// <inheritdoc/>
		public override StandardValuesCollection GetStandardValues( ITypeDescriptorContext context )
		{
			return new StandardValuesCollection(
				new string[] {
					"1",
					"2",
					"3",
					"4",} );
		}

		/// <inheritdoc/>
		public override bool GetStandardValuesExclusive( ITypeDescriptorContext context )
		{
			return false;  // 是否标准值
		}

		/// <inheritdoc/>
		public override bool IsValid( ITypeDescriptorContext context, object value )
		{
			int check = (int)value;
			if (check <= 0 || check > 4) return false;

			return base.IsValid( context, value );
		}
	}
}
