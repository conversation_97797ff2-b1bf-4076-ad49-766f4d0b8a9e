using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Device;
using System.Xml.Linq;
using HslCommunication.BasicFramework;
using HslCommunication;
using HslCommunication.MQTT;
using HslTechnology.Edge.Plugins;
using System.Security.Permissions;
using HslCommunication.Core;
using HslCommunication.Reflection;
using System.ComponentModel;
using HslTechnology.Edge.Device.PLCDevice;
using System.Linq.Expressions;

namespace HslTechnology.Edge.Reflection
{
	/// <summary>
	/// 反射相关的辅助方法
	/// </summary>
	public class EdgeReflectionHelper
	{
		/// <summary>
		/// 从设备的类型创建当前设备的节点信息
		/// </summary>
		/// <param name="deviceType">设备的类型</param>
		/// <returns>设备的节点对象，采用默认的实例化</returns>
		public static DeviceNode CreateInstanceFrom( DeviceType deviceType )
		{
			return CreateInstanceFrom( deviceType.ToString( ) );
		}

		public static DeviceNode CreateInstanceFrom( string deviceType )
		{
			if (deviceType == DeviceDefinitions.Plugins)
			{
				DeviceNode deviceNode = new DeviceNode( null, DeviceType.Plugins );
				if (string.IsNullOrEmpty( deviceNode.Name ))
					deviceNode.Name = "Plugins";
				return deviceNode;
			}

			if (DeviceDefinitions.ContainsDeviceKey(deviceType))
			{
				DeviceItemDefinition definition = DeviceDefinitions.Devices[deviceType];
				return (DeviceNode)definition.DeviceNode.Assembly.CreateInstance( definition.DeviceNode.FullName );
			}
			return null;
		}

		/// <summary>
		/// 从设备的定义类里创建当前的设备节点信息
		/// </summary>
		/// <param name="definition">设备定义对象</param>
		/// <returns>节点信息</returns>
		public static DeviceNode CreateInstanceFrom( DeviceItemDefinition definition )
		{
			if (definition.DeviceObject == null && definition.DeviceNode == null)
			{
				// 说明是插件
				DeviceNode deviceNode = new DeviceNode( null, DeviceType.Plugins );
				if (string.IsNullOrEmpty( deviceNode.Name ))
					deviceNode.Name = "Plugins";
				return deviceNode;
			}

			return (DeviceNode)definition.DeviceNode.Assembly.CreateInstance( definition.DeviceNode.FullName );
		}

		/// <summary>
		/// 根据Xml信息还原出实际的设备节点对象
		/// </summary>
		/// <param name="device">Xml的设备配置信息</param>
		/// <returns>设备的节点对象</returns>
		public static DeviceNode CreateInstanceFrom( XElement device )
		{
			string deviceType = device.Attribute( nameof( DeviceNode.DeviceType ) ).Value;
			if (deviceType == DeviceDefinitions.Plugins)
			{
				// 基于插件的设备信息，需要去寻找相关的插件节点信息，寻找dll，寻找dll指定的特性
				string pluginsType = GroupNode.GetXmlValue(device, nameof( DeviceNode.PluginsType ), string.Empty, m => m );
				if (string.IsNullOrEmpty( pluginsType )) return new DeviceNode( device, DeviceType.Plugins );

				// 应该先去插件资源里查找匹配的类型
				PluginsDeviceDefinition deviceDefinition = PluginsHelper.GetPluginsDeviceDefinition( pluginsType );
				if (deviceDefinition == null) return null;

				// 这里的设备节点定义分为两种情况，一种是继承自 DeviceNode，一种完全自定义的
				if (deviceDefinition.DeviceNode.IsSubclassOf( typeof( DeviceNode ) ))
				{
					DeviceNode deviceNode = deviceDefinition.DeviceNode.Assembly.CreateInstance( deviceDefinition.DeviceNode.FullName, true,
						BindingFlags.Default, null, new object[] { device }, null, null ) as DeviceNode;
					return deviceNode;
				}
				else
				{
					return new DeviceNode( device, DeviceType.Plugins );
				}
				
			}
			if (device.Name == nameof( NodeType.GroupSerialPipe )) return new NodeSerialPipe( device );
			if (device.Name == nameof( NodeType.GroupSocketPipe )) return new NodeSocketPipe( device );
			if (DeviceDefinitions.ContainsDeviceKey( deviceType ))
			{
				DeviceItemDefinition definition = DeviceDefinitions.Devices[deviceType];
				DeviceNode node = (DeviceNode)definition.DeviceNode.Assembly.CreateInstance( definition.DeviceNode.FullName );
				node.LoadByXmlElement( device );
				return node;
			}
			return null;
		}

		/// <summary>
		/// 根据设备的类型获取设备当前显示的图标信息
		/// </summary>
		/// <param name="deviceType">设备的类型</param>
		/// <returns>图片的关键字信息</returns>
		public static string GetImageKeyFrom( DeviceType deviceType )
		{
			return GetImageKeyFrom( deviceType.ToString( ) );
		}

		/// <summary>
		/// 根据设备的类型获取设备当前显示的图标信息
		/// </summary>
		/// <param name="deviceType">设备的类型</param>
		/// <returns>图片的关键字信息</returns>
		public static string GetImageKeyFrom( string deviceType )
		{
			if (DeviceDefinitions.ContainsDeviceKey( deviceType ))
			{
				DeviceItemDefinition definition = DeviceDefinitions.Devices[deviceType];
				return definition.ImageKey;
			}
			return null;
		}

		/// <summary>
		/// 通过指定的设备类型来获取相关的示例地址信息
		/// </summary>
		/// <param name="deviceType">设备的基础类型</param>
		/// <returns>地址示例的数组</returns>
		public static DeviceAddressExample[] GetDeviceAddressExamples( string deviceType )
		{
			if (DeviceDefinitions.ContainsDeviceKey( deviceType ))
			{
				DeviceItemDefinition definition = DeviceDefinitions.Devices[ deviceType ];
				if (definition.DeviceObject != null) return GetDeviceAddressExamples( definition.DeviceObject );
			}
			return null;
		}

		/// <summary>
		/// 通过指定的设备类型对象来获取相关的示例地址信息
		/// </summary>
		/// <param name="deviceCoreBase">设备的基础类型</param>
		/// <returns>地址示例的数组</returns>
		public static DeviceAddressExample[] GetDeviceAddressExamples( Type deviceCoreBase )
		{
			if (deviceCoreBase == null) return null;
			MethodInfo method = deviceCoreBase.GetMethod( "GetDeviceAddressExamples" );
			if (method != null)
			{
				object ret = method.Invoke( null, null );
				if (ret is DeviceAddressExample[] example)
				{
					return example;
				}
			}
			return null;
		}

		/// <summary>
		/// 根据设备类型信息，获取该设备的示例地址数据信息
		/// </summary>
		/// <param name="deviceType">设备的类型信息</param>
		/// <param name="pluginsType">插件的类型信息</param>
		/// <returns>地址示例数据</returns>
		public static OperateResult<DeviceAddressExample[]> GetDeviceExampleAddress( DeviceType deviceType, string pluginsType )
		{
			return GetDeviceExampleAddress( deviceType.ToString( ), pluginsType );
		}

		/// <summary>
		/// 根据设备类型信息，获取该设备的示例地址数据信息
		/// </summary>
		/// <param name="deviceType">设备的类型信息</param>
		/// <param name="pluginsType">插件的类型信息</param>
		/// <returns>地址示例数据</returns>
		public static OperateResult<DeviceAddressExample[]> GetDeviceExampleAddress( string deviceType, string pluginsType )
		{
			if (deviceType != DeviceDefinitions.Plugins)
			{
				if (DeviceDefinitions.ContainsDeviceKey( deviceType ))
				{
					DeviceItemDefinition definition = DeviceDefinitions.Devices[deviceType];
					if (definition.DeviceNode == null) return new OperateResult<DeviceAddressExample[]>( "当前的设备节点类型不存在，获取示例地址失败" );

					MethodInfo method = definition.DeviceNode.GetMethod( "GetDeviceAddressExamples" );
					if (method == null) return new OperateResult<DeviceAddressExample[]>( "当前的设备节点类型不正确，没有获取示例地址的接口方法" );

					object obj = definition.DeviceNode.Assembly.CreateInstance( definition.DeviceNode.FullName );
					DeviceAddressExample[] addressExamples = method.Invoke( obj, null ) as DeviceAddressExample[];
					return OperateResult.CreateSuccessResult( addressExamples );
				}
				else
					return new OperateResult<DeviceAddressExample[]>( "当前的设备类不支持获取方法信息，因为没有绑定节点对象信息" );
			}
			else
			{
				// 应该先去缓存查找类型
				PluginsDeviceDefinition deviceDefinition = PluginsHelper.GetPluginsDeviceDefinition( pluginsType );
				if (deviceDefinition == null) return new OperateResult<DeviceAddressExample[]>( $"当前的插件类型[{pluginsType}]在网关插件里差不到" );

				MethodInfo method = deviceDefinition.DeviceNode.GetMethod( "GetDeviceAddressExamples" );
				if (method == null) return new OperateResult<DeviceAddressExample[]>( "当前的设备节点类型没有定义示例地址的接口方法，无法获取地址示例" );

				object obj = deviceDefinition.DeviceNode.Assembly.CreateInstance( deviceDefinition.DeviceNode.FullName );
				object address = method.Invoke( obj, null );
				if (address is DeviceAddressExample[] addressExamples)
				{
					return OperateResult.CreateSuccessResult( addressExamples );
				}
				else if (address is List<object> list)
				{
					// 如果来自自定义的插件里，完全自定义的一个方法
					List<DeviceAddressExample> result = new List<DeviceAddressExample>( );
					foreach (object item in list)
					{
						try
						{
							result.Add( new DeviceAddressExample( item ) );
						}
						catch
						{

						}
					}

					return OperateResult.CreateSuccessResult( result.ToArray( ) );
				}
				else
				{
					return OperateResult.CreateSuccessResult( new DeviceAddressExample[0] );
				}

			}
		}

		/// <summary>
		/// 根据设备的类型，获取该设备的注册的方法信息
		/// </summary>
		/// <param name="deviceType">设备类型</param>
		/// <returns>设备上包含的方法信息</returns>
		public static OperateResult<MqttRpcApiInfo[]> GetMethodByDeviceType( string deviceType )
		{
			if (DeviceDefinitions.ContainsDeviceKey( deviceType ))
			{
				DeviceItemDefinition definition = DeviceDefinitions.Devices[deviceType];
				if (definition == null) return new OperateResult<MqttRpcApiInfo[]>( "当前的设备类不支持获取方法信息" );

				MqttRpcApiInfo[] rpcApiInfos = MqttHelper.GetSyncServicesApiInformationFromObject( "", definition.DeviceObject ).ToArray( );
				return OperateResult.CreateSuccessResult( rpcApiInfos );
			}
			else
				return new OperateResult<MqttRpcApiInfo[]>( "当前的设备类不支持获取方法信息" );
		}

		/// <summary>
		/// 根据设备的对象，获取相关的方法的定义信息。
		/// </summary>
		/// <param name="device">设备信息</param>
		/// <param name="methodRpcInfos">输出的方法列表信息</param>
		/// <returns>设备相关的字典信息</returns>
		public static Dictionary<string, MethodRpcInfo> GetMethodByDeviceObject( object device, out MethodRpcInfo[] methodRpcInfos )
		{
			List<MethodRpcInfo> methods = new List<MethodRpcInfo>( );
			MqttRpcApiInfo[] rpcApiInfos = device.GetType( ).IsSubclassOf( typeof( DeviceCoreBase ) ) ?
				MqttHelper.GetSyncServicesApiInformationFromObject( "", device ).ToArray( ) : GetPluginApiInfo( "", device );

			Dictionary<string, MethodRpcInfo> dicts = new Dictionary<string, MethodRpcInfo>( );
			RpcExtensionInfoAttribute rpcExtensionInfoAttribute = null;

			for (int i = 0; i < rpcApiInfos.Length; i++)
			{
				object[] customAttributes = rpcApiInfos[i].Method.GetCustomAttributes( typeof( RpcExtensionInfoAttribute ), inherit: false );
				if (customAttributes == null || customAttributes.Length == 0)
				{
					rpcExtensionInfoAttribute = new RpcExtensionInfoAttribute( rpcApiInfos[i].Method.ReturnType );
				}
				else
				{
					rpcExtensionInfoAttribute = (RpcExtensionInfoAttribute)customAttributes[0];
				}

				MethodRpcInfo methodRpc = new MethodRpcInfo( );
				methodRpc.RpcApiInfo = rpcApiInfos[i];
				methodRpc.ExtensionInfoAttribute = rpcExtensionInfoAttribute;
				dicts.Add( rpcApiInfos[i].ApiTopic, methodRpc );
				methods.Add( methodRpc );
			}
			methodRpcInfos = methods.ToArray( );
			return dicts;
		}

		internal static MqttRpcApiInfo[] GetPluginApiInfo( string api, object obj )
		{
			Type objType = null;
			if (obj is Type type)   // 表示注册的是静态的方法或是静态属性
			{
				objType = type;
				obj = null;
			}
			else
			{
				objType = obj.GetType( );
			}

			PropertyInfo allApiNames = objType.GetProperty( "ApiMethods" );
			string[] names = new string[0];
			if (allApiNames != null)
			{
				// 这里加了个判断，如果是静态方法，则直接获取值，否则，实例化一个默认的对象，再获取值信息
				if (allApiNames.GetMethod.IsStatic)
					names = allApiNames.GetValue( obj ) as string[];
				else if (obj == null)
				{
					obj = objType.Assembly.CreateInstance( objType.FullName );
					names = allApiNames.GetValue( obj ) as string[];
				}
				else
				{
					names = allApiNames.GetValue( obj ) as string[];
				}
			}
			if (names == null || names.Length == 0) return new MqttRpcApiInfo[0];  // 没有注册的方法内容

			MethodInfo[] methodInfos = objType.GetMethods( );
			List<MqttRpcApiInfo> mqttSyncServices = new List<MqttRpcApiInfo>( );
			foreach (var method in methodInfos)
			{
				if (!names.Contains( method.Name )) continue;

				var apiResult = GetMqttSyncServicesApiFromMethod( api, method, obj );
				if (!apiResult.IsSuccess) continue;

				mqttSyncServices.Add( apiResult.Content );
			}
			PropertyInfo[] propertyInfos = objType.GetProperties( );
			foreach (var property in propertyInfos)
			{
				var apiResult = MqttHelper.GetMqttSyncServicesApiFromProperty( api, property, obj, null );
				if (!apiResult.IsSuccess) continue;

				if (!apiResult.Content1.PropertyUnfold)
					mqttSyncServices.Add( apiResult.Content2 );
				else
				{
					if (property.GetValue( obj, null ) == null) continue;
					var apis = MqttHelper.GetSyncServicesApiInformationFromObject( apiResult.Content2.ApiTopic, property.GetValue( obj, null ), null );
					mqttSyncServices.AddRange( apis );
				}
			}

			return mqttSyncServices.ToArray( );
		}

		private static OperateResult<MqttRpcApiInfo> GetMqttSyncServicesApiFromMethod( string api, MethodInfo method, object obj )
		{
			MqttRpcApiInfo mqttRpcApiInfo = new MqttRpcApiInfo( );
			mqttRpcApiInfo.SourceObject = obj;
			mqttRpcApiInfo.Method = method;
			object[] customAttributes = method.GetCustomAttributes( typeof( DescriptionAttribute ), inherit: false );
			if (customAttributes != null && customAttributes.Length >= 1)
			{
				if (customAttributes[0] is DescriptionAttribute attribute)
				{
					mqttRpcApiInfo.Description = attribute.Description;
				}
			}
			string apiName = method.Name;
			customAttributes = method.GetCustomAttributes( typeof( DisplayNameAttribute ), inherit: false );
			if (customAttributes != null && customAttributes.Length >= 1)
			{
				if (customAttributes[0] is DisplayNameAttribute attribute)
				{
					apiName = attribute.DisplayName;
				}
			}

			if (string.IsNullOrEmpty( api ))
			{
				mqttRpcApiInfo.ApiTopic = apiName;
			}
			else
			{
				mqttRpcApiInfo.ApiTopic = api + "/" + apiName;
			}

			ParameterInfo[] parameters = method.GetParameters( );
			StringBuilder stringBuilder = new StringBuilder( );
			if (method.ReturnType.IsSubclassOf( typeof( Task ) ))
			{
				stringBuilder.Append( "Task<" + method.ReturnType.GetProperty( "Result" ).PropertyType.Name + ">" );
			}
			else
			{
				stringBuilder.Append( method.ReturnType.Name );
			}

			stringBuilder.Append( " " );
			stringBuilder.Append( mqttRpcApiInfo.ApiTopic );
			stringBuilder.Append( "(" );
			for (int i = 0; i < parameters.Length; i++)
			{
				if (parameters[i].ParameterType != typeof( ISessionContext ))
				{
					stringBuilder.Append( parameters[i].ParameterType.Name );
					stringBuilder.Append( " " );
					stringBuilder.Append( parameters[i].Name );
					if (i != parameters.Length - 1)
					{
						stringBuilder.Append( "," );
					}
				}
			}

			stringBuilder.Append( ")" );
			mqttRpcApiInfo.MethodSignature = stringBuilder.ToString( );
			mqttRpcApiInfo.ExamplePayload = HslReflectionHelper.GetParametersFromJson( method, parameters ).ToString( );
			return OperateResult.CreateSuccessResult( mqttRpcApiInfo );
		}


		/// <summary>
		/// 根据设备的XML配置信息来创建真实的设备对象
		/// </summary>
		/// <param name="device">设备的配置信息</param>
		/// <returns>设备的实体对象</returns>
		public static OperateResult<DeviceCoreBase> GetDeviceCoreFrom( XElement device )
		{
			if (device.Name != NodeType.DeviceNode.ToString( ) &&
				device.Name != NodeType.RobotNode.ToString( ) &&
				device.Name != NodeType.CncNode.ToString( )) return null;       // 节点名称校验失败

			if (device.Attribute( "DeviceType" ) == null) return null;             // 节点不是设备的情况
			string deviceType = "Device";
			try
			{
				deviceType = device.Attribute( nameof( DeviceNode.DeviceType ) ).Value;
				if (!DeviceDefinitions.ContainsDeviceKey( deviceType )) return new OperateResult<DeviceCoreBase>( $"当前的设备类型[{deviceType}]没有在设备协议列表是进行注册操作！" ); ;   // 如果在注册列表里没有找到的话
			}
			catch
			{
				return null;    // 如果遇见不支持的 DeviceType
			}

			if (deviceType == DeviceDefinitions.Plugins)
			{
				// 基于插件的设备信息，需要去寻找相关的插件节点信息，寻找dll，寻找dll指定的特性
				string pluginsType = device.Attribute( nameof( DeviceNode.PluginsType ) ).Value;
				// 应该先去缓存查找类型
				PluginsDeviceDefinition deviceDefinition = PluginsHelper.GetPluginsDeviceDefinition( pluginsType );
				if (deviceDefinition == null)
				{
					// 当前的插件设备里没有相关的类型定义
					return OperateResult.CreateSuccessResult( new DeviceCoreBase( device, $"当前设备绑定的插件不存在，设备无法请求" ) );
				}

				try
				{
					if (deviceDefinition.DeviveObject == typeof( DeviceCustomPlugins ))
					{
						// 说明是完全自定义创建的插件
						object objDevice = deviceDefinition.PluginDeivceType.Assembly.CreateInstance( deviceDefinition.PluginDeivceType.FullName, true, BindingFlags.Default, null, new object[] { }, null, null );
						// 先找下是否有定义的内置资源，有的话，就添加进XML元素里
						return OperateResult.CreateSuccessResult( (DeviceCoreBase)new DeviceCustomPlugins( device, objDevice ) );
					}
					else
					{
						// 说明创建的是DeviceCoreBase类的子类的设备对象
						if (deviceDefinition.PluginDeivceType != null && deviceDefinition.Tag != null)
						{
							// 需要先找到绑定的节点类的设备类型，然后强制修改这个信息
							DeviceNode deviceNode = deviceDefinition.DeviceNode.Assembly.CreateInstance( deviceDefinition.DeviceNode.FullName, true, BindingFlags.Default, null, new object[0], null, null ) as DeviceNode;
							//XElement device_clone = XElement.Parse( device.ToString( ) );
							device.SetAttributeValue( "DeviceType", deviceNode.DeviceType );
							device.SetAttributeValue( nameof( DeviceNode.PluginsType ), string.Empty );

							object objDevice = deviceDefinition.DeviveObject.Assembly.CreateInstance( deviceDefinition.DeviveObject.FullName, true, BindingFlags.Default, null, new object[] { device }, null, null );
							if (objDevice is DeviceCore deviceCore)
							{
								if (deviceDefinition.PluginDeivceType != null)
								{
									// 实际通过扩展设备实现的
									object pluginsDevice = deviceDefinition.PluginDeivceType.Assembly.CreateInstance( deviceDefinition.PluginDeivceType.FullName, true, BindingFlags.Default, null, new object[] { }, null, null );
									deviceCore.SetPluginsDeviceObject( pluginsDevice );
								}
							}
							return OperateResult.CreateSuccessResult( (DeviceCoreBase)objDevice );
						}
						else
						{
							// 依赖网关基础库创建插件的方式
							object objDevice = deviceDefinition.DeviveObject.Assembly.CreateInstance( deviceDefinition.DeviveObject.FullName, true, BindingFlags.Default, null, new object[] { device }, null, null );
							return OperateResult.CreateSuccessResult( (DeviceCoreBase)objDevice );
						}
					}
				}
				catch (Exception ex)
				{
					return new OperateResult<DeviceCoreBase>( $"当前的插件设备类型[{pluginsType}]创建失败，原因：" + ex.Message );
				}
			}
			else
			{
				// 如果想要禁用某个设备的配置
				// if (deviceType == DeviceType.AllenBradleyCIP) return new OperateResult<DeviceCoreBase>( $"当前的设备类型[{deviceType}] 在网关服务器禁用！" );

				if (DeviceDefinitions.ContainsDeviceKey( deviceType ))
				{
					DeviceItemDefinition definition = DeviceDefinitions.Devices[deviceType];
					if (definition.DeviceObject == null) return new OperateResult<DeviceCoreBase>( $"当前的设备类型[{deviceType}]没有绑定任何的实体设备！" );

					try
					{
						DeviceCoreBase deviceCoreBase = definition.DeviceNode.Assembly.CreateInstance( definition.DeviceObject.FullName,
							true, BindingFlags.Default, null, new object[] { device }, null, null ) as DeviceCoreBase;
						if (deviceCoreBase == null) return new OperateResult<DeviceCoreBase>( $"该设备的类型不是继承自[{nameof( DeviceCoreBase )}]类型，无法创建设备对象! " );
						return OperateResult.CreateSuccessResult( deviceCoreBase );
					}
					catch (Exception ex)
					{
						return new OperateResult<DeviceCoreBase>( ex.Message );
					}
				}
				return new OperateResult<DeviceCoreBase>( $"当前的设备类型[{deviceType}]没有在设备协议列表是进行注册操作！" ); ;   // 如果在注册列表里没有找到的话
			}
		}

		/// <summary>
		/// 获取指定类型及对象的方法信息，并将其转换为一个表达式
		/// </summary>
		/// <param name="type">对象的类型信息</param>
		/// <param name="deviceObject">设备类对象</param>
		/// <param name="methodName">方法的名称</param>
		/// <returns>表达式</returns>
		public static Func<int, Task> ExecuteTimerMethod( Type type, object deviceObject, string methodName )
		{
			MethodInfo method = type.GetMethod( methodName );
			if (method == null) return null;

			ConstantExpression target = Expression.Constant( deviceObject, type );
			ParameterExpression seconds = Expression.Parameter( typeof( int ), "seconds" );

			// 构造表达式
			Expression call = Expression.Call( target, method, seconds );

			var lambda = Expression.Lambda<Func<int, Task>>( call, seconds );
			return lambda.Compile( );
		}
	}
}
