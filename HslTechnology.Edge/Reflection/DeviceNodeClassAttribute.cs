using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;

namespace HslTechnology.Edge.Reflection
{
	/// <summary>
	/// 设备类型的特性，用来绑定节点的实体类的操作，这样就可以脱离很多的if判断，在新增设备的时候更加便捷<br />
	/// The characteristics of the device type are used to bind the operation of the entity class of the node, 
	/// so that it can be separated from a lot of if judgments, and it is more convenient when adding devices
	/// </summary>
	public class DeviceNodeClassAttribute : Attribute
	{
		/// <summary>
		/// 实例化一个设备类型的特性类<br />
		/// Instantiate a characteristic class of a device type
		/// </summary>
		/// <param name="deviceNode">绑定的设备节点类型</param>
		/// <param name="imageKey">绑定的小图片关键字</param>
		public DeviceNodeClassAttribute( Type deviceNode, string imageKey = "" )
		{
			this.DeviceNode = deviceNode;
			this.ImageKey = imageKey;
		}

		/// <summary>
		/// 获取或设置绑定的设备节点的类
		/// </summary>
		public Type DeviceNode { get; set; }

		/// <summary>
		/// 获取或设置设备的实体对象类型，用来真正的采集设备的
		/// </summary>
		public Type DeviceObject { get; set; }

		/// <summary>
		/// 获取或设置当前的设备类型绑定的小图标列表
		/// </summary>
		public string ImageKey { get; set; }
	}
}
