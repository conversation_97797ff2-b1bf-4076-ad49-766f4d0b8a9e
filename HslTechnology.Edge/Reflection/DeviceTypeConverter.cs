using HslCommunication.BasicFramework;
using HslTechnology.Edge.Node;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	/// <summary>
	/// 设备类型的转换信息
	/// </summary>
	public class DeviceTypeConverter : StringConverter
	{
		/// <inheritdoc/>
		public override bool GetStandardValuesSupported( ITypeDescriptorContext context )
		{
			return true;  // 启动选择集，数据来自GetStandardValues
		}

		/// <inheritdoc/>
		public override StandardValuesCollection GetStandardValues( ITypeDescriptorContext context )
		{
			return new StandardValuesCollection(
				Enum.GetValues( typeof( DeviceType ) ) );
		}

		/// <inheritdoc/>
		public override bool GetStandardValuesExclusive( ITypeDescriptorContext context )
		{
			return false;  // 是否标准值
		}

	}
}
