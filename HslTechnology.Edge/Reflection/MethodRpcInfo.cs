using HslCommunication.MQTT;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	/// <summary>
	/// 基于方法接口的信息
	/// </summary>
	public class MethodRpcInfo
	{
		/// <summary>
		/// RPC接口的基本信息内容
		/// </summary>
		public MqttRpcApiInfo RpcApiInfo { get; set; }

		/// <summary>
		/// 接口扩展信息
		/// </summary>
		public RpcExtensionInfoAttribute ExtensionInfoAttribute { get; set; }
	}
}
