using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	public class RegularScalarNodeConverter : TypeConverter
	{
		/// <inheritdoc/>
		public override PropertyDescriptorCollection GetProperties( ITypeDescriptorContext context, object value, Attribute[] attributes )
		{
			return TypeDescriptor.GetProperties( value, attributes );
		}

		/// <inheritdoc/>
		public override bool GetPropertiesSupported( ITypeDescriptorContext context )
		{
			return true;
		}
	}
}
