using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	public class SerialPortConverter : StringConverter
	{
		/// <inheritdoc/>
		public override bool GetStandardValuesSupported( ITypeDescriptorContext context )
		{
			return true;  // 启动选择集，数据来自GetStandardValues
		}

		/// <inheritdoc/>
		public override StandardValuesCollection GetStandardValues( ITypeDescriptorContext context )
		{
			return new StandardValuesCollection( StandardValues );
		}

		/// <inheritdoc/>
		public override bool GetStandardValuesExclusive( ITypeDescriptorContext context )
		{
			return false;  // 是否标准值
		}

		/// <inheritdoc/>
		public override bool IsValid( ITypeDescriptorContext context, object value )
		{
			return base.IsValid( context, value );
		}

		/// <summary>
		/// 获取或设置可选的串口信息
		/// </summary>
		public static string[] StandardValues { get; set; }
	}
}
