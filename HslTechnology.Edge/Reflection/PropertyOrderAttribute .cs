using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	[AttributeUsage( AttributeTargets.Property )]
	public class PropertyOrderAttribute : Attribute
	{
		private int _order;
		public PropertyOrderAttribute( int order )
		{
			_order = order;
		}

		public int Order
		{
			get
			{
				return _order;
			}
		}

	}
}
