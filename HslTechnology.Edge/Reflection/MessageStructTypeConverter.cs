using HslTechnology.Edge.Node.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	public class MessageStructTypeConverter : EnumConverter
	{
		public MessageStructTypeConverter( ) : base( typeof( MessageStructType ) )
		{

		}
		public override object ConvertFrom( ITypeDescriptorContext context, CultureInfo culture, object value )
		{
			return base.ConvertFrom( context, culture, value );
		}
	}
}
