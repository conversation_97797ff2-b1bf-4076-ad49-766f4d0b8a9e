using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	public class BaudRateItemConverter : Int32Converter
	{
		/// <inheritdoc/>
		public override bool GetStandardValuesSupported( ITypeDescriptorContext context )
		{
			return true;  // 启动选择集，数据来自GetStandardValues
		}

		/// <inheritdoc/>
		public override StandardValuesCollection GetStandardValues( ITypeDescriptorContext context )
		{
			return new StandardValuesCollection( 
				new string[] { 
					"600", 
					"1200", 
					"2400",
					"4800",
					"9600",
					"19200",
					"38400"} );
		}

		/// <inheritdoc/>
		public override bool GetStandardValuesExclusive( ITypeDescriptorContext context )
		{
			return false;  // 是否标准值
		}

		/// <inheritdoc/>
		public override bool IsValid( ITypeDescriptorContext context, object value )
		{
			int check = (int)value;
			if (check <= 0) return false;

			return base.IsValid( context, value );
		}


	}
}
