using HslCommunication.BasicFramework;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Reflection
{
	public class JsonPasswordConverter : JsonConverter
	{
		/// <inheritdoc/>
		public override void Write<PERSON><PERSON>( JsonWriter writer, object value, JsonSerializer serializer )
		{
			if (value is string pwd)
			{
				RSACryptoServiceProvider rsa = HslCommunication.Core.Security.RSAHelper.CreateRsaProviderFromPrivateKey( global::HslTechnology.Edge.Properties.Resources.AAA );
				writer.WriteValue( Convert.ToBase64String( rsa.Encrypt( Encoding.UTF8.GetBytes( pwd ), false ) ) );
			}
		}

		/// <inheritdoc/>
		public override object ReadJson( JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer )
		{
			return TransValueFromJson( reader.Value.ToString( ) );
		}

		/// <inheritdoc/>
		public override bool CanConvert( Type objectType )
		{
			return true;
		}

		public static string TransValueFromJson( string json )
		{
			var strValue = json;
			if (strValue == null) return string.Empty;
			if (strValue.Length < 100) return strValue;

			RSACryptoServiceProvider rsa = HslCommunication.Core.Security.RSAHelper.CreateRsaProviderFromPrivateKey( global::HslTechnology.Edge.Properties.Resources.AAA );
			byte[] buffer = Convert.FromBase64String( strValue );
			byte[] data = rsa.Decrypt( buffer, false );
			return Encoding.UTF8.GetString( data );
		}
	}
}
