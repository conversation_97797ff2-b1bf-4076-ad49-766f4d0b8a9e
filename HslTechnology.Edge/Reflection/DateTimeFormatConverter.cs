using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Converters;

namespace HslTechnology.Edge.Reflection
{
	/// <summary>
	/// 时间格式转换器
	/// </summary>
	public class DateTimeFormatConverter : IsoDateTimeConverter
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public DateTimeFormatConverter( )
		{
			DateTimeFormat = "yyyy-MM-dd HH:mm:ss";
		}
	}
}
