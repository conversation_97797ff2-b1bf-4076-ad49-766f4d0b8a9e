using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// BCD码的格式信息
	/// </summary>
	public enum BCDFormat
	{
		/// <summary>
		/// 8421编码，最常见的BCD编码
		/// </summary>
		C8421 = 1,

		/// <summary>
		/// 5421编码
		/// </summary>
		C5421 = 2,

		/// <summary>
		/// 2421编码
		/// </summary>
		C2421 = 3,

		/// <summary>
		/// 8421余3码
		/// </summary>
		C3 = 4,

		/// <summary>
		/// 格雷码
		/// </summary>
		Gray = 5,
	}
}
