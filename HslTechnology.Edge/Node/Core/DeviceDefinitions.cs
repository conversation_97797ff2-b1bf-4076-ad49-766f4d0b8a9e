using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Server;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Device.CncDevice;
using HslTechnology.Edge.Device.RobotDevice;
using HslTechnology.Edge.Device.ServerDevice;
using HslTechnology.Edge.Device.Instrument.DLT;
using HslTechnology.Edge.Device.Redis;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Device.Hsl;
using HslTechnology.Edge.Node.Device.CJT;
using HslTechnology.Edge.Device.Instrument.CJT;
using HslTechnology.Edge.Node.Device.DLT;
using HslTechnology.Edge.Node.Device.OpenProtocol;
using HslTechnology.Edge.Device.PLCDevice.OpenProtocol;
using HslTechnology.Edge.Node.Device.Robot;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 设备类型定义的集合信息
	/// </summary>
	public class DeviceDefinitions
	{

		public static string[] GetDeviceKeys( )
		{
			return Devices.Keys.ToArray( );
		}

		public static bool ContainsDeviceKey( string deviceType )
		{
			return Devices.ContainsKey( deviceType );
		}

		/// <summary>
		/// 根据传入的节点类型对象，获取当前的设备类型关键字信息
		/// </summary>
		/// <param name="nodeType">节点类型信息</param>
		/// <returns>类型关键字</returns>
		public static string GetDeviceTypeByNode( Type nodeType )
		{
			foreach (var item in Devices)
			{
				DeviceItemDefinition definition = item.Value;
				if (definition.DeviceNode != null)
				{
					if (definition.DeviceNode == nodeType) return item.Key;
				}
			}
			return null;
		}


		public const string Plugins = "Plugins";

		/// <summary>
		/// 所有设备的定义的集合
		/// </summary>
		public static Dictionary<string, DeviceItemDefinition> Devices { get; set; } = new Dictionary<string, DeviceItemDefinition>( )
		{
			{ "Device",                    new DeviceItemDefinition( typeof( NodeMachine ),                   typeof( DeviceMachine ),                   "VirtualMachineGroup", "新增PLC等设备/富设备(Machine)" ) },                         // 富设备的节点，下面可以挂多个不同的PLC，或是从共享PLC的数据创建数据
			{ "MelsecMcQna3E",             new DeviceItemDefinition( typeof( NodeMelsecMc ),                  typeof( DeviceMelsecMc3E ),                "melsec",              "新增PLC等设备/三菱PLC(Melsec)/MC(Qna3E)" ) },               // 三菱的Qna兼容3E帧协议的客户端
			{ "MelsecMcR",                 new DeviceItemDefinition( typeof( NodeMelsecMcR ),                 typeof( DeviceMelsecMcR ),                 "melsec",              "新增PLC等设备/三菱PLC(Melsec)/MCR(Qna3E-R系列)" ) },        // 三菱的Qna兼容3E帧协议的客户端，适用于R系列的PLC
			{ "MelsecMcUdpQna3E",          new DeviceItemDefinition( typeof( NodeMelsecMcUdp ),               typeof( DeviceMelsecMcUdp ),               "melsec",              "新增PLC等设备/三菱PLC(Melsec)/MC-UDP(Qna3E)"  ) },          // 三菱的Qna兼容3E帧协议的UDP客户端
			{ "MelsecMcQna1E",             new DeviceItemDefinition( typeof( NodeMelsec1E ),                  typeof( DeviceMelsecMc1E ),                "melsec",              "新增PLC等设备/三菱PLC(Melsec)/A1E(fx3u)" ) },               // 三菱的Qna兼容1E帧协议的客户端
			{ "MelsecFxlinks",             new DeviceItemDefinition( typeof( NodeMelsecFxlinks ),             typeof( DeviceMelsecFxlinks ),             "melsec",              "新增PLC等设备/三菱PLC(Melsec)/Fxlinks(计算机链接协议)" ) },  // 三菱的Fxlinks的串口设备
			{ "MelsecFxlinksOverTcp",      new DeviceItemDefinition( typeof( NodeMelsecFxlinksOverTcp ),      typeof( DeviceMelsecFxlinksOverTcp ),      "melsec",              "新增PLC等设备/三菱PLC(Melsec)/FxlinksOverTcp(串口透传)" ) }, // 三菱的Fxlinks的网口透传设备
			{ "MelsecFxSerial",            new DeviceItemDefinition( typeof( NodeMelsecFxSerial ),            typeof( DeviceMelsecFxSerial ),            "melsec",              "新增PLC等设备/三菱PLC(Melsec)/MelsecFxSerial(编程口)" ) },   // 三菱的编程口串口设备
			{ "MelsecFxSerialOverTcp",     new DeviceItemDefinition( typeof( NodeMelsecFxSerialOverTcp ),     typeof( DeviceMelsecFxSerialOverTcp ),     "melsec",              "新增PLC等设备/三菱PLC(Melsec)/MelsecFxSerialOverTcp" ) },    // 三菱的编程口的透传版本
			{ "MelsecA3C",                 new DeviceItemDefinition( typeof( NodeMelsecA3C ),                 typeof( DeviceMelsecA3C ),                 "melsec",              "新增PLC等设备/三菱PLC(Melsec)/A3C(串口MC)" ) },              // 三菱的MC-3C的串口设备
			{ "MelsecA3COverTcp",          new DeviceItemDefinition( typeof( NodeMelsecA3COverTcp ),          typeof( DeviceMelsecA3COverTcp ),          "melsec",              "新增PLC等设备/三菱PLC(Melsec)/MelsecA3COverTcp" ) },         // 三菱的MC-3C的网口透传设备
			{ "MelsecMCServer",            new DeviceItemDefinition( typeof( NodeMelsecMCServer ),            typeof( DeviceMcServer ),                  "melsec",              "新增PLC等设备/三菱PLC(Melsec)/MCServer(虚拟服务器)" ) },     // 三菱PLC的MC协议的虚拟服务器
			{ "PanasonicMc3E",             new DeviceItemDefinition( typeof( NodePanasonicMc ),               typeof( DevicePanasonicMc3E ),             "panasonic",           "新增PLC等设备/松下PLC(Panasonic)/MC3E(三菱MC协议)" ) },      // 松下的PLC的Qna3E协议信息
			{ "PanasonicMewtocol",         new DeviceItemDefinition( typeof( NodePanasonicMewtocol ),         typeof( DevicePanasonicMewtocol ),         "panasonic",           "新增PLC等设备/松下PLC(Panasonic)/Mewtocol" ) },              // 松下的PLC的Mewtocol协议的PLC
			{ "PanasonicMewtocolOverTcp",  new DeviceItemDefinition( typeof( NodePanasonicMewtocolOverTcp ),  typeof( DevicePanasonicMewtocolOverTcp ),  "panasonic",           "新增PLC等设备/松下PLC(Panasonic)/MewtocolOverTcp(串口透传)" ) }, // 松下的PLC的Mewtocol协议的PLC，采用网口透传实现
			{ "ModbusTcpClient",           new DeviceItemDefinition( typeof( NodeModbusTcp ),                 typeof( DeviceModbusTcp ),                 "modbus",              "新增PLC等设备/Modbus设备/ModbusTcp" ) },                     // 常规的Modbus-Tcp客户端
			{ "ModbusUdp",                 new DeviceItemDefinition( typeof( NodeModbusUdp ),                 typeof( DeviceModbusUdp ),                 "modbus",              "新增PLC等设备/Modbus设备/ModbusUdp" ) },                     // modbustcp客户端，但是使用UDP协议传送的报文
			{ "ModbusRtu",                 new DeviceItemDefinition( typeof( NodeModbusRtu ),                 typeof( DeviceModbusRtu ),                 "modbus",              "新增PLC等设备/Modbus设备/ModbusRtu" ) },                     // 常规的Modbus-Rtu客户端
			{ "ModbusRtuOverTcp",          new DeviceItemDefinition( typeof( NodeModbusRtuOverTcp ),          typeof( DeviceModbusRtuOverTcp ),          "modbus",              "新增PLC等设备/Modbus设备/ModbusRtuOverTcp" ) },              // 常规的Modbus-Rtu客户端，但是使用网口透传的
			{ "ModbusAscii",               new DeviceItemDefinition( typeof( NodeModbusAscii ),               typeof( DeviceModbusAscii ),               "modbus",              "新增PLC等设备/Modbus设备/ModbusAscii" ) },                   // 常规的Modbus-Ascii客户端
			{ "ModbusServer",              new DeviceItemDefinition( typeof( NodeModbusServer ),              typeof( DeviceModbusServer ),              "modbus",              "新增PLC等设备/Modbus设备/ModbusServer" ) },                  // Modbus的服务器，包含了TCP和RTU,ASCII的服务器构建
			{ "SiemensS7",                 new DeviceItemDefinition( typeof( NodeSiemensS7 ),                 typeof( DeviceSiemensS7 ),                 "siemens",             "新增PLC等设备/西门子PLC(Siemens)/SiemensS7" ) },              // 西门子的PLC设备
			{ "SiemensWebApi",             new DeviceItemDefinition( typeof( NodeSiemensWebApi ),             typeof( DeviceSiemensWebApi ),             "siemens",             "新增PLC等设备/西门子PLC(Siemens)/SiemensWebApi" ) },          // 西门子的设备，基于webapi接口访问的协议
			{ "SiemensFW",                 new DeviceItemDefinition( typeof( NodeSiemensFW ),                 typeof( DeviceSiemensFW ),                 "siemens",             "新增PLC等设备/西门子PLC(Siemens)/FetchWrite" ) },             // 西门子的PLC设备，基于fetch/write协议实现
			{ "SiemensPPI",                new DeviceItemDefinition( typeof( NodeSiemensPPI ),                typeof( DeviceSiemensPPI ),                "siemens",             "新增PLC等设备/西门子PLC(Siemens)/SiemensPPI" ) },             // 西门子的PPI协议的设备
			{ "SiemensPPIOverTcp",         new DeviceItemDefinition( typeof( NodeSiemensPPIOverTcp ),         typeof( DeviceSiemensPPIOverTcp ),         "siemens",             "新增PLC等设备/西门子PLC(Siemens)/SiemensPPIOverTcp" ) },      // 西门子PLC，通过PPI协议实现的网络透传
			{ "SiemensS7Server",           new DeviceItemDefinition( typeof( NodeSiemensS7Server ),           typeof( DeviceS7Server ),                  "siemens",             "新增PLC等设备/西门子PLC(Siemens)/SiemensS7Server" ) },        // 西门子PLC的FW的虚拟服务器
			{ "OmronFinsTcp",              new DeviceItemDefinition( typeof( NodeOmronFinsTcp ),              typeof( DeviceOmronFins ),                 "omron",               "新增PLC等设备/欧姆龙PLC(Omron)/OmronFinsTcp" ) },             // 欧姆龙的PLC设备
			{ "OmronFinsUdp",              new DeviceItemDefinition( typeof( NodeOmronFinsUdp ),              typeof( DeviceOmronFinsUdp ),              "omron",               "新增PLC等设备/欧姆龙PLC(Omron)/OmronFinsUdp" ) },             // 欧姆龙的PLC设备，finsudp
			{ "OmronHostLink",             new DeviceItemDefinition( typeof( NodeOmronHostLink ),             typeof( DeviceOmronHostLink ),             "omron",               "新增PLC等设备/欧姆龙PLC(Omron)/HostLink" ) },                 // 欧姆龙的HostLink协议的设备
			{ "OmronHostLinkOverTcp",      new DeviceItemDefinition( typeof( NodeOmronHostLinkOverTcp ),      typeof( DeviceOmronHostLinkOverTcp ),      "omron",               "新增PLC等设备/欧姆龙PLC(Omron)/HostLinkOverTcp" ) },          // 欧姆龙的HostLink协议的网口透传设备
			{ "OmronHostLinkCMode",        new DeviceItemDefinition( typeof( NodeOmronHostLinkCMode ),        typeof( DeviceOmronHostLinkCMode ),        "omron",               "新增PLC等设备/欧姆龙PLC(Omron)/HostLinkCMode" ) },            // 欧姆龙的HostLink-CMode协议的设备
			{ "OmronHostLinkCModeOverTcp", new DeviceItemDefinition( typeof( NodeOmronHostLinkCModeOverTcp ), typeof( DeviceOmronHostLinkCModeOverTcp ), "omron",               "新增PLC等设备/欧姆龙PLC(Omron)/HostLinkCModeOverTcp" ) },     // 欧姆龙的HostLink-CMode协议的设备，网口透传
			{ "OmronCipNet",               new DeviceItemDefinition( typeof( NodeOmronCipNet ),               typeof( DeviceOmronCipNet ),               "omron",               "新增PLC等设备/欧姆龙PLC(Omron)/OmronCipNet" ) },              // 欧姆龙的CIP协议实现
			{ "OmronConnectedCipNet",      new DeviceItemDefinition( typeof( NodeOmronConnectedCipNet),       typeof( DeviceOmronConnectedCipNet ),      "omron",               "新增PLC等设备/欧姆龙PLC(Omron)/ConnectedCipNet" ) },          // 欧姆龙的ConnectedCip协议的实现
			{ "KeyenceMcQna3E",            new DeviceItemDefinition( typeof( NodeKeyenceMc ),                 typeof( DeviceKeyenceMc3E ),               "keyence",             "新增PLC等设备/基恩士PLC(Keyence)/KeyenceMc3E" ) },            // 基恩士的Qna兼容3E帧协议的客户端
			{ "KeyenceNano",               new DeviceItemDefinition( typeof( NodeKeyenceNano ),               typeof( DeviceKeyenceNano ),               "keyence",             "新增PLC等设备/基恩士PLC(Keyence)/上位链路协议(Nano)" ) },      // 基恩士的Nano协议的PLC设备，上位链路协议
			{ "KeyenceNanoOverTcp",        new DeviceItemDefinition( typeof( NodeKeyenceNanoOverTcp ),        typeof( DeviceKeyenceNanoOverTcp ),        "keyence",             "新增PLC等设备/基恩士PLC(Keyence)/上位链路协议(TCP)" ) },       // 基恩士的Nano协议的PLC设备，通过网口透传来实现的
			{ "AllenBradleyCIP",           new DeviceItemDefinition( typeof( NodeAllenBradleyCIP ),           typeof( DeviceAllenBradleyNet ),           "ab",                  "新增PLC等设备/罗克韦尔PLC(AllenBradley)/AllenBradleyCip" ) },  // AB PLC的设备信息
			{ "AllenBradleyConnectedCIP",  new DeviceItemDefinition( typeof( NodeAllenBradleyConnectedCIP ),  typeof( DeviceAllenBradleyConnectedCIP ),  "ab",                  "新增PLC等设备/罗克韦尔PLC(AllenBradley)/ConnectedCIP" ) },     // AB 基于连接的CIP协议
			{ "AllenBradleyPCCC",          new DeviceItemDefinition( typeof( NodeAllenBradleyPCCC ),          typeof( DeviceAllenBradleyPCCC ),          "ab",                  "新增PLC等设备/罗克韦尔PLC(AllenBradley)/AllenBradleyPCCC" ) }, // AB PLC的设备信息，适用1400
			{ "AllenBradleyMicroCIP",      new DeviceItemDefinition( typeof( NodeAllenBradleyMicroCip ),      typeof( DeviceAllenBradleyMicroCip ),      "ab",                  "新增PLC等设备/罗克韦尔PLC(AllenBradley)/AllenBradleyMicroCip" ) },   // AB PLC的设备信息，基于MicroCip协议
			{ "AllenBradleySLC",           new DeviceItemDefinition( typeof( NodeAllenBradleySLC ),           typeof( DeviceAllenBradleySLC ),           "ab",                  "新增PLC等设备/罗克韦尔PLC(AllenBradley)/SLC Net" ) },          // AB PLC的设备，使用SLC协议实现
			{ "AllenBradleyCipServer",     new DeviceItemDefinition( typeof( NodeAllenBradleyCipServer ),     typeof( DeviceAllenBradleyCipServer ),     "ab",                  "新增PLC等设备/罗克韦尔PLC(AllenBradley)/CipServer" ) },        // AB PLC的CIP协议的虚拟服务器
			{ "GeSRTPNet",                 new DeviceItemDefinition( typeof( NodeGeSRTPNet ),                 typeof( DeviceGeSRTPNet ),                 "ge",                  "新增PLC等设备/通用电气设备(GE)/SRTPNet" ) },                   // 通用电气的PLC设备
			{ "BeckhoffAdsNet",            new DeviceItemDefinition( typeof( NodeBeckhoffAds ),               typeof( DeviceBeckhoffAds ),               "beckhoff",            "新增PLC等设备/倍福PLC(beckhoff)/BeckhoffADS" ) },              // 倍福的ADS协议的客户端
			{ "DeltaDvpTcpNet",            new DeviceItemDefinition( typeof( NodeDeltaDvpTcp ),               typeof( DeviceDeltaDvpTcp ),               "delta",               "新增PLC等设备/台达PLC/DeltaTcp(Modbus)" ) },                   // 台达的DVP系列的设备
			{ "DeltaDvpTcpSerial",         new DeviceItemDefinition( typeof( NodeDeltaDvpSerial ),            typeof( DeviceDeltaDvpSerial ) ,           "delta",               "新增PLC等设备/台达PLC/DvpSerial(ModbusRtu)" ) },               // 达的DVP系列的串口设备，基于Modbus-rtu协议
			{ "DeltaDvpTcpSerialOverTcp",  new DeviceItemDefinition( typeof( NodeDeltaDvpSerialOverTcp ),     typeof( DeviceDeltaDvpSerialOverTcp ),     "delta",               "新增PLC等设备/台达PLC/SerialOverTcp(RtuOnTcp)" ) },           // 台达的DVP系列的串口设备，基于Modbus-rtu协议
			{ "DeltaDvpTcpAscii",          new DeviceItemDefinition( typeof( NodeDeltaDvpAscii ),             typeof( DeviceDeltaDvpAscii ),             "delta",               "新增PLC等设备/台达PLC/DvpAscii(ModbusAscii)" ) },              // 台达的DVP系列的串口设备，基于Modbus-Ascii协议
			{ "InovanceSerial",            new DeviceItemDefinition( typeof( NodeInovanceSerial ),            typeof( DeviceInovanceSerial ),            "inovance",            "新增PLC等设备/汇川PLC(Inovance)/Serial(ModbusRtu)" ) },        // 汇川的串口通信，底层使用modbus-rtu协议
			{ "InovanceSerialOverTcp",     new DeviceItemDefinition( typeof( NodeInovanceSerialOverTcp ),     typeof( DeviceInovanceSerialOverTcp ),     "inovance",            "新增PLC等设备/汇川PLC(Inovance)/SerialOverTcp(RtuOnTcp)" ) },   // 汇川的串口透传通信类，使用的网口传送modbus-rtu协议
			{ "InovanceTcpNet",            new DeviceItemDefinition( typeof( NodeInovanceTcpNet ),            typeof( DeviceInovanceTcpNet ),            "inovance",            "新增PLC等设备/汇川PLC(Inovance)/InovanceTcp(Modbus)" ) },       // 汇川的网口通信类，使用的网口传送 modbus-tcp协议
			{ "FatekProgram",              new DeviceItemDefinition( typeof( NodeFatekProgram ),              typeof( DeviceFatekProgram ),              "fatek",               "新增PLC等设备/永宏PLC(Fatek)/FatekProgram" ) },                 // 永宏的编程口协议的设备
			{ "FatekProgramOverTcp",       new DeviceItemDefinition( typeof( NodeFatekProgramOverTcp ),       typeof( DeviceFatekProgramOverTcp ),       "fatek",               "新增PLC等设备/永宏PLC(Fatek)/FatekProgramOverTcp" ) },          // 永宏的编程口协议的网络透传版本
			{ "FujiSPB",                   new DeviceItemDefinition( typeof( NodeFujiSPB ),                   typeof( DeviceFujiSPB ),                   "fujifilm",            "新增PLC等设备/富士PLC(Fuji)/FujiSPB" ) },                       // 富士PLC设备
			{ "FujiSPBOverTcp",            new DeviceItemDefinition( typeof( NodeFujiSPBOverTcp ),            typeof( DeviceFujiSPBOverTcp ),            "fujifilm",            "新增PLC等设备/富士PLC(Fuji)/FujiSPBOverTcp" ) },                // 富士PLC设备，使用网口透传实现
			{ "FujiSPHNet",                new DeviceItemDefinition( typeof( NodeFujiSPHNet ),                typeof( DeviceFujiSPHNet ),                "fujifilm",            "新增PLC等设备/富士PLC(Fuji)/FujiSPHNet" ) },                    // 富士PLC设备
			{ "YokogawaLinkTcp",           new DeviceItemDefinition( typeof( NodeYokogawaLinkTcp ),           typeof( DeviceYokogawaLinkTcp ),           "yokogawa",            "新增PLC等设备/横河PLC(Yokogawa)/YokogawaLinkTcp" ) },           // 横河PLC的设备
			{ "DLT645",                    new DeviceItemDefinition( typeof( NodeDlt645 ),                    typeof( DeviceDLT645 ),                    "Event_594",           "新增PLC等设备/电力规约(DLT)/DLT645-2007" ) },                   // DLT645的设备信息
			{ "DLT645OverTcp",             new DeviceItemDefinition( typeof( NodeDlt645OverTcp ),             typeof( DeviceDLT645OverTcp ),             "Event_594",           "新增PLC等设备/电力规约(DLT)/DLT645-Tcp" ) },                    // DLT645的串口转网口的设备信息
			{ "DLT698",                    new DeviceItemDefinition( typeof( NodeDlt698 ),                    typeof( DeviceDLT698 ),                    "Event_594",           "新增PLC等设备/电力规约(DLT)/DLT698(串口)" ) },                  // DLT698的设备信息
			{ "DLT698OverTcp",             new DeviceItemDefinition( typeof( NodeDlt698OverTcp ),             typeof( DeviceDLT698OverTcp ),             "Event_594",           "新增PLC等设备/电力规约(DLT)/DLT698OverTcp(串口透传)" ) },       // DLT698的串口转网口设备
			{ "DLT698TcpNet",              new DeviceItemDefinition( typeof( NodeDlt698TcpNet ),              typeof( DeviceDLT698TcpNet ),              "Event_594",           "新增PLC等设备/电力规约(DLT)/DLT698Net" ) },                    // DLT698的纯网口协议实现
			{ "DLT645With1997",            new DeviceItemDefinition( typeof( NodeDlt645With1997 ),            typeof( DeviceDLT645With1997 ),            "Event_594",           "新增PLC等设备/电力规约(DLT)/DLT645-1997" ) },                  // DLT645-1997的设备信息
			{ "DLT645With1997OverTcp",     new DeviceItemDefinition( typeof( NodeDlt645With1997OverTcp ),     typeof( DeviceDLT645With1997OverTcp ),     "Event_594",           "新增PLC等设备/电力规约(DLT)/DLT645-1997(串口透传)" ) },        // DLT645-1997的串口转网口的设备信息
			{ "VigorSerial",               new DeviceItemDefinition( typeof( NodeVigorSerial ),               typeof( DeviceVigorSerial ),               "vigor",               "新增PLC等设备/丰炜PLC(Vigor)/VigorSerial" ) },               // 丰炜PLC的串口设备
			{ "VigorOverTcp",              new DeviceItemDefinition( typeof( NodeVigorSerialOverTcp ),        typeof( DeviceVigorSerialOverTcp ),        "vigor",               "新增PLC等设备/丰炜PLC(Vigor)/VigorSerialOverTcp" ) },        // 丰炜PLC的串口转网口设备
			{ "XinJESerial",               new DeviceItemDefinition( typeof( NodeXinJESerial ),               typeof( DeviceXinJESerial ),               "xinje",               "新增PLC等设备/信捷PLC(XinJE)/XinJESerial" ) },                // 信捷的串口协议
			{ "XinJESerialOverTcp",        new DeviceItemDefinition( typeof( NodeXinJESerialOverTcp ),        typeof( DeviceXinJESerialOverTcp ),        "xinje",               "新增PLC等设备/信捷PLC(XinJE)/XinJESerialOverTcp" ) },         // 信捷的串口转网口设备
			{ "XinJETcpNet",               new DeviceItemDefinition( typeof( NodeXinJETcpNet ),               typeof( DeviceXinJETcpNet ),               "xinje",               "新增PLC等设备/信捷PLC(XinJE)/XinJETcpNet" ) },                // 信捷的ModbusTcp协议
			{ "XinJEInternalNet",          new DeviceItemDefinition( typeof( NodeXinJEInternalNet ),          typeof( DeviceXinJEInternalNet ),          "xinje",               "新增PLC等设备/信捷PLC(XinJE)/InternalNet(专用协议s)" ) },      // 信捷的内部TCP协议
			{ "CJT188",                    new DeviceItemDefinition( typeof( NodeCJT188 ),                    typeof( DeviceCJT188 ),                    "water",               "新增PLC等设备/水表188(cjt)/CJT188" ) },                       // CJT188协议
			{ "CJT188OverTcp",             new DeviceItemDefinition( typeof( NodeCJT188OverTcp ),             typeof( DeviceCJT188OverTcp ),             "water",               "新增PLC等设备/水表188(cjt)/CJT188OverTcp" ) },                // CJT188协议网口透传版本
			{ "OpenProtocol",              new DeviceItemDefinition( typeof( NodeOpenProtocol ),              typeof( DeviceOpenProtocol ),              "Method_636",          "新增PLC等设备/OpenProtocol/OpenProtocol(拧紧枪)" ) },         // 基于OpenProtocol协议信息
			{ "CncFanucSerise0i",          new DeviceItemDefinition( typeof( NodeCncFanucSerise ),            typeof( CncFanucSerise0i ),                "fanuc",               "新增Cnc/Fanuc数控机床/Serise0i" ) },                          // Fanuc公司的CNC数据信息
			{ "RobotEfort",                new DeviceItemDefinition( typeof( NodeRobotEfort ),                typeof( RobotEfortNet ),                   "efort",               "新增Robot/埃夫特机器人/埃夫特机器人" ) },                      // 埃夫特公司的机器人数据信息
			{ "RobotFanucInterface",       new DeviceItemDefinition( typeof( NodeRobotFanucInterface ),       typeof( RobotFanucInterface ),             "fanuc",               "新增Robot/发那科特机器人/FanucInterface" ) },                  // Fanuc公司的机器人数据信息
			{ "RobotABBWebApi",            new DeviceItemDefinition( typeof( NodeRobotAbb ),                  typeof( RobotAbb ),                        "abb",                 "新增Robot/ABB机器人/AbbWebApi") },                            // ABB公司的机器人数据信息
			{ "RobotYRC1000TcpNet",        new DeviceItemDefinition( typeof( NodeRobotYRC1000TcpNet ),        typeof( RobotYRC1000TcpNet ),              "yaskawa",             "新增Robot/安川机器人/YRC1000" ) },                            // 安川YRC1000的机器人协议信息
			{ "RobotYRCHighEthernet",      new DeviceItemDefinition( typeof( NodeRobotYRCHighEthernet ),      typeof( RobotYRCHighEthernet ),            "yaskawa",             "新增Robot/安川机器人/高速协议" ) },                            // 安川机器人的高速协议
			{ "MqttRpcClient",             new DeviceItemDefinition( typeof( NodeMqttRpcClient ),             typeof( DeviceMqttRpcClient ),             "HslCommunication",    "新增特殊设备/MqttRpcClient" ) },                              // MRPC的接口客户端
			{ "RedisClient",               new DeviceItemDefinition( typeof( NodeRedisClient ),               typeof( DeviceRedis ),                     "redis",               "新增特殊设备/RedisClient" ) },                                // Redis客户端的信息
			{ "TcpToTcp",                  new DeviceItemDefinition( typeof( NodeTcpToTcp ),                  typeof( DeviceTcpToTcp ),                  "HslCommunication",    "新增特殊设备/TcpToTcp(网口转网口)" ) },                      // TCP转TCP的设备信息
			{ "SerialToTcp",               new DeviceItemDefinition( typeof( NodeSerialToTcp ),               typeof( DeviceSerialToTcp ),               "HslCommunication",    "新增特殊设备/SerialToTcp(串口转网口)" ) },                   // 串口转TCP的设备信息
			{ "SerialToDtu",               new DeviceItemDefinition( typeof( NodeSerialToDTU ),               typeof( DeviceSerialToDtu ),               "HslCommunication",    "新增特殊设备/SerialToDtu(串口转DTU)" ) },                    // 串口转DTU的设备信息
			{ "TcpToDtu",                  new DeviceItemDefinition( typeof( NodeTcpToDTU ),                  typeof( DeviceTcpToDtu ),                  "HslCommunication",    "新增特殊设备/TcpToDtu(网口转DTU)" ) },                       // TCP转DTU的设备信息
			{ "SerialToMqtt",              new DeviceItemDefinition( typeof( NodeSerialToMqtt ),              typeof( DeviceSerialToMqtt ),              "mqtt",                "新增特殊设备/SerialToMqtt(串口转MQTT)" ) },                  // 串口转MQTT的设备对象
			{ "TcpToMqtt",                 new DeviceItemDefinition( typeof( NodeTcpToMqtt ),                 typeof( DeviceTcpToMqtt ),                 "mqtt",                "新增特殊设备/TcpToMqtt(网口转MQTT)" ) },                     // TCP转MQTT的设备对象
			{ "SerialToRemoteMqtt",        new DeviceItemDefinition( typeof( NodeSerialToRemoteMqtt ),        typeof( DeviceSerialToRemoteMqtt ),        "mqtt",                "新增特殊设备/SerialToRemoteMqtt(串口转MQTT)" ) },             // 串口转远程MQTT的设备对象
			{ "TcpToRemoteMqtt",           new DeviceItemDefinition( typeof( NodeTcpToRemoteMqtt ),           typeof( DeviceTcpToRemoteMqtt ),           "mqtt",                "新增特殊设备/TcpToRemoteMqtt(网口转MQTT)" ) },                // TCP转远程MQTT的设备对象
			{ "TcpFreedom",                new DeviceItemDefinition( typeof( NodeTcpFreedom ),                typeof( DeviceTcpFreedom ),                "HslCommunication",    "新增特殊设备/TcpFreedom(自由TCP)" ) },                        // 基于自由的TCP协议信息
			{ "SerialFreedom",             new DeviceItemDefinition( typeof( NodeSerialFreedom ),             typeof( DeviceSerialFreedom ),             "HslCommunication",    "新增特殊设备/SerialFreedom(自由串口)" ) },                    // 基于自由的串口协议信息
			{ Plugins, new DeviceItemDefinition( ) },                                                                // 基于插件的设备信息，需要去插件系统里查找绑定的节点信息，实体设备类型，图标信息
		};




	}
}
