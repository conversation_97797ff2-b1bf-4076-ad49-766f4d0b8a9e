using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Core
{
	/// <summary>
	/// 字节变换的规律类型
	/// </summary>
	public enum ByteTransformType
	{
		/// <summary>
		/// 大端的数据变化顺序
		/// </summary>
		BigEndianOrder = 0,

		/// <summary>
		/// 小端的数据变化顺序
		/// </summary>
		LittleEndianOrder = 1,

		/// <summary>
		/// 按照字进行反转，对应的是modbus协议的cdab
		/// </summary>
		ReverseByWord = 2
	}
}
