using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 字符串的顺序信息
	/// </summary>
	public enum StringOrder
	{
		/// <summary>
		/// 默认的顺序
		/// </summary>
		Default,

		/// <summary>
		/// 按照字为单位进行反转
		/// </summary>
		WordReverse,

		/// <summary>
		/// 全部的字节进行反转
		/// </summary>
		Reverse
	}
}
