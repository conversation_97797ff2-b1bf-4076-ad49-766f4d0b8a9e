using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 请求的类型，分为读取请求，读取的格式内容视PLC而定，定时写入的请求
	/// </summary>
	public enum RequestType
	{
		/// <summary>
		/// 标量读取的请求类型，读取一个数据，支持配置倍率，偏移运算，也可以读取原始字节数组，但不支持自定义的解析
		/// </summary>
		[DeviceNodeClass( null, ImageKey = "Method_636", DeviceObject = null )]
		ScalarRead,

		/// <summary>
		/// 原始的字节数组的读取，用于自定义的数据解析，共享其他PLC解析
		/// </summary>
		[DeviceNodeClass( null, ImageKey = "Method_636", DeviceObject = null )]
		SourceRead,

		/// <summary>
		/// 定时写入的请求类型
		/// </summary>
		[DeviceNodeClass( null, ImageKey = "Method_636", DeviceObject = null )]
		WriteInterval,

		/// <summary>
		/// 设备的方法调用
		/// </summary>
		[DeviceNodeClass( null, ImageKey = "Method_636", DeviceObject = null )]
		MethodCall,

		/// <summary>
		/// 缓存的标量数据，不映射任何的设备地址信息，但是可以对变量本身进行远程的读写操作
		/// </summary>
		[DeviceNodeClass( null, ImageKey = "Enum_582", DeviceObject = null )]
		ScalarCache,

		/// <summary>
		/// 数据库操作的请求
		/// </summary>
		[DeviceNodeClass( null, ImageKey = "Database_request", DeviceObject = null )]
		DatabaseOperate,
	}
}
