using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Core
{
	/// <summary>
	/// 结构体解析时候的类型枚举
	/// </summary>
	public enum ParseType
	{
		/// <summary>
		/// 以结构体的形式呈现，使用一个大 json 包
		/// </summary>
		Struct,

		/// <summary>
		/// 属性全称，使用结构体名+'.'+属性名表示
		/// </summary>
		FullName,

		/// <summary>
		/// 按名称展开，只剩下了属性名称
		/// </summary>
		Unflod,
	}
}
