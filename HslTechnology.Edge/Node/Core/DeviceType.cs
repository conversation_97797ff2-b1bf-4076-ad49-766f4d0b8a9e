using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Node.Server;
using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Device.CncDevice;
using HslTechnology.Edge.Device.RobotDevice;
using HslTechnology.Edge.Device.ServerDevice;
using HslTechnology.Edge.Device.Instrument.DLT;
using HslTechnology.Edge.Device.Redis;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Device.Hsl;
using HslTechnology.Edge.Node.Device.CJT;
using HslTechnology.Edge.Device.Instrument.CJT;
using HslTechnology.Edge.Node.Device.DLT;
using HslTechnology.Edge.Node.Device.OpenProtocol;
using HslTechnology.Edge.Device.PLCDevice.OpenProtocol;
using HslTechnology.Edge.Node.Device.Robot;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 当前系统的所有的注册的设备类型，包括PLC设备，modbus设备，cnc设备，机器人设备
	/// </summary>
	public enum DeviceType
	{
		/// <summary>
		/// 富设备的节点，下面可以挂多个不同的PLC，或是从共享PLC的数据创建数据
		/// </summary>
		Device,

		/// <summary>
		/// 三菱的Qna兼容3E帧协议的客户端
		/// </summary>
		MelsecMcQna3E,

		/// <summary>
		/// 三菱的Qna兼容3E帧协议的客户端，适用于R系列的PLC
		/// </summary>
		MelsecMcR,

		/// <summary>
		/// 三菱的Qna兼容3E帧协议的客户端
		/// </summary>
		MelsecMcUdpQna3E,

		/// <summary>
		/// 三菱的Qna兼容1E帧协议的客户端
		/// </summary>
		MelsecMcQna1E,

		/// <summary>
		/// 三菱的Fxlinks的串口设备
		/// </summary>
		MelsecFxlinks,

		/// <summary>
		/// 三菱的Fxlinks的网口透传设备
		/// </summary>
		MelsecFxlinksOverTcp,

		/// <summary>
		/// 三菱的编程口串口设备
		/// </summary>
		MelsecFxSerial,

		/// <summary>
		/// 三菱的编程口的透传版本
		/// </summary>
		MelsecFxSerialOverTcp,

		/// <summary>
		/// 三菱的MC-3C的串口设备
		/// </summary>
		MelsecA3C,

		/// <summary>
		/// 三菱的MC-3C的网口透传设备
		/// </summary>
		MelsecA3COverTcp,

		/// <summary>
		/// 松下的PLC的Qna3E协议信息
		/// </summary>
		PanasonicMc3E,

		/// <summary>
		/// 松下的PLC的Mewtocol协议的PLC
		/// </summary>
		PanasonicMewtocol,

		/// <summary>
		/// 松下的PLC的Mewtocol协议的PLC，采用网口透传实现
		/// </summary>
		PanasonicMewtocolOverTcp,

		/// <summary>
		/// 常规的Modbus-Tcp客户端
		/// </summary>
		ModbusTcpClient,

		/// <summary>
		/// modbustcp客户端，但是使用UDP协议传送的报文
		/// </summary>
		ModbusUdp,

		/// <summary>
		/// 常规的Modbus-Rtu客户端
		/// </summary>
		ModbusRtu,

		/// <summary>
		/// 常规的Modbus-Ascii客户端
		/// </summary>
		ModbusAscii,

		/// <summary>
		/// 常规的Modbus-Rtu客户端
		/// </summary>
		ModbusRtuOverTcp,

		/// <summary>
		/// 西门子的PLC设备
		/// </summary>
		SiemensS7,

		/// <summary>
		/// 西门子的设备，基于webapi接口访问的协议
		/// </summary>
		SiemensWebApi,

		/// <summary>
		/// 西门子的PLC设备
		/// </summary>
		SiemensFW,

		/// <summary>
		/// 西门子的PPI协议的设备
		/// </summary>
		SiemensPPI,

		/// <summary>
		/// 西门子PLC，通过PPI协议实现的网络透传
		/// </summary>
		SiemensPPIOverTcp,

		/// <summary>
		/// 欧姆龙的PLC设备
		/// </summary>
		OmronFinsTcp,

		/// <summary>
		/// 欧姆龙的PLC设备
		/// </summary>
		OmronFinsUdp,

		/// <summary>
		/// 欧姆龙的HostLink协议的设备
		/// </summary>
		OmronHostLink,

		/// <summary>
		/// 欧姆龙的HostLink协议的网口透传设备
		/// </summary>
		OmronHostLinkOverTcp,

		/// <summary>
		/// 欧姆龙的HostLink-CMode协议的设备
		/// </summary>
		OmronHostLinkCMode,

		/// <summary>
		/// 欧姆龙的HostLink-CMode协议的设备
		/// </summary>
		OmronHostLinkCModeOverTcp,

		/// <summary>
		/// 欧姆龙的CIP协议实现
		/// </summary>
		OmronCipNet,

		/// <summary>
		/// 欧姆龙的ConnectedCip协议的实现
		/// </summary>
		OmronConnectedCipNet,

		/// <summary>
		/// 基恩士的Qna兼容3E帧协议的客户端
		/// </summary>
		KeyenceMcQna3E,

		/// <summary>
		/// 基恩士的Nano协议的PLC设备
		/// </summary>
		KeyenceNano,

		/// <summary>
		/// 基恩士的Nano协议的PLC设备，通过网口透传来实现的
		/// </summary>
		KeyenceNanoOverTcp,

		/// <summary>
		/// AB PLC的设备信息
		/// </summary>
		AllenBradleyCIP,

		/// <summary>
		/// 基于连接的CIP协议
		/// </summary>
		AllenBradleyConnectedCIP,

		/// <summary>
		/// AB PLC的设备信息
		/// </summary>
		AllenBradleyPCCC,

		/// <summary>
		/// AB PLC的设备信息，基于MicroCip协议
		/// </summary>
		AllenBradleyMicroCIP,

		/// <summary>
		/// AB PLC的设备，使用SLC协议实现
		/// </summary>
		AllenBradleySLC,

		/// <summary>
		/// 通用电气的PLC设备
		/// </summary>
		GeSRTPNet,

		/// <summary>
		/// Fanuc公司的CNC数据信息
		/// </summary>
		CncFanucSerise0i,

		/// <summary>
		/// 埃夫特公司的机器人数据信息
		/// </summary>
		RobotEfort,

		/// <summary>
		/// Fanuc公司的机器人数据信息
		/// </summary>
		RobotFanucInterface,

		/// <summary>
		/// ABB公司的机器人数据信息
		/// </summary>
		RobotABBWebApi,

		/// <summary>
		/// 倍福的ADS协议的客户端
		/// </summary>
		BeckhoffAdsNet,

		/// <summary>
		/// 台达的DVP系列的设备
		/// </summary>
		DeltaDvpTcpNet,

		/// <summary>
		/// 台达的DVP系列的串口设备，基于Modbus-rtu协议
		/// </summary>
		DeltaDvpTcpSerial,

		/// <summary>
		/// 台达的DVP系列的串口设备，基于Modbus-rtu协议
		/// </summary>
		DeltaDvpTcpSerialOverTcp,

		/// <summary>
		/// 台达的DVP系列的串口设备，基于Modbus-Ascii协议
		/// </summary>
		DeltaDvpTcpAscii,

		/// <summary>
		/// 汇川的串口通信，底层使用modbus-rtu协议
		/// </summary>
		InovanceSerial,

		/// <summary>
		/// 汇川的串口透传通信类，使用的网口传送modbus-rtu协议
		/// </summary>
		InovanceSerialOverTcp,

		/// <summary>
		/// 汇川的网口通信类，使用的网口传送 modbus-tcp协议
		/// </summary>
		InovanceTcpNet,

		/// <summary>
		/// 永宏的编程口协议的设备
		/// </summary>
		FatekProgram,

		/// <summary>
		/// 永宏的编程口协议的网络透传版本
		/// </summary>
		FatekProgramOverTcp,

		/// <summary>
		/// 富士PLC设备
		/// </summary>
		FujiSPB,

		/// <summary>
		/// 富士PLC设备，使用网口透传实现
		/// </summary>
		FujiSPBOverTcp,

		/// <summary>
		/// 富士PLC设备
		/// </summary>
		FujiSPHNet,

		/// <summary>
		/// 横河PLC的设备
		/// </summary>
		YokogawaLinkTcp,

		/// <summary>
		/// DLT645的设备信息
		/// </summary>
		DLT645,

		/// <summary>
		/// DLT645的串口转网口的设备信息
		/// </summary>
		DLT645OverTcp,

		/// <summary>
		/// DLT698的设备信息
		/// </summary>
		DLT698,

		/// <summary>
		/// MRPC的接口客户端
		/// </summary>
		MqttRpcClient,

		/// <summary>
		/// Redis客户端的信息
		/// </summary>
		RedisClient,


		// ****************************************************************************************
		// *
		// * Server相关的设备信息
		// *
		// *
		// ****************************************************************************************

		/// <summary>
		/// Modbus的服务器，包含了TCP和RTU,ASCII的服务器构建
		/// </summary>
		ModbusServer,

		/// <summary>
		/// 西门子PLC的S7的虚拟服务器
		/// </summary>
		SiemensS7Server,

		/// <summary>
		/// 西门子PLC的FW的虚拟服务器
		/// </summary>
		SiemensFWServer,

		/// <summary>
		/// 三菱PLC的MC协议的虚拟服务器
		/// </summary>
		MelsecMCServer,

		/// <summary>
		/// AB PLC的CIP协议的虚拟服务器
		/// </summary>
		AllenBradleyCipServer,

		/// <summary>
		/// AB PLC的PCCC协议的虚拟服务器
		/// </summary>
		AllenBradleyPcccServer,

		/// <summary>
		/// 欧姆龙的FINS协议的虚拟服务器
		/// </summary>
		OmronFinsServer,

		/// <summary>
		/// 基于插件的设备信息，需要去插件系统里查找绑定的节点信息，实体设备类型，图标信息
		/// </summary>
		Plugins,

		/// <summary>
		/// TCP转TCP的设备信息
		/// </summary>
		TcpToTcp,

		/// <summary>
		/// 串口转TCP的设备信息
		/// </summary>
		SerialToTcp,

		/// <summary>
		/// 串口转DTU的设备信息
		/// </summary>
		SerialToDtu,

		/// <summary>
		/// TCP转DTU的设备信息
		/// </summary>
		TcpToDtu,

		/// <summary>
		/// 串口转MQTT的设备对象
		/// </summary>
		SerialToMqtt,

		/// <summary>
		/// TCP转MQTT的设备对象
		/// </summary>
		TcpToMqtt,

		/// <summary>
		/// 丰炜PLC的串口设备
		/// </summary>
		VigorSerial,

		/// <summary>
		/// 丰炜PLC的串口转网口设备
		/// </summary>
		VigorOverTcp,

		/// <summary>
		/// 信捷的串口协议
		/// </summary>
		XinJESerial,

		/// <summary>
		/// 信捷的串口转网口设备
		/// </summary>
		XinJESerialOverTcp,

		/// <summary>
		/// 信捷的ModbusTcp协议
		/// </summary>
		XinJETcpNet,

		/// <summary>
		/// 信捷的内部TCP协议
		/// </summary>
		XinJEInternalNet,

		/// <summary>
		/// CJT188协议
		/// </summary>
		CJT188,

		/// <summary>
		/// CJT188协议
		/// </summary>
		CJT188OverTcp,

		/// <summary>
		/// 串口转远程MQTT的设备对象
		/// </summary>
		SerialToRemoteMqtt,

		/// <summary>
		/// TCP转远程MQTT的设备对象
		/// </summary>
		TcpToRemoteMqtt,

		/// <summary>
		/// DLT645-1997的设备信息
		/// </summary>
		DLT645With1997,

		/// <summary>
		/// DLT645-1997的串口转网口的设备信息
		/// </summary>
		DLT645With1997OverTcp,

		/// <summary>
		/// 基于自由的TCP协议信息
		/// </summary>
		TcpFreedom,

		/// <summary>
		/// 基于自由的串口协议信息
		/// </summary>
		SerialFreedom,

		/// <summary>
		/// 基于OpenProtocol协议信息
		/// </summary>
		OpenProtocol,

		/// <summary>
		/// Fanuc公司的机器人数据信息
		/// </summary>
		RobotYRC1000TcpNet,

		/// <summary>
		/// Fanuc公司的机器人数据信息
		/// </summary>
		RobotYRCHighEthernet,

		/// <summary>
		/// DLT698的设备信息
		/// </summary>
		DLT698OverTcp,

		/// <summary>
		/// DLT698的设备信息
		/// </summary>
		DLT698TcpNet,

	}
}
