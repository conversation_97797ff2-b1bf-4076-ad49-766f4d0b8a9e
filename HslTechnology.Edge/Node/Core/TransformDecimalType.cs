using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 指示在进行小数点操作时候的模式，四舍五入，还是取上限，还是取下限
	/// </summary>
	public enum TransformDecimalType
	{
		/// <summary>
		/// 四舍五入的操作，当中间值时取偶数
		/// </summary>
		RoundEven,

		/// <summary>
		/// 大于或等于指定数的最大整数，取下限值
		/// </summary>
		Floor,

		/// <summary>
		/// 大于或等于指定数的最小整数，取上限值
		/// </summary>
		Ceiling
	}
}
