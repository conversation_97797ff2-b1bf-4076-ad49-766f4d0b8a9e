using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 字符串数据的解析格式信息
	/// </summary>
	public enum StringFormat
	{
		/// <summary>
		/// 格式1，定长的字符串内容
		/// </summary>
		Format1= 1,

		/// <summary>
		/// 格式2，1字节长度+可变长度的字符串内容
		/// </summary>
		Format2,

		/// <summary>
		/// 格式3，[Max][Len]+可变长度的字符串内容
		/// </summary>
		Format3,

		/// <summary>
		/// 格式4，[Len][Max]+可变长度的字符串内容
		/// </summary>
		Format4,

		/// <summary>
		/// 格式5，一个short类型的长度+可变长度的字符串内容
		/// </summary>
		Format5,

		/// <summary>
		/// 格式6，一个int类型的长度+可变长度的字符串内容
		/// </summary>
		Format6,

		/// <summary>
		/// 格式7，4字节的描述头+可变short长度的字符串内容
		/// </summary>
		Format7,
	}
}
