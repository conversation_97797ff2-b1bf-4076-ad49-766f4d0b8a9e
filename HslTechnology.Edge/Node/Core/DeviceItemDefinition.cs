using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 网关所有内置的设备定义对象信息
	/// </summary>
	public class DeviceItemDefinition
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public DeviceItemDefinition( )
		{

		}

		/// <summary>
		/// 根据指定的参数来实例化一个对象信息
		/// </summary>
		/// <param name="deviceNode">设备的节点类</param>
		/// <param name="deviceObject">设备的运行类</param>
		/// <param name="imageKey">设备的绑定的图标关键字</param>
		public DeviceItemDefinition( Type deviceNode, Type deviceObject, string imageKey, string pathName )
		{
			this.DeviceObject = deviceObject;
			this.ImageKey = imageKey;
			this.DeviceNode = deviceNode;
			this.DeviceName = pathName;
		}

		#endregion

		/// <summary>
		/// 获取或设置绑定的设备节点的类
		/// </summary>
		public Type DeviceNode { get; set; }

		/// <summary>
		/// 获取或设置设备的实体对象类型，用来真正的采集设备的
		/// </summary>
		public Type DeviceObject { get; set; }

		/// <summary>
		/// 获取或设置当前的设备类型绑定的小图标列表
		/// </summary>
		public string ImageKey { get; set; }

		/// <summary>
		/// 携带Path的一个设备名称，将决定在客户端添加设备的菜单里如何呈现
		/// </summary>
		public string DeviceName { get; set; }
	}
}
