using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Core
{
	/// <summary>
	/// 消息的结构类型
	/// </summary>
	public enum MessageStructType
	{
		/// <summary>
		/// 默认的消息，不指定长度，就是接收一次的数据
		/// </summary>
		None = 1,

		/// <summary>
		/// 固定长度类型
		/// </summary>
		FixedLength = 2,

		/// <summary>
		/// 可变长度类型
		/// </summary>
		VariableLength = 3,

		/// <summary>
		/// 指定字节结尾，支持 0D 0A 的HEX格式
		/// </summary>
		SpecifiesEndChar = 4,
	}
}
