using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 标量数据的转换功能的接口
	/// </summary>
	public interface IScalarTransform
	{
		/// <inheritdoc cref="GroupNode.Name"/>
		string Name { get; set; }

		/// <inheritdoc cref="GroupNode.Description"/>
		string Description { get; set; }

		/// <inheritdoc cref="ScalarReadRequest.DispalyName"/>
		string DisplayName { get; set; }

		/// <inheritdoc cref="ScalarReadRequest.Length"/>
		int Length { get; set; }

		/// <summary>
		/// 本次请求解析字节数据的类型
		/// </summary>
		string DataTypeCode { get; set; }

		/// <summary>
		/// 获取或设置是否启动数据变换操作，0 表示没有变换，1 表示倍数及偏移变换，2 表示取反变换（仅用于bool）, 3 表示表达式变换
		/// </summary>
		int TransformType { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的倍数信息，也就是执行乘法操作
		/// </summary>
		double TransformMultiply { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的偏移信息，也就是执行加法操作
		/// </summary>
		double TransformAddition { get; set; }

		/// <summary>
		/// 此处不支持关联同设备其他点位，内置 x 表示旧的值
		/// </summary>
		string TransformExpress { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的小数点位数，如果是小于0的话，则不启用四舍五入操作
		/// </summary>
		int TransformDecimal { get; set; }

		/// <summary>
		/// 在数据类型为 string 的时候，解析字符串的时候，字符串是否遇到 0 为止自动截取
		/// </summary>
		bool StringEndwithZero { get; set; }

		/// <summary>
		/// 当读取类型为字符串的时候，获取或设置单个的字符串对应的长度信息，可能是地址长度，也可能是字节长度信息
		/// </summary>
		int StringLength { get; set; }

		/// <summary>
		/// 在数据变换的四舍五入操作中，指定四舍五入的类型，可选四舍五入，取上限，取下限
		/// </summary>
		TransformDecimalType TransformDecimalType { get; set; }

		/// <summary>
		/// 在数据类型为 bcd 的时候，解析bcd码的时候，可选的编码信息
		/// </summary>
		BCDFormat BCDFormat { get; set; }

		/// <summary>
		/// 是否是bool的规则信息
		/// </summary>
		/// <returns>是否bool规则</returns>
		bool IsBoolRegular( );
	}
}
