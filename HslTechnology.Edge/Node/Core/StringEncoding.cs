using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 字符串数据的编码内容
	/// </summary>
	public enum StringEncoding
	{
		/// <summary>
		/// ASCII编码，一般英文的都是这个编码
		/// </summary>
		ASCII,

		/// <summary>
		/// ASCII的扩展编码，常见于windows系统
		/// </summary>
		ANSI,

		/// <summary>
		/// 万国码，一般网页用这个编码
		/// </summary>
		UTF8,

		/// <summary>
		/// Unicode编码，支持中文
		/// </summary>
		UTF16,

		/// <summary>
		/// 大端的Unicode编码，支持中文
		/// </summary>
		UTF16Big,

		/// <summary>
		/// 国标2312编码，中文操作系统使用的编码信息
		/// </summary>
		GB2312,

	}
}
