using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 数据类型信息
	/// </summary>
	public enum DataType
	{
		/// <summary>
		/// Bool类型
		/// </summary>
		Bo<PERSON>,
		/// <summary>
		/// 无符号的8位整数
		/// </summary>
		Byte,
		/// <summary>
		/// 有符号的8位整数
		/// </summary>
		SByte,
		/// <summary>
		/// 有符号的16位整数
		/// </summary>
		Int16,
		/// <summary>
		/// 无符号的16位整数
		/// </summary>
		UInt16,
		/// <summary>
		/// 有符号的32位整数
		/// </summary>
		Int32,
		/// <summary>
		/// 无符号的32位整数
		/// </summary>
		UInt32,
		/// <summary>
		/// 有符号的64位整数
		/// </summary>
		Int64,
		/// <summary>
		/// 无符号的64位整数
		/// </summary>
		UInt64,
		/// <summary>
		/// 单精度的浮点数
		/// </summary>
		Float,
		/// <summary>
		/// 双精度的浮点数
		/// </summary>
		Double,
		/// <summary>
		/// 字符串内容
		/// </summary>
		String,
		/// <summary>
		/// BCD码的数据
		/// </summary>
		BCD,
		/// <summary>
		/// 16进制的字符串类型
		/// </summary>
		Hex,
		/// <summary>
		/// 表示时间的数据
		/// </summary>
		DateTime,
		/// <summary>
		/// 唯一的编码类型GUID
		/// </summary>
		Guid,
		/// <summary>
		/// 复杂的结构体数据信息
		/// </summary>
		Struct,
		/// <summary>
		/// 一个自定义的方法内容
		/// </summary>
		Method,
		/// <summary>
		/// 使用了实体的类构建的数据节点
		/// </summary>
		Class,
		/// <summary>
		/// 枚举的变量，用于节点属性的反射信息
		/// </summary>
		Enum,
	}
}
