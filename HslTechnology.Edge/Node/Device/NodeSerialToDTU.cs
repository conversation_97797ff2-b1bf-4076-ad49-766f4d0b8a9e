using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 串口转DTU的设备信息
	/// </summary>
	public class NodeSerialToDTU : DeviceNodeSerial, INodeDtu
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeSerialToDTU( )
		{
			this.DeviceType     = DeviceType.SerialToDtu;
			this.Name           = "SerialToDtu";
			this.DtuPort        = 10000;
			this.DtuIpAddress   = "*************";
			this.DtuId          = "12345678901";
			this.DtuPassword    = "123456";
			this.DtuResponseAck = true;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSerialToDTU( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// DTU设备的远程IP地址
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "DTU远程IP" )]
		[Description( "DTU设备的远程IP地址" )]
		[DefaultValue( "*************" )]
		[PropertyOrder( 1002 )]
		public string DtuIpAddress { get; set; }

		/// <summary>
		/// DTU设备的远程端口号
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "DTU远程端口" )]
		[Description( "DTU设备的远程I端口号信息" )]
		[DefaultValue( 10000 )]
		[PropertyOrder( 1003 )]
		public int DtuPort { get; set; }

		/// <summary>
		/// DTU设备的唯一ID标识信息
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "DTU标识" )]
		[Description( "DTU设备的唯一ID标识信息，最多11位长度，由字母和数字组成，区分大小写" )]
		[DefaultValue( "12345678901" )]
		[PropertyOrder( 1001 )]
		public string DtuId { get; set; }

		/// <summary>
		/// DTU设备的远程密码信息
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "DTU远程密码" )]
		[Description( "DTU设备的远程登录的密码，为6位的字母数字组成，区分大小写" )]
		[DefaultValue( "123456" )]
		[PropertyOrder( 1004 )]
		public string DtuPassword { get; set; }

		/// <summary>
		/// 是否使用自定义的注册包，如果设置本值，则表示自定义注册包，值为HEX格式的字符串信息
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "DTU自定义注册包" )]
		[Description( "是否使用自定义的注册包，如果设置本值不为空，则表示自定义注册包(忽略标识及密码)，值使用HEX格式的字符串信息" )]
		[DefaultValue( "" )]
		[PropertyOrder( 1005 )]
		public string DtuRegistrationPackage { get; set; }

		/// <summary>
		/// 获取或设置DTU设备是否支持注册包的反馈
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "DTU反馈确认" )]
		[Description( "如果设置为 true，则在发送注册包之后，需要进行检测服务器的返回状态，否则，不进行检测。" )]
		[DefaultValue( true )]
		[PropertyOrder( 1006 )]
		public bool DtuResponseAck { get; set; } = true;

		/// <summary>
		/// 获取或设置当前的DTU设备的活动超时时间，单位为秒，当监测DTU超过指定时间不活跃时，重连DTU服务器。
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "DTU活动超时" )]
		[Description( "获取或设置当前的DTU设备的活动超时时间，单位为秒，当监测DTU超过指定时间不活跃时，重连DTU服务器。" )]
		[DefaultValue( 60 )]
		[PropertyOrder( 1007 )]
		public int DtuKeepAliveTimeout { get; set; } = 60;

		/// <summary>
		/// 是否启动PING生存确认，将会每30秒进行一次ping操作
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "PING生存确认" )]
		[Description( "是否启动PING生存确认，将会每30秒进行一次ping操作" )]
		[DefaultValue( true )]
		[PropertyOrder( 1008 )]
		public bool PingEnable { get; set; } = true;

		/// <summary>
		/// 在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "报文记录格式" )]
		[Description( "在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录" )]
		[DefaultValue( true )]
		public bool LogMsgFormatBinary { get; set; } = true;

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( DtuIpAddress ),           DtuIpAddress );
			element.SetAttributeValue( nameof( DtuPort ),                DtuPort );
			element.SetAttributeValue( nameof( DtuId ),                  DtuId );
			element.SetAttributeValue( nameof( DtuPassword ),            DtuPassword );
			element.SetAttributeValue( nameof( PingEnable ),             PingEnable );
			element.SetAttributeValue( nameof( DtuResponseAck ),         DtuResponseAck );
			element.SetAttributeValue( nameof( DtuKeepAliveTimeout ),    DtuKeepAliveTimeout );
			element.SetAttributeValue( nameof( DtuRegistrationPackage ), DtuRegistrationPackage );
			element.SetAttributeValue( nameof( LogMsgFormatBinary ),     LogMsgFormatBinary );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			DtuIpAddress           = GetXmlValue( element, nameof( DtuIpAddress ),           DtuIpAddress,           m => m );
			DtuPort                = GetXmlValue( element, nameof( DtuPort ),                DtuPort,                int.Parse );
			DtuId                  = GetXmlValue( element, nameof( DtuId ),                  DtuId,                  m => m );
			DtuPassword            = GetXmlValue( element, nameof( DtuPassword ),            DtuPassword,            m => m );
			PingEnable             = GetXmlValue( element, nameof( PingEnable ),             PingEnable,             bool.Parse );
			DtuResponseAck         = GetXmlValue( element, nameof( DtuResponseAck ),         DtuResponseAck,         bool.Parse );
			DtuKeepAliveTimeout    = GetXmlValue( element, nameof( DtuKeepAliveTimeout ),    DtuKeepAliveTimeout,    int.Parse );
			DtuRegistrationPackage = GetXmlValue( element, nameof( DtuRegistrationPackage ), DtuRegistrationPackage, m => m );
			LogMsgFormatBinary     = GetXmlValue( element, nameof( LogMsgFormatBinary ),     LogMsgFormatBinary,     bool.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeSerialToDtu[{PortName}:{DtuPort}]";
	}
}
