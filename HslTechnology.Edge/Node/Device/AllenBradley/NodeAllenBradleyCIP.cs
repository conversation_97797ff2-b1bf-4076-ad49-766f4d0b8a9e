using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Node;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// AB PLC的节点数据信息
	/// </summary>
	public class NodeAllenBradleyCIP : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeAllenBradleyCIP( )
		{
			Name         = "罗克韦尔设备-CIP";
			DeviceType   = DeviceType.AllenBradleyCIP;
			Port         = 44818;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeAllenBradleyCIP( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// PLC的槽号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC槽号[slot]" )]
		[Description( "PLC的槽号信息，和实际的PLC一致" )]
		public byte Slot { get; set; }

		/// <summary>
		/// PLC当前的通信的消息路由信息，默认为空，可以实现一些复杂情况的通信，数据包含背板号，路由参数，slot，例如：*********.1.1
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "消息路由" )]
		[Description( "PLC当前的通信的消息路由信息，默认为空，如果设置了可以实现一些复杂情况的通信，数据包含背板号，路由参数，slot，例如：*********.1.1" )]
		public string MessageRouter { get; set; }

		#endregion

		#region Xml Interface
		
		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Slot          = GetXmlValue( element, nameof( Slot ),          Slot,          byte.Parse );
			MessageRouter = GetXmlValue( element, nameof( MessageRouter ), MessageRouter, m => m );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Slot ),      Slot );
			if (!string.IsNullOrEmpty( MessageRouter ))
				element.SetAttributeValue( nameof( MessageRouter ), MessageRouter );
			return element;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "A1", "全局变量名", true, true, "类型一定要和PLC实际数据对应上" ),
				new DeviceAddressExample( "Program:MainProgram.A1", "局部变量名", true, true, "如果变量是局部的，前面带上程序名" ),
				new DeviceAddressExample( "slot=2;A1", "全局变量名", true, true, "地址也可以携带额外的slot信息" ),
				new DeviceAddressExample( "i=A[0]", "全局变量名", true, true, "如果A在PLC是基于uint类型的bool数组，可以使用这种访问每个位" ),
				new DeviceAddressExample( "B[0]", "全局变量名", true, true, "如果B是数组，则可以使用索引访问每个元素" )
			};
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressBatchRequest( ) => true;

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AB PLC设备] {Name}";

		#endregion
	}
}
