using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	public class NodeAllenBradleyConnectedCIP : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeAllenBradleyConnectedCIP( )
		{
			Name = "罗克韦尔设备-CIP";
			DeviceType = DeviceType.AllenBradleyConnectedCIP;
			Port = 44818;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeAllenBradleyConnectedCIP( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "A1", "全局变量名", true, true, "类型一定要和PLC实际数据对应上" ),
				new DeviceAddressExample( "Program:MainProgram.A1", "局部变量名", true, true, "如果变量是局部的，前面带上程序名" ),
				new DeviceAddressExample( "slot=2;A1", "全局变量名", true, true, "地址也可以携带额外的slot信息" ),
				new DeviceAddressExample( "i=A[0]", "全局变量名", true, true, "如果A在PLC是基于uint类型的bool数组，可以使用这种访问每个位" ),
				new DeviceAddressExample( "B[0]", "全局变量名", true, true, "如果B是数组，则可以使用索引访问每个元素" )
			};
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AB PLC设备] {Name}";

		#endregion

	}
}
