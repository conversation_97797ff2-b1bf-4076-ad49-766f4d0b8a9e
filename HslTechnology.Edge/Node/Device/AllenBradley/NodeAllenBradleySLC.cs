using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// AB-plc的SLC协议的设备
	/// </summary>
	public class NodeAllenBradleySLC : DeviceNodeNetDTU
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeAllenBradleySLC( )
		{
			Name         = "罗克韦尔设备-SLC";
			DeviceType   = DeviceType.AllenBradleySLC;
			Port         = 44818;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeAllenBradleySLC( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 1;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "A9:0", "ASCII 变量", true, true, "如果访问位 A9:0/1 或 A9:0.1" ),
				new DeviceAddressExample( "B9:0", "Bit 变量", true, true, "如果访问位 B9:0/1 或 B9:0.1" ),
				new DeviceAddressExample( "N9:0", "Integer 变量", true, true, "如果访问位 N9:0/1 或 N9:0.1" ),
				new DeviceAddressExample( "F9:0", "Floating point", true, true, "如果访问位 F9:0/1 或 F9:0.1" ),
				new DeviceAddressExample( "S9:0", "Status 变量", true, true, "如果访问位 S:0/1 或 S:0.1 ,S:0 等同于 S2:0" ),
				new DeviceAddressExample( "ST1:0", "String", true, true, "" ),
				new DeviceAddressExample( "C9:0", "Counter", true, true, "如果访问位 C9:0/1 或 C9:0.1" ),
				new DeviceAddressExample( "I9:0", "Input", true, true, "如果访问位 I9:0/1 或 I9:0.1" ),
				new DeviceAddressExample( "O9:0", "Output", true, true, "如果访问位 O9:0/1 或 O9:0.1" ),
				new DeviceAddressExample( "R9:0", "Control", true, true, "如果访问位 R9:0/1 或 R9:0.1" ),
				new DeviceAddressExample( "T9:0", "Timer", true, true, "如果访问位 T9:0/1 或 T9:0.1" ),
				new DeviceAddressExample( "L9:0", "long integer", true, true, "如果访问位 L9:0/1 或 L9:0.1" ),
			};
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[AllenBradleySLC] {Name}";
	}
}
