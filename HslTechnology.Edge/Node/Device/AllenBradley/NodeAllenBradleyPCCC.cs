using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// AB PLC的PCCC协议的通信实现
	/// </summary>
	public class NodeAllenBradleyPCCC : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeAllenBradleyPCCC( )
		{
			Name       = "罗克韦尔设备-PCCC";
			DeviceType = DeviceType.AllenBradleyPCCC;
			Port       = 44818;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeAllenBradleyPCCC( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			return element;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "A9:0", "ASCII",           true, true, "" ),
				new DeviceAddressExample( "B2:0", "Bit",             true, true, "" ),
				new DeviceAddressExample( "N2:0", "Integer",         true, true, "" ),
				new DeviceAddressExample( "L17:0", "Long Integer",   true, true, "" ),
				new DeviceAddressExample( "ST2:0", "string",         true, true, "" ),
				new DeviceAddressExample( "F8:5", "Floating",        true, true, "" ),
				new DeviceAddressExample( "S:1/15", "Status",        true, true, "" ),
				new DeviceAddressExample( "C2:0", "Counter",         true, true, "" ),
				new DeviceAddressExample( "T2:0", "Timer",           true, true, "" ),
				new DeviceAddressExample( "I:0/15", "Input",         true, true, "" ),
				new DeviceAddressExample( "O:0/15", "Output",        true, true, "" ),
			};
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[AB PLC设备] {Name}";

		#endregion
	}
}
