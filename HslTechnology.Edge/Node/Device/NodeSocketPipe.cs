using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.Reflection;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 网口管道的节点信息
	/// </summary>
	public class NodeSocketPipe : DeviceNodeNet
	{
		/// <inheritdoc cref="GroupNode.GroupNode"/>
		public NodeSocketPipe( )
		{
			Name     = "SocketPipe";
			NodeType = NodeType.GroupSocketPipe;
		}

		/// <summary>
		/// 获取或设置是否使用为分类节点的效果，如果设置为 false，则在系统路径里，不显示，不使用管道的名称
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "当做路径使用" )]
		[Description( "获取或设置是否使用为分类节点的效果，如果设置为 false，则在系统路径里，不显示，不使用管道的名称" )]
		[DefaultValue( true )]
		[PropertyOrder( 301 )]
		public bool UseAsGroupNode { get; set; } = true;

		/// <summary>
		/// 当设备网络异常的时候，是否立即切换其他设备执行请求，默认为 true
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "设备异常是否立即切换设备" )]
		[Description( "当设备网络异常的时候，是否立即切换其他设备执行请求，默认为 true" )]
		[DefaultValue( true )]
		[PropertyOrder( 302 )]
		public bool ChangeDeviceWhenError { get; set; } = true;

		/// <summary>
		/// 获取设置设备之前切换的时候，线程休眠的时间，默认为0，表示不进行休眠操作
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "线程休眠时间" )]
		[Description( "获取设置设备之间切换的时候，线程休眠的时间，默认为0，表示不进行休眠操作" )]
		[DefaultValue( 0 )]
		[PropertyOrder( 303 )]
		public int SleepTimeBetweenDevices { get; set; } = 0;

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSocketPipe( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			if (!UseAsGroupNode) element.SetAttributeValue( nameof( UseAsGroupNode ), UseAsGroupNode );
			if (ChangeDeviceWhenError == false) element.SetAttributeValue( nameof( ChangeDeviceWhenError ), ChangeDeviceWhenError );
			if (SleepTimeBetweenDevices > 0) element.SetAttributeValue( nameof( SleepTimeBetweenDevices ), SleepTimeBetweenDevices );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UseAsGroupNode          = GetXmlValue( element, nameof( UseAsGroupNode ), UseAsGroupNode, bool.Parse );
			ChangeDeviceWhenError   = GetXmlValue( element, nameof( ChangeDeviceWhenError ), true, bool.Parse );
			SleepTimeBetweenDevices = GetXmlValue( element, nameof( SleepTimeBetweenDevices ), 0, int.Parse );
		}

		/// <summary>
		/// 创建网络的管道信息
		/// </summary>
		/// <returns>网络的管道信息</returns>
		public EdgePipeSocket CreatePipeSocket( )
		{
			PipeSocket socket     = new PipeSocket( );
			socket.IpAddress      = IpAddress;
			socket.Port           = Port;
			socket.ConnectTimeOut = ConnectTimeOut;
			socket.ReceiveTimeOut = ReceiveTimeOut;
			return new EdgePipeSocket( this, socket );
		}

		/// <inheritdoc/>
		public override string GetSocketInfo( )
		{
			return Name + "->" + base.GetSocketInfo( );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeSocketPipe[{Name}]";
	}
}
