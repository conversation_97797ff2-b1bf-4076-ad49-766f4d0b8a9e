using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// Redis的服务器配置信息
	/// </summary>
	public class NodeRedisClient : DeviceNodeNet
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeRedisClient( )
		{
			Name       = "Redis设备";
			DeviceType = DeviceType.RedisClient;
			Port       = 6379;
			Password   = string.Empty;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeRedisClient( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// Redis的端口号数据，当前的属性需要进行加密存储
		/// </summary>
		[Category( "Redis信息" )]
		[DisplayName( "密码信息" )]
		[Description( "服务器的密码信息，如果没有，就不填写" )]
		[DefaultValue( "" )]
		public string Password { get; set; }

		/// <summary>
		/// 当前的Redis指定的数据块编号
		/// </summary>
		[Category( "Redis信息" )]
		[DisplayName( "数据库编号" )]
		[Description( "准备访问的数据库编号，也就是DB块号码" )]
		[DefaultValue( 0 )]
		public int DbBlock { get; set; }

		#endregion

		#region Xml Support

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Password = HslTechnologyHelper.Decrypted( element.Attribute( nameof( Password ) ).Value );
			DbBlock  = int.Parse( element.Attribute( nameof( DbBlock ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Password ), HslTechnologyHelper.Encrypted( Password ) );
			element.SetAttributeValue( nameof( DbBlock ), DbBlock.ToString( ) );
			return element;
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[RedisClient] {Name}";
	}
}
