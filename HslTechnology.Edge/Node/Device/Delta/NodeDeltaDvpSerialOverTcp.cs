using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 台达系列的串口透传对象信息
	/// </summary>
	public class NodeDeltaDvpSerialOverTcp : NodeDeltaDvpTcp
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeDeltaDvpSerialOverTcp( )
		{
			DeviceType = DeviceType.DeltaDvpTcpSerialOverTcp;
			Name = "台达-OverTCP";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeDeltaDvpSerialOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}


		/// <inheritdoc/>
		public override string ToString( ) => $"[DeltaDvpSerialOverTcp] {Name}";
	}
}
