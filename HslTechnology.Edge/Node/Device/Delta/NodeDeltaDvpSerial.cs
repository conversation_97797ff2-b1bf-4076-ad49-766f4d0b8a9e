using HslCommunication.Profinet.Delta;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 台达的Dvp系列的串口PLC节点对象
	/// </summary>
	public class NodeDeltaDvpSerial : DeviceNodeSerial
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeDeltaDvpSerial( )
		{
			DeviceType = DeviceType.DeltaDvpTcpSerial;
			Name       = "台达-Serial";
			BaudRate   = 9600;
			DataBits   = 7;
			Parity     = System.IO.Ports.Parity.Even;
			StopBits   = System.IO.Ports.StopBits.One;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeDeltaDvpSerial( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 客户端的站号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "设备标识符，也叫站号，设置范围：0-255，如果设备需要指定，请和设备方保持一致" )]
		[DefaultValue( 1 )]
		public byte Station { get; set; }

		/// <summary>
		/// 台达的系列信息
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC系列" )]
		[Description( "台达的系列信息，可以指定DVP，或是AS系列" )]
		[DefaultValue( typeof( DeltaSeries ), "Dvp" )]
		public DeltaSeries Series { get; set; } = DeltaSeries.Dvp;

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station = byte.Parse( element.Attribute( nameof( Station ) ).Value );
			Series  = GetXmlEnum( element, nameof( Series ), Series );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ), Station );
			element.SetAttributeValue( nameof( Series ), Series.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Delta.NodeDeltaHelper.GetDeviceAddressExamples( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[DeltaDvpSerial] {Name}";
	}
}
