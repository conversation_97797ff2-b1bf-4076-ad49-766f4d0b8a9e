using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 富士PLC，使用SPB协议访问，采用网口透传的方法实现
	/// </summary>
	public class NodeFujiSPBOverTcp : DeviceNodeNetDTU
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeFujiSPBOverTcp( )
		{
			DeviceType  = DeviceType.FujiSPBOverTcp;
			Name        = "富士PLC-SPB-OverTcp";
			Station     = 0;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeFujiSPBOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc cref="NodeModbusTcp.Station"/>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "需要和设备方实际的站号信息一致，0-255" )]
		[DefaultValue( 0 )]
		public byte Station { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station                  = byte.Parse( element.Attribute( nameof( Station ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),                Station );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Fuji.NodeFujiSPBHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[FujiSPBOverTcp] {Name}";

		#endregion
	}
}
