using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device.Fuji
{
	/// <summary>
	/// Fuji的SPB辅助类
	/// </summary>
	public class NodeFujiSPBHelper
	{
		/// <inheritdoc cref="DeviceNode.GetDeviceAddressExamples"/>
		public static DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "M0", "内部继电器", true, true, "读写字单位的时候，M2代表位的M32" ),
				new DeviceAddressExample( "X0", "输入继电器", true, true, "读写字单位的时候，X2代表位的X32" ),
				new DeviceAddressExample( "Y0", "输出继电器", true, true, "读写字单位的时候，Y2代表位的Y32" ),
				new DeviceAddressExample( "L0", "锁存继电器	", true, true, "" ),
				new DeviceAddressExample( "TC0", "定时器的线圈", true, true, "" ),
				new DeviceAddressExample( "TN0", "定时器的当前值", false, true, "" ),
				new DeviceAddressExample( "CC0", "计数器的线圈", true, true, "" ),
				new DeviceAddressExample( "CN0", "计数器的当前", false, true, "" ),
				new DeviceAddressExample( "D0", "数据寄存器", true, true, "读位的时候，D10.15代表第10个字的第15位" ),
				new DeviceAddressExample( "R0", "文件寄存器", true, true, "读位的时候，R10.15代表第10个字的第15位" ),
				new DeviceAddressExample( "W0", "链接寄存器", true, true, "读位的时候，W10.15代表第10个字的第15位" ),
			};
		}
	}
}
