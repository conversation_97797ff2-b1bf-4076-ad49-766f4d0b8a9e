using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 富士的SPH网络协议
	/// </summary>
	public class NodeFujiSPHNet : DeviceNodeNetDTU
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeFujiSPHNet( )
		{
			DeviceType = DeviceType.FujiSPHNet;
			Name = "富士PLC-SPH";
			ConnectionID = 254;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeFujiSPHNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 对于 CPU0-CPU7来说是CPU的站号，分为对应 0xFE-0xF7，对于P/PE link, FL-net是模块站号，分别对应0xF6-0xEF
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "连接ID" )]
		[Description( "对于 CPU0-CPU7来说是CPU的站号，分为对应 0xFE-0xF7，对于P/PE link, FL-net是模块站号，分别对应0xF6-0xEF" )]
		[DefaultValue( 254 )]
		public byte ConnectionID { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			ConnectionID = byte.Parse( element.Attribute( nameof( ConnectionID ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( ConnectionID ), ConnectionID );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "M1.0",   "内部继电器", true, true, "" ),
				new DeviceAddressExample( "M3.0",   "内部继电器", true, true, "" ),
				new DeviceAddressExample( "M10.0",  "内部继电器", true, true, "" ),
				new DeviceAddressExample( "I0",     "输入继电器", true, true, "" ),
				new DeviceAddressExample( "Q0",     "输出继电器", true, true, "" ),
			};
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeFujiSPHNet] {Name}";

		#endregion
	}
}
