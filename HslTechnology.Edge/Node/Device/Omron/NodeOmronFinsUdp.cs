using HslCommunication.Core;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 欧姆龙的FINS的UDP协议类对象
	/// </summary>
	public class NodeOmronFinsUdp  : DeviceNodeNet
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeOmronFinsUdp( )
		{
			Name               = "欧姆龙PLC-FinsUdp";
			DeviceType         = DeviceType.OmronFinsUdp;
			Port               = 9600;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeOmronFinsUdp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// PLC单元号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC单元号" )]
		[Description( "通常都是0" )]
		[DefaultValue( (byte)0 )]
		public byte DA2 { get; set; } = 0x00;

		/// <summary>
		/// 上位机的节点地址
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "本机网络号" )]
		[Description( "服务器程序所在电脑设备IP地址的最后一个数据，例如 ************* 就是105" )]
		[DefaultValue( (byte)0x0C )]
		public byte SA1 { get; set; } = 0x0C;

		/// <summary>
		/// 网络层信息，默认0x02，如果有八层消息，就设置为 7
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "网络层信息" )]
		[Description( "网络层信息，默认0x02，如果有八层消息，就设置为0x07" )]
		[DefaultValue( (byte)0x02 )]
		public byte GCT { get; set; } = 0x02;

		/// <summary>
		/// 设备的标识号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "设备的标识号" )]
		[Description( "设备的标识号，通常是 0" )]
		[DefaultValue( (byte)0x00 )]
		public byte SID { get; set; } = 0x00;

		/// <summary>
		/// 字节分析是否颠倒
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "高低字节排列顺序" )]
		[Description( "指定4字节及以上的数据类型的字节排列顺序，影响范围：int,uint,long,ulong,float,double,需要和设置一致才能读取到正确的数据" )]
		[DefaultValue( typeof( DataFormat ), "CDAB" )]
		public DataFormat DataFormat { get; set; } = DataFormat.CDAB;

		/// <summary>
		/// 字符串数据是否按照字单位进行翻转
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "字符串反转" )]
		[Description( "字符串数据是否按照字单位进行翻转，默认为True" )]
		[DefaultValue( true )]
		public bool IsStringReverse { get; set; } = true;

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			SA1             = GetXmlValue( element, nameof( SA1 ),             this.SA1, byte.Parse );
			DA2             = GetXmlValue( element, nameof( DA2 ),             this.DA2, byte.Parse );
			GCT             = GetXmlValue( element, nameof( GCT ),             this.GCT, byte.Parse );
			SID             = GetXmlValue( element, nameof( SID ),             this.SID, byte.Parse );
			IsStringReverse = GetXmlValue( element, nameof( IsStringReverse ), this.IsStringReverse, bool.Parse );
			DataFormat      = GetXmlEnum(  element, nameof( DataFormat ),      this.DataFormat );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( SA1 ),             SA1.ToString( ) );
			element.SetAttributeValue( nameof( DA2 ),             DA2.ToString( ) );
			element.SetAttributeValue( nameof( GCT ),             GCT.ToString( ) );
			element.SetAttributeValue( nameof( SID ),             SID.ToString( ) );
			element.SetAttributeValue( nameof( DataFormat ),      DataFormat.ToString( ) );
			element.SetAttributeValue( nameof( IsStringReverse ), IsStringReverse.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Omron.NodeOmronHelper.GetDeviceAddressExamples( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronFinsUdp] {Name}";
	}
}
