using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	internal class NodeOmronConnectedCipNet : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeOmronConnectedCipNet( )
		{
			Name       = "欧姆龙-CIP";
			DeviceType = DeviceType.OmronConnectedCipNet;
			Port       = 44818;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeOmronConnectedCipNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			return element;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "A1", "节点名称，根据PLC确定", true, true, "类型一定要和PLC实际数据对应上" ),
			};
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Omron PLC设备] {Name}";

		#endregion
	}
}