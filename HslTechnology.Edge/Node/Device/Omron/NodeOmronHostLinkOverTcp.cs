using HslCommunication.Core;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 欧姆龙HostLink协议的网口透传版本
	/// </summary>
	public class NodeOmronHostLinkOverTcp : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeOmronHostLinkOverTcp( )
		{
			this.Name       = "欧姆龙PLC-HostLink-OverTcp";
			this.UnitNumber = 0;
			this.DA2        = 0;
			this.DeviceType = DeviceType.OmronHostLinkOverTcp;
			this.DataFormat = DataFormat.DCBA;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeOmronHostLinkOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion
		
		#region Public Properties

		/// <inheritdoc cref="HslCommunication.Profinet.Omron.OmronHostLink.UnitNumber"/>
		[Category( "PLC信息" )]
		[DisplayName( "站号" )]
		[Description( "PLC的站号信息，通常是0，如果PLC设置了数值，这里需要和PLC设置的一致。" )]
		public byte UnitNumber { get; set; }

		/// <inheritdoc cref="HslCommunication.Profinet.Omron.OmronHostLink.DA2"/>
		[Category( "PLC信息" )]
		[DisplayName( "PLC单元号" )]
		[Description( "通常都是0" )]
		[DefaultValue( (byte)0 )]
		public byte DA2 { get; set; } = 0x00;

		/// <inheritdoc cref="HslCommunication.Profinet.Omron.OmronHostLink.SA2"/>
		[Category( "PLC信息" )]
		[DisplayName( "上位机单元号" )]
		[Description( "连接到CPU单元，串行通信板或串行通信单元时，设置“00”以指示CPU单元 " )]
		[DefaultValue( (byte)0x00 )]
		public byte SA2 { get; set; } = 0x00;

		/// <summary>
		/// 字节分析是否颠倒
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "高低字节排列顺序" )]
		[Description( "指定4字节及以上的数据类型的字节排列顺序，影响范围：int,uint,long,ulong,float,double,需要和设置一致才能读取到正确的数据" )]
		[DefaultValue( typeof( DataFormat ), "DCBA" )]
		public DataFormat DataFormat { get; set; } = DataFormat.DCBA;

		/// <summary>
		/// 字符串数据是否按照字单位进行翻转
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "字符串反转" )]
		[Description( "字符串数据是否按照字单位进行翻转，默认为True" )]
		[DefaultValue( true )]
		public bool IsStringReverse { get; set; } = true;

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			SA2             = GetXmlValue( element, nameof( SA2 ),             this.SA2,        byte.Parse );
			DA2             = GetXmlValue( element, nameof( DA2 ),             this.DA2,        byte.Parse );
			UnitNumber      = GetXmlValue( element, nameof( UnitNumber ),      this.UnitNumber, byte.Parse );
			DataFormat      = GetXmlEnum(  element, nameof( DataFormat ),      this.DataFormat );
			IsStringReverse = GetXmlValue( element, nameof( IsStringReverse ), this.IsStringReverse, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( SA2 ),             SA2.ToString( ) );
			element.SetAttributeValue( nameof( DA2 ),             DA2.ToString( ) );
			element.SetAttributeValue( nameof( UnitNumber ),      UnitNumber.ToString( ) );
			element.SetAttributeValue( nameof( DataFormat ),      DataFormat.ToString( ) );
			element.SetAttributeValue( nameof( IsStringReverse ), IsStringReverse.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Omron.NodeOmronHelper.GetDeviceAddressExamples( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronHostLinkOverTcp] {Name}";
	}
}
