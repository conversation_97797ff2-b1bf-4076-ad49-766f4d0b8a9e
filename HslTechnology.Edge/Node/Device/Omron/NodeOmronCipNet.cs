using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	public class NodeOmronCipNet : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeOmronCipNet( )
		{
			Name       = "欧姆龙-CIP";
			DeviceType = DeviceType.OmronCipNet;
			Port       = 44818;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeOmronCipNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// PLC的槽号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC槽号[slot]" )]
		[Description( "PLC的槽号信息，和实际的PLC一致" )]
		public byte Slot { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Slot = GetXmlValue( element, nameof( Slot ), Slot, byte.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Slot ), Slot );
			return element;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "A1", "节点名称，根据PLC确定", true, true, "类型一定要和PLC实际数据对应上" ),
			};
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressBatchRequest( ) => true;

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Omron PLC设备] {Name}";

		#endregion
	}
}
