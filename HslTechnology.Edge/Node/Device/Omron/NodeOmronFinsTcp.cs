using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Reflection;
using System.ComponentModel;
using HslCommunication.Core;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 指示欧姆龙对象的设备节点信息
	/// </summary>
	public class NodeOmronFinsTcp : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeOmronFinsTcp( )
		{
			Name               = "欧姆龙PLC-FinsTcp";
			DeviceType         = DeviceType.OmronFinsTcp;
			Port               = 9600;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeOmronFinsTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// PLC单元号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC单元号" )]
		[Description( "通常都是0" )]
		[DefaultValue( (byte)0 )]
		public byte DA2 { get; set; } = 0x00;

		/// <summary>
		/// 字节分析是否颠倒
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "高低字节排列顺序" )]
		[Description( "指定4字节及以上的数据类型的字节排列顺序，影响范围：int,uint,long,ulong,float,double,需要和设置一致才能读取到正确的数据" )]
		[DefaultValue( typeof( DataFormat ), "DCBA" )]
		public DataFormat DataFormat { get; set; } = DataFormat.DCBA;

		/// <summary>
		/// 字符串数据是否按照字单位进行翻转
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "字符串反转" )]
		[Description( "字符串数据是否按照字单位进行翻转，默认为True" )]
		[DefaultValue( true )]
		public bool IsStringReverse { get; set; } = true;

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			DA2             = byte.Parse( element.Attribute( nameof( DA2 ) ).Value );
			DataFormat      = GetXmlEnum( element, nameof( DataFormat ), this.DataFormat );
			IsStringReverse = GetXmlValue( element, nameof( IsStringReverse ), this.IsStringReverse, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( DA2 ),             DA2.ToString( ) );
			element.SetAttributeValue( nameof( DataFormat ),      DataFormat.ToString( ) );
			element.SetAttributeValue( nameof( IsStringReverse ), IsStringReverse.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Omron.NodeOmronHelper.GetDeviceAddressExamples( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronFinsTcp] {Name}";
	}
}
