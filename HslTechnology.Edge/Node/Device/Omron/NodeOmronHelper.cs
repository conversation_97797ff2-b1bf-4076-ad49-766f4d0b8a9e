using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device.Omron
{
	/// <summary>
	/// 欧姆龙的复制类对象
	/// </summary>
	public class NodeOmronHelper
	{
		/// <inheritdoc cref="DeviceNode.GetDeviceAddressExamples"/>
		public static DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "D0 / DM0",   "DM Area",            true, true, "读取位使用 D10.11" ),
				new DeviceAddressExample( "C0 / CIO0",   "CIO Area",           true, true, "读取位使用 C10.11" ),
				new DeviceAddressExample( "W0 / WR0",   "Work Area",          true, true, "读取位使用 W10.11" ),
				new DeviceAddressExample( "H0 / HR0",   "Holding Bit Area",   true, true, "读取位使用 H10.11" ),
				new DeviceAddressExample( "A0 / AR0",   "Auxiliary Bit Area", true, true, "读取位使用 A10.11" ),
				new DeviceAddressExample( "E0.0 / EM0.0", "EM Area",            true, true, "E0.0-EF.0" ),
				new DeviceAddressExample( "TIM0",   "Timer Area",            true, true, "读位就是完成标记，读字就是当前值" ),
				new DeviceAddressExample( "CNT0",   "Counter Area",            true, true, "读位就是完成标记，读字就是当前值" ),
				new DeviceAddressExample( "IR0",   "Index Register",            false, true, "只能读字" ),
				new DeviceAddressExample( "DR0",   "Data Register",            false, true, "只能读字" ),
				new DeviceAddressExample( "CF1.2",   "Condition Flags",            true, false, "只能读位" ),
			};
		}

	}
}
