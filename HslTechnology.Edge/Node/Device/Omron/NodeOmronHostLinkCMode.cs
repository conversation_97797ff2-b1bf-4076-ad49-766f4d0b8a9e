using HslCommunication.Core;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 欧姆龙的HostLinkCMode协议的PLC，通过串口访问的设备
	/// </summary>
	public class NodeOmronHostLinkCMode : DeviceNodeSerial
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeOmronHostLinkCMode( )
		{
			this.Name       = "欧姆龙PLC-HostLinkCMode";
			this.UnitNumber = 0;
			this.DeviceType = DeviceType.OmronHostLinkCMode;
			this.DataFormat = DataFormat.DCBA;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeOmronHostLinkCMode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion
		
		#region Public Properties

		/// <inheritdoc cref="HslCommunication.Profinet.Omron.OmronHostLink.UnitNumber"/>
		[Category( "PLC信息" )]
		[DisplayName( "站号" )]
		[Description( "PLC的站号信息，通常是0，如果PLC设置了数值，这里需要和PLC设置的一致。" )]
		public byte UnitNumber { get; set; }

		/// <summary>
		/// 字节分析是否颠倒
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "高低字节排列顺序" )]
		[Description( "指定4字节及以上的数据类型的字节排列顺序，影响范围：int,uint,long,ulong,float,double,需要和设置一致才能读取到正确的数据" )]
		[DefaultValue( typeof( DataFormat ), "DCBA" )]
		public DataFormat DataFormat { get; set; } = DataFormat.DCBA;

		/// <summary>
		/// 字符串数据是否按照字单位进行翻转
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "字符串反转" )]
		[Description( "字符串数据是否按照字单位进行翻转，默认为True" )]
		[DefaultValue( true )]
		public bool IsStringReverse { get; set; } = true;

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UnitNumber      = GetXmlValue( element, nameof( UnitNumber ),      this.UnitNumber,      byte.Parse );
			DataFormat      = GetXmlEnum(  element, nameof( DataFormat ),      this.DataFormat );
			IsStringReverse = GetXmlValue( element, nameof( IsStringReverse ), this.IsStringReverse, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( UnitNumber ),      UnitNumber.ToString( ) );
			element.SetAttributeValue( nameof( DataFormat ),      DataFormat.ToString( ) );
			element.SetAttributeValue( nameof( IsStringReverse ), IsStringReverse.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Omron.NodeOmronHelper.GetDeviceAddressExamples( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronHostLinkCMode] [{Name}] [{PortName}:{BaudRate}]";
	}
}
