using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	public class NodeDlt698TcpNet : NodeDlt698OverTcp
	{

		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeDlt698TcpNet( )
		{
			DeviceType = DeviceType.DLT698TcpNet;
			Name = "Dlt698TcpNet";
			Station = "1";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeDlt698TcpNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}


		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeDlt698TcpNet] {Name}";

		#endregion
	}
}
