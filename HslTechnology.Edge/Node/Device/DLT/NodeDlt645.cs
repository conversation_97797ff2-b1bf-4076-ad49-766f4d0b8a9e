using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// DLT645相关的节点类
	/// </summary>
	public class NodeDlt645 : DeviceNodeSerial
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeDlt645( )
		{
			DeviceType = DeviceType.DLT645;
			Name = "DLT645";
			Station = "1";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeDlt645( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 客户端的地址域
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "地址域信息" )]
		[Description( "设备客户端的地址域，请和设备方保持一致" )]
		[DefaultValue( "1" )]
		[PropertyOrder( 401 )]
		public string Station { get; set; }

		/// <summary>
		/// 密码信息
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "密码" )]
		[Description( "设备的密码信息，默认为00000000" )]
		[DefaultValue( "00000000" )]
		[PropertyOrder( 402 )]
		public string Password { get; set; }

		/// <summary>
		/// 操作者代码
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "操作者代码" )]
		[Description( "操作做的代码信息，默认为00000000" )]
		[DefaultValue( "00000000" )]
		[PropertyOrder( 403 )]
		public string OpCode { get; set; }

		/// <summary>
		/// 获取或设置在连接之后是否
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "是否地址初始化" )]
		[Description( "如果是232单点直连的情况下，可以设置为True，来自动的初始化地址" )]
		[DefaultValue( false )]
		[PropertyOrder( 404 )]
		public bool ReadAddressAfterOpen { get; set; }

		/// <summary>
		/// 获取或设置是否在打开串口的时候进行激活命令的操作
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "初始化激活" )]
		[Description( "否在打开串口的时候进行激活命令的操作，可以设置为True，将会自动发送 FE FE FE FE报文" )]
		[DefaultValue( false )]
		[PropertyOrder( 405 )]
		public bool ActiveWhenOpen { get; set; }

		/// <summary>
		/// 获取或设置是否在每次通信时，前面增加FE FE FE FE的指令头
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "是否增加指令头" )]
		[Description( "否在打开串口的时候进行激活命令的操作，可以设置为True，将会自动发送 FE FE FE FE报文" )]
		[DefaultValue( false )]
		[PropertyOrder( 406 )]
		public bool EnableFECode { get; set; }
		
		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station                  = GetXmlValue( element, nameof( Station ),  Station,  m => m );
			Password                 = GetXmlValue( element, nameof( Password ), Password, m => m );
			OpCode                   = GetXmlValue( element, nameof( OpCode ),   OpCode,   m => m );
			ReadAddressAfterOpen     = GetXmlValue( element, nameof( ReadAddressAfterOpen ), ReadAddressAfterOpen, bool.Parse );
			ActiveWhenOpen           = GetXmlValue( element, nameof( ActiveWhenOpen ),       ActiveWhenOpen,       bool.Parse );
			EnableFECode             = GetXmlValue( element, nameof( EnableFECode ),         EnableFECode,         bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),                Station );
			element.SetAttributeValue( nameof( Password ),               Password );
			element.SetAttributeValue( nameof( OpCode ),                 OpCode );
			element.SetAttributeValue( nameof( ReadAddressAfterOpen ),   ReadAddressAfterOpen.ToString( ) );
			element.SetAttributeValue( nameof( ActiveWhenOpen ),         ActiveWhenOpen );
			element.SetAttributeValue( nameof( EnableFECode ),           EnableFECode );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return DLT.NodeDltHelper.GetDlt645Address( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeDlt645] {Name}";

		#endregion
	}
}
