using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device.DLT
{
	/// <summary>
	/// DLT645-1997的设备，使用串口转网口透传实现
	/// </summary>
	public class NodeDlt645With1997OverTcp : DeviceNodeNetDTU
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeDlt645With1997OverTcp( )
		{
			DeviceType = DeviceType.DLT645With1997OverTcp;
			Name       = "Dlt645With1997OverTcp";
			Station    = "1";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeDlt645With1997OverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 客户端的地址域
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "地址域信息" )]
		[Description( "设备客户端的地址域，请和设备方保持一致" )]
		[DefaultValue( "1" )]
		[PropertyOrder( 401 )]
		public string Station { get; set; }

		/// <summary>
		/// 获取或设置是否在打开串口的时候进行激活命令的操作
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "初始化激活" )]
		[Description( "否在打开串口的时候进行激活命令的操作，可以设置为True，将会自动发送 FE FE FE FE报文" )]
		[DefaultValue( false )]
		[PropertyOrder( 402 )]
		public bool ActiveWhenOpen { get; set; }

		/// <summary>
		/// 获取或设置是否在每次通信时，前面增加FE FE FE FE的指令头
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "是否增加指令头" )]
		[Description( "否在打开串口的时候进行激活命令的操作，可以设置为True，将会自动发送 FE FE FE FE报文" )]
		[DefaultValue( false )]
		[PropertyOrder( 403 )]
		public bool EnableFECode { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station         = GetXmlValue( element, nameof( Station ),        Station,        m => m );
			ActiveWhenOpen  = GetXmlValue( element, nameof( ActiveWhenOpen ), ActiveWhenOpen, bool.Parse );
			EnableFECode    = GetXmlValue( element, nameof( EnableFECode ),   EnableFECode,   bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),        Station );
			element.SetAttributeValue( nameof( ActiveWhenOpen ), ActiveWhenOpen );
			element.SetAttributeValue( nameof( EnableFECode ),   EnableFECode );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return DLT.NodeDltHelper.GetDlt6451997Address( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Dlt645With1997OverTcp] {Name}";

		#endregion
	}
}
