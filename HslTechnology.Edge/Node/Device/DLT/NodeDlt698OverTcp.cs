using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Reflection;
using System.ComponentModel;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	public class NodeDlt698OverTcp : DeviceNodeNetDTU
	{

		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeDlt698OverTcp( )
		{
			DeviceType = DeviceType.DLT698OverTcp;
			Name = "Dlt698OverTcp";
			Station = "1";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeDlt698OverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 客户端的地址域
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "地址域信息" )]
		[Description( "设备客户端的地址域，请和设备方保持一致" )]
		[DefaultValue( "1" )]
		[PropertyOrder( 401 )]
		public string Station { get; set; }

		/// <summary>
		/// 获取或设置在连接之后是否
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "是否地址初始化" )]
		[Description( "如果是232单点直连的情况下，可以设置为True，来自动的初始化地址" )]
		[DefaultValue( false )]
		[PropertyOrder( 402 )]
		public bool ReadAddressAfterOpen { get; set; }

		/// <summary>
		/// 获取或设置是否在每次通信时，前面增加FE FE FE FE的指令头
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "是否增加指令头" )]
		[Description( "否在打开串口的时候进行激活命令的操作，可以设置为True，将会自动发送 FE FE FE FE报文" )]
		[DefaultValue( false )]
		[PropertyOrder( 403 )]
		public bool EnableFECode { get; set; }

		/// <summary>
		/// 获取或设置是否使用安全的请求模式，对于有些仪表来说，不支持使用安全的模式，就需要设置为<c>False</c>
		/// </summary>
		[Category( "DLT信息" )]
		[DisplayName( "是否使用安全请求模式" )]
		[Description( "获取或设置是否使用安全的请求模式，对于有些仪表来说，不支持使用安全的模式，就需要设置为 False" )]
		[DefaultValue( true )]
		[PropertyOrder( 404 )]
		public bool UseSecurityResquest { get; set; } = true;

		//public byte CA { get; set; } = 0x00;

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station              = GetXmlValue( element, nameof( Station ),              Station, m => m );
			ReadAddressAfterOpen = GetXmlValue( element, nameof( ReadAddressAfterOpen ), ReadAddressAfterOpen, bool.Parse );
			EnableFECode         = GetXmlValue( element, nameof( EnableFECode ),         EnableFECode, bool.Parse );
			UseSecurityResquest  = GetXmlValue( element, nameof( UseSecurityResquest ),  UseSecurityResquest, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ), Station );
			element.SetAttributeValue( nameof( ReadAddressAfterOpen ), ReadAddressAfterOpen.ToString( ) );
			element.SetAttributeValue( nameof( EnableFECode ), EnableFECode );
			element.SetAttributeValue( nameof( UseSecurityResquest ), UseSecurityResquest );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return DLT.NodeDltHelper.GetDlt698Address( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeDlt698OverTcp] {Name}";

		#endregion

	}
}
