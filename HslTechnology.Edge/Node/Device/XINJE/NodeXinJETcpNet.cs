using HslCommunication.Profinet.XINJE;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	public class NodeXinJETcpNet : NodeModbusTcp
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeXinJETcpNet( )
		{
			Name       = "XinJETcpNet";
			DeviceType = DeviceType.XinJETcpNet;
			PlcType    = XinJESeries.XD;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeXinJETcpNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		/// <summary>
		/// 选择的是什么类型的PLC
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC类型" )]
		[Description( "PLC的系列，根据实际的型号进行选择。" )]
		[DefaultValue( typeof( XinJESeries ), "XD" )]
		public XinJESeries PlcType { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			PlcType = GetXmlEnum( element, nameof( PlcType ), this.PlcType );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( PlcType ), this.PlcType );
			return element;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return XINJE.NodeXinJEHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[XinJETcpNet-{PlcType}] {Name}";

		#endregion
	}
}
