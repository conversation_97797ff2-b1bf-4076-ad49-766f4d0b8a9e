using HslCommunication.Profinet.XINJE;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	public class NodeXinJEInternalNet : DeviceNodeNetDTU
	{

		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeXinJEInternalNet( )
		{
			Name       = "XinJEInternalNet";
			DeviceType = DeviceType.XinJEInternalNet;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeXinJEInternalNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		/// <inheritdoc cref="NodeModbusTcp.Station"/>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "需要和设备方实际的站号信息一致，0-255" )]
		[DefaultValue( 0 )]
		public byte Station { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station = GetXmlValue( element, nameof( Station ), this.Station, byte.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ), this.Station );
			return element;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return XINJE.NodeXinJEHelper.GetDeviceAddressExamples( );
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[XinJEInternalNet] {Name}";

		#endregion
	}
}
