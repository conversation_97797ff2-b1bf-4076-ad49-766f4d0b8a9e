using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Profinet.XINJE;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 基于Mdbus协议的信捷串口设备，底层使用的modbusrtu协议
	/// </summary>
	public class NodeXinJESerial : NodeModbusRtu
	{

		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeXinJESerial( )
		{
			Name        = "XinJESerial";
			DeviceType  = DeviceType.XinJESerial;
			PlcType     = XinJESeries.XD;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeXinJESerial( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion


		/// <summary>
		/// 选择的是什么类型的PLC
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC类型" )]
		[Description( "PLC的系列，根据实际的型号进行选择。" )]
		[DefaultValue( typeof( XinJESeries ), "XD" )]
		public XinJESeries PlcType { get; set; }



		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			PlcType = GetXmlEnum( element, nameof( PlcType ), this.PlcType );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( PlcType ), this.PlcType );
			return element;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return XINJE.NodeXinJEHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[XinJESerial-{PlcType}] {Name}";

		#endregion

	}
}
