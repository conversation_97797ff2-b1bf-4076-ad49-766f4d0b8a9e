using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 实例化一个基恩士MC协议的节点对象的设备
	/// </summary>
	public class NodeKeyenceMc : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeKeyenceMc( )
		{
			Name              = "基恩士PLC-MC";
			DeviceType        = DeviceType.KeyenceMcQna3E;
			Port              = 5000;

			IsBinary          = true;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeKeyenceMc( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 网络号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "网络号" )]
		[Description( "设备的网络号，详细参照PLC的网络配置，通常都为0" )]
		[DefaultValue( (byte)0 )]
		public byte NetworkNumber { get; set; } = 0x00;

		/// <summary>
		/// 网络站号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "网络站号" )]
		[Description( "设备的网络站号，详细参照PLC的网络配置，通常都为0" )]
		[DefaultValue( (byte)0 )]
		public byte NetworkStationNumber { get; set; } = 0x00;

		/// <summary>
		/// 是否是二进制通讯
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "是否二进制" )]
		[Description( "报文格式，通常二进制和ASCII，具体取决于PLC的网络参数配置。默认二进制" )]
		[DefaultValue( true )]
		public bool IsBinary { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			NetworkNumber              = byte.Parse( element.Attribute( nameof( NetworkNumber ) ).Value );
			NetworkStationNumber       = byte.Parse( element.Attribute( nameof( NetworkStationNumber ) ).Value );
			IsBinary                   = bool.Parse( element.Attribute( nameof( IsBinary ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( NetworkNumber ),         NetworkNumber );
			element.SetAttributeValue( nameof( NetworkStationNumber ),  NetworkStationNumber );
			element.SetAttributeValue( nameof( IsBinary ),              IsBinary.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => "[基恩士MC] " + Name;

		#endregion
	}
}
