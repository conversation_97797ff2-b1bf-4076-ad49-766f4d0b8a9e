using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 基恩士Nano系列的PLC设备，使用网口透传来实现
	/// </summary>
	public class NodeKeyenceNanoOverTcp : DeviceNodeNetDTU
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeKeyenceNanoOverTcp( )
		{
			DeviceType = DeviceType.KeyenceNanoOverTcp;
			Name       = "基恩士PLC-Nano-OverTcp";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeKeyenceNanoOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Keyence.NodeKeyenceHelper.GetDeviceAddressExamples( );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[KeyenceNanoOverTcp] {Name}";
	}
}
