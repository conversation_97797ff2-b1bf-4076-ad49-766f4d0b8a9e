using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 基恩士的Nano协议的PLC设备
	/// </summary>
	public class NodeKeyenceNano : DeviceNodeSerial
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeKeyenceNano( )
		{
			DeviceType = DeviceType.KeyenceNano;
			Name = "基恩士PLC-Nano";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeKeyenceNano( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Keyence.NodeKeyenceHelper.GetDeviceAddressExamples( );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[KeyenceNano] {Name}";
	}
}
