using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device.Keyence
{
	/// <summary>
	/// 基恩士的辅助类
	/// </summary>
	public class NodeKeyenceHelper
	{

		/// <inheritdoc cref="DeviceNode.GetDeviceAddressExamples"/>
		public static DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "R0", "继电器", true, false, "" ),
				new DeviceAddressExample( "B0", "链路继电器", true, false, "KV5500/KV5000/KV3000" ),
				new DeviceAddressExample( "CR0", "控制继电器", true, false, "" ),
				new DeviceAddressExample( "MR0", "内部辅助继电器", true, false, "" ),
				new DeviceAddressExample( "LR0", "锁存继电器", true, false, "" ),
				new DeviceAddressExample( "VB0", "工作继电器", true, false, "KV5500/KV5000/KV3000" ),
				new DeviceAddressExample( "T0", "定时器", true, false, "通断信息" ),
				new DeviceAddressExample( "C0", "计数器", true, false, "通断信息" ),
				new DeviceAddressExample( "CTH0", "高速计数器", true, false, "通断信息" ),
				new DeviceAddressExample( "CTC0", "高速计数器比较器", true, false, "通断信息" ),
				new DeviceAddressExample( "DM0", "数据存储器", false, true, "" ),
				new DeviceAddressExample( "CM0", "控制存储器", false, true, "" ),
				new DeviceAddressExample( "TM0", "临时数据存储器", false, true, "" ),
				new DeviceAddressExample( "EM0", "扩展数据存储器", false, true, "" ),
				new DeviceAddressExample( "FM0", "扩展数据存储器", false, true, "" ),
				new DeviceAddressExample( "Z0", "变址寄存器", false, true, "" ),
				new DeviceAddressExample( "AT0", "数字微调器", false, true, "" ),
				new DeviceAddressExample( "W0", "链路寄存器", false, true, "KV5500/KV5000/KV3000" ),
				new DeviceAddressExample( "VM0", "工作寄存器", false, true, "KV5500/KV5000/KV3000" ),
				new DeviceAddressExample( "T0", "定时器", false, true, "当前值(current value), 读int" ),
				new DeviceAddressExample( "C0", "计数器", false, true, "当前值(current value), 读int" ),
			};
		}

	}
}
