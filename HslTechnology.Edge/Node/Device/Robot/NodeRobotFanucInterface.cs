using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// Fanuc机器人的节点对象
	/// </summary>
	public class NodeRobotFanucInterface : RobotNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeRobotFanucInterface( )
		{
			Name         = "Fanuc机器人设备";
			DeviceType   = DeviceType.RobotFanucInterface;
			Port         = 60008;
			RefreshTime  = 100;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeRobotFanucInterface( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => true;

		#endregion

		#region Public Properties

		/// <summary>
		/// 当前的缓存数据更新的时间，默认100ms
		/// </summary>
		[Category( "Robot信息" )]
		[DisplayName( "缓存刷新时间" )]
		[Description( "数据来源于缓存，当超过指定时间后，缓存就会刷新，单位毫秒，100就是100ms" )]
		[DefaultValue( 100 )]
		public int RefreshTime { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			RefreshTime = int.Parse( element.Attribute( nameof( RefreshTime ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( RefreshTime ), RefreshTime );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Fanuc机器人设备] {Name}";

		#endregion
	}
}
