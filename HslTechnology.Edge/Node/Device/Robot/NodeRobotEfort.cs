using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 埃夫特机器人的节点对象
	/// </summary>
	public class NodeRobotEfort : RobotNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeRobotEfort( )
		{
			Name            = "埃夫特机器人";
			DeviceType      = DeviceType.RobotEfort;
			Port            = 8008;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeRobotEfort( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 设备的版本是否是最新的，新版是字节对齐的
		/// </summary>
		[Category( "Robot信息" )]
		[DisplayName( "是否新版协议" )]
		[Description( "新版协议是字节对齐的，应该先测试一下，确定是否新版，是的话，设置 True" )]
		public bool IsVersionNew { get; set; }

		#endregion

		#region Xml Interface
		
		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			IsVersionNew             = bool.Parse( element.Attribute( nameof( IsVersionNew ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( IsVersionNew ),          IsVersionNew.ToString( ) );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[RobotEfort] {Name}";

		#endregion
	}
}
