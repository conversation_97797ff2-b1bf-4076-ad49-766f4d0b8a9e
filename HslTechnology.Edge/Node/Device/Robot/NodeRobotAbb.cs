using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// Abb机器人的节点对象
	/// </summary>
	public class NodeRobotAbb : RobotNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeRobotAbb( )
		{
			Name             = "Abb机器人-WebApi";
			DeviceType       = DeviceType.RobotABBWebApi;
			Port             = 80;
			UserName         = "Default User";
			Password         = "robotics";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeRobotAbb( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 用户名
		/// </summary>
		[Category( "Robot信息" )]
		[DisplayName( "用户名" )]
		[Description( "机器人侧配置的登录用户名信息" )]
		[DefaultValue( "Default User" )]
		public string UserName { get; set; }

		/// <summary>
		/// 密码
		/// </summary>
		[Category( "Robot信息" )]
		[DisplayName( "密码" )]
		[Description( "机器人侧配置的密码信息，默认" )]
		[DefaultValue( "robotics" )]
		public string Password { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UserName = element.Attribute( nameof( UserName ) ).Value;
			Password = HslTechnologyHelper.Decrypted( element.Attribute( nameof( Password ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( UserName ), UserName );
			element.SetAttributeValue( nameof( Password ), HslTechnologyHelper.Encrypted( Password ) );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[RobotAbbWebApi] {Name}";

		#endregion
	}
}
