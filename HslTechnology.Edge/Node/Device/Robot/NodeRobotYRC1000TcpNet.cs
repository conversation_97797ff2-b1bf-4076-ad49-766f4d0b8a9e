using HslCommunication.Robot.YASKAWA;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device.Robot
{
	/// <summary>
	/// 安川机器人YRC1000TcpNet的节点类
	/// </summary>
	public class NodeRobotYRC1000TcpNet : RobotNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeRobotYRC1000TcpNet( )
		{
			Name = "安川机器人设备";
			DeviceType = DeviceType.RobotYRC1000TcpNet;
			Port = 80;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeRobotYRC1000TcpNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		#endregion

		#region Public Properties

		/// <summary>
		/// 获取或设置当前的机器人类型，默认为 <see cref="YRCType.YRC1000"/>
		/// </summary>
		[Category( "Robot信息" )]
		[DisplayName( "机器人类型" )]
		[Description( "获取或设置当前的机器人类型，默认为 YRCType.YRC1000" )]
		public YRCType Type { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Type = GetXmlEnum( element, nameof( Type ), Type );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Type ), Type.ToString( ) );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[RobotYRC1000] {Name}";

		#endregion
	}
}
