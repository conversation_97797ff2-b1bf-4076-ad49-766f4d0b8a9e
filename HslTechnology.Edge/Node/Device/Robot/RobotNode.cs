using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 机器人相关的节点信息
	/// </summary>
	public class RobotNode : DeviceNodeNet
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public RobotNode( )
		{
			NodeType = NodeType.RobotNode;
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override string ToString( ) => $"RobotNode[{Name}]";
	}
}
