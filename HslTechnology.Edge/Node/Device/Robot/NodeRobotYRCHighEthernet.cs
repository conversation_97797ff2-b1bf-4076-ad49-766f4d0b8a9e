using HslCommunication.Robot.YASKAWA;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device.Robot
{
	public class NodeRobotYRCHighEthernet : RobotNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeRobotYRCHighEthernet( )
		{
			Name = "安川机器人设备";
			DeviceType = DeviceType.RobotYRCHighEthernet;
			Port = 10040;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeRobotYRCHighEthernet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		#endregion

		#region Public Properties

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[RobotYRCHigh] {Name}";

		#endregion
	}
}
