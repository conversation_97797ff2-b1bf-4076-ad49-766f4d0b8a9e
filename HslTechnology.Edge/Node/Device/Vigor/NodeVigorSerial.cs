using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 丰炜PLC的，串口的波特率默认为 19200
	/// </summary>
	public class NodeVigorSerial : DeviceNodeSerial
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeVigorSerial( )
		{
			DeviceType  = DeviceType.VigorSerial;
			Name        = "丰炜PLC";
			BaudRate    = 19200;
			Station     = 0;
			DataBits    = 8;
			StopBits    = System.IO.Ports.StopBits.One;
			Parity      = System.IO.Ports.Parity.None;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeVigorSerial( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc cref="NodeModbusTcp.Station"/>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "需要和设备方实际的站号信息一致，0-255" )]
		[DefaultValue( 0 )]
		public byte Station { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station                  = byte.Parse( element.Attribute( nameof( Station ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),                Station );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Vigor.NodeVigorHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[VigorSerial] {Name}";

		#endregion
	}
}
