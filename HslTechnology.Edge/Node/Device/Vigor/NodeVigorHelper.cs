using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device.Vigor
{
	public class NodeVigorHelper
	{
		/// <inheritdoc cref="DeviceNode.GetDeviceAddressExamples"/>
		public static DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "M0", "内部继电器",     bit: true, word: false, "" ),
				new DeviceAddressExample( "X0", "输入继电器",     bit: true, word: false, "" ),
				new DeviceAddressExample( "Y0", "输出继电器",     bit: true, word: false, "" ),
				new DeviceAddressExample( "SM0", "特殊继电器	",   bit: true, word: false, "" ),
				new DeviceAddressExample( "TS0", "定时器的触点",  bit: true, word: false, "" ),
				new DeviceAddressExample( "TC0", "定时器的线圈",  bit: true, word: false, "" ),
				new DeviceAddressExample( "CS0", "计数器的触点",  bit: true, word: false, "" ),
				new DeviceAddressExample( "CC0", "计数器的线圈",  bit: true, word: false, "" ),


				new DeviceAddressExample( "D0", "数据寄存器",      false, true, "D9000及以上是特殊数据寄存器" ),
				new DeviceAddressExample( "SD0", "特殊数据寄存器", false, true, "" ),
				new DeviceAddressExample( "R0", "文件寄存器",      false, true, "" ),
				new DeviceAddressExample( "T0", "定时器当前值",    false, true, "" ),
				new DeviceAddressExample( "C0", "定计数器当前值",  false, true, "C0~C199读ushort，C200~C250读uint" ),
			};
		}
	}
}
