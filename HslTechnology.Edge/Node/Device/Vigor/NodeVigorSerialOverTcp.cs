using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 丰炜PLC的网口透传类的实现
	/// </summary>
	public class NodeVigorSerialOverTcp : DeviceNodeNetDTU
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeVigorSerialOverTcp( )
		{
			DeviceType = DeviceType.VigorOverTcp;
			Name       = "Vigor-OverTcp";
			Station    = 0;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeVigorSerialOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc cref="NodeVigorSerial.Station"/>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "需要和设备方实际的站号信息一致，0-255" )]
		[DefaultValue( 0 )]
		public byte Station { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station = byte.Parse( element.Attribute( nameof( Station ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ), Station );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Vigor.NodeVigorHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[VigorSerialOverTcp] {Name}";

		#endregion
	}
}
