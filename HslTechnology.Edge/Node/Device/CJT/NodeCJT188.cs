using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device.CJT
{
	/// <summary>
	/// CJT188水表协议
	/// </summary>
	public class NodeCJT188 : DeviceNodeSerial
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeCJT188( )
		{
			DeviceType = DeviceType.CJT188;
			Name = "CJT188";
			Station = "78330015040963";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeCJT188( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 获取或设置仪表的类型
		/// </summary>
		[Category( "CJT信息" )]
		[DisplayName( "仪表类型" )]
		[Description( "获取或设置仪表的类型，输入16进制的字符串数据" )]
		[DefaultValue( "19" )]
		[PropertyOrder( 401 )]
		[TypeConverter( typeof( CJTInstrumentTypeConverter ) )]
		public string TypeCode { get; set; } = "19";

		/// <summary>
		/// 客户端的地址域
		/// </summary>
		[Category( "CJT信息" )]
		[DisplayName( "地址域信息" )]
		[Description( "设备客户端的地址域，请和设备方保持一致" )]
		[DefaultValue( "78330015040963" )]
		[PropertyOrder( 402 )]
		public string Station { get; set; }

		/// <summary>
		/// 获取或设置是否在每次通信时，前面增加FE FE FE FE的指令头
		/// </summary>
		[Category( "CJT信息" )]
		[DisplayName( "是否增加指令头" )]
		[Description( "否在打开串口的时候进行激活命令的操作，可以设置为True，将会自动发送 FE FE FE FE报文" )]
		[DefaultValue( false )]
		[PropertyOrder( 403 )]
		public bool EnableFECode { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			TypeCode     = GetXmlValue( element, nameof( TypeCode ),     TypeCode,     m => m );
			Station      = GetXmlValue( element, nameof( Station ),      Station,      m => m );
			EnableFECode = GetXmlValue( element, nameof( EnableFECode ), EnableFECode, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( TypeCode ),     TypeCode );
			element.SetAttributeValue( nameof( Station ),      Station );
			element.SetAttributeValue( nameof( EnableFECode ), EnableFECode );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return NodeCJTHelper.GetCJT188Address( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeCJT188] {Name}";

		#endregion
	}
}
