using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 横河PLC的设备节点
	/// </summary>
	public class NodeYokogawaLinkTcp : DeviceNodeNetDTU
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeYokogawaLinkTcp( )
		{
			DeviceType = DeviceType.YokogawaLinkTcp;
			Name       = "横河PLC-Link";
			CpuNumber  = 1;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeYokogawaLinkTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc cref="NodeModbusTcp.Station"/>
		[Category( "PLC信息" )]
		[DisplayName( "设备单元号" )]
		[Description( "需要和设备方实际的单元号信息一致，0-255" )]
		[DefaultValue( 1 )]
		public byte CpuNumber { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			CpuNumber = byte.Parse( element.Attribute( nameof( CpuNumber ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( CpuNumber ), CpuNumber );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "X100", "Input relay", true, true, "只能读，不能写" ),
				new DeviceAddressExample( "Y100", "Output relay", true, true, "" ),
				new DeviceAddressExample( "I100", "Internal relay", true, true, "" ),
				new DeviceAddressExample( "E100", "Share relay", true, true, "" ),
				new DeviceAddressExample( "M100", "Special relay", true, true, "" ),
				new DeviceAddressExample( "T100", "Time relay", true, true, "" ),
				new DeviceAddressExample( "C100", "Counter relay", true, true, "" ),
				new DeviceAddressExample( "L100", "link relay", true, true, "" ),
				new DeviceAddressExample( "D100", "Data register", false, true, "" ),
				new DeviceAddressExample( "B100", "File register", false, true, "Only available for sequence CPU modules F3SP22, F3SP25, F3SP28, F3SP35, F3SP38, F3SP53, F3SP58, F3SP59, F3SP66, F3SP67, F3SP71 and F3SP76" ),
				new DeviceAddressExample( "F100", "Cache register", false, true, "Only available for sequence CPU modules F3SP71 and F3SP76" ),
				new DeviceAddressExample( "R100", "Shared register", false, true, "" ),
				new DeviceAddressExample( "V100", "Index register", false, true, "" ),
				new DeviceAddressExample( "Z100", "Special register", false, true, "" ),
				new DeviceAddressExample( "W100", "Link register", false, true, "" ),
				new DeviceAddressExample( "TN100", "Timer current value", false, true, "" ),
				new DeviceAddressExample( "CN100", "Counter current value", false, true, "" ),
			};
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[YokogawaLinkTcp] {Name}";

		#endregion
	}
}
