using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// Modbus Ascii的基本信息
	/// </summary>
	public class NodeModbusAscii : NodeModbusRtu
	{
		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeModbusAscii() : base( )
		{
			Name       = "ModbusAscii";
			DeviceType = DeviceType.ModbusAscii;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeModbusAscii( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusAscii] {Name}";
	}
}
