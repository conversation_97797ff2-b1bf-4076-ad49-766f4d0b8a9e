using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// Modbus Rtu的基本信息
	/// </summary>
	public class NodeModbusRtu : DeviceNodeSerial
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeModbusRtu( )
		{
			DeviceType  = DeviceType.ModbusRtu;
			Name        = "ModbusRtu";
			Station     = 1;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeModbusRtu( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 客户端的站号
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "站号信息" )]
		[Description( "设备标识符，也叫站号，设置范围：0-255，如果设备需要指定，请和设备方保持一致" )]
		[DefaultValue( 1 )]
		public byte Station { get; set; }

		/// <summary>
		/// 起始地址是否从0开始
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "是否地址从 0 开始？" )]
		[Description( "大部分的情况是从0开始，也即设置 True，偶尔少部分的设备是从1开始的，这时候就需要设置 False" )]
		[DefaultValue( true )]
		public bool IsAddressStartWithZero { get; set; } = true;

		/// <summary>
		/// 字节分析是否颠倒
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "高低字节排列顺序" )]
		[Description( "指定4字节及以上的数据类型的字节排列顺序，影响范围：int,uint,long,ulong,float,double,需要和设置一致才能读取到正确的数据" )]
		[DefaultValue( typeof( DataFormat ), "ABCD" )]
		public DataFormat DataFormat { get; set; } = DataFormat.ABCD;

		/// <summary>
		/// 字符串分析是否颠倒
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "字符串是否颠倒解析" )]
		[Description( "在解析字符串数据时，是否需要两两字节颠倒，因为有些设备是需要这么操作的。" )]
		[DefaultValue( false )]
		public bool IsStringReverse { get; set; } = false;

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station                  = GetXmlValue( element, nameof( Station ),                Station,                byte.Parse );
			IsAddressStartWithZero   = GetXmlValue( element, nameof( IsAddressStartWithZero ), IsAddressStartWithZero, bool.Parse );
			DataFormat               = GetXmlEnum( element,  nameof( DataFormat ),             DataFormat );
			IsStringReverse          = GetXmlValue( element, nameof( IsStringReverse ),        IsStringReverse,        bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),                Station );
			element.SetAttributeValue( nameof( IsAddressStartWithZero ), IsAddressStartWithZero );
			element.SetAttributeValue( nameof( DataFormat ),             DataFormat.ToString( ) );
			element.SetAttributeValue( nameof( IsStringReverse ),        IsStringReverse );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address )
		{
			if (System.Text.RegularExpressions.Regex.IsMatch( address, "x=[12];", System.Text.RegularExpressions.RegexOptions.IgnoreCase ))
			{
				return -8;
			}
			return 2;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Modbus.NodeModbusHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusRtu] {Name}";

		#endregion

	}
}
