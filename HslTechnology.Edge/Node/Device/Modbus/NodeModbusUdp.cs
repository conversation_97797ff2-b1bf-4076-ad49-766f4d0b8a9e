using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// ModbusUdp的设备信息
	/// </summary>
	public class NodeModbusUdp : NodeModbusTcp
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeModbusUdp( )
		{
			DeviceType    = DeviceType.ModbusUdp;
			Name          = "ModbusUdp";
			Port          = 502;
			Station       = 1;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeModbusUdp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusUdp] {Name}";
	}
}
