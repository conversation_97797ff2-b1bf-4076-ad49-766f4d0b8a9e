using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device.Modbus
{
	/// <summary>
	/// Modbus协议相关的辅助类
	/// </summary>
	public class NodeModbusHelper
	{
		/// <inheritdoc cref="DeviceNode.GetDeviceAddressExamples"/>
		public static DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "100", "[Bool] 线圈", true, false, "读写BOOL" ),
				new DeviceAddressExample( "x=2;100", "[Bool] 输入线圈", true, false, "读写BOOL，有些设备表示为 100100" ),
				new DeviceAddressExample( "100", "[Word] 寄存器", false, true, "有些设备会表示为 400100" ),
				new DeviceAddressExample( "x=4;100", "[Word] 输入寄存器", false, true, "有些设备会表示为 300100" ),
				new DeviceAddressExample( "100.1", "[Bool] 寄存器bool操作", true, false, "写入时掩码功能码，需要设备支持" ),
				new DeviceAddressExample( "s=2;100", "[Bool] 线圈", true, false, "支持携带站号信息，额外指定站号" ),
				new DeviceAddressExample( "format=DCBA;100", "[Word] 寄存器", false, true, "读写int,float支持强制指定格式" ),
				new DeviceAddressExample( "s=2;x=4;100", "[Word] 输入寄存器", false, true, "读取站号2，输入寄存器数据" ),
                new DeviceAddressExample( "x=7;w=8;100", "自定义规则时", false, true, "读取使用7功能码，写入使用8功能码" ),
            };
		}

	}
}
