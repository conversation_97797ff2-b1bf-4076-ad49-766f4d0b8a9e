using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;
using HslCommunication.Core;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 常规的Modbus-Tcp的客户端
	/// </summary>
	public class NodeModbusTcp : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeModbusTcp( )
		{
			DeviceType    = DeviceType.ModbusTcpClient;
			Name          = "ModbusTcp";
			Port          = 502;
			Station       = 1;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeModbusTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 客户端的站号
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "站号信息" )]
		[Description( "设备标识符，也叫站号，设置范围：0-255，如果设备需要指定，请和设备方保持一致" )]
		[DefaultValue( 1 )]
		public byte Station { get; set; }

		/// <summary>
		/// 起始地址是否从0开始
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "是否地址从 0 开始？" )]
		[Description( "大部分的情况是从0开始，也即设置 True，偶尔少部分的设备是从1开始的，这时候就需要设置 False" )]
		[DefaultValue( true )]
		public bool IsAddressStartWithZero { get; set; } = true;

		/// <summary>
		/// 字节分析是否颠倒
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "高低字节排列顺序" )]
		[Description( "指定4字节及以上的数据类型的字节排列顺序，影响范围：int,uint,long,ulong,float,double,需要和设置一致才能读取到正确的数据" )]
		[DefaultValue( typeof(DataFormat), "CDAB" )]
		public DataFormat DataFormat { get; set; } = DataFormat.CDAB;

		/// <summary>
		/// 字符串分析是否颠倒
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "字符串是否颠倒解析" )]
		[Description( "在解析字符串数据时，是否需要两两字节颠倒，因为有些设备是需要这么操作的。" )]
		[DefaultValue( false )]
		public bool IsStringReverse { get; set; } = false;

		[Category( "Modbus信息" )]
		[DisplayName( "是否校验消息ID一致" )]
		[Description( "在标准的modbus协议里，服务器方需要返回和发送方一致的消息ID。" )]
		[DefaultValue( true )]
		public bool CheckMessageID { get; set; } = true;

		#endregion

		#region Xml Interface
		
		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station                  = byte.Parse( element.Attribute( nameof( Station ) ).Value );
			IsAddressStartWithZero   = bool.Parse( element.Attribute( nameof( IsAddressStartWithZero ) ).Value );
			DataFormat               = SoftBasic.GetEnumFromString<DataFormat>( element.Attribute( nameof( DataFormat ) ).Value );
			IsStringReverse          = bool.Parse( element.Attribute( nameof( IsStringReverse ) ).Value );
			CheckMessageID           = GetXmlValue( element, nameof( CheckMessageID ), CheckMessageID, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),                Station );
			element.SetAttributeValue( nameof( IsAddressStartWithZero ), IsAddressStartWithZero );
			element.SetAttributeValue( nameof( DataFormat ),             DataFormat.ToString( ) );
			element.SetAttributeValue( nameof( IsStringReverse ),        IsStringReverse );
			element.SetAttributeValue( nameof( CheckMessageID ),         CheckMessageID );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address )
		{
			if (System.Text.RegularExpressions.Regex.IsMatch( address, "x=[12];", System.Text.RegularExpressions.RegexOptions.IgnoreCase ))
			{
				return -8;
			}
			return 2;
		}

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Modbus.NodeModbusHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusTcp] {Name}";

		#endregion
	}
}
