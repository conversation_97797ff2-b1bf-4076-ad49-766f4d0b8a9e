using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 一个Modbus Rtu Over Tcp的节点对象
	/// </summary>
	public class NodeModbusRtuOverTcp : NodeModbusTcp
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeModbusRtuOverTcp( ) : base( )
		{
			Name       = "ModbusRtu-OverTcp";
			DeviceType = DeviceType.ModbusRtuOverTcp;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeModbusRtuOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusRtuOverTcp] {Name}";
	}
}
