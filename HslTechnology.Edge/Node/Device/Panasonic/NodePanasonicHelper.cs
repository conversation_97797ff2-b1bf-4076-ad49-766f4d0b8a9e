using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device.Panasonic
{
	/// <summary>
	/// 松下的辅助类信息
	/// </summary>
	public class NodePanasonicHelper
	{
		/// <inheritdoc cref="DeviceNode.GetDeviceAddressExamples"/>
		public static DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "X0", "外部输入继电器", true, false, "X33 等同于 X3.3" ),
				new DeviceAddressExample( "Y0", "外部输出继电器", true, false, "Y33 等同于 Y3.3" ),
				new DeviceAddressExample( "R2.1", "内部继电器", true, false, "R21 等同于 R2.1" ),
				new DeviceAddressExample( "T0", "定时器", true, false, "" ),
				new DeviceAddressExample( "C0", "计数器", true, false, "" ),
				new DeviceAddressExample( "L2.1", "链接继电器", true, false, "L21 等同于 L2.1" ),
				new DeviceAddressExample( "D0", "数据寄存器 DT", false, true, "" ),
				new DeviceAddressExample( "LD0", "链接寄存器 LD", false, true, "" ),
				new DeviceAddressExample( "F0", "文件寄存器 FL", false, true, "" ),
				new DeviceAddressExample( "S0", "目标值 SV", false, true, "" ),
				new DeviceAddressExample( "K0", "经过值 EV", false, true, "" ),
				new DeviceAddressExample( "S0", "目标值 SV", false, true, "" ),
				new DeviceAddressExample( "IX", "索引寄存器 IX", false, true, "" ),
				new DeviceAddressExample( "IY", "索引寄存器 IY", false, true, "" ),
			};
		}
	}
}
