using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 松下PLC，使用Mewtocol协议来访问，采用网口透传的方式实现
	/// </summary>
	public class NodePanasonicMewtocolOverTcp: DeviceNodeNetDTU
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodePanasonicMewtocolOverTcp( )
		{
			DeviceType = DeviceType.PanasonicMewtocolOverTcp;
			Name       = "松下PLC-Mewtocol-OverTcp";
			Station    = 238;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodePanasonicMewtocolOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 客户端的站号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "设备标识符，也叫站号，设置范围：0-255，如果设备需要指定，请和设备方保持一致" )]
		[DefaultValue( 238 )]
		public byte Station { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station = byte.Parse( element.Attribute( nameof( Station ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ), Station );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Panasonic.NodePanasonicHelper.GetDeviceAddressExamples( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[PanasonicMewtocolOverTcp] {Name}";
	}
}
