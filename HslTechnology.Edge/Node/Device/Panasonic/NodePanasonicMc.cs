using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 松下PLC的设备节点信息
	/// </summary>
	public class NodePanasonicMc : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodePanasonicMc( )
		{
			Name             = "松下PLC-MC";
			DeviceType       = DeviceType.PanasonicMc3E;
			Port             = 5000;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodePanasonicMc(XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[PanasonicMc] {Name}";

		#endregion
	}
}
