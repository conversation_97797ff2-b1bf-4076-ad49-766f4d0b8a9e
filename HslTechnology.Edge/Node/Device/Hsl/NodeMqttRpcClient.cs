using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// MQTT客户端的节点对象，可以请求任意的基于HSL的实现的MQTTServer
	/// </summary>
	public class NodeMqttRpcClient : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode"/>
		public NodeMqttRpcClient( )
		{
			DeviceType    = DeviceType.MqttRpcClient;
			Name          = "MqttRpcClient";
			Port          = 521;
			UserName      = "admin";
			Password      = "";
			UseRSAEncrypt = true;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMqttRpcClient( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// MQTT服务器的用户名信息
		/// </summary>
		[Category( "MRPC信息" )]
		[DisplayName( "用户名" )]
		[Description( "登录时候的用户名信息" )]
		[DefaultValue( "admin" )]
		[PropertyOrder( 1101 )]
		public string UserName { get; set; }

		/// <summary>
		/// MQTT服务器的密码信息
		/// </summary>
		[Category( "MRPC信息" )]
		[DisplayName( "密码" )]
		[Description( "登录时候的密码信息" )]
		[DefaultValue( "" )]
		[PropertyOrder( 1102 )]
		public string Password { get; set; }

		/// <summary>
		/// MQTT服务器的密码信息
		/// </summary>
		[Category( "MRPC信息" )]
		[DisplayName( "是否加密模式" )]
		[Description( "登录及通信的时候是否使用加密的模式" )]
		[DefaultValue( true )]
		[PropertyOrder( 1103 )]
		public bool UseRSAEncrypt { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UserName        = GetXmlValue( element, nameof( UserName ), UserName, m => m );
			Password        = GetXmlValue( element, nameof( Password ), Password, m => m );
			UseRSAEncrypt   = GetXmlValue( element, nameof( UseRSAEncrypt ), UseRSAEncrypt, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( UserName ), UserName );
			element.SetAttributeValue( nameof( Password ), Password );
			element.SetAttributeValue( nameof( UseRSAEncrypt ), UseRSAEncrypt.ToString( ) );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[MqttRpcClient] {Name}";

		#endregion

	}
}
