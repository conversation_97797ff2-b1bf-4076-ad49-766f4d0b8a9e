using HslTechnology.Edge.Node.Core;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 基于单线程控制设备采集顺序的请求类对象，本节点下面只能携带设备对象
	/// </summary>
	public class NodeSingleThread : GroupNode
	{
		/// <inheritdoc cref="GroupNode.GroupNode"/>
		public NodeSingleThread( )
		{
			Name = "SingleThread";
			NodeType = NodeType.GroupSingleThread;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSingleThread( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 获取或设置是否使用为分类节点的效果，如果设置为 false，则在系统路径里，不显示，不使用管道的名称
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "当做路径使用" )]
		[Description( "获取或设置是否使用为分类节点的效果，如果设置为 false，则在系统路径里，不显示，不使用管道的名称" )]
		[DefaultValue( true )]
		[PropertyOrder( 301 )]
		public bool UseAsGroupNode { get; set; } = true;

		/// <summary>
		/// 获取设置设备之前切换的时候，线程休眠的时间，默认为0，表示不进行休眠操作
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "线程休眠时间" )]
		[Description( "获取设置设备之间切换的时候，线程休眠的时间，默认为0，表示不进行休眠操作" )]
		[DefaultValue( 0 )]
		[PropertyOrder( 302 )]
		public int SleepTimeBetweenDevices { get; set; } = 0;

		/// <summary>
		/// 当设备网络异常的时候，是否立即切换其他设备执行请求，默认为 true
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "设备异常是否立即切换设备" )]
		[Description( "当设备网络异常的时候，是否立即切换其他设备执行请求，默认为 true" )]
		[DefaultValue( true )]
		[PropertyOrder( 303 )]
		public bool ChangeDeviceWhenError { get; set; } = true;

		/// <summary>
		/// 当前的设备是否启用
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "设备使用状态" )]
		[Description( "设置当前设备的状态，使用中，停用中，还是停用并且从设备列表里隐藏" )]
		[DefaultValue( typeof( DeviceStatusType ), "OnWork" )]
		[PropertyOrder( 304 )]
		public DeviceStatusType StatusType { get; set; } = DeviceStatusType.OnWork;

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			if (!UseAsGroupNode) element.SetAttributeValue( nameof( UseAsGroupNode ), UseAsGroupNode );
			if (SleepTimeBetweenDevices > 0) element.SetAttributeValue( nameof( SleepTimeBetweenDevices ), SleepTimeBetweenDevices );
			if (ChangeDeviceWhenError == false) element.SetAttributeValue( nameof( ChangeDeviceWhenError ), ChangeDeviceWhenError );
			if (StatusType != DeviceStatusType.OnWork) element.SetAttributeValue( nameof( StatusType ), StatusType );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UseAsGroupNode          = GetXmlValue( element, nameof( UseAsGroupNode ), UseAsGroupNode, bool.Parse );
			SleepTimeBetweenDevices = GetXmlValue( element, nameof( SleepTimeBetweenDevices ), SleepTimeBetweenDevices, int.Parse );
			ChangeDeviceWhenError   = GetXmlValue( element, nameof( ChangeDeviceWhenError ), true, bool.Parse );
			StatusType              = GetXmlEnum( element, nameof( StatusType ), DeviceStatusType.OnWork );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeSingleThread[{Name}]";
	}
}
