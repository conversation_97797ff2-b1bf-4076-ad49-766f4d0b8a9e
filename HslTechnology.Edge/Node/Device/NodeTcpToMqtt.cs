using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	public class NodeTcpToMqtt : DeviceNodeNet
	{

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeTcpToMqtt( )
		{
			this.DeviceType = DeviceType.TcpToMqtt;
			this.Name = "TcpToMqtt";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeTcpToMqtt( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 是否启动PING生存确认，将会每30秒进行一次ping操作
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "PING生存确认" )]
		[Description( "是否启动PING生存确认，将会每30秒进行一次ping操作" )]
		[DefaultValue( true )]
		[PropertyOrder( 205 )]
		public bool PingEnable { get; set; } = true;

		/// <summary>
		/// 在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "报文记录格式" )]
		[Description( "在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录" )]
		[DefaultValue( true )]
		public bool LogMsgFormatBinary { get; set; } = true;

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( PingEnable ),         PingEnable );
			element.SetAttributeValue( nameof( LogMsgFormatBinary ), LogMsgFormatBinary );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			PingEnable         = GetXmlValue( element, nameof( PingEnable ),         PingEnable,         bool.Parse );
			LogMsgFormatBinary = GetXmlValue( element, nameof( LogMsgFormatBinary ), LogMsgFormatBinary, bool.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeTcpToMqtt[{IpAddress}]";

	}
}
