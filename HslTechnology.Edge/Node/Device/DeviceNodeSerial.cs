using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.IO.Ports;
using HslCommunication.BasicFramework;
using System.ComponentModel;
#if NET451 || NET461
using System.Drawing.Design;
using System.Windows.Forms;
using System.Windows.Forms.Design;
#endif

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 串口类设备的节点基类
	/// </summary>
	public class DeviceNodeSerial : DeviceNode
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public DeviceNodeSerial( )
		{
			BaudRate       = 9600;
			DataBits       = 8;
			StopBits       = StopBits.One;
			Parity         = Parity.Odd;
			ReceiveTimeOut = 5000;
			PortName       = "";
			RtsEnable      = false;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public DeviceNodeSerial( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 串口名称
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "端口名称" )]
		[Description( "实际设备连接服务器串行端口的名称" )]
		[DefaultValue( "" )]
		[PropertyOrder( 101 )]
		[ReadOnly( false )]
		[TypeConverter( typeof( SerialPortConverter ) )]
		public string PortName { get; set; }

		/// <summary>
		/// 波特率
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "波特率" )]
		[Description( "实际设备连接服务器串行端口的波特率，常见值为：2400,4800,9600,19200,38400" )]
		[DefaultValue( 9600 )]
		[TypeConverter(typeof( BaudRateItemConverter ) )]
		[PropertyOrder( 102 )]
		[ReadOnly( false )]
		public int BaudRate { get; set; }

		/// <summary>
		/// 标准数据位长度
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "数据位" )]
		[Description( "实际设备连接服务器串行端口的数据位长度，通常为 7, 8" )]
		[DefaultValue( 8 )]
		[PropertyOrder( 103 )]
		[ReadOnly( false )]
		public int DataBits { get; set; }

		/// <summary>
		/// 标准停止位长度
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "停止位" )]
		[Description( "实际设备连接服务器串行端口的停止位信息，None: 没有，One: 1位，Two: 两位，OnePointFive: 一点五位" )]
		[DefaultValue( typeof( StopBits ), "One" )]
		[PropertyOrder( 104 )]
		[ReadOnly( false )]
		public StopBits StopBits { get; set; }

		/// <summary>
		/// 奇偶校验检查
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "奇偶校验位" )]
		[Description( "实际设备连接服务器串行端口的奇偶校验信息" )]
		[DefaultValue( typeof( Parity ), "Odd" )]
		[TypeConverter( typeof( Converter.ParityConverter ) )]
		[PropertyOrder( 105 )]
		[ReadOnly( false )]
		public Parity Parity { get; set; }

		/// <summary>
		/// Gets or sets a value indicating whether the Request to Send (RTS) signal is enabled during serial communication.
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "RTS信号" )]
		[Description( "在串行通信中，是否请求RTS信号" )]
		[DefaultValue( false )]
		[PropertyOrder( 106 )]
		[ReadOnly( false )]
		public bool RtsEnable { get; set; }

		/// <summary>
		/// 连接超时的时间，单位毫秒
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "通讯超时时间" )]
		[Description( "单次数据通讯的超时时间，单位毫秒，默认2000，就是 2 秒" )]
		[DefaultValue( 5000 )]
		[PropertyOrder( 107 )]
		public int ReceiveTimeOut { get; set; }

		/// <summary>
		/// 每个刷新接口数据时的延时时间，一般来说，波特率越低，本质就要设置越大，默认为-1，也就是不设置
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "通讯间歇时间" )]
		[Description( "每个刷新接口数据时的延时时间，一般来说，波特率越低，本质就要设置越大，默认为-1，也就是不设置" )]
		[DefaultValue( -1 )]
		[PropertyOrder( 108 )]
		public int SleepTime { get; set; } = -1;

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( PortName ),       PortName );
			element.SetAttributeValue( nameof( BaudRate ),       BaudRate );
			element.SetAttributeValue( nameof( DataBits ),       DataBits );
			element.SetAttributeValue( nameof( StopBits ),       StopBits.ToString( ) );
			element.SetAttributeValue( nameof( Parity ),         Parity.ToString( ) );
			element.SetAttributeValue( nameof( ReceiveTimeOut ), ReceiveTimeOut.ToString( ) );
			element.SetAttributeValue( nameof( SleepTime ),      SleepTime );
			if (RtsEnable) element.SetAttributeValue( nameof( RtsEnable ),      RtsEnable );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			this.PortName       = GetXmlValue( element, nameof( PortName ),       this.PortName, m => m );
			this.BaudRate       = GetXmlValue( element, nameof( BaudRate ),       this.BaudRate, int.Parse );
			this.DataBits       = GetXmlValue( element, nameof( DataBits ),       this.DataBits, int.Parse );
			this.StopBits       = GetXmlEnum(  element, nameof( StopBits ),       this.StopBits );
			this.Parity         = GetXmlEnum(  element, nameof( Parity ),         this.Parity );
			this.ReceiveTimeOut = GetXmlValue( element, nameof( ReceiveTimeOut ), this.ReceiveTimeOut, int.Parse );
			this.SleepTime      = GetXmlValue( element, nameof( SleepTime ),      this.SleepTime,      int.Parse );
			this.RtsEnable      = GetXmlValue( element, nameof( RtsEnable ),      this.RtsEnable,      bool.Parse );
		}

		/// <summary>
		/// 获取当前串口的数据信息，例如返回 COM1:9600-8-N-1，分别为 端口号，波特率，数据位，校验，停止位
		/// </summary>
		/// <returns>返回 COM1:9600-8-N-1，分别为 端口号，波特率，数据位，校验，停止位</returns>
		public virtual string GetSerialInfo( )
		{
			if (serialPipe != null) return serialPipe.GetSerialInfo( );         // 如果有关联的管道信息，就先返回管道信息

			StringBuilder sb = new StringBuilder( );
			sb.Append( PortName );
			sb.Append( ":" );
			sb.Append( BaudRate );
			sb.Append( "-" );
			sb.Append( DataBits );
			sb.Append( "-" );
			if (Parity == Parity.None) sb.Append( "N" );
			else if (Parity == Parity.Even) sb.Append( "E" );
			else if (Parity == Parity.Odd) sb.Append( "O" );
			else if (Parity == Parity.Mark) sb.Append( "Mark" );
			else if (Parity == Parity.Space) sb.Append( "Space" );
			else sb.Append( "*" );
			sb.Append( "-" );
			if (StopBits == StopBits.None) sb.Append( "0" );
			else if (StopBits == StopBits.One) sb.Append( "1" );
			else if (StopBits == StopBits.Two) sb.Append( "2" );
			else if (StopBits == StopBits.OnePointFive) sb.Append( "1.5" );
			return sb.ToString( );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[串口设备类] {GetSerialInfo( )}";

		public void SetNodeSerialPipe( NodeSerialPipe pipe )
		{
			if (pipe != null)
			{
				serialPipe = pipe;
				PortName   = pipe.PortName;
				BaudRate   = pipe.BaudRate;
				DataBits   = pipe.DataBits;
				StopBits   = pipe.StopBits;
				Parity     = pipe.Parity;
				RtsEnable  = pipe.RtsEnable;
				if (pipe.SleepTime >= 0) SleepTime = pipe.SleepTime;
				if (pipe.ReceiveTimeOut >= 0) ReceiveTimeOut = pipe.ReceiveTimeOut;
			}
		}

		private NodeSerialPipe serialPipe = null;
	}
}
