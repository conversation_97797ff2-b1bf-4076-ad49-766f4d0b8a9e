using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	public class NodeInovanceSerialOverTcp : NodeInovanceTcpNet
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeInovanceSerialOverTcp( )
		{
			this.DeviceType = DeviceType.InovanceSerialOverTcp;
			this.Name       = "InovanceOverTCP";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeInovanceSerialOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[DeltaDvpSerialOverTcp] {Name}";

	}
}
