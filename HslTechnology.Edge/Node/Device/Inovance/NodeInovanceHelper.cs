using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device.Inovance
{
	/// <summary>
	/// 汇川的辅助类对象
	/// </summary>
	public class NodeInovanceHelper
	{
		/// <inheritdoc cref="DeviceNode.GetDeviceAddressExamples"/>
		public static DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "AM400-800地址", "", false, false, "", true ),
				new DeviceAddressExample( "Q0.0", "输出", true, false, "	Q0.0-Q8191.7 或是 Q0-Q65535" ),
				new DeviceAddressExample( "IX0.0", "输入", true, false, "IX0.0-IX8191.7 或是 I0-I65535" ),
				new DeviceAddressExample( "MX0.0", "M寄存器", true, false, "MX0.0-MX1000.10" ),
				new DeviceAddressExample( "MW0", "M寄存器", false, true, "MW0-MW65535" ),
				new DeviceAddressExample( "SM0", "", false, true, "AM600系列还支持 SM0-SM65535" ),
				new DeviceAddressExample( "SD0", "", false, true, "AM600系列还支持 SDW0-SDW65535" ),

				new DeviceAddressExample( "H3U及H5U", "", false, false, "", true ),
				new DeviceAddressExample( "M", "中间继电器", true, false, "M0-M7679，M8000-M8511" ),
				new DeviceAddressExample( "SM", "中间继电器", true, false, "SM0-SM1023" ),
				new DeviceAddressExample( "S", "中间继电器", true, false, "S0-S4095" ),
				new DeviceAddressExample( "T", "定时器", true, true, "T0-T511，读bool就是线圈，读字就是当前值" ),
				new DeviceAddressExample( "C", "计数器", true, true, "C0-C255，读bool就是线圈，读字就是当前值" ),
				new DeviceAddressExample( "X", "输入", true, false, "X0-X377 或者X0.0-X37.7" ),
				new DeviceAddressExample( "Y", "输出", true, false, "Y0-Y377 或者Y0.0-Y37.7" ),
				new DeviceAddressExample( "D", "数据寄存器", false, true, "D0-D8511" ),
				new DeviceAddressExample( "SD", "数据寄存器", false, true, "SD0-SD1023" ),
				new DeviceAddressExample( "R", "", false, true, "R0-R32767" ),

			};
		}
	}
}
