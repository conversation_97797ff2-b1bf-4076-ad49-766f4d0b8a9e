using HslCommunication.Profinet.Inovance;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 汇川的modbus-tcp通信
	/// </summary>
	public class NodeInovanceTcpNet : NodeModbusTcp
	{

		#region Constructor

		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeInovanceTcpNet( )
		{
			this.DeviceType = DeviceType.InovanceTcpNet;
			this.Name       = "InovanceTcpNet";
			this.DataFormat = HslCommunication.Core.DataFormat.CDAB;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeInovanceTcpNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 汇川的系列信息
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC系列" )]
		[Description( "汇川的系列信息，可以指定AM，H3U, H5U系列" )]
		[DefaultValue( typeof( InovanceSeries ), "AM" )]
		public InovanceSeries Series { get; set; } = InovanceSeries.AM;

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Series = GetXmlEnum( element, nameof( Series ), Series );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Series ), Series.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Inovance.NodeInovanceHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[InovanceTcpNet] {Name}";

		#endregion
	}
}
