using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// TCP转TCP的接口
	/// </summary>
	public class NodeTcpToTcp : DeviceNodeNet
	{

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeTcpToTcp( )
		{
			this.DeviceType = DeviceType.TcpToTcp;
			this.Name       = "TcpToTcp";
			this.ServerPort = 1000;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeTcpToTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 设备的端口号
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "服务端口号信息" )]
		[Description( "设备的服务端口号信息" )]
		[DefaultValue( 2000 )]
		public int ServerPort { get; set; }

		/// <summary>
		/// 是否启动PING生存确认，将会每30秒进行一次ping操作
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "PING生存确认" )]
		[Description( "是否启动PING生存确认，将会每30秒进行一次ping操作" )]
		[DefaultValue( true )]
		public bool PingEnable { get; set; } = true;

		/// <summary>
		/// 在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "报文记录格式" )]
		[Description( "在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录" )]
		[DefaultValue( true )]
		public bool LogMsgFormatBinary { get; set; } = true;

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( ServerPort ), ServerPort );
			element.SetAttributeValue( nameof( PingEnable ), PingEnable );
			element.SetAttributeValue( nameof( LogMsgFormatBinary ), LogMsgFormatBinary );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			ServerPort         = GetXmlValue( element, nameof( ServerPort ),         ServerPort,         int.Parse );
			PingEnable         = GetXmlValue( element, nameof( PingEnable ),         PingEnable,         bool.Parse );
			LogMsgFormatBinary = GetXmlValue( element, nameof( LogMsgFormatBinary ), LogMsgFormatBinary, bool.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeTcpToTcp[{IpAddress},{Port}->{ServerPort}]";
	}
}
