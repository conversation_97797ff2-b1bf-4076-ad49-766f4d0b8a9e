using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 西门子PLC，使用PPI协议，通过网口透传的模式
	/// </summary>
	public class NodeSiemensPPIOverTcp : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeSiemensPPIOverTcp( )
		{
			Name          = "西门子PLC-PPI-OverTcp";
			DeviceType    = DeviceType.SiemensPPIOverTcp;
			Station       = 2;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSiemensPPIOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 客户端的站号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "站号" )]
		[Description( "设备站号，设置范围：0-255，请和设备方保持一致" )]
		[DefaultValue( 2 )]
		public byte Station { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station = byte.Parse( element.Attribute( nameof( Station ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ), Station );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 1;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Siemens.NodeSiemensHelper.GetMcAddress( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensPPIOverTcp] {Name}";

		#endregion
	}
}
