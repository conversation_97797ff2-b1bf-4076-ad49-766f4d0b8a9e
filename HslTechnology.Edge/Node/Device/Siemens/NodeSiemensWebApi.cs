using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 西门子Webapi协议的PLC设备，主要是1500系列的设备
	/// </summary>
	public class NodeSiemensWebApi : DeviceNodeNet
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeSiemensWebApi( )
		{
			Name = "西门子PLC-Webapi";
			DeviceType = DeviceType.SiemensWebApi;
			Port = 443;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSiemensWebApi( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 当前的WebApi接口所绑定的用户名信息
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "用户名" )]
		[Description( "当前的WebApi接口所绑定的用户名信息" )]
		[DefaultValue( "admin" )]
		public string UserName { get; set; }

		/// <summary>
		/// 当前的WebApi接口所绑定的密码信息
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "密码" )]
		[Description( "当前的WebApi接口所绑定的密码信息" )]
		[DefaultValue( "" )]
		public string Password { get; set; }

		#endregion

		#region Xml Interface


		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UserName = GetXmlValue( element, nameof( UserName ), UserName, m => m );
			Password = GetXmlValue( element, nameof( Password ), Password, m => m );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( UserName ), UserName );
			element.SetAttributeValue( nameof( Password ), Password );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeSiemensWebApi] {Name}";

		#endregion
	}
}
