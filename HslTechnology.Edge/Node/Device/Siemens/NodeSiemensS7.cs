using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslCommunication.Profinet.Siemens;
using System.ComponentModel;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Node.Request;
using System.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 西门子设备的类支持
	/// </summary>
	public class NodeSiemensS7 : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeSiemensS7( )
		{
			Name           = "西门子设备";
			DeviceType     = DeviceType.SiemensS7;
			Port           = 102;
			PlcType        = SiemensPLCS.S1200;
			ConnectionType = 0x01;
			LocalTSAP      = 258;
			DestTSAP       = 0x4D57;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSiemensS7( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 选择的是什么类型的PLC
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC类型" )]
		[Description( "PLC的系列，根据实际的型号进行选择。" )]
		[DefaultValue( typeof(SiemensPLCS), "S1200" )]
		public SiemensPLCS PlcType { get; set; }

		/// <summary>
		/// PLC的机架号，某些特殊的PLC需要这个数据
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "机架号" )]
		[Description( "PLC的机架号，在博图软件进行查看" )]
		[DefaultValue( (byte)0 )]
		public byte Rack { get; set; }

		/// <summary>
		/// PLC的槽号，某些特殊的PLC需要这个数据
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "槽号" )]
		[Description( "PLC的槽号信息" )]
		[DefaultValue( (byte)0 )]
		public byte Slot { get; set; }

		/// <summary>
		/// PLC的连接方式，PG: 0x01，OP: 0x02，S7Basic: 0x03...0x10
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "PLC连接方式" )]
		[Description( "PLC的连接方式，PG: 0x01，OP: 0x02，S7Basic: 0x03...0x10，默认是 0x01" )]
		[DefaultValue( (byte)1 )]
		public byte ConnectionType { get; set; }

		/// <summary>
		/// 西门子的远程参数信息，只对S7-200有效
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "远程TSAP参数" )]
		[Description( "西门子的远程参数信息，只对S7-200有效" )]
		[DefaultValue( 0x4D57 )]
		public int DestTSAP { get; set; }

		/// <summary>
		/// 西门子相关的一个参数信息
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "本地TSAP参数" )]
		[Description( "西门子相关的一个参数信息，默认258即可，如果s7-200，则设置为19799" )]
		[DefaultValue( 258 )]
		public int LocalTSAP { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			PlcType             = SoftBasic.GetEnumFromString<SiemensPLCS>( element.Attribute( nameof( PlcType ) ).Value );
			Rack                = GetXmlValue( element, nameof( Rack ), this.Rack, byte.Parse );
			Slot                = GetXmlValue( element, nameof( Slot ), this.Slot, byte.Parse );
			ConnectionType      = GetXmlValue( element, nameof( ConnectionType ), this.ConnectionType, byte.Parse );
			LocalTSAP           = GetXmlValue( element, nameof( LocalTSAP ), this.LocalTSAP, int.Parse );
			DestTSAP            = GetXmlValue( element, nameof( DestTSAP ), this.DestTSAP, int.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( PlcType ),        PlcType.ToString( ) );
			element.SetAttributeValue( nameof( Rack ),           Rack );
			element.SetAttributeValue( nameof( Slot ),           Slot );
			element.SetAttributeValue( nameof( ConnectionType ), ConnectionType );
			element.SetAttributeValue( nameof( LocalTSAP ),      LocalTSAP );
			element.SetAttributeValue( nameof( DestTSAP ),       DestTSAP );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 1;

		/// <inheritdoc/>
		public override bool IsSupportAddressBatchRequest( ) => true;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( ) => Siemens.NodeSiemensHelper.GetMcAddress( );

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensS7-{PlcType}] {Name}";

		#endregion
	}
}
