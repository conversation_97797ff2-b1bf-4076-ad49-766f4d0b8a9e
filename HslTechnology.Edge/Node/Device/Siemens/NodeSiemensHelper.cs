using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device.Siemens
{
	/// <summary>
	/// 西门子的节点辅助类
	/// </summary>
	public class NodeSiemensHelper
	{
		/// <summary>
		/// 获取MC协议的地址示例
		/// </summary>
		/// <returns>地址示例信息</returns>
		public static DeviceAddressExample[] GetMcAddress( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "I0", "输入寄存器", true, true, "位地址示例：I1.6" ),
				new DeviceAddressExample( "Q0", "输出寄存器", true, true, "位地址示例：Q1.6" ),
				new DeviceAddressExample( "M0", "内部寄存器", true, true, "位地址示例：M1.6" ),
				new DeviceAddressExample( "DB1.0", "数据寄存器", true, true, "位地址示例：DB1.0.1" ),
				new DeviceAddressExample( "V0", "数据寄存器", true, true, "等同于DB1.0" ),

				new DeviceAddressExample( "T0", "定时器寄存器", true, true, "smart200测试通过" ),
				new DeviceAddressExample( "C0", "计数器寄存器", true, true, "smart200测试通过" ),
				new DeviceAddressExample( "AI0", "智能输入寄存器", false, true, "仅支持字单位" ),
				new DeviceAddressExample( "AQ0", "智能输出寄存器", false, true, "仅支持字单位" ),
			};
		}
	}
}
