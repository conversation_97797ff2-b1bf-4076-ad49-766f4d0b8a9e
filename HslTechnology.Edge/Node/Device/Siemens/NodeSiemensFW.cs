using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 基于西门子的fetch/write协议的节点信息
	/// </summary>
	public class NodeSiemensFW : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeSiemensFW( )
		{
			Name        = "西门子PLC-FW";
			DeviceType  = DeviceType.SiemensFW;
			Port        = 2000;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSiemensFW( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		#endregion

		#region Xml Interface


		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 1;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Siemens.NodeSiemensHelper.GetMcAddress( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensFW] {Name}";

		#endregion
	}
}
