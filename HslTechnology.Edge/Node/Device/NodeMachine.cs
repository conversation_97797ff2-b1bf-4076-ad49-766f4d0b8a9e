using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using static System.Collections.Specialized.BitVector32;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 一个富设备的节点，数据可以包含多个的子设备，或是
	/// </summary>
	public class NodeMachine : DeviceNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeMachine( )
		{
			Name         = "富设备";
			DeviceType   = DeviceType.Device;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMachine( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 客户端的站号
		/// </summary>
		[Category( "设备附加信息" )]
		[DisplayName( "关联设备" )]
		[Description( "获取或设置关联的第三方设备信息，将通过别的设备获取到相关的读写数据" )]
		[DefaultValue( "" )]
		public string OtherDevice { get; set; }

		#endregion


		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			this.OtherDevice = GetXmlValue( element, nameof( OtherDevice ), OtherDevice, m => m );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			if (!string.IsNullOrEmpty( OtherDevice ))
				element.SetAttributeValue( nameof( OtherDevice ), OtherDevice );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[富设备] {Name}";

		#endregion
	}
}
