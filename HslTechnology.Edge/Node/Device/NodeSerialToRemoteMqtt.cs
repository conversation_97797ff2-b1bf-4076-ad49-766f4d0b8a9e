using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 串口转MQTT
	/// </summary>
	public class NodeSerialToRemoteMqtt : DeviceNodeSerial
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeSerialToRemoteMqtt( )
		{
			this.DeviceType = DeviceType.SerialToRemoteMqtt;
			this.Name = "SerialToRemoteMqtt";
			this.MqttIpAddress = "127.0.0.1";
			this.MqttPort = 1883;
			this.UserName = "";
			this.MqttConnectTimeOut = 5000;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSerialToRemoteMqtt( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 设备的Ip地址
		/// </summary>
		[Category( "MQTT信息" )]
		[DisplayName( "IP地址" )]
		[DefaultValue( "***********00" )]
		[Description( "设备的IP地址信息，可以是 *********** 也可以是网址 www.hslcommunication.com" )]
		[PropertyOrder( 401 )]
		public string MqttIpAddress { get; set; }

		/// <summary>
		/// 设备的端口号
		/// </summary>
		[Category( "MQTT信息" )]
		[DisplayName( "端口号信息" )]
		[Description( "设备的端口号信息" )]
		[PropertyOrder( 402 )]
		[DefaultValue( 1883 )]
		public int MqttPort { get; set; }

		/// <summary>
		/// 连接超时的时间，单位毫秒
		/// </summary>
		[Category( "MQTT信息" )]
		[DisplayName( "连接的超时时间" )]
		[Description( "单位毫秒，默认是5000，就是 5 秒" )]
		[DefaultValue( 5000 )]
		[PropertyOrder( 403 )]
		public int MqttConnectTimeOut { get; set; }

		[Category( "MQTT信息" )]
		[DisplayName( "远程用户名" )]
		[Description( "MQTT的用户名信息，如果为空，则不进行设置" )]
		[DefaultValue( "" )]
		[PropertyOrder( 404 )]
		public string UserName { get; set; }

		[Category( "MQTT信息" )]
		[DisplayName( "远程密码" )]
		[Description( "MQTT的密码，当且用户名不为空的时候生效" )]
		[DefaultValue( "" )]
		[PropertyOrder( 405 )]
		public string Password { get; set; }

		[Category( "MQTT信息" )]
		[DisplayName( "客户端ID信息" )]
		[Description( "MQTT的客户端ID信息，如果为空的话，就默认使用当前节点设备的唯一路径信息" )]
		[DefaultValue( "" )]
		[PropertyOrder( 406 )]
		public string ClientID { get; set; }

		[Category( "MQTT信息" )]
		[DisplayName( "是否通信加密" )]
		[Description( "报文通信过程是否加密，防止第三方截获报文，当且仅当服务器是HSL创建的服务器有效。" )]
		[DefaultValue( false )]
		[PropertyOrder( 407 )]
		public bool UseRSA { get; set; }

		/// <summary>
		/// 用于订阅的主题信息，网关收到订阅的数据，然后把数据写入到设备中去。
		/// </summary>
		[Category( "MQTT信息" )]
		[DisplayName( "订阅的主题" )]
		[DefaultValue( "MqttToDevice" )]
		[Description( "用于订阅的主题信息，网关收到订阅的数据，然后把数据写入到设备中去。" )]
		[PropertyOrder( 408 )]
		public string ReadTopic { get; set; } = "MqttToDevice";

		/// <summary>
		/// 用于发布的主题信息，网关收到设备的反馈数据，然后把数据发布到远程的MQTT服务器
		/// </summary>
		[Category( "MQTT信息" )]
		[DisplayName( "发布的主题" )]
		[DefaultValue( "DeviceToMqtt" )]
		[Description( "用于发布的主题信息，网关收到设备的反馈数据，然后把数据发布到远程的MQTT服务器。" )]
		[PropertyOrder( 409 )]
		public string WriteTopic { get; set; } = "DeviceToMqtt";

		/// <summary>
		/// 在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录
		/// </summary>
		[Category( "MQTT信息" )]
		[DisplayName( "报文记录格式" )]
		[Description( "在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录" )]
		[DefaultValue( true )]
		[PropertyOrder( 410 )]
		public bool LogMsgFormatBinary { get; set; } = true;

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( MqttIpAddress ),      MqttIpAddress );
			element.SetAttributeValue( nameof( MqttPort ),           MqttPort );
			element.SetAttributeValue( nameof( UserName ),           UserName );
			element.SetAttributeValue( nameof( Password ),           Password );
			element.SetAttributeValue( nameof( ClientID ),           ClientID );
			element.SetAttributeValue( nameof( UseRSA ),             UseRSA );
			element.SetAttributeValue( nameof( ReadTopic ),          ReadTopic );
			element.SetAttributeValue( nameof( WriteTopic ),         WriteTopic );
			element.SetAttributeValue( nameof( LogMsgFormatBinary ), LogMsgFormatBinary );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			MqttIpAddress          = GetXmlValue( element, nameof( MqttIpAddress ), MqttIpAddress,       m => m );
			MqttPort               = GetXmlValue( element, nameof( MqttPort ),      MqttPort,            int.Parse );
			UserName               = GetXmlValue( element, nameof( UserName ),      UserName,            m => m );
			Password               = GetXmlValue( element, nameof( Password ),      Password,            m => m );
			ClientID               = GetXmlValue( element, nameof( ClientID ),      ClientID,            m => m );
			UseRSA                 = GetXmlValue( element, nameof( UseRSA ),        UseRSA,              bool.Parse );
			ReadTopic              = GetXmlValue( element, nameof( ReadTopic ),     ReadTopic,           m => m );
			WriteTopic             = GetXmlValue( element, nameof( WriteTopic ),    WriteTopic,          m => m );
			LogMsgFormatBinary = GetXmlValue( element, nameof( LogMsgFormatBinary ), LogMsgFormatBinary, bool.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeSerialToRemoteMqtt[{PortName}]";
	}
}
