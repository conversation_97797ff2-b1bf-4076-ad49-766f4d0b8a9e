using HslCommunication.BasicFramework;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Node.Converter;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.Node.Core;
using HslCommunication;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 指明这是一个设备对象类，包含设备的一些基本信息
	/// </summary>
	public class DeviceNode : GroupNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public DeviceNode( )
		{
			DeviceType       = DeviceType.Device;
			NodeType         = NodeType.DeviceNode;
			CreateTime       = DateTime.Now;
			InstallationDate = DateTime.Now;
			StatusType       = DeviceStatusType.OnWork;
			Description      = "此设备安装在角落，编号0001";
		}

		/// <inheritdoc cref="GroupNode.GroupNode( XElement )"/>
		public DeviceNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 使用固定的类型，数据内容来初始化当前的设备节点对象
		/// </summary>
		/// <param name="element">元素内容</param>
		/// <param name="deviceType">设备类型</param>
		public DeviceNode( XElement element, DeviceType deviceType ) : this( )
		{
			DeviceType = deviceType;
			if (element != null) LoadByXmlElement( element );
		}

		/// <summary>
		/// 根据插件类型，自动生成一个插件节点对象
		/// </summary>
		/// <param name="pluginType">插件类型</param>
		public DeviceNode( string pluginType ) : this( )
		{
			DeviceType = DeviceType.Plugins;
			PluginsType = pluginType;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 设备的类别
		/// </summary>
		[Category( "设备信息" )]
		[ReadOnly( true )]
		[DisplayName( "设备类型" )]
		[Description( "用来标记设备" )]
		[PropertyOrder( 301 )]
		public DeviceType DeviceType { get; protected set; }

		/// <summary>
		/// 当前的设备是否启用
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "设备使用状态" )]
		[Description( "设置当前设备的状态，使用中，停用中，还是停用并且从设备列表里隐藏" )]
		[DefaultValue( typeof( DeviceStatusType ), "OnWork" )]
		[PropertyOrder( 302 )]
		public DeviceStatusType StatusType { get; set; } = DeviceStatusType.OnWork;

		/// <summary>
		/// 服务器的创建日期
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "创建日期" )]
		[ReadOnly( true )]
		[Description( "当前的设备的创建日期" )]
		[TypeConverter( typeof( DateConverter ) )]
		[PropertyOrder( 303 )]
		public DateTime CreateTime { get; set; }

		/// <summary>
		/// 安装的时间
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "安装日期" )]
		[Description( "设备的安装日期" )]
		[TypeConverter( typeof( DateConverter ) )]
		[PropertyOrder( 304 )]
		public DateTime InstallationDate { get; set; }

		/// <summary>
		/// 设备的固定编号，方便进行管理
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "固定编号" )]
		[Description( "设备的唯一编号，方便进行管理" )]
		[DefaultValue( "" )]
		[PropertyOrder( 305 )]
		public string FixedNumber { get; set; }

		/// <summary>
		/// 是否开启MRPC的远程调用接口，如果当前设备支持的话
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "是否注册MRPC接口" )]
		[Description( "如果设置为 True, 则表示设备的通信对象将使用MRPC接口暴露出来，默认为 False" )]
		[DefaultValue( false )]
		[PropertyOrder( 306 )]
		public bool MRpcEnable { get; set; }

		/// <summary>
		/// 是否开启Webapi的远程调用接口，如果当前设备支持的话
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "是否注册WebApi接口" )]
		[Description( "如果设置为 True, 则表示设备的通信对象将使用WeiApi接口暴露出来，默认为 False" )]
		[DefaultValue( false )]
		[PropertyOrder( 307 )]
		public bool WebApiEnable { get; set; }

		/// <summary>
		/// 如果当前的设备为插件设备，那么本属性就是插件节点信息
		/// </summary>
		[Category( "设备信息" )]
		[DisplayName( "插件类型" )]
		[ReadOnly( true )]
		[Description( "当前插件设备的插件类型，当且仅当设备类型是插件设备时有效" )]
		[PropertyOrder( 308 )]
		public string PluginsType { get; protected set; }

		[Category( "设备信息" )]
		[DisplayName( "设备模板" )]
		[Description( "如果当前设备关联其他的模板，则本数据为模板唯一名称" )]
		[DefaultValue( "" )]
		[PropertyOrder( 309 )]
		public string Template { get; set; }

		[Category( "设备信息" )]
		[DisplayName( "主题前置信息" )]
		[Description( "如果设置本值，则表示用于MQTT上传时，使用自定义的前置主题数据" )]
		[DefaultValue( "" )]
		[PropertyOrder( 310 )]
		public string PreTopic { get; set; }

		[Category( "设备信息" )]
		[DisplayName( "连续请求间歇时间" )]
		[Description( "如果设置本值大于0，则表示所有的请求之间至少的休眠时间，默认为 0，单位毫秒，最大值为 30_000" )]
		[DefaultValue( 0 )]
		[PropertyOrder( 311 )]
		public int RequestIntervalSleep { get; set; }

		/// <summary>
		/// 当前设备节点携带的自定义的对象，不纳入XML配置信息，可以用于运行时
		/// </summary>
		[Browsable( false )]
		public object Tag { get; set; }

		#endregion

		#region Virtual Method

		/// <summary>
		/// 获取每个地址所占用的字节数量信息，从而可以计算读取到的字符串长度，或是原始字节请求时的总字节长度信息，如果小于0则表示长度不固定，不进行计算<br />
		/// Obtain the number of bytes occupied by each address, so that the length of the read string, 
		/// or the total byte length information when the original byte is requested, if less than 0, it means that the length is not fixed and no calculation is performed
		/// </summary>
		/// <param name="address">读取的地址信息</param>
		/// <returns>每个地址所占用的字节长度信息，如果返回 -1 ，表示长度不固定</returns>
		public virtual int GetEveryAddressOccupyByte( string address ) => -1;

		/// <summary>
		/// 获取当前的设备信息的地址示例，需要继承重写该示例信息<br />
		/// Example of obtaining the address of the current device information, you need to inherit and rewrite the example information
		/// </summary>
		/// <returns>地址示例的列表</returns>
		public virtual DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[0];
		}

		/// <summary>
		/// 获取当前的设备信息，是否支持地址的请求，默认为支持，如果不支持则需要重写，返回 false
		/// </summary>
		/// <returns>是否支持地址的请求</returns>
		public virtual bool IsSupportAddressRequest( )
		{
			return true;
		}

		/// <summary>
		/// 获取当前的设备是否支持地址数组的形式读取，如果支持，重写返回 <c>True</c>
		/// </summary>
		/// <returns>是否支持地址数组的形式</returns>
		public virtual bool IsSupportAddressBatchRequest( ) => false;

		/// <summary>
		/// 检查当前批量读取的地址是否合法
		/// </summary>
		/// <param name="address">等待检查的地址</param>
		/// <returns>地址是否合法</returns>
		public virtual OperateResult CheckBatchRequest( string address ) => OperateResult.CreateSuccessResult( );

		#endregion

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( DeviceType ),        DeviceType );
			element.SetAttributeValue( nameof( CreateTime ),        CreateTime.ToString( "yyyy-MM-dd HH:mm:ss" ) );
			element.SetAttributeValue( nameof( InstallationDate ),  InstallationDate.ToString( "yyyy-MM-dd HH:mm:ss" ) );
			element.SetAttributeValue( nameof( PluginsType ),       PluginsType );
			if (!string.IsNullOrEmpty( FixedNumber ))     element.SetAttributeValue( nameof( FixedNumber ),          FixedNumber );
			if (StatusType != DeviceStatusType.OnWork)    element.SetAttributeValue( nameof( StatusType ),           StatusType );
			if (MRpcEnable)                               element.SetAttributeValue( nameof( MRpcEnable ),           MRpcEnable.ToString( ) );
			if (WebApiEnable)                             element.SetAttributeValue( nameof( WebApiEnable ),         WebApiEnable.ToString( ) );
			if (!string.IsNullOrEmpty( Template ))        element.SetAttributeValue( nameof( Template ),             Template );
			if (!string.IsNullOrEmpty( PreTopic ))        element.SetAttributeValue( nameof( PreTopic ),             PreTopic );
			if (RequestIntervalSleep > 0)                 element.SetAttributeValue( nameof( RequestIntervalSleep ), RequestIntervalSleep );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			DeviceType type      = GetXmlEnum( element,  nameof( DeviceType ),           this.DeviceType );
			FixedNumber          = GetXmlValue( element, nameof( FixedNumber ),          FixedNumber,          m => m );
			CreateTime           = GetXmlValue( element, nameof( CreateTime ),           CreateTime,           DateTime.Parse );
			InstallationDate     = GetXmlValue( element, nameof( InstallationDate ),     InstallationDate,     DateTime.Parse );
			StatusType           = GetXmlEnum(  element, nameof( StatusType ),           StatusType );
			MRpcEnable           = GetXmlValue( element, nameof( MRpcEnable ),           MRpcEnable,           bool.Parse );
			WebApiEnable         = GetXmlValue( element, nameof( WebApiEnable ),         WebApiEnable,         bool.Parse );
			PluginsType          = GetXmlValue( element, nameof( PluginsType ),          PluginsType,          m => m );
			Template             = GetXmlValue( element, nameof( Template ),             Template,             m => m );
			PreTopic             = GetXmlValue( element, nameof( PreTopic ),             PreTopic,             m => m );
			RequestIntervalSleep = GetXmlValue( element, nameof( RequestIntervalSleep ), RequestIntervalSleep, int.Parse );

			if (type != this.DeviceType)
			{
				throw new Exception( $"{ToString( )} DeviceType Not Same, expect:{this.DeviceType} actual:{type}" );
			}
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"DeviceNode[{Name}]";

		#endregion
	}
}
