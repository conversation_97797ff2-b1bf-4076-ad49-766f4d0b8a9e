using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 使用以太网访问设备类节点对象，包含了IP地址，端口号，连接超时信息，并且支持了DTU的模式
	/// </summary>
	public class DeviceNodeNetDTU : DeviceNodeNet
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public DeviceNodeNetDTU( )
		{
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public DeviceNodeNetDTU( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 是否为DTU模式的设备，是的话，则设置当前的DTU的唯一ID信息，最长为11位字母或是数字，为空则不使用DTU模式
		/// </summary>
		[Category( "DTU信息" )]
		[DisplayName( "DTU唯一ID" )]
		[Description( "是否为DTU模式的设备，是的话，则设置当前的DTU的唯一ID信息，最长为11位字母或是数字，为空则不使用DTU模式。" )]
		[DefaultValue( "" )]
		public string DTU { get; set; } = string.Empty;

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			DTU = GetXmlValue( element, nameof( DTU ), DTU, m => m );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( DTU ), DTU );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"DeviceNodeNetDTU[{Name}]";

		#endregion
	}
}
