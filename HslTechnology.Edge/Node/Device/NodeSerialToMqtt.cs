using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 串口转MQTT
	/// </summary>
	public class NodeSerialToMqtt : DeviceNodeSerial
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeSerialToMqtt( )
		{
			this.DeviceType = DeviceType.SerialToMqtt;
			this.Name = "SerialToMqtt";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSerialToMqtt( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "报文记录格式" )]
		[Description( "在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录" )]
		[DefaultValue( true )]
		public bool LogMsgFormatBinary { get; set; } = true;

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( LogMsgFormatBinary ), LogMsgFormatBinary );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			LogMsgFormatBinary = GetXmlValue( element, nameof( LogMsgFormatBinary ), LogMsgFormatBinary, bool.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeSerialToMqtt[{PortName}]";
	}
}
