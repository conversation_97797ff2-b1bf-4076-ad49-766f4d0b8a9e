using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// CNC节点的基类
	/// </summary>
	public class CncNode : DeviceNodeNet
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public CncNode( )
		{
			NodeType = NodeType.CncNode;
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override string ToString( ) => $"CncNode[{Name}]";
	}
}
