using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// Fanuc的CNC系列的通讯芥蒂娜信息
	/// </summary>
	public class NodeCncFanucSerise : CncNode
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeCncFanucSerise( )
		{
			Name        = "Fanuc设备-0i";
			DeviceType  = DeviceType.CncFanucSerise0i;
			Port        = 8193;
		}
		
		/// <inheritdoc/>
		public NodeCncFanucSerise( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Override Method

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => true;

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 1;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "G1", "PMC->CNC", false, true, "原始字节请求" ),
				new DeviceAddressExample( "F1", "CNC->PMC", false, true, "原始字节请求" ),
				new DeviceAddressExample( "Y1", "PMC->Machine", false, true, "原始字节请求" ),
				new DeviceAddressExample( "X1", "Machine->PMC", false, true, "原始字节请求" ),
				new DeviceAddressExample( "A1", "Message demand", false, true, "原始字节请求" ),
				new DeviceAddressExample( "R1", "Internal relay", false, true, "原始字节请求" ),
				new DeviceAddressExample( "T1", "Timer", false, true, "原始字节请求" ),
				new DeviceAddressExample( "K1", "Keep relay", false, true, "原始字节请求" ),
				new DeviceAddressExample( "C1", "counter", false, true, "原始字节请求" ),
				new DeviceAddressExample( "D1", "Data table", false, true, "原始字节请求" ),
				new DeviceAddressExample( "E1", "Extended relay", false, true, "原始字节请求" ),
			};
		}

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Fanuc CNC] {Name}";

		#endregion
	}
}
