using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.ComponentModel;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 永宏PLC的串口访问节点
	/// </summary>
	public class NodeFatekProgram : DeviceNodeSerial
	{
		///<inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeFatekProgram( )
		{
			DeviceType  = DeviceType.FatekProgram;
			Name        = "永宏PLC-Program";
			Station     = 1;
			DataBits    = 7;
			StopBits    = System.IO.Ports.StopBits.Two;
			Parity      = System.IO.Ports.Parity.Even;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeFatekProgram( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc cref="NodeModbusTcp.Station"/>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "需要和设备方实际的站号信息一致，0-255" )]
		[DefaultValue( 1 )]
		public byte Station { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station                  = byte.Parse( element.Attribute( nameof( Station ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),                Station );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Fatek.NodeFatekHelper.GetDeviceAddressExamples( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[FatekProgram] {Name}";

		#endregion
	}
}
