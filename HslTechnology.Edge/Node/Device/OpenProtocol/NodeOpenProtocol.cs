using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device.OpenProtocol
{
	/// <summary>
	/// 实例化一个OpenProtocol协议的节点信息
	/// </summary>
	public class NodeOpenProtocol : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeOpenProtocol( )
		{
			Name = "OpenProtocol";
			DeviceType = DeviceType.OpenProtocol;
			Port = 4545;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeOpenProtocol( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			return element;
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( )
		{
			return false;
		}

		/// <inheritdoc/>
		public override bool IsSupportAddressBatchRequest( )
		{
			return false;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => "[OpenProtocol] " + Name;

		#endregion
	}
}
