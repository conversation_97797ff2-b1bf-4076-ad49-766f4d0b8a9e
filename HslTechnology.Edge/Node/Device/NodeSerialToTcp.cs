using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 串口转TCP的节点
	/// </summary>
	public class NodeSerialToTcp : DeviceNodeSerial
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeSerialToTcp( )
		{
			this.DeviceType = DeviceType.SerialToTcp;
			this.Name = "SerialToTcp";
			this.ServerPort = 1000;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSerialToTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 设备的端口号
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "服务端口号信息" )]
		[Description( "设备的服务端口号信息" )]
		[DefaultValue( 1000 )]
		public int ServerPort { get; set; }

		/// <summary>
		/// 网络端口的工作模式，默认为透传的工作模式
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "工作模式" )]
		[Description( "网络端口的工作模式，默认为透传的工作模式" )]
		[DefaultValue( typeof( NetWorkMode ), "Transparent" )]
		public NetWorkMode WorkMode { get; set; } = NetWorkMode.Transparent;

		/// <summary>
		/// 是否启动PING生存确认，将会每30秒进行一次ping操作
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "PING生存确认" )]
		[Description( "是否启动PING生存确认，将会每30秒进行一次ping操作" )]
		[DefaultValue( true )]
		public bool PingEnable { get; set; } = true;

		/// <summary>
		/// 在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "报文记录格式" )]
		[Description( "在通信报文记录日志的时候，设置是否按照二进制来进行记录，默认为True，也就是二进制，否则就是ASCII格式记录" )]
		[DefaultValue( true )]
		public bool LogMsgFormatBinary { get; set; } = true;

		/// <summary>
		/// 当工作模式为 ModbusRtu2Tcp 时，表示rtu的报文的超时重置时间，默认为 3000 毫秒
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "RTU报文超时重置时间" )]
		[Description( "当工作模式为 ModbusRtu2Tcp 时，表示rtu的报文的超时重置时间，默认为 3000 毫秒" )]
		[DefaultValue( 3000 )]
		public int RtuResetTime { get; set; } = 3000;

		/// <inheritdoc/>
		public override bool IsSupportAddressRequest( ) => false;

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( ServerPort ),         ServerPort );
			element.SetAttributeValue( nameof( PingEnable ),         PingEnable );
			element.SetAttributeValue( nameof( WorkMode ),           WorkMode.ToString( ) );
			element.SetAttributeValue( nameof( LogMsgFormatBinary ), LogMsgFormatBinary );
			element.SetAttributeValue( nameof( RtuResetTime ),       RtuResetTime );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			ServerPort         = GetXmlValue( element, nameof( ServerPort ),         ServerPort, int.Parse );
			PingEnable         = GetXmlValue( element, nameof( PingEnable ),         PingEnable, bool.Parse );
			WorkMode           = GetXmlEnum(  element, nameof( WorkMode ),           WorkMode );
			LogMsgFormatBinary = GetXmlValue( element, nameof( LogMsgFormatBinary ), LogMsgFormatBinary, bool.Parse );
			RtuResetTime       = GetXmlValue( element, nameof( RtuResetTime ),       RtuResetTime, int.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeSerialToTcp[{PortName}:{ServerPort}]";
	}
}
