using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 使用以太网访问设备类节点对象，包含了IP地址，端口号，连接超时信息
	/// </summary>
	public class DeviceNodeNet : DeviceNode
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public DeviceNodeNet( )
		{
			ConnectTimeOut = 2000;
			ReceiveTimeOut = 2000;
			IpAddress      = "*************";
			Port           = 7000;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public DeviceNodeNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 设备的Ip地址
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "IP地址" )]
		[DefaultValue( "*************" )]
		[Description( "设备的IP地址信息，可以是 *********** 也可以是网址 www.hslcommunication.com" )]
		[PropertyOrder( 201 )]
		public string IpAddress { get; set; }

		/// <summary>
		/// 冗余的设备IP地址
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "冗余IP地址" )]
		[DefaultValue( "*************" )]
		[Description( "冗余的设备IP地址信息，当主地址无法通信的时候，启用冗余的IP地址进行通信" )]
		[PropertyOrder( 202 )]
		public string RedundantIpAddress { get; set; }

		/// <summary>
		/// 设备的端口号
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "端口号信息" )]
		[Description( "设备的端口号信息" )]
		[PropertyOrder( 203 )]
		public int Port { get; set; }

		/// <summary>
		/// 连接超时的时间，单位毫秒
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "连接的超时时间" )]
		[Description( "单位毫秒，默认是2000，就是 2 秒" )]
		[DefaultValue( 2000 )]
		[PropertyOrder( 204 )]
		public int ConnectTimeOut { get; set; }

		/// <summary>
		/// 连接超时的时间，单位毫秒
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "通讯超时时间" )]
		[Description( "在单次的数据交互中，指定的超时时间，如果仍然未收到设备的数据，视为设备离线，默认是2000，就是 2 秒" )]
		[DefaultValue( 2000 )]
		[PropertyOrder( 205 )]
		public int ReceiveTimeOut { get; set; }

		/// <summary>
		/// 每条报文发送之前，需要发的另外的二进制报文内容，当使用Lora设备中转的时候，就可以设置当前的属性
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "额外通信报文" )]
		[Description( "每条报文发送之前，需要发的另外的二进制报文内容，当使用Lora设备中转的时候，就可以设置当前的属性" )]
		[PropertyOrder( 206 )]
		public string HexBeforeSend { get; set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			IpAddress          = GetXmlValue( element, nameof( IpAddress ),      IpAddress,      m => m );
			RedundantIpAddress = GetXmlValue( element, nameof( RedundantIpAddress ), RedundantIpAddress, m => m );
			Port               = GetXmlValue( element, nameof( Port ),           Port,           int.Parse );
			ConnectTimeOut     = GetXmlValue( element, nameof( ConnectTimeOut ), ConnectTimeOut, int.Parse );
			ReceiveTimeOut     = GetXmlValue( element, nameof( ReceiveTimeOut ), ReceiveTimeOut, int.Parse);
			HexBeforeSend      = GetXmlValue( element, nameof( HexBeforeSend ),  HexBeforeSend,  m => m );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( IpAddress ),          IpAddress );
			element.SetAttributeValue( nameof( RedundantIpAddress ), RedundantIpAddress );
			element.SetAttributeValue( nameof( Port ),               Port );
			element.SetAttributeValue( nameof( ConnectTimeOut ),     ConnectTimeOut );
			element.SetAttributeValue( nameof( ReceiveTimeOut ),     ReceiveTimeOut );
			if (!string.IsNullOrEmpty(HexBeforeSend))
				element.SetAttributeValue( nameof( HexBeforeSend ), HexBeforeSend );
			return element;
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 获取当前连接的参数信息，一般是返回当前的节点参数信息，如果设置了共享管道信息，那么就返回共享的管道信息
		/// </summary>
		/// <returns>通常是IP地址及端口</returns>
		public virtual string GetSocketInfo( )
		{
			if (socketPipe != null) return socketPipe.GetSocketInfo( );
			if (useRedundantIp)
				return $"{RedundantIpAddress}:{Port}";
			else
				return $"{IpAddress}:{Port}";
		}

		public void SetNodeSocketPipe( NodeSocketPipe pipe )
		{
			if (pipe != null)
			{
				socketPipe         = pipe;
				IpAddress          = socketPipe.IpAddress;
				RedundantIpAddress = socketPipe.RedundantIpAddress;
				Port               = socketPipe.Port;
				//if (pipe.ReceiveTimeOut >= 0) this.ReceiveTimeOut = pipe.ReceiveTimeOut;
				//if (pipe.ConnectTimeOut >= 0) this.ConnectTimeOut = pipe.ConnectTimeOut;
			}
		}

		/// <summary>
		/// 设置冗余的IP地址信息
		/// </summary>
		/// <param name="useRedundantIp"></param>
		public void SetRedundantIp( bool useRedundantIp )
		{
			this.useRedundantIp = useRedundantIp;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"DeviceNodeNet[{Name}]";

		#endregion

		#region Private Member

		private bool useRedundantIp = false;                // 是否使用了冗余的IP地址进行请求
		private NodeSocketPipe socketPipe = null;          // 共享的管道信息，用于当配置共享管道的时候，返回管道的参数信息

		#endregion
	}
}
