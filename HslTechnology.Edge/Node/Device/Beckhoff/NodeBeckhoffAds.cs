using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Reflection;
using System.ComponentModel;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 倍福ADS协议的节点信息
	/// </summary>
	public class NodeBeckhoffAds : DeviceNodeNetDTU
	{
		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public NodeBeckhoffAds( )
		{
			Name              = "倍福设备-ADS";
			DeviceType        = DeviceType.BeckhoffAdsNet;
			Port              = 48898;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeBeckhoffAds( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 目标的网络ID号，如果和 <see cref="SenderNetId"/> 一起为空则切换为自动获取
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "目标网络号" )]
		[Description( "目标的网络ID号，如果和本机网络号一起为空则切换为自动获取，举例：*************.1.1:801 ,并不是ip地址，TwinCAT2:801,811,821,831  TwinCAT3:851,852,853" )]
		public string TargetNetId { get; set; }

		/// <summary>
		/// 发送方的网络ID号，如果和 <see cref="TargetNetId"/> 一起为空则切换为自动获取
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "本机网络号" )]
		[Description( "发送方的网络ID号，如果和目标网络号一起为空则切换为自动获取，举例：*************.1.1 ,并不是ip地址，实际的值需要在 TwinCAT 上设置。" )]
		public string SenderNetId { get; set; }

		/// <summary>
		/// 是否启动标签缓存的功能
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "是否标签缓存" )]
		[Description( "标签缓存可以加速读取" )]
		[DefaultValue( true )]
		public bool TagCache { get; set; } = true;

		/// <summary>
		/// Ams路由信息的端口号, TwinCAT2:801,811,821,831  TwinCAT3:851,852,853
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "AMS端口号" )]
		[Description( "Ams路由信息的端口号, TwinCAT2:801,811,821,831  TwinCAT3:851,852,853" )]
		[DefaultValue( 851 )]
		public int AmsPort { get; set; } = 851;
		
		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			TargetNetId = GetXmlValue( element, nameof( TargetNetId ), this.TargetNetId, m => m );
			SenderNetId = GetXmlValue( element, nameof( SenderNetId ), this.SenderNetId, m => m );
			TagCache    = GetXmlValue( element, nameof( TagCache ),    this.TagCache, bool.Parse );
			AmsPort     = GetXmlValue( element, nameof( AmsPort ),     this.AmsPort, int.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( TargetNetId ), TargetNetId );
			element.SetAttributeValue( nameof( SenderNetId ), SenderNetId );
			element.SetAttributeValue( nameof( TagCache ),    TagCache.ToString( ) );
			element.SetAttributeValue( nameof( AmsPort ),     AmsPort );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 1;

		/// <inheritdoc/>
		public override bool IsSupportAddressBatchRequest( ) => true;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return new DeviceAddressExample[]
			{
				new DeviceAddressExample( "M100",     "", true, true, "绝对地址" ),
				new DeviceAddressExample( "I100",     "", true, true, "绝对地址" ),
				new DeviceAddressExample( "Q100",     "", true, true, "绝对地址" ),
				new DeviceAddressExample( "s=abc",    "", true, true, "符号地址，abc全局变量" ),
				new DeviceAddressExample( "s=MAIN.A", "", true, true, "符号地址，A是MAIN函数地址" ),
				new DeviceAddressExample( "i=100000", "", true, true, "内存地址" ),
			};
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[BeckhoffAds] {Name}";

		#endregion
	}
}
