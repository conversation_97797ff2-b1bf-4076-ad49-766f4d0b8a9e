using HslTechnology.Edge.Node.Core;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 基于自由的TCP实现的协议信息
	/// </summary>
	public class NodeTcpFreedom : DeviceNodeNetDTU, INodeAsciiFormate
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode"/>
		public NodeTcpFreedom( )
		{
			DeviceType = DeviceType.TcpFreedom;
			Name       = "TcpFreedom";
			MessageRegularNode = new RegularScalarNode( );
			MessageRegularNode.Name = "__MessageRegular";

			//ErrorRegularNode = new RegularScalarNode( );
			//ErrorRegularNode.Name = "__ErrorRegular";
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeTcpFreedom( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 如果想使用问答机制通信的话，设置为默认值False, 否则设置为True, 使用异步推送的机制
		/// </summary>
		[Category( "高级配置信息" )]
		[DisplayName( "是否异步推送" )]
		[Description( "如果想使用问答机制通信的话，设置为默认值False, 否则设置为True, 使用异步推送的机制" )]
		[DefaultValue( false )]
		[PropertyOrder( 1101 )]
		public bool UseServerActivePush { get; set; }

		/// <summary>
		/// 是否使用长连接的功能，当且 是否异步推送 配置为 False 时才有效的
		/// </summary>
		[Category( "高级配置信息" )]
		[DisplayName( "是否使用长连接" )]
		[Description( "是否使用长连接的功能，当且 是否异步推送 配置为 False 时才有效的" )]
		[DefaultValue( true )]
		[PropertyOrder( 1102 )]
		public bool UsePersistentConnection { get; set; } = true;

		/// <summary>
		/// 获取或设置是否使用ASCII的格式字符串来解析地址和记录电文通信，虽然是ASCII报文，仍然支持输入 \0D\0A 来表示 \r\n 等不可见字符
		/// </summary>
		[Category( "高级配置信息" )]
		[DisplayName( "是否使用ASCII格式" )]
		[Description( "获取或设置是否使用ASCII的格式字符串来解析地址和记录电文通信，虽然是ASCII报文，仍然支持输入 \\0D\\0A 来表示 \\r\\n 等不可见字符" )]
		[DefaultValue( false )]
		[PropertyOrder( 1103 )]
		public bool UseAsciiFormate { get; set; } = false;

		/// <summary>
		/// 当前的字节转换顺序，默认是小端的顺序
		/// </summary>
		[Category( "高级配置信息" )]
		[DisplayName( "字节转换规则" )]
		[Description( "当解析数据的时候，使用的高低字节的信息" )]
		[DefaultValue( typeof( ByteTransformType ), "LittleEndianOrder" )]
		[PropertyOrder( 1104 )]
		public ByteTransformType ByteTransformType { get; set; } = ByteTransformType.LittleEndianOrder;

		/// <summary>
		/// 当前消息的规则信息，可以为无，固定长度，可变长度，指定结尾
		/// </summary>
		[Category( "高级配置信息" )]
		[DisplayName( "消息结构类型" )]
		[Description( "当接收数据报文的时候，选择怎么的规则去校验数据的完整性" )]
		[DefaultValue( typeof( MessageStructType ), "None" )]
		[PropertyOrder( 1105 )]
		public MessageStructType MessageType { get; set; } = MessageStructType.None;

		/// <summary>
		/// 固定的头字节长度信息，当消息规则信息设置为固定长度或是可变长度有效
		/// </summary>
		[Category( "高级配置信息" )]
		[DisplayName( "固定长度信息" )]
		[Description( "固定的头字节长度信息，当消息规则信息设置为固定长度或是可变长度有效" )]
		[DefaultValue( 0 )]
		[PropertyOrder( 1106 )]
		public int FixLength { get; set; } = 0;

		/// <summary>
		/// 消息的指定结束字符信息，当消息规则设置为固定字符结尾的时候有效
		/// </summary>
		[Category( "高级配置信息" )]
		[DisplayName( "指定结尾字符" )]
		[Description( "消息的指定结束字符信息，当消息规则设置为固定字符结尾的时候有效" )]
		[DefaultValue( "0D 0A" )]
		[PropertyOrder( 1107 )]
		public string EndHexString { get; set; } = "0D 0A";

		/// <summary>
		/// 消息的规则节点信息，当消息规则设置为可变长度时有效
		/// </summary>
		[Category( "高级配置信息" )]
		[DisplayName( "解析的消息规则信息" )]
		[Description( "当前消息结构规则指定为可变长度时，需要设备本对象来设置相关的长度解析信息" )]
		[TypeConverter(typeof( RegularScalarNodeConverter ) )]
		[DesignerSerializationVisibility( DesignerSerializationVisibility.Content )]
		[PropertyOrder( 1108 )]
		public RegularScalarNode MessageRegularNode { get; set; }

		///// <summary>
		///// 用于分析报文是否错误的规则信息，可以进行一定的运算，然后和 0 做比较
		///// </summary>
		//[Category( "高级配置信息" )]
		//[DisplayName( "解析的错误码规则信息" )]
		//[Description( "用于分析报文是否错误的规则信息，可以进行一定的运算，然后和 0 做比较" )]
		//[TypeConverter( typeof( RegularScalarNodeConverter ) )]
		//[DesignerSerializationVisibility( DesignerSerializationVisibility.Content )]
		//[PropertyOrder( 1108 )]
		//public RegularScalarNode ErrorRegularNode { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UseServerActivePush     = GetXmlValue( element, nameof( UseServerActivePush ),     UseServerActivePush,     bool.Parse );
			UsePersistentConnection = GetXmlValue( element, nameof( UsePersistentConnection ), UsePersistentConnection, bool.Parse );
			UseAsciiFormate         = GetXmlValue( element, nameof( UseAsciiFormate ),         UseAsciiFormate,         bool.Parse );
			ByteTransformType       = GetXmlEnum(  element, nameof( ByteTransformType ),       ByteTransformType );
			MessageType             = GetXmlEnum(  element, nameof( MessageType ),             MessageType );
			FixLength               = GetXmlValue( element, nameof( FixLength ),               FixLength, int.Parse );
			EndHexString            = GetXmlValue( element, nameof( EndHexString ),            EndHexString, m => m );

			IEnumerable<XElement> ele = element.Elements( nameof( NodeType.RegularScalarNode ) );
			if (ele != null)
			{
				foreach (var item in ele)
				{
					string name = GetXmlValue( item, nameof( Name ), "__MessageRegular", m => m );
					if (name == "__MessageRegular")
						MessageRegularNode = new RegularScalarNode( item );
					//else if (item.Name == "__ErrorRegular")
					//	ErrorRegularNode = new RegularScalarNode( item );
				}
			}
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( UseServerActivePush ),     UseServerActivePush );
			element.SetAttributeValue( nameof( UsePersistentConnection ), UsePersistentConnection );
			element.SetAttributeValue( nameof( UseAsciiFormate ),         UseAsciiFormate );
			element.SetAttributeValue( nameof( ByteTransformType ),       ByteTransformType );
			element.SetAttributeValue( nameof( MessageType ),             MessageType );
			element.SetAttributeValue( nameof( FixLength ),               FixLength );
			element.SetAttributeValue( nameof( EndHexString ),            EndHexString );
			if (MessageRegularNode != null)
				element.Add( MessageRegularNode.ToXmlElement( ) );
			//if (ErrorRegularNode != null)
			//	element.Add( ErrorRegularNode.ToXmlElement( ) );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[TcpFreedom] {Name}";

		#endregion

	}
}
