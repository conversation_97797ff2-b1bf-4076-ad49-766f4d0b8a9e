using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslCommunication.Core.Pipe;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.Reflection;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 串口的管道节点信息
	/// </summary>
	public class NodeSerialPipe : DeviceNodeSerial
	{
		/// <inheritdoc cref="GroupNode.GroupNode"/>
		public NodeSerialPipe( )
		{
			Name     = "SerialPipe";
			NodeType = NodeType.GroupSerialPipe;
		}

		/// <summary>
		/// 获取或设置是否使用为分类节点的效果，如果设置为 false，则在系统路径里，不显示，不使用管道的名称
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "当做路径使用" )]
		[Description( "获取或设置是否使用为分类节点的效果，如果设置为 false，则在系统路径里，不显示，不使用管道的名称" )]
		[DefaultValue( true )]
		[PropertyOrder( 301 )]
		public bool UseAsGroupNode { get; set; } = true;

		/// <summary>
		/// 当设备网络异常的时候，是否立即切换其他设备执行请求，默认为 true
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "设备异常是否立即切换设备" )]
		[Description( "当设备网络异常的时候，是否立即切换其他设备执行请求，默认为 true" )]
		[DefaultValue( true )]
		[PropertyOrder( 302 )]
		public bool ChangeDeviceWhenError { get; set; } = true;

		/// <summary>
		/// 当设备切换采集的时候，是否关闭当前的设备连接操作。
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "设备切换时是否关闭" )]
		[Description( "当设备切换采集的时候，是否关闭当前的设备连接操作。" )]
		[DefaultValue( false )]
		[PropertyOrder( 303 )]
		public bool CloseWhenChangeDevice { get; set; } = false;

		/// <summary>
		/// 获取设置设备之前切换的时候，线程休眠的时间，默认为0，表示不进行休眠操作
		/// </summary>
		[Category( "管道信息" )]
		[DisplayName( "线程休眠时间" )]
		[Description( "获取设置设备之间切换的时候，线程休眠的时间，默认为0，表示不进行休眠操作" )]
		[DefaultValue( 0 )]
		[PropertyOrder( 304 )]
		public int SleepTimeBetweenDevices { get; set; } = 0;

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSerialPipe( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 根据当前的串口管道的配置，创建真实的串口管道对象，方便真实的设备进行使用管道。<br />
		/// According to the current serial port pipeline configuration, a real serial port pipeline object is created to facilitate the use of pipelines by real devices.
		/// </summary>
		/// <returns>串口的管道对象</returns>
		public EdgePipeSerial CreatePipeSerial( )
		{
			PipeSerial pipeSerial = new PipeSerial( );
			pipeSerial.SerialPortInni( PortName, BaudRate, DataBits, StopBits, Parity );
			pipeSerial.RtsEnable = RtsEnable;
			return new EdgePipeSerial( this, pipeSerial );
		}

		/// <inheritdoc/>
		public override string GetSerialInfo( )
		{
			return Name + "->" + base.GetSerialInfo( );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			if (!UseAsGroupNode) element.SetAttributeValue( nameof( UseAsGroupNode ), UseAsGroupNode );
			if (ChangeDeviceWhenError == false) element.SetAttributeValue( nameof( ChangeDeviceWhenError ), ChangeDeviceWhenError );
			if (CloseWhenChangeDevice) element.SetAttributeValue( nameof( CloseWhenChangeDevice ), CloseWhenChangeDevice );
			if (SleepTimeBetweenDevices > 0) element.SetAttributeValue( nameof( SleepTimeBetweenDevices ), SleepTimeBetweenDevices );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UseAsGroupNode          = GetXmlValue( element, nameof( UseAsGroupNode ), UseAsGroupNode, bool.Parse );
			ChangeDeviceWhenError   = GetXmlValue( element, nameof( ChangeDeviceWhenError ), true, bool.Parse );
			CloseWhenChangeDevice   = GetXmlValue( element, nameof( CloseWhenChangeDevice ), false, bool.Parse );
			SleepTimeBetweenDevices = GetXmlValue( element, nameof( SleepTimeBetweenDevices ), 0, int.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeSerialPipe[{Name}]";
	}
}
