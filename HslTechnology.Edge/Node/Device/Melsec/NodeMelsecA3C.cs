using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 三菱PLC，使用A3C实现的协议通信，具体格式为格式一
	/// </summary>
	public class NodeMelsecA3C : DeviceNodeSerial
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeMelsecA3C( )
		{
			Name         = "三菱PLC-A3C";
			DeviceType   = DeviceType.MelsecA3C;
			SumCheck     = true;
			Format       = 1;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMelsecA3C( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Properties

		/// <summary>
		/// PLC站号信息
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "站号，设置范围：0-255，如果设备需要指定，请和设备方保持一致" )]
		[DefaultValue( (byte)0 )]
		public byte Station { get; set; }

		/// <summary>
		/// 获取或设置当前的A3C协议是否使用和校验
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "和校验" )]
		[Description( "获取或设置当前的A3C协议是否使用和校验" )]
		[DefaultValue( true )]
		public bool SumCheck { get; set; }

		/// <summary>
		/// 当前协议使用的格式信息，可选1，2，3，4
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "通信格式" )]
		[Description( "当前协议使用的格式信息，可选1，2，3，4" )]
		[DefaultValue( 1 )]
		[TypeConverter( typeof( A3CFormatConverter ) )]
		public int Format { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station  = GetXmlValue( element, nameof( Station ),  this.Station,  byte.Parse );
			SumCheck = GetXmlValue( element, nameof( SumCheck ), this.SumCheck, bool.Parse );
			Format   = GetXmlValue( element, nameof( Format ),   this.Format,   int.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),  this.Station );
			element.SetAttributeValue( nameof( SumCheck ), this.SumCheck );
			element.SetAttributeValue( nameof( Format ),   this.Format );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Melsec.NodeMelsecHelper.GetMcAddress( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecA3C] {Name}";
	}
}
