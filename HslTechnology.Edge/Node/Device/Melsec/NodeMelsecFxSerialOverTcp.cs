using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 三菱编程口的网口透传对象
	/// </summary>
	public class NodeMelsecFxSerialOverTcp : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeMelsecFxSerialOverTcp( )
		{
			Name         = "三菱PLC-FxSerial-OverTcp";
			DeviceType   = DeviceType.MelsecFxSerialOverTcp;
			IsVersionNew = true;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMelsecFxSerialOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Properties

		/// <summary>
		/// 设备的版本是否是最新的，新版是字节对齐的
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "是否新版协议" )]
		[Description( "新版协议的读取报文是稍微调整过的，对有些系列有用，有些没用，具体需要测试" )]
		[DefaultValue( true )]
		public bool IsVersionNew { get; set; }

		/// <summary>
		/// 获取或指示当前的设备在启动时进行激活操作。
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "激活操作" )]
		[Description( "获取或指示当前的设备在启动时进行激活操作，默认会尝试3次激活" )]
		[DefaultValue( false )]
		public bool IsActiveOnStart { get; set; }

		/// <summary>
		/// 获取或设置是否使用GOP透传的方式来连接PLC，注意该连接是独占式，独占触摸屏的连接端口。
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "使用GOT" )]
		[Description( "获取或设置是否使用GOP透传的方式来连接PLC，注意该连接是独占式，独占触摸屏的连接端口。" )]
		[DefaultValue( false )]
		public bool UseGotConnect { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			IsVersionNew    = GetXmlValue( element, nameof( IsVersionNew ),    this.IsVersionNew,    bool.Parse );
			IsActiveOnStart = GetXmlValue( element, nameof( IsActiveOnStart ), this.IsActiveOnStart, bool.Parse );
			UseGotConnect   = GetXmlValue( element, nameof( UseGotConnect ),   this.UseGotConnect,   bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( IsVersionNew ),    this.IsVersionNew.ToString( ) );
			element.SetAttributeValue( nameof( IsActiveOnStart ), this.IsActiveOnStart.ToString( ) );
			element.SetAttributeValue( nameof( UseGotConnect ),   this.UseGotConnect.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Melsec.NodeMelsecHelper.GetMcAddress( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeMelsecFxSerialOverTcp] {Name}";
	}
}
