using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 三菱编程口的PLC设备
	/// </summary>
	public class NodeMelsecFxSerial : DeviceNodeSerial
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeMelsecFxSerial( )
		{
			Name         = "三菱PLC-FxSerial";
			DeviceType   = DeviceType.MelsecFxSerial;
			IsVersionNew = true;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMelsecFxSerial( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Properties

		/// <summary>
		/// 设备的版本是否是最新的，新版是字节对齐的
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "是否新版协议" )]
		[Description( "新版协议的读取报文是稍微调整过的，对有些系列有用，有些没用，具体需要测试" )]
		[DefaultValue( true )]
		public bool IsVersionNew { get; set; }

		/// <summary>
		/// 获取或指示当前的设备在启动时进行激活操作。
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "激活操作" )]
		[Description( "获取或指示当前的设备在启动时进行激活操作，默认会尝试3次激活" )]
		[DefaultValue( false )]
		public bool IsActiveOnStart { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			IsVersionNew    = GetXmlValue( element, nameof( IsVersionNew ),    this.IsVersionNew,    bool.Parse );
			IsActiveOnStart = GetXmlValue( element, nameof( IsActiveOnStart ), this.IsActiveOnStart, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( IsVersionNew ),    this.IsVersionNew.ToString( ) );
			element.SetAttributeValue( nameof( IsActiveOnStart ), this.IsActiveOnStart.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Melsec.NodeMelsecHelper.GetMcAddress( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecFxSerial] {Name}";
	}
}
