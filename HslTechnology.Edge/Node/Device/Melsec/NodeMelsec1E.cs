using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Device;
using HslCommunication;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 三菱的PLC，支持Qna兼容1E帧的协议机制
	/// </summary>
	public class NodeMelsec1E : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeMelsec1E( )
		{
			Name        = "三菱PLC-A1E";
			DeviceType  = DeviceType.MelsecMcQna1E;

			Port        = 6000;
			IsBinary    = true;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMelsec1E( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// PLC编号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "站号" )]
		[Description( "PLC的站号信息，通常都是 0" )]
		[DefaultValue( (byte)255 )]
		public byte PLCNumber { get; set; } = 0xFF;

		/// <summary>
		/// 是否是二进制通讯
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "是否二进制" )]
		[Description( "报文格式，通常二进制和ASCII，具体取决于PLC的网络参数配置。默认二进制" )]
		[DefaultValue( true )]
		public bool IsBinary { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			PLCNumber              = byte.Parse( element.Attribute( nameof( PLCNumber ) ).Value );
			IsBinary               = bool.Parse( element.Attribute( nameof( IsBinary ) ).Value );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( PLCNumber ), PLCNumber );
			element.SetAttributeValue( nameof( IsBinary ),  IsBinary.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Melsec.NodeMelsecHelper.GetMcAddress( );
		}

		/// <inheritdoc/>
		public override OperateResult CheckBatchRequest( string address )
		{
			if (System.Text.RegularExpressions.Regex.IsMatch( address, "[XYM][0-9]+$", System.Text.RegularExpressions.RegexOptions.IgnoreCase ))
			{
				string digtal = address.Substring( 1 );
				int add = 0;
				if (address.StartsWith( "M", StringComparison.OrdinalIgnoreCase ))
					add = Convert.ToInt32( digtal );
				else
					add = Convert.ToInt32( digtal, digtal.StartsWith( "0" ) ? 8 : 16 );
				if (add % 16 != 0) return new OperateResult( "位地址的时候，起始软元件的地址必须为16的倍数，例如 M16,M32,M48,参考A1E的协议手册" );
			}
			return base.CheckBatchRequest( address );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[Melsec1E] {Name}";

		#endregion
	}
}
