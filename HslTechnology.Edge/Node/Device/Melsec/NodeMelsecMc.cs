using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 实例化一个三菱MC协议的节点对象的设备
	/// </summary>
	public class NodeMelsecMc : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeMelsecMc( )
		{
			Name            = "三菱PLC-MC";
			DeviceType      = DeviceType.MelsecMcQna3E;
			Port            = 6000;
			IsBinary        = true;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMelsecMc( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 网络号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "网络号" )]
		[Description( "设备的网络号，详细参照PLC的网络配置，通常都为0" )]
		[DefaultValue( (byte)0 )]
		public byte NetworkNumber { get; set; } = 0x00;

		/// <summary>
		/// 网络站号
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "网络站号" )]
		[Description( "设备的网络站号，详细参照PLC的网络配置，通常都为0" )]
		[DefaultValue( (byte)0 )]
		public byte NetworkStationNumber { get; set; } = 0x00;

		/// <summary>
		/// 是否是二进制通讯
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "是否二进制" )]
		[Description( "报文格式，通常二进制和ASCII，具体取决于PLC的网络参数配置。默认二进制" )]
		[DefaultValue( true )]
		public bool IsBinary { get; set; }

		/// <summary>
		/// 获取或设置是否允许写入位到字寄存器
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "强制写位到字" )]
		[Description( "获取或设置是否允许写入位到字寄存器" )]
		[DefaultValue( false )]
		public bool EnableWriteBitToWord { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			NetworkNumber              = GetXmlValue( element, nameof( NetworkNumber ),        NetworkNumber,        byte.Parse );
			NetworkStationNumber       = GetXmlValue( element, nameof( NetworkStationNumber ), NetworkStationNumber, byte.Parse );
			IsBinary                   = GetXmlValue( element, nameof( IsBinary ),             IsBinary,             bool.Parse );
			EnableWriteBitToWord       = GetXmlValue( element, nameof( EnableWriteBitToWord ), EnableWriteBitToWord, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( NetworkNumber ),         NetworkNumber );
			element.SetAttributeValue( nameof( NetworkStationNumber ),  NetworkStationNumber );
			element.SetAttributeValue( nameof( IsBinary ),              IsBinary.ToString( ) );
			element.SetAttributeValue( nameof( EnableWriteBitToWord ),  EnableWriteBitToWord.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Melsec.NodeMelsecHelper.GetMcAddress( );
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => "[三菱设备] " + Name;

		#endregion
	}
}
