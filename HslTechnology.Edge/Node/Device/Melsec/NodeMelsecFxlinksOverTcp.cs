using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 三菱的Fxlinks的网口透传版设备
	/// </summary>
	public class NodeMelsecFxlinksOverTcp : DeviceNodeNetDTU
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeMelsecFxlinksOverTcp( )
		{
			Name         = "三菱PLC-FxLinks-OverTcp";
			SumCheck     = true;
			DeviceType   = DeviceType.MelsecFxlinksOverTcp;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMelsecFxlinksOverTcp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Properties

		/// <inheritdoc cref="NodeMelsecFxlinks.Station"/>
		[Category( "PLC信息" )]
		[DisplayName( "站号信息" )]
		[Description( "站号，设置范围：0-255，如果设备需要指定，请和设备方保持一致" )]
		[DefaultValue( (byte)0 )]
		public byte Station { get; set; }

		/// <inheritdoc cref="NodeMelsecFxlinks.SumCheck"/>
		[Category( "PLC信息" )]
		[DisplayName( "和校验" )]
		[Description( "是否开启和校验功能，如果在PLC侧配置了相关的信息，这里就需要和PLC配置一致" )]
		[DefaultValue( true )]
		public bool SumCheck { get; set; }

		/// <summary>
		/// 报文等待时间，单位10ms，设置范围为0-15
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "报文等待时间" )]
		[Description( "报文等待时间，单位10ms，设置范围为0-15" )]
		[DefaultValue( (byte)0x00 )]
		public byte WaittingTime { get; set; }

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Station      = GetXmlValue( element, nameof( Station ),      this.Station,      byte.Parse );
			SumCheck     = GetXmlValue( element, nameof( SumCheck ),     this.SumCheck,     bool.Parse );
			WaittingTime = GetXmlValue( element, nameof( WaittingTime ), this.WaittingTime, byte.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),      this.Station );
			element.SetAttributeValue( nameof( SumCheck ),     this.SumCheck.ToString( ) );
			element.SetAttributeValue( nameof( WaittingTime ), this.WaittingTime );
			return element;
		}

		/// <inheritdoc/>
		public override int GetEveryAddressOccupyByte( string address ) => 2;

		/// <inheritdoc/>
		public override DeviceAddressExample[] GetDeviceAddressExamples( )
		{
			return Melsec.NodeMelsecHelper.GetMcAddress( );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecFxlinksOverTcp] {Name}";
	}
}
