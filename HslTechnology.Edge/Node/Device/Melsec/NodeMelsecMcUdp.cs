using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Device
{
	/// <summary>
	/// 三菱的MC-3E协议的UDP版本
	/// </summary>
	public class NodeMelsecMcUdp : NodeMelsecMc
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeMelsecMcUdp( ) : base( )
		{
			Name = "三菱PLC-MC-UDP";
			DeviceType = DeviceType.MelsecMcUdpQna3E;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeMelsecMcUdp( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => base.ToString( );
	}
}
