using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 支持进行报警关联的节点，当前节点自身的数据类型，长度信息，关联的报警的唯一名称
	/// </summary>
	public interface IAlarmRelateNode
	{
		/// <inheritdoc cref="GroupNode.Name"/>
		string Name { get; set; }

		/// <inheritdoc cref="Regular.RegularScalarNode.DataTypeCode"/>
		string DataTypeCode { get; set; }

		/// <inheritdoc cref="Regular.RegularScalarNode.Length"/>
		int Length { get; set; }

		/// <inheritdoc cref="Regular.RegularScalarNode.AlarmRelate"/>
		string AlarmRelate { get; set; }

		/// <inheritdoc cref="Regular.RegularScalarNode.IsBoolRegular"/>
		bool IsBoolRegular( );
	}
}
