using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Converters;
using System.Reflection;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node.Request;
using HslCommunication;
using HslCommunication.MQTT;
using HslTechnology.Edge.Reflection;
using System.Text.RegularExpressions;

namespace HslTechnology.Edge.Node.Render
{
	/// <summary>
	/// 标量数据节点信息
	/// </summary>
	public class ScalarDataNode
	{
		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public ScalarDataNode( )
		{
			DataType = DataType.Int16;
		}

		/// <summary>
		/// 节点的名称，在节点上显示的<br />
		/// The name of the node, as shown on the node
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// 当前节点的描述信息<br />
		/// Description of the current node
		/// </summary>
		public string Description { get; set; }

		/// <summary>
		/// 当前的数据节点显示的名称，如果为空，则显示 <see cref="GroupNode.Name"/> 属性名称信息
		/// </summary>
		public string DisplayName { get; set; }

		/// <summary>
		/// 当前节点的单位信息
		/// </summary>
		public string Unit { get; set; }

		/// <summary>
		/// 数据类型信息
		/// </summary>
		[JsonConverter( typeof( StringEnumConverter ) )]
		public DataType DataType { get; set; }

		/// <summary>
		/// 数据的维度信息，指示标量，还是一维数组，二维数组
		/// </summary>
		[JsonConverter( typeof( StringEnumConverter ) )]
		public DataDimension DataDimension { get; set; }

		/// <summary>
		/// 数据的访问等级，只读，还是可读写
		/// </summary>
		[JsonConverter( typeof( StringEnumConverter ) )]
		public AccessLevel AccessLevel { get; set; }

		/// <summary>
		/// 如果是结构体的数据信息，则表示结构体的名称信息
		/// </summary>
		public string StructName { get; set; }

		/// <summary>
		/// 结构体的长度信息
		/// </summary>
		public int ArrayLength { get; set; }

		/// <summary>
		/// 获取或设置数据的路径信息，在客户端用于信息缓存，再服务器端没有什么作用
		/// </summary>
		[JsonIgnore]
		public string DataUrl { get; set; }

		/// <summary>
		/// 如果是结构体的话，是结构体的子结构的定义
		/// </summary>
		public ScalarDataNode[] StructNodes { get; set; }

		/// <summary>
		/// 根据实际的数值进行额外的变量转换
		/// </summary>
		[JsonIgnore]
		public Func<JToken, string> TransDispalyFunction { get; set; }

		/// <summary>
		/// 克隆当前的显示的数据节点的副本
		/// </summary>
		/// <returns>数据节点信息</returns>
		public ScalarDataNode Clone( )
		{
			return new ScalarDataNode( )
			{
				AccessLevel          = this.AccessLevel,
				ArrayLength          = this.ArrayLength,
				DataDimension        = this.DataDimension,
				DataType             = this.DataType,
				Description          = this.Description,
				DisplayName          = this.DisplayName,
				Unit                 = this.Unit,
				Name                 = this.Name,
				StructName           = this.StructName,
				StructNodes          = this.StructNodes,
				TransDispalyFunction = this.TransDispalyFunction,
			};
		}

		/// <summary>
		/// 根据数据的维度信息，获取数据的类型的字符串描述
		/// </summary>
		/// <returns>类型的字符串描述</returns>
		public string GetDataTypeText( )
		{
			if (DataDimension == DataDimension.Scalar) return DataType.ToString( );
			else if (DataDimension == DataDimension.One) return DataType.ToString( ) + "[]";
			else return DataType.ToString( ) + "[,]";
		}

		/// <summary>
		/// 获取当前用于显示的名称信息，根据 <see cref="DisplayName"/> 及 <see cref="GroupNode.Name"/> 两个属性共同决定
		/// </summary>
		/// <returns>显示名称</returns>
		public string GetDisplayName( )
		{
			return string.IsNullOrEmpty( DisplayName ) ? Name : DisplayName;
		}

		/// <summary>
		/// 检查输入的文本信息对当前的节点定义是否合法。
		/// </summary>
		/// <param name="input">字符串输入信息</param>
		public void CheckInputStringLegal(string input )
		{
			if (DataDimension == DataDimension.Scalar)
			{
				if      (DataType == DataType.Bool)     bool.Parse( input );
				else if (DataType == DataType.Byte)     byte.Parse( input );
				else if (DataType == DataType.SByte)    sbyte.Parse( input );
				else if (DataType == DataType.Int16)    short.Parse( input );
				else if (DataType == DataType.UInt16)   ushort.Parse( input );
				else if (DataType == DataType.Int32)    int.Parse( input );
				else if (DataType == DataType.UInt32)   uint.Parse( input );
				else if (DataType == DataType.Int64)    long.Parse( input );
				else if (DataType == DataType.UInt64)   ulong.Parse( input );
				else if (DataType == DataType.Float)    float.Parse( input );
				else if (DataType == DataType.Double)   double.Parse( input );
				else if (DataType == DataType.DateTime) DateTime.Parse( input );
				else if (DataType == DataType.String) return;
				else if (DataType == DataType.BCD)
				{
					if(!Regex.IsMatch(input, "^[0-9]+$"))
						throw new Exception( "BCD type must only contains [0-9]." );
				}
				else if (DataType == DataType.Guid) Guid.Parse( input );
				else throw new Exception( "Not supported data type." );
			}
			else if (DataDimension == DataDimension.One)
			{
				if      (DataType == DataType.Bool)     input.ToStringArray<bool>( );
				else if (DataType == DataType.Byte)     
				{
					if (Regex.IsMatch( input, @"^[0-9a-fA-F ]+$" ))
					{
						input.ToHexBytes( );
					}
					else
					{
						input.ToStringArray<byte>( );
					}
				}
				else if (DataType == DataType.SByte)    input.ToStringArray<sbyte>( );
				else if (DataType == DataType.Int16)    input.ToStringArray<short>( );
				else if (DataType == DataType.UInt16)   input.ToStringArray<ushort>( );
				else if (DataType == DataType.Int32)    input.ToStringArray<int>( );
				else if (DataType == DataType.UInt32)   input.ToStringArray<uint>( );
				else if (DataType == DataType.Int64)    input.ToStringArray<long>( );
				else if (DataType == DataType.UInt64)   input.ToStringArray<ulong>( );
				else if (DataType == DataType.Float)    input.ToStringArray<float>( );
				else if (DataType == DataType.Double)   input.ToStringArray<double>( );
				else if (DataType == DataType.DateTime) input.ToStringArray<DateTime>( );
				else if (DataType == DataType.String)   JArray.Parse( input );
				else if (DataType == DataType.Guid)     input.ToStringArray( Guid.Parse );
				else if (DataType == DataType.BCD)      JArray.Parse( input );
				else throw new Exception( "Not supported data type." );
			}
			else
				throw new Exception( "Not supported two Dimension data type." );
		}

		/// <summary>
		/// 从自定义的类型来构建相关的数据节点信息
		/// </summary>
		/// <param name="type">类型信息</param>
		/// <returns>数据节点数组</returns>
		public static ScalarDataNode[] ParseFromType( Type type )
		{
			List<ScalarDataNode> list = new List<ScalarDataNode>( );

			foreach (PropertyInfo property in type.GetProperties( ))
			{
				ScalarDataNode node = new ScalarDataNode( );
				node.Name = property.Name;
				if      (property.PropertyType == typeof( bool ))   { node.DataType = DataType.Bool; node.DataDimension = DataDimension.Scalar; }
				else if (property.PropertyType == typeof( bool[] )) { node.DataType = DataType.Bool; node.DataDimension = DataDimension.One; }
				else if (property.PropertyType == typeof( byte ))   { node.DataType = DataType.Byte; node.DataDimension = DataDimension.Scalar; }

			}
			return list.ToArray( );
		}

		/// <summary>
		/// 根据结构体对象解析出相关的数据节点信息
		/// </summary>
		/// <param name="structNode">结构体节点信息</param>
		/// <returns>数据节点信息</returns>
		public static ScalarDataNode ParseFrom( RegularStructNode structNode )
		{
			ScalarDataNode dataNode = new ScalarDataNode( );
			dataNode.Name           = structNode.Name;
			dataNode.Description    = structNode.Description;
			dataNode.AccessLevel    = AccessLevel.Read;
			dataNode.DataType       = DataType.Struct;
			dataNode.DataDimension  = structNode.ArrayLength < 0 ? DataDimension.Scalar : DataDimension.One;
			dataNode.ArrayLength    = structNode.ArrayLength;
			dataNode.StructName     = structNode.StructName;
			return dataNode;
		}

		/// <summary>
		/// 标量的节点数据信息，从方法请求进行加载
		/// </summary>
		/// <param name="callMethodRequest">方法请求的信息</param>
		/// <param name="rpcExtension">方法接口的额外信息</param>
		/// <returns>数据节点信息，方便客户端进行显示的操作</returns>
		public static ScalarDataNode ParseFrom( CallMethodRequest callMethodRequest, RpcExtensionInfoAttribute rpcExtension )
		{
			ScalarDataNode dataNode = new ScalarDataNode( );
			dataNode.Name           = callMethodRequest.Name;
			dataNode.DisplayName    = callMethodRequest.DisplayName;
			dataNode.Description    = callMethodRequest.Description;
			dataNode.Unit           = callMethodRequest.StoreResultUnit;
			dataNode.AccessLevel    = AccessLevel.Read;
			if ( rpcExtension == null)
			{
				// 普通的方法节点信息
				dataNode.Name = callMethodRequest.Address;
				dataNode.DataType = DataType.Method;
				dataNode.DataDimension = DataDimension.Scalar;
			}
			else
			{
				if(rpcExtension.DataType == DataType.Method)
				{
					dataNode.Name          = callMethodRequest.Address;
					dataNode.DataType      = DataType.Method;
					dataNode.DataDimension = DataDimension.Scalar;
				}
				else
				{
					dataNode.DataType      = rpcExtension.DataType;
					dataNode.DataDimension = rpcExtension.DataDimension;
				}
			}
			return dataNode;
		}

		/// <summary>
		/// 将当前的节点信息转换为可视化的数据节点信息
		/// </summary>
		/// <param name="scalarTransform">变换的对象信息</param>
		/// <param name="access">访问的等级信息</param>
		/// <returns>数据节点信息</returns>
		public static ScalarDataNode ParseFrom( IScalarTransform scalarTransform, AccessLevel access )
		{
			ScalarDataNode dataNode  = new ScalarDataNode( );
			dataNode.Name            = scalarTransform.Name;
			dataNode.DisplayName     = scalarTransform.DisplayName;
			dataNode.Description     = scalarTransform.Description;
			dataNode.AccessLevel     = access;
			if(scalarTransform is ScalarReadRequest scalarReadRequest)
			{
				if (scalarReadRequest.ForbidRemoteWrite) dataNode.AccessLevel = AccessLevel.Read;
				dataNode.Unit = scalarReadRequest.Unit;
			}
			else if (scalarTransform is RegularScalarNode scalarNode)
			{
				if (scalarNode.ForbidRemoteWrite) dataNode.AccessLevel = AccessLevel.Read;
				dataNode.Unit = scalarNode.Unit;
			}

			if      (scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text)           dataNode.DataType = DataType.Bool;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)     dataNode.DataType = DataType.Bool;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text)           dataNode.DataType = DataType.Byte;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text)          dataNode.DataType = DataType.SByte;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Int16.Text)          dataNode.DataType = DataType.Int16;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.UInt16.Text)         dataNode.DataType = DataType.UInt16;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Int32.Text)          dataNode.DataType = DataType.Int32;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.UInt32.Text)         dataNode.DataType = DataType.UInt32;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Int64.Text)          dataNode.DataType = DataType.Int64;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.UInt64.Text)         dataNode.DataType = DataType.UInt64;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Float.Text)          dataNode.DataType = DataType.Float;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.Double.Text)         dataNode.DataType = DataType.Double;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.String.Text)         dataNode.DataType = DataType.String;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)    dataNode.DataType = DataType.Int64;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text) dataNode.DataType = DataType.Double;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.BCD.Text)            dataNode.DataType = DataType.BCD;
			else if (scalarTransform.DataTypeCode == RegularNodeTypeItem.StringJson.Text)     dataNode.DataType = DataType.String;
			if (scalarTransform.Length < 0) dataNode.DataDimension = DataDimension.Scalar;
			else dataNode.DataDimension = DataDimension.One;

			if( scalarTransform.DataTypeCode == RegularNodeTypeItem.Bool.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Byte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.SByte.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Int16.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.UInt16.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Int32.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.UInt32.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Int64.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.UInt64.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Float.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.Double.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.BCD.Text ||
				scalarTransform.DataTypeCode == RegularNodeTypeItem.IntOfString.Text
				)
			{
				if (scalarTransform.TransformType == RegularHelper.TransformType_Value)
				{
					if (scalarTransform.TransformDecimal == 0)
						dataNode.DataType = DataType.Int64;
					else
						dataNode.DataType = DataType.Double;
				}
				else if (scalarTransform.TransformType == RegularHelper.TransformType_Express)
				{
					if (scalarTransform.TransformDecimal == 0)
						dataNode.DataType = DataType.Int64;
					else
						dataNode.DataType = DataType.Double;
					dataNode.AccessLevel = AccessLevel.Read;
				}
			}
			dataNode.ArrayLength = scalarTransform.Length;
			return dataNode;
		}

		//private static string GetTypeNameFromOperateResult( Type type )
		//{
		//	if (type.IsSubclassOf( typeof( OperateResult ) ))
		//	{
		//		if (type == typeof( OperateResult )) return "Void";
		//		PropertyInfo property = type.GetProperty( "Content" );
		//		if (property != null)
		//		{
		//			return property.PropertyType.Name;
		//		}
		//		else
		//		{
		//			StringBuilder sb = new StringBuilder( "OperateResult<" );
		//			for (int i = 1; i <= 10; i++)
		//			{
		//				if (type.GetProperty( "Content" + i.ToString( ) ) != null)
		//				{
		//					if (i != 1) sb.Append( "," );
		//					sb.Append( type.GetProperty( "Content" + i.ToString( ) ).PropertyType.Name );
		//				}
		//				else
		//				{
		//					break;
		//				}
		//			}
		//			sb.Append( ">" );
		//			return sb.ToString( );
		//		}
		//	}
		//	else 
		//	{
		//		return type.Name;
		//	}
		//}

		//private static string GetTypeNameFromType( Type type )
		//{
		//	if (type.IsSubclassOf( typeof( Task ) ))
		//	{
		//		if (type == typeof( Task )) return "Task";
		//		PropertyInfo property = type.GetProperty( "Result" );
		//		if (property == null) return "Task";
		//		return GetTypeNameFromOperateResult( property.PropertyType );
		//	}
		//	else
		//	{
		//		return GetTypeNameFromOperateResult( type );
		//	}
		//}

	}
}
