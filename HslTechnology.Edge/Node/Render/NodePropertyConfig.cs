using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using System.ComponentModel;
using HslTechnology.Edge.Node;
using HslTechnology.Edge.Node.Device;
using System.Xml.Linq;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json;

namespace HslTechnology.Edge.Node.Render
{
	/// <summary>
	/// 节点数据的配置类信息
	/// </summary>
	public class NodePropertyConfig
	{
		/// <summary>
		/// 当前变量的名称
		/// </summary>
		public string Name { get; set; }

		/// <summary>
		/// 当前变量的类别信息
		/// </summary>
		public string Category { get; set; }

		/// <summary>
		/// 当前变量的显示信息
		/// </summary>
		public string DisplayName { get; set; }

		/// <summary>
		/// 当前变量的注释内容，如果设置了Description特性的话
		/// </summary>
		public string Description { get; set; }

		/// <summary>
		/// 当前变量的数据类型
		/// </summary>
		[JsonConverter( typeof( StringEnumConverter ) )]
		public DataType DataType { get; set; }

		/// <summary>
		/// 默认的值
		/// </summary>
		public string DefaultValue { get; set; }

		/// <summary>
		/// 如果当前值是枚举的话，此处是可组合的对象的选择信息
		/// </summary>
		public string[] EnumValues { get; set; }

		/// <inheritdoc/>
		public override string ToString( ) => $"{Name}[{DisplayName}]";

		/// <summary>
		/// 深度克隆当前的数据对象信息
		/// </summary>
		/// <returns>属性节点对象</returns>
		public NodePropertyConfig DeepClone( )
		{
			return new NodePropertyConfig( )
			{
				Category = this.Category,
				DataType = this.DataType,
				DefaultValue = this.DefaultValue,
				Description = this.Description,
				DisplayName = this.DisplayName,
				EnumValues = this.EnumValues,
				Name = this.Name
			};
		}

		/// <summary>
		/// 深度克隆当前的数组对象信息
		/// </summary>
		/// <param name="nodeProperties">属性节点内容</param>
		/// <returns>属性节点</returns>
		public static NodePropertyConfig[] DeepCloneArray( NodePropertyConfig[] nodeProperties )
		{
			if (nodeProperties == null) return null;
			if (nodeProperties.Length == 0) return new NodePropertyConfig[0];

			NodePropertyConfig[] array = new NodePropertyConfig[nodeProperties.Length];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = nodeProperties[i].DeepClone( );
			}
			return array;
		}

		public static NodePropertyConfig CreateFromProperty( PropertyInfo property, object node )
		{
			NodePropertyConfig propertyConfig = new NodePropertyConfig( );
			propertyConfig.Name = property.Name;

			foreach (Attribute attribute in property.GetCustomAttributes( ))
			{
				if (attribute is CategoryAttribute category)
				{
					propertyConfig.Category = category.Category;
				}
				else if (attribute is DisplayNameAttribute display)
				{
					propertyConfig.DisplayName = display.DisplayName;
				}
				else if (attribute is DescriptionAttribute description)
				{
					propertyConfig.Description = description.Description;
				}
				else if (attribute is BrowsableAttribute browsable)
				{
					if (!browsable.Browsable) return null;
				}
			}
			if      (property.PropertyType == typeof( bool ))     propertyConfig.DataType = DataType.Bool;
			else if (property.PropertyType == typeof( byte ))     propertyConfig.DataType = DataType.Byte;
			else if (property.PropertyType == typeof( sbyte ))    propertyConfig.DataType = DataType.SByte;
			else if (property.PropertyType == typeof( short ))    propertyConfig.DataType = DataType.Int16;
			else if (property.PropertyType == typeof( ushort ))   propertyConfig.DataType = DataType.UInt16;
			else if (property.PropertyType == typeof( int ))      propertyConfig.DataType = DataType.Int32;
			else if (property.PropertyType == typeof( uint ))     propertyConfig.DataType = DataType.UInt32;
			else if (property.PropertyType == typeof( long ))     propertyConfig.DataType = DataType.Int64;
			else if (property.PropertyType == typeof( ulong ))    propertyConfig.DataType = DataType.UInt64;
			else if (property.PropertyType == typeof( float ))    propertyConfig.DataType = DataType.Float;
			else if (property.PropertyType == typeof( double ))   propertyConfig.DataType = DataType.Double;
			else if (property.PropertyType == typeof( DateTime )) propertyConfig.DataType = DataType.DateTime;
			else if (property.PropertyType == typeof( string ))   propertyConfig.DataType = DataType.String;
			else if (property.PropertyType == typeof( Guid ))     propertyConfig.DataType = DataType.Guid;
			else if (property.PropertyType.IsSubclassOf( typeof( Enum ) ))
			{
				propertyConfig.DataType = DataType.Enum;
				Array array = Enum.GetValues( property.PropertyType );
				List<string> enums = new List<string>( );
				foreach (var item in array)
				{
					enums.Add( item.ToString( ) );
				}
				propertyConfig.EnumValues = enums.ToArray( );
			}
			else
			{
				return null;
			}

			if (node != null)
			{
				object obj = property.GetValue( node );
				if (obj != null)
				{
					if(propertyConfig.DataType == DataType.DateTime)
						propertyConfig.DefaultValue = ((DateTime)obj).ToString( "yyyy-MM-dd HH:mm:ss" );
					else
						propertyConfig.DefaultValue = obj.ToString( );

				}
			}
			return propertyConfig;
		}

		public static NodePropertyConfig[] CreateFromObject( object node, string ingoreProperty )
		{
			Type nodeType = node.GetType( );

			List<List<NodePropertyConfig>> nodeProperties = new List<List<NodePropertyConfig>>( );
			for (int i = 0; i < HslTechnologyHelper.GroupNodeCategorys.Count; i++)
			{
				nodeProperties.Add( new List<NodePropertyConfig>( ) );
			}
			// 其他的不知如何分类的节点放到一起去
			nodeProperties.Add( new List<NodePropertyConfig>( ) );
			foreach (PropertyInfo property in nodeType.GetProperties( ))
			{
				if (property.Name == nameof( GroupNode.NodeType )) continue;      // 这个跳过
				if (property.Name == nameof( DeviceNode.DeviceType )) continue;   // 设备类型跳过
				if (property.Name == nameof( DeviceNode.Tag )) continue;            // 自定义对象跳过
				if (!string.IsNullOrEmpty( ingoreProperty ))
				{
					if (property.Name == ingoreProperty) continue;
				}

				NodePropertyConfig nodeProperty = CreateFromProperty( property, node );
				if (nodeProperty != null)
				{
					if (nodeProperty.Category == null) nodeProperty.Category = "";
					if (string.IsNullOrEmpty( nodeProperty.Category ) || HslTechnologyHelper.GroupNodeCategorys.IndexOf( nodeProperty.Category ) < 0)
					{
						nodeProperties[nodeProperties.Count - 1].Add( nodeProperty );
					}
					else
					{
						nodeProperties[HslTechnologyHelper.GroupNodeCategorys.IndexOf( nodeProperty.Category )].Add( nodeProperty );
					}
				}
			}

			nodeProperties[nodeProperties.Count - 1].Sort( new Comparison<NodePropertyConfig>( ( m, n ) => m.Category.CompareTo( n.Category ) ) );
			List <NodePropertyConfig> result = new List<NodePropertyConfig>( );
			for (int i = 0; i < nodeProperties.Count; i++)
			{
				if (nodeProperties[i].Count > 0)
					result.AddRange( nodeProperties[i] );
			}
			return result.ToArray( );
		}

		public static XElement CreateXmlFromPluginsProperty( NodePropertyConfig[] nodeProperties )
		{
			XElement element = new XElement( nameof( NodeType.DeviceNode ) );
			element.SetAttributeValue( nameof( DeviceNode.DeviceType ), DeviceType.Plugins );
			foreach (NodePropertyConfig propertyConfig in nodeProperties)
			{
				if (propertyConfig.Name == nameof( GroupNode.NodeType )) continue;
				if (propertyConfig.Name == nameof( DeviceNode.DeviceType )) continue;

				if      (propertyConfig.DataType == DataType.Bool)     XmlSetAttributeValue( element, propertyConfig, bool.Parse );
				else if (propertyConfig.DataType == DataType.Byte )    XmlSetAttributeValue( element, propertyConfig, byte.Parse );
				else if (propertyConfig.DataType == DataType.SByte)    XmlSetAttributeValue( element, propertyConfig, sbyte.Parse );
				else if (propertyConfig.DataType == DataType.Int16)    XmlSetAttributeValue( element, propertyConfig, short.Parse );
				else if (propertyConfig.DataType == DataType.UInt16)   XmlSetAttributeValue( element, propertyConfig, ushort.Parse );
				else if (propertyConfig.DataType == DataType.Int32)    XmlSetAttributeValue( element, propertyConfig, int.Parse );
				else if (propertyConfig.DataType == DataType.UInt32)   XmlSetAttributeValue( element, propertyConfig, uint.Parse );
				else if (propertyConfig.DataType == DataType.Int64)    XmlSetAttributeValue( element, propertyConfig, long.Parse );
				else if (propertyConfig.DataType == DataType.UInt64)   XmlSetAttributeValue( element, propertyConfig, ulong.Parse );
				else if (propertyConfig.DataType == DataType.Float)    XmlSetAttributeValue( element, propertyConfig, float.Parse );
				else if (propertyConfig.DataType == DataType.Double)   XmlSetAttributeValue( element, propertyConfig, double.Parse );
				else if (propertyConfig.DataType == DataType.String)   element.SetAttributeValue( propertyConfig.Name, propertyConfig.DefaultValue );
				else if (propertyConfig.DataType == DataType.DateTime) element.SetAttributeValue( propertyConfig.Name, DateTime.Parse( propertyConfig.DefaultValue ).ToString( "yyyy-MM-dd HH:mm:ss" ) );
				else if (propertyConfig.DataType == DataType.Guid)     element.SetAttributeValue( propertyConfig.Name, Guid.Parse( propertyConfig.DefaultValue ).ToString( ) );
				else if (propertyConfig.DataType == DataType.Enum)     element.SetAttributeValue( propertyConfig.Name, propertyConfig.DefaultValue );
				else continue;
			}
			return element;
		}

		private static void XmlSetAttributeValue<T>( XElement element, NodePropertyConfig propertyConfig, Func<string, T> trans )
		{
			if (!string.IsNullOrEmpty( propertyConfig.DefaultValue ))
				element.SetAttributeValue( propertyConfig.Name, trans( propertyConfig.DefaultValue ).ToString( ) );
		}

		public static void SetNodePropertyConfigFromXml( NodePropertyConfig[] nodeProperties, XElement element )
		{
			foreach (NodePropertyConfig propertyConfig in nodeProperties)
			{
				XAttribute attribute = element.Attribute( propertyConfig.Name );
				if (attribute == null) propertyConfig.DefaultValue = string.Empty;
				else propertyConfig.DefaultValue = attribute.Value;
			}
		}
	}
}
