using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.ComponentModel;
using HslCommunication.BasicFramework;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using HslCommunication;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 一个用于描述基本信息的节点类，也是所有节点类对象的基类，提供了最基本的节点信息描述，只包含名称，描述，节点类型<br />
	/// A node class used to describe basic information. It is also the base class of all node class objects. 
	/// It provides the most basic node information description, including only name, description, and node type.
	/// </summary>
	[TypeConverter( typeof( PropertySorter ) )]
	public class GroupNode : IXmlConvert
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认构造方法的对象<br />
		/// Instantiate an object with a default constructor
		/// </summary>
		public GroupNode( )
		{
			NodeType = NodeType.GroupNode;
		}

		/// <summary>
		/// 通过名称及描述信息来实例化一个对象
		/// </summary>
		/// <param name="name">名称</param>
		/// <param name="decs">描述信息</param>
		public GroupNode( string name, string decs ) : this( )
		{
			this.name        = name;
			this.Description = decs;
		}

		/// <summary>
		/// 通过指定的Xml配置信息来实例化一个基本节点信息<br />
		/// Instantiate a basic node information through the specified Xml configuration information
		/// </summary>
		/// <param name="element">包含数据的xml元素</param>
		public GroupNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 节点的名称，在节点上显示的<br />
		/// The name of the node, as shown on the node
		/// </summary>
		[Category( "节点信息" )]
		[DisplayName( "节点名称" )]
		[Description( "如果节点是设备，就是设备的名称信息" )]
		[PropertyOrder( 1 )]
		public string Name { 
			get => name;
			set
			{
				OperateResult check = CheckGroupNameLegal( value );
				if (!check.IsSuccess) throw new Exception( $"Name[{value}] 异常：{check.Message}" );

				if (value != this.name)
				{
					this.name = value;
					OnNameChanged?.Invoke( this, new EventArgs( ) );
				}
				else
				{
					this.name = value;
				}
			}
		}

		/// <summary>
		/// 获取或设置在客户端的界面上显示的变量的文本名称，如果为空，则直接显示变量代号
		/// </summary>
		[Category( "节点信息" )]
		[DisplayName( "节点别名" )]
		[Description( "获取或设置在客户端的界面上显示的变量的文本名称，如果为空，则直接显示节点名词" )]
		[PropertyOrder( 2 )]
		public string DisplayName 
		{
			get => this.displayName;
			set
			{
				if (value != this.displayName)
				{
					this.displayName = value;
					OnNameChanged?.Invoke( this, new EventArgs( ) );
				}
				else
				{
					this.displayName = value;
				}
			}
		}

		/// <summary>
		/// 当前节点的描述信息<br />
		/// Description of the current node
		/// </summary>
		[Category( "节点信息" )]
		[DisplayName( "节点的描述信息" )]
		[Description( "用于注释，或是备注设备的基本信息" )]
		[PropertyOrder( 3 )]
		public string Description { get; set; }

		/// <summary>
		/// 节点的类型，用于多种不同用途的功能标记
		/// </summary>
		[Category( "节点信息" )]
		[DisplayName( "节点类型" )]
		[ReadOnly( true )]
		[Description( "标记节点的基本用途" )]
		[JsonConverter( typeof( StringEnumConverter ) )]
		[PropertyOrder( 4 )]
		public NodeType NodeType { get; protected set; }

		#endregion

		#region IXmlConvert Implement

		/// <summary>
		/// 对象从xml元素解析，初始化指定的数据
		/// </summary>
		/// <param name="element">包含节点信息的Xml元素</param>
		public virtual void LoadByXmlElement( XElement element )
		{
			Name        = GetXmlValue( element, nameof( Name ),        Name,        m => m );
			Description = GetXmlValue( element, nameof( Description ), Description, m => m );
			DisplayName = GetXmlValue( element, nameof( DisplayName ), DisplayName, m => m );
		}

		/// <summary>
		/// 对象解析为Xml元素，方便的存储
		/// </summary>
		/// <returns>包含节点信息的Xml元素</returns>
		public virtual XElement ToXmlElement( )
		{
			XElement element = new XElement( NodeType.ToString( ) );
			element.SetAttributeValue( nameof( Name ),        Name );
			if (!string.IsNullOrEmpty( Description ))
				element.SetAttributeValue( nameof( Description ), Description );
			if (!string.IsNullOrEmpty(DisplayName))
				element.SetAttributeValue( nameof( DisplayName ), DisplayName );
			return element;
		}

		#endregion

		#region Public Method

		/// <summary>
		/// 是否是设备节点，包含普通设备，机器人设备，CNC设备，服务器设备等。
		/// </summary>
		/// <returns>是否设备节点</returns>
		public bool IsDeviceNode( )
		{
			return NodeType == NodeType.DeviceNode || NodeType == NodeType.RobotNode || NodeType == NodeType.CncNode;
		}

		/// <summary>
		/// 是否属于分类节点，分类节点主要包含基础分类，管道分类，需要展开子项解析
		/// </summary>
		/// <returns>是否分类节点</returns>
		public bool IsGroup( )
		{
			return NodeType == NodeType.GroupNode || NodeType == NodeType.GroupSerialPipe || NodeType == NodeType.GroupSocketPipe || NodeType == NodeType.GroupSingleThread;
		}

		/// <summary>
		/// 获取当前的节点的显示名称信息，可以指定是否使用显示别名，否则，一律返回<see cref="Name"/>
		/// </summary>
		/// <param name="displayMode">显示模式，可以决定是否可以显示别名信息，如果别名也为空，则显示唯一名称信息</param>
		/// <returns>最终显示的文本信息</returns>
		public string GetDisplayName( NodeDisplayMode displayMode )
		{
			this.displayMode = displayMode;
			switch (displayMode)
			{
				case NodeDisplayMode.ShowName: return Name;
				case NodeDisplayMode.ShowDisplayName: return string.IsNullOrEmpty( DisplayName ) ? Name : DisplayName;
				case NodeDisplayMode.ShowCombine: return string.IsNullOrEmpty( DisplayName ) ? Name : $"{Name} [{DisplayName}]";
				default: return Name;
			}
		}

		/// <inheritdoc cref="GetDisplayName(NodeDisplayMode)"/>
		public string GetDisplayName( ) => GetDisplayName( this.displayMode );

		/// <summary>
		/// 克隆一个节点对象信息
		/// </summary>
		/// <returns>返回最原始的基类操作</returns>
		public GroupNode Clone( ) => Activator.CreateInstance( this.GetType( ), new object[] { ToXmlElement( ) } ) as GroupNode;

		#endregion

		#region Event Handle

		/// <summary>
		/// 当名称变化的时候，触发的事件
		/// </summary>
		public event EventHandler OnNameChanged;

		#endregion

		#region Private Member

		private NodeDisplayMode displayMode = NodeDisplayMode.ShowName;
		private string name = string.Empty;                      // 当前节点的名称信息
		private string displayName = string.Empty;               // 当前节点的别名信息

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"GroupNode[{Name}]";

		#endregion

		#region Static

		/// <summary>
		/// 从XElement中提取相关的属性信息，如果不存在，就返回默认值
		/// </summary>
		/// <typeparam name="T">最终的类型信息</typeparam>
		/// <param name="element">元素内容</param>
		/// <param name="name">属性的名称</param>
		/// <param name="defaultValue">默认提供的值</param>
		/// <param name="trans">转换方式</param>
		/// <returns>最终的值</returns>
		public static T GetXmlValue<T>( XElement element, string name, T defaultValue, Func<string, T> trans )
		{
			XAttribute attribute = element.Attribute( name );
			if (attribute == null) return defaultValue;

			try
			{
				return trans( attribute.Value );
			}
			catch
			{
				return defaultValue;
			}
		}

		/// <inheritdoc cref="GetXmlValue{T}(XElement, string, T, Func{string, T})"/>
		public static T GetXmlEnum<T>( XElement element, string name, T defaultValue ) where T : struct
		{
			XAttribute attribute = element.Attribute( name );
			if (attribute == null) return defaultValue;

			try
			{
				return SoftBasic.GetEnumFromString<T>( attribute.Value );
			}
			catch
			{
				return defaultValue;
			}
		}

		/// <summary>
		/// 构建一个系统的默认的基础XML配置文件信息
		/// </summary>
		/// <returns>设备配置文件信息</returns>
		public static XElement CreateDefaultXmlSettings( )
		{
			XElement xElement = new XElement( RootSettings );
			// 设备的节点信息
			List<GroupNode> nodes = GetDefaultGroupNodes( );
			for (int i = 0; i < nodes.Count; i++)
				xElement.Add( nodes[i].ToXmlElement( ) );
			return xElement;
		}

		/// <summary>
		/// 获取默认的基础的节点信息
		/// </summary>
		/// <returns>默认的节点列表</returns>
		public static List<GroupNode> GetDefaultGroupNodes( )
		{
			return new List<GroupNode>( )
			{
				new GroupNode( GroupNode.RootDevices,  "所有的设备的集合对象，用于实际的数据采集" ),
				new GroupNode( GroupNode.RootRegular,  "所有的解析规则的信息，用于对原始数据进行分析" ),
				new GroupNode( GroupNode.RootAlarm,    "所有报警信息的规则，用于分析设备的报警" ),
				new GroupNode( GroupNode.RootOEE,      "所有设备状态信息的规则，统计设备不同状态的时间" ),
				new GroupNode( GroupNode.RootTemplate, "所有的模板设备信息，从网关设备创建的设备标准模板" ),
				new GroupNode( GroupNode.RootDatabase, "所有的数据库连接信息，用来进行数据存储的" )
			};
		}

		/// <summary>
		/// 检查当前的名称输入是否成功，返回名称是否合法
		/// </summary>
		/// <param name="input">输入的新的名称</param>
		/// <returns>是否成功</returns>
		public static OperateResult CheckGroupNameLegal( string input )
		{
			if (
				input.Contains( "/" ) ||
				input.Contains( "#" ) ||
				input.Contains( "+" ))
			{
				return new OperateResult( "节点名称里不能包含 '/#+' 特殊字符，请重新输入！" );
			}
			if (string.IsNullOrEmpty( input.Trim( ) ))
			{
				return new OperateResult( "节点名称不能为空，请重新输入！" );
			}
			return OperateResult.CreateSuccessResult( );
		}

		/// <summary>
		/// 最外层根节点的名称
		/// </summary>
		public static readonly string RootSettings = "Settings";

		/// <summary>
		/// 根节点的Devices
		/// </summary>
		public static readonly string RootDevices = "Devices";
		/// <summary>
		/// 根节点的Regular
		/// </summary>
		public static readonly string RootRegular = "Regular";
		/// <summary>
		/// 根节点的Alarm
		/// </summary>
		public static readonly string RootAlarm = "Alarm";
		/// <summary>
		/// 根节点的OEE
		/// </summary>
		public static readonly string RootOEE = "OEE";
		/// <summary>
		/// 模板设备的节点信息
		/// </summary>
		public static readonly string RootTemplate = "Template";
		/// <summary>
		/// 根节点的数据库
		/// </summary>
		public static readonly string RootDatabase = "Database";

		#endregion

	}
}
