using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Database
{
	/// <summary>
	/// 数据库的节点基类，通常是基于网络的模型的数据库
	/// </summary>
	public class DatabaseNodeNet : GroupNode
	{
		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public DatabaseNodeNet( )
		{
			NodeType       = NodeType.DatabaseNode;
			ConnectTimeOut = 2000;
			IpAddress      = "***********00";
			Port           = 7000;
			DbType         = DatabaseType.SQLServer;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public DatabaseNodeNet( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 数据库的Ip地址，或是实例的名称
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "IP地址" )]
		[DefaultValue( "***********00" )]
		[Description( "数据库的Ip地址，或是实例的名称，可以是 *********** 也可以是网址 www.hslcommunication.com" )]
		[PropertyOrder( 201 )]
		public string IpAddress { get; set; }

		/// <summary>
		/// 设备的端口号信息，如果想要屏蔽端口号信息，则设置负数即可
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "端口号信息" )]
		[Description( "设备的端口号信息，如果想要屏蔽端口号信息，则设置负数即可" )]
		[PropertyOrder( 202 )]
		public int Port { get; set; }

		/// <summary>
		/// 连接超时的时间，单位毫秒
		/// </summary>
		[Category( "网络层信息" )]
		[DisplayName( "连接的超时时间" )]
		[Description( "单位毫秒，默认是2000，就是 2 秒" )]
		[DefaultValue( 2000 )]
		[PropertyOrder( 203 )]
		public int ConnectTimeOut { get; set; }

		/// <summary>
		/// 数据库的类型信息
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "数据库类型" )]
		[Description( "获取或设置数据库的类型信息" )]
		[PropertyOrder( 251 )]
		public DatabaseType DbType { get; protected set; }

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			IpAddress      = GetXmlValue( element, nameof( IpAddress ),      IpAddress,      m => m );
			Port           = GetXmlValue( element, nameof( Port ),           Port,           int.Parse );
			ConnectTimeOut = GetXmlValue( element, nameof( ConnectTimeOut ), ConnectTimeOut, int.Parse );
			DbType         = GetXmlEnum(  element, nameof( DbType ),         DbType );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( IpAddress ),      IpAddress );
			element.SetAttributeValue( nameof( Port ),           Port );
			element.SetAttributeValue( nameof( ConnectTimeOut ), ConnectTimeOut );
			element.SetAttributeValue( nameof( DbType ),         DbType );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"DatabaseNodeNet[{Name}]";

		#endregion
	}
}
