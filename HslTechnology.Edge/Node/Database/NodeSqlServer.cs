using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Database
{
	/// <summary>
	/// 微软SQL SERVER数据库的对象
	/// </summary>
	public class NodeSqlServer : DatabaseNodeNet
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode( )"/>
		public NodeSqlServer( )
		{
			Name       = "SqlServer";
			Port       = 1433;
			DbType     = DatabaseType.SQLServer;
		}

		/// <inheritdoc cref="GroupNode.GroupNode(XElement)"/>
		public NodeSqlServer( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 数据库名称
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "数据库名称" )]
		[Description( "准备需要使用的数据库的名称" )]
		[PropertyOrder( 301 )]
		public string Database { get; set; }

		/// <summary>
		/// 登陆的用户名信息
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "用户名" )]
		[Description( "登陆数据库的的用户名" )]
		[PropertyOrder( 302 )]
		public string UserName { get; set; }

		/// <summary>
		/// 登陆的密码信息
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "密码" )]
		[Description( "登陆数据库的的密码" )]
		[PropertyOrder( 303 )]
		public string Password { get; set; }

		/// <summary>
		/// 获取或设置是否启动多个活动的结果集
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "MultipleActiveResultSets" )]
		[Description( "获取或设置是否启动多个活动的结果集(MARS)，默认false，某些情况下需要配置为 true" )]
		[PropertyOrder( 304 )]
		[DefaultValue( false )]
		public bool MultipleActiveResultSets { get; set; } = false;

		#endregion

		#region Xml Interface

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			UserName = GetXmlValue( element, nameof( UserName ), UserName, m => m );
			string password = GetXmlValue( element, nameof( Password ), string.Empty, m => m );
			if (!string.IsNullOrEmpty( password ))
				Password = HslCommunication.BasicFramework.SoftSecurity.MD5Decrypt( password, naiwdahs );
			Database = GetXmlValue( element, nameof( Database ), Database, m => m );
			MultipleActiveResultSets = GetXmlValue( element, nameof( MultipleActiveResultSets ), MultipleActiveResultSets, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( UserName ), UserName );
			if (!string.IsNullOrEmpty( Password ))
				element.SetAttributeValue( nameof( Password ), HslCommunication.BasicFramework.SoftSecurity.MD5Encrypt( Password, naiwdahs ) );
			element.SetAttributeValue( nameof( Database ), Database );
			element.SetAttributeValue( nameof( MultipleActiveResultSets ), MultipleActiveResultSets );
			return element;
		}

		#endregion

		#region Object Override

		public override string ToString( ) => $"NodeSqlServer[{IpAddress}]";
		private const string naiwdahs = "Xa2jAc81";

		#endregion
	}
}
