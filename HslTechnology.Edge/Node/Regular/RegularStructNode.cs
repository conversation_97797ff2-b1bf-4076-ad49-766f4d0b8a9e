using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;
using HslTechnology.Edge.Node.Core;

namespace HslTechnology.Edge.Node.Regular
{
	/// <summary>
	/// 原始字节的结构体规则节点，
	/// </summary>
	public class RegularStructNode : GroupNode
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的解析对象
		/// </summary>
		public RegularStructNode( )
		{
			this.StructName   = "";
			this.NodeType     = NodeType.RegularStructNode;
			this.ArrayLength  = -1;
		}

		/// <summary>
		/// 根据XML配置文件来实例化一个对象
		/// </summary>
		/// <param name="element">配置文件</param>
		public RegularStructNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 通过指定一系列的节点属性来实例化一个结构体对象本身
		/// </summary>
		/// <param name="name">节点的名称</param>
		/// <param name="description">节点的描述信息</param>
		/// <param name="structName">结构体的名称，优先在本地资源查找，然后再去网关的全局资源查找</param>
		/// <param name="structIndex">结构体所在字节数组的起始索引位置</param>
		/// <param name="arrayLength">结构体的数组长度，如果是-1，则表示单个结构体，否则表示结构体的数组长度</param>
		/// <param name="parseType">结构体的数据是否展开的类型</param>
		public RegularStructNode( string name, string description, string structName, int structIndex, int arrayLength, ParseType parseType ) : this( )
		{
			this.Name            = name;
			this.Description     = description;
			this.StructName      = structName;
			this.StructIndex     = structIndex;
			this.ArrayLength     = arrayLength;
			this.StructParseType = parseType;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 绑定的结构体数据对象的名称
		/// </summary>
		[Category( "规则解析" )]
		[DisplayName( "结构体名称" )]
		[Description( "绑定的结构体数据对象的名称，如果结构体不存在，则跳过相关的数据解析" )]
		[DefaultValue("")]
		public string StructName { get; set; }

		/// <summary>
		/// 设置或获取解析当前结构体的起始字节索引位置，按照字节为单位，从原始的报文数据里进行解析
		/// </summary>
		[Category( "规则解析" )]
		[DisplayName( "字节索引位置" )]
		[Description( "设置或获取解析当前结构体的起始字节索引位置，按照字节为单位，从原始的报文数据里进行解析" )]
		[DefaultValue( 0 )]
		public int StructIndex { get; set; }

		/// <summary>
		/// 获取或设置当前的结构体解析对象的数组长度，小于0表示标量，大于0表示数组长度信息
		/// </summary>
		[Category( "规则解析" )]
		[DisplayName( "结构体数组长度" )]
		[Description( "获取或设置当前的结构体解析对象的数组长度，小于0表示标量，大于0表示数组长度信息" )]
		[DefaultValue( -1 )]
		public int ArrayLength { get; set; }

		/// <summary>
		/// 获取或设置当前的结构解析方式，是否按照一个大JSON包，还是按照结构名+属性名，还是单纯的保留属性名称
		/// </summary>
		[Category( "规则解析" )]
		[DisplayName( "结构体解析方式" )]
		[Description( "当前的结构解析方式，是否按照一个大JSON包(Struct)，还是按照结构名+属性名()，还是单纯的保留属性名称" )]
		[DefaultValue( typeof( ParseType ), "Struct" )]
		public ParseType StructParseType { get; set; } = ParseType.Struct;

		#endregion

		#region Runtime Properties

		/// <summary>
		/// 当前结构体绑定的解析标量列表，此属性为运行时数据，值根据结构体代号从字典中查找到的。
		/// </summary>
		[Browsable(false)]
		public RegularStructItemNode RegularStructItem { get; set; }

		/// <summary>
		/// 获取当前结构体解析规则的节点所占用的字节数量
		/// </summary>
		/// <returns>长度</returns>
		public int GetLengthByte( )
		{
			if (RegularStructItem == null) return 0;

			int actualLength = ArrayLength < 0 ? 1 : ArrayLength;
			return actualLength * RegularStructItem.StructLength;
		}

		/// <summary>
		/// 获取当前的结构体占用的最大的字节索引
		/// </summary>
		/// <returns>最大的字节索引</returns>
		public int GetMaxEndIndex( ) => GetLengthByte( ) + StructIndex;

		/// <summary>
		/// 获取当前显示的变量的名字，如果是数组的，则显示数组的个数
		/// </summary>
		/// <param name="arrayIndex">数组的第几个结构体信息</param>
		/// <returns>显示的文本信息</returns>
		public string GetSourceDisplayName( int arrayIndex )
		{
			if (ArrayLength < 0) return GetDisplayName( NodeDisplayMode.ShowDisplayName );
			return $"{GetDisplayName(NodeDisplayMode.ShowDisplayName )} [{arrayIndex}]";
		}

		/// <summary>
		/// 获取当前标量解析的节点名称信息，这个名称可能是配置的节点名，也可能是 对象名+节点名
		/// </summary>
		/// <param name="scalarNode">标量解析节点</param>
		/// <param name="structIndex">数据的索引信息</param>
		/// <returns>节点名称</returns>
		public string GetTagName( RegularScalarNode scalarNode, int structIndex = -1 )
		{
			if (StructParseType == ParseType.Unflod) return scalarNode.Name;
			if ( ArrayLength < 0 || structIndex < 0)
			{
				return this.Name + "." + scalarNode.Name;
			}
			else
			{
				return this.Name + $"[{structIndex}]." + scalarNode.Name;
			}
		}

		#endregion

		#region Override XmlConvert

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			StructName     = GetXmlValue( element, nameof( StructName ),   StructName,     m => m );
			StructIndex    = GetXmlValue( element, nameof( StructIndex ),  StructIndex,    int.Parse );
			ArrayLength    = GetXmlValue( element, nameof( ArrayLength ),  ArrayLength,    int.Parse );

			if (element.Attribute( nameof( StructParseType ) ) != null)
			{
				StructParseType = GetXmlEnum( element, nameof( StructParseType ), this.StructParseType );
			}
			else if (element.Attribute( "Unflod" ) != null)
			{
				// 此处为了兼容旧版本的 bool Unflod 特性写的代码
				bool unfold = GetXmlValue( element, "Unflod", false, bool.Parse );
				if (unfold) StructParseType = ParseType.Unflod;
				else StructParseType = ParseType.Struct;
			}
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( StructName ),      StructName );
			element.SetAttributeValue( nameof( StructIndex ),     StructIndex );
			element.SetAttributeValue( nameof( ArrayLength ),     ArrayLength );
			element.SetAttributeValue( nameof( StructParseType ), StructParseType );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"[RegularStructNode] {Name} [Struct] {StructName}";

		#endregion

	}
}
