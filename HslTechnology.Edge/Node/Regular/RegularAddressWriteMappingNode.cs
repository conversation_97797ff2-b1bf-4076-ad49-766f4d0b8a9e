using HslTechnology.Edge.Device.PLCDevice;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Regular
{
	/// <summary>
	/// 在使用虚拟服务器时，可以创建写入地址映射到另一个设备中去
	/// </summary>
	public class RegularAddressWriteMappingNode : GroupNode
	{

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public RegularAddressWriteMappingNode( )
		{
			Name = "AddressWriteMapping";
			NodeType = NodeType.RegularWriteMapping;
		}

		/// <summary>
		/// 通过指定的XML配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public RegularAddressWriteMappingNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 映射的起始地址信息
		/// </summary>
		[Category( "规则解析信息" )]
		[DisplayName( "映射的起始地址" )]
		[Description( "服务器端准备映射的起始地址信息" )]
		[DefaultValue( "0" )]
		[PropertyOrder( 201 )]
		public string StartAddress { get; set; } = "0";

		/// <summary>
		/// 映射的地址长度信息
		/// </summary>
		[Category( "规则解析信息" )]
		[DisplayName( "映射的地址长度" )]
		[Description( "服务器端准备映射的地址长度信息，按照地址为单位" )]
		[DefaultValue( 10 )]
		[PropertyOrder( 202 )]
		public int Length { get; set; } = 10;

		/// <summary>
		/// 目标设备
		/// </summary>
		[Category( "规则解析信息" )]
		[DisplayName( "目标设备信息" )]
		[Description( "目标设备的路径信息，例如 工厂一/设备一 ，需要注意的是服务器类型和目标设备类型匹配" )]
		[DefaultValue( 10 )]
		[PropertyOrder( 203 )]
		public string TargetDevice { get; set; }

		/// <summary>
		/// 目标地址
		/// </summary>
		[Category( "规则解析信息" )]
		[DisplayName( "目标设备地址" )]
		[Description( "目标设备的地址信息" )]
		[DefaultValue( 10 )]
		[PropertyOrder( 204 )]
		public string TargetAddress { get; set; }

		/// <summary>
		/// 当前的映射关系，所关联的实际的设备信息，运行时有效
		/// </summary>
		[Browsable(false)]
		public DeviceCore DeviceCore { get; set; }

		#region IXmlConvert Implement

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement(element);
			this.StartAddress  = GetXmlValue( element, nameof( StartAddress ),   StartAddress, m => m );
			this.Length        = GetXmlValue( element, nameof( Length ),         Length, int.Parse );
			this.TargetDevice  = GetXmlValue( element, nameof( TargetDevice ),   TargetDevice, m => m );
			this.TargetAddress = GetXmlValue( element, nameof( TargetAddress ),  TargetAddress, m => m );

		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( StartAddress ), StartAddress );
			element.SetAttributeValue( nameof( Length ), Length );
			element.SetAttributeValue( nameof( TargetDevice ), TargetDevice );
			element.SetAttributeValue( nameof( TargetAddress ), TargetAddress );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"RegularWriteMapping[{Name}]";

		#endregion
	}
}
