using HslTechnology.Edge.Node.Render;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Regular
{
	/// <summary>
	/// 结构体相关的节点类信息
	/// </summary>
	public class RegularStructItemNode : GroupNode
	{
		/// <summary>
		/// 实例化一个默认的解析对象
		/// </summary>
		public RegularStructItemNode( )
		{
			this.NodeType     = NodeType.RegularStructItemNode;
			this.scalarNodes  = new RegularScalarNodeCollection( );
			this.StructLength = 10;
		}

		/// <summary>
		/// 从节点的参数配置来实例化一个对象
		/// </summary>
		/// <param name="element">节点的参数配置</param>
		public RegularStructItemNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 实例化一个默认的解析对象
		/// </summary>
		public RegularStructItemNode( List<RegularScalarNode> regularScalars) : this( )
		{
			List<ScalarDataNode> dataNodes = new List<ScalarDataNode>( );
			foreach (RegularScalarNode regularItemNode in regularScalars)
			{
				RegularScalarNodes.Add( regularItemNode );
				dataNodes.Add( ScalarDataNode.ParseFrom( regularItemNode, AccessLevel.Read ) );
			}
			DataNodes = dataNodes.ToArray( );
		}

		/// <summary>
		/// 获取或设置当前的结构体解析对象的字节长度，在进行数组解析的时候派上用场，按照 <see cref="StructLength"/> 长度一个结构体进行切割
		/// </summary>
		[Category( "解析信息" )]
		[DisplayName( "结构体字节长度" )]
		[Description( "获取或设置当前的结构体解析对象的字节长度，在进行数组解析的时候派上用场，按照当前值表示的字节长度一个结构体进行切割" )]
		[DefaultValue( 10 )]
		public int StructLength { get; set; }

		/// <summary>
		/// 本次请求所有关联的单个数据解析的节点信息
		/// </summary>
		[Category( "解析信息" )]
		[DisplayName( "数据自定义解析列表" )]
		[Description( "根据数据进行解析定义规则" )]
		[DesignerSerializationVisibility( DesignerSerializationVisibility.Content )]
		[TypeConverter( typeof( CollectionConverter ) )]
		public RegularScalarNodeCollection RegularScalarNodes => scalarNodes;

		/// <summary>
		/// 运行时有效的当前的结构体绑定的数据标签集合，主要用来在客户端进行节点浏览的功能提供支持。
		/// </summary>
		[Browsable(false)]
		public ScalarDataNode[] DataNodes { get; set; }

		#region Xml Support

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			StructLength = GetXmlValue( element, nameof( StructLength ), StructLength, int.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( StructLength ), StructLength );
			return element;
		}

		#endregion

		#region Private Member

		private RegularScalarNodeCollection scalarNodes;

		#endregion

		#region Static Method

		/// <summary>
		/// 所有解析规则的词典，只在系统加载配置文件的时候初始化。调用<see cref="ParesRegular(XElement)"/>方法来完成初始化操作。
		/// </summary>
		public static Dictionary<string, RegularStructItemNode> DictionaryRegularStruct { get; set; }

		/// <summary>
		/// 解析一个配置文件中的所有的结构体规则解析，并返回一个词典信息
		/// </summary>
		/// <param name="regularStructXml">配置文件的根信息</param>
		/// <returns>词典</returns>
		public static Dictionary<string, RegularStructItemNode> ParesRegular( XElement regularStructXml )
		{
			Dictionary<string, RegularStructItemNode> regularkeyValuePairs = new Dictionary<string, RegularStructItemNode>( );
			foreach (var xmlNode in regularStructXml.Elements( ))
			{
				if (xmlNode.Attribute( nameof( Name ) ).Value == GroupNode.RootRegular)
				{
					foreach (XElement element in xmlNode.Elements( nameof( RegularStructItemNode ) ))
					{
						RegularStructItemNode structItemNode = RegularStructItemNode.PraseFromFullXml( element );
						if (regularkeyValuePairs.ContainsKey( element.Attribute( nameof( Name ) ).Value ))
							regularkeyValuePairs[element.Attribute( nameof( Name ) ).Value] = structItemNode;
						else
							regularkeyValuePairs.Add( element.Attribute( nameof( Name ) ).Value, structItemNode );
					}
					break;
				}
			}
			return regularkeyValuePairs;
		}

		/// <summary>
		/// 从一个完整的XML元素（包含子项解析）解析出完整的 <see cref="RegularStructItemNode"/> 对象信息
		/// </summary>
		/// <param name="element">元素信息</param>
		/// <returns>结构体对象</returns>
		public static RegularStructItemNode PraseFromFullXml( XElement element )
		{
			RegularStructItemNode regularStructItem = new RegularStructItemNode( element );

			List<ScalarDataNode> dataNodes = new List<ScalarDataNode>( );
			foreach (XElement regularScalarXml in element.Elements( nameof( NodeType.RegularScalarNode ) ))
			{
				RegularScalarNode regularItemNode = new RegularScalarNode( regularScalarXml );
				regularStructItem.RegularScalarNodes.Add( regularItemNode );

				dataNodes.Add( ScalarDataNode.ParseFrom( regularItemNode, AccessLevel.Read ) );
			}
			regularStructItem.DataNodes = dataNodes.ToArray( );
			return regularStructItem;
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[RegularStructItemNode] {Name}";
	}
}
