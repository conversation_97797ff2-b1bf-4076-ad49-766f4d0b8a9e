using HslTechnology.Edge.Node.Regular;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Regular
{
	/// <summary>
	/// <see cref="RegularScalarNode"/> 相关的集合对象，方便进行配置
	/// </summary>
	public class RegularScalarNodeCollection
		: Collection<RegularScalarNode>
	{

    }
}
