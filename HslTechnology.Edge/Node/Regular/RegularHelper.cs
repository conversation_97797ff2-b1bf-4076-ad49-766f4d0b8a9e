using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Node.Regular
{
	/// <summary>
	/// 一些规则节点的静态辅助方法
	/// </summary>
	public class RegularHelper
	{
		private static double GetExpValue( int count )
		{
			switch (count)
			{
				case 0: return 1;
				case 1: return 10;
				case 2: return 100;
				case 3: return 1000;
				case 4: return 10000;
				case 5: return 100000;
				case 6: return 1000000;
				default: return Math.Pow( 10, count );
			}
		}

		/// <summary>
		/// 根据<see cref="IScalarTransform"/>配置信息，对传入的 value 值进行二次加工操作。
		/// </summary>
		/// <param name="transform">变换的规则信息</param>
		/// <param name="value">等待加工的值</param>
		/// <returns>结果数据内容</returns>
		public static dynamic TransValueByScalarTransform( IScalarTransform transform, dynamic value )
		{
			if (transform.TransformType == RegularHelper.TransformType_None) return value;
			if (transform.TransformType != RegularHelper.TransformType_Value) return value;

			double tmp = value * transform.TransformMultiply + transform.TransformAddition;
			if (transform.TransformDecimal < 0) return tmp;
			switch (transform.TransformDecimalType)
			{
				case TransformDecimalType.RoundEven:           // 四舍五入操作
					if (transform.TransformDecimal == 0)
						return Convert.ToInt64( Math.Round( tmp, transform.TransformDecimal ) );
					else
						return Math.Round( tmp, transform.TransformDecimal );
				case TransformDecimalType.Floor:               // 取下限
					if (transform.TransformDecimal == 0)
						return Convert.ToInt64( Math.Floor( tmp ) );
					else
					{
						double scale = GetExpValue( transform.TransformDecimal );
						return Math.Round( Math.Floor( tmp * scale ) / scale, transform.TransformDecimal );
					}
				case TransformDecimalType.Ceiling:             // 取上限
					if (transform.TransformDecimal == 0)
						return Convert.ToInt64( Math.Ceiling( tmp ) );
					else
					{
						double scale = GetExpValue( transform.TransformDecimal );
						return Math.Round( Math.Ceiling( tmp * scale ) / scale, transform.TransformDecimal );
					}
				default: return tmp;
			}
		}

		private static bool GetBoolValue( string value )
		{
			if (value == "1") return true;
			if (value == "0") return false;
			value = value.ToUpper( );
			if (value == "TRUE") return true;
			if (value == "FALSE") return false;
			if (value == "ON") return true;
			if (value == "OFF") return false;
			return bool.Parse( value );
		}

		/// <summary>
		/// 传入字符串的数值，获取bool值信息
		/// </summary>
		/// <param name="value">传入的值</param>
		/// <returns>Bool值信息</returns>
		public static bool GetBoolValue( IScalarTransform transform, string value )
		{
			if (transform == null) return GetBoolValue( value );
			if (transform.TransformType == RegularHelper.TransformType_None) return GetBoolValue( value );
			if (transform.TransformType == RegularHelper.TransformType_Not) return !GetBoolValue( value );
			else if (transform.TransformType == RegularHelper.TransformType_Value)
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					tmp = Convert.ToDouble( (tmp - transform.TransformAddition) / transform.TransformMultiply );
				else
					tmp = Convert.ToDouble( tmp - transform.TransformAddition );

				// 预留一点点误差
				if (Math.Abs(tmp) < 0.001d)
				{
					return false;
				}
				else
				{
					return true;
				}
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 传入字符串数组，获取bool数组
		/// </summary>
		/// <param name="value">字符串信息</param>
		/// <returns>bool数组信息</returns>
		public static bool[] GetBoolArrayValue( IScalarTransform transform, string value )
		{
			return value.ToStringArray( m => GetBoolValue( transform, m ) ).ToArray( );
		}

		/// <summary>
		/// 根据数据转换规则，传入字符串的数据，转换成方法结果的数据类型
		/// </summary>
		/// <param name="transform">数据变换规则</param>
		/// <param name="value">字符串的内容</param>
		/// <returns>转换后的数据值</returns>
		public static byte GetByteValue( IScalarTransform transform, string value )
		{
			if (transform == null) return byte.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return byte.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToByte( (tmp - transform.TransformAddition) / transform.TransformMultiply );
				else
					return Convert.ToByte( tmp - transform.TransformAddition );
			}
		}

		/// <inheritdoc cref="GetSByteValue(IScalarTransform, string)"/>
		public static byte[] GetByteArrayValue( IScalarTransform transform, string value )
		{
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<byte>( );
			return value.ToStringArray<byte>( );
		}

		/// <inheritdoc cref="GetByteValue(IScalarTransform, string)"/>
		public static sbyte GetSByteValue( IScalarTransform transform, string value )
		{
			if (transform == null) return sbyte.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return sbyte.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToSByte( (tmp - transform.TransformAddition) / transform.TransformMultiply );
				else
					return Convert.ToSByte( tmp - transform.TransformAddition );
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static sbyte[] GetSByteArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<sbyte>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<sbyte>( );
			return value.ToStringArray<string>( ).Select( m => GetSByteValue( transform, m ) ).ToArray( );
		}

		/// <inheritdoc cref="GetSByteValue(IScalarTransform, string)"/>
		public static short GetInt16Value( IScalarTransform transform, string value )
		{
			if (transform == null) return short.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return short.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToInt16((tmp - transform.TransformAddition) / transform.TransformMultiply);
				else
					return Convert.ToInt16( tmp - transform.TransformAddition);
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static short[] GetInt16ArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<short>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<short>( );
			return value.ToStringArray<string>( ).Select( m => GetInt16Value( transform, m ) ).ToArray( );
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static ushort GetUInt16Value( IScalarTransform transform, string value )
		{
			if (transform == null) return ushort.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return ushort.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToUInt16( (tmp - transform.TransformAddition) / transform.TransformMultiply);
				else
					return Convert.ToUInt16( tmp - transform.TransformAddition);
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static ushort[] GetUInt16ArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<ushort>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<ushort>( );
			return value.ToStringArray<string>( ).Select( m => GetUInt16Value( transform, m ) ).ToArray( );
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static int GetInt32Value( IScalarTransform transform, string value )
		{
			if (transform == null) return int.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return int.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToInt32( (tmp - transform.TransformAddition) / transform.TransformMultiply);
				else
					return Convert.ToInt32( tmp - transform.TransformAddition);
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static int[] GetInt32ArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<int>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<int>( );
			return value.ToStringArray<string>( ).Select( m => GetInt32Value( transform, m ) ).ToArray( );
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static uint GetUInt32Value( IScalarTransform transform, string value )
		{
			if (transform == null) return uint.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return uint.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToUInt32( (tmp - transform.TransformAddition) / transform.TransformMultiply);
				else
					return Convert.ToUInt32( tmp - transform.TransformAddition);
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static uint[] GetUInt32ArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<uint>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<uint>( );
			return value.ToStringArray<string>( ).Select( m => GetUInt32Value( transform, m ) ).ToArray( );
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static long GetInt64Value( IScalarTransform transform, string value )
		{
			if (transform == null) return long.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return long.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToInt64( (tmp - transform.TransformAddition) / transform.TransformMultiply);
				else
					return Convert.ToInt64( tmp - transform.TransformAddition);
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static long[] GetInt64ArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<long>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<long>( );
			return value.ToStringArray<string>( ).Select( m => GetInt64Value( transform, m ) ).ToArray( );
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static ulong GetUInt64Value( IScalarTransform transform, string value )
		{
			if (transform == null) return ulong.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return ulong.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToUInt64( (tmp - transform.TransformAddition) / transform.TransformMultiply);
				else
					return Convert.ToUInt64( tmp - transform.TransformAddition);
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static ulong[] GetUInt64ArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<ulong>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<ulong>( );
			return value.ToStringArray<string>( ).Select( m => GetUInt64Value( transform, m ) ).ToArray( );
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static float GetFloatValue( IScalarTransform transform, string value )
		{
			if (transform == null) return float.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return float.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToSingle( (tmp - transform.TransformAddition) / transform.TransformMultiply);
				else
					return Convert.ToSingle( tmp - transform.TransformAddition);
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static float[] GetFloatArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<float>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<float>( );
			return value.ToStringArray<string>( ).Select( m => GetFloatValue( transform, m ) ).ToArray( );
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static double GetDoubleValue( IScalarTransform transform, string value )
		{
			if (transform == null) return double.Parse( value );
			if (transform.TransformType != RegularHelper.TransformType_Value) return double.Parse( value );
			else
			{
				double tmp = double.Parse( value );
				if (transform.TransformMultiply != 0)
					return Convert.ToDouble( (tmp - transform.TransformAddition) / transform.TransformMultiply);
				else
					return Convert.ToDouble( tmp - transform.TransformAddition);
			}
		}

		/// <inheritdoc cref="GetInt16Value(IScalarTransform, string)"/>
		public static double[] GetDoubleArrayValue( IScalarTransform transform, string value )
		{
			if (transform == null) return value.ToStringArray<double>( );
			if (transform.TransformType != RegularHelper.TransformType_Value) return value.ToStringArray<double>( );
			return value.ToStringArray<string>( ).Select( m => GetDoubleValue( transform, m ) ).ToArray( );
		}

		/// <summary>
		/// 根据<see cref="IScalarTransform"/>配置信息，对传入数组的 value 值进行二次加工操作。
		/// </summary>
		/// <param name="transform">变换的规则信息</param>
		/// <param name="value">等待加工的数组</param>
		/// <returns>处理后的界面</returns>
		public static dynamic TransArrayByScalarTransform( IScalarTransform transform, dynamic value )
		{
			if (transform == null) return value;
			if (transform.TransformType != RegularHelper.TransformType_Value) return value;

			if (transform.TransformDecimal < 0)
			{
				double[] array = new double[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					array[i] = value[i] * transform.TransformMultiply + transform.TransformAddition;
				}
				return array;
			}
			if (transform.TransformDecimalType == TransformDecimalType.RoundEven)
			{
				if (transform.TransformDecimal == 0)
				{
					long[] array = new long[value.Length];
					for (int i = 0; i < value.Length; i++)
					{
						double tmp = value[i] * transform.TransformMultiply + transform.TransformAddition;
						array[i] = Convert.ToInt64( Math.Round( tmp, transform.TransformDecimal ) );
					}
					return array;
				}
				else
				{
					double[] array = new double[value.Length];
					for (int i = 0; i < value.Length; i++)
					{
						array[i] = value[i] * transform.TransformMultiply + transform.TransformAddition;
						array[i] = Math.Round( array[i], transform.TransformDecimal );
					}
					return array;
				}
			}
			else if (transform.TransformDecimalType == TransformDecimalType.Floor)
			{
				if (transform.TransformDecimal == 0)
				{
					long[] array = new long[value.Length];
					for (int i = 0; i < value.Length; i++)
					{
						double tmp = value[i] * transform.TransformMultiply + transform.TransformAddition;
						array[i] = Convert.ToInt64( Math.Floor( tmp ) );
					}
					return array;
				}
				else
				{
					double scale = GetExpValue( transform.TransformDecimal );
					double[] array = new double[value.Length];
					for (int i = 0; i < value.Length; i++)
					{
						array[i] = value[i] * transform.TransformMultiply + transform.TransformAddition;
						array[i] = Math.Round( Math.Floor( array[i] * scale ) / scale, transform.TransformDecimal );
					}
					return array;
				}
			}
			else if (transform.TransformDecimalType == TransformDecimalType.Ceiling)
			{
				if (transform.TransformDecimal == 0)
				{
					long[] array = new long[value.Length];
					for (int i = 0; i < value.Length; i++)
					{
						double tmp = value[i] * transform.TransformMultiply + transform.TransformAddition;
						array[i] = Convert.ToInt64( Math.Ceiling( tmp ) );
					}
					return array;
				}
				else
				{
					double scale = GetExpValue( transform.TransformDecimal );
					double[] array = new double[value.Length];
					for (int i = 0; i < value.Length; i++)
					{
						array[i] = value[i] * transform.TransformMultiply + transform.TransformAddition;
						array[i] = Math.Round( Math.Ceiling( array[i] * scale ) / scale, transform.TransformDecimal );
					}
					return array;
				}
			}
			else
				return value;

		}

		private static object TransformDecimalDealWith( IScalarTransform transform , double tmp)
		{
			if (transform.TransformDecimal < 0) return tmp;
			switch (transform.TransformDecimalType)
			{
				case TransformDecimalType.RoundEven:           // 四舍五入操作
					if (transform.TransformDecimal == 0)
						return Convert.ToInt64( Math.Round( tmp, transform.TransformDecimal ) );
					else
						return Math.Round( tmp, transform.TransformDecimal );
				case TransformDecimalType.Floor:               // 取下限
					if (transform.TransformDecimal == 0)
						return Convert.ToInt64( Math.Floor( tmp ) );
					else
					{
						double scale = GetExpValue( transform.TransformDecimal );
						return Math.Round( Math.Floor( tmp * scale ) / scale, transform.TransformDecimal );
					}
				case TransformDecimalType.Ceiling:             // 取上限
					if (transform.TransformDecimal == 0)
						return Convert.ToInt64( Math.Ceiling( tmp ) );
					else
					{
						double scale = GetExpValue( transform.TransformDecimal );
						return Math.Round( Math.Ceiling( tmp * scale ) / scale, transform.TransformDecimal );
					}
				default: return tmp;
			}
		}

		/// <summary>
		/// 根据<see cref="IScalarTransform"/>配置信息，对传入的 value 值进行二次加工操作。
		/// </summary>
		/// <param name="transform">变换的规则信息</param>
		/// <param name="value">等待加工的值</param>
		/// <returns>结果数据内容</returns>
		public static object TransValueByTransform<T>( IScalarTransform transform, T value, Func<T, decimal> trans )
		{
			double tmp = TransDoubleValueByTransform( transform, value, trans );
			return TransformDecimalDealWith( transform, tmp );
		}

		//private static Dictionary<string, DynamicExpresso.Interpreter> regularExpress = new Dictionary<string, DynamicExpresso.Interpreter>( );
		private static DynamicExpresso.Interpreter regularExpress = null;

		public static object TransValueByExpressTransform<T>( IScalarTransform transform, T value )
		{
			if (string.IsNullOrEmpty( transform.TransformExpress )) return value;
			if (regularExpress == null)
			{
				regularExpress = new DynamicExpresso.Interpreter( );
				regularExpress.Reference( typeof( Random ) );
			}

			object result = regularExpress.Eval( transform.TransformExpress, new DynamicExpresso.Parameter[] { new DynamicExpresso.Parameter( "x", value ) } );
			if (transform.TransformDecimal < 0) return result;

			return TransformDecimalDealWith( transform, Convert.ToDouble( result ) );
		}

		public static object TransArrayByExpressTransform<T>( IScalarTransform transform, T[] value )
		{
			if (string.IsNullOrEmpty( transform.TransformExpress )) return value;
			if (regularExpress == null)
			{
				regularExpress = new DynamicExpresso.Interpreter( );
				regularExpress.Reference( typeof( Random ) );
			}

			if (value == null) return null;
			if (transform.TransformDecimal == 0)
			{
				if (value.Length == 0) return new long[0];

				long[] array = new long[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					double tmp = Convert.ToDouble( regularExpress.Eval( transform.TransformExpress, new DynamicExpresso.Parameter[] { new DynamicExpresso.Parameter( "x", value[i] ) } ) );
					array[i] = (long)TransformDecimalDealWith( transform, tmp );
				}
				return array;
			}
			else
			{
				if (value.Length == 0) return new double[0];

				double[] array = new double[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					double tmp = Convert.ToDouble( regularExpress.Eval( transform.TransformExpress, new DynamicExpresso.Parameter[] { new DynamicExpresso.Parameter( "x", value[i] ) } ) );
					if (transform.TransformDecimal < 0)
						array[i] = tmp;
					else
						array[i] = (double)TransformDecimalDealWith( transform, tmp );
				}
				return array;
			}
			
		}

		private static double TransDoubleValueByTransform<T>( IScalarTransform transform, T value, Func<T, decimal> trans )
		{
			return (double)(trans( value ) * ((decimal)transform.TransformMultiply) + ((decimal)transform.TransformAddition));
		}

		/// <summary>
		/// 根据<see cref="IScalarTransform"/>配置信息，对传入数组的 value 值进行二次加工操作。
		/// </summary>
		/// <param name="transform">变换的规则信息</param>
		/// <param name="value">等待加工的数组</param>
		/// <returns>处理后的界面</returns>
		public static double[] TransArrayByTransform<T>( IScalarTransform transform, T[] value, Func<T, decimal> trans )
		{
			if (transform.TransformDecimal < 0)
			{
				double[] array = new double[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					array[i] = TransDoubleValueByTransform( transform, value[i], trans );
				}
				return array;
			}

			if (transform.TransformDecimalType == TransformDecimalType.RoundEven)
			{
				double[] array = new double[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					array[i] = Math.Round( TransDoubleValueByTransform( transform, value[i], trans ), transform.TransformDecimal );
				}
				return array;
			}
			else if (transform.TransformDecimalType == TransformDecimalType.Floor)
			{
				double scale = GetExpValue( transform.TransformDecimal );
				double[] array = new double[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					array[i] = TransDoubleValueByTransform( transform, value[i], trans );
					array[i] = Math.Round( Math.Floor( array[i] * scale ) / scale, transform.TransformDecimal );
				}
				return array;
			}
			else // if (transform.TransformDecimalType == TransformDecimalType.Ceiling)
			{
				double scale = GetExpValue( transform.TransformDecimal );
				double[] array = new double[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					array[i] = TransDoubleValueByTransform( transform, value[i], trans );
					array[i] = Math.Round( Math.Ceiling( array[i] * scale ) / scale, transform.TransformDecimal );
				}
				return array;
			}
		}

		public static long[] TransLongArrayByTransform<T>( IScalarTransform transform, T[] value, Func<T, decimal> trans )
		{
			if (transform.TransformDecimalType == TransformDecimalType.RoundEven)
			{
				long[] array = new long[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					double tmp = TransDoubleValueByTransform( transform, value[i], trans );
					array[i] = (long)Math.Round( tmp, transform.TransformDecimal );
				}
				return array;
			}
			else if (transform.TransformDecimalType == TransformDecimalType.Floor)
			{
				double scale = GetExpValue( transform.TransformDecimal );
				long[] array = new long[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					double tmp = TransDoubleValueByTransform( transform, value[i], trans );
					array[i] = (long)Math.Round( Math.Floor( tmp * scale ) / scale, transform.TransformDecimal );
				}
				return array;
			}
			else // if (transform.TransformDecimalType == TransformDecimalType.Ceiling)
			{
				double scale = GetExpValue( transform.TransformDecimal );
				long[] array = new long[value.Length];
				for (int i = 0; i < value.Length; i++)
				{
					double tmp = TransDoubleValueByTransform( transform, value[i], trans );
					array[i] = (long)Math.Round( Math.Ceiling( tmp * scale ) / scale, transform.TransformDecimal );
				}
				return array;
			}
		}

		/// <summary>
		/// 将字符串的数据转换为实际的byte[]，需要根据<see cref="IScalarTransform"/>节点的变换
		/// </summary>
		/// <param name="byteTransform">字节变换对象</param>
		/// <param name="transform">标量变换对象</param>
		/// <param name="value">值数据</param>
		/// <returns>原始字节信息</returns>
		public static OperateResult<byte[]> GetSourceDataFromString( IByteTransform byteTransform, IScalarTransform transform, string value )
		{
			if (transform.IsBoolRegular( ))
			{
				if (transform.Length < 0) return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetBoolValue( transform, value ) ) );
				else return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetBoolArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Byte.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetByteValue( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( value.ToHexBytes( ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
			{
				if (transform.Length < 0) return OperateResult.CreateSuccessResult( byteTransform.TransByte( (byte)RegularHelper.GetSByteValue( transform, value ) ) );

				byte[] array = HslTechnologyHelper.GetByteArrayFrom( RegularHelper.GetSByteArrayValue( transform, value ) );
				return OperateResult.CreateSuccessResult( array );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int16.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetInt16Value( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetInt16ArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt16.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetUInt16Value( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetUInt16ArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int32.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetInt32Value( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetInt32ArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt32.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetUInt32Value( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetUInt32ArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int64.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetInt64Value( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetInt64ArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetUInt64Value( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetUInt64ArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Float.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetFloatValue( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetFloatArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Double.Text)
			{
				if (transform.Length < 0)
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetDoubleValue( transform, value ) ) );
				else
					return OperateResult.CreateSuccessResult( byteTransform.TransByte( RegularHelper.GetDoubleArrayValue( transform, value ) ) );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.String.Text)
			{
				try
				{
					// UNDONE: 这里还可以继续优化的，就是字符串的基本情况
					if (transform is ScalarReadRequest request)
					{
						if (request.StringEncoding != StringEncoding.ASCII)
							return new OperateResult<byte[]>( "当前只支持ASCII编码的字符串写入操作。" );
					}
					return OperateResult.CreateSuccessResult( Encoding.ASCII.GetBytes( value ) );
				}
				catch (Exception ex)
				{
					return new OperateResult<byte[]>( $"当前写入操作失败，需要类型为 {RegularNodeTypeItem.String.Text} 的数据，数据转化失败！" + ex.Message );
				}
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				if (transform is RegularScalarNode regularScalarNode)
				{
					if (value.Length > regularScalarNode.Length * 2)
						return new OperateResult<byte[]>( $"写入的 [{transform.DataTypeCode}] 数组长度太长，超过原本的定义" );
				}

				return OperateResult.CreateSuccessResult( RegularScalarNode.GetBytesFromBCD( value, transform.BCDFormat ) );
			}
			else
				return new OperateResult<byte[]>( $"当前的数据类型[{transform.DataTypeCode}] 暂时不支持写入操作" );
		}







		/// <summary>
		/// 变换类型常量：没有变化
		/// </summary>
		public const int TransformType_None = 0;
		/// <summary>
		/// 变化类型常量，值变化
		/// </summary>
		public const int TransformType_Value = 1;
		/// <summary>
		/// 变化类型常量，bool的非变换
		/// </summary>
		public const int TransformType_Not = 2;
		/// <summary>
		/// 变化类型常量，表达式变换
		/// </summary>
		public const int TransformType_Express = 3;
	}
}
