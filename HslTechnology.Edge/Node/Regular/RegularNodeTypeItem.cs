using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Regular
{
	/// <summary>
	/// 单个规则配置项相关的类型资源，比如配置了什么类型的数据，多少长之类的
	/// </summary>
	public class RegularNodeTypeItem
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public RegularNodeTypeItem( ) { }

		/// <summary>
		/// 实例化信息
		/// </summary>
		/// <param name="text">文本</param>
		/// <param name="length">单位长度</param>
		/// <param name="color">颜色信息</param>
		public RegularNodeTypeItem( string text, int length, Color color )
		{
			Text = text;
			Length = length;
			BackColor = color;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 类型的文本描述
		/// </summary>
		[Category( "规则节点" )]
		[DisplayName( "类型名称" )]
		[Description( "类型的文本描述" )]
		[DefaultValue( "" )]
		public string Text { get; set; }

		/// <summary>
		/// 类型的长度，仅仅是单个数据对象的长度
		/// </summary>
		[Category( "规则节点" )]
		[DisplayName( "类型长度" )]
		[Description( "类型的长度，仅仅是单个数据对象的长度" )]
		[DefaultValue( 0 )]
		public int Length { get; set; }

		/// <summary>
		/// 类型使用的背景色
		/// </summary>
		[Category( "规则节点" )]
		[DisplayName( "类型背景色" )]
		[Description( "类型使用的背景色" )]
		[DefaultValue( typeof( Color ), "Red" )]
		public Color BackColor { get; set; } = Color.Red;

		#endregion

		#region Public Method

		/// <summary>
		/// 根据是否ASCII格式的报文，获取真实的占位长度信息
		/// </summary>
		/// <param name="ascii">是否是ASCII格式的</param>
		/// <returns>实际的占位长度信息</returns>
		public int GetLength( bool ascii = false )
		{
			if (ascii == false)
			{
				return Length;
			}
			switch (Text)
			{
				case "byte":
				case "sbyte":
				case "short":
				case "ushort":
				case "int":
				case "uint":
				case "long":
				case "ulong":
				case "float":
				case "double": return Length * 2;
				case "string":
				case "bcd":
				case "bool":
				case "boolOfByte":
				case "intOfString":
				case "doubleOfString":
				default: return Length;
			}
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => Text;

		#endregion

		#region Const Resource

		/// <summary>
		/// 不存在的数据读取
		/// </summary>
		public static readonly RegularNodeTypeItem None = new RegularNodeTypeItem( "none", 0, Color.DimGray );

		/// <summary>
		/// Bool数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem Bool = new RegularNodeTypeItem( "bool", 1, Color.PaleGreen );

		/// <summary>
		/// Bool数据类型，但是按照字节为单位的索引和长度，通常是一个字节是否为0来表示是否为真
		/// </summary>
		public static readonly RegularNodeTypeItem BoolOfByte = new RegularNodeTypeItem( "boolOfByte", 1, Color.DarkSeaGreen );

		/// <summary>
		/// Byte数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem Byte = new RegularNodeTypeItem( "byte", 1, Color.Gold );

		/// <summary>
		/// SByte数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem SByte = new RegularNodeTypeItem( "sbyte", 1, Color.Orange );

		/// <summary>
		/// short数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem Int16 = new RegularNodeTypeItem( "short", 2, Color.Pink );

		/// <summary>
		/// ushort数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem UInt16 = new RegularNodeTypeItem( "ushort", 2, Color.LavenderBlush );

		/// <summary>
		/// int数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem Int32 = new RegularNodeTypeItem( "int", 4, Color.BlanchedAlmond );

		/// <summary>
		/// uint数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem UInt32 = new RegularNodeTypeItem( "uint", 4, Color.LightGoldenrodYellow );

		/// <summary>
		/// long数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem Int64 = new RegularNodeTypeItem( "long", 8, Color.Khaki );

		/// <summary>
		/// ulong数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem UInt64 = new RegularNodeTypeItem( "ulong", 8, Color.NavajoWhite );

		/// <summary>
		/// float数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem Float = new RegularNodeTypeItem( "float", 4, Color.LightCyan );

		/// <summary>
		/// double数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem Double = new RegularNodeTypeItem( "double", 8, Color.LightSkyBlue );

		/// <summary>
		/// 使用字符串来表示一个整数
		/// </summary>
		public static readonly RegularNodeTypeItem IntOfString = new RegularNodeTypeItem( "intOfString", 0, Color.Yellow );

		/// <summary>
		/// 使用字符串来表示一个浮点数
		/// </summary>
		public static readonly RegularNodeTypeItem DoubleOfString = new RegularNodeTypeItem( "doubleOfString", 0, Color.Yellow );

		/// <summary>
		/// string数据类型，编码信息需要另外指令
		/// </summary>
		public static readonly RegularNodeTypeItem String = new RegularNodeTypeItem( "string", 0, Color.Yellow );

		/// <summary>
		/// json格式的string数据类型
		/// </summary>
		public static readonly RegularNodeTypeItem StringJson = new RegularNodeTypeItem( "json", 0, Color.Yellow );

		/// <summary>
		/// BCD数据类型，BCD编码信息需要另外指令
		/// </summary>
		public static readonly RegularNodeTypeItem BCD = new RegularNodeTypeItem( "bcd", 0, Color.SpringGreen );

		/// <summary>
		/// 从实际的描述信息进行解析类型
		/// </summary>
		/// <param name="text">实际的文本描述</param>
		/// <returns>类型信息</returns>
		public static RegularNodeTypeItem GetDataPraseItemByCode( string text )
		{
			switch (text)
			{
				case "bool":            return Bool;
				case "boolOfByte":      return BoolOfByte;
				case "byte":            return Byte;
				case "sbyte":           return SByte;
				case "short":           return Int16;
				case "ushort":          return UInt16;
				case "int":             return Int32;
				case "uint":            return UInt32;
				case "long":            return Int64;
				case "ulong":           return UInt64;
				case "float":           return Float;
				case "double":          return Double;
				case "string":          return String;
				case "bcd":             return BCD;
				case "intOfString":     return IntOfString;
				case "doubleOfString":  return DoubleOfString;
				default:                return None;
			}
		}

		/// <summary>
		/// 获取当前的所有支持的规则类型对象数组
		/// </summary>
		/// <returns>类型数组信息</returns>
		public static RegularNodeTypeItem[] GetAllRegularNodeTypeItems( bool withBoolOfByte )
		{
			if (withBoolOfByte)
				return new RegularNodeTypeItem[]
				{
					Bool,
					BoolOfByte,
					Byte,
					SByte,
					Int16,
					UInt16,
					Int32,
					UInt32,
					Int64,
					UInt64,
					Float,
					Double,
					String,
					IntOfString,
					DoubleOfString,
					BCD,
				};
			else
				return new RegularNodeTypeItem[]
				{
					Bool,
					Byte,
					SByte,
					Int16,
					UInt16,
					Int32,
					UInt32,
					Int64,
					UInt64,
					Float,
					Double,
					String,
					IntOfString,
					DoubleOfString,
					BCD,
				};
		}

		/// <summary>
		/// 判断指定的类型是否是字符串类型，从字符串转的int，double类型，本质也是从字符串转换的
		/// </summary>
		/// <param name="dataType">类型信息</param>
		/// <returns>是否字符串的类型</returns>
		public static bool IsDataTypeString( string dataType )
		{
			if (dataType == String.Text) return true;
			if (dataType == StringJson.Text) return true;
			if (dataType == IntOfString.Text) return true;
			if (dataType == DoubleOfString.Text) return true;
			if (dataType == BCD.Text) return true;
			return false;
		}

		#endregion
	}
}
