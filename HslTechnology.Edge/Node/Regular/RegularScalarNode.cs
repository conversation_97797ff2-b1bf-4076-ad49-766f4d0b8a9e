using HslCommunication.Core;
using HslTechnology.Edge.Node.Request;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Xml.Linq;
using HslCommunication;
using HslTechnology.Edge.Node.Render;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Reflection;
using System.Linq;
using HslTechnology.Edge.Node.Device;
using System.IO;
using Newtonsoft.Json.Linq;

namespace HslTechnology.Edge.Node.Regular
{
	/// <summary>
	/// 程序解析规则的节点
	/// </summary>
	public class RegularScalarNode
		: GroupNode, IComparable<RegularScalarNode>, IScalarTransform, IAlarmRelateNode, IOeeRelateNode, IBussinessRelateNode
	{
		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public RegularScalarNode( )
		{
			NodeType          = NodeType.RegularScalarNode;
			Length            = -1;
			TransformMultiply = 1;
			TransformDecimal  = -1;
		}

		/// <summary>
		/// 通过指定的XML配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public RegularScalarNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 通过几个基本的节点来实例化一个对象
		/// </summary>
		/// <param name="name">节点名称</param>
		/// <param name="description">节点的描述信息</param>
		/// <param name="index">字节索引信息</param>
		/// <param name="dataTypeCode">数据类型的文本描述</param>
		/// <param name="length">数据长度信息，如果小于0，则表示是一个标量，读取大于0，则表示数组长度，对于字符串来说，就是占用的字节长度信息</param>
		public RegularScalarNode( string name, string description, int index, string dataTypeCode, int length ) : this( )
		{
			Name         = name;
			Description  = description;
			Index        = index;
			DataTypeCode = dataTypeCode;
			Length       = length;
		}

		/// <summary>
		/// 类型的代号，详细参见const数据
		/// </summary>
		[Category( "规则解析信息" )]
		[DisplayName( "解析数据类型" )]
		[Description( "指定当前请求的方式，是以byte[]，还是short,int,float,short[]等等" )]
		[TypeConverter( typeof( RegularNodeTypeConverter ) )]
		public string DataTypeCode { get; set; } = RegularNodeTypeItem.Int16.Text;

		/// <summary>
		/// 类型的长度，对于string来说，就是字符串长度，其他的来说，小于0则表示单个变量，大于0就是数组长度
		/// </summary>
		[Category( "规则解析信息" )]
		[DisplayName( "解析的数据长度" )]
		[Description( "类型的长度，对于string来说，就是字符串长度，其他的来说，小于0则表示单个变量，大于0就是数组长度" )]
		[DefaultValue( -1 )]
		public int Length { get; set; }

		/// <summary>
		/// 数据位于字节数据的索引，以字节为单位，对于bool变量来说，就是按照位的索引
		/// </summary>
		[Category( "规则解析信息" )]
		[DisplayName( "起始字节索引" )]
		[Description( "数据位于字节数据的索引，以字节为单位，对于bool变量来说，就是按照位的索引" )]
		[DefaultValue( 0 )]
		public int Index { get; set; }

		/// <summary>
		/// 是否禁止远程的账户写入数据操作（包括管理员账户），默认不禁止
		/// </summary>
		[Category( "规则解析信息" )]
		[DisplayName( "是否禁止远程账户写入" )]
		[Description( "是否禁止远程的账户写入数据操作（包括管理员账户），默认不禁止" )]
		[DefaultValue( false )]
		public bool ForbidRemoteWrite { get; set; }

		/// <summary>
		/// 是否支持当前变量订阅的操作，一旦启用，可以对单个变量进行MQTT订阅操作
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "是否启用订阅" )]
		[Description( "是否支持当前变量订阅的操作，一旦启用，可以对单个变量进行MQTT订阅操作" )]
		[DefaultValue( false )]
		public bool Subscription { get; set; }

		/// <summary>
		/// 获取或设置当前的数据的单位信息，通常bool类型没有单位，默认为空，也即没有单位信息
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "数据单位" )]
		[Description( "获取或设置当前的数据的单位信息，通常bool类型没有单位，默认为空，也即没有单位信息" )]
		[DefaultValue( "" )]
		public string Unit { get; set; } = "";

		/// <inheritdoc cref="IScalarTransform.TransformType"/>
		[Category( "数据变换" )]
		[DisplayName( "数据变换的类型" )]
		[Description( "是否启动数据变换操作，0 表示没有变换，1 表示倍数及偏移变换，2 表示取反变换（仅用于bool）, 3 表示表达式变换" )]
		[DefaultValue( false )]
		public int TransformType { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的倍数信息，也就是执行乘法操作
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "变换乘法值" )]
		[Description( "使用数据变换操作时候的倍数信息，也就是执行乘法操作" )]
		[DefaultValue( 1d )]
		public double TransformMultiply { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的偏移信息，也就是执行加法操作
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "变换加法值" )]
		[Description( "使用数据变换操作时候的偏移信息，也就是执行加法操作" )]
		[DefaultValue( 0d )]
		public double TransformAddition { get; set; }

		/// <inheritdoc cref="IScalarTransform.TransformExpress"/>
		[Category( "数据变换" )]
		[DisplayName( "变换表达式" )]
		[Description( "此处不支持关联同设备其他点位，内置 x 表示旧的值" )]
		public string TransformExpress { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的小数点位数，如果是小于0的话，则不启用四舍五入操作
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "小数点位数" )]
		[Description( "使用数据变换操作时候的小数点位数，如果是小于0的话，则不启用四舍五入操作" )]
		[DefaultValue( -1 )]
		public int TransformDecimal { get; set; }

		/// <summary>
		/// 在数据变换的四舍五入操作中，指定四舍五入的类型，可选四舍五入，取上限，取下限
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "小数点进位方式" )]
		[Description( "使用数据变换操作的时候确定小数点的进位方式，是四舍五入还是取上限，取下限" )]
		[DefaultValue( typeof( TransformDecimalType ), "RoundEven" )]
		public TransformDecimalType TransformDecimalType { get; set; } = TransformDecimalType.RoundEven;

		/// <summary>
		/// 在数据类型为 string 的时候，可以选择6种不同的字符串解析格式，用来适配各种可变长度的字符串内容
		/// </summary>
		[Category( "字符串变换" )]
		[DisplayName( "字符串格式内容" )]
		[Description( "在数据类型为 string 的时候，可以选择7种不同的字符串解析格式，用来适配各种可变长度的字符串内容" )]
		[DefaultValue( typeof( StringFormat ), "Format1" )]
		public StringFormat StringFormat { get; set; } = StringFormat.Format1;

		/// <summary>
		/// 在数据类型为 string 的时候，解析字符串的时候，字符串是否遇到 0 为止自动截取
		/// </summary>
		[Category( "字符串变换" )]
		[DisplayName( "字符串是否0结束" )]
		[Description( "在数据类型为 string 的时候，解析字符串的时候，字符串是否遇到 0 为止自动截取" )]
		[DefaultValue( false )]
		public bool StringEndwithZero { get; set; } = false;

		/// <summary>
		/// 获取或设置获取的字符串原始字节长度是否补充到偶数长度
		/// </summary>
		[Category( "字符串变换" )]
		[DisplayName( "字符串偶数长度" )]
		[Description( "获取或设置获取的字符串原始字节长度是否补充到偶数长度" )]
		[DefaultValue( false )]
		public bool StringSourceLengthToEven { get; set; } = false;

		/// <summary>
		/// 当读取类型为字符串的时候，获取或设置单个的字符串对应的字节长度
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "单个字符串长度" )]
		[Description( "当读取类型为字符串的时候，获取或设置单个的字符串对应的字节长度" )]
		[DefaultValue( 10 )]
		public int StringLength { get; set; }

		/// <summary>
		/// 在数据类型为 BCD 的时候，
		/// </summary>
		[Category( "字符串变换" )]
		[DisplayName( "BCD码格式内容" )]
		[Description( "在数据类型为 BCD 的时候，可以选择5种不同的字符串解析格式，用来适配各种可变长度的字符串内容" )]
		[DefaultValue( typeof( BCDFormat ), "C8421" )]
		public BCDFormat BCDFormat { get; set; } = BCDFormat.C8421;

		/// <summary>
		/// 在数据类型为 string 的时候，解析字符串的时候，可选的编码信息
		/// </summary>
		[Category( "字符串变换" )]
		[DisplayName( "字符串编码信息" )]
		[Description( "在数据类型为 string 的时候，解析字符串的时候，可选的编码信息" )]
		[DefaultValue( typeof( StringEncoding ), "ASCII" )]
		public StringEncoding StringEncoding { get; set; } = StringEncoding.ASCII;

		/// <summary>
		/// 在数据类型为 string 的时候，解析字符串的时候，每个字节进行的额外的运算信息
		/// </summary>
		[Category( "字符串变换" )]
		[DisplayName( "字符串运算偏移" )]
		[Description( "在数据类型为 string 的时候，解析字符串的时候，每个字节进行的额外的运算信息" )]
		[DefaultValue( typeof( StringOrder ), "Default" )]
		public int StringOffset { get; set; } = 0;

		/// <summary>
		/// 在数据类型为 string 的时候，解析字符串的时候，可选的字节顺序信息
		/// </summary>
		[Category( "字符串变换" )]
		[DisplayName( "字符串顺序变换信息" )]
		[Description( "在数据类型为 string 的时候，解析字符串的时候，可选的字节顺序信息" )]
		[DefaultValue( typeof( StringOrder ), "Default" )]
		public StringOrder StringOrder { get; set; } = StringOrder.Default;

		/// <summary>
		/// 绑定的报警名称信息
		/// </summary>
		[Category( "额外功能" )]
		[DisplayName( "关联报警" )]
		[Description( "当前请求需要绑定报警信息时，可以和配置的报警关联，如果找不到相关的报警配置或是类型不匹配，则报警失效" )]
		[DefaultValue( "" )]
		public string AlarmRelate { get; set; }

		/// <summary>
		/// 绑定的OEE的资源信息
		/// </summary>
		[Category( "额外功能" )]
		[DisplayName( "OEE报警" )]
		[Description( "当前请求需要绑定OEE信息时，可以和配置的OE关联，按照OEE的配置进行分析" )]
		[DefaultValue( "" )]
		public string OeeRelate { get; set; }

		#region IComparable Interface

		/// <summary>
		/// 实现了比较的接口，可以用来方便的排序
		/// </summary>
		/// <param name="other">规则文件进行解析</param>
		/// <returns>是否大小</returns>
		public int CompareTo( RegularScalarNode other )
		{
			return this.GetStartedByteIndex( ).CompareTo( other.GetStartedByteIndex( ) );
		}

		#endregion

		#region Public Method

		/// <inheritdoc cref="IScalarTransform.IsBoolRegular"/>
		public bool IsBoolRegular( ) => DataTypeCode == RegularNodeTypeItem.Bool.Text || DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text;

		/// <summary>
		/// 只获取bool相关的索引信息
		/// </summary>
		/// <returns>起始位置</returns>
		public int GetStartedBoolIndex( )
		{
			if (DataTypeCode == RegularNodeTypeItem.Bool.Text)
				return Index;
			else
				return 0;
		}

		/// <summary>
		/// 获取在字节流中的起始点
		/// </summary>
		/// <returns>起始位置</returns>
		public int GetStartedByteIndex( )
		{
			if (DataTypeCode == RegularNodeTypeItem.Bool.Text)
			{
				return Index / 8;
			}
			else
			{
				return Index;
			}
		}

		/// <summary>
		/// 获取当前显示的变量的名字，如果是数组的，则显示数组的个数
		/// </summary>
		/// <returns>显示的文本信息</returns>
		public string GetSourceDisplayName( )
		{
			if (Length < 0) return GetDisplayName( NodeDisplayMode.ShowDisplayName );
			//if (DataTypeCode == RegularNodeTypeItem.String.Text ||
			//	DataTypeCode == RegularNodeTypeItem.StringJson.Text ||
			//	DataTypeCode == RegularNodeTypeItem.BCD.Text ||
			//	DataTypeCode == RegularNodeTypeItem.IntOfString.Text ||
			//	DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text
			//	)
			//{
			//	return GetDisplayName( NodeDisplayMode.ShowDisplayName );
			//}
			return $"{GetDisplayName( NodeDisplayMode.ShowDisplayName )} * {Length}";
		}

		private Encoding GetEncoding( ) => GetEncoding( StringEncoding );

		private int GetEncodingUnitLength( )
		{
			switch (StringEncoding)
			{
				case StringEncoding.ASCII:    return 1;
				case StringEncoding.ANSI:     return 1;
				case StringEncoding.UTF8:     return 1;
				case StringEncoding.UTF16:    return 2;
				case StringEncoding.UTF16Big: return 2;
				case StringEncoding.GB2312:   return 1;
				default: return 1;
			}
		}

		public bool GetBoolOfByteValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			byte compare = ascii ? (byte)0x30 : (byte)0x00;
			return data[actualIndex] != compare;
		}

		public bool[] GetBoolOfByteArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			byte compare = ascii ? (byte)0x30 : (byte)0x00;
			return data.SelectMiddle( actualIndex, Length ).Select( m => m != compare ).ToArray( );
		}

		public byte GetByteValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 2 ) ).ToHexBytes( );
				return byteTransform.TransByte( buffer, 0 );
			}
			else
			{
				return byteTransform.TransByte( data, actualIndex );
			}
		}

		public byte[] GetByteArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, Length * 2 ) ).ToHexBytes( );
				return byteTransform.TransByte( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransByte( data, actualIndex, Length );
			}
		}

		public sbyte GetSByteValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 2 ) ).ToHexBytes( );
				return (sbyte)byteTransform.TransByte( buffer, 0 );
			}
			else
			{
				return (sbyte)byteTransform.TransByte( data, actualIndex );
			}
		}

		public sbyte[] GetSByteArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, Length * 2 ) ).ToHexBytes( );
				return HslTechnologyHelper.GetSByteArrayFrom( byteTransform.TransByte( buffer, 0, Length ) );
			}
			else
			{
				return HslTechnologyHelper.GetSByteArrayFrom( byteTransform.TransByte( data, actualIndex, Length ) );
			}
		}

		public short GetInt16Value( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 4 ) ).ToHexBytes( );
				return byteTransform.TransInt16( buffer, 0 );
			}
			else
			{
				return byteTransform.TransInt16( data, actualIndex );
			}
		}

		public short[] GetInt16ArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 4 * Length ) ).ToHexBytes( );
				return byteTransform.TransInt16( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransInt16( data, actualIndex, Length );
			}
		}

		public ushort GetUInt16Value( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 4 ) ).ToHexBytes( );
				return byteTransform.TransUInt16( buffer, 0 );
			}
			else
			{
				return byteTransform.TransUInt16( data, actualIndex );
			}
		}

		public ushort[] GetUInt16ArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 4 * Length ) ).ToHexBytes( );
				return byteTransform.TransUInt16( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransUInt16( data, actualIndex, Length );
			}
		}

		public int GetInt32Value( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 8 ) ).ToHexBytes( );
				return byteTransform.TransInt32( buffer, 0 );
			}
			else
			{
				return byteTransform.TransInt32( data, actualIndex );
			}
		}

		public int[] GetInt32ArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 8 * Length ) ).ToHexBytes( );
				return byteTransform.TransInt32( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransInt32( data, actualIndex, Length );
			}
		}

		public uint GetUInt32Value( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 8 ) ).ToHexBytes( );
				return byteTransform.TransUInt32( buffer, 0 );
			}
			else
			{
				return byteTransform.TransUInt32( data, actualIndex );
			}
		}

		public uint[] GetUInt32ArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 8 * Length ) ).ToHexBytes( );
				return byteTransform.TransUInt32( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransUInt32( data, actualIndex, Length );
			}
		}

		public long GetInt64Value( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 16 ) ).ToHexBytes( );
				return byteTransform.TransInt64( buffer, 0 );
			}
			else
			{
				return byteTransform.TransInt64( data, actualIndex );
			}
		}

		public long[] GetInt64ArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 16 * Length ) ).ToHexBytes( );
				return byteTransform.TransInt64( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransInt64( data, actualIndex, Length );
			}
		}

		public ulong GetUInt64Value( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 16 ) ).ToHexBytes( );
				return byteTransform.TransUInt64( buffer, 0 );
			}
			else
			{
				return byteTransform.TransUInt64( data, actualIndex );
			}
		}

		public ulong[] GetUInt64ArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 16 * Length ) ).ToHexBytes( );
				return byteTransform.TransUInt64( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransUInt64( data, actualIndex, Length );
			}
		}

		public float GetFloatValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 8 ) ).ToHexBytes( );
				return byteTransform.TransSingle( buffer, 0 );
			}
			else
			{
				return byteTransform.TransSingle( data, actualIndex );
			}
		}

		public float[] GetFloatArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 8 * Length ) ).ToHexBytes( );
				return byteTransform.TransSingle( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransSingle( data, actualIndex, Length );
			}
		}
		public double GetDoubleValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 16 ) ).ToHexBytes( );
				return byteTransform.TransDouble( buffer, 0 );
			}
			else
			{
				return byteTransform.TransDouble( data, actualIndex );
			}
		}

		public double[] GetDoubleArrayValue( byte[] data, int actualIndex, IByteTransform byteTransform, bool ascii = false )
		{
			if (ascii)
			{
				byte[] buffer = Encoding.ASCII.GetString( data.SelectMiddle( actualIndex, 16 * Length ) ).ToHexBytes( );
				return byteTransform.TransDouble( buffer, 0, Length );
			}
			else
			{
				return byteTransform.TransDouble( data, actualIndex, Length );
			}
		}

		/// <summary>
		/// 根据当前的标量规则配置信息来获取当前的数据信息实际值，返回动态类型，可能是short，可能是别的值
		/// </summary>
		/// <param name="data">真实的数据信息</param>
		/// <param name="startIndex">起始的偏移字节</param>
		/// <param name="byteTransform">数据变换</param>
		/// <param name="forceScale">强制单个的数据解析</param>
		/// <param name="ascii">是否使用</param>
		/// <returns>动态数据</returns>
		public dynamic GetValue( byte[] data, int startIndex, IByteTransform byteTransform, bool forceScale = false, bool ascii = false )
		{
			int actualIndex = Index + startIndex;
			if (DataTypeCode == RegularNodeTypeItem.Bool.Text)
			{
				if (Length < 0)
					return SoftBasic.ByteToBoolArray( data )[actualIndex];
				else
					return SoftBasic.ByteToBoolArray( data ).SelectMiddle( actualIndex, Length );
			}
			else if (DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)
			{
				if (Length < 0)
					return RegularHelper.TransValueByScalarTransform( this, GetBoolOfByteValue( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransValueByScalarTransform( this, GetBoolOfByteArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.Byte.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetByteValue( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransValueByScalarTransform( this, GetByteArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.SByte.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetSByteValue( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransValueByScalarTransform( this, GetSByteArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.Int16.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetInt16Value( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransArrayByScalarTransform( this, GetInt16ArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.UInt16.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetUInt16Value( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransArrayByScalarTransform( this, GetUInt16ArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.Int32.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetInt32Value( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransArrayByScalarTransform( this, GetInt32ArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.UInt32.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetUInt32Value( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransArrayByScalarTransform( this, GetUInt32ArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.Int64.Text)
			{

				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetInt64Value( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransArrayByScalarTransform( this, GetInt64ArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.UInt64.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetUInt64Value( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransArrayByScalarTransform( this, GetUInt64ArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.Float.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetFloatValue( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransArrayByScalarTransform( this, GetFloatArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.Double.Text)
			{
				if (Length < 0 || forceScale)
					return RegularHelper.TransValueByScalarTransform( this, GetDoubleValue( data, actualIndex, byteTransform, ascii ) );
				else
					return RegularHelper.TransArrayByScalarTransform( this, GetDoubleArrayValue( data, actualIndex, byteTransform, ascii ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.String.Text)
			{
				// 可变长的解析
				return GetStringValue( data, startIndex, byteTransform );
			}
			else if (DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
			{
				// 可变长的解析
				return Convert.ToInt64( GetStringValue( data, startIndex, byteTransform ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
			{
				// 可变长的解析
				return Convert.ToDouble( GetStringValue( data, startIndex, byteTransform ) );
			}
			else if (DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				if (TransformType == RegularHelper.TransformType_Value)
				{
					double value = double.Parse( GetBCDValue( data, startIndex, byteTransform ) );
					return RegularHelper.TransValueByScalarTransform( this, value );
				}
				else
					return GetBCDValue( data, startIndex, byteTransform );
			}
			else
			{
				throw new Exception( "Not Supported Data Type" );
			}
		}

		private string GetStringValue( Encoding encoding, byte[] data )
		{
			if (data == null) return string.Empty;
			if (StringOffset != 0)
			{
				for (int i = 0; i < data.Length; i++)
				{
					data[i] = (byte)(data[i] + StringOffset);
				}
			}

			if (StringOrder == StringOrder.WordReverse)
			{
				for (int i = 0; i < data.Length; i += 2)
				{
					if ((i + 1) < data.Length)
					{
						// 交换数据
						byte tmp = data[i];
						data[i] = data[i + 1];
						data[i + 1] = tmp;
					}
				}
			}
			else if (StringOrder == StringOrder.Reverse)
			{
				data = data.Reverse( ).ToArray( );
			}

			// 到业务处理时，统一判断字符串是否按照\0结尾
			return encoding.GetString( data );
		}

		private byte[] GetSourceValue( Encoding encoding, string value  )
		{
			byte[] data = string.IsNullOrEmpty( value ) ? new byte[0] : encoding.GetBytes( value );

			// 如果配置字节计算的话，就进行字节运算操作
			if (StringOffset != 0)
			{
				for (int i = 0; i < data.Length; i++)
				{
					data[i] = (byte)(data[i] - StringOffset);
				}
			}

			// 固定长度的情况下，如果使用了\0作为字符串结尾，但是长度不足的情况下，需要手动补\0
			if (StringFormat == StringFormat.Format1)
			{
				if (StringEndwithZero && data.Length < StringLength)
					data = SoftBasic.SpliceArray( data, new byte[] { 0x00 } );
			}

			if (StringSourceLengthToEven && data.Length % 2 == 1 && data.Length < StringLength)
			{
				data = SoftBasic.SpliceArray( data, new byte[] { 0x00 } );
			}

			if (StringOrder == StringOrder.WordReverse)
			{
				for (int i = 0; i < data.Length; i += 2)
				{
					if ((i + 1) < data.Length)
					{
						// 交换数据
						byte tmp = data[i];
						data[i] = data[i + 1];
						data[i + 1] = tmp;
					}
				}
			}
			else if (StringOrder == StringOrder.Reverse)
			{
				data = data.Reverse( ).ToArray( );
			}

			return data;
		}

		private byte[] stringHead = null;

		/// <summary>
		/// 获取字符串的值分析，需要传入原始字节数据，起始字节分析，数据转换对象
		/// </summary>
		/// <param name="data">原始数据信息</param>
		/// <param name="startIndex">起始的偏移地址</param>
		/// <param name="byteTransform">数据变换规则</param>
		/// <returns>结果字符串信息</returns>
		public string GetStringValue( byte[] data, int startIndex, IByteTransform byteTransform )
		{
			int actualIndex = Index + startIndex;
			if (DataTypeCode == RegularNodeTypeItem.String.Text || DataTypeCode == RegularNodeTypeItem.IntOfString.Text || DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
			{
				// 可变长的解析
				if      (StringFormat == StringFormat.Format1) return GetStringValue( GetEncoding( ), data.SelectMiddle( actualIndex + 0, this.StringLength ) );
				else if (StringFormat == StringFormat.Format2) return GetStringValue( GetEncoding( ), data.SelectMiddle( actualIndex + 1, data[actualIndex] ) );
				else if (StringFormat == StringFormat.Format3)
				{
					stringHead = data.SelectMiddle( actualIndex, 2 );
					int len = data[actualIndex + 1];
					if (actualIndex + 2 + len > data.Length) len = data.Length - (actualIndex + 2);
					return GetStringValue( GetEncoding( ), data.SelectMiddle( actualIndex + 2, len ) );
				}
				else if (StringFormat == StringFormat.Format4)
				{
					stringHead = data.SelectMiddle( actualIndex, 2 ); 
					int len = data[actualIndex];
					if (actualIndex + 2 + len > data.Length) len = data.Length - (actualIndex + 2);
					return GetStringValue( GetEncoding( ), data.SelectMiddle( actualIndex + 2, len ) );
				}
				else if (StringFormat == StringFormat.Format5) return GetStringValue( GetEncoding( ), data.SelectMiddle( actualIndex + 2, byteTransform.TransInt16( data, actualIndex ) * GetEncodingUnitLength( ) ) );
				else if (StringFormat == StringFormat.Format6) return GetStringValue( GetEncoding( ), data.SelectMiddle( actualIndex + 4, byteTransform.TransInt32( data, actualIndex ) * GetEncodingUnitLength( ) ) );
				else if (StringFormat == StringFormat.Format7)
				{
					stringHead = data.SelectMiddle( actualIndex, 4 );
					return GetStringValue( GetEncoding( ), data.SelectMiddle( actualIndex + 4, byteTransform.TransInt16( data, actualIndex ) * GetEncodingUnitLength( ) ) );
				}
				return GetEncoding( ).GetString( data, actualIndex, this.StringLength );
			}
			else
			{
				throw new Exception( "Not Supported Data Type" );
			}
		}

		/// <summary>
		/// 获取字符串数组的值数据信息，需要传入原始字节的数据，起始字节，数据转换对象
		/// </summary>
		/// <param name="data">原始数据信息</param>
		/// <param name="startIndex">起始的偏移地址</param>
		/// <param name="byteTransform">数据变换规则</param>
		/// <returns>结果字符串信息</returns>
		public T[] GetStringArrayValue<T>( byte[] data, int startIndex, IByteTransform byteTransform, Func<string, T> trans )
		{
			T[] values = new T[this.Length];
			for (int i = 0; i < this.Length; i++)
			{
				values[i] = trans( GetStringValue( data, startIndex + i * this.StringLength, byteTransform ) );
			}
			return values;
		}

		public byte[] GetSourceValueFromString( string value, IByteTransform byteTransform )
		{
			if (DataTypeCode == RegularNodeTypeItem.String.Text || DataTypeCode == RegularNodeTypeItem.IntOfString.Text || DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
			{
				byte[] encodingData = GetSourceValue( GetEncoding( ), value: value );
				// 可变长的解析
				if      (StringFormat == StringFormat.Format1) return encodingData;
				else if (StringFormat == StringFormat.Format2) return SoftBasic.SpliceArray( new byte[] { (byte)encodingData.Length }, encodingData );
				else if (StringFormat == StringFormat.Format3)
				{
					byte max = StringLength < 2 ? ((stringHead?.Length > 0) ? stringHead[0] : (byte)0xfe) : (byte)(StringLength - 2);
					return SoftBasic.SpliceArray( new byte[] { max, (byte)encodingData.Length }, encodingData );
				}
				else if (StringFormat == StringFormat.Format4)
				{
					byte max = StringLength < 2 ? ((stringHead?.Length > 1) ? stringHead[1] : (byte)0xfe) : (byte)(StringLength - 2);
					return SoftBasic.SpliceArray( new byte[] { (byte)encodingData.Length, max }, encodingData );
				}
				else if (StringFormat == StringFormat.Format5) return SoftBasic.SpliceArray( byteTransform.TransByte( (short)(encodingData.Length / GetEncodingUnitLength( )) ), encodingData );
				else if (StringFormat == StringFormat.Format6) return SoftBasic.SpliceArray( byteTransform.TransByte( encodingData.Length / GetEncodingUnitLength( ) ), encodingData );
				else if (StringFormat == StringFormat.Format7)
				{
					return SoftBasic.SpliceArray( (stringHead?.Length > 1) ? stringHead.SelectBegin( 2 ) : new byte[2], byteTransform.TransByte( (short)(encodingData.Length / GetEncodingUnitLength( )) ), encodingData );
				}
				return encodingData;
			}
			else
			{
				throw new Exception( "Not Supported Data Type" );
			}
		}

		public byte[] GetSourceValueFromStringArray( string value, IByteTransform byteTransform )
		{
			string[] strings = JArray.Parse( value ).Values<string>( ).ToArray( );

			MemoryStream ms = new MemoryStream( );
			for (int i = 0; i < strings.Length; i++)
			{
				if (i < Length)
				{
					byte[] temp = GetSourceValueFromString( strings[i], byteTransform );
					ms.Write( SoftBasic.ArrayExpandToLength( temp, StringLength ) );
				}
				else
				{
					break;
				}
			}

			return ms.ToArray( );
		}

		/// <summary>
		/// 获取BCD码的分析值，需要传入原始字节数据，起始字节地址，数据转换对象
		/// </summary>
		/// <param name="data">原始字节数据信息</param>
		/// <param name="startIndex">起始字节地址</param>
		/// <param name="byteTransform">数据变换规则</param>
		/// <returns>BCD码的字节</returns>
		public string GetBCDValue( byte[] data, int startIndex, IByteTransform byteTransform )
		{
			int actualIndex = Index + startIndex;
			if (DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				if (StringLength <= 0) return string.Empty;
				return GetBCDValue( data.SelectMiddle( actualIndex, StringLength ), this.BCDFormat );
			}
			else
			{
				throw new Exception( "Not Supported Data Type" );
			}
		}

		/// <summary>
		/// 获取BCD码数组值，需要传入原始字节数据，起始字节地址，数据转换对象
		/// </summary>
		/// <param name="data">原始字节数据信息</param>
		/// <param name="startIndex">起始字节地址</param>
		/// <param name="byteTransform">数据变换规则</param>
		/// <returns>BCD码的字节</returns>
		public string[] GetBCDArray( byte[] data, int startIndex, IByteTransform byteTransform )
		{
			string[] values = new string[this.Length];
			for (int i = 0; i < this.Length; i++)
			{
				values[i] = GetBCDValue( data, startIndex + i * this.StringLength, byteTransform );
			}
			return values;
		}

		/// <summary>
		/// 获取BCD码的分析值，需要传入原始字节数据，其实字节地址，数据转换对象
		/// </summary>
		/// <param name="data">原始字节数据信息</param>
		/// <param name="startIndex">起始字节地址</param>
		/// <param name="byteTransform">数据变换规则</param>
		/// <returns>BCD码的字节</returns>
		public static string GetBCDValue( byte[] buffer, BCDFormat format )
		{
			StringBuilder sb = new StringBuilder( );

			for (int i = 0; i < buffer.Length; i++)
			{
				int low = buffer[i] & 0x0F;
				int high = buffer[i] >> 4;
				sb.Append( GetBcdFromByte( high, format ) );
				sb.Append( GetBcdFromByte( low, format ) );
			}
			return sb.ToString( );
		}

		public static byte[] GetBytesFromBCD( string value, BCDFormat format )
		{
			if (string.IsNullOrEmpty( value )) return new byte[0];
			int length = value.Length % 2 == 1 ? value.Length / 2 + 1 : value.Length / 2;
			byte[] buffer = new byte[length];
			for (int i = 0; i < length; i++)
			{
				buffer[i] = (byte)(buffer[i] | (GetBcdCodeFromChar( value[2 * i + 0], format ) << 4));
				if (2 * i + 1 < value.Length)
					buffer[i] = (byte)(buffer[i] | GetBcdCodeFromChar( value[2 * i + 1], format ));
			}
			return buffer;
		}

		public static OperateResult<byte[]> GetBytesFromBCD( string value, IScalarTransform transform, DeviceNode deviceNode )
		{
			if (transform is RegularScalarNode regularScalarNode)
			{
				int len = regularScalarNode.StringLength * 2;
				if (transform.TransformType == RegularHelper.TransformType_Value)
				{
					// 当前值启动了变换的操作
					double tmp = RegularHelper.GetDoubleValue( transform, value );
					value = Convert.ToInt32( tmp ).ToString( ).PadLeft( len, '0' );
				}

				// 单个的数据写入操作
				byte[] buffer = RegularScalarNode.GetBytesFromBCD( value, transform.BCDFormat );

				if (value.Length > len)
					return new OperateResult<byte[]>( $"写入的 [{transform.DataTypeCode}] 数组长度太长，超过原本的定义" );

				if (buffer.Length < len && regularScalarNode.StringEndwithZero)
					buffer = SoftBasic.ArrayExpandToLength( buffer, len );

				return OperateResult.CreateSuccessResult( buffer );
			}
			else if (transform is ScalarReadRequest scalarReadRequest)
			{
				int len = scalarReadRequest.StringLength * 2 * deviceNode.GetEveryAddressOccupyByte( scalarReadRequest.Address );
				if (transform.TransformType == RegularHelper.TransformType_Value)
				{
					// 当前值启动了变换的操作
					double tmp = RegularHelper.GetDoubleValue( transform, value );
					value = Convert.ToInt32( tmp ).ToString( ).PadLeft( len, '0' );
				}

				// 单个的数据写入操作
				byte[] buffer = RegularScalarNode.GetBytesFromBCD( value, transform.BCDFormat );

				if (value.Length > len)
					return new OperateResult<byte[]>( $"写入的 [{transform.DataTypeCode}] 数组长度太长，超过原本的定义" );

				if (buffer.Length < len && scalarReadRequest.StringEndwithZero)
					buffer = SoftBasic.ArrayExpandToLength( buffer, len );

				return OperateResult.CreateSuccessResult( buffer );
			}
			else
			{
				return OperateResult.CreateSuccessResult( RegularScalarNode.GetBytesFromBCD( value, transform.BCDFormat ) );
			}
		}

		public static OperateResult<byte[]> GetBytesFromBCDArray( string value, IScalarTransform transform, DeviceNode deviceNode )
		{
			string address = string.Empty;
			if (transform is ScalarReadRequest scalarReadRequest) address = scalarReadRequest.Address;

			string[] bcds = JArray.Parse( value ).Values<string>( ).ToArray( );
			int everyLength = transform.GetType( ) == typeof( RegularScalarNode ) ? transform.StringLength : transform.StringLength * deviceNode.GetEveryAddressOccupyByte( address );

			MemoryStream ms = new MemoryStream( );
			for (int i = 0; i < bcds.Length; i++)
			{
				if (i < transform.Length)
				{
					OperateResult<byte[]> build = GetBytesFromBCD( bcds[i], transform, deviceNode );
					if (!build.IsSuccess) return build;

					ms.Write( SoftBasic.ArrayExpandToLength( build.Content, everyLength ) );
				}
				else
				{
					break;
				}
			}

			return OperateResult.CreateSuccessResult( ms.ToArray( ) );
		}

		private static byte GetBcdCodeFromChar( char value, BCDFormat format )
		{
			if (format == BCDFormat.C8421)
			{
				switch (value)
				{
					case '0': return 0x00;
					case '1': return 0x01;
					case '2': return 0x02;
					case '3': return 0x03;
					case '4': return 0x04;
					case '5': return 0x05;
					case '6': return 0x06;
					case '7': return 0x07;
					case '8': return 0x08;
					case '9': return 0x09;
					default: return 0xff;
				}
			}
			else if (format == BCDFormat.C5421)
			{
				switch (value)
				{
					case '0': return 0x00;
					case '1': return 0x01;
					case '2': return 0x02;
					case '3': return 0x03;
					case '4': return 0x04;
					case '5': return 0x08;
					case '6': return 0x09;
					case '7': return 0x0A;
					case '8': return 0x0B;
					case '9': return 0x0C;
					default: return 0xff;
				}
			}
			else if (format == BCDFormat.C2421)
			{
				switch (value)
				{
					case '0': return 0x00;
					case '1': return 0x01;
					case '2': return 0x02;
					case '3': return 0x03;
					case '4': return 0x04;
					case '5': return 0x0B;
					case '6': return 0x0C;
					case '7': return 0x0D;
					case '8': return 0x0E;
					case '9': return 0x0F;
					default: return 0xff;
				}
			}
			else if (format == BCDFormat.C3)
			{
				switch (value)
				{
					case '0': return 0x03;
					case '1': return 0x04;
					case '2': return 0x05;
					case '3': return 0x06;
					case '4': return 0x07;
					case '5': return 0x08;
					case '6': return 0x09;
					case '7': return 0x0A;
					case '8': return 0x0B;
					case '9': return 0x0C;
					default: return 0xff;
				}
			}
			else if (format == BCDFormat.Gray)
			{
				switch (value)
				{
					case '0': return 0x00;
					case '1': return 0x01;
					case '2': return 0x03;
					case '3': return 0x02;
					case '4': return 0x06;
					case '5': return 0x07;
					case '6': return 0x05;
					case '7': return 0x04;
					case '8': return 0x0C;
					case '9': return 0x08;
					default: return 0xff;
				}
			}
			else
			{
				return 0xff;
			}
		}

		private static string GetBcdFromByte( int value, BCDFormat format )
		{
			if(format == BCDFormat.C8421)
			{
				switch (value)
				{
					case 0x00: return "0";
					case 0x01: return "1";
					case 0x02: return "2";
					case 0x03: return "3";
					case 0x04: return "4";
					case 0x05: return "5";
					case 0x06: return "6";
					case 0x07: return "7";
					case 0x08: return "8";
					case 0x09: return "9";
					default: return "*";
				}
			}
			else if(format == BCDFormat.C5421)
			{
				switch (value)
				{
					case 0x00: return "0";
					case 0x01: return "1";
					case 0x02: return "2";
					case 0x03: return "3";
					case 0x04: return "4";
					case 0x08: return "5";
					case 0x09: return "6";
					case 0x0A: return "7";
					case 0x0B: return "8";
					case 0x0C: return "9";
					default: return "*";
				}
			}
			else if (format == BCDFormat.C2421)
			{
				switch (value)
				{
					case 0x00: return "0";
					case 0x01: return "1";
					case 0x02: return "2";
					case 0x03: return "3";
					case 0x04: return "4";
					case 0x0B: return "5";
					case 0x0C: return "6";
					case 0x0D: return "7";
					case 0x0E: return "8";
					case 0x0F: return "9";
					default: return "*";
				}
			}
			else if(format == BCDFormat.C3)
			{
				switch (value)
				{
					case 0x03: return "0";
					case 0x04: return "1";
					case 0x05: return "2";
					case 0x06: return "3";
					case 0x07: return "4";
					case 0x08: return "5";
					case 0x09: return "6";
					case 0x0A: return "7";
					case 0x0B: return "8";
					case 0x0C: return "9";
					default: return "*";
				}
			}
			else if (format == BCDFormat.Gray)
			{
				switch (value)
				{
					case 0x00: return "0";
					case 0x01: return "1";
					case 0x03: return "2";
					case 0x02: return "3";
					case 0x06: return "4";
					case 0x07: return "5";
					case 0x05: return "6";
					case 0x04: return "7";
					case 0x0C: return "8";
					case 0x08: return "9";
					default: return "*";
				}
			}
			else
			{
				return "*";
			}
		}

		/// <summary>
		/// 获取bool的长度信息，起始偏移地址 + 读取长度信息
		/// </summary>
		/// <returns>长度信息</returns>
		public int GetLengthBool( )
		{
			int actualLength = Length < 0 ? 1 : Length;
			if (DataTypeCode == RegularNodeTypeItem.Bool.Text)
				return actualLength;
			else
				return 0;
		}

		/// <summary>
		/// 获取当前的解析规则的节点所占用的最长字节
		/// </summary>
		/// <param name="ascii">是否ASCII格式的信息</param>
		/// <returns>长度</returns>
		public int GetLengthByte( bool ascii = false )
		{
			int asciiScale = ascii ? 2 : 1;
			int actualLength = Length < 0 ? 1 : Length;
			if (DataTypeCode == RegularNodeTypeItem.Bool.Text)
			{
				int byteStart = Index / 8;
				int byteEnd = (Index + actualLength - 1) / 8;
				return byteEnd - byteStart + 1;
			}
			else if (DataTypeCode == RegularNodeTypeItem.String.Text ||
				DataTypeCode == RegularNodeTypeItem.IntOfString.Text ||
				DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text ||
				DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				return actualLength * StringLength;
			}
			else if (DataTypeCode == RegularNodeTypeItem.UInt16.Text ||
				DataTypeCode == RegularNodeTypeItem.Int16.Text)
			{
				return actualLength * 2 * asciiScale;
			}
			else if (DataTypeCode == RegularNodeTypeItem.Int32.Text ||
				DataTypeCode == RegularNodeTypeItem.UInt32.Text ||
				DataTypeCode == RegularNodeTypeItem.Float.Text)
			{
				return actualLength * 4 * asciiScale;
			}
			else if (DataTypeCode == RegularNodeTypeItem.Int64.Text ||
				DataTypeCode == RegularNodeTypeItem.UInt64.Text ||
				DataTypeCode == RegularNodeTypeItem.Double.Text)
			{
				return actualLength * 8 * asciiScale;
			}
			else
			{
				return actualLength;
			}
		}

		/// <summary>
		/// 获取当前的结构体占用的最大的字节索引
		/// </summary>
		/// <returns>最大的字节索引</returns>
		public int GetMaxEndIndex( )
		{
			return GetLengthByte( ) + GetStartedByteIndex( );
		}

		#endregion

		#region Override XmlConvert

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			DataTypeCode         = GetXmlValue( element, nameof( DataTypeCode ),         DataTypeCode,      m => m );
			Length               = GetXmlValue( element, nameof( Length ),               Length,            int.Parse );
			if (RegularNodeTypeItem.IsDataTypeString( DataTypeCode ))
			{
				if (element.Attribute( nameof( StringLength ) ) == null)
				{
					// 旧版的文档，此处需要兼容
					if (Length >= 0)
					{
						StringLength = Length;
						Length = -1;
					}
					else
					{
						StringLength = 1;
					}
				}
				else
				{
					// 新版的文档信息
					StringLength = GetXmlValue( element, nameof( StringLength ), StringLength, int.Parse );
				}
			}
			Index                = GetXmlValue( element, nameof( Index ),                Index,             int.Parse );
			ForbidRemoteWrite    = GetXmlValue( element, nameof( ForbidRemoteWrite ),    ForbidRemoteWrite, bool.Parse );
			Subscription         = GetXmlValue( element, nameof( Subscription ),         Subscription,      bool.Parse );
			Unit                 = GetXmlValue( element,  nameof( Unit ),                Unit,              m => m );
			if (element.Attribute( "TransformEnable" ) != null)
			{
				// 包含特性 TransformEnable ，说明文件使用的旧版本
				TransformType = GetXmlValue( element, "TransformEnable", false, bool.Parse ) ? RegularHelper.TransformType_Value : RegularHelper.TransformType_None;
			}
			else
			{
				TransformType = GetXmlValue( element, nameof( TransformType ), TransformType, int.Parse );
			}
			if (TransformType != RegularHelper.TransformType_None)
			{
				if (TransformType == RegularHelper.TransformType_Express)
					TransformExpress = GetXmlValue( element, nameof( TransformExpress ),     TransformExpress,  m => m );
				TransformMultiply    = GetXmlValue( element, nameof( TransformMultiply ),    TransformMultiply, double.Parse );
				TransformAddition    = GetXmlValue( element, nameof( TransformAddition ),    TransformAddition, double.Parse );
				TransformDecimal     = GetXmlValue( element, nameof( TransformDecimal ),     TransformDecimal,  int.Parse );
				TransformDecimalType = GetXmlEnum(  element, nameof( TransformDecimalType ), TransformDecimalType.RoundEven );
			}
			AlarmRelate              = GetXmlValue( element, nameof( AlarmRelate ),              AlarmRelate,       m => m );
			OeeRelate                = GetXmlValue( element, nameof( OeeRelate ),                OeeRelate,         m => m );
			StringFormat             = GetXmlEnum(  element, nameof( StringFormat ),             StringFormat );
			StringEncoding           = GetXmlEnum(  element, nameof( StringEncoding ),           StringEncoding );
			BCDFormat                = GetXmlEnum(  element, nameof( BCDFormat ),                BCDFormat );
			StringOffset             = GetXmlValue( element, nameof( StringOffset ),             StringOffset,      int.Parse );
			StringOrder              = GetXmlEnum(  element, nameof( StringOrder ),              StringOrder );
			StringEndwithZero        = GetXmlValue( element, nameof( StringEndwithZero ),        StringEndwithZero, bool.Parse );
			StringSourceLengthToEven = GetXmlValue( element, nameof( StringSourceLengthToEven ), StringSourceLengthToEven, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Index ),                              Index );
			element.SetAttributeValue( nameof( DataTypeCode ),                       DataTypeCode );
			if (Length != -1) 
				element.SetAttributeValue( nameof( Length ),                         Length );
			if (ForbidRemoteWrite)
				element.SetAttributeValue( nameof( ForbidRemoteWrite ),              ForbidRemoteWrite );
			if (Subscription) 
				element.SetAttributeValue( nameof( Subscription ),                   Subscription );
			if (!string.IsNullOrEmpty( Unit ))
				element.SetAttributeValue( nameof( Unit ), Unit );
			if (TransformType != RegularHelper.TransformType_None)
			{
				element.SetAttributeValue( nameof( TransformType ),        TransformType );
				if (TransformType == RegularHelper.TransformType_Value)
				{
					element.SetAttributeValue( nameof( TransformMultiply ), TransformMultiply );
					element.SetAttributeValue( nameof( TransformAddition ), TransformAddition );
				}
				else if (TransformType == RegularHelper.TransformType_Express)
				{
					element.SetAttributeValue( nameof( TransformExpress ), TransformExpress );
				}
				if (TransformDecimal != -1)
				{
					element.SetAttributeValue( nameof( TransformDecimal ),     TransformDecimal );
					element.SetAttributeValue( nameof( TransformDecimalType ), TransformDecimalType.ToString( ) );
				}
			}
			if (!string.IsNullOrEmpty( AlarmRelate ))
				element.SetAttributeValue( nameof( AlarmRelate ),          AlarmRelate );
			if (!string.IsNullOrEmpty( OeeRelate ))
				element.SetAttributeValue( nameof( OeeRelate ),            OeeRelate );
			if (DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				element.SetAttributeValue( nameof( StringEndwithZero ), StringEndwithZero );
				element.SetAttributeValue( nameof( BCDFormat ),         BCDFormat.ToString( ) );
				element.SetAttributeValue( nameof( StringLength ),      StringLength );
				if (StringSourceLengthToEven)
					element.SetAttributeValue( nameof( StringSourceLengthToEven ), StringSourceLengthToEven );
			}
			else if (RegularNodeTypeItem.IsDataTypeString( DataTypeCode ))
			{
				element.SetAttributeValue( nameof( StringFormat ),             StringFormat.ToString( ) );
				element.SetAttributeValue( nameof( StringEncoding ),           StringEncoding.ToString( ) );
				element.SetAttributeValue( nameof( StringEndwithZero ),        StringEndwithZero );
				element.SetAttributeValue( nameof( StringLength ),             StringLength );
				if (StringSourceLengthToEven)
					element.SetAttributeValue( nameof( StringSourceLengthToEven ), StringSourceLengthToEven );
				if (StringOffset != 0)
					element.SetAttributeValue( nameof( StringOffset ), StringOffset.ToString( ) );
				if (StringOrder != StringOrder.Default)
					element.SetAttributeValue( nameof( StringOrder ), StringOrder.ToString( ) );
			}
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"RegularScalarNode[{Name}:{DataTypeCode}:{Index}]";

		#endregion

		internal static Lazy<Encoding> EncodingGB = new Lazy<Encoding>( ( ) => {
			Encoding.RegisterProvider( CodePagesEncodingProvider.Instance );
			return Encoding.GetEncoding( "gb2312" );
		} );

		/// <summary>
		/// 根据字符串编码配置信息获取到真正的编码信息
		/// </summary>
		/// <param name="stringEncoding">字符串编码</param>
		/// <returns>真正的encoding对象</returns>
		public static Encoding GetEncoding( StringEncoding stringEncoding )
		{
			switch (stringEncoding)
			{
				case StringEncoding.ASCII:    return Encoding.ASCII;
				case StringEncoding.ANSI:     return Encoding.Default;
				case StringEncoding.UTF8:     return Encoding.UTF8;
				case StringEncoding.UTF16:    return Encoding.Unicode;
				case StringEncoding.UTF16Big: return Encoding.BigEndianUnicode;
				case StringEncoding.GB2312:   return EncodingGB.Value;
				default:                      return Encoding.ASCII;
			}
		}
	}
}
