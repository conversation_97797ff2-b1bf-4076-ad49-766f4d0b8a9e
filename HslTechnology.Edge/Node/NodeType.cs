using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 当前的边缘微服务所有的配置的节点的类型<br />
	/// Types of all configured nodes of the current edge microservice
	/// </summary>
	public enum NodeType
	{
		/// <summary>
		/// 分类的节点信息，用来组织各种子节点的数据信息。<br />
		/// The classified node information is used to organize the data information of various child nodes.
		/// </summary>
		GroupNode = 1,

		/// <summary>
		/// 当前的串口管道节点，用来管理共享管道的设备信息。<br />
		/// The current serial pipe node, used to manage the device information of the shared pipe
		/// </summary>
		GroupSerialPipe = 10,

		/// <summary>
		/// 当前的网口的管道节点，用来管理共享网口管道的设备信息。<br />
		/// The pipe node of the current network port is used to manage the equipment information of the shared network port pipe.
		/// </summary>
		GroupSocketPipe = 11,

		/// <summary>
		/// 当前的单线程管道节点，在单线程管道下面所有的设备使用一个线程来控制顺序请求。<br />
		/// In the current single-threaded pipeline node, all devices under the single-threaded pipeline use one thread to control sequential requests.
		/// </summary>
		GroupSingleThread = 12,

		/// <summary>
		/// 用于描述设备的节点信息，使用 <b>DeviceType</b> 来区分具体的不同的设备。<br />
		/// Used to describe the node information of the device, use <b>DeviceType</b>  to distinguish specific different devices.
		/// </summary>
		DeviceNode = 100,

		/// <summary>
		/// 机器人的节点信息
		/// </summary>
		RobotNode = 101,

		/// <summary>
		/// 数控机床的节点信息
		/// </summary>
		CncNode = 102,

		/// <summary>
		/// 用于描述单次请求的数据信息，服务器将根据这个节点来实现数据采集的。<br />
		/// Used to describe the data information of a single request, the server will realize data collection according to this node.
		/// </summary>
		RequestNode = 200,

		/// <summary>
		/// 用于请求信息的分类的节点
		/// </summary>
		RequestGroupNode = 201,

		/// <summary>
		/// 结构体规则节点，指示了哪个结构体节点信息
		/// </summary>
		RegularStructNode = 300,

		/// <summary>
		/// 单个数据或是数组的解析规则节点，最基本的数据解析单元，可以配置类型，是否数组，对应的字节位置，是否ax+b操作<br />
		/// The parsing rule node of a single data or an array, the most basic data parsing unit, you can configure the type, 
		/// whether it is an array, the corresponding byte position, whether ax+b operation
		/// </summary>
		RegularScalarNode = 301,

		/// <summary>
		/// 用来描述一个复杂的组合的解析规则，是 <see cref="RegularScalarNode"/> 的集合体，用来解析原始的字节数据，转变成实际的有意义的数据
		/// </summary>
		RegularStructItemNode = 302,

		/// <summary>
		/// 当虚拟服务器写入地址数据的时候，进行映射到的另一个设备的其他地址的操作
		/// </summary>
		RegularWriteMapping = 303,

		/// <summary>
		/// 用于数据分析的节点信息，主要是对已经采集或是配置的数据列表进行二次数据分析。<br />
		/// The node information used for data analysis is mainly to perform secondary data analysis on the data list that has been collected or configured.
		/// </summary>
		AnalysisNode = 500,

		/// <summary>
		/// 在数据分析中，用于报警的节点数据分析，使用 <b>AlarmType</b> 区分不同的报警，可定义HEX报警分析，INT报警，数值分析报警<br />
		/// In data analysis, node data analysis for alarms can be defined for HEX alarm analysis, INT alarm, and numerical analysis alarm
		/// </summary>
		AlarmNode = 501,

		/// <summary>
		/// 定义报警内容的节点信息
		/// </summary>
		AlarmDefinitionNode = 502,

		/// <summary>
		/// 报警相关的数据库操作的节点信息
		/// </summary>
		AlarmDatabase = 503,

		/// <summary>
		/// 产品产量信息的节点，指定什么数据分析，规则型号信息，分析多久的数据
		/// </summary>
		ProductNode = 510,

		/// <summary>
		/// 单个设备的稼动率分析，用来分析设备的工作状态信息，有效工作时间，工作的不同状态区分。<br />
		/// The utilization rate analysis of a single device is used to analyze the working status information of the device, the effective working time, and the different working statuses.
		/// </summary>
		OeeNode = 520,
		
		/// <summary>
		/// 单个分析数据的信息，用来定义不同的OEE状态的
		/// </summary>
		DataOeeDefinitionNode = 521,

		/// <summary>
		/// 设备的方法配置信息
		/// </summary>
		MethodConfig = 600,

		/// <summary>
		/// 单个方法的配置信息
		/// </summary>
		MethodItemConfig = 601,

		/// <summary>
		/// 基于数据库的节点信息
		/// </summary>
		DatabaseNode = 1000,
	}
}
