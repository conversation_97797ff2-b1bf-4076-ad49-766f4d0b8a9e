using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 节点的显示模式信息
	/// </summary>
	public enum NodeDisplayMode
	{
		/// <summary>
		/// 显示节点的原始名称
		/// </summary>
		ShowName,

		/// <summary>
		/// 显示节点的别名，如果别名为空，显示为节点的原始名词
		/// </summary>
		ShowDisplayName,

		/// <summary>
		/// 显示组合信息，例如 Templature [温度]，如果别名为空，显示为节点的原始名词
		/// </summary>
		ShowCombine,
	}
}
