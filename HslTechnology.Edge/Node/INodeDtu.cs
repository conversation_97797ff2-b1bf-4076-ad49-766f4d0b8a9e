using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// DTU设备的接口信息
	/// </summary>
	public interface INodeDtu
	{
		/// <summary>
		/// DTU设备的远程IP地址
		/// </summary>
		string DtuIpAddress { get; set; }

		/// <summary>
		/// DTU设备的远程端口号
		/// </summary>
		int DtuPort { get; set; }

		/// <summary>
		/// DTU设备的唯一ID标识信息
		/// </summary>
		string DtuId { get; set; }

		/// <summary>
		/// 是否使用自定义的注册包，如果设置本值不为空，则表示自定义注册包(忽略标识及密码)，值使用HEX格式的字符串信息
		/// </summary>
		string DtuRegistrationPackage { get; set; }

		/// <summary>
		/// DTU设备的远程密码信息
		/// </summary>
		string DtuPassword { get; set; }

		/// <summary>
		/// 获取或设置DTU设备是否支持注册包的反馈
		/// </summary>
		bool DtuResponseAck { get; set; }

		/// <summary>
		/// 是否启动PING生存确认，将会每30秒进行一次ping操作
		/// </summary>
		bool PingEnable { get; set; }

	}
}
