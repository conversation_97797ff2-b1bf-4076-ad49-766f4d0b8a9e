using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 报警关联的数据库节点
	/// </summary>
	public class AlarmDatabaseNode : GroupNode
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public AlarmDatabaseNode( )
		{
			Name = "报警写入数据库节点";
			Description = "一次的数据库请求调用";
			NodeType = NodeType.AlarmDatabase;
		}

		/// <summary>
		/// 指定一个xml配置信息来实例化一个方法请求的对象
		/// </summary>
		/// <param name="element">Xml配置信息</param>
		public AlarmDatabaseNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 当前执行的SQL命令信息
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "SQL命令" )]
		[Description( "当前数据库的执行命令信息，使用 \"{数据名}\" 来表示实际的设备数据信息，在最终执行的时候，如果数据为空，将会插入NULL值。" )]
		public string SqlCommand { get; set; } = "INSERT INTO [dbo].[TABLE] ([DeviceName],[AlarmCode],[AlarmContent],[StartTime],[FinishTime],[Degree]) " +
			"VALUES ({DeviceName},{AlarmCode},{AlarmContent},{StartTime},{FinishTime},{Degree})";

		/// <summary>
		/// 选择的数据库名称
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "数据库名称" )]
		[Description( "当前网关数据库资源中的数据库名称" )]
		public string Database { get; set; }

		/// <summary>
		/// 当前命令执行的时间，1表示报警刚发生的时候，2表示报警刚结束的时候
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "执行SQL时机" )]
		[Description( "当前命令执行的时间，1表示报警刚发生的时候，2表示报警刚结束的时候" )]
		public int ExecuteMoment { get; set; } = 2;

		/// <summary>
		/// 获取或设置报警等级的存储格式，1表示使用数字存储（1，2，3，4），2表示使用字符串存储(Hint,Warn,Error,Fatal)，分别表示提示，警告，错误，致命的意思。
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "报警等级格式" )]
		[Description( "获取或设置报警等级的存储格式，1表示使用数字存储（1，2，3，4），2表示使用字符串存储(Hint,Warn,Error,Fatal)，分别表示提示，警告，错误，致命的意思。" )]
		public int DegreeFormate { get; set; } = 1;

		#endregion

		#region IXML Load

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			SqlCommand    = GetXmlValue( element, nameof( SqlCommand ),    SqlCommand,    m => m );
			Database      = GetXmlValue( element, nameof( Database ),      Database,      m => m );
			ExecuteMoment = GetXmlValue( element, nameof( ExecuteMoment ), ExecuteMoment, int.Parse );
			DegreeFormate = GetXmlValue( element, nameof( DegreeFormate ), DegreeFormate, int.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( SqlCommand ),    SqlCommand );
			element.SetAttributeValue( nameof( Database ),      Database );
			element.SetAttributeValue( nameof( ExecuteMoment ), ExecuteMoment );
			element.SetAttributeValue( nameof( DegreeFormate ), DegreeFormate );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"AlarmDatabaseNode[{Name}]";

		#endregion
	}
}
