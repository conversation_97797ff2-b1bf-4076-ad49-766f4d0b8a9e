using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 基于索引的报警机制的单个报警对象的定义
	/// </summary>
	public class AlarmDefinitionNode : GroupNode, IComparer<AlarmDefinitionNode>, IComparable
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public AlarmDefinitionNode( )
		{
			Degree   = AlarmDegree.Hint;
			NodeType = NodeType.AlarmDefinitionNode;
		}

		/// <summary>
		/// 通过指定的xml配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">xml配置信息</param>
		public AlarmDefinitionNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 通过另一个报警标记对象来初始化当前的报警对象，通过数据完全拷贝的方式
		/// </summary>
		/// <param name="other">另一个对象</param>
		public AlarmDefinitionNode( AlarmDefinitionNode other ) : this( )
		{
			Name        = other.Name;
			Description = other.Description;
			Degree      = other.Degree;
			Code        = other.Code;
			Delay       = other.Delay;
			Conditions  = other.Conditions;
		}

		/// <summary>
		/// 报警关联的代号数字，或是bool数组，hex数组的索引
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警代号" )]
		[Description( "当前报警的代号信息，可能是Bool的索引，可能是报警整数值。" )]
		[DefaultValue( 0 )]
		public int Code { get; set; }

		/// <summary>
		/// 获取或设置当前报警的等级
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警等级" )]
		[Description( "当前报警的代号信息，可能是Bool的索引，可能是报警整数值。" )]
		[DefaultValue( typeof( AlarmDegree ), "Hint" )]
		public AlarmDegree Degree { get; set; }

		/// <summary>
		/// 获取或设置提示性报警的提升时间，当报警从发生时，到当前为止，及提升报警等级
		/// </summary>
		public int DegreeHintRaise { get; set; }

		/// <summary>
		/// 获取或设置警告性报警的提升时间，当报警从发生时，到当前为止，及提升报警等级
		/// </summary>
		public int DegreeWarnRaise { get; set; }

		/// <summary>
		/// 获取或设置错误性报警的提升时间，当报警从发生时，到当前为止，及提升报警等级
		/// </summary>
		public int DegreeErrorRaise { get; set; }

		/// <summary>
		/// 当前报警的条件，为当前设备下的另一个参数值，格式为 i/A/1-5  如果是bool类型，bool/B/5
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警条件" )]
		[Description( "当前报警的其他条件信息，为当前设备下的另一个参数值，格式为 i/A/1-5  如果是bool类型，bool/B/5。" )]
		[DefaultValue( "" )]
		public string Conditions { get; set; }

		/// <summary>
		/// 延时报警，当报警条件满足时，持续一定的时间才算作是真正的报警，按照秒为单位
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警延时" )]
		[Description( "延时报警，当报警条件满足时，持续一定的时间才算作是真正的报警，按照秒为单位" )]
		[DefaultValue( 0 )]
		public int Delay { get; set; }

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Code ),             Code );
			element.SetAttributeValue( nameof( Degree ),           Degree.ToString( ) );
			element.SetAttributeValue( nameof( Delay ),            Delay );
			element.SetAttributeValue( nameof( Conditions ),       Conditions );
			element.SetAttributeValue( nameof( DegreeHintRaise ),  DegreeHintRaise );
			element.SetAttributeValue( nameof( DegreeWarnRaise ),  DegreeWarnRaise );
			element.SetAttributeValue( nameof( DegreeErrorRaise ), DegreeErrorRaise );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Code             = GetXmlValue( element, nameof( Code ),             Code, int.Parse );
			Degree           = GetXmlEnum(  element, nameof( Degree ),           Degree );
			Conditions       = GetXmlValue( element, nameof( Conditions ),       Conditions, m => m );
			Delay            = GetXmlValue( element, nameof( Delay ),            Delay, int.Parse );
			DegreeHintRaise  = GetXmlValue( element, nameof( DegreeHintRaise ),  DegreeHintRaise, int.Parse );
			DegreeWarnRaise  = GetXmlValue( element, nameof( DegreeWarnRaise ),  DegreeWarnRaise, int.Parse );
			DegreeErrorRaise = GetXmlValue( element, nameof( DegreeErrorRaise ), DegreeErrorRaise, int.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"NodeAlarmMarkItem[{Name}]";

		/// <inheritdoc cref="IComparable.CompareTo(object)"/>
		public int Compare( AlarmDefinitionNode x, AlarmDefinitionNode y )
		{
			return x.Code.CompareTo( y.Code );
		}

		/// <inheritdoc cref="IComparable.CompareTo(object)"/>
		public int CompareTo( object obj )
		{
			return Compare( this, obj as AlarmDefinitionNode );
		}

		#endregion

		#region Static Helper

		/// <summary>
		/// 从其他的列表拷贝数据，使用深度拷贝数据的机制来实现
		/// </summary>
		/// <param name="source">原始的列表信息</param>
		/// <returns>新的数组列表</returns>
		public static List<AlarmDefinitionNode> CopyList( List<AlarmDefinitionNode> source )
		{
			if (source == null || source.Count == 0) return new List<AlarmDefinitionNode>( );
			List<AlarmDefinitionNode> list = new List<AlarmDefinitionNode>( source.Count );
			foreach (var item in source)
			{
				list.Add( new AlarmDefinitionNode( item ) );
			}
			return list;
		}

		#endregion
	}
}
