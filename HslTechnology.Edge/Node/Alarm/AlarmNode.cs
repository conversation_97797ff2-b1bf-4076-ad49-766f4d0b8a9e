using HslCommunication.BasicFramework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 报警节点的基类
	/// </summary>
	/// <remarks>
	///  1. 基于Bool的报警信息，true就是报警，false就是结束报警<br />
	///  2. 基于整数的报警信息，这种方式是最简单的，相当于把报警的触发条件做进了PLC，网关只需要采集报警信息。<br />
	///     注意，整数的报警是互斥的，多个整数的报警不能同时触发，多个报警之间切换时，视为上个报警结束。<br />
	///  3. 当报警持续一段时间后，是否需要提升报警等级？<br /><br />
	///  
	///  最终，所有的报警的开始，结束，都会在MQTT发布订阅操作出来
	/// </remarks>
	public class AlarmNode : GroupNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public AlarmNode( )
		{
			NodeType = NodeType.AlarmNode;
			AlarmType = AlarmType.Boolean;
		}

		/// <summary>
		/// 指定XML参数来实例化一个对象
		/// </summary>
		/// <param name="element">XML参数信息</param>
		public AlarmNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 当前的报警的类型
		/// </summary>
		[Category("报警信息")]
		[DisplayName("报警类型")]
		[Description("当前报警节点的类型信息，")]
		public AlarmType AlarmType { get; protected set; }

		/// <summary>
		/// 获取或设置当前的报警关联的定义，对数据范围报警来说，则不存在
		/// </summary>
		[Browsable(false)]
		public AlarmDefinitionNode[] AlarmDefinitions { get; set; }

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( AlarmType ),    AlarmType );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			AlarmType  = GetXmlEnum(  element, nameof( AlarmType ),  AlarmType );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"AlarmNode[{Name}:{AlarmType}]";

		#endregion

		#region Static Helper

		/// <summary>
		/// 根据不同的报警类型，创建不同的报警节点信息
		/// </summary>
		/// <param name="element">报警配置信息</param>
		/// <returns>报警节点对象</returns>
		public static AlarmNode CreateAlarmNodeFromXml( XElement element )
		{
			AlarmType type = SoftBasic.GetEnumFromString<AlarmType>( element.Attribute( nameof( AlarmType ) ).Value );
			switch (type)
			{
				case AlarmType.Boolean:   return new NodeAlarmBool( element );
				case AlarmType.Integer:   return new NodeAlarmInteger( element );
				case AlarmType.Hex:       return new NodeAlarmHex( element );
				case AlarmType.DataRange: return new NodeDataRangeAlarm( element );
				default: return null;
			}
		}

		#endregion
	}
}
