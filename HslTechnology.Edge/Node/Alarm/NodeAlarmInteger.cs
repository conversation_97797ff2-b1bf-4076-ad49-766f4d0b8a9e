using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 基于整数的报警信息的报警
	/// </summary>
	public class NodeAlarmInteger : AlarmNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeAlarmInteger( )
		{
			AlarmType = AlarmType.Integer;
		}

		/// <summary>
		/// 通过指定xml配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">xml配置信息</param>
		public NodeAlarmInteger( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}
	}
}
