using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 报警的状态信息，一个报警的状态从 预报警(可无)->报警->报警结束->报警确认(可无)
	/// </summary>
	public enum AlarmStatus
	{
		/// <summary>
		/// 预报警阶段，并不算真正的报警，用于延时报警的分析，比如温度大于100度10秒才真正报警
		/// </summary>
		Prepared,

		/// <summary>
		/// 报警中
		/// </summary>
		Alarm,

		/// <summary>
		/// 报警结束，应该通知用户，或是自动存储到数据库
		/// </summary>
		Finish,

		/// <summary>
		/// 报警被强制中断掉，也会触发结束的事件，需要额外的处理
		/// </summary>
		Abort,

		/// <summary>
		/// 报警被确认，对于需要确认的报警才有的属性
		/// </summary>
		Check,

	}
}
