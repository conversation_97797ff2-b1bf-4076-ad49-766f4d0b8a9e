using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 单个的bool报警节点
	/// </summary>
	public class NodeAlarmBool : AlarmNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeAlarmBool( )
		{
			AlarmType = AlarmType.Boolean;
		}

		/// <summary>
		/// 通过指定xml配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">xml配置信息</param>
		public NodeAlarmBool( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}
	}
}
