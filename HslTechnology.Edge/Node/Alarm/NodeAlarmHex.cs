using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 基于HEX的报警节点信息
	/// </summary>
	public class NodeAlarmHex : AlarmNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeAlarmHex( )
		{
			AlarmType = AlarmType.Hex;
		}

		/// <summary>
		/// 通过指定xml配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">xml配置信息</param>
		public NodeAlarmHex( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 在解析byte[]数组时，获取或设置设置按照字单位进行反转操作
		/// </summary>
		public bool RevserveByWord { get; set; }


		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( RevserveByWord ), RevserveByWord );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			RevserveByWord = GetXmlValue( element, nameof( RevserveByWord ), RevserveByWord, bool.Parse );
		}

		#endregion

	}
}
