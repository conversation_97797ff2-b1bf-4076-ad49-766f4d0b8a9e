using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 基于数值范围的报警，有延迟时间，达到一定时间条件才报警
	/// </summary>
	public class NodeDataRangeAlarm : AlarmNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeDataRangeAlarm( )
		{
			AlarmType       = AlarmType.DataRange;
			Degree          = AlarmDegree.Warn;
			SecondDegree    = AlarmDegree.Error;
		}

		/// <summary>
		/// 通过指定xml配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">xml配置信息</param>
		public NodeDataRangeAlarm( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 当前的数值范围等级
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警等级" )]
		[Description( "当前报警节点的等级，可选 Hint, Warn，Error, Fatal四种等级" )]
		[DefaultValue( typeof( AlarmDegree ), "Warn" )]
		public AlarmDegree Degree { get; set; }

		/// <summary>
		/// 当前报警的二级等级信息
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警二级等级" )]
		[Description( "当前报警节点的等级，可选 Hint, Warn，Error, Fatal四种等级" )]
		[DefaultValue( typeof( AlarmDegree ), "Error" )]
		public AlarmDegree SecondDegree { get; set; }

		/// <summary>
		/// 当前的数值范围的下限值
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警下限值" )]
		[Description( "当数值低于当前的下限值，即发生报警，如果是 NaN 值，就不进行判断" )]
		[DefaultValue( double.NaN )]
		public double MinValue { get; set; } = double.NaN;

		/// <summary>
		/// 当前的数值范围的极小值，危险值
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警极小值" )]
		[Description( "当数值低于当前的极小值，即发生严重报警" )]
		[DefaultValue( double.MinValue )]
		public double MinMinValue { get; set; } = double.NaN;

		/// <summary>
		/// 当值小于数据的最小值的报警内容
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "下限报警内容" )]
		[Description( "当数值低于当前的下限值，即发生报警，报警的内容" )]
		[DefaultValue( "数据低于正常值" )]
		public string MinAlarmContent { get; set; } = "数据低于正常值";

		/// <summary>
		/// 当值小于数据的最小值的报警内容
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "极小值报警内容" )]
		[Description( "当数值低于当前的极小值，即发生报警，报警的内容" )]
		[DefaultValue( "数据低于极小值" )]
		public string MinMinAlarmContent { get; set; } = "数据低于极小值";

		/// <summary>
		/// 当前的数据的上限值
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警上限值" )]
		[Description( "当数值高于当前的上限值，即发生报警，如果是 NaN 值，就不进行判断" )]
		[DefaultValue( double.NaN )]
		public double MaxValue { get; set; } = double.NaN;

		/// <summary>
		/// 当前的数值范围的极大值，危险值
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警极大值" )]
		[Description( "当数值低于当前的极大值，即发生严重报警" )]
		[DefaultValue( double.MaxValue )]
		public double MaxMaxValue { get; set; } = double.NaN;

		/// <summary>
		/// 当值超过最大值时的报警内容
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "上限报警内容" )]
		[Description( "当数值高于当前的上限值，即发生报警，报警的内容" )]
		[DefaultValue( "数据超出正常值" )]
		public string MaxAlarmContent { get; set; } = "数据超出正常值";

		/// <summary>
		/// 当值超过最大值时的报警内容
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "极大值报警内容" )]
		[Description( "当数值高于当前的极大值，即发生报警，报警的内容" )]
		[DefaultValue( "数据超出极大值" )]
		public string MaxMaxAlarmContent { get; set; } = "数据超出极大值";

		/// <summary>
		/// 当前报警的条件，为当前设备下的另一个参数值，格式为 i/A/1-5  如果是bool类型，bool/B/5
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "报警条件" )]
		[Description( "当前报警生效的其他条件，满足才触发报警" )]
		[DefaultValue( "" )]
		public string Conditions { get; set; }

		/// <summary>
		/// 延时报警，当报警条件满足时，持续一定的时间才算作是真正的报警，按照秒为单位
		/// </summary>
		[Category( "报警信息" )]
		[DisplayName( "延时报警" )]
		[Description( "当前的报警的延时时间，当满足指定的时间，才发生真正的报警" )]
		[DefaultValue( 0 )]
		public int Delay { get; set; }

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Degree ),             Degree.ToString( ) );
			element.SetAttributeValue( nameof( MinValue ),           MinValue );
			element.SetAttributeValue( nameof( MinMinValue ),        MinMinValue );
			element.SetAttributeValue( nameof( MaxValue ),           MaxValue );
			element.SetAttributeValue( nameof( MaxMaxValue ),        MaxMaxValue );
			element.SetAttributeValue( nameof( Delay ),              Delay );
			element.SetAttributeValue( nameof( Conditions ),         Conditions );
			element.SetAttributeValue( nameof( MinAlarmContent ),    MinAlarmContent );
			element.SetAttributeValue( nameof( MinMinAlarmContent ), MinMinAlarmContent );
			element.SetAttributeValue( nameof( MaxAlarmContent ),    MaxAlarmContent );
			element.SetAttributeValue( nameof( SecondDegree ),       SecondDegree );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Degree             = GetXmlEnum(  element, nameof( Degree ),             Degree );
			SecondDegree       = GetXmlEnum(  element, nameof( SecondDegree ),       SecondDegree );
			MinValue           = GetXmlValue( element, nameof( MinValue ),           MinValue, double.Parse );
			MinMinValue        = GetXmlValue( element, nameof( MinMinValue ),        MinMinValue, double.Parse );
			MaxValue           = GetXmlValue( element, nameof( MaxValue ),           MaxValue, double.Parse );
			MaxMaxValue        = GetXmlValue( element, nameof( MaxMaxValue ),        MaxMaxValue, double.Parse );
			Conditions         = GetXmlValue( element, nameof( Conditions ),         Conditions, m => m );
			Delay              = GetXmlValue( element, nameof( Delay ),              Delay, int.Parse );
			MinAlarmContent    = GetXmlValue( element, nameof( MinAlarmContent ),    MinAlarmContent, m => m );
			MinMinAlarmContent = GetXmlValue( element, nameof( MinMinAlarmContent ), MinMinAlarmContent, m => m );
			MaxAlarmContent    = GetXmlValue( element, nameof( MaxAlarmContent ),    MaxAlarmContent, m => m );
			MaxMaxAlarmContent = GetXmlValue( element, nameof( MaxMaxAlarmContent ), MaxMaxAlarmContent, m => m );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"AlarmNode[{Name}:{AlarmType}]";

		#endregion
	}
}
