using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node.Alarm
{
	/// <summary>
	/// 报警的类型，所有的报警可设置条件，可绑定一个bool值，或是一个byte,short,ushort,int,uint,long,ulong的特定值，或是范围值
	/// </summary>
	public enum AlarmType
	{
		/// <summary>
		/// Bool报警，适用于bool，bool数组类型
		/// </summary>
		Boolean,

		/// <summary>
		/// 整数的报警，适用于byte,short,int,long,类型
		/// </summary>
		Integer,

		/// <summary>
		/// 十六进制字节数组的报警，byte[]的每个位都需要配置报警内容
		/// </summary>
		Hex,

		/// <summary>
		/// 字符串报警信息，自带描述信息，为空时表示报警结束
		/// </summary>
		String,

		/// <summary>
		/// 数据的范围报警，比如超过上限，或是低于下限多少时间
		/// </summary>
		DataRange,
	}
}
