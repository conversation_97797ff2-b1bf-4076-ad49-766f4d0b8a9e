using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Oee
{
	/// <summary>
	/// 用于OEE时间区间的类
	/// </summary>
	public class OeeTimePeriod
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public OeeTimePeriod( )
		{

		}

		/// <summary>
		/// 使用指定的格式来初始化当前的对象，例如 0-3600-早班
		/// </summary>
		/// <param name="attr">传入的字符串信息</param>
		public OeeTimePeriod( string attr )
		{
			string[] splits = attr.Split( new char[] { '-' }, StringSplitOptions.RemoveEmptyEntries );
			StartSecond = Convert.ToInt32( splits[0] );
			EndSecond = Convert.ToInt32( splits[1] );
			ShiftName = splits[2];
		}

		/// <summary>
		/// 当前oee班次计算的开始秒信息，0-86400秒为一天
		/// </summary>
		public int StartSecond{ get; set; }

		/// <summary>
		/// 当前OEE班次的计算的结束秒信息，0-86400秒，如果大于这个数，或是小于起始时间，就是第二天了
		/// </summary>
		public int EndSecond{ get; set; }

		/// <summary>
		/// 班次名称
		/// </summary>
		public string ShiftName{ get; set; }

		/// <inheritdoc/>
		public override string ToString( ) => $"OeeTimePeriod[{GetTimeText( StartSecond )}-{GetTimeText( EndSecond )}] {ShiftName}";


		public static int GetSecondsFromDateTime( DateTime dateTime )
		{
			return dateTime.Hour * 3600 + dateTime.Minute * 60 + dateTime.Second;
		}

		public static string GetTimeText( int time )
		{
			int hour = time / 3600;
			int minute = time % 3600 / 60;
			int second = time % 3600 % 60;

			if (second == 0) return $"{hour:D2}:{minute:D2}";
			return $"{hour:D2}:{minute:D2}:{second:D2}";
		}

		public static int GetTimeFromText( string text )
		{
			string[] splits = text.Split( new char[] { ':' }, StringSplitOptions.RemoveEmptyEntries );
			int value = 0;
			value += Convert.ToInt32( splits[0] ) * 3600;
			if (splits.Length > 1) value += Convert.ToInt32( splits[1] ) * 60;
			if (splits.Length > 2) value += Convert.ToInt32( splits[2] );
			return value;
		}

		public static string GetXmlAttr( OeeTimePeriod[] timePeriods )
		{
			if (timePeriods == null || timePeriods.Length == 0) return string.Empty;
			StringBuilder sb = new StringBuilder( );
			for (int i = 0; i < timePeriods.Length; i++)
			{
				OeeTimePeriod period = timePeriods[i];
				sb.Append( period.StartSecond );
				sb.Append( "-" );
				sb.Append( period.EndSecond );
				sb.Append( "-" );
				sb.Append( period.ShiftName );

				if (i < timePeriods.Length - 1)
				{
					sb.Append( "|" );
				}
			}
			return sb.ToString( );
		}

		public static OeeTimePeriod[] CreateFromXmlAttr( string attr )
		{
			if (string.IsNullOrEmpty( attr )) return new OeeTimePeriod[0];
			List<OeeTimePeriod> list = new List<OeeTimePeriod>( );
			string[] splits = attr.Split( new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries );
			for (int i = 0; i < splits.Length; i++)
			{
				list.Add( new OeeTimePeriod( splits[i] ) );
			}
			return list.ToArray( );
		}
	}
}
