using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Oee
{
	/// <summary>
	/// OEE相关的子节点定义
	/// </summary>
	public class OeeDefinitionNode : GroupNode, IComparer<OeeDefinitionNode>, IComparable
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public OeeDefinitionNode( )
		{
			NodeType = NodeType.DataOeeDefinitionNode;
		}

		/// <summary>
		/// 使用指定的XML配置文件来实例化一个对象
		/// </summary>
		/// <param name="element">配置文件信息</param>
		public OeeDefinitionNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// OEE状态关联的代号信息，一般情况来说，0表示设备休息中，1表示设备工作中
		/// </summary>
		[Category( "OEE信息" )]
		[DisplayName( "设备状态代号" )]
		[Description( "OEE状态关联的代号信息，一般情况来说，0表示设备休息中，1表示设备工作中" )]
		[DefaultValue( 0 )]
		public int Code { get; set; }

		/// <summary>
		/// 当前的节点信息是否属于工作时间，如果是工作时间，则当前的状态都会记录到有效工作时间
		/// </summary>
		[Category( "OEE信息" )]
		[DisplayName( "是否工作时间" )]
		[Description( "当前的节点信息是否属于工作时间，如果是工作时间，则当前的状态都会记录到有效工作时间" )]
		[DefaultValue( false )]
		public bool IsWorkingTime { get; set; }


		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Code ), Code );
			element.SetAttributeValue( nameof( IsWorkingTime ), IsWorkingTime );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Code          = GetXmlValue( element, nameof( Code ), Code, int.Parse );
			IsWorkingTime = GetXmlValue( element, nameof( IsWorkingTime ), IsWorkingTime, bool.Parse );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"OeeDefinitionNode[{Name}]";

		/// <inheritdoc cref="IComparable.CompareTo(object)"/>
		public int Compare( OeeDefinitionNode x, OeeDefinitionNode y )
		{
			return x.Code.CompareTo( y.Code );
		}

		/// <inheritdoc cref="IComparable.CompareTo(object)"/>
		public int CompareTo( object obj )
		{
			return Compare( this, obj as OeeDefinitionNode );
		}

		#endregion
	}
}
