using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using HslTechnology.Edge.Reflection;

namespace HslTechnology.Edge.Node.Oee
{
	/// <summary>
	/// OEE的节点定义
	/// </summary>
	public class OeeNode
		: GroupNode
	{
		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public OeeNode( )
		{
			NodeType    = NodeType.OeeNode;
			Name        = "设备运行状态";
			Description = "当前的设备的运行状态的定义集合";
		}

		/// <summary>
		/// 通过指定的XML信息来实例化一个对象
		/// </summary>
		/// <param name="element">xml的配置信息</param>
		public OeeNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 获取或设置当前的OEE关联的节点定义信息
		/// </summary>
		[Browsable( false )]
		public OeeDefinitionNode[] OeeDefinitions { get; set; }

		/// <summary>
		/// 获取或设置当前的重置时间，以秒为单位，表示一天中的秒时间
		/// </summary>
		[Category( "OEE信息" )]
		[DisplayName( "OEE重置时间" )]
		[Description( "获取或设置当前的重置时间，以秒为单位，表示一天中的秒时间，每天到该时间时，重新开始计算 OEE 信息" )]
		[PropertyOrder( 101 )]
		public int ResetTime{ get; set; }

		/// <summary>
		/// 获取或设置当前OEE的班组信息
		/// </summary>
		[Category( "OEE信息" )]
		[DisplayName( "OEE班组信息" )]
		[Description( "获取或设置当前OEE的班组信息" )]
		[PropertyOrder( 102 )]
		public string OeeTimePeriods{ get; set; }

		/// <summary>
		/// 获取或设置当前的OEE关联的班次信息定义
		/// </summary>
		[Browsable( false )]
		public OeeTimePeriod[] OeeTimePeriodsRuntime{ get; set; }

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( ResetTime ), ResetTime );
			element.SetAttributeValue( nameof( OeeTimePeriods ), OeeTimePeriods );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			ResetTime = GetXmlValue( element, nameof( ResetTime ), ResetTime, int.Parse );
			OeeTimePeriods = GetXmlValue( element, nameof( OeeTimePeriods ), OeeTimePeriods, m => m );

			if (!string.IsNullOrEmpty( OeeTimePeriods ))
			{
				OeeTimePeriodsRuntime = OeeTimePeriod.CreateFromXmlAttr( OeeTimePeriods );
			}
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"Oee[{Name}]";

		#endregion
	}
}
