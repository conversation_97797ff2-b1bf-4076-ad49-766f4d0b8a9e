using HslTechnology.Edge.Device.Base;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Request
{
	/// <summary>
	/// 方法调用的请求信息
	/// </summary>
	public class CallMethodRequest : RequestBase
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public CallMethodRequest( )
		{
			Name                    = "方法数据请求";
			Description             = "一次方法的调用信息";
			RequestType             = RequestType.MethodCall;
			StoreResultToDeviceData = true;
		}

		/// <summary>
		/// 指定一个xml配置信息来实例化一个方法请求的对象
		/// </summary>
		/// <param name="element">Xml配置信息</param>
		public CallMethodRequest( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		/// <param name="device">当前的设备对象信息</param>
		public CallMethodRequest( XElement element, DeviceCoreBase device ) : this( element )
		{
			this.DeviceActual = device;
		}

		/// <summary>
		/// 当前的参数信息，以一个json格式的字符串保存的。
		/// </summary>
		[Category( "方法信息" )]
		[DisplayName( "参数信息" )]
		[Description( "当前调用方法的参数，所有的参数需要放入一个JSON格式的字符串。" )]
		[DefaultValue( "" )]
		public string ParameterJson { get; set; }

		/// <summary>
		/// 方法调用的结果是否存储到设备上去，空返回类型的无效。
		/// </summary>
		[Category( "方法信息" )]
		[DisplayName( "结果存储" )]
		[Description( "方法调用的结果是否存储到设备上去，空返回类型的无效。" )]
		[DefaultValue( true )]
		public bool StoreResultToDeviceData { get; set; }

		/// <summary>
		/// 方法调用的结果如果存储到设备上时，数据关联的单位信息
		/// </summary>
		[Category( "方法信息" )]
		[DisplayName( "单位信息" )]
		[Description( "方法调用的结果否存储到设备上去时指示的单位信息。" )]
		[DefaultValue( true )]
		public string StoreResultUnit { get; set; }

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			ParameterJson           = GetXmlValue( element, nameof( ParameterJson ),           ParameterJson, m => m );
			StoreResultToDeviceData = GetXmlValue( element, nameof( StoreResultToDeviceData ), StoreResultToDeviceData, bool.Parse );
			StoreResultUnit         = GetXmlValue( element, nameof( StoreResultUnit ),         StoreResultUnit, m => m );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( ParameterJson ),           ParameterJson );
			element.SetAttributeValue( nameof( StoreResultToDeviceData ), StoreResultToDeviceData );
			element.SetAttributeValue( nameof( StoreResultUnit ),         StoreResultUnit );
			return element;
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"CallMethodRequest[{Name}]";
	}
}
