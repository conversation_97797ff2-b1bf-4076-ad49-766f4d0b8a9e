using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Request
{
	/// <summary>
	/// 用于对请求节点分类的节点类对象，可以统一做一些控制
	/// </summary>
	public class RequestGroupNode : GroupNode
	{
		#region Constuctor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public RequestGroupNode( )
		{
			this.Name = "RequestGroupNode";
			this.NodeType = NodeType.RequestGroupNode;
		}

		/// <summary>
		/// 通过指定的XML配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">XML格式的配置信息</param>
		public RequestGroupNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#endregion


		/// <summary>
		/// 当前组的请求的时间间隔，单位为毫秒，如果小于0，则表示由每个请求自身控制，反之，该组下的所有的请求都使用本值
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求时间间隔(毫秒)" )]
		[Description( "当前组的请求的时间间隔，单位为毫秒，如果小于0，则表示由每个请求自身控制，反之，该组下的所有的请求都使用本值" )]
		[DefaultValue( -1 )]
		public int RequestInterval
		{
			get => this.requestInterval;
			set
			{
				this.requestInterval = value;
			}
		}

		/// <summary>
		/// 获取或设置当前组的所有请求是否生效，默认为 True，表示启动请求
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求是否生效" )]
		[Description( "获取或设置当前组的所有请求是否生效，默认为 True，表示启动请求" )]
		[DefaultValue( true )]
		public bool Enable
		{
			get => enable;
			set
			{
				bool eventOccur = value != enable;
				enable = value;
				if (eventOccur) OnEnableChanged?.Invoke( this, new EventArgs( ) );
			}
		}

		/// <summary>
		/// 当前组的请求是否同时执行请求操作，并在所有请求完成后，进行同步更新数据操作
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求同步信息" )]
		[Description( "当前组的请求是否同时执行请求操作，并在所有请求完成后，进行同步更新数据操作" )]
		[DefaultValue( false )]
		public bool DataSyncUpdate
		{
			get;
			set;
		}

		#region Xml Support

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			RequestInterval = GetXmlValue( element, nameof( RequestInterval ), RequestInterval,   int.Parse );
			DataSyncUpdate  = GetXmlValue( element, nameof( DataSyncUpdate ),  DataSyncUpdate,    bool.Parse );
			Enable          = GetXmlValue( element, nameof( Enable ),          Enable,            bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( RequestInterval ), RequestInterval );
			element.SetAttributeValue( nameof( DataSyncUpdate ),  DataSyncUpdate );
			if (!Enable) element.SetAttributeValue( nameof( Enable ), Enable );
			return element;
		}

		#endregion

		#region Event Handle

		/// <summary>
		/// 当名称变化的时候，触发的事件
		/// </summary>
		public event EventHandler OnEnableChanged;

		#endregion

		#region Private Member

		private bool enable = true;
		private int requestInterval = -1;

		#endregion
	}
}
