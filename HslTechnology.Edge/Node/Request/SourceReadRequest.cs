using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Node.Regular;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Request
{
	/// <summary>
	/// 原始的数据读取，需要对数据进行二次加工，子节点的XML由两部分组成，一个是 RegularStructItemNode 数组，另一个是 
	/// </summary>
	public class SourceReadRequest : RequestBase, IXmlConvert
	{
		/// <summary>
		/// 实例化一个默认的读取对象
		/// </summary>
		public SourceReadRequest( )
		{
			RequestType     = RequestType.SourceRead;
			Length          = "10";
			regularsScalars = new RegularScalarNodeCollection( );
			regularStructs  = new RegularStructNodeCollection( );
			Name            = "SourceReadRequest";
		}

		/// <summary>
		/// 通过一个xml配置文件来实例化一个对象
		/// </summary>
		/// <param name="element">xml配置信息</param>
		public SourceReadRequest( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		/// <param name="device">当前的设备对象信息</param>
		public SourceReadRequest( XElement element, DeviceCoreBase device ) : this( element )
		{
			this.DeviceActual = device;
		}

		/// <summary>
		/// 读取的数据长度，如果小于0，表示读取标量，反之，读取选定类型的数组
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求数据长度" )]
		[Description( "读取长度信息，如果类型为byte[],那就是地址长度，其他是数据长度，对某些PLC可能无效。" )]
		[DefaultValue( "10" )]
		public string Length { get; set; }

		/// <summary>
		/// 本次请求所有关联的单个数据解析的节点信息
		/// </summary>
		[Category( "解析信息" )]
		[DisplayName( "数据自定义解析列表" )]
		[Description( "根据数据进行解析定义规则" )]
		[DesignerSerializationVisibility( DesignerSerializationVisibility.Content )]
		[TypeConverter( typeof( CollectionConverter ) )]
		public RegularScalarNodeCollection RegularScalarNodes => regularsScalars;

		/// <summary>
		/// 本次请求所有关联的结构体数据解析的节点信息
		/// </summary>
		[Category( "解析信息" )]
		[DisplayName( "数据结构体解析列表" )]
		[Description( "根据指定的结构体进行解析定义规则" )]
		[DesignerSerializationVisibility( DesignerSerializationVisibility.Content )]
		public RegularStructNodeCollection RegularStructNodes => regularStructs;

		/// <summary>
		/// 获取长度的整型表示方式
		/// </summary>
		/// <returns>长度信息</returns>
		public int GetLength( int everyAddressOccupyLength = -1 )
		{
			try
			{
				int len = 0;
				if (Length.Contains( ";" ) || Length.Contains( "," ))
					len = Length.Split( new char[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries ).Select( m => int.Parse( m ) ).Sum( );
				else
					len = int.Parse( Length );

				if (everyAddressOccupyLength == -8) return (len + 7) / 8;  // len 表示位信息
				if (everyAddressOccupyLength == -16) {
					int tmp = (len + 7) / 8;
					if (tmp % 2 == 1) return tmp + 1;
					return tmp;
				}
				if (everyAddressOccupyLength < 0) return everyAddressOccupyLength;
				return len * everyAddressOccupyLength;
			}
			catch
			{
				return -1;
			}
		}

		#region Xml Support

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			this.Length = GetXmlValue( element, nameof( this.Length ), this.Length, m => m );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( this.Length ), this.Length );

			return element;
		}

		#endregion

		#region Private Member

		private RegularScalarNodeCollection regularsScalars;
		private RegularStructNodeCollection regularStructs;

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"SourceReadRequest[{Name}]";

		#endregion

		#region Static Helper

		public static string CalculatePhysicalAddressFromSourceReadRequest(
			SourceReadRequest sourceReadRequest,
			int byteOffset,
			int index,
			IScalarTransform scalarTransform,
			Func<string, int, int, IScalarTransform, string> calculatePhysicalAddressFromString )
		{
			if ((sourceReadRequest.Address.Contains( ";" ) || sourceReadRequest.Address.Contains( "," )) &&
				(sourceReadRequest.Length.Contains( ";" ) || sourceReadRequest.Length.Contains( "," )))
			{
				string[] address = sourceReadRequest.Address.Split( new char[] { ';', ',' } );
				int[]     length = sourceReadRequest.Length.Split(  new char[] { ';', ',' } ).Select( m => int.Parse( m ) ).ToArray( );

				// 如果长度不一致，直接返回解析失败
				if (address.Length != length.Length) return string.Empty;

				int count = 0;
				int[] offset = new int[length.Length + 1];
				for (int i = 0; i < length.Length; i++)
				{
					offset[i] = count;
					count += length[i];
				}
				offset[offset.Length - 1] = count;

				for (int i = 0; i < offset.Length - 1; i++)
				{
					if (offset[i] == byteOffset + index) return address[i];
					if (offset[i] < byteOffset + index && byteOffset + index < offset[i + 1])
					{
						// 如果是处于两个地址之间的，就需要重新根据新的地址信息计算偏移值
						return calculatePhysicalAddressFromString( address[i], byteOffset - offset[i], index, scalarTransform );
					}
				}

				return string.Empty;
			}
			else
			{
				return calculatePhysicalAddressFromString( sourceReadRequest.Address, byteOffset, index, scalarTransform );
			}
		}

		#endregion
	}
}
