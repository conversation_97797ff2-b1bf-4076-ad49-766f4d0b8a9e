using HslTechnology.Edge.Device.Base;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Request
{
	/// <summary>
	/// 基于数据库的请求信息
	/// </summary>
	public class DatabaseRequest : RequestBase
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public DatabaseRequest( )
		{
			Name        = "数据库请求";
			Description = "一次的数据库请求调用";
			RequestType = RequestType.DatabaseOperate;
		}

		/// <summary>
		/// 指定一个xml配置信息来实例化一个方法请求的对象
		/// </summary>
		/// <param name="element">Xml配置信息</param>
		public DatabaseRequest( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		/// <param name="device">当前的设备对象信息</param>
		public DatabaseRequest( XElement element, DeviceCoreBase device ) : this( element )
		{
			this.DeviceActual = device;
		}

		#endregion

		#region Public Properties

		/// <summary>
		/// 当设备处于掉线的时候，是否执行当前的命令，默认执行
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "掉线时有效" )]
		[Description( "当设备处于掉线的时候，是否执行当前的命令，默认执行" )]
		public bool ExecuteWhenOffline { get; set; } = true;

		/// <summary>
		/// 当前执行的SQL命令信息
		/// </summary>
		[Category( "数据库信息" )]
		[DisplayName( "SQL命令" )]
		[Description( "当前数据库的执行命令信息，使用 \"{数据名}\" 来表示实际的设备数据信息，在最终执行的时候，如果数据为空，将会插入NULL值。" )]
		public string SqlCommand { get; set; } = "INSERT INTO [dbo].[TABLE] ([COLUMN1],[COLUMN2]) VALUES ({Name1},{Name2})";

		#endregion

		#region IXML Load

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			SqlCommand = GetXmlValue( element, nameof( SqlCommand ), SqlCommand, m => m );
			ExecuteWhenOffline = GetXmlValue( element, nameof( ExecuteWhenOffline ), true, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( SqlCommand ), SqlCommand );
			element.SetAttributeValue( nameof( ExecuteWhenOffline ), ExecuteWhenOffline );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"DatabaseRequest[{Name}]";

		#endregion

	}
}
