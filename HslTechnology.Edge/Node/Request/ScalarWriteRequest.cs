using DynamicExpresso;
using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Request
{
	/// <summary>
	/// 写入标量的请求信息，通常可以指定写入机制，固定值，01反复，自增，自减，还是正弦
	/// </summary>
	public class ScalarWriteRequest : RequestBase, IXmlConvert
	{
		#region Constructor

		/// <summary>
		/// 实例化一个对象
		/// </summary>
		public ScalarWriteRequest( )
		{
			RequestType = RequestType.WriteInterval;
			FormatValue = "0";
			Name        = "定时写入请求";
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		public ScalarWriteRequest( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		/// <param name="device">当前的设备对象信息</param>
		public ScalarWriteRequest( XElement element, DeviceCoreBase device ) : this( element )
		{
			this.DeviceActual = device;
		}

		#endregion

		#region Runtime Properties

		/// <summary>
		/// 等待写入的格式化的值，写入的格式支持如下几种<br />
		/// 1. 纯数字，例如 0，1，100，True，就一直写入恒定的常数
		/// 2. 数组，例如 [1,2,3,100,5], [True, False], 那就在数组之间来回写入
		/// 3. 范围，例如 [1-10000][1], [10000-1][-1]，从起始值写到终值，并指定跳变值 
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "等待写入的格式化的值" )]
		[Description( "等待写入的格式化的值，写入的格式支持多种表示方式。" )]
		[DefaultValue( "0" )]
		public string FormatValue { get; set; }

		/// <summary>
		/// 当使用了数组的时候，本属性表示当前数组的索引信息
		/// </summary>
		[Browsable( false )]
		public int ArrayIndex = 0;

		/// <summary>
		/// 是否使用绝对的物理地址来进行表示写入的点位信息
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "是否绝对地址" )]
		[Description( "是否使用绝对的物理地址来进行表示写入的点位信息" )]
		[TypeConverter( typeof( ScalarReadTypeConverter ) )]
		[DefaultValue( false )]
		public bool IsAbsoluteAddress { get; set; }

		/// <summary>
		/// 本次请求解析字节数据的类型
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求数据类型" )]
		[Description( "指定当前请求的方式，是以byte[]，还是short,int,float,short[]等等" )]
		[TypeConverter( typeof( ScalarReadTypeConverter ) )]
		[DefaultValue( "short" )]
		public string DataTypeCode { get; set; } = "short";

		/// <summary>
		/// 获取或设置当前的数据信息是否一维数组，在byte[]时，需要手动区分实现
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "是否数组" )]
		[Description( "获取或设置当前的数据信息是否一维数组" )]
		[DefaultValue( false )]
		public bool IsArray { get; set; } = false;

		/// <summary>
		/// 运行时候的脚本对象信息，如果数值的转换使用了脚本，则实例化该对象
		/// </summary>
		[Browsable( false )]
		public Interpreter Script { get; set; }

		/// <summary>
		/// 运行时的参数名称
		/// </summary>
		[Browsable( false )]
		public List<string> ParameterNames { get; set; }

		#endregion

		#region Xml Support

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			this.FormatValue       = GetXmlValue( element, nameof( this.FormatValue ),       this.FormatValue,       m => m );
			this.IsAbsoluteAddress = GetXmlValue( element, nameof( this.IsAbsoluteAddress ), this.IsAbsoluteAddress, bool.Parse );
			this.DataTypeCode      = GetXmlValue( element, nameof( this.DataTypeCode ),      this.DataTypeCode,      m => m );
			this.IsArray           = GetXmlValue( element, nameof( this.IsArray ),           this.IsArray,           bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( this.FormatValue ),       this.FormatValue );
			element.SetAttributeValue( nameof( this.IsAbsoluteAddress ), this.IsAbsoluteAddress );
			element.SetAttributeValue( nameof( this.DataTypeCode ),      this.DataTypeCode );
			element.SetAttributeValue( nameof( this.IsArray ),           this.IsArray );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"ScalarWriteRequest[{Name}]";

		#endregion
	}
}
