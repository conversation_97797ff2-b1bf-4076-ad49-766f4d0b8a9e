using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;
using HslTechnology.Edge.Device.Base;
using DynamicExpresso;

namespace HslTechnology.Edge.Node.Request
{
	/// <summary>
	/// 服务器的数据请求类的基类信息
	/// </summary>
	public class RequestBase : GroupNode, IXmlConvert
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public RequestBase( )
		{
			this.Name            = "数据名字";
			this.Description     = "一次完整的数据请求";
			this.Address         = "";
			this.NodeType        = NodeType.RequestNode;
			this.RequestType     = RequestType.ScalarRead;
			this.RequestCount    = -1;
			this.hslTimerTick    = new HslTimerTick( RequestInterval );
		}

		#endregion

		#region Node Properties

		/// <summary>
		/// 本次请求的时间间隔，单位为毫秒
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求时间间隔(毫秒)" )]
		[Description( "指示当前的数据请求的时间间隔，如果设置了1000，就是每隔1秒采集一次数据。" )]
		public int RequestInterval 
		{
			get => this.requestInterval;
			set
			{
				this.requestInterval = value;
				this.hslTimerTick = new HslTimerTick( RequestInterval );
			}
		}

		/// <summary>
		/// 当前请求类型的请求次数，如果小于0，表示无限制，反之，代表实际的请求次数。
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求次数" )]
		[Description( "当前请求类型的请求次数，如果小于0，表示无限制，反之，代表实际的请求次数。" )]
		public long RequestCount { get; set; }

		/// <summary>
		/// 变量的地址
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求地址" )]
		[Description( "从PLC或是其他设备的请求的地址，具体支持的地址需要根据实际不同的PLC来决定的。" )]
		public string Address { get; set; }

		/// <summary>
		/// 当前的请求类型，指示当前的请求类型，读取请求还是其他什么类型的请求
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求类型" )]
		[Description( "指示当前的请求类型，读取请求还是其他什么类型的请求。" )]
		[ReadOnly(true)]
		public RequestType RequestType { get; protected set; }

		/// <summary>
		/// 当前的请求是否生效，默认为 True，表示启动请求
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求是否生效" )]
		[Description( "当前的请求是否生效，默认为 True，表示启动请求。" )]
		[DefaultValue(true)]
		public bool Enable {
			get => enable;
			set
			{
				bool eventOccur = value != enable;
				enable = value;
				if (eventOccur) OnEnableChanged?.Invoke( this, new EventArgs( ) );
			}
		}

		/// <summary>
		/// 当前请求生效的附加条件，需要输入脚本判断，返回bool，最终将根据条件类型来判断执行方式
		/// </summary>
		[Category( "附加信息" )]
		[DisplayName( "请求生效的附加条件" )]
		[Description( "当前请求生效的附加条件，需要输入脚本判断，返回bool，最终将根据条件类型来判断执行方式" )]
		[DefaultValue( "" )]
		public string AdditionalConditions { get; set; }

		/// <summary>
		/// 附加条件生效的类型，0:不生效  1：上升沿生效  2: 下降沿生效  3: 满足时持续生效
		/// </summary>
		[Category( "附加信息" )]
		[DisplayName( "附加条件生效类型" )]
		[Description( "附加条件生效的类型，0:不生效  1：上升沿生效  2: 下降沿生效  3: 满足时持续生效" )]
		[DefaultValue( 0 )]
		public int ConditionsEnableType { get; set; } = 0;

		#endregion

		#region Runtime Properties

		/// <summary>
		/// 上一次读取数据的时间信息
		/// </summary>
		[Browsable( false )]
		public DateTime LastRequstTime => this.hslTimerTick.OperateTime;

		/// <summary>
		/// 当前请求实际的请求次数
		/// </summary>
		[Browsable( false )]
		public long RequestInfactCount { get; set; }

		/// <summary>
		/// 当前请求关联的设备对象
		/// </summary>
		[Browsable( false )]
		public DeviceCoreBase DeviceActual{ get; set; }

		/// <summary>
		/// 当前请求关联的请求组信息
		/// </summary>
		[Browsable( false )]
		public RequestGroupNode RequestGroup { get; set; }

		/// <summary>
		/// 运行时的脚本的参数的缓存
		/// </summary>
		[Browsable( false )]
		public List<string> AdditionalParameterNames{ get; set; }

		/// <summary>
		/// 上一次条件的信息
		/// </summary>
		[Browsable( false )]
		public bool AdditionalConditionLast{ get; set; }

		#endregion

		#region Public Method

		/// <summary>
		/// 如果设置设置了请求类型信息，则加载该请求类别
		/// </summary>
		/// <param name="requestGroup">请求类别</param>
		public void LoadRequestGroup( RequestGroupNode requestGroup )
		{
			if (requestGroup != null)
			{
				if (!requestGroup.Enable) Enable = false;
				if (requestGroup.RequestInterval >= 0) RequestInterval = requestGroup.RequestInterval;
				if (requestGroup.DataSyncUpdate) RequestGroup = requestGroup;
			}
		}

		/// <summary>
		/// 根据传入的时间来检查当前是否发起请求的操作，如果发起请求，则更新当前的请求活动时间
		/// </summary>
		/// <param name="time">当前的请求开始的时间信息</param>
		/// <returns>是否发起请求</returns>
		public bool CheckIfRequestAndUpdateTime( DateTime time ) => this.hslTimerTick.IsTickHappen( time );

		/// <summary>
		/// 设置真正的业务逻辑执行的设备对象
		/// </summary>
		/// <param name="deviceCoreBase">外界给定的设备对象</param>
		/// <returns>最终执行的设备对象信息</returns>
		public DeviceCoreBase GetExecuteDevice( DeviceCoreBase deviceCoreBase )
		{
			if (this.DeviceActual == null) return deviceCoreBase;
			return this.DeviceActual;
		}

		#endregion

		#region Event Handle

		/// <summary>
		/// 当名称变化的时候，触发的事件
		/// </summary>
		public event EventHandler OnEnableChanged;

		#endregion

		#region Xml Support

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			RequestInterval         = GetXmlValue( element, nameof( RequestInterval ), RequestInterval, int.Parse );
			RequestCount            = GetXmlValue( element, nameof( RequestCount ),    RequestCount,    long.Parse );
			Address                 = element.Attribute(    nameof( Address ) ).Value;
			RequestType             = GetXmlEnum( element,  nameof( RequestType ),     RequestType.ScalarRead );
			Enable                  = GetXmlValue( element, nameof( Enable ),          Enable,          bool.Parse );
			AdditionalConditions    = GetXmlValue( element, nameof( AdditionalConditions ), AdditionalConditions, m => m );
			ConditionsEnableType    = GetXmlValue( element, nameof( ConditionsEnableType ), ConditionsEnableType, int.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( RequestInterval ), RequestInterval );
			if (RequestCount != -1) element.SetAttributeValue( nameof( RequestCount ), RequestCount );
			element.SetAttributeValue( nameof( Address ),      Address );
			element.SetAttributeValue( nameof( RequestType ),  RequestType.ToString( ) );
			if (!Enable) element.SetAttributeValue( nameof( Enable ),       Enable );
			if (!string.IsNullOrEmpty( AdditionalConditions )) element.SetAttributeValue( nameof( AdditionalConditions ), AdditionalConditions );
			if (ConditionsEnableType != 0) element.SetAttributeValue( nameof( ConditionsEnableType ), ConditionsEnableType );
			return element;
		}

		#endregion

		#region Private Member

		private bool enable = true;
		private HslTimerTick hslTimerTick;
		private int requestInterval = 1000;

		#endregion
	}
}
