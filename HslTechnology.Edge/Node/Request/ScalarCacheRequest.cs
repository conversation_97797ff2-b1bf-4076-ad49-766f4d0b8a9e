using HslTechnology.Edge.Device.Base;
using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Request
{
	/// <summary>
	/// 缓存标量数据信息，不绑定任何的实际的地址
	/// </summary>
	public class ScalarCacheRequest : ScalarReadRequest
	{
		#region Constructor

		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public ScalarCacheRequest( )
		{
			RequestType = RequestType.ScalarCache;
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		public ScalarCacheRequest( XElement element ) : base( element )
		{
			RequestType = RequestType.ScalarCache;
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		/// <param name="device">当前的设备对象信息</param>
		public ScalarCacheRequest( XElement element, DeviceCoreBase device ) : base( element, device )
		{
			RequestType = RequestType.ScalarCache;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"ScalarCacheRequest[{Name}]";

		#endregion
	}
}
