using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using System.ComponentModel;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Reflection;
using HslTechnology.Edge.Device.Base;

namespace HslTechnology.Edge.Node.Request
{
	/// <summary>
	/// 标量数据的请求类，一个数据请求一次，一个客户端可以进行多次的数据请求
	/// </summary>
	public class ScalarReadRequest
		: RequestBase, IXmlConvert, IScalarTransform, IAlarmRelateNode, IOeeRelateNode, IBussinessRelateNode
	{
		#region Constructor

		/// <summary>
		/// 实例化一个对象
		/// </summary>
		public ScalarReadRequest( )
		{
			Length                   = -1;
			RequestType              = RequestType.ScalarRead;
			DataTypeCode             = RegularNodeTypeItem.Int16.Text;
			TransformDecimalType     = TransformDecimalType.RoundEven;
			TransformMultiply        = 1d;
			TransformDecimal         = -1;
			this.StringLength        = 10;
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		public ScalarReadRequest( XElement element ) : this()
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 通过指定的XML配置文件来初始化当前的实例
		/// </summary>
		/// <param name="element">xml配置文件信息</param>
		/// <param name="device">当前的设备对象信息</param>
		public ScalarReadRequest( XElement element, DeviceCoreBase device ) : this( element )
		{
			this.DeviceActual = device;
		}

		#endregion

		#region Runtime Properties

		/// <summary>
		/// 读取的数据长度，如果小于0，表示读取标量，反之，读取选定类型的数组
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求数据长度" )]
		[Description( "读取长度信息，如果类型为byte[],那就是地址长度，其他是数据长度，对某些PLC可能无效。" )]
		[DefaultValue( -1 )]
		public int Length { get; set; }

		/// <summary>
		/// 本次请求解析字节数据的类型
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "请求数据类型" )]
		[Description( "指定当前请求的方式，是以byte[]，还是short,int,float,short[]等等" )]
		[TypeConverter( typeof( ScalarReadTypeConverter ) )]
		public string DataTypeCode { get; set; }

		/// <summary>
		/// 是否禁止远程的账户写入数据操作（包括管理员账户），默认不禁止
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "是否禁止远程账户写入" )]
		[Description( "是否禁止远程的账户写入数据操作（包括管理员账户），默认不禁止" )]
		[DefaultValue( false )]
		public bool ForbidRemoteWrite { get; set; }

		/// <summary>
		/// 是否支持当前变量订阅的操作，一旦启用，可以对单个变量进行MQTT订阅操作
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "是否启用订阅" )]
		[Description( "是否支持当前变量订阅的操作，一旦启用，可以对单个变量进行MQTT订阅操作" )]
		[DefaultValue( false )]
		public bool Subscription { get; set; }

		/// <summary>
		/// 获取或设置当前的数据的单位信息，通常bool类型没有单位，默认为空，也即没有单位信息
		/// </summary>
		[Category( "请求信息" )]
		[DisplayName( "数据单位" )]
		[Description( "获取或设置当前的数据的单位信息，通常bool类型没有单位，默认为空，也即没有单位信息" )]
		[DefaultValue( "" )]
		public string Unit { get; set; } = "";

		/// <inheritdoc cref="IScalarTransform.TransformType"/>
		[Category( "数据变换" )]
		[DisplayName( "数据变换的类型" )]
		[Description( "是否启动数据变换操作，0 表示没有变换，1 表示倍数及偏移变换，2 表示取反变换（仅用于bool）, 3 表示表达式变换" )]
		[DefaultValue( false )]
		public int TransformType { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的倍数信息，也就是执行乘法操作
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "变换乘法值" )]
		[Description( "使用数据变换操作时候的倍数信息，也就是执行乘法操作" )]
		public double TransformMultiply { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的偏移信息，也就是执行加法操作
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "变换加法值" )]
		[Description( "使用数据变换操作时候的偏移信息，也就是执行加法操作" )]
		public double TransformAddition { get; set; }

		/// <inheritdoc cref="IScalarTransform.TransformExpress"/>
		[Category( "数据变换" )]
		[DisplayName( "变换表达式" )]
		[Description( "此处不支持关联同设备其他点位，内置 x 表示旧的值" )]
		public string TransformExpress { get; set; }

		/// <summary>
		/// 使用数据变换操作时候的小数点位数，如果是小于0的话，则不启用四舍五入操作
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "小数点位数" )]
		[Description( "使用数据变换操作时候的小数点位数，如果是小于0的话，则不启用四舍五入操作" )]
		public int TransformDecimal { get; set; }

		/// <summary>
		/// 在数据变换的四舍五入操作中，指定四舍五入的类型，可选四舍五入，取上限，取下限
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "小数点进位方式" )]
		[Description( "使用数据变换操作的时候确定小数点的进位方式，是四舍五入还是取上限，取下限" )]
		public TransformDecimalType TransformDecimalType { get; set; }

		/// <summary>
		/// 在数据类型为 string 的时候，解析字符串的时候，可选的编码信息
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "字符串编码信息" )]
		[Description( "在数据类型为 string 的时候，解析字符串的时候，可选的编码信息" )]
		[DefaultValue( typeof( StringEncoding ), "ASCII" )]
		public StringEncoding StringEncoding { get; set; } = StringEncoding.ASCII;

		/// <summary>
		/// 在数据类型为 string 的时候，解析字符串的时候，字符串是否遇到 0 为止自动截取
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "字符串是否0结束" )]
		[Description( "在数据类型为 string 的时候，解析字符串的时候，字符串是否遇到 0 为止自动截取" )]
		[DefaultValue( false )]
		public bool StringEndwithZero { get; set; } = false;

		/// <summary>
		/// 当读取类型为字符串的时候，获取或设置单个的字符串对应的地址长度
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "单个字符串长度" )]
		[Description( "当读取类型为字符串的时候，获取或设置单个的字符串对应的地址长度" )]
		[DefaultValue( 10 )]
		public int StringLength { get; set; }

		/// <summary>
		/// 在数据类型为 bcd 的时候，解析bcd码的时候，可选的编码信息
		/// </summary>
		[Category( "数据变换" )]
		[DisplayName( "BCD编码信息" )]
		[Description( "在数据类型为 bcd 的时候，解析bcd码的时候，可选的编码信息" )]
		[DefaultValue( typeof( BCDFormat ), "C8421" )]
		public BCDFormat BCDFormat { get; set; } = BCDFormat.C8421;

		/// <summary>
		/// 绑定的报警名称信息
		/// </summary>
		[Category( "额外功能" )]
		[DisplayName( "关联报警" )]
		[Description( "当前请求需要绑定报警信息时，可以和配置的报警关联，如果找不到相关的报警配置或是类型不匹配，则报警失效" )]
		[DefaultValue( "" )]
		public string AlarmRelate { get; set; }

		/// <summary>
		/// 绑定的OEE的资源信息
		/// </summary>
		[Category( "额外功能" )]
		[DisplayName( "OEE报警" )]
		[Description( "当前请求需要绑定OEE信息时，可以和配置的OE关联，按照OEE的配置进行分析" )]
		[DefaultValue( "" )]
		public string OeeRelate { get; set; }

		/// <inheritdoc cref="IScalarTransform.IsBoolRegular"/>
		public bool IsBoolRegular( ) => DataTypeCode == RegularNodeTypeItem.Bool.Text || DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text;

		#endregion

		#region Xml Support

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			DataTypeCode         = GetXmlValue(  element, nameof( DataTypeCode ),         DataTypeCode,      m => m );
			Length               = GetXmlValue(  element, nameof( Length ),               Length,            int.Parse );
			if (RegularNodeTypeItem.IsDataTypeString( DataTypeCode ))
			{
				if (element.Attribute( nameof( StringLength ) ) == null)
				{
					// 旧版的文档，此处需要兼容
					if( Length >= 0)
					{
						StringLength = Length;
						Length = -1;
					}
					else
					{
						StringLength = 1;
					}
				}
				else
				{
					// 新版的文档信息
					StringLength = GetXmlValue( element, nameof( StringLength ), StringLength, int.Parse );
				}
			}

			ForbidRemoteWrite    = GetXmlValue(  element, nameof( ForbidRemoteWrite ),    ForbidRemoteWrite, bool.Parse );
			Subscription         = GetXmlValue(  element, nameof( Subscription ),         Subscription,      bool.Parse );
			Unit                 = GetXmlValue( element,  nameof( Unit ),                 Unit,              m => m );

			if (element.Attribute( "TransformEnable" ) != null)
			{
				// 包含特性 TransformEnable ，说明文件使用的旧版本
				TransformType = GetXmlValue( element, "TransformEnable", false, bool.Parse ) ? RegularHelper.TransformType_Value : RegularHelper.TransformType_None;
			}
			else
			{
				TransformType = GetXmlValue( element, nameof( TransformType ), TransformType, int.Parse );
			}
			if (TransformType != RegularHelper.TransformType_None)
			{
				if (TransformType == RegularHelper.TransformType_Express)
					TransformExpress = GetXmlValue( element, nameof( TransformExpress ),      TransformExpress, m => m );
				TransformMultiply    = GetXmlValue(  element, nameof( TransformMultiply ),    TransformMultiply, double.Parse );
				TransformAddition    = GetXmlValue(  element, nameof( TransformAddition ),    TransformAddition, double.Parse );
				TransformDecimal     = GetXmlValue(  element, nameof( TransformDecimal ),     TransformDecimal,  int.Parse );
				TransformDecimalType = GetXmlEnum(   element, nameof( TransformDecimalType ), TransformDecimalType.RoundEven );
			}
			StringEncoding       = GetXmlEnum(   element, nameof( StringEncoding ),       StringEncoding.ASCII );
			BCDFormat            = GetXmlEnum(   element, nameof( BCDFormat ),            BCDFormat.C8421 );
			AlarmRelate          = GetXmlValue(  element, nameof( AlarmRelate ),          AlarmRelate,       m => m );
			OeeRelate            = GetXmlValue(  element, nameof( OeeRelate ),            OeeRelate,         m => m );
			StringEndwithZero    = GetXmlValue(  element, nameof( StringEndwithZero ),    StringEndwithZero, bool.Parse );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( DataTypeCode ),          DataTypeCode );
			if (Length != -1)
				element.SetAttributeValue( nameof( Length ),            Length );
			if (ForbidRemoteWrite)
				element.SetAttributeValue( nameof( ForbidRemoteWrite ), ForbidRemoteWrite );
			if (Subscription) 
				element.SetAttributeValue( nameof( Subscription ),      Subscription );
			if (!string.IsNullOrEmpty( Unit ))
				element.SetAttributeValue( nameof( Unit ), Unit );

			if (TransformType != RegularHelper.TransformType_None)
			{
				element.SetAttributeValue( nameof( TransformType ),        TransformType );
				if (TransformType == RegularHelper.TransformType_Value)
				{
					element.SetAttributeValue( nameof( TransformMultiply ), TransformMultiply );
					element.SetAttributeValue( nameof( TransformAddition ), TransformAddition );
				}
				else if (TransformType == RegularHelper.TransformType_Express)
				{
					element.SetAttributeValue( nameof( TransformExpress ), TransformExpress );
				}
				if (TransformDecimal != -1)
				{
					element.SetAttributeValue( nameof( TransformDecimal ),     TransformDecimal );
					element.SetAttributeValue( nameof( TransformDecimalType ), TransformDecimalType.ToString( ) );
				}
			}
			if (DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				element.SetAttributeValue( nameof( BCDFormat ),         BCDFormat.ToString( ) );
				element.SetAttributeValue( nameof( StringEndwithZero ), StringEndwithZero );
				element.SetAttributeValue( nameof( StringLength ),      StringLength );
			}
			else if (RegularNodeTypeItem.IsDataTypeString( DataTypeCode ))
			{
				element.SetAttributeValue( nameof( StringEncoding ), StringEncoding.ToString( ) );
				element.SetAttributeValue( nameof( StringEndwithZero ), StringEndwithZero );
				element.SetAttributeValue( nameof( StringLength ), StringLength );
			}
			if (!string.IsNullOrEmpty( AlarmRelate ))
				element.SetAttributeValue( nameof( AlarmRelate ), AlarmRelate );
			if (!string.IsNullOrEmpty( OeeRelate ))
				element.SetAttributeValue( nameof( OeeRelate ), OeeRelate );
			return element;
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"ScalarReadRequest[{Name}]";

		#endregion
	}
}
