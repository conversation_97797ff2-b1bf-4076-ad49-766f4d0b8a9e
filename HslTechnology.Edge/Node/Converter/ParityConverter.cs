using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Globalization;
using System.IO.Ports;

namespace HslTechnology.Edge.Node.Converter
{
	/// <summary>
	/// <see cref="Parity"/> 的自动转换类
	/// </summary>
	public class ParityConverter : EnumConverter
	{
		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public ParityConverter( ) : base( typeof( Parity ) )
		{

		}

		/// <inheritdoc/>
		public override object ConvertTo( ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType )
		{
			Parity parity = (Parity)value;
			switch (parity)
			{
				case Parity.Even: return "偶";
				case Parity.Mark: return "保留1";
				case Parity.None: return "无";
				case Parity.Odd: return "奇";
				case Parity.Space: return "保留0";
			}
			return base.ConvertTo( context, culture, value, destinationType );
		}

		/// <inheritdoc/>
		public override object ConvertFrom( ITypeDescriptorContext context, CultureInfo culture, object value )
		{
			string str = (string)value;
			if (str == "偶") return Parity.Even;
			if (str == "保留1") return Parity.Mark;
			if (str == "无") return Parity.None;
			if (str == "奇") return Parity.Odd;
			if (str == "保留0") return Parity.Space;
			return base.ConvertFrom( context, culture, value );
		}
	}
}
