using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Globalization;

namespace HslTechnology.Edge.Node.Converter
{
	/// <summary>
	/// 日期格式的数据转换信息
	/// </summary>
	public class DateConverter : DateTimeConverter
	{
		/// <inheritdoc/>
		public override object ConvertTo( ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType )
		{
			DateTime dateTime = (DateTime)value;
			return dateTime.ToString( "yyyy年MM月dd日" );
		}
	}
}
