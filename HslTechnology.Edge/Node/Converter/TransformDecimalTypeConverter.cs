using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslTechnology.Edge.Node.Request;

namespace HslTechnology.Edge.Node.Converter
{
	/// <summary>
	/// 用于进行小数点四舍五入的操作的显示转换类
	/// </summary>
	public class TransformDecimalTypeConverter : EnumConverter
	{
		/// <inheritdoc cref="GroupNode.GroupNode()"/>
		public TransformDecimalTypeConverter( ) : base( typeof( TransformDecimalType ) )
		{

		}

		/// <inheritdoc/>
		public override object ConvertTo( ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType )
		{
			TransformDecimalType type = (TransformDecimalType)value;
			switch (type)
			{
				case TransformDecimalType.RoundEven: return "四舍五入(偶数)";
				case TransformDecimalType.Floor:     return "取下限";
				case TransformDecimalType.Ceiling:   return "取上限";
			}
			return base.ConvertTo( context, culture, value, destinationType );
		}

		/// <inheritdoc/>
		public override object ConvertFrom( ITypeDescriptorContext context, CultureInfo culture, object value )
		{
			string str = (string)value;
			if (str == "四舍五入(偶数)") return TransformDecimalType.RoundEven;
			if (str == "取下限")         return TransformDecimalType.Floor;
			if (str == "取上限")         return TransformDecimalType.Ceiling;
			return base.ConvertFrom( context, culture, value );
		}
	}
}
