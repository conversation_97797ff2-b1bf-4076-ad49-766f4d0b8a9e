using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// OEE相关的节点信息
	/// </summary>
	public interface IOeeRelateNode
	{
		/// <inheritdoc cref="GroupNode.Name"/>
		string Name { get; set; }

		/// <inheritdoc cref="Regular.RegularScalarNode.DataTypeCode"/>
		string DataTypeCode { get; set; }

		/// <inheritdoc cref="Regular.RegularScalarNode.Length"/>
		int Length { get; set; }

		/// <inheritdoc cref="Regular.RegularScalarNode.OeeRelate"/>
		string OeeRelate { get; set; }
	}
}
