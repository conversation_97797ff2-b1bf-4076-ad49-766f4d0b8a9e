using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;

namespace HslTechnology.Edge.Node
{
	/// <summary>
	/// 支持 AsciiFormate 格式的节点信息
	/// </summary>
	public interface INodeAsciiFormate
	{
		/// <summary>
		/// 获取或设置是否使用ASCII的格式字符串来解析地址和记录电文通信，虽然是ASCII报文，仍然支持输入 \0D\0A 来表示 \r\n 等不可见字符
		/// </summary>
		bool UseAsciiFormate { get; set; }
	}
}
