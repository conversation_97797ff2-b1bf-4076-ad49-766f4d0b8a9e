using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// AB的PCCC协议实现的虚拟服务器
	/// </summary>
	public class NodeAllenBradleyPcccServer : ServerNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeAllenBradleyPcccServer( )
		{
			Name        = "AllenBradley Pccc 服务器";
			Description = "这是一个 PCCC 服务器";
			DeviceType  = DeviceType.AllenBradleyPcccServer;
			Port        = 44818;
		}

		/// <summary>
		/// 通过Xml配置文件来实例化一个对象
		/// </summary>
		/// <param name="element">Xml配置文件</param>
		public NodeAllenBradleyPcccServer( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeAllenBradleyPcccServer] {Name}";
	}
}
