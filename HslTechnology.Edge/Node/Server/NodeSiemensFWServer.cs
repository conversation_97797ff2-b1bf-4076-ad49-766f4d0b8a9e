using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// 西门子PLC的FW协议的虚拟服务器
	/// </summary>
	public class NodeSiemensFWServer : ServerNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeSiemensFWServer( )
		{
			Name           = "Siemens FW 服务器";
			Description    = "这是一个西门子的FW协议的服务器";
			DeviceType     = DeviceType.SiemensFWServer;
			Port           = 2000;
		}
		/// <summary>
		/// 通过指定的Xml配置文件来实例化一个对象
		/// </summary>
		/// <param name="element">Xml配置信息</param>
		public NodeSiemensFWServer( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensFWServer] {Name}";
	}
}
