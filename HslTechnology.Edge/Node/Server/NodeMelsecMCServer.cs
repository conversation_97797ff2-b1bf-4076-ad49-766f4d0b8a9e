using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.ComponentModel;
using HslCommunication.BasicFramework;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// 一个三菱MC协议的虚拟服务器节点对象
	/// </summary>
	public class NodeMelsecMCServer : ServerNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeMelsecMCServer( )
		{
			Name           = "Melsec MC 服务器";
			Description    = "这是一个三菱MC协议的服务器";
			DeviceType     = DeviceType.MelsecMCServer;
			Port           = 6000;
			IsBinary       = true;
			Protocol       = ProtocolType.TCP;
		}

		/// <summary>
		/// 通过指定的Xml配置信息来初始化设备对象
		/// </summary>
		/// <param name="element">Xml的设备配置信息</param>
		public NodeMelsecMCServer( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 当前的协议，可选 TCP，UDP
		/// </summary>
		[Category("PLC信息")]
		[DisplayName( "协议类型" )]
		[Description( "可选 TCP 和 UDP，根据实际的需求来选择" )]
		[DefaultValue( typeof(ProtocolType), "TCP" )]
		public ProtocolType Protocol { get; set; }

		/// <summary>
		/// 当前的协议是否是二进制的，如果是 ASCII，设置为 False
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "是否二进制" )]
		[Description( "报文格式，通常二进制和ASCII。默认二进制" )]
		[DefaultValue( true )]
		public bool IsBinary { get; set; }

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( IsBinary ), IsBinary.ToString( ) );
			element.SetAttributeValue( nameof( Protocol ), Protocol.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			IsBinary = bool.Parse( element.Attribute( nameof( IsBinary ) ).Value );
			Protocol = SoftBasic.GetEnumFromString<ProtocolType>( element.Attribute( nameof( Protocol ) ).Value );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[MelsecMCServer-{Protocol}] {Name}";
	}
}
