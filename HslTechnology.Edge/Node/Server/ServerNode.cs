using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Reflection;
using System.ComponentModel;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// 服务器节点的基类，包含了端口号信息，服务器类型，服务器创建时间等基本的要素
	/// </summary>
	public class ServerNode : Device.DeviceNode
	{
		#region Constructor

		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public ServerNode( )
		{
			Port        = 502;
			CreateTime  = DateTime.Now;
			Description = "这是一个基础的服务器";
			ActiveTime  = 7200;
		}

		#endregion

		/// <summary>
		/// 当前服务器的端口号信息
		/// </summary>
		[Category( "服务器信息" )]
		[DisplayName( "端口号" )]
		[Description( "服务器的端口号，需要唯一的，没被占用的。" )]
		[DefaultValue( 502 )]
		public int Port { get; set; }

		/// <summary>
		/// 客户端的活动时间，单位秒，客户端超过指定时间不进行数据交互，就判断为离线状态，进行强制下线
		/// </summary>
		[Category( "服务器信息" )]
		[DisplayName( "活动时间" )]
		[Description( "客户端的活动时间，单位秒，客户端超过指定时间不进行数据交互，就判断为离线状态，进行强制下线" )]
		[DefaultValue( 7200 )]
		public int ActiveTime { get; set; }

		/// <summary>
		/// 获取或设置当前的服务器的设备信息，在网关监视情况下是否显示出来，默认为显示
		/// </summary>
		[Category( "服务器信息" )]
		[DisplayName( "设备是否显示" )]
		[Description( "获取或设置当前的服务器的设备信息，在网关监视情况下是否显示出来，默认为显示" )]
		[DefaultValue( true )]
		public bool EdgeViewerShow { get; set; } = true;

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Port ),       Port );
			element.SetAttributeValue( nameof( ActiveTime ), ActiveTime.ToString( ) );
			element.SetAttributeValue( nameof( EdgeViewerShow ), EdgeViewerShow.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			Port               = Convert.ToInt32( element.Attribute( nameof( Port ) ).Value );
			ActiveTime         = int.Parse( element.Attribute( nameof( ActiveTime ) ).Value );
			EdgeViewerShow     = GetXmlValue( element, nameof( EdgeViewerShow ), EdgeViewerShow, bool.Parse );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[ServerNode] {Name}";
	}
}
