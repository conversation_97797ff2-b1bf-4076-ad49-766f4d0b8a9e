using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Linq;
using HslTechnology.Edge.Reflection;
using System.ComponentModel;
using System.IO.Ports;
using HslCommunication.Core;
using HslCommunication.BasicFramework;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// Modbus服务器对象
	/// </summary>
	public class NodeModbusServer : ServerNodeSerial
	{
		/// <summary>
		/// 实例化一个Modbus服务器的节点对象
		/// </summary>
		public NodeModbusServer( )
		{
			Name           = "Modbus 服务器";
			Description    = "这是一个Modbus服务器";
			DeviceType     = DeviceType.ModbusServer;
			Port           = 502;
			PortName       = "";
			BaudRate       = 9600;
			DataBits       = 8;
			StopBits       = StopBits.One;
			Parity         = Parity.Odd;
			Station        = 1;
		}

		/// <summary>
		/// 指定一个XML参数配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">Xml参数配置信息</param>
		public NodeModbusServer( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 客户端的站号
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "站号信息" )]
		[Description( "如果客户端请求的设备站号（有的称为设备标识单元）不一致，将不响应回复" )]
		[DefaultValue( (byte)1 )]
		public byte Station { get; set; }

		/// <summary>
		/// 字节分析是否颠倒
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "高低字节排列顺序" )]
		[Description( "指定4字节及以上的数据类型的字节排列顺序，影响范围：int,uint,long,ulong,float,double,需要和设置一致才能读取到正确的数据" )]
		[DefaultValue( typeof( DataFormat ), "ABCD" )]
		public DataFormat DataFormat { get; set; } = DataFormat.ABCD;

		/// <summary>
		/// 获取或设置Modbus的服务器的站号是否隔离，如果设置为True，每个站号的数据都是隔离的，将会占100M内存左右
		/// </summary>
		[Category( "Modbus信息" )]
		[DisplayName( "站号数据隔离" )]
		[Description( "获取或设置Modbus的服务器的站号是否隔离，如果设置为True，每个站号的数据都是隔离的，将会占100M内存左右" )]
		[DefaultValue( false )]
		public bool StationDataIsolation { get; set; }

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Station ),    Station );
			element.SetAttributeValue( nameof( DataFormat ), DataFormat.ToString( ) );
			element.SetAttributeValue( nameof( StationDataIsolation ), StationDataIsolation );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			this.Station              = GetXmlValue( element, nameof( Station ),              this.Station, byte.Parse );
			this.DataFormat           = GetXmlEnum(  element, nameof( DataFormat ),           this.DataFormat );
			this.StationDataIsolation = GetXmlValue( element, nameof( StationDataIsolation ), this.StationDataIsolation, bool.Parse );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[ModbusServer] {Name}";
	}
}
