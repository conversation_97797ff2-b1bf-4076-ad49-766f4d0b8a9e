using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// 西门子S7协议实现的虚拟服务器
	/// </summary>
	public class NodeSiemensS7Server : ServerNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeSiemensS7Server( )
		{
			Name        = "S7-1200 服务器";
			Description = "这是一个 S7-1200 服务器";
			DeviceType  = DeviceType.SiemensS7Server;
			Port        = 102;
		}

		/// <summary>
		/// 通过Xml配置文件来实例化一个对象
		/// </summary>
		/// <param name="element">Xml配置文件</param>
		public NodeSiemensS7Server( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override string ToString( ) => $"[SiemensS7Server] {Name}";
	}
}
