using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// 一个欧姆龙FINS协议的虚拟PLC对象
	/// </summary>
	public class NodeOmronFinsServer : ServerNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeOmronFinsServer( )
		{
			Name           = "Omron Fins 服务器";
			Description    = "这是一个欧姆龙FINS协议的服务器";
			DeviceType     = DeviceType.OmronFinsServer;
			Port           = 9600;
			Protocol       = ProtocolType.TCP;
		}

		/// <summary>
		/// 通过指定的Xml配置文件来实例化一个对象
		/// </summary>
		/// <param name="element">配置信息</param>
		public NodeOmronFinsServer( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 当前的协议，可选 TCP，UDP
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "协议类型" )]
		[Description( "可选 TCP 和 UDP，根据实际的需求来选择" )]
		[DefaultValue( typeof( ProtocolType ), "TCP" )]
		public ProtocolType Protocol { get; set; }

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( Protocol ), Protocol.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			this.Protocol = GetXmlEnum( element, nameof( Protocol ), this.Protocol );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[OmronFinsServer-{Protocol}] {Name}";
	}
}
