using HslTechnology.Edge.Reflection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// 带有串口和网口的服务器节点信息
	/// </summary>
	public class ServerNodeSerial : ServerNode
	{
		/// <summary>
		/// 实例化一个Modbus服务器的节点对象
		/// </summary>
		public ServerNodeSerial( )
		{
			PortName = "";
			BaudRate = 9600;
			DataBits = 8;
			StopBits = StopBits.One;
			Parity = Parity.Odd;
		}

		/// <summary>
		/// 指定一个XML参数配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">Xml参数配置信息</param>
		public ServerNodeSerial( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}


		/// <summary>
		/// 串口名称
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "端口名称" )]
		[Description( "串口信息，如果不需要启动modbus的串口协议，就设置为空" )]
		[DefaultValue( "" )]
		[TypeConverter( typeof( SerialPortConverter ) )]
		public string PortName { get; set; }

		/// <summary>
		/// 波特率
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "波特率" )]
		[Description( "实际设备连接服务器串行端口的波特率，常见值为：2400,4800,9600,19200,38400" )]
		[DefaultValue( 9600 )]
		public int BaudRate { get; set; }

		/// <summary>
		/// 标准数据位长度
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "数据位" )]
		[Description( "实际设备连接服务器串行端口的数据位长度，通常为 7, 8" )]
		[DefaultValue( 8 )]
		public int DataBits { get; set; }

		/// <summary>
		/// 标准停止位长度
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "停止位" )]
		[Description( "实际设备连接服务器串行端口的停止位信息，None: 没有，One: 1位，Two: 两位，OnePointFive: 一点五位" )]
		[DefaultValue( typeof( StopBits ), "One" )]
		public StopBits StopBits { get; set; }

		/// <summary>
		/// 奇偶校验检查
		/// </summary>
		[Category( "串口信息" )]
		[DisplayName( "奇偶校验位" )]
		[Description( "实际设备连接服务器串行端口的奇偶校验信息" )]
		[DefaultValue( typeof( Parity ), "Odd" )]
		[TypeConverter( typeof( HslTechnology.Edge.Node.Converter.ParityConverter ) )]
		public Parity Parity { get; set; }


		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( PortName ),   PortName );
			element.SetAttributeValue( nameof( BaudRate ),   BaudRate );
			element.SetAttributeValue( nameof( DataBits ),   DataBits );
			element.SetAttributeValue( nameof( StopBits ),   StopBits.ToString( ) );
			element.SetAttributeValue( nameof( Parity ),     Parity.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			this.PortName   = GetXmlValue( element, nameof( PortName ),   this.PortName, m => m );
			this.BaudRate   = GetXmlValue( element, nameof( BaudRate ),   this.BaudRate, int.Parse );
			this.DataBits   = GetXmlValue( element, nameof( DataBits ),   this.DataBits, int.Parse );
			this.StopBits   = GetXmlEnum(  element, nameof( StopBits ),   this.StopBits );
			this.Parity     = GetXmlEnum(  element, nameof( Parity ),     this.Parity );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[ServerNodeSerial] {Name}";
	}
}
