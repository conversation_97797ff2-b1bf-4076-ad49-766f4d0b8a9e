using HslCommunication.BasicFramework;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Server
{
	/// <summary>
	/// CIP协议的虚拟服务器
	/// </summary>
	public class NodeAllenBradleyCipServer : ServerNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public NodeAllenBradleyCipServer( )
		{
			Name               = "AllenBradley Cip 服务器";
			Description        = "这是一个CIP协议的服务器";
			DeviceType         = DeviceType.AllenBradleyCipServer;
			Port               = 44818;
			CreateTagWithWrite = false;
		}

		/// <summary>
		/// 通过指定的Xml配置信息来初始化设备对象
		/// </summary>
		/// <param name="element">Xml的设备配置信息</param>
		public NodeAllenBradleyCipServer( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 写入标签数据的时候，如果标签不存在，是否创建新的标签信息
		/// </summary>
		[Category( "PLC信息" )]
		[DisplayName( "写入时创建标签" )]
		[Description( "写入标签数据的时候，如果标签不存在，是否创建新的标签信息" )]
		[DefaultValue( false )]
		public bool CreateTagWithWrite { get; set; } = false;

		#region Override Method

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( CreateTagWithWrite ), CreateTagWithWrite.ToString( ) );
			return element;
		}

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			CreateTagWithWrite = bool.Parse( element.Attribute( nameof( CreateTagWithWrite ) ).Value );
		}

		#endregion

		/// <inheritdoc/>
		public override string ToString( ) => $"[NodeAllenBradleyCipServer] {Name}";
	}
}
