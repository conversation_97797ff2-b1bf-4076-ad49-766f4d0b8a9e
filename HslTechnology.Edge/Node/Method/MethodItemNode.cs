using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Method
{
	/// <summary>
	/// 单个方法的配置信息
	/// </summary>
	public class MethodItemNode : GroupNode
	{
		/// <summary>
		/// 实例化一个默认的对象
		/// </summary>
		public MethodItemNode( )
		{
			NodeType = NodeType.MethodItemConfig;
		}

		/// <summary>
		/// 通过XML配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">XML配置信息</param>
		public MethodItemNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		#region Xml Support

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			return element;
		}

		#endregion

	}
}
