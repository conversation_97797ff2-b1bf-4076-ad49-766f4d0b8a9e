using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Xml.Linq;

namespace HslTechnology.Edge.Node.Method
{
	/// <summary>
	/// 一个设备的所有的方法的配置节点信息
	/// </summary>
	public class MethodConfigNode : GroupNode
	{
		/// <summary>
		/// 实例化一个默认的对象<br />
		/// Instantiate a default object
		/// </summary>
		public MethodConfigNode( )
		{
			NodeType    = NodeType.MethodConfig;
			MethodItems = new List<MethodItemNode>( );
		}

		/// <summary>
		/// 指定XML配置信息来实例化一个对象
		/// </summary>
		/// <param name="element">Xml配置信息</param>
		public MethodConfigNode( XElement element ) : this( )
		{
			LoadByXmlElement( element );
		}

		/// <summary>
		/// 是否所有的方法的使能，开放给远程客户端读写
		/// </summary>
		[Category( "方法配置" )]
		[DisplayName( "激活所有方法接口" )]
		[Description( "是否所有的方法的使能，开放给远程客户端读写" )]
		public bool EnableAllMethod { get; set; }

		/// <summary>
		/// 所有的运行时的方法信息
		/// </summary>
		[Browsable( false )]
		public List<MethodItemNode> MethodItems { get; set; }

		#region Xml Support

		/// <inheritdoc/>
		public override void LoadByXmlElement( XElement element )
		{
			base.LoadByXmlElement( element );
			EnableAllMethod   = GetXmlValue( element, nameof( EnableAllMethod ),   EnableAllMethod,   bool.Parse );

			MethodItems = new List<MethodItemNode>( );
			foreach (XElement item in element.Elements( nameof( NodeType.MethodItemConfig ) ))
			{
				MethodItems.Add( new MethodItemNode( item ) );
			}
		}

		/// <inheritdoc/>
		public override XElement ToXmlElement( )
		{
			XElement element = base.ToXmlElement( );
			element.SetAttributeValue( nameof( EnableAllMethod ),   EnableAllMethod );
			foreach (MethodItemNode item in MethodItems)
			{
				element.Add( item.ToXmlElement( ) );
			}
			return element;
		}

		#endregion

	}
}
