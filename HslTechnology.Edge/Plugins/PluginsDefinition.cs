using HslCommunication.BasicFramework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using HslTechnology.Edge.Reflection;
using System.Xml.Linq;

namespace HslTechnology.Edge.Plugins
{
	/// <summary>
	/// 插件的定义类对象
	/// </summary>
	public class PluginsDefinition
	{
		/// <summary>
		/// 当前的插件的DLL的文件名称，例如 hugong.vulcanizer.dll
		/// </summary>
		public string DllName { get; set; }

		/// <summary>
		/// 公司的名称信息
		/// </summary>
		public string Company { get; set; }

		/// <summary>
		/// 当前公司的介绍信息
		/// </summary>
		public string Description { get; set; }

		/// <summary>
		/// 插件的网址信息
		/// </summary>
		public string Http { get; set; }

		/// <summary>
		/// 当前插件的框架信息
		/// </summary>
		public string Framework { get; set; }

		/// <summary>
		/// 当前的插件的版本
		/// </summary>
		[JsonConverter(typeof( JsonSystemVersionConverter ) )]
		public SystemVersion Version { get; set; }

		/// <summary>
		/// 当前的品牌的自定义的图标信息，需要为一个16*16大小的png,jpg格式的图片的内容
		/// </summary>
		public byte[] Icon16 { get; set; }

		/// <summary>
		/// 当前插件的发布日期
		/// </summary>
		public DateTime ReleaseData { get; set; }

		/// <summary>
		/// 设备定义的列表
		/// </summary>
		public Dictionary<string, PluginsDeviceDefinition> DeviceDefinitions { get; set; }

		/// <summary>
		/// 获取插件类型的字符串信息，如果没有定义的话，就使用dll本身的名字作为插件类型信息
		/// </summary>
		/// <returns>字符串内容</returns>
		public string GetPluginTypeString( )
		{
			if (this.DllName.EndsWith( ".dll", StringComparison.OrdinalIgnoreCase ))
				return this.DllName.Substring( 0, this.DllName.Length - 4 );
			return this.DllName;
		}
	}
}
