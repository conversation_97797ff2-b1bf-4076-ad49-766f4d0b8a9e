using HslTechnology.Edge.Node.Device;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Xml.Linq;
using System.Reflection;
using HslTechnology.Edge.Node;

namespace HslTechnology.Edge.Plugins
{
	/// <summary>
	/// 插件设备的定义内容
	/// </summary>
	public class PluginsDeviceDefinition
	{
		/// <summary>
		/// 默认的实例化构造方法
		/// </summary>
		public PluginsDeviceDefinition( )
		{

		}

		/// <summary>
		/// 根据传入的自定义对象，来初始化当前的设备插件对象
		/// </summary>
		/// <param name="plugin">设备对象</param>
		public PluginsDeviceDefinition( object plugin)
		{
			Type pluginType = plugin.GetType( );
			DeviceName = PluginsHelper.GetObjectProperty<string>(  pluginType, plugin, nameof( DeviceName ) );
			Description = PluginsHelper.GetObjectProperty<string>( pluginType, plugin, nameof( Description ) );

			Type nodeType = PluginsHelper.GetObjectProperty<Type>( pluginType, plugin, nameof( DeviceNode ) );
			if (nodeType == null)
			{
				// 扩展网关内置设备，但是不依赖网关库的插件实现
				string deviceType = PluginsHelper.GetObjectProperty<string>( pluginType, plugin, nameof( DeviceNode ) );
				// 然后去网关库里找信息
				if ( DeviceDefinitions.ContainsDeviceKey( deviceType ) )
				{
					DeviceItemDefinition deviceItem = DeviceDefinitions.Devices[deviceType];
					DeviceNode = deviceItem.DeviceNode;
					DeviveObject = deviceItem.DeviceObject;
					PluginDeivceType = PluginsHelper.GetObjectProperty<Type>( pluginType, plugin, nameof( DeviveObject ) );
					string pluginsType = PluginsHelper.GetObjectProperty<string>( pluginType, plugin, "PluginsType" );
					if (string.IsNullOrEmpty( pluginsType ) )
					{
						Tag = deviceType;
					}
					else
					{
						Tag = pluginsType;
					}
				}
			}
			else
			{
				// 完全自定义实现的插件类型
				DeviceNode = nodeType;
				PluginDeivceType = PluginsHelper.GetObjectProperty<Type>( pluginType, plugin, nameof( DeviveObject ) );  // 插件里设备的本身的类型，还需要做一层封装操作
				DeviveObject = typeof( DeviceCustomPlugins );
			}
		}


		/// <summary>
		/// 设备的基本名称信息
		/// </summary>
		public string DeviceName { get; set; }

		/// <inheritdoc cref="Node.Device.DeviceNode.IsSupportAddressRequest"/>
		public bool IsSupportAddressRequest { get; set; } = true;

		/// <inheritdoc cref="Node.Device.DeviceNode.GetEveryAddressOccupyByte(string)"/>
		[JsonIgnore]
		public Func<string, int> EveryAddressOccupyByteFunc { get; set; } = m => -1;

		/// <summary>
		/// 当前设备的描述信息
		/// </summary>
		public string Description { get; set; }

		/// <summary>
		/// 设备的节点类型信息
		/// </summary>
		[JsonIgnore]
		public Type DeviceNode { get; set; }

		/// <summary>
		/// 设备本身的图片信息，建议图片尺寸 128 * 128
		/// </summary>
		public byte[] Image { get; set; }

		/// <summary>
		/// 实际的设备类型对象信息
		/// </summary>
		[JsonIgnore]
		public Type DeviveObject { get; set; }

		/// <summary>
		/// 实际真实设备的插件类型
		/// </summary>
		[JsonIgnore]
		public Type PluginDeivceType { get; set; }

		/// <summary>
		/// 实际运行时用于携带额外的数据内容，在JSON中被忽略
		/// </summary>
		[JsonIgnore]
		public object Tag { get; set; }

		/// <summary>
		/// 如果当前的插件是基于模板的插件设置，则本对象有值。
		/// </summary>
		public string Template { get; set; }
	}
}
