using HslCommunication.BasicFramework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using HslTechnology.Edge.Reflection;
using System.Xml.Linq;
using HslTechnology.Edge.Device.Base;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Core;
using HslCommunication.LogNet;
using HslCommunication;
using Newtonsoft.Json.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using HslTechnology.Edge.Node.Device;
using HslTechnology.Edge.Device;
using HslTechnology.Edge.Node.Request;
using HslTechnology.Edge.Resources;
using HslTechnology.Edge.Node.Regular;
using HslTechnology.Edge.Node;
using System.Linq.Expressions;
using System.Net;


namespace HslTechnology.Edge.Plugins
{
	/// <summary>
	/// һ����ȫ�Զ�����豸ʵ��
	/// </summary>
	public class DeviceCustomPlugins : DeviceCoreBase
	{
		#region Constructor

		/// <summary>
		/// ʵ����һ��PLC�����ݷ��ʵ��豸����
		/// </summary>
		/// <param name="element">�豸��Xml������Ϣ</param>
		public DeviceCustomPlugins( XElement element, object actualDevice ) : base( element )
		{
			this.deviceXml = element;
			this.deviceNode = new DeviceNode( element, DeviceType.Plugins );

			this.SetDeviceObject( actualDevice );
			this.dictRpcApiInfos = EdgeReflectionHelper.GetMethodByDeviceObject( actualDevice, out this.methodRpcInfosBuffer );
		}

		/// <inheritdoc/>
		public override void IniDevice( XElement element, EdgeDeviceResources deviceResources )
		{
			base.IniDevice( element, deviceResources );

			if (this.deviceObject == null) return;
			Type deviceType = this.deviceObject.GetType( );

			MethodInfo method = deviceType.GetMethod( "IniDevice", new Type[] { typeof( XElement ) } );
			if (method != null)
			{
				method.Invoke( this.deviceObject, new object[] { element } );
			}


			MethodInfo methodDataFormat = deviceType.GetMethod( "GetDataFormat" );
			if (methodDataFormat != null && methodDataFormat.ReturnType == typeof( string ))
			{
				string dataformat = (string)methodDataFormat.Invoke( this.deviceObject, null );
				DataFormat format = (DataFormat)Enum.Parse( typeof( DataFormat ), dataformat );

				if (format == DataFormat.ABCD)
				{
					this.ByteTransform = new ReverseBytesTransform( );
				}
				else if (format == DataFormat.DCBA)
				{
					this.ByteTransform = new RegularByteTransform( );
				}
				else if (format == DataFormat.CDAB)
				{
					this.ByteTransform = new ReverseWordTransform( format );
				}
				else
				{
					this.ByteTransform = new ReverseBytesTransform( format );
				}
			}
		}

		/// <inheritdoc/>
		protected override XElement GetBuildInRequestRescources( )
		{
			MethodInfo method = this.deviceObject.GetType( ).GetMethod( "BuildInRequestRescources" );
			if (method != null && method.ReturnType == typeof( XElement ))
			{
				return (XElement)method.Invoke( this.deviceObject, null );
			}
			return base.GetBuildInRequestRescources( );
		}

		/// <inheritdoc cref="DeviceCoreBase.SetJsonValue(string, dynamic, bool)"/>
		private void SetJsonValueHelper( string name, object value, bool isArray )
		{
			SetJsonValue( name, value, isArray );
		}

		#endregion

		#region Override Method

		/// <inheritdoc/>
		protected override void BeforStart( )
		{
			if (this.deviceObject != null)
			{
				MethodInfo method = this.deviceObject.GetType( ).GetMethod( "BeforStart", BindingFlags.Instance | BindingFlags.Public );
				if (method != null)
				{
					try
					{
						method.Invoke( this.deviceObject, new object[0] );
					}
					catch (Exception ex)
					{
						this.LogNet?.WriteError( GetDeviceNameWithPath( ), "BeforStart", ex.Message );
					}
				}
			}
			base.BeforStart( );
		}

		/// <inheritdoc/>
		protected override void AfterClose( )
		{
			if (this.deviceObject != null)
			{
				MethodInfo method = this.deviceObject.GetType( ).GetMethod( "AfterClose", BindingFlags.Instance | BindingFlags.Public );
				if (method != null)
				{
					try
					{
						method.Invoke( this.deviceObject, new object[0] );
					}
					catch (Exception ex)
					{
						this.LogNet?.WriteError( GetDeviceNameWithPath( ), "AfterClose", ex.Message );
					}
				}
			}
			base.AfterClose( );
		}

		#endregion

		#region Plugins Method

		private Func<string, Task<T>> ExecuteReadValue<T>( Type type, object deviceObject, string methodName )
		{
			// �ҵ�����
			MethodInfo method = type.GetMethod( methodName, new Type[] { typeof( string ) } );
			if (method == null) return null;
			// �������������
			ConstantExpression target = Expression.Constant( deviceObject, type );
			ParameterExpression address = Expression.Parameter( typeof( string ), "address" );
			// ������ʽ
			Expression call = Expression.Call( target, method, address );

			var lambda = Expression.Lambda<Func<string, Task<T>>>( call, address );
			return lambda.Compile( );
		}

		private Func<string, int, Task<T>> ExecuteReadArray<T>( Type type, object deviceObject, string methodName )
		{
			// �ҵ�����
			MethodInfo method = type.GetMethod( methodName, new Type[] { typeof( string ), typeof( int ) } );
			if (method == null) return null;
			// �������������
			ConstantExpression target   = Expression.Constant( deviceObject, type );
			ParameterExpression address = Expression.Parameter( typeof( string ), "address" );
			ParameterExpression length  = Expression.Parameter( typeof( int ), "length" );
			// ������ʽ
			Expression call = Expression.Call( target, method, address, length );

			var lambda = Expression.Lambda<Func<string, int, Task<T>>>( call, address, length );
			return lambda.Compile( );
		}

		private Func<string, T, Task<int>> ExecuteWriteValue<T>( Type type, object deviceObject, string methodName )
		{
			// �ҵ�����
			MethodInfo method = type.GetMethod( methodName, new Type[] { typeof( string ), typeof( T ) } );
			if (method == null) return null;
			// �������������
			ConstantExpression target   = Expression.Constant( deviceObject, type );
			ParameterExpression address = Expression.Parameter( typeof( string ), "address" );
			ParameterExpression value   = Expression.Parameter( typeof( T ), "value" );
			// ������ʽ
			Expression call = Expression.Call( target, method, address, value );

			var lambda = Expression.Lambda<Func<string, T, Task<int>>>( call, address, value );
			return lambda.Compile( );
		}

		private Func<string, string, Encoding, Task<int>> ExecuteWriteString( Type type, object deviceObject, string methodName )
		{
			// �ҵ�����
			MethodInfo method = type.GetMethod( methodName, new Type[] { typeof( string ), typeof( string ), typeof( Encoding ) } );
			if (method == null) return null;
			// �������������
			ConstantExpression target = Expression.Constant( deviceObject, type );
			ParameterExpression address = Expression.Parameter( typeof( string ), "address" );
			ParameterExpression value = Expression.Parameter( typeof( string ), "value" );
			ParameterExpression encoding = Expression.Parameter( typeof( Encoding ), "encoding" );
			// ������ʽ
			Expression call = Expression.Call( target, method, address, value, encoding );

			var lambda = Expression.Lambda<Func<string, string, Encoding, Task<int>>>( call, address, value, encoding );
			return lambda.Compile( );
		}

		private Func<string, string, string> ExecuteAccessLevelFromRequest( Type type, object deviceObject, string methodName )
		{
			MethodInfo method = type.GetMethod( methodName );
			if (method == null) return null;

			// �������������
			ConstantExpression target = Expression.Constant( deviceObject, type );
			ParameterExpression address = Expression.Parameter( typeof( string ), "address" );
			ParameterExpression value = Expression.Parameter( typeof( string ), "dataType" );
			// ������ʽ
			Expression call = Expression.Call( target, method, address, value );

			var lambda = Expression.Lambda<Func<string, string, string>>( call, address, value );
			return lambda.Compile( );
		}

		private Func<string, int, int, string, string> ExecutePhysicalAddressFromSourceReadRequest( Type type, object deviceObject, string methodName )
		{
			MethodInfo method = type.GetMethod( methodName );
			if (method == null) return null;

			// �������������
			ConstantExpression target = Expression.Constant( deviceObject, type );
			ParameterExpression address = Expression.Parameter( typeof( string ), "address" );
			ParameterExpression byteOffset = Expression.Parameter( typeof( int ), "byteOffset" );
			ParameterExpression index = Expression.Parameter( typeof( int ), "index" );
			ParameterExpression dataType = Expression.Parameter( typeof( string ), "dataType" );

			// ������ʽ
			Expression call = Expression.Call( target, method, address, byteOffset, index, dataType );

			var lambda = Expression.Lambda<Func<string, int, int, string, string>>( call, address, byteOffset, index, dataType );
			return lambda.Compile( );
		}

		public void SetDeviceObject( object deviceObject )
		{
			this.deviceObject = deviceObject;
			Type type = deviceObject.GetType( );

			this.readBoolFunc         = ExecuteReadValue<bool>(      type, deviceObject, "ReadBool" );
			this.readBoolArrayFunc    = ExecuteReadArray<bool[]>(    type, deviceObject, "ReadBool" );
			this.readByteFunc         = ExecuteReadValue<byte>(      type, deviceObject, "ReadByte" );
			this.readByteArrayFunc    = ExecuteReadArray<byte[]>(    type, deviceObject, "Read" );
			this.readInt16Func        = ExecuteReadValue<short>(     type, deviceObject, "ReadInt16" );
			this.readInt16ArrayFunc   = ExecuteReadArray<short[]>(   type, deviceObject, "ReadInt16" );
			this.readUInt16Func       = ExecuteReadValue<ushort>(    type, deviceObject, "ReadUInt16" );
			this.readUInt16ArrayFunc  = ExecuteReadArray<ushort[]>(  type, deviceObject, "ReadUInt16" );
			this.readInt32Func        = ExecuteReadValue<int>(       type, deviceObject, "ReadInt32" );
			this.readInt32ArrayFunc   = ExecuteReadArray<int[]>(     type, deviceObject, "ReadInt32" );
			this.readUInt32Func       = ExecuteReadValue<uint>(      type, deviceObject, "ReadUInt32" );
			this.readUInt32ArrayFunc  = ExecuteReadArray<uint[]>(    type, deviceObject, "ReadUInt32" );
			this.readInt64Func        = ExecuteReadValue<long>(      type, deviceObject, "ReadInt64" );
			this.readInt64ArrayFunc   = ExecuteReadArray<long[]>(    type, deviceObject, "ReadInt64" );
			this.readUInt64Func       = ExecuteReadValue<ulong>(     type, deviceObject, "ReadUInt64" );
			this.readUInt64ArrayFunc  = ExecuteReadArray<ulong[]>(   type, deviceObject, "ReadUInt64" );
			this.readFloatFunc        = ExecuteReadValue<float>(     type, deviceObject, "ReadFloat" );
			this.readFloatArrayFunc   = ExecuteReadArray<float[]>(   type, deviceObject, "ReadFloat" );
			this.readDoubleFunc       = ExecuteReadValue<double>(    type, deviceObject, "ReadDouble" );
			this.readDoubleArrayFunc  = ExecuteReadArray<double[]>(  type, deviceObject, "ReadDouble" );
			this.readStringFunc       = ExecuteReadValue<string>(    type, deviceObject, "ReadString" );
			this.readStringArrayFunc  = ExecuteReadArray<string[]>(  type, deviceObject, "ReadString" );

			this.writeBoolFunc        = ExecuteWriteValue<bool>(     type, deviceObject, "WriteBool" );
			this.writeBoolArrayFunc   = ExecuteWriteValue<bool[]>(   type, deviceObject, "WriteBool" );
			this.writeByteFunc        = ExecuteWriteValue<byte>(     type, deviceObject, "WriteByte" );
			this.writeByteArrayFunc   = ExecuteWriteValue<byte[]>(   type, deviceObject, "Write" );
			this.writeInt16Func       = ExecuteWriteValue<short>(    type, deviceObject, "WriteInt16" );
			this.writeInt16ArrayFunc  = ExecuteWriteValue<short[]>(  type, deviceObject, "WriteInt16" );
			this.writeUInt16Func      = ExecuteWriteValue<ushort>(   type, deviceObject, "WriteUInt16" );
			this.writeUInt16ArrayFunc = ExecuteWriteValue<ushort[]>( type, deviceObject, "WriteUInt16" );
			this.writeInt32Func       = ExecuteWriteValue<int>(      type, deviceObject, "WriteInt32" );
			this.writeInt32ArrayFunc  = ExecuteWriteValue<int[]>(    type, deviceObject, "WriteInt32" );
			this.writeUInt32Func      = ExecuteWriteValue<uint>(     type, deviceObject, "WriteUInt32" );
			this.writeUInt32ArrayFunc = ExecuteWriteValue<uint[]>(   type, deviceObject, "WriteUInt32" );
			this.writeInt64Func       = ExecuteWriteValue<long>(     type, deviceObject, "WriteInt64" );
			this.writeInt64ArrayFunc  = ExecuteWriteValue<long[]>(   type, deviceObject, "WriteInt64" );
			this.writeUInt64Func      = ExecuteWriteValue<ulong>(    type, deviceObject, "WriteUInt64" );
			this.writeUInt64ArrayFunc = ExecuteWriteValue<ulong[]>(  type, deviceObject, "WriteUInt64" );
			this.writeFloatFunc       = ExecuteWriteValue<float>(    type, deviceObject, "WriteFloat" );
			this.writeFloatArrayFunc  = ExecuteWriteValue<float[]>(  type, deviceObject, "WriteFloat" );
			this.writeDoubleFunc      = ExecuteWriteValue<double>(   type, deviceObject, "WriteDouble" );
			this.writeDoubleArrayFunc = ExecuteWriteValue<double[]>( type, deviceObject, "WriteDouble" );
			this.writeStringFunc      = ExecuteWriteValue<string>(   type, deviceObject, "WriteString" );
			this.writeStringArrayFunc = ExecuteWriteValue<string[]>( type, deviceObject, "WriteString" );
			this.writeStringEncodingFunc = ExecuteWriteString( type, deviceObject, "WriteString" );

			// �����Ƿ��м�¼���ĵĹ��ܣ��еĻ������ø�ֵ
			PropertyInfo propertyTele = type.GetProperty( "LoggerTelegram" );
			if ( propertyTele != null && propertyTele.CanWrite && propertyTele.PropertyType == typeof( Action<string, byte[], bool> ))
			{
				propertyTele.SetValue( deviceObject, new Action<string, byte[], bool>( this.LoggerTelegram ) );
			}

			// �����Ƿ��������豸��ǰ���ݵĹ��ܣ��еĻ������ø�ֵ
			PropertyInfo propertySet = type.GetProperty( "SetJsonValue" );
			if (propertySet != null && propertySet.CanWrite && propertySet.PropertyType == typeof( Action<string, object, bool> ))
			{
				propertySet.SetValue( deviceObject, new Action<string, object, bool>( this.SetJsonValueHelper ) );
			}

			// �����Ƿ�����������Ȩ�޺�ԭʼ�ֽ�������������ַ��
			this.GetAccessLevelFromRequestFunc = ExecuteAccessLevelFromRequest( type, deviceObject, "ExecuteAccessLevelFromRequest" );
			this.CalculatePhysicalAddressFromSourceReadRequestFunc = ExecutePhysicalAddressFromSourceReadRequest( type, deviceObject, "CalculatePhysicalAddressFromSourceReadRequest" );

			this.everySecondsExecuteMethod = EdgeReflectionHelper.ExecuteTimerMethod( type, deviceObject, "EverySecondsExecuteMethod" );
			this.everyMinuteExecuteMethod  = EdgeReflectionHelper.ExecuteTimerMethod( type, deviceObject, "EveryMinuteExecuteMethod" );
			this.everyHourExecuteMethod    = EdgeReflectionHelper.ExecuteTimerMethod( type, deviceObject, "EveryHourExecuteMethod" );
			this.everyDayExecuteMethod     = EdgeReflectionHelper.ExecuteTimerMethod( type, deviceObject, "EveryDayExecuteMethod" );
		}

		// ��ȡ
		private Func<string, Task<bool>>          readBoolFunc = null;
		private Func<string, int, Task<bool[]>>   readBoolArrayFunc = null;
		private Func<string, Task<byte>>          readByteFunc = null;
		private Func<string, int, Task<byte[]>>   readByteArrayFunc = null;
		private Func<string, Task<short>>         readInt16Func = null;
		private Func<string, int, Task<short[]>>  readInt16ArrayFunc = null;
		private Func<string, Task<ushort>>        readUInt16Func = null;
		private Func<string, int, Task<ushort[]>> readUInt16ArrayFunc = null;
		private Func<string, Task<int>>           readInt32Func = null;
		private Func<string, int, Task<int[]>>    readInt32ArrayFunc = null;
		private Func<string, Task<uint>>          readUInt32Func = null;
		private Func<string, int, Task<uint[]>>   readUInt32ArrayFunc = null;
		private Func<string, Task<long>>          readInt64Func = null;
		private Func<string, int, Task<long[]>>   readInt64ArrayFunc = null;
		private Func<string, Task<ulong>>         readUInt64Func = null;
		private Func<string, int, Task<ulong[]>>  readUInt64ArrayFunc = null;
		private Func<string, Task<float>>         readFloatFunc = null;
		private Func<string, int, Task<float[]>>  readFloatArrayFunc = null;
		private Func<string, Task<double>>        readDoubleFunc = null;
		private Func<string, int, Task<double[]>> readDoubleArrayFunc = null;
		private Func<string, Task<string>>        readStringFunc = null;
		private Func<string, int, Task<string[]>> readStringArrayFunc = null;

		// д��
		private Func<string, bool, Task<int>>     writeBoolFunc = null;
		private Func<string, bool[], Task<int>>   writeBoolArrayFunc = null;
		private Func<string, byte, Task<int>>     writeByteFunc = null;
		private Func<string, byte[], Task<int>>   writeByteArrayFunc = null;
		private Func<string, short, Task<int>>    writeInt16Func = null;
		private Func<string, short[], Task<int>>  writeInt16ArrayFunc = null;
		private Func<string, ushort, Task<int>>   writeUInt16Func = null;
		private Func<string, ushort[], Task<int>> writeUInt16ArrayFunc = null;
		private Func<string, int, Task<int>>      writeInt32Func = null;
		private Func<string, int[], Task<int>>    writeInt32ArrayFunc = null;
		private Func<string, uint, Task<int>>     writeUInt32Func = null;
		private Func<string, uint[], Task<int>>   writeUInt32ArrayFunc = null;
		private Func<string, long, Task<int>>     writeInt64Func = null;
		private Func<string, long[], Task<int>>   writeInt64ArrayFunc = null;
		private Func<string, ulong, Task<int>>    writeUInt64Func = null;
		private Func<string, ulong[], Task<int>>  writeUInt64ArrayFunc = null;
		private Func<string, float, Task<int>>    writeFloatFunc = null;
		private Func<string, float[], Task<int>>  writeFloatArrayFunc = null;
		private Func<string, double, Task<int>>   writeDoubleFunc = null;
		private Func<string, double[], Task<int>> writeDoubleArrayFunc = null;
		private Func<string, string, Task<int>>   writeStringFunc = null;
		private Func<string, string[], Task<int>> writeStringArrayFunc = null;
		private Func<string, string, Encoding, Task<int>> writeStringEncodingFunc = null;

		// ��ʱ
		private Func<int, Task> everySecondsExecuteMethod = null;
		private Func<int, Task> everyMinuteExecuteMethod = null;
		private Func<int, Task> everyHourExecuteMethod = null;
		private Func<int, Task> everyDayExecuteMethod = null;



		private string getExceptionMessage( Exception ex )
		{
			return ex.InnerException == null ? ex.Message : ex.InnerException.Message;
		}

		#endregion

		#region XML Convert

		public OperateResult<T> ConvertToValue<T>( string xml, Func<string, T> trans )
		{
			XElement ele = XElement.Parse( xml );
			bool success = GroupNode.GetXmlValue( ele, nameof( OperateResult.IsSuccess ), false, bool.Parse );
			if (!success)
			{
				string message = GroupNode.GetXmlValue( ele, nameof( OperateResult.Message ), "", m => m );
				int error = GroupNode.GetXmlValue( ele, nameof( OperateResult.ErrorCode ), -1, int.Parse );
				return new OperateResult<T>( message );
			}

			return OperateResult.CreateSuccessResult( GroupNode.GetXmlValue( ele, nameof( OperateResult<T>.Content ), default( T ), trans ) );
		}

		#endregion

		#region Write Support

		/// <inheritdoc/>
		public override OperateResult WriteValueByName( DeviceSingleAddressLabel addressLabel, string value )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			return WriteValueByScalarRequest( addressLabel.PhysicalAddress, addressLabel.ScalarTransform, value );
		}

		private OperateResult GetWriteValueHelper<T>( IScalarTransform transform, string address, T[] value, Func<string, T[], OperateResult> write )
		{
			if (value.Length <= transform.Length)
				return write( address, value );
			else
				return new OperateResult( $"д��� [{transform.DataTypeCode}] ���鳤��̫��������ԭ���Ķ���" );
		}

		private OperateResult WriteArrayHelper<T>( IScalarTransform transform, string address, T[] value, Func<string, T[], Task<int>> write )
		{
			if (write == null) return new OperateResult( EdgeStringResource.MethodNullException );
			if (value.Length <= transform.Length)
			{
				try
				{
					int _ = write( address, value ).Result;
					return OperateResult.CreateSuccessResult( );
				}
				catch ( Exception ex )
				{
					return new OperateResult( ex.Message );
				}
			}
			else
				return new OperateResult( $"д��� [{transform.DataTypeCode}] ���鳤��̫��������ԭ���Ķ���" );
		}

		private OperateResult WriteValueHelper<T>( string address, T value, Func<string, T, Task<int>> write )
		{
			if (write == null) return new OperateResult( EdgeStringResource.MethodNullException );

			try
			{
				int _ = write( address, value ).Result;
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				return new OperateResult( ex.Message );
			}
		}

		private async Task<OperateResult> WriteValueHelperAsync<T>( string address, T value, Func<string, T, Task<int>> write )
		{
			if (write == null) return new OperateResult( EdgeStringResource.MethodNullException );

			try
			{
				int _ = await write( address, value );
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				return new OperateResult( ex.Message );
			}
		}

		private OperateResult WriteStringEncodingHelper( string address, string value, Encoding encoding, Func<string, string, Encoding, Task<int>> write )
		{
			if (write == null) return new OperateResult( EdgeStringResource.MethodNullException );

			try
			{
				int _ = write( address, value, encoding ).Result;
				return OperateResult.CreateSuccessResult( );
			}
			catch (Exception ex)
			{
				return new OperateResult( ex.Message );
			}
		}

		/// <summary>
		/// ���ݱ������ݵ��������ݣ��Ѵ�����ַ������ݣ�����<see cref="IScalarTransform"/>ָ����ת������ת�ɶ�Ӧ�����ݣ�Ȼ��д�뵽�豸��ȥ��
		/// </summary>
		/// <param name="readWriteDevice">�豸��ͨ�Ŷ���</param>
		/// <param name="address">��ַ��Ϣ</param>
		/// <param name="transform">����ת������</param>
		/// <param name="value">�ȴ�д��ֵ���ַ���</param>
		/// <returns>�Ƿ�д��ɹ�</returns>
		protected virtual OperateResult WriteValueByScalarRequest( string address, IScalarTransform transform, string value )
		{
			if (transform.DataTypeCode == RegularNodeTypeItem.Bool.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetBoolValue( transform, value ), this.writeBoolFunc );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetBoolArrayValue( transform, value ), this.writeBoolArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetBoolValue( transform, value ) ? (byte)0x01 : (byte)0x00, this.writeByteFunc );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetBoolArrayValue( transform, value ).Select( m => m ? (byte)0x01 : (byte)0x00 ).ToArray( ), this.writeByteArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Byte.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetByteValue( transform, value ), this.writeByteFunc );
				else
					return WriteArrayHelper( transform, address, Regex.IsMatch( value, @"^[0-9a-fA-F ]+$" ) ? value.ToHexBytes( ) : value.ToStringArray<byte>( ), this.writeByteArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.SByte.Text)
			{
				if (transform.Length < 0) return WriteValueHelper( address, (byte)RegularHelper.GetSByteValue( transform, value ), this.writeByteFunc );

				byte[] array = HslTechnologyHelper.GetByteArrayFrom( RegularHelper.GetSByteArrayValue( transform, value ) );
				return WriteArrayHelper( transform, address, array, this.writeByteArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int16.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetInt16Value( transform, value ), this.writeInt16Func );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetInt16ArrayValue( transform, value ), this.writeInt16ArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt16.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetUInt16Value( transform, value ), this.writeUInt16Func );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetUInt16ArrayValue( transform, value ), this.writeUInt16ArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int32.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetInt32Value( transform, value ), this.writeInt32Func );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetInt32ArrayValue( transform, value ), this.writeInt32ArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt32.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetUInt32Value( transform, value ), this.writeUInt32Func );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetUInt32ArrayValue( transform, value ), this.writeUInt32ArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Int64.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetInt64Value( transform, value ), this.writeInt64Func );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetInt64ArrayValue( transform, value ), this.writeInt64ArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetUInt64Value( transform, value ), this.writeUInt64Func );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetUInt64ArrayValue( transform, value ), this.writeUInt64ArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Float.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetFloatValue( transform, value ), this.writeFloatFunc );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetFloatArrayValue( transform, value ), this.writeFloatArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.Double.Text)
			{
				if (transform.Length < 0)
					return WriteValueHelper( address, RegularHelper.GetDoubleValue( transform, value ), this.writeDoubleFunc );
				else
					return WriteArrayHelper( transform, address, RegularHelper.GetDoubleArrayValue( transform, value ), this.writeDoubleArrayFunc );
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.String.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.IntOfString.Text ||
				transform.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text
				)
			{
				try
				{
					if (transform is ScalarReadRequest request)   // ���������
					{
						int maxLength = request.StringLength * this.deviceNode.GetEveryAddressOccupyByte( request.Address );
						if (transform.Length < 0)
						{
							return WriteStringEncodingHelper( address, value, RegularScalarNode.GetEncoding( request.StringEncoding ), this.writeStringEncodingFunc );

							//if (maxLength < 0)
								//return WriteStringEncodingHelper( address, value, RegularScalarNode.GetEncoding( request.StringEncoding ), this.writeStringEncodingFunc );
							//else
								//return WriteStringEncodingHelper( address, value, maxLength, RegularScalarNode.GetEncoding( request.StringEncoding ), this.writeStringEncodingFunc );
						}
						else
						{
							byte[] buffer = new byte[transform.Length * maxLength];
							string[] strings = JArray.Parse( value ).Values<string>( ).ToArray( );
							for (int i = 0; i < strings.Length; i++)
							{
								if (i < transform.Length)
								{
									byte[] temp = RegularScalarNode.GetEncoding( request.StringEncoding ).GetBytes( strings[i] );
									SoftBasic.ArrayExpandToLength( temp, maxLength ).CopyTo( buffer, i * maxLength );
								}
								else
								{
									break;
								}
							}
							return WriteValueHelper( address, buffer, this.writeByteArrayFunc );
						}
					}
					else if (transform is RegularScalarNode regularScalarNode)  // �ṹ����������
					{
						if (regularScalarNode.Length < 0)
						{
							byte[] source = regularScalarNode.GetSourceValueFromString( value, this.ByteTransform );
							if (source != null && regularScalarNode.StringSourceLengthToEven && source.Length % 2 == 1)
							{
								source = SoftBasic.SpliceArray( source, new byte[] { 0x00 } );
							}
							return WriteValueHelper( address, source, this.writeByteArrayFunc );
						}
						else
						{
							byte[] source = regularScalarNode.GetSourceValueFromStringArray( value, this.ByteTransform );
							return WriteValueHelper( address, source, this.writeByteArrayFunc );
						}
					}
					return WriteValueHelper( address, value, this.writeStringFunc );
				}
				catch (Exception ex)
				{
					return new OperateResult( $"��ǰд�����ʧ�ܣ���Ҫ����Ϊ {RegularNodeTypeItem.String.Text} �����ݣ�����ת��ʧ�ܣ�" + ex.Message );
				}
			}
			else if (transform.DataTypeCode == RegularNodeTypeItem.BCD.Text)
			{
				if (transform.Length < 0)
				{
					// ����������д�����
					OperateResult<byte[]> buffer = RegularScalarNode.GetBytesFromBCD( value, transform, this.deviceNode );
					if (!buffer.IsSuccess) return buffer;

					return WriteValueHelper( address, buffer.Content, this.writeByteArrayFunc );
				}
				else
				{
					// �����д����������
					OperateResult<byte[]> buffer = RegularScalarNode.GetBytesFromBCDArray( value, transform, this.deviceNode );
					if (!buffer.IsSuccess) return buffer;

					return WriteValueHelper( address, buffer.Content, this.writeByteArrayFunc );
				}
			}
			else
				return new OperateResult( $"��ǰ����������[{transform.DataTypeCode}] ��ʱ��֧��д�����" );
		}

		#endregion

		#region Read Support

		private async Task<OperateResult<T>> ReadValueHelper<T>( string address, Func<string, Task<T>> func )
		{
			try
			{
				return OperateResult.CreateSuccessResult( await func( address ).ConfigureAwait( false ) );
			}
			catch (Exception ex)
			{
				return new OperateResult<T>( getExceptionMessage( ex ) );
			}
		}
		private async Task<OperateResult<T[]>> ReadArrayHelper<T>( string address, int length, Func<string, int, Task<T[]>> func )
		{
			try
			{
				return OperateResult.CreateSuccessResult( await func( address, length ).ConfigureAwait( false ) );
			}
			catch (Exception ex)
			{
				return new OperateResult<T[]>( getExceptionMessage( ex ) );
			}
		}

		/// <summary>
		/// ���豸��ȡ��ʵ��������Ϣ�����ݵ�ǰ�ı�����������
		/// </summary>
		/// <param name="scalarRequest">�����������������</param>
		/// <returns>�Ƿ�����ɹ�������ֵ���������������</returns>
		protected virtual async Task<OperateResult> ReadActualBoolAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readBoolFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<bool> read = await ReadValueHelper( scalarRequest.Address, this.readBoolFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readBoolArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<bool[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readBoolArrayFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualByteAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readByteFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<byte> read = await ReadValueHelper( scalarRequest.Address, this.readByteFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readByteArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<byte[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readByteArrayFunc ).ConfigureAwait( false );       // �ֽ������ȡ����ת������
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualSByteAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readByteFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<byte> read = await ReadValueHelper( scalarRequest.Address, this.readByteFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read.IsSuccess ? OperateResult.CreateSuccessResult( (sbyte)read.Content ) : OperateResult.CreateFailedResult<sbyte>( read ) );
			}
			else
			{
				if (this.readByteArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<byte[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readByteArrayFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, HslTechnologyHelper.ConvertFromByte( read ) );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualInt16Async( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readInt16Func == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<short> read = await ReadValueHelper( scalarRequest.Address, this.readInt16Func ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readInt16ArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<short[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readInt16ArrayFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualUInt16Async( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readUInt16Func == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<ushort> read = await ReadValueHelper( scalarRequest.Address, this.readUInt16Func ).ConfigureAwait ( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readUInt16ArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<ushort[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readUInt16ArrayFunc ).ConfigureAwait ( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualInt32Async( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readInt32Func == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<int> read = await ReadValueHelper( scalarRequest.Address, this.readInt32Func ).ConfigureAwait ( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readInt32ArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<int[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readInt32ArrayFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualUInt32Async( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readUInt32Func == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<uint> read = await ReadValueHelper( scalarRequest.Address, this.readUInt32Func ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readUInt32ArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<uint[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readUInt32ArrayFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualInt64Async( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readInt64Func == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<long> read = await ReadValueHelper( scalarRequest.Address, this.readInt64Func ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readInt64ArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<long[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readInt64ArrayFunc ).ConfigureAwait ( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualUInt64Async( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readUInt64Func == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<ulong> read = await ReadValueHelper( scalarRequest.Address, this.readUInt64Func ).ConfigureAwait ( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readUInt64ArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<ulong[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readUInt64ArrayFunc ).ConfigureAwait ( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualFloatAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readFloatFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<float> read = await ReadValueHelper( scalarRequest.Address, this.readFloatFunc ).ConfigureAwait ( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readFloatArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<float[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readFloatArrayFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualDoubleAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (scalarRequest.Length < 0)
			{
				if (this.readDoubleFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<double> read = await ReadValueHelper( scalarRequest.Address, this.readDoubleFunc ).ConfigureAwait( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
			else
			{
				if (this.readDoubleArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
				OperateResult<double[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readDoubleArrayFunc ).ConfigureAwait ( false );
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
			}
		}

		private async Task<OperateResult> ScalarReadRequestStringAsync( ScalarReadRequest scalarRequest )
		{
			// �ȶ�ȡ�ֽ����飬Ȼ�������ʵ�ʵ��ַ���������Ϣ
			Encoding encoding = RegularScalarNode.GetEncoding( scalarRequest.StringEncoding );
			if (scalarRequest.Length < 0)
			{
				string value = null;
				if (this.readStringFunc == null)
				{
					OperateResult<byte[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.StringLength, this.readByteArrayFunc ).ConfigureAwait( false );
					if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateFailedResult<string>( read ) );
					value = encoding.GetString( read.Content );
				}
				else
				{
					OperateResult<string> read = await ReadValueHelper( scalarRequest.Address, this.readStringFunc ).ConfigureAwait( false );
					if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, read );
					value = read.Content;
				}

				if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToInt64( value ) ) );
				else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( Convert.ToDouble( value ) ) );
				else
					return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( value ) );
			}
			else
			{
				if (this.readStringArrayFunc == null)
				{
					OperateResult<byte[]> read = await ReadArrayHelper( scalarRequest.Address, (ushort)(scalarRequest.StringLength * scalarRequest.Length), this.readByteArrayFunc ).ConfigureAwait( false );
					if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateFailedResult<string[]>( read ) );

					string[] values = new string[scalarRequest.Length];
					int everyLength = read.Content.Length / scalarRequest.Length;
					for (int i = 0; i < scalarRequest.Length; i++)
					{
						values[i] = encoding.GetString( read.Content, i * everyLength, everyLength );
					}

					if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
						return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values.Select( m => Convert.ToInt64( m ) ).ToArray( ) ) );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
						return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values.Select( m => Convert.ToDouble( m ) ).ToArray( ) ) );
					else
						return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values ) );
				}
				else
				{
					OperateResult<string[]> read = await ReadArrayHelper( scalarRequest.Address, scalarRequest.Length, this.readStringArrayFunc ).ConfigureAwait( false );
					if (!read.IsSuccess) return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateFailedResult<string[]>( read ) );

					string[] values = read.Content;

					if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)
						return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values.Select( m => Convert.ToInt64( m ) ).ToArray( ) ) );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text)
						return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values.Select( m => Convert.ToDouble( m ) ).ToArray( ) ) );
					else
						return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values ) );
				}
			}
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			return await ScalarReadRequestStringAsync( scalarRequest ).ConfigureAwait( false );
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualIntOfStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			return await ScalarReadRequestStringAsync( scalarRequest ).ConfigureAwait( false );
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualDoubleOfStringAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult<string>( -1, EdgeStringResource.DeviceNullException );

			return await ScalarReadRequestStringAsync( scalarRequest ).ConfigureAwait( false );
		}

		/// <inheritdoc cref="ReadActualBoolAsync(ScalarReadRequest)"/>
		protected virtual async Task<OperateResult> ReadActualBCDAsync( ScalarReadRequest scalarRequest )
		{
			if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
			if (this.readByteArrayFunc == null) return new OperateResult( -1, EdgeStringResource.MethodNullException );
			if (scalarRequest.Length < 0)
			{
				// ��ȡbyte
				OperateResult<byte[]> read = OperateResult.CreateSuccessResult( await this.readByteArrayFunc( scalarRequest.Address, (ushort)scalarRequest.StringLength ).ConfigureAwait( false ) );
				if (!read.IsSuccess) return DealWithReadResult( OperateResult.CreateFailedResult<string>( read ), null );

				// ��ֵ������
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( RegularScalarNode.GetBCDValue( read.Content, scalarRequest.BCDFormat ) ) );
			}
			else
			{
				// ��ȡbyte
				OperateResult<byte[]> read = OperateResult.CreateSuccessResult( await this.readByteArrayFunc( scalarRequest.Address, (ushort)(scalarRequest.StringLength * scalarRequest.Length) ).ConfigureAwait( false ) );
				if (!read.IsSuccess) return DealWithReadResult( OperateResult.CreateFailedResult<string>( read ), null );

				int everyLength = scalarRequest.Length == 0 ? read.Content.Length : (read.Content.Length / scalarRequest.Length);
				string[] values = new string[scalarRequest.Length];
				for (int i = 0; i < scalarRequest.Length; i++)
				{
					byte[] buffer = read.Content.SelectMiddle( i * everyLength, everyLength );
					values[i] = RegularScalarNode.GetBCDValue( buffer, scalarRequest.BCDFormat );
				}
				// ��ֵ������
				return scalarRequest.DeviceActual.BussinessDataHelper( scalarRequest.Name, scalarRequest, OperateResult.CreateSuccessResult( values ) );
			}
		}

		/// <summary>
		/// ��ȡԭʼ�ֽڵ�����
		/// </summary>
		/// <param name="sourceReadRequest">ԭʼ���ֽڵ�����</param>
		/// <returns>��������Ϣ</returns>
		public virtual async Task<OperateResult<byte[]>> ReadActualSourceRequest( SourceReadRequest sourceReadRequest )
		{
			if (sourceReadRequest.Address.Contains( ";" ) && sourceReadRequest.Length.Contains( ";" ))
			{
				try
				{
					string[] address = sourceReadRequest.Address.Split( new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries );
					ushort[] length = sourceReadRequest.Length.Split( new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries ).Select( m => ushort.Parse( m ) ).ToArray( );

					OperateResult<byte[]> read = await ReadBatch( address, length );
					return DealWithSourceReadResult( read, content => ParseFromRequest( content, sourceReadRequest, ByteTransform ) );
				}
				catch (Exception ex)
				{
					this.edgeResources.FatalMessage.AddMessage( SoftBasic.GetExceptionMessage( ex ) );
					return new OperateResult<byte[]>( $"SourceRead Length[{sourceReadRequest.Length}] formate wrong: " + ex.Message );
				}
			}
			else
			{
				if (ushort.TryParse( sourceReadRequest.Length, out ushort length ))
				{
					try
					{
						OperateResult<byte[]> read = this.readByteArrayFunc == null ? new OperateResult<byte[]>( -1, EdgeStringResource.MethodNullException ) :
							OperateResult.CreateSuccessResult( await this.readByteArrayFunc( sourceReadRequest.Address, length ).ConfigureAwait( false ) );
						return DealWithSourceReadResult( read, content => ParseFromRequest( content, sourceReadRequest, ByteTransform ) );
					}
					catch (Exception ex)
					{
						this.edgeResources.FatalMessage.AddMessage( SoftBasic.GetExceptionMessage( ex ) );
						return new OperateResult<byte[]>( "SourceRead Request Length wrong: " + ex.Message );
					}
				}
				else
					return new OperateResult<byte[]>( "SourceRead Request Length formate wrong: " + sourceReadRequest.Length );
			}
		}

		/// <summary>
		/// ������ȡ��ַ��ԭʼ�ֽ���Ϣ�������Щ�豸֧�֣��������д֧��
		/// </summary>
		/// <param name="address">��ַ������Ϣ</param>
		/// <param name="length">����������Ϣ</param>
		/// <returns>���ص�ԭʼ�ֽ�������Ϣ</returns>
		protected virtual Task<OperateResult<byte[]>> ReadBatch( string[] address, ushort[] length )
		{
			return Task.FromResult( new OperateResult<byte[]>( "Not Supported Request" ) );
		}

		/// <inheritdoc/>
		protected override async Task<OperateResult> ReadActualAsync( RequestBase request )
		{
			if (request.RequestType == RequestType.ScalarRead)
			{
				try
				{
					ScalarReadRequest scalarRequest = (ScalarReadRequest)request;
					if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Bool.Text)                return await ReadActualBoolAsync(           scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)     return await ReadActualBoolAsync(           scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Byte.Text)           return await ReadActualByteAsync(           scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.SByte.Text)          return await ReadActualSByteAsync(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int16.Text)          return await ReadActualInt16Async(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt16.Text)         return await ReadActualUInt16Async(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int32.Text)          return await ReadActualInt32Async(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt32.Text)         return await ReadActualUInt32Async(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Int64.Text)          return await ReadActualInt64Async(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.UInt64.Text)         return await ReadActualUInt64Async(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Float.Text)          return await ReadActualFloatAsync(          scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.Double.Text)         return await ReadActualDoubleAsync(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.String.Text)         return await ReadActualStringAsync(         scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.IntOfString.Text)    return await ReadActualIntOfStringAsync(    scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.DoubleOfString.Text) return await ReadActualDoubleOfStringAsync( scalarRequest ).ConfigureAwait( false );
					else if (scalarRequest.DataTypeCode == RegularNodeTypeItem.BCD.Text)            return await ReadActualBCDAsync(            scalarRequest ).ConfigureAwait( false );
					else return new OperateResult( "Not implementation type: " + scalarRequest.DataTypeCode );
				}
				catch (Exception ex)
				{
					// ���Ƿ������ش��쳣����¼ϵͳ���쳣��Ϣ��
					this.edgeResources.FatalMessage.AddMessage( SoftBasic.GetExceptionMessage( ex ) );
					return new OperateResult( "ReadActualAsync failed: " + ex.Message );
				}
			}
			else if (request.RequestType == RequestType.SourceRead)
			{
				if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );

				SourceReadRequest sourceReadRequest = (SourceReadRequest)request;
				OperateResult<byte[]> read = null;
				if (sourceReadRequest.Address.StartsWith( "var=" ) && !sourceReadRequest.Address.Contains( ";" ))
				{
					// ����ڵ��������ص������ڵ�
					read = this.edgeResources.EdgeServices.GetSourceRequestCahce( sourceReadRequest.Address.Substring( 4 ) );
					DealWithSourceReadResult( read, content => ParseFromRequest( content, sourceReadRequest, ByteTransform ) );
				}
				else
				{
					read = await ReadActualSourceRequest( sourceReadRequest );
				}

				lock (this.localSourceRequestCache)
				{
					if (this.localSourceRequestCache.ContainsKey( request.Name ))
						this.localSourceRequestCache[request.Name] = read;
					else
						this.localSourceRequestCache.Add( request.Name, read );
				}
				return read;
			}
			else
			{
				return new OperateResult( "Not Supported Request" );
			}
		}


		/// <inheritdoc/>
		protected async override Task<OperateResult> WriteActualAsync( ScalarWriteRequest request, string value )
		{
			if (request.RequestType == RequestType.WriteInterval)
			{
				try
				{
					if (this.deviceObject == null) return new OperateResult( -1, EdgeStringResource.DeviceNullException );
					bool isArray = value.StartsWith( "[" ) && value.EndsWith( "]" );
					if (request.DataTypeCode == RegularNodeTypeItem.Bool.Text)
					{
						if (!isArray) 
							return await WriteValueHelperAsync( request.Address, bool.Parse( value ), this.writeBoolFunc ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, RegularHelper.GetBoolArrayValue( transform: null, value ), this.writeBoolArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.BoolOfByte.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, RegularHelper.GetBoolValue( transform: null, value ) ? (byte)0x01 : (byte)0x00, this.writeByteFunc ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, RegularHelper.GetBoolArrayValue( transform: null, value ).Select( m => m ? (byte)0x01 : (byte)0x00 ).ToArray( ), this.writeByteArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Byte.Text)
					{
						if (!request.IsArray)
							return await WriteValueHelperAsync( request.Address, byte.Parse( value ), this.writeByteFunc).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToHexBytes( ), this.writeByteArrayFunc).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.SByte.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, (byte)sbyte.Parse( value ), this.writeByteFunc).ConfigureAwait ( false );
						else
							return await WriteValueHelperAsync( request.Address, HslTechnologyHelper.GetByteArrayFrom( value.ToStringArray<sbyte>( ) ), this.writeByteArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Int16.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, short.Parse( value ), this.writeInt16Func ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToStringArray<short>( ), this.writeInt16ArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.UInt16.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, ushort.Parse( value ), this.writeUInt16Func ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToStringArray<ushort>( ), this.writeUInt16ArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Int32.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, int.Parse( value ), this.writeInt32Func ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToStringArray<int>( ), this.writeInt32ArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.UInt32.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, uint.Parse( value ), this.writeUInt32Func ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToStringArray<uint>( ), this.writeUInt32ArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Int64.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, long.Parse( value ), this.writeInt64Func ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToStringArray<long>( ), this.writeInt64ArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.UInt64.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, ulong.Parse( value ), this.writeUInt64Func ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToStringArray<ulong>( ), this.writeUInt64ArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Float.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, float.Parse( value ), this.writeFloatFunc ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToStringArray<float>( ), this.writeFloatArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.Double.Text)
					{
						if (!isArray)
							return await WriteValueHelperAsync( request.Address, double.Parse( value ), this.writeDoubleFunc ).ConfigureAwait( false );
						else
							return await WriteValueHelperAsync( request.Address, value.ToStringArray<double>( ), this.writeDoubleArrayFunc ).ConfigureAwait( false );
					}
					else if (request.DataTypeCode == RegularNodeTypeItem.BCD.Text)
					{
						return await WriteValueHelperAsync( request.Address, RegularScalarNode.GetBytesFromBCD( value, BCDFormat.C8421 ), this.writeByteArrayFunc ).ConfigureAwait( false );
					}
					else if (RegularNodeTypeItem.IsDataTypeString( request.DataTypeCode ))
					{
						return await WriteValueHelperAsync( request.Address, value, this.writeStringFunc ).ConfigureAwait( false );
					}
					else return new OperateResult( "Not implementation type: " + request.DataTypeCode );
				}
				catch (Exception ex)
				{
					return new OperateResult( ex.Message );
				}
			}
			return await base.WriteActualAsync( request, value );
		}

		#endregion

		#region Asccess Control

		private Func<string, string, string> GetAccessLevelFromRequestFunc = null;

		/// <inheritdoc/>
		protected override AccessLevel GetAccessLevelFromRequest( ScalarReadRequest scalarReadRequest )
		{
			if (this.GetAccessLevelFromRequestFunc != null)
			{
				return (AccessLevel)Enum.Parse( typeof( AccessLevel ), this.GetAccessLevelFromRequestFunc( scalarReadRequest.Address, scalarReadRequest.DataTypeCode ) );
			}
			return base.GetAccessLevelFromRequest( scalarReadRequest );
		}

		private Func<string, int, int, string, string> CalculatePhysicalAddressFromSourceReadRequestFunc = null;

		/// <inheritdoc/>
		protected override string CalculatePhysicalAddressFromSourceReadRequest( SourceReadRequest sourceReadRequest, int byteOffset, int index, IScalarTransform scalarTransform )
		{
			if (this.CalculatePhysicalAddressFromSourceReadRequestFunc != null)
			{
				return this.CalculatePhysicalAddressFromSourceReadRequestFunc( sourceReadRequest.Address, byteOffset, index, scalarTransform != null ? scalarTransform.DataTypeCode : string.Empty );
			}
			return base.CalculatePhysicalAddressFromSourceReadRequest( sourceReadRequest, byteOffset, index, scalarTransform );
		}

		#endregion

		#region LoggerTelegram

		public void LoggerTelegram( string key, byte[] telegram, bool logMsgFormatBinary )
		{
			this.LogNet?.LoggerTelegram( GetDeviceNameWithPath( ), $"{ToString( )} {key}", telegram, logMsgFormatBinary );
		}

		#endregion

		#region Every Seconds

		/// <inheritdoc/>
		protected override async Task EverySecondsExecuteMethod( int second )
		{
			if (this.everySecondsExecuteMethod != null)
			{
				await this.everySecondsExecuteMethod( second );
			}
		}

		/// <inheritdoc/>
		protected override async Task EveryMinuteExecuteMethod( int minute )
		{
			if (this.everyMinuteExecuteMethod != null)
			{
				await this.everyMinuteExecuteMethod( minute );
			}
		}

		/// <inheritdoc/>
		protected override async Task EveryHourExecuteMethod( int hour )
		{
			if (this.everyHourExecuteMethod != null)
			{
				await this.everyHourExecuteMethod( hour );
			}
		}

		/// <inheritdoc/>
		protected override async Task EveryDayExecuteMethod( int day )
		{
			if (this.everyDayExecuteMethod != null)
			{ 
				await this.everyDayExecuteMethod( day ); 
			}
		}

		#endregion

		#region Object Override

		/// <inheritdoc/>
		public override string ToString( ) => $"{deviceObject} [{GetDeviceNameWithPath( )}]";

		#endregion

		#region Private Member

		private object deviceObject = null;
		private int addressBytesLength = 1;
		private XElement deviceXml;

		private IByteTransform ByteTransform = new RegularByteTransform( );

		#endregion

	}
}
