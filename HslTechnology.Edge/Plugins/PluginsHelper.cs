using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Reflection;
using HslCommunication.BasicFramework;
using HslTechnology.Edge.Reflection;
using HslCommunication.MQTT;
using HslTechnology.Edge.Node.Render;
using HslTechnology.Edge.Node.Device;
using HslCommunication.LogNet;
using System.Xml.Linq;
using HslTechnology.Edge.Node;
using HslCommunication;
using System.Diagnostics;
using System.Linq.Expressions;

namespace HslTechnology.Edge.Plugins
{
	/// <summary>
	/// 插件设备的辅助列表
	/// </summary>
	public class PluginsHelper
	{
		/// <summary>
		/// 根据类型名称，示例对象，属性名来获取实际对象值
		/// </summary>
		/// <typeparam name="T">属性类型信息</typeparam>
		/// <param name="pluginType">插件类型</param>
		/// <param name="obj">插件对象</param>
		/// <param name="name">属性名</param>
		/// <returns>实际的属性信息</returns>
		public static T GetObjectProperty<T>( Type pluginType, object obj, string name )
		{
			PropertyInfo propertyInfo = pluginType.GetProperty( name );
			if (propertyInfo == null) return default( T );

			object value = propertyInfo.GetValue( obj, null );
			if (value == null) return default( T );
			try
			{
				return (T)value;
			}
			catch
			{
				return default( T );
			}
		}

		/// <inheritdoc cref="GetObjectProperty{T}(Type, object, string)"/>
		public static T GetObjectProperty<T>( object obj, string name )
		{
			Type pluginType = obj.GetType();
			return GetObjectProperty<T>( pluginType, obj, name );
		}

		/// <summary>
		/// 插件的目录信息
		/// </summary>
		public static string PluginsDirectory( ) =>
			Path.Combine( AppDomain.CurrentDomain.BaseDirectory, "Plugins" );

		/// <summary>
		/// 当前的插件资源集合
		/// </summary>
		public static Dictionary<string, PluginsDefinition> Plugins { get; set; }

		/// <summary>
		/// 根据插件的唯一类型信息获取方法注册信息
		/// </summary>
		/// <param name="pluginsType">插件的类型信息</param>
		/// <returns>方法接口信息</returns>
		public static MqttRpcApiInfo[] GetDeviceRpcInfoFromPlugins( string pluginsType )
		{
			if (pluginsDeviceDict.ContainsKey( pluginsType ))
			{
				if (pluginsDeviceDict[pluginsType].DeviveObject == typeof( DeviceCustomPlugins ))
				{
					return EdgeReflectionHelper.GetPluginApiInfo( "", pluginsDeviceDict[pluginsType].PluginDeivceType );
				}
				else
				{
					return MqttHelper.GetSyncServicesApiInformationFromObject( "", pluginsDeviceDict[pluginsType].DeviveObject ).ToArray( );
				}
			}
			return null;
		}

		/// <summary>
		/// 根据插件类型获取到设备定义对象
		/// </summary>
		/// <param name="pluginsType">插件类型信息</param>
		/// <returns>设备定义对象</returns>
		public static PluginsDeviceDefinition GetPluginsDeviceDefinition( string pluginsType )
		{
			if (pluginsDeviceDict.ContainsKey( pluginsType ))
			{
				return pluginsDeviceDict[pluginsType];
			}
			return null;
		}

		/// <summary>
		/// 根据插件的唯一类型，获取插件设备节点的属性列表。
		/// </summary>
		/// <param name="pluginsType">插件的类型信息</param>
		/// <returns>属性信息列表</returns>
		public static NodePropertyConfig[] GetPluginsPropertyConfigs( string pluginsType )
		{
			try
			{
				if (pluginsPropertyConfigs.ContainsKey( pluginsType ))
					return pluginsPropertyConfigs[pluginsType];
			}
			catch (Exception ex)
			{
				Console.WriteLine( SoftBasic.GetExceptionMessage( ex ) );
			}
			return null;
		}

		/// <summary>
		/// 获取所有的注册的插件的信息列表
		/// </summary>
		/// <returns>插件信息</returns>
		public static Dictionary<string, NodePropertyConfig[]> GetAllPluginsPropertyConfigs( ) => pluginsPropertyConfigs;

		private static Dictionary<string, NodePropertyConfig[]> pluginsPropertyConfigs = new Dictionary<string, NodePropertyConfig[]>( );
		private static Dictionary<string, PluginsDeviceDefinition> pluginsDeviceDict = new Dictionary<string, PluginsDeviceDefinition>( );

		/// <summary>
		/// 根据插件的类型信息，获取每个地址占用的字节信息，-1表示动态，1表示一个字节，例如西门子，2表示2个字节，例如modbus协议
		/// </summary>
		/// <param name="pluginsType">插件的类型信息</param>
		/// <param name="address">传入的地址信息</param>
		/// <returns>获取每个地址占用的字节数量信息</returns>
		public static int GetPluginsEveryAddressOccupyByte( string pluginsType, string address )
		{
			if (!pluginsDeviceDict.ContainsKey( pluginsType )) return -1;
			PluginsDeviceDefinition pluginsDeviceDefinition = pluginsDeviceDict[pluginsType];
			if (pluginsDeviceDefinition.EveryAddressOccupyByteFunc == null) return -1;
			return pluginsDeviceDefinition.EveryAddressOccupyByteFunc( address );
		}

		/// <summary>
		/// 卸载指定的插件信息
		/// </summary>
		/// <param name="dllName">插件名称信息</param>
		/// <returns>是否卸载成功</returns>
		public static OperateResult UnloadPlugins( string dllName )
		{
			string path = dllName;
			if (path.EndsWith( ".dll" ) || path.EndsWith( ".DLL" )) path = path.RemoveLast( 4 );
			try
			{
				path = Path.Combine( PluginsDirectory( ), path );
				if (Directory.Exists( path ))
				{
					Directory.Delete( path, true );
					return OperateResult.CreateSuccessResult( );
				}
				else
					return new OperateResult( $"Current Plugins[{dllName}] not in edge server!" );
			}
			catch (Exception ex)
			{
				return new OperateResult( $"Upload Plugins[{dllName}] failed: " + ex.Message );
			}
		}

		/// <summary>
		/// 加载单个的插件信息，适用于新添加的插件文件
		/// </summary>
		/// <param name="dllName">插件名称信息</param>
		/// <returns>是否加载成功</returns>
		public static OperateResult LoadPlugins( string dllName )
		{
			string path = dllName;
			if (path.EndsWith( ".dll" ) || path.EndsWith( ".DLL" )) path = path.RemoveLast( 4 );

			path = Path.Combine( PluginsDirectory( ), path );
			OperateResult<string, PluginsDefinition> plugins = LoadSinglePlugin( null, path );
			if (!plugins.IsSuccess) return OperateResult.CreateFailedResult<string>( plugins );

			if (Plugins == null) return new OperateResult<string>( "插件系统异常，对象空值！" );
			if (Plugins.ContainsKey( plugins.Content1 ))
				return new OperateResult<string>( $"当前插件[{plugins.Content1}]在边缘网关系统已经存在，无法重复加载，需要重启系统加载。" );
			else
			{
				Plugins.Add( plugins.Content1, plugins.Content2 );
				return OperateResult.CreateSuccessResult( "" );
			}
		}

		public static OperateResult<int> CheckRegisterPluginsExist( string dllName )
		{
			string path = dllName;
			if (path.EndsWith( ".dll" ) || path.EndsWith( ".DLL" )) path = path.RemoveLast( 4 );
			try
			{
				path = Path.Combine( PluginsDirectory( ), path );
				if (Directory.Exists( path ))
				{
					path = Path.Combine( path, dllName );
					return File.Exists( path ) ? OperateResult.CreateSuccessResult( 1 ) : OperateResult.CreateSuccessResult( 0 );
				}
				else
					return OperateResult.CreateSuccessResult( 0 );
			}
			catch (Exception ex)
			{
				return new OperateResult<int>( $"check Plugins[{dllName}] failed: " + ex.Message );
			}

		}

		/// <summary>
		/// 加载插件信息，一个插件对应的是一个文件夹，这样插件可以携带其他的dll或是数据文件信息
		/// </summary>
		/// <param name="logNet">日志信息</param>
		/// <param name="path">插件路径</param>
		/// <returns>插件字典</returns>
		public static Dictionary<string, PluginsDefinition> LoadPlugins( ILogNet logNet, string path )
		{
			Dictionary<string, PluginsDefinition> plugins = new Dictionary<string, PluginsDefinition>( );
			string[] pluginsFolders = Directory.GetDirectories( path );

			foreach (string folder in pluginsFolders)
			{
				OperateResult<string, PluginsDefinition> load = LoadSinglePlugin( logNet, folder );
				if (!load.IsSuccess)
				{
					logNet?.WriteError( load.Message );
					continue;
				}

				plugins.Add( load.Content1, load.Content2 );
			}
			return plugins;
		}

		/// <summary>
		/// 加载单个的插件到边缘网关系统，返回是否加载成功的结果信息，新增插件可以在运行时加载
		/// </summary>
		/// <param name="logNet">日志对象</param>
		/// <param name="folder">插件的目录</param>
		/// <returns>是否加载成功</returns>
		public static OperateResult<string, PluginsDefinition> LoadSinglePlugin( ILogNet logNet, string folder )
		{
			DirectoryInfo directory = null;
			try
			{
				directory = new DirectoryInfo( folder );
			}
			catch (Exception ex)
			{
				return new OperateResult<string, PluginsDefinition>( "加载插件失败，原因为DirectoryInfo对象创建失败：" + ex.Message );
			}
			string pluginFileName = Path.Combine( folder, directory.Name + ".dll" );
			if (!File.Exists( pluginFileName ))
			{
				// 路径名的插件不存在
				return new OperateResult<string, PluginsDefinition>( "插件加载异常，插件路径同名的DLL文件不存在。" );
			}
			FileInfo pluginFile = null;
			try
			{
				pluginFile = new FileInfo( pluginFileName );
			}
			catch (Exception ex)
			{
				return new OperateResult<string, PluginsDefinition>( $"插件[{directory.Name}]加载异常，FileInfo创建失败：" + ex.Message );
			}

			try
			{
				// 将插件的所有的文件都复制过去
				foreach (FileInfo fileInfo in directory.GetFiles( ))
				{
					string fileNewName = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, fileInfo.Name );
					try
					{
						fileInfo.CopyTo( fileNewName, true );
					}
					catch
					{
						// 移动文件失败，说明可能已经存在了，就跳过
						continue;
					}
				}
			}
			catch (Exception ex)
			{
				return new OperateResult<string, PluginsDefinition>( $"插件[{pluginFile.Name}]加载异常，复制插件：{pluginFileName} 到网关目录失败，当前插件跳过。原因：" + ex.Message );
			}

			string pluginsNewFile = Path.Combine( AppDomain.CurrentDomain.BaseDirectory, directory.Name + ".dll" );
			Assembly assembly = null;
			Type pluginsHelperType = null;
			try
			{
				assembly = Assembly.LoadFrom( pluginsNewFile );
				pluginsHelperType = assembly.GetTypes( ).FirstOrDefault( m => m.Name == "PluginsHelper" );
				if (pluginsHelperType == null)
				{
					return new OperateResult<string, PluginsDefinition>( $"加载[{pluginFile.Name}]插件异常，原因：该插件命名空间里不存在 PluginsHelper 类！" );
				}
			}
			catch (Exception ex)
			{
				return new OperateResult<string, PluginsDefinition>( $"加载[{pluginFile.Name}]插件到程序集时发生异常，原因：" + ex.Message );
			}

			PluginsDefinition pluginsDefinition = new PluginsDefinition( );
			pluginsDefinition.DllName = assembly.ManifestModule.Name;
			pluginsDefinition.Company = pluginsHelperType.GetProperty( "Company" ).GetValue( null, null ) as string;
			pluginsDefinition.ReleaseData = (DateTime)pluginsHelperType.GetProperty( "ReleaseDate" ).GetValue( null, null );
			pluginsDefinition.Description = pluginsHelperType.GetProperty( "Description" ).GetValue( null, null ) as string;
			pluginsDefinition.Http = pluginsHelperType.GetProperty( "Http" )?.GetValue( null, null ) as string;
			pluginsDefinition.Framework = pluginsHelperType.GetProperty( "Framework" )?.GetValue( null, null ) as string;
			pluginsDefinition.Icon16 = pluginsHelperType.GetProperty( "Icon16" )?.GetValue( null, null ) as byte[];

			// 插件的版本号信息支持直接设置 string，不依赖hslcommunication通信库，也可以设置为 SystemVersion 类型
			PropertyInfo propertyInfoVerison = pluginsHelperType.GetProperty( "Version" );
			if (propertyInfoVerison.PropertyType == typeof( string ))
				pluginsDefinition.Version = new SystemVersion( propertyInfoVerison.GetValue( null, null ) as string );
			else
				pluginsDefinition.Version = propertyInfoVerison.GetValue( null, null ) as SystemVersion;
			// 定义插件支持的设备列表信息
			pluginsDefinition.DeviceDefinitions = new Dictionary<string, PluginsDeviceDefinition>( );
			// 插件的实际模式支持完全自由的继承实现
			object obj_devices = pluginsHelperType.GetMethod( "DeviceDefinitions" )?.Invoke( null, null );
			if (obj_devices != null)
			{
				if (obj_devices.GetType( ) == typeof( PluginsDeviceDefinition[] ))
				{
					PluginsDeviceDefinition[] devices = obj_devices as PluginsDeviceDefinition[];
					if (devices != null)
					{
						foreach (PluginsDeviceDefinition item in devices)
						{
							AddPluginsDeviceDefinition( pluginsDefinition, logNet, item );
						}
						return OperateResult.CreateSuccessResult( assembly.ManifestModule.Name, pluginsDefinition );
					}
				}
				else if (obj_devices.GetType( ) == typeof( List<object> ))
				{
					List<object> list = obj_devices as List<object>;
					foreach (var item in list)
					{
						// 这是插件里完全自定义的类实现的，不依赖任何的dll组件实现的插件信息
						PluginsDeviceDefinition definition = new PluginsDeviceDefinition( item );
						AddPluginsDeviceDefinition( pluginsDefinition, logNet, definition );
					}
				}
			}

			// 以下是基于模板的插件实现
			XElement[] templates = pluginsHelperType.GetMethod( "PluginsTemplateDefinition" )?.Invoke( null, null ) as XElement[];
			if (templates != null)
			{
				foreach (XElement template in templates)
				{
					PluginsDeviceDefinition pluginsDeviceDefinition = new PluginsDeviceDefinition( );
					pluginsDeviceDefinition.DeviceName = template.Attribute( nameof( GroupNode.Name ) ).Value;
					pluginsDeviceDefinition.Description = template.Attribute( nameof( GroupNode.Description ) ).Value;
					pluginsDeviceDefinition.Template = template.ToString( );

					string templateType = pluginsHelperType.Namespace + "." + pluginsDeviceDefinition.DeviceName;

					pluginsDefinition.DeviceDefinitions.Add( templateType, pluginsDeviceDefinition );
					if (!pluginsDeviceDict.ContainsKey( templateType ))
					{
						pluginsDeviceDict.Add( templateType, pluginsDeviceDefinition );
					}

					// 将模板添加到系统的资源里
					DataBusiness.BusinessEngine.Business.AddTemplates( templateType, template );

				}
				return OperateResult.CreateSuccessResult( assembly.ManifestModule.Name, pluginsDefinition );
			}
			return OperateResult.CreateSuccessResult( assembly.ManifestModule.Name, pluginsDefinition );
		}

		private static void AddPluginsDeviceDefinition( PluginsDefinition pluginsDefinition, ILogNet logNet, PluginsDeviceDefinition item )
		{
			object obj = item.DeviceNode.Assembly.CreateInstance( item.DeviceNode.FullName );
			if (obj is DeviceNode deviceNode)
			{
				string pluginsType = deviceNode.PluginsType;
				if (item.PluginDeivceType != null && item.Tag != null)
				{
					// 基于网关内置设备扩展的功能
					pluginsType = item.Tag.ToString( );
					PropertyInfo propertyInfo = obj.GetType( ).GetProperty( nameof( DeviceNode.PluginsType ) );
					propertyInfo?.SetValue( obj, pluginsType );
				}

				if (string.IsNullOrEmpty( pluginsType ))
				{
					logNet?.WriteError( $"Plugins 没有定义 PluginsType 新插件加载失败！" );
				}
				else if (!pluginsPropertyConfigs.ContainsKey( pluginsType ))
				{
					item.IsSupportAddressRequest = deviceNode.IsSupportAddressRequest( );
					item.EveryAddressOccupyByteFunc = deviceNode.GetEveryAddressOccupyByte;
					pluginsDefinition.DeviceDefinitions.Add( pluginsType, item );
					pluginsPropertyConfigs.Add( pluginsType, NodePropertyConfig.CreateFromObject( obj, string.Empty ) );
					pluginsDeviceDict.Add( pluginsType, item );
				}
				else
					logNet?.WriteError( $"Plugins[{pluginsType}] 已经存在，说明插件已经重复定义，新插件加载失败！" );
			}
			else
			{
				// 这是完全自定义的插件实现操作
				string pluginsType = PluginsHelper.GetObjectProperty<string>( obj, "PluginsType" );
				if (string.IsNullOrEmpty( pluginsType ))
				{
					// 看看有没有定义方法，实在没有定义的话，就使用dll本身的名字
					MethodInfo methodInfo = obj.GetType( ).GetMethod( "GetPluginsType" );
					if (methodInfo != null && methodInfo.ReturnType == typeof( string ))
					{
						pluginsType = (string)methodInfo.Invoke( obj, null );
					}
					else
					{
						pluginsType = pluginsDefinition.GetPluginTypeString( );
					}
				}

				if (!pluginsPropertyConfigs.ContainsKey( pluginsType ))
				{
					MethodInfo method = item.DeviceNode.GetMethod( "IsSupportAddressRequest" );
					if (method != null && method.ReturnType == typeof( bool ))
					{
						item.IsSupportAddressRequest = (bool)method.Invoke( obj, null );
					}
					MethodInfo methodEveryAddress = item.DeviceNode.GetMethod( "GetEveryAddressOccupyByte" );
					if (methodEveryAddress != null && methodEveryAddress.ReturnType == typeof( int ))
					{
						try
						{
							// 定义变量及参数
							ConstantExpression target = Expression.Constant( obj, item.DeviceNode );
							ParameterExpression address = Expression.Parameter( typeof( string ), "address" );
							// 构造表达式
							Expression call = Expression.Call( target, methodEveryAddress, address );

							var lambda = Expression.Lambda<Func<string, int>>( call, address );
							// 提炼lambda表达式赋值
							item.EveryAddressOccupyByteFunc = lambda.Compile( );
							// item.EveryAddressOccupyByte = (int)methodEveryAddress.Invoke( obj, new object[] { string.Empty } );
						}
						catch (Exception ex)
						{
							logNet?.WriteError( $"Plugins[{pluginsType}] GetEveryAddressOccupyByte加载失败！" + ex.Message );
						}
					}

					pluginsDefinition.DeviceDefinitions.Add( pluginsType, item );
					// 此处的完全自定义的插件，需要添加
					DeviceNode emptyNode = new DeviceNode( pluginsType );
					emptyNode.Name = item.DeviceName;

					List<NodePropertyConfig> configs = new List<NodePropertyConfig>( );
					configs.AddRange( NodePropertyConfig.CreateFromObject( emptyNode, string.Empty ) );
					configs.AddRange( NodePropertyConfig.CreateFromObject( obj, "PluginsType" ) );
					pluginsPropertyConfigs.Add( pluginsType, configs.ToArray( ) );
					pluginsDeviceDict.Add( pluginsType, item );
				}
				else
					logNet?.WriteError( $"Plugins[{pluginsType}] 已经存在，说明插件已经重复定义，新插件加载失败！" );
			}
		}
	}
}
