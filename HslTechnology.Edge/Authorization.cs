using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge
{
	/// <summary>
	/// 授权相关
	/// </summary>
	public class Authorization
	{
		static Authorization( )
		{
			niahdiahduasdbubfas = iashdagsdawbdawda( );
			if (naihsdadaasdasdiwasdaid != 0)
			{
				naihsdadaasdasdiwasdaid = 0;
			}

			if (nuasgdawydaishdgas != 24)
			{
				nuasgdawydaishdgas = 24;
			}

			if (nuasgdaaydbishdgas != 0)
			{
				nuasgdaaydbishdgas = 0;
			}

			if (nuasgdawydbishdgas != 24 * 7)
			{
				nuasgdawydbishdgas = 24 * 7;
			}
		}


		/// <summary>
		/// 商业授权则返回true，否则返回false
		/// </summary>
		/// <returns>是否成功进行商业授权</returns>
		public static bool asdniasnfaksndiqwhawfskhfaiw( )
		{
			// 这里是一些只能商业授权对象使用的接口方法
			// 如果需要移除验证，这里返回true即可。
			return true;
			if (naihsdadaasdasdiwasdaid == niasdhasdguawdwdad && nuasgdaaydbishdgas > 0) return nuasduagsdwydbasudasd( );

			double tmp = (iashdagsdawbdawda( ) - niahdiahduasdbubfas).TotalHours;
			if(tmp < 0) return asdhuasdgawydaduasdgu( );
			if (tmp < nuasgdawydbishdgas) // .TotalHours < nuasgdawydbishdgas)
			{
				return nuasduagsdwydbasudasd( );
			}
			return asdhuasdgawydaduasdgu( );
		}

		internal static bool nuasduagsdwydbasudasd( )
		{
			return true;
		}

		internal static bool asdhuasdgawydaduasdgu( )
		{
			return false;
		}

		internal static bool ashdadgawdaihdadsidas( )
		{
			return niasdhasdguawdwdad == 12345;
		}

		internal static DateTime iashdagsdawbdawda( )
		{
			return DateTime.Now;
		}
		internal static DateTime iashdagsaawbdawda( )
		{
			return DateTime.Now.AddDays( 1 );
		}

		internal static DateTime iashdagsaawadawda( )
		{
			return DateTime.Now.AddDays( 2 );
		}

		internal static void oasjodaiwfsodopsdjpasjpf( )
		{
			System.Threading.Interlocked.Increment( ref iahsduiwikaskfhishfdi );
		}

		internal static string nasduabwduadawdb( string miawdiawduasdhasd )
		{
			StringBuilder asdnawdawdawd = new StringBuilder( );
			MD5 asndiawdniad = MD5.Create( );
			byte[] asdadawdawdas = asndiawdniad.ComputeHash( Encoding.Unicode.GetBytes( miawdiawduasdhasd ) );
			asndiawdniad.Clear( );
			for (int andiawbduawbda = 0; andiawbduawbda < asdadawdawdas.Length; andiawbduawbda++)
			{
				asdnawdawdawd.Append( (255 - asdadawdawdas[andiawbduawbda]).ToString( "X2" ) );
			}
			return asdnawdawdawd.ToString( );
		}

		/// <summary>
		/// 设置本组件系统的授权信息，如果激活失败，只能使用24小时，24小时后所有的网络通信不会成功<br />
		/// Set the authorization information of this component system. If the activation fails, it can only be used for 8 hours. All network communication will not succeed after 8 hours
		/// </summary>
		/// <param name="code">授权码</param>
		public static bool SetAuthorizationCode( string code )
		{
			if (nasduabwduadawdb( code ) == "54AD5EDBCC1FB6CC49524E07ADE216FE")         // 测试ID
			{
				nuasgdaaydbishdgas = 1;
				nuasgdawydbishcgas = 286512937;
				nuasgdawydaishdgas = 24 * 365 * 10;
				return nuasduagsdwydbasudasd( );
			}
			else if (nasduabwduadawdb( code ) == "7FDFF720EF5EE3227FD21DDEF1EDA7AE")    // 超级vip群的固定的激活码
			{
				nuasgdaaydbishdgas = 10000;
				nuasgdawydbishcgas = nuasgdawydbishdgas;
				naihsdadaasdasdiwasdaid = niasdhasdguawdwdad;
				return nuasduagsdwydbasudasd( );
			}
			return true;
			return asdhuasdgawydaduasdgu( );
		}

#pragma warning disable CS0414 // 删除未使用的私有成员
		private static readonly string Declaration = "禁止对本组件进行反编译，篡改源代码，如果用于商业用途，将追究法律责任，如需注册码，请联系作者，QQ号：200962190，邮箱：<EMAIL>，欢迎企业合作。";
#pragma warning restore CS0414 // 删除未使用的私有成员

		private static DateTime niahdiahduasdbubfas = DateTime.Now;
		internal static long naihsdadaasdasdiwasdaid = 0;
		internal static long moashdawidaisaosdas = 0;
		internal static double nuasgdawydbishcgas = 8;
		internal static int nuasgdaaydbishdgas = 0;
		internal static int nuasgdawydbishdgas = 8;
		internal static double nuasgdawydaishdgas = 24;
		internal static int nasidhadguawdbasd = 1000;
		internal static int niasdhasdguawdwdad = 12345;
		internal static int hidahwdauushduasdhu = 23456;
		internal static long iahsduiwikaskfhishfdi = 0;
		internal static int zxnkasdhiashifshfsofh = 0;
		internal static double oawdhjoasidhiasdhiasd = 0d;

		// 超级vip 激活码 12a6dfec-6b01-443c-90fe-cb23893c9ebe
		// 高级测试激活码 ab979805-6f5b-449d-8cdc-3c699bd6d050
	}
}
