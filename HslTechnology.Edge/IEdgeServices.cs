using HslCommunication;
using HslCommunication.Core;
using HslTechnology.Edge.DataBusiness.Log;
using HslTechnology.Edge.DataBusiness.Time;
using HslTechnology.Edge.Device.PLCDevice;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HslTechnology.Edge
{
	/// <summary>
	/// 网关服务接口信息
	/// </summary>
	public interface IEdgeServices
	{
		/// <summary>
		/// 获取网关数据的方法
		/// </summary>
		/// <param name="data">数据的唯一路径信息</param>
		/// <returns>获取的值信息</returns>
		OperateResult<JToken> DeviceData( string data );

		/// <summary>
		/// 批量获取网关数据的方法，可以指定任意的两个数据信息
		/// </summary>
		/// <param name="data">地址数组信息</param>
		/// <returns>获取的值数组信息</returns>
		OperateResult<JArray> DeviceDataArray( string[] data );

		/// <summary>
		/// 根据设备的路径获取设备对象的方法
		/// </summary>
		/// <param name="data">设备的唯一路径信息</param>
		/// <returns>设备对象信息</returns>
		OperateResult<DeviceCore> GetDeviceCore( string data );

		/// <summary>
		/// 获取网关的原始字节请求的缓存信息，用于客户端的配置原始字节时的，数据预览功能
		/// </summary>
		/// <param name="data">原始字节的唯一路径信息</param>
		/// <returns>原始字节数据</returns>
		OperateResult<byte[]> GetSourceRequestCahce( string data );

		/// <summary>
		/// 写入设备指定的数据，只能针对配置的节点写入数据
		/// </summary>
		/// <param name="context">会话上下文</param>
		/// <param name="data">数据的url信息</param>
		/// <param name="value">数据的字符串表示形式</param>
		/// <returns>是否写入成功</returns>
		OperateResult WriteData( ISessionContext context, string data, string value );

		/// <summary>
		/// 批量写入设备指定的数据，只能针对配置的节点写入数据
		/// </summary>
		/// <param name="context">会话上下文</param>
		/// <param name="data">数据的url信息</param>
		/// <param name="value">数据的字符串表示形式</param>
		/// <param name="parallel">是否使用并发的模式，并发模式可以大大提高写入速度</param>
		/// <returns>返回的bool数组指示了每个标签是否写入成功</returns>
		OperateResult<bool[]> WriteDataArray( ISessionContext context, string[] data, string[] value, bool parallel );

		/// <summary>
		/// 当前网关的日志对象信息
		/// </summary>
		EdgeLog EdgeLogNet { get; }

		/// <summary>
		/// 网关服务器运行时间的记录信息，记录从开始运行到结束运行的时间段时间
		/// </summary>
		EdgeRunTimeAnalysis RunTimeAnalysis { get; }

		/// <summary>
		/// 获取当前的网关服务是否处于运行之中
		/// </summary>
		bool IsRunning { get; }

	}
}
