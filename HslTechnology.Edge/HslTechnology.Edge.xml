<?xml version="1.0"?>
<doc>
    <assembly>
        <name>HslTechnology.Edge</name>
    </assembly>
    <members>
        <member name="T:HslTechnology.Edge.Config.ConfigHelper">
            <summary>
            配置类对象的辅助静态方法
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Config.ConfigHelper.GetConfig``1(Newtonsoft.Json.Linq.JToken,System.String,``0)">
            <summary>
            从JToken中读取相关的配置参数信息，如果不存在，就返回默认的值<br />
            Read the relevant configuration parameter information from JToken, if it does not exist, return the default value
            </summary>
            <typeparam name="T">值的类型</typeparam>
            <param name="token">JToken对象</param>
            <param name="name">参数的名称</param>
            <param name="defaultValue">默认的值</param>
            <returns>最终返回的值，如果不存在，就返回默认值</returns>
        </member>
        <member name="T:HslTechnology.Edge.Config.HslServerSettings">
            <summary>
            网关服务器的配置信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Config.HslServerSettings.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.HslServerSettings.IpAddress">
            <summary>
            Ip地址信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.HslServerSettings.Port">
            <summary>
            端口号信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.HslServerSettings.Alias">
            <summary>
            服务器的别名
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.HslServerSettings.Token">
            <summary>
            服务器的令牌
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.HslServerSettings.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.HslServerSettings.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Config.HslServerSettings.GetMqttSyncClient">
            <summary>
            根据配置信息获取到服务器的连接对象信息
            </summary>
            <returns>客户端的连接信息</returns>
        </member>
        <member name="M:HslTechnology.Edge.Config.HslServerSettings.GetMqttClient">
            <summary>
            获取到MqttClient对象
            </summary>
            <returns>连接对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Config.HslServerSettings.GetServicesFileClient">
            <summary>
            根据配置信息获取到服务器的连接对象信息
            </summary>
            <returns>客户端的连接信息</returns>
        </member>
        <member name="M:HslTechnology.Edge.Config.HslServerSettings.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Config.LogInfoConfig">
            <summary>
            日志相关的配置信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Config.LogInfoConfig.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="P:HslTechnology.Edge.Config.LogInfoConfig.LogMode">
            <summary>
            文件存储模式  None SingleFile FileSize DateTime
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.LogInfoConfig.LogFileSize">
            <summary>
            文件大小，默认 1024 单位，KB
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.LogInfoConfig.LogFileSizeCount">
            <summary>
            文件模式下存储的文件的数量，默认是10个
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.LogInfoConfig.LogDateTimeMode">
            <summary>
            按时间存储模式下的
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.LogInfoConfig.LogDateTimeFileCount">
            <summary>
            按照时间存储的模式的文件个数
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.LogInfoConfig.MessageDegree">
            <summary>
            存储的消息等级
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Config.LogInfoConfig.GetLogNet">
            <summary>
            根据日志的配置信息，获取相关的日志对象
            </summary>
            <returns>日志对象</returns>
        </member>
        <member name="T:HslTechnology.Edge.Config.ServerInfoConfig">
            <summary>
            服务器的基本信息的配置
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Config.ServerInfoConfig.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="P:HslTechnology.Edge.Config.ServerInfoConfig.DeviceName">
            <summary>
            获取或设置设备的名称信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.ServerInfoConfig.UniqueId">
            <summary>
            当前设备系统的唯一ID信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.ServerInfoConfig.ServerPort">
            <summary>
            配置服务器的端口号信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.ServerInfoConfig.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.ServerInfoConfig.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.ServerInfoConfig.RemoteServerIp">
            <summary>
            用于检测更新的远程服务器的IP地址信息
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Config.UploadInfoConfig">
            <summary>
            上传服务器这部分的配置信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Config.UploadInfoConfig.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.UseRedisServer">
            <summary>
            是否启动Redis的服务器存储信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.RedisIpAddress">
            <summary>
            整个服务器对应的Redis的IP地址信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.RedisPort">
            <summary>
            整个服务器对应的Redis的端口号信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.RedisPassword">
            <summary>
            Redis服务器的密码，如果没有的话，就设置为String.Empty
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.RedisDBNumber">
            <summary>
            Redis相关的DB块信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.UseMqttServer">
            <summary>
            是否启动远程Mqtt服务器
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.MqttIpAddress">
            <summary>
            远程Mqtt服务器的Ip地址
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.MqttClientID">
            <summary>
            Mqtt的客户端ID信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.MqttPort">
            <summary>
            远程Mqtt服务器的端口地址
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.MqttUserName">
            <summary>
            远程Mqtt的用户名，默认为空
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.MqttPassword">
            <summary>
            远程Mqtt的密码，默认为空
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.IsUploadRemoteEntire">
            <summary>
            是否整个信息一起打包上传
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Config.UploadInfoConfig.UploadTimeInterval">
            <summary>
            获取或设置上传服务器的时间
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.DataBusiness.NamespaceDoc">
            <summary>
            当前的命名空间主要用来处理数据的相关的内容的，目前主要包含3大块，产量分析，报警分析，OEE分析
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Device.Base.DeviceCoreBase">
            <summary>
            设备交互的核心类对象，主要定义了设备的请求逻辑
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.#ctor">
            <summary>
            使用默认的无参构造方法
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.DeviceNodes">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.DeviceNodes"/>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.Requests">
            <summary>
            所有的请求列表
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.ActiveTime">
            <summary>
            设备上次激活的时间节点，用来判断失效状态
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.UniqueId">
            <summary>
            唯一的识别码，方便异形客户端寻找对应的处理逻辑
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.Name">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.Name"/>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.RequestSuccessCount">
            <summary>
            请求成功的次数统计
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.RequestFailedCount">
            <summary>
            请求失败的次数统计
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.DeviceStatus">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.DeviceStatus"/>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.ThreadActiveTime">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.ThreadActiveTime"/>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.TimeDeviation">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.TimeDeviation"/>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.JsonData">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.JsonData"/>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.JsonObject">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.JsonObject"/>
        </member>
        <member name="P:HslTechnology.Edge.Device.Base.DeviceCoreBase.LogNet">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.LogNet"/>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.LoadRequest(System.Xml.Linq.XElement)">
            <summary>
            使用固定的节点加载数据信息
            </summary>
            <param name="element">数据请求的所有列表信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.DealWithReadResult``1(HslCommunication.OperateResult{``0},System.Action{``0})">
            <summary>
            处理读取结果的统一方法，需要传入读取的结果对象，以及如何处理的 <see cref="T:System.Action`1"/> 的委托对象
            </summary>
            <typeparam name="T">当前的类型对象</typeparam>
            <param name="read">读取的结果对象</param>
            <param name="action">如何处理的委托信息</param>
            <returns>最后处理的结果内容</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.SetJsonValue(System.String,System.Object,System.Boolean)">
            <summary>
            设置设备的富数据信息，是一种富JSON的数据格式信息
            </summary>
            <param name="name">数据的名称</param>
            <param name="value">数据值</param>
            <param name="isArray">是否是数组</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.SetJsonObjectValue(System.String,System.Object)">
            <summary>
            设置设备的JSON数据信息，类型为JObject，或是JArray
            </summary>
            <param name="name">数据的名称</param>
            <param name="value">数据值</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.SetNameDescriptionJsonValue(HslTechnology.Edge.Node.GroupNode)">
            <summary>
            设置名称和描述的设备数据信息
            </summary>
            <param name="node">设备描述信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.ParseFromRequest(System.Byte[],HslTechnology.Edge.Node.Request.DeviceRequest,HslCommunication.Core.IByteTransform)">
            <summary>
            从设备请求去解析成真实的数据对象
            </summary>
            <param name="data">原始的数据</param>
            <param name="request">请求</param>
            <param name="byteTransform">解析转换规则</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.StartRead">
            <summary>
            启动读取数据
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.CloseRead">
            <summary>
            停止读取数据
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.SetAlineSession(HslCommunication.Core.Net.AlienSession)">
            <summary>
            设置为异形客户端对象
            </summary>
            <param name="alienSession">异形对象</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.IsCurrentDevice(System.String[])">
            <summary>
            判断当前的设备是否是传入的节点参数信息
            </summary>
            <param name="nodes">传入的节点参数信息</param>
            <returns>是否是当前的设备</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.GetValueByName(System.String[])">
            <summary>
            获取本设备对象的值信息
            </summary>
            <param name="nodes">节点数据</param>
            <returns>值信息数据</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.BeforStart">
            <summary>
            设备在启动之前进行的操作信息，例如连接对象实例化，长连接设置<br />
            Operation information performed before the device is started, such as instantiation of the connection object, long connection settings
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.AfterClose">
            <summary>
            在关闭的时候需要进行的操作，将设备的连接对象进行关闭，或是其他额外的操作信息<br />
            The operation that needs to be performed when shutting down, closing the connected object of the device, or other additional operation information
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.CheckDeviceStatus">
            <summary>
            检查当前的设备的状态，进行连接一次设备，如果连接失败或是打开串口失败，则返回错误消息，用于客户端测试设备的网络状态<br />
            Check the current device status and connect to the device once. If the connection fails or the serial port fails to open, 
            an error message will be returned for the client to test the network status of the device
            </summary>
            <returns>是否成功的状态信息，如果失败，请检查携带的<see cref="P:HslCommunication.OperateResult.Message"/>消息</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.ReadActualAsync(HslTechnology.Edge.Node.Request.DeviceRequest)">
            <summary>
            真正读取的方法，需要进行相关的重写操作
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.CncDevice.CncFanucSerise0i">
            <summary>
            数控机床的设备对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.CncDevice.CncFanucSerise0i.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个默认的对象
            </summary>
            <param name="element">设备的配置对象</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.CncDevice.CncFanucSerise0i.ReadActualAsync(HslTechnology.Edge.Node.Request.DeviceRequest)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.CncDevice.CncFanucSerise0i.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.CncDevice.CncFanucSerise0i.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.CncDevice.CncFanucSerise0i.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.CncDevice.CncFanucSerise0i.ToString">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.CncDevice.CncFanucSerise0i.GetAddress">
            <summary>
            支持的读取的地址内容信息
            </summary>
            <returns>返回一个字符串数组</returns>
        </member>
        <member name="T:HslTechnology.Edge.Device.DeviceData">
            <summary>
            设备的富数据对象，支持动态的设置和获取数据信息，支持获取所有的数据对象，或是其中某一个的数据对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.#ctor">
            <summary>
            实例化一个默认的设备富数据对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.GetJTokenValueByName(System.String)">
            <summary>
            通过节点值名称，获取本设备信息的值，如果节点名称不存在，则返回为null
            </summary>
            <param name="name">节点值名称</param>
            <returns>返回富数据中的JToken数值</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.GetStringValueByName(System.String)">
            <summary>
            通过节点值名称，获取本设备信息的值
            </summary>
            <param name="name">节点值名称</param>
            <returns>动态值</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.SetJsonValue(System.String,System.Object,System.Boolean)">
            <summary>
            设置JSON的数据对象，需要指定是否为数组对象
            </summary>
            <param name="name">数据名称</param>
            <param name="value">数据值</param>
            <param name="isArray">是否是数组</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.SetJObjectValue(System.String,System.Object)">
            <summary>
            设置JObject的数据对象
            </summary>
            <param name="name">数据名称</param>
            <param name="value">数据值</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.SetJArrayValue(System.String,System.Object)">
            <summary>
            设置JArray的数据对象
            </summary>
            <param name="name">数据名称</param>
            <param name="value">数据值</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.SetJsonbject(System.String,System.Object)">
            <summary>
            设置json的对象内容
            </summary>
            <param name="name">数据名称</param>
            <param name="value">数据值信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.SetJArray(System.String,Newtonsoft.Json.Linq.JArray)">
            <summary>
            设置jarray的对象内容
            </summary>
            <param name="name">数据名称</param>
            <param name="value">数据值信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.UpdateJsonTmp">
            <summary>
            更新所有数据的缓存信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.DeviceData.DeviceJsonTemp">
            <summary>
            获取整个设备的数据信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.DeviceData.DeviceJsonClone">
            <summary>
            整个设备对象的
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.DeviceData.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.IDeviceCore">
            <summary>
            设备层的接口数据信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.IDeviceCore.IsCurrentDevice(System.String[])">
            <summary>
            判断当前的设备是否是传入的节点参数信息
            </summary>
            <param name="nodes">传入的节点参数信息</param>
            <returns>是否是当前的设备</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.IDeviceCore.GetValueByName(System.String[])">
            <summary>
            获取本设备对象的值信息
            </summary>
            <param name="nodes">节点数据</param>
            <returns>值信息数据</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.IDeviceCore.StartRead">
            <summary>
            启动读取数据
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.IDeviceCore.CloseRead">
            <summary>
            停止读取数据
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.IDeviceCore.ToString">
            <summary>
            返回表示当前对象的字符串
            </summary>
            <returns>字符串信息</returns>
        </member>
        <member name="P:HslTechnology.Edge.Device.IDeviceCore.Name">
            <summary>
            设备的名称
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.IDeviceCore.JsonData">
            <summary>
            获取本设备所有的属性数据
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.IDeviceCore.JsonObject">
            <summary>
            获取本设备所有的属性数据
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.IDeviceCore.ThreadActiveTime">
            <summary>
            当前线程的活动时间，用来判定线程是否处于假死状态
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.IDeviceCore.DeviceStatus">
            <summary>
            指示设备是否正常的状态，如果是true，就是代表设备正常在线，false是读取失败，多半是因为
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.IDeviceCore.TimeDeviation">
            <summary>
            和服务器的时间差
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.IDeviceCore.LogNet">
            <summary>
            获取或设置当前的日志对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.IDeviceCore.DeviceNodes">
            <summary>
            设备的节点分布的信息点
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.IDeviceCore.CheckDeviceStatus">
            <summary>
            检查当前的设备是否网络在线
            </summary>
            <returns>检查的结果</returns>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyMicroCip">
            <summary>
            AB-PLC的设备，采用了 MicroCIP协议实现的
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyMicroCip.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个AB PLC的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyMicroCip.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyMicroCip.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyMicroCip.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyMicroCip.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyNet">
            <summary>
            一个基于AB PLC专用协议的ab plc设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyNet.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个AB PLC的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyNet.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyNet.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyNet.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleyNet.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleySLC">
            <summary>
            使用SLC协议来通信的AB-PLC
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleySLC.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个AB PLC的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleySLC.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleySLC.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleySLC.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceAllenBradleySLC.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceCore">
            <summary>
            PLC设备的数据访问对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceCore.#ctor">
            <summary>
            实例化一个PLC的数据访问的设备对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.PLCDevice.DeviceCore.ByteTransform">
            <summary>
            数据转换规则
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.PLCDevice.DeviceCore.ReadWriteDevice">
            <summary>
            当前的数据读写信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceCore.ReadActualAsync(HslTechnology.Edge.Node.Request.DeviceRequest)">
            <summary>
            重写数据的读取服务
            </summary>
            <param name="request">请求的数据信息，来源一次单独的请求</param>
            <returns>数据请求信息</returns>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceCore.CreateFromXElement(System.Xml.Linq.XElement)">
            <summary>
            通过真实配置的设备信息，来创建一个真实的设备，如果类型不存在，将返回null
            </summary>
            <param name="device">设备的配置信息</param>
            <returns>真实的设备对象</returns>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceKeyenceMc3E">
            <summary>
            三菱PLC的Qna兼容3E帧的请求方案
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceKeyenceMc3E.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个基恩士的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceKeyenceMc3E.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceKeyenceMc3E.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceKeyenceMc3E.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceKeyenceMc3E.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc1E">
            <summary>
            三菱的PLC设备，所使用的协议为Qna兼容1E帧协议
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc1E.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个三菱的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc1E.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc1E.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc1E.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc1E.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc3E">
            <summary>
            三菱PLC的Qna兼容3E帧的请求方案
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc3E.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个三菱的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc3E.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc3E.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc3E.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMc3E.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMcUdp">
            <summary>
            MELSEC设备的MC协议的
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMcUdp.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个三菱的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMcUdp.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMcUdp.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMcUdp.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceMelsecMcUdp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceModbusAscii">
            <summary>
            Modbus Ascii协议的设备对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusAscii.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个Modbus-Ascii的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusAscii.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusAscii.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusAscii.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusAscii.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtu">
            <summary>
            Modbus的rtu设置，基于串口完成通讯
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtu.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个Modbus-Tcp的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtu.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtu.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtu.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtu.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtuOverTcp">
            <summary>
            使用透传的方式连接ModbusRtu的设备，但是走的网络确实TCP的
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtuOverTcp.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个Modbus-Tcp的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtuOverTcp.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtuOverTcp.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtuOverTcp.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusRtuOverTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceModbusTcp">
            <summary>
            ModbusTcp的设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusTcp.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个Modbus-Tcp的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusTcp.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusTcp.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusTcp.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceModbusUdp">
            <summary>
            Modbus TCP协议实现的通信对象，但是走的网络确实UDP的网络
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusUdp.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个Modbus-Tcp的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusUdp.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusUdp.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusUdp.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceModbusUdp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFins">
            <summary>
            这是一个基于fins协议的欧姆龙的设备对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFins.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个欧姆龙的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFins.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFins.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFins.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFins.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFinsUdp">
            <summary>
            欧姆龙的设备UDP对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFinsUdp.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个欧姆龙的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFinsUdp.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFinsUdp.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFinsUdp.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceOmronFinsUdp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DevicePanasonicMc3E">
            <summary>
            松下的设备对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DevicePanasonicMc3E.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个松下的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DevicePanasonicMc3E.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DevicePanasonicMc3E.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DevicePanasonicMc3E.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DevicePanasonicMc3E.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensFW">
            <summary>
            基于fetch/write协议的西门子设备信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensFW.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个西门子的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensFW.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensFW.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensFW.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensFW.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensS7">
            <summary>
            西门子的S7协议的设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensS7.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个西门子的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensS7.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensS7.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensS7.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.PLCDevice.DeviceSiemensS7.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.RobotDevice.RobotAbb">
            <summary>
            Abb机器人的设备类对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotAbb.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个Abb机器人的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotAbb.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotAbb.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotAbb.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotAbb.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.RobotDevice.RobotCore">
            <summary>
            一个机器人的统一数据读取对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotCore.#ctor">
            <summary>
            实例化一个机器人的数据访问的设备对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.RobotDevice.RobotCore.ByteTransform">
            <summary>
            数据转换规则
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.RobotDevice.RobotCore.RobotNetDevice">
            <summary>
            当前的数据读写信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotCore.ReadActualAsync(HslTechnology.Edge.Node.Request.DeviceRequest)">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.RobotDevice.RobotEfortNet">
            <summary>
            埃夫特机器人的网络数据类
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotEfortNet.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个埃夫特机器人的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotEfortNet.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotEfortNet.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotEfortNet.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotEfortNet.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.RobotDevice.RobotFanucInterface">
            <summary>
            Fanuc机器人的网络数据类
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotFanucInterface.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个埃夫特机器人的设备对象，从配置信息创建
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotFanucInterface.BeforStart">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotFanucInterface.AfterClose">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotFanucInterface.CheckDeviceStatus">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.RobotDevice.RobotFanucInterface.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Device.ServerDevice.DeviceModbusServer">
            <summary>
            Modbus服务器的节点信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.DeviceModbusServer.#ctor(System.Xml.Linq.XElement)">
            <summary>
            实例化一个默认的对象
            </summary>
            <param name="element">配置信息</param>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.DeviceModbusServer.Start">
            <inheritdoc cref="M:HslTechnology.Edge.Device.ServerDevice.IServerDevice.Start"/>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.DeviceModbusServer.Close">
            <inheritdoc cref="M:HslTechnology.Edge.Device.ServerDevice.IServerDevice.Close"/>
        </member>
        <member name="T:HslTechnology.Edge.Device.ServerDevice.IServerDevice">
            <summary>
            服务器相关的设备接口信息<br />
            Server-related device interface information
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.IServerDevice.Start">
            <summary>
            启动服务器的服务<br />
            Start the server
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.IServerDevice.Close">
            <summary>
            关闭服务器的服务<br />
            Close the server
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase">
            <summary>
            服务器设备的基类
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.Name">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.Name"/>
        </member>
        <member name="P:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.LogNet">
            <inheritdoc cref="P:HslTechnology.Edge.Device.IDeviceCore.LogNet"/>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.SetJsonValue(System.String,System.Object,System.Boolean)">
            <inheritdoc cref="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.SetJsonValue(System.String,System.Object,System.Boolean)"/>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.SetJsonObjectValue(System.String,System.Object)">
            <inheritdoc cref="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.SetJsonObjectValue(System.String,System.Object)"/>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.SetNameDescriptionJsonValue(HslTechnology.Edge.Node.GroupNode)">
            <inheritdoc cref="M:HslTechnology.Edge.Device.Base.DeviceCoreBase.SetNameDescriptionJsonValue(HslTechnology.Edge.Node.GroupNode)"/>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.Start">
            <inheritdoc cref="M:HslTechnology.Edge.Device.ServerDevice.IServerDevice.Start"/>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.Close">
            <inheritdoc cref="M:HslTechnology.Edge.Device.ServerDevice.IServerDevice.Close"/>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.ToString">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Device.ServerDevice.ServerDeviceBase.CreateServerFromXml(System.Xml.Linq.XElement)">
            <summary>
            根据节点信息创建服务器的内容
            </summary>
            <param name="device">Xml描述的节点信息</param>
            <returns>服务器内容</returns>
        </member>
        <member name="T:HslTechnology.Edge.HslTechnologyHelper">
            <summary>
            边缘系统相关的静态辅助办法<br />
            Static assistance related to the edge system
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.HslTechnologyHelper.GetEnumByString``1(System.String)">
            <summary>
            将指定描述的枚举字符串转变为枚举对象本身
            </summary>
            <typeparam name="T">枚举的类型</typeparam>
            <param name="desc">描述信息</param>
            <returns>枚举对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.HslTechnologyHelper.Encrypted(System.String)">
            <summary>
            加密当前的数据信息，对最终用户进行隐藏
            </summary>
            <param name="content">字符串的数据信息</param>
            <returns>加密之后的结果信息</returns>
        </member>
        <member name="M:HslTechnology.Edge.HslTechnologyHelper.Decrypted(System.String)">
            <summary>
            解密当前的数据信息，解析出真实的数据信息，如果解析失败，就返回空字符串数据信息
            </summary>
            <param name="content">等待解密的数据信息</param>
            <returns>解密后真实的值</returns>
        </member>
        <member name="T:HslTechnology.Edge.Node.Converter.DateConverter">
            <summary>
            日期格式的数据转换信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Converter.DateConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Converter.ParityConverter">
            <summary>
            <see cref="T:System.IO.Ports.Parity"/> 的自动转换类
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Converter.ParityConverter.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Converter.ParityConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Converter.ParityConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.DeviceType">
            <summary>
            当前系统的所有的注册的设备类型，包括PLC设备，modbus设备，cnc设备，机器人设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.Device">
            <summary>
            富设备的节点，下面可以挂多个不同的PLC，或是从共享PLC的数据创建数据
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecMcQna3E">
            <summary>
            三菱的Qna兼容3E帧协议的客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecMcUdpQna3E">
            <summary>
            三菱的Qna兼容3E帧协议的客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.PanasonicMc3E">
            <summary>
            松下的PLC的Qna3E协议信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecMcQna1E">
            <summary>
            三菱的Qna兼容1E帧协议的客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecFxlinks">
            <summary>
            三菱的Fxlinks的串口设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecFxlinksOverTcp">
            <summary>
            三菱的Fxlinks的网口透传设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecFxSerial">
            <summary>
            三菱的编程口串口设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecFxSerialOverTcp">
            <summary>
            三菱的编程口的透传版本
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecA3C">
            <summary>
            三菱的MC-3C的串口设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecA3COverTcp">
            <summary>
            三菱的MC-3C的网口透传设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.ModbusTcpClient">
            <summary>
            常规的Modbus-Tcp客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.ModbusUdp">
            <summary>
            modbustcp客户端，但是使用UDP协议传送的报文
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.ModbusRtu">
            <summary>
            常规的Modbus-Rtu客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.ModbusAscii">
            <summary>
            常规的Modbus-Ascii客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.ModbusRtuOverTcp">
            <summary>
            常规的Modbus-Rtu客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.ModbusTcpAlien">
            <summary>
            异形的Modbus-Tcp客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.SiemensS7">
            <summary>
            西门子的PLC设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.SiemensFW">
            <summary>
            西门子的PLC设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.SiemensPPI">
            <summary>
            西门子的PPI协议的设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.SiemensPPIOverTcp">
            <summary>
            西门子PLC，通过PPI协议实现的网络透传
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.OmronFinsTcp">
            <summary>
            欧姆龙的PLC设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.OmronFinsUdp">
            <summary>
            欧姆龙的PLC设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.OmronHostLink">
            <summary>
            欧姆龙的HostLink协议的设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.OmronHostLinkOverTcp">
            <summary>
            欧姆龙的HostLink协议的网口透传设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.KeyenceMcQna3E">
            <summary>
            基恩士的Qna兼容3E帧协议的客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.AllenBradleyCIP">
            <summary>
            AB PLC的设备信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.AllenBradleyMicroCIP">
            <summary>
            AB PLC的设备信息，基于MicroCip协议
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.AllenBradleySLC">
            <summary>
            AB PLC的设备，使用SLC协议实现
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.SimplifyNet">
            <summary>
            其他电脑的SimplifyNet服务器
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.CncFanucSerise0i">
            <summary>
            Fanuc公司的CNC数据信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.RobotEfort">
            <summary>
            埃夫特公司的机器人数据信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.RobotFanucInterface">
            <summary>
            Fanuc公司的机器人数据信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.RobotABBWebApi">
            <summary>
            ABB公司的机器人数据信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.RedisClient">
            <summary>
            Redis客户端的信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.BeckhoffAdsNet">
            <summary>
            倍福的ADS协议的客户端
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.DeltaDvpTcpNet">
            <summary>
            台达的DVP系列的设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.FatekProgram">
            <summary>
            永宏的编程口协议的设备
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.ModbusServer">
            <summary>
            Modbus的服务器，包含了TCP和RTU,ASCII的服务器构建
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.SiemensS7Server">
            <summary>
            西门子PLC的S7的虚拟服务器
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.SiemensFWServer">
            <summary>
            西门子PLC的FW的虚拟服务器
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.MelsecMCServer">
            <summary>
            三菱PLC的MC协议的虚拟服务器
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.DeviceType.OmronFinsServer">
            <summary>
            欧姆龙的FINS协议的虚拟服务器
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeAllenBradley">
            <summary>
            AB PLC的节点数据信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradley.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradley.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeAllenBradley.Slot">
            <summary>
            PLC的槽号
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradley.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradley.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradley.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeAllenBradleyMicroCip">
            <summary>
            罗克韦尔的设备，基于MicroCIP协议实现的设备访问
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradleyMicroCip.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradleyMicroCip.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradleyMicroCip.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeAllenBradleySLC">
            <summary>
            AB-plc的SLC协议的设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradleySLC.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradleySLC.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeAllenBradleySLC.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.DeviceNode">
            <summary>
            指明这是一个设备对象类，包含设备的一些基本信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNode.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNode.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNode.DeviceType">
            <summary>
            设备的类别
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNode.DeviceEnable">
            <summary>
            当前的设备是否启用
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNode.MRpcEnable">
            <summary>
            是否开启MRPC的远程调用接口，如果当前设备支持的话
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNode.WebApiEnable">
            <summary>
            是否开启Webapi的远程调用接口，如果当前设备支持的话
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNode.InstallationDate">
            <summary>
            安装的时间
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNode.FixedNumber">
            <summary>
            设备的固定编号，方便进行管理
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNode.CreateTime">
            <summary>
            服务器的创建日期
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNode.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNode.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNode.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.DeviceNodeNet">
            <summary>
            使用以太网访问设备类节点对象，包含了IP地址，端口号，连接超时信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeNet.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeNet.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeNet.IpAddress">
            <summary>
            设备的Ip地址
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeNet.Port">
            <summary>
            设备的端口号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeNet.ConnectTimeOut">
            <summary>
            连接超时的时间，单位毫秒
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeNet.ReceiveTimeOut">
            <summary>
            连接超时的时间，单位毫秒
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeNet.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeNet.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeNet.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.DeviceNodeSerial">
            <summary>
            串口类设备的节点基类
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeSerial.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeSerial.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeSerial.PortName">
            <summary>
            串口名称
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeSerial.BaudRate">
            <summary>
            波特率
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeSerial.DataBits">
            <summary>
            标准数据位长度
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeSerial.StopBits">
            <summary>
            标准停止位长度
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeSerial.Parity">
            <summary>
            奇偶校验检查
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.DeviceNodeSerial.ReceiveTimeOut">
            <summary>
            连接超时的时间，单位毫秒
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeSerial.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeSerial.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.DeviceNodeSerial.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsec1E">
            <summary>
            三菱的PLC，支持Qna兼容1E帧的协议机制
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsec1E.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsec1E.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsec1E.PLCNumber">
            <summary>
            PLC编号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsec1E.IsBinary">
            <summary>
            是否是二进制通讯
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsec1E.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsec1E.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsec1E.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsecA3C">
            <summary>
            三菱PLC，使用A3C实现的协议通信，具体格式为格式一
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3C.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3C.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecA3C.Station">
            <summary>
            PLC站号信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3C.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3C.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3C.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsecA3COverTcp">
            <summary>
            三菱PLC的MC协议的A3C格式的网口透传版本
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3COverTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3COverTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecA3COverTcp.Station">
            <inheritdoc cref="P:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.Station"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3COverTcp.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3COverTcp.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecA3COverTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks">
            <summary>
            三菱的FxLinks的通信协议
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.Station">
            <summary>
            客户端的站号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.SumCheck">
            <summary>
            和校验
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsecFxlinksOverTcp">
            <summary>
            三菱的Fxlinks的网口透传版设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinksOverTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinksOverTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecFxlinksOverTcp.Station">
            <inheritdoc cref="P:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.Station"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecFxlinksOverTcp.SumCheck">
            <inheritdoc cref="P:HslTechnology.Edge.Node.Device.NodeMelsecFxlinks.SumCheck"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinksOverTcp.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinksOverTcp.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxlinksOverTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsecFxSerial">
            <summary>
            三菱编程口的PLC设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerial.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerial.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecFxSerial.IsVersionNew">
            <summary>
            设备的版本是否是最新的，新版是字节对齐的
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerial.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerial.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerial.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsecFxSerialOverTcp">
            <summary>
            三菱编程口的网口透传对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerialOverTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerialOverTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecFxSerialOverTcp.IsVersionNew">
            <summary>
            设备的版本是否是最新的，新版是字节对齐的
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerialOverTcp.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerialOverTcp.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecFxSerialOverTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsecMc">
            <summary>
            实例化一个三菱MC协议的节点对象的设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecMc.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecMc.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecMc.NetworkNumber">
            <summary>
            网络号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecMc.NetworkStationNumber">
            <summary>
            网络站号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeMelsecMc.IsBinary">
            <summary>
            是否是二进制通讯
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecMc.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecMc.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecMc.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMelsecMcUdp">
            <summary>
            三菱的MC-3E协议的UDP版本
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecMcUdp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecMcUdp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMelsecMcUdp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeModbusAscii">
            <summary>
            Modbus Ascii的基本信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusAscii.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusAscii.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusAscii.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeModbusRtu">
            <summary>
            Modbus Rtu的基本信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusRtu.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusRtu.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeModbusRtu.Station">
            <summary>
            客户端的站号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeModbusRtu.IsAddressStartWithZero">
            <summary>
            起始地址是否从0开始
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeModbusRtu.DataFormat">
            <summary>
            字节分析是否颠倒
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeModbusRtu.IsStringReverse">
            <summary>
            字符串分析是否颠倒
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusRtu.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusRtu.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusRtu.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeModbusRtuOverTcp">
            <summary>
            一个Modbus Rtu Over Tcp的节点对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusRtuOverTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusRtuOverTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusRtuOverTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeModbusTcp">
            <summary>
            常规的Modbus-Tcp的客户端
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeModbusTcp.Station">
            <summary>
            客户端的站号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeModbusTcp.IsAddressStartWithZero">
            <summary>
            起始地址是否从0开始
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeModbusTcp.DataFormat">
            <summary>
            字节分析是否颠倒
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeModbusTcp.IsStringReverse">
            <summary>
            字符串分析是否颠倒
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusTcp.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusTcp.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeModbusUdp">
            <summary>
            ModbusUdp的设备信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusUdp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusUdp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeModbusUdp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeBeckhoffAds">
            <summary>
            倍福ADS协议的节点信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeBeckhoffAds.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeBeckhoffAds.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeBeckhoffAds.TargetNetId">
            <summary>
            目标的网络ID号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeBeckhoffAds.SenderNetId">
            <summary>
            发送方的网络ID号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeBeckhoffAds.TagCache">
            <summary>
            是否启动标签缓存的功能
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeBeckhoffAds.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeBeckhoffAds.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeBeckhoffAds.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeCncFanucSerise">
            <summary>
            Fanuc的CNC系列的通讯芥蒂娜信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeCncFanucSerise.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeCncFanucSerise.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeCncFanucSerise.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeCncFanucSerise.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeCncFanucSerise.GetNodeClassRenders">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeCncFanucSerise.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeDeltaDvpTcp">
            <summary>
            台达的DVP系列的通信节点
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeDeltaDvpTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeDeltaDvpTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeDeltaDvpTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeFatekProgram">
            <summary>
            永宏PLC的串口访问节点
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeFatekProgram.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeFatekProgram.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeFatekProgram.Station">
            <inheritdoc cref="P:HslTechnology.Edge.Node.Device.NodeModbusTcp.Station"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeFatekProgram.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeFatekProgram.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeFatekProgram.GetNodeClassRenders">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeFatekProgram.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeKeyenceMc">
            <summary>
            实例化一个基恩士MC协议的节点对象的设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeKeyenceMc.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeKeyenceMc.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeKeyenceMc.NetworkNumber">
            <summary>
            网络号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeKeyenceMc.NetworkStationNumber">
            <summary>
            网络站号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeKeyenceMc.IsBinary">
            <summary>
            是否是二进制通讯
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeKeyenceMc.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeKeyenceMc.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeKeyenceMc.GetNodeClassRenders">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeKeyenceMc.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeMachine">
            <summary>
            一个富设备的节点，数据可以包含多个的子设备，或是
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMachine.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMachine.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeMachine.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodePanasonicMc">
            <summary>
            松下PLC的设备节点信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodePanasonicMc.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodePanasonicMc.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodePanasonicMc.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodePanasonicMc.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodePanasonicMc.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeRedisClient">
            <summary>
            redis的服务器配置信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRedisClient.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRedisClient.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeRedisClient.Password">
            <summary>
            Redis的端口号数据，当前的属性需要进行加密存储
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeRedisClient.DbBlock">
            <summary>
            当前的Redis指定的数据块编号
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRedisClient.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRedisClient.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRedisClient.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeRobotAbb">
            <summary>
            Abb机器人的节点对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotAbb.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotAbb.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeRobotAbb.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeRobotAbb.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotAbb.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotAbb.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotAbb.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeRobotEfort">
            <summary>
            埃夫特机器人的节点对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotEfort.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotEfort.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeRobotEfort.IsVersionNew">
            <summary>
            设备的版本是否是最新的，新版是字节对齐的
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotEfort.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotEfort.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotEfort.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeRobotFanucInterface">
            <summary>
            Fanuc机器人的节点对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotFanucInterface.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotFanucInterface.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeRobotFanucInterface.RefreshTime">
            <summary>
            当前的缓存数据更新的时间，默认100ms
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotFanucInterface.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotFanucInterface.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeRobotFanucInterface.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeSimplifyNet">
            <summary>
            SimplifyNet的设备信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSimplifyNet.#ctor">
            <summary>
            使用默认的参数实例化一个设备信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeSimplifyNet.Token">
            <summary>
            Token网络令牌
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSimplifyNet.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSimplifyNet.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSimplifyNet.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp">
            <summary>
            指示欧姆龙对象的设备节点信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp.DA2">
            <summary>
            PLC单元号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp.SA1">
            <summary>
            上位机的节点地址
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp.AutoChangeSA1">
            <summary>
            是否自动更换 SA1 的值，在连接失败的时候
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronFinsTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeOmronFinsUdp">
            <summary>
            欧姆龙的FINS的UDP协议类对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronFinsUdp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronFinsUdp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronFinsUdp.ToString">
            <inheritdoc />
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeOmronHostLink">
            <summary>
            欧姆龙的HostLink协议的PLC实现
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLink.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLink.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronHostLink.UnitNumber">
            <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLink.UnitNumber"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronHostLink.DA2">
            <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLink.DA2"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronHostLink.SA2">
            <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLink.SA2"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLink.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLink.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLink.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp">
            <summary>
            欧姆龙HostLink协议的网口透传版本
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp.UnitNumber">
            <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLink.UnitNumber"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp.DA2">
            <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLink.DA2"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp.SA2">
            <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLink.SA2"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeOmronHostLinkOverTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeSiemensFW">
            <summary>
            基于西门子的fetch/write协议的节点信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensFW.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensFW.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensFW.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensFW.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensFW.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeSiemensPPI">
            <summary>
            西门子PLC，使用基于串口的PPI来实现的设备
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPI.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPI.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeSiemensPPI.Station">
            <summary>
            客户端的站号
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPI.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPI.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPI.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeSiemensPPIOverTcp">
            <summary>
            西门子PLC，使用PPI协议，通过网口透传的模式
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPIOverTcp.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPIOverTcp.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeSiemensPPIOverTcp.Station">
            <summary>
            客户端的站号
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPIOverTcp.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPIOverTcp.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensPPIOverTcp.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Device.NodeSiemensS7">
            <summary>
            西门子设备的类支持
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensS7.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensS7.#ctor(System.Xml.Linq.XElement)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeSiemensS7.PlcType">
            <summary>
            选择的是什么类型的PLC
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeSiemensS7.Rack">
            <summary>
            PLC的机架号，某些特殊的PLC需要这个数据
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeSiemensS7.Slot">
            <summary>
            PLC的槽号，某些特殊的PLC需要这个数据
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeSiemensS7.ConnectionType">
            <summary>
            PLC的连接方式，PG: 0x01，OP: 0x02，S7Basic: 0x03...0x10
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Device.NodeSiemensS7.LocalTSAP">
            <summary>
            西门子相关的一个参数信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensS7.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensS7.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Device.NodeSiemensS7.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.GroupNode">
            <summary>
            一个用于描述基本信息的节点类，也是所有节点类对象的基类，提供了最基本的节点信息描述，只包含名称，描述，节点类型<br />
            A node class used to describe basic information. It is also the base class of all node class objects. 
            It provides the most basic node information description, including only name, description, and node type.
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.GroupNode.#ctor">
            <summary>
            实例化一个默认构造方法的对象<br />
            Instantiate an object with a default constructor
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.GroupNode.#ctor(System.Xml.Linq.XElement)">
            <summary>
            通过指定的Xml配置信息来实例化一个基本节点信息<br />
            Instantiate a basic node information through the specified Xml configuration information
            </summary>
            <param name="element">包含数据的xml元素</param>
        </member>
        <member name="P:HslTechnology.Edge.Node.GroupNode.Name">
            <summary>
            节点的名称，在节点上显示的<br />
            The name of the node, as shown on the node
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.GroupNode.Description">
            <summary>
            当前节点的描述信息<br />
            Description of the current node
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.GroupNode.NodeType">
            <summary>
            节点的类型，用于多种不同用途的功能标记
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.GroupNode.LoadByXmlElement(System.Xml.Linq.XElement)">
            <summary>
            对象从xml元素解析，初始化指定的数据
            </summary>
            <param name="element">包含节点信息的Xml元素</param>
        </member>
        <member name="M:HslTechnology.Edge.Node.GroupNode.ToXmlElement">
            <summary>
            对象解析为Xml元素，方便的存储
            </summary>
            <returns>包含节点信息的Xml元素</returns>
        </member>
        <member name="E:HslTechnology.Edge.Node.GroupNode.OnNameChanged">
            <summary>
            当名称变化的时候，触发的事件
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.GroupNode.ToString">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.GroupNode.GetNodeClassRenders">
            <summary>
            获取用于在数据表信息中显示的键值数据对信息
            </summary>
            <returns>键值数据对列表</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.GroupNode.GetXmlValue``1(System.Xml.Linq.XElement,System.String,``0,System.Func{System.String,``0})">
            <summary>
            从XElement中提取相关的属性信息，如果不存在，就返回默认值
            </summary>
            <typeparam name="T">最终的类型信息</typeparam>
            <param name="element">元素内容</param>
            <param name="name">属性的名称</param>
            <param name="defaultValue">默认提供的值</param>
            <param name="trans">转换方式</param>
            <returns>最终的值</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.GroupNode.GetXmlEnum``1(System.Xml.Linq.XElement,System.String,``0)">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.GetXmlValue``1(System.Xml.Linq.XElement,System.String,``0,System.Func{System.String,``0})"/>
        </member>
        <member name="T:HslTechnology.Edge.Node.IXmlConvert">
            <summary>
            一个接口，表示该对象就有和Xml元素相互转换的能力
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.IXmlConvert.ToXmlElement">
            <summary>
            对象解析为Xml元素，方便的存储
            </summary>
            <returns>包含节点信息的Xml元素</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.IXmlConvert.LoadByXmlElement(System.Xml.Linq.XElement)">
            <summary>
            对象从xml元素解析，初始化指定的数据
            </summary>
            <param name="element">包含节点信息的Xml元素</param>
        </member>
        <member name="T:HslTechnology.Edge.Node.NamespaceDoc">
            <summary>
            当前的命名空间主要用来放置节点相关的源代码的
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Node.NodeClassInfo">
            <summary>
            节点类相关的常用资源
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeClassInfo.NodeRoot">
            <summary>
            系统的根节点信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeClassInfo.NodeClass">
            <summary>
            普通的分类节点，允许再跟分类节点
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeClassInfo.DeviceNode">
            <summary>
            设备节点信息，下面只能跟请求节点信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeClassInfo.ServerNode">
            <summary>
            服务器类型Server的节点
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeClassInfo.RegularNode">
            <summary>
            解析规则的节点
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeClassInfo.DeviceRequest">
            <summary>
            设备的请求信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeClassInfo.DeviceRequests">
            <summary>
            设备的请求信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeClassInfo.RegularItemNode">
            <summary>
            设备的解析规则的子节点
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Node.NodeClassRenderItem">
            <summary>
            单个节点的单个数据对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.#ctor(System.String,System.String)">
            <summary>
            实例化一个对象，需要指定当前的键值信息
            </summary>
            <param name="valueName">名称</param>
            <param name="value">值</param>
        </member>
        <member name="P:HslTechnology.Edge.Node.NodeClassRenderItem.ValueName">
            <summary>
            数据名称
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.NodeClassRenderItem.Value">
            <summary>
            数据值
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.ToString">
            <summary>
            返回表示当前对象的字符串
            </summary>
            <returns>字符串信息</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreatNodeName(System.String)">
            <summary>
            创建一个显示的节点对象
            </summary>
            <param name="value">节点值</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateNodeDescription(System.String)">
            <summary>
            创建一个显示描述的键值对象
            </summary>
            <param name="description">描述信息</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateDeviceType(System.String)">
            <summary>
            创建一个显示的设备类型的键值对象
            </summary>
            <param name="type">设备类型</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateIpAddress(System.String)">
            <summary>
            创建一个显示的Ip地址的键值对象
            </summary>
            <param name="ip">Ip地址</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateIpPort(System.Int32)">
            <summary>
            创建一个显示端口信息的键值对象
            </summary>
            <param name="port">端口号</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateConnectTimeOut(System.Int32)">
            <summary>
            创建一个显示超时时间信息的键值对象
            </summary>
            <param name="timeout">超时时间，单位 ms</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateTime(System.DateTime)">
            <summary>
            创建一个显示创建时间信息的键值对象
            </summary>
            <param name="time">创建时间</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateStation(System.Int32)">
            <summary>
            创建一个显示站号信息的键值对象
            </summary>
            <param name="station">站号信息</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateInstallationPlace(System.String)">
            <summary>
            创建一个显示安装地点的键值对象
            </summary>
            <param name="place">安装地址信息</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateInstallationDate(System.DateTime)">
            <summary>
            创建一个显示安装日期的键值对象
            </summary>
            <param name="date">安装日期</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateUniqueId(System.String)">
            <summary>
            创建一个显示唯一标识的键值对象
            </summary>
            <param name="uniqueId">唯一信息</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateIsAddressStartWithZero(System.Boolean)">
            <summary>
            创建一个显示是否从0开始的键值对象
            </summary>
            <param name="value">是否值</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateDataFormat(System.String)">
            <summary>
            创建一个显示是数据格式的键值对象
            </summary>
            <param name="value">是否值</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreateIsStringReverse(System.Boolean)">
            <summary>
            创建一个显示是否字符串的键值对象
            </summary>
            <param name="value">是否值</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreatePlcMode(System.String)">
            <summary>
            创建一个显示PLC型号的键值对象
            </summary>
            <param name="plcMode">PLC的型号</param>
            <returns>键值对象</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.NodeClassRenderItem.CreatePassword(System.String)">
            <summary>
            创建一个显示密码的键值对象
            </summary>
            <param name="password">密码</param>
            <returns>键值对象</returns>
        </member>
        <member name="T:HslTechnology.Edge.Node.NodeType">
            <summary>
            当前的边缘微服务所有的配置的节点的类型<br />
            Types of all configured nodes of the current edge microservice
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.GroupNode">
            <summary>
            分类的节点信息，用来组织各种子节点的数据信息。<br />
            The classified node information is used to organize the data information of various child nodes.
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.DeviceNode">
            <summary>
            用于描述设备的节点信息，使用 <b>DeviceType</b> 来区分具体的不同的设备。<br />
            Used to describe the node information of the device, use <b>DeviceType</b>  to distinguish specific different devices.
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.RobotNode">
            <summary>
            机器人的节点信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.CncNode">
            <summary>
            数控机床的节点信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.RequestNode">
            <summary>
            用于描述单次请求的数据信息，服务器将根据这个节点来实现数据采集的。<br />
            Used to describe the data information of a single request, the server will realize data collection according to this node.
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.RequestRegular">
            <summary>
            在单次的数据请求里，如果请求的原始字节数据，可以引用不同的 <see cref="F:HslTechnology.Edge.Node.NodeType.RegularNode"/> 来进行解析数据内容，可以批量解析数据。<br />
            In a single data request, if the requested raw byte data, you can refer to different <see cref="F:HslTechnology.Edge.Node.NodeType.RegularNode"/> to parse the data content, and you can parse the data in batches.
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.RegularNode">
            <summary>
            用来描述一个复杂的组合的解析规则，是 <see cref="F:HslTechnology.Edge.Node.NodeType.RegularItemNode"/> 的集合体，用来解析原始的字节数据，转变成实际的有意义的数据
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.RegularItemNode">
            <summary>
            单个数据或是数组的解析规则节点，最基本的数据解析单元，可以配置类型，是否数组，对应的字节位置，是否ax+b操作<br />
            The parsing rule node of a single data or an array, the most basic data parsing unit, you can configure the type, 
            whether it is an array, the corresponding byte position, whether ax+b operation
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.ServerNode">
            <summary>
            用于服务器的相关的节点，用来启动一些虚拟的PLC，Modbus服务器之类的。<br />
            Related nodes for the server, used to start some virtual PLC, Modbus server and the like.
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.AnalysisNode">
            <summary>
            用于数据分析的节点信息，主要是对已经采集或是配置的数据列表进行二次数据分析。<br />
            The node information used for data analysis is mainly to perform secondary data analysis on the data list that has been collected or configured.
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.DataAlarmNode">
            <summary>
            在数据分析中，用于报警的节点数据分析，使用 <b>AlarmType</b> 区分不同的报警，可定义HEX报警分析，INT报警，数值分析报警<br />
            In data analysis, node data analysis for alarms can be defined for HEX alarm analysis, INT alarm, and numerical analysis alarm
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.DataAlarmItemNode">
            <summary>
            单个的报警信息
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.DataProductNode">
            <summary>
            产品产量信息的节点，指定什么数据分析，规则型号信息，分析多久的数据
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.NodeType.DataOeeNode">
            <summary>
            单个设备的稼动率分析，用来分析设备的工作状态信息，有效工作时间，工作的不同状态区分。<br />
            The utilization rate analysis of a single device is used to analyze the working status information of the device, the effective working time, and the different working statuses.
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Node.ProtocolType">
            <summary>
            网络的协议，
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.ProtocolType.TCP">
            <summary>
            TCP协议
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.ProtocolType.UDP">
            <summary>
            UDP协议
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Node.Regular.RegularItemNode">
            <summary>
            程序解析规则的节点
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.#ctor">
            <inheritdoc cref="M:HslTechnology.Edge.Node.GroupNode.#ctor"/>
        </member>
        <member name="P:HslTechnology.Edge.Node.Regular.RegularItemNode.RegularCode">
            <summary>
            类型的代号，详细参见const数据
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Regular.RegularItemNode.TypeLength">
            <summary>
            类型的长度，对于string来说，就是字符串长度，其他的来说，就是数组长度
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Regular.RegularItemNode.Index">
            <summary>
            数据位于字节数据的索引，对于bool变量来说，就是按照位的索引
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Regular.RegularItemNode.Scale">
            <summary>
            数据的倍率信息，默认小于0，不发生倍率计算，倍率计算后，一律类型改为float
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Regular.RegularItemNode.ValueOffset">
            <summary>
            数据的偏移信息，默认为0，不发生任何的偏移，一旦不为0，则发生偏移计算
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.CompareTo(HslTechnology.Edge.Node.Regular.RegularItemNode)">
            <summary>
            实现了比较的接口，可以用来方便的排序
            </summary>
            <param name="other">规则文件进行解析</param>
            <returns>是否大小</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.GetStartedBoolIndex">
            <summary>
            只获取bool相关的索引信息
            </summary>
            <returns>起始位置</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.GetStartedByteIndex">
            <summary>
            获取在字节流中的起始点
            </summary>
            <returns>起始位置</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.GetValue(System.Byte[],HslCommunication.Core.IByteTransform)">
            <summary>
            获取当前的数据信息实际值
            </summary>
            <param name="data">真实的数据信息</param>
            <param name="byteTransform">数据变换</param>
            <returns>动态数据</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.GetLengthBool">
            <summary>
            获取bool的长度信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.GetLengthByte">
            <summary>
            获取当前的解析规则的节点所占用的最长字节
            </summary>
            <returns>长度</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.ParesRegular(System.Xml.Linq.XElement)">
            <summary>
            解析一个配置文件中的所有的规则解析，并返回一个词典信息
            </summary>
            <param name="nodeClass">配置文件的根信息</param>
            <returns>词典</returns>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularItemNode.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Regular.RegularNode">
            <summary>
            字节解析规则节点，该节点下挂载解析节点的子项
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularNode.#ctor">
            <summary>
            实例化一个默认的解析对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularNode.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem">
            <summary>
            单个规则配置项相关的类型资源，比如配置了什么类型的数据，多少长之类的
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.#ctor(System.String,System.Int32,System.Drawing.Color)">
            <summary>
            实例化信息
            </summary>
            <param name="text">文本</param>
            <param name="length">单位长度</param>
            <param name="color">颜色信息</param>
        </member>
        <member name="P:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Text">
            <summary>
            类型的文本描述
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Length">
            <summary>
            类型的长度，仅仅是单个数据对象的长度
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.BackColor">
            <summary>
            类型使用的背景色
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.ToString">
            <inheritdoc/>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.None">
            <summary>
            不存在的数据读取
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Bool">
            <summary>
            Bool数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Byte">
            <summary>
            Byte数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Int16">
            <summary>
            short数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.UInt16">
            <summary>
            ushort数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Int32">
            <summary>
            int数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.UInt32">
            <summary>
            uint数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Int64">
            <summary>
            long数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.UInt64">
            <summary>
            ulong数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Float">
            <summary>
            float数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.Double">
            <summary>
            double数据类型
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.StringAscii">
            <summary>
            string数据类型，ASCII编码
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.StringUnicode">
            <summary>
            string数据类型，Unicode编码
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.StringUtf8">
            <summary>
            string数据类型，UTF8编码
            </summary>
        </member>
        <member name="F:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.StringJson">
            <summary>
            json格式的string数据类型
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Regular.RegularNodeTypeItem.GetDataPraseItemByCode(System.String)">
            <summary>
            从实际的描述信息进行解析类型
            </summary>
            <param name="text">实际的文本描述</param>
            <returns>类型信息</returns>
        </member>
        <member name="T:HslTechnology.Edge.Node.Request.DeviceRequest">
            <summary>
            通用的客户端模型，指示了一般的客户端模式下的，一次数据请求，一个客户端可以进行多次的数据请求
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.DeviceRequest.#ctor">
            <summary>
            实例化一个对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Request.DeviceRequest.RegularNodes">
            <summary>
            本次请求所有关联的节点信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.DeviceRequest.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.DeviceRequest.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.DeviceRequest.GetNodeClassRenders">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.DeviceRequest.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Request.RegularItemNodeCollection">
            <summary>
            <see cref="T:HslTechnology.Edge.Node.Regular.RegularItemNode"/> 相关的集合对象，方便进行配置
            </summary>
        </member>
        <member name="T:HslTechnology.Edge.Node.Request.RequestBase">
            <summary>
            服务器的数据请求类的基类信息
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.RequestBase.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Request.RequestBase.CaptureInterval">
            <summary>
            本次请求的时间间隔，单位为毫秒
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Request.RequestBase.RegularType">
            <summary>
            本次请求解析字节数据的类型
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Request.RequestBase.RegularCode">
            <summary>
            本次请求解析字节数据的规则
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Request.RequestBase.Address">
            <summary>
            变量的地址
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Request.RequestBase.Length">
            <summary>
            读取的数据长度，指定类型读取时无效，字符串也无效
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Request.RequestBase.LastActiveTime">
            <summary>
            上一次读取数据的时间节点
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.RequestBase.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.RequestBase.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Request.RequestBase.GetNodeClassRenders">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Server.NodeMelsecMCServer">
            <summary>
            一个三菱MC协议的虚拟服务器节点对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeMelsecMCServer.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeMelsecMCServer.Protocol">
            <summary>
            当前的协议，可选 TCP，UDP
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeMelsecMCServer.IsBinary">
            <summary>
            当前的协议是否是二进制的，如果是 ASCII，设置为 False
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeMelsecMCServer.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeMelsecMCServer.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeMelsecMCServer.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Server.NodeModbusServer">
            <summary>
            Modbus服务器对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeModbusServer.#ctor">
            <summary>
            实例化一个Modbus服务器的节点对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeModbusServer.PortName">
            <summary>
            串口名称
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeModbusServer.BaudRate">
            <summary>
            波特率
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeModbusServer.DataBits">
            <summary>
            标准数据位长度
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeModbusServer.StopBits">
            <summary>
            标准停止位长度
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeModbusServer.Parity">
            <summary>
            奇偶校验检查
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeModbusServer.Station">
            <summary>
            客户端的站号
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeModbusServer.DataFormat">
            <summary>
            字节分析是否颠倒
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeModbusServer.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeModbusServer.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeModbusServer.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Server.NodeOmronFinsServer">
            <summary>
            一个欧姆龙FINS协议的虚拟PLC对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeOmronFinsServer.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.NodeOmronFinsServer.Protocol">
            <summary>
            当前的协议，可选 TCP，UDP
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeOmronFinsServer.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeOmronFinsServer.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeOmronFinsServer.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Server.NodeSiemensFWServer">
            <summary>
            西门子PLC的FW协议的虚拟服务器
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeSiemensFWServer.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeSiemensFWServer.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Server.NodeSiemensS7Server">
            <summary>
            西门子S7协议实现的虚拟服务器
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeSiemensS7Server.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.NodeSiemensS7Server.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Node.Server.ServerNode">
            <summary>
            服务器节点的基类，包含了端口号信息，服务器类型，服务器创建时间等基本的要素
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.ServerNode.#ctor">
            <summary>
            实例化一个默认的对象
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.ServerNode.Port">
            <summary>
            当前服务器的端口号信息
            </summary>
        </member>
        <member name="P:HslTechnology.Edge.Node.Server.ServerNode.ActiveTime">
            <summary>
            客户端的活动时间，单位秒，客户端超过指定时间不进行数据交互，就判断为离线状态，进行强制下线
            </summary>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.ServerNode.ToXmlElement">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.ServerNode.LoadByXmlElement(System.Xml.Linq.XElement)">
            <inheritdoc/>
        </member>
        <member name="M:HslTechnology.Edge.Node.Server.ServerNode.ToString">
            <inheritdoc/>
        </member>
        <member name="T:HslTechnology.Edge.Reflection.EdgeReflectionHelper">
            <summary>
            反射相关的辅助方法
            </summary>
        </member>
    </members>
</doc>
