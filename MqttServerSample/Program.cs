using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HslCommunication.MQTT;

namespace MqttServerSample
{
	class Program
	{
        static void Main( string[] args )
        {
            MqttServer mqttServer = new MqttServer( );
            mqttServer.OnClientApplicationMessageReceive += MqttServer_OnClientApplicationMessageReceive;
            mqttServer.ServerStart( 1883 );

            Console.WriteLine( "本测试的客户端可以连续测试24小时，感谢使用测试。端口号：1883" );
            // 然后每秒不停的发布数据即可
            while (true)
            {
                System.Threading.Thread.Sleep( 1000 );
                //mqttServer.PublishTopicPayload( "Data", Encoding.UTF8.GetBytes( "你的JSON数据" + DateTime.Now.ToString( ) ) );
            }
        }

        private static void MqttServer_OnClientApplicationMessageReceive( MqttSession session, MqttClientApplicationMessage message )
        {
            string msg = @"发送消息的客户端id:" + message.ClientId + Environment.NewLine
           + "发送时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff") + Environment.NewLine
           + "发送消息的主题：" + message.Topic + Environment.NewLine
           + "发送的消息内容：" + Encoding.UTF8.GetString( message.Payload ?? new byte[0] ) + "\n"
           + "--------------------------------------------------\n"
          ;
            Console.WriteLine( msg );
        }
    }
}
